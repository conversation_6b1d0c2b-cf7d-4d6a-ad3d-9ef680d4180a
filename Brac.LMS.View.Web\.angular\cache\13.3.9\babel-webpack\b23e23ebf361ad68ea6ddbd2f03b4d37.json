{"ast": null, "code": "import { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function sequenceEqual(compareTo, comparator = (a, b) => a === b) {\n  return operate((source, subscriber) => {\n    const aState = createState();\n    const bState = createState();\n\n    const emit = isEqual => {\n      subscriber.next(isEqual);\n      subscriber.complete();\n    };\n\n    const createSubscriber = (selfState, otherState) => {\n      const sequenceEqualSubscriber = new OperatorSubscriber(subscriber, a => {\n        const {\n          buffer,\n          complete\n        } = otherState;\n\n        if (buffer.length === 0) {\n          complete ? emit(false) : selfState.buffer.push(a);\n        } else {\n          !comparator(a, buffer.shift()) && emit(false);\n        }\n      }, () => {\n        selfState.complete = true;\n        const {\n          complete,\n          buffer\n        } = otherState;\n        complete && emit(buffer.length === 0);\n        sequenceEqualSubscriber === null || sequenceEqualSubscriber === void 0 ? void 0 : sequenceEqualSubscriber.unsubscribe();\n      });\n      return sequenceEqualSubscriber;\n    };\n\n    source.subscribe(createSubscriber(aState, bState));\n    compareTo.subscribe(createSubscriber(bState, aState));\n  });\n}\n\nfunction createState() {\n  return {\n    buffer: [],\n    complete: false\n  };\n} //# sourceMappingURL=sequenceEqual.js.map", "map": null, "metadata": {}, "sourceType": "module"}