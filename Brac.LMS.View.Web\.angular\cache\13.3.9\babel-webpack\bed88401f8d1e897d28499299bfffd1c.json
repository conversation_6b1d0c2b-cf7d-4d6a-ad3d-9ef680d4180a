{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { TemplateRef } from '@angular/core';\nimport { BlockUI } from 'ng-block-ui';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"../_services/authentication.service\";\nimport * as i5 from \"ngx-smart-modal\";\nimport * as i6 from \"ng-block-ui\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"ngx-extended-pdf-viewer\";\n\nfunction ReportsComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ReportsComponent_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return ctx_r3.downloadMyEvaluationTestResults(\"WebView\");\n    });\n    i0.ɵɵtext(1, \" My Evaluation Test Results \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ReportsComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelementStart(1, \"ngx-extended-pdf-viewer\", 15);\n    i0.ɵɵlistener(\"srcChange\", function ReportsComponent_div_16_Template_ngx_extended_pdf_viewer_srcChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return ctx_r5.pdfSrc = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r2.pdfSrc)(\"zoom\", \"page-width\")(\"showPrintButton\", false)(\"showRotateButton\", false)(\"showOpenFileButton\", false)(\"showDownloadButton\", false)(\"showBookmarkButton\", true)(\"showSecondaryToolbarButton\", false)(\"useBrowserLocale\", true);\n  }\n}\n\nexport class ReportsComponent {\n  constructor(formBuilder, _service, toastr, authService, ngxSmartModalService) {\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.toastr = toastr;\n    this.authService = authService;\n    this.ngxSmartModalService = ngxSmartModalService;\n    this.pdfSrc = null;\n    this.isTrainee = false;\n    this.authService.getCurrentUser().subscribe(user => {\n      this.isTrainee = user.Roles.indexOf('Trainee') !== -1;\n    });\n  }\n\n  ngOnInit() {}\n\n  downloadMyCourseStudyReport(reportType) {\n    this.blockUI.start('Generating report. Please wait...');\n    const obj = {\n      reportType: reportType === 'WebView' ? 'Pdf' : reportType\n    };\n\n    this._service.downloadFile('trainee/get-my-course-study-report', obj).subscribe({\n      next: res => {\n        if (reportType === 'WebView') {\n          this.reportFileName = 'My_Course_Study_Report_' + moment().format('DD_MM_YYYY') + '.pdf';\n          this.pdfSrc = res;\n          this.ngxSmartModalService.create('pdfViewerModal', this.tpl).open();\n          return;\n        }\n\n        const url = window.URL.createObjectURL(res);\n        var link = document.createElement('a');\n        link.href = url;\n        link.target = 'blank';\n        link.rel = 'noopener';\n        link.download = \"My_Course_Study_Report_\" + moment().format('DD_MM_YYYY') + \".xlsx\";\n        link.click();\n        link.remove();\n      },\n      error: err => {\n        this.toastr.error(err.message || err, 'Error!', {\n          timeOut: 2000\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  downloadMyEvaluationTestResults(reportType) {\n    this.blockUI.start('Generating report. Please wait...');\n    const obj = {\n      reportType: reportType === 'WebView' ? 'Pdf' : reportType,\n      timeZoneOffset: new Date().getTimezoneOffset()\n    };\n\n    this._service.downloadFile('evaluation-exam/get-my-evaluation-test-results', obj).subscribe({\n      next: res => {\n        if (reportType === 'WebView') {\n          this.reportFileName = 'My_Course_Study_Report_' + moment().format('DD_MM_YYYY') + '.pdf';\n          this.pdfSrc = res;\n          console.log('res', res);\n          this.ngxSmartModalService.create('pdfViewerModal', this.tpl).open();\n          return;\n        }\n\n        const url = window.URL.createObjectURL(res);\n        var link = document.createElement('a');\n        link.href = url;\n        link.target = 'blank';\n        link.rel = 'noopener';\n        link.download = \"My_Course_Study_Report_\" + moment().format('DD_MM_YYYY') + \".xlsx\";\n        link.click();\n        link.remove();\n      },\n      error: err => {\n        this.toastr.error(err.message || err, 'Error!', {\n          timeOut: 2000\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n}\n\nReportsComponent.ɵfac = function ReportsComponent_Factory(t) {\n  return new (t || ReportsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i4.AuthenticationService), i0.ɵɵdirectiveInject(i5.NgxSmartModalService));\n};\n\nReportsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ReportsComponent,\n  selectors: [[\"app-reports\"]],\n  viewQuery: function ReportsComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tpl = _t.first);\n    }\n  },\n  decls: 17,\n  vars: 2,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"style-1\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"mt-1\", \"mb-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"d-grid\", \"gap-2\", \"col-6\", \"mx-auto\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"class\", \"btn btn-primary\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"identifier\", \"pdfViewerModal\", \"customClass\", \"nsm-dialog-animation-btt modal-xl\"], [\"pdfViewerModal\", \"\"], [\"class\", \"w-100 style-2\", 4, \"ngIf\"], [1, \"w-100\", \"style-2\"], [3, \"src\", \"zoom\", \"showPrintButton\", \"showRotateButton\", \"showOpenFileButton\", \"showDownloadButton\", \"showBookmarkButton\", \"showSecondaryToolbarButton\", \"useBrowserLocale\", \"srcChange\"]],\n  template: function ReportsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h1\", 4);\n      i0.ɵɵtext(6, \"Reports\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(7, \"hr\", 5);\n      i0.ɵɵelementStart(8, \"div\", 6);\n      i0.ɵɵelementStart(9, \"div\", 7);\n      i0.ɵɵelementStart(10, \"div\", 8);\n      i0.ɵɵelementStart(11, \"button\", 9);\n      i0.ɵɵlistener(\"click\", function ReportsComponent_Template_button_click_11_listener() {\n        return ctx.downloadMyCourseStudyReport(\"WebView\");\n      });\n      i0.ɵɵtext(12, \" My Course Study Report \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(13, ReportsComponent_button_13_Template, 2, 0, \"button\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"ngx-smart-modal\", 11, 12);\n      i0.ɵɵtemplate(16, ReportsComponent_div_16_Template, 2, 9, \"div\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"ngIf\", ctx.isTrainee);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.pdfSrc);\n    }\n  },\n  directives: [i6.BlockUIComponent, i7.NgIf, i5.NgxSmartModalComponent, i8.NgxExtendedPdfViewerComponent],\n  styles: [\".mt--1-c{margin-top:-1px!important}.style-1{margin-top:-15px}.style-2{height:605px}\\n\"],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], ReportsComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}