{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { environment } from 'src/environments/environment';\nimport { BlockUI } from 'ng-block-ui';\nimport { debounceTime } from 'rxjs';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { Page } from '../_models/page';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../_services/common.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"../_services/authentication.service\";\nimport * as i6 from \"ng-block-ui\";\nimport * as i7 from \"@ng-select/ng-select\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"ngx-pagination\";\n\nconst _c0 = function (a1) {\n  return [\"/course-details-preview\", a1];\n};\n\nfunction AvailableCoursesComponent_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelementStart(1, \"a\", 22);\n    i0.ɵɵelementStart(2, \"div\", 23);\n    i0.ɵɵelement(3, \"img\", 24);\n    i0.ɵɵelementStart(4, \"div\", 25);\n    i0.ɵɵelementStart(5, \"div\", 6);\n    i0.ɵɵelementStart(6, \"div\", 26);\n    i0.ɵɵelementStart(7, \"h4\", 27);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 6);\n    i0.ɵɵelementStart(10, \"div\", 28);\n    i0.ɵɵelementStart(11, \"div\", 29);\n    i0.ɵɵtext(12);\n    i0.ɵɵelement(13, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 28);\n    i0.ɵɵelementStart(15, \"div\", 31);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const course_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(5, _c0, course_r5.Id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r4.mediaBaseUrl + course_r5.ImagePath, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", course_r5.Title, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", course_r5.Rating, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", course_r5.NoOfContents, \" Lectures \");\n  }\n}\n\nconst _c1 = function (a0, a1, a2) {\n  return {\n    itemsPerPage: a0,\n    currentPage: a1,\n    totalItems: a2\n  };\n};\n\nfunction AvailableCoursesComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, AvailableCoursesComponent_div_21_div_1_Template, 17, 7, \"div\", 20);\n    i0.ɵɵpipe(2, \"paginate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(2, 1, ctx_r0.courseList, i0.ɵɵpureFunction3(4, _c1, ctx_r0.page.size, ctx_r0.page.pageNumber, ctx_r0.page.totalElements)));\n  }\n}\n\nfunction AvailableCoursesComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h6\", 32);\n    i0.ɵɵtext(1, \"No Item Found\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AvailableCoursesComponent_div_24_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.page.showingResult());\n  }\n}\n\nfunction AvailableCoursesComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelementStart(1, \"div\", 34);\n    i0.ɵɵtemplate(2, AvailableCoursesComponent_div_24_p_2_Template, 2, 1, \"p\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵelementStart(4, \"nav\", 37);\n    i0.ɵɵelementStart(5, \"pagination-controls\", 38);\n    i0.ɵɵlistener(\"pageChange\", function AvailableCoursesComponent_div_24_Template_pagination_controls_pageChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return ctx_r8.setPage($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.page);\n  }\n}\n\nexport class AvailableCoursesComponent {\n  constructor(formBuilder, router, _service, toastr, authService) {\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.authService = authService;\n    this.coursesMenu = ['Amsterdam', 'Antwerp', 'Athens'];\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.timestamp = new Date().getTime();\n    this.page = new Page();\n    this.panelOpenState = false;\n    this.categoryList = [];\n    this.courseList = [];\n    this.page.startsFrom = 1;\n    this.page.pageNumber = 1;\n    this.page.size = 8;\n    this.authService.getCurrentUser().subscribe(user => this.currentUser = user);\n  }\n\n  ngOnInit() {\n    this.getCourseList();\n    this.filterForm = this.formBuilder.group({\n      name: [null],\n      categoryId: [null],\n      courseType: [null]\n    });\n    this.filterForm.get(\"name\").valueChanges.pipe(debounceTime(700)).subscribe(() => {\n      this.getList();\n    });\n    this.getList();\n    this.getCategoryList(); // this.getCourseList();\n  }\n\n  getCategoryList() {\n    this._service.get('course-category/dropdown-list').subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.categoryList = res.Data;\n      console.log('this.categoryList', this.categoryList);\n    }, err => {});\n  }\n\n  getCourseList() {\n    this.courseList = [{\n      Id: 0,\n      NgIf: \"isTrainee\",\n      Name: 'Available Courses',\n      RouterLink: '/available-courses',\n      RouterLinkActive: 'active',\n      Icon: 'fa fa-book-open fs-lg me-2 fw-bolder text-dark'\n    }, {\n      Id: 1,\n      NgIf: \"isTrainee\",\n      Name: 'My Courses',\n      RouterLink: '/my-courses',\n      RouterLinkActive: 'active',\n      Icon: 'fa fa-book-reader fs-lg me-2 fw-bolder text-dark'\n    }, {\n      Id: 2,\n      NgIf: \"isTrainee\",\n      Name: 'Bookmarks',\n      RouterLink: '/bookmarks',\n      RouterLinkActive: 'active',\n      Icon: 'ai-bookmark fs-lg me-2 fw-bolder text-dark'\n    }];\n    this.courseList = [{\n      Id: 0,\n      Name: 'Available Courses'\n    }, {\n      Id: 1,\n      Name: 'My Courses'\n    }, {\n      Id: 2,\n      Name: 'Bookmarks'\n    }];\n    console.table(this.courseList);\n  }\n\n  setPage(pageNumber) {\n    this.page.pageNumber = pageNumber;\n    this.getList();\n  }\n\n  getList() {\n    let obj = {\n      name: this.filterForm.value.name ? this.filterForm.value.name.trim() : null,\n      categoryId: this.filterForm.value.categoryId,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber - 1\n    };\n    this.blockUI.start('Getting data. Please wait...');\n\n    this._service.get('course/get-available-courses', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.courseList = res.Data.Records;\n        this.page.pageTotalElements = res.Data.Records.length;\n        this.page.totalElements = res.Data.Total;\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  navigateTo() {\n    var courseType = this.filterForm.value.courseType;\n    console.log('courseType', courseType);\n    this.router.navigateByUrl(courseType);\n  }\n\n}\n\nAvailableCoursesComponent.ɵfac = function AvailableCoursesComponent_Factory(t) {\n  return new (t || AvailableCoursesComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.CommonService), i0.ɵɵdirectiveInject(i4.ToastrService), i0.ɵɵdirectiveInject(i5.AuthenticationService));\n};\n\nAvailableCoursesComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AvailableCoursesComponent,\n  selectors: [[\"app-available-courses\"]],\n  decls: 25,\n  vars: 7,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"mt-1\", \"mb-4\"], [1, \"row\"], [\"autocomplete\", \"off\", 1, \"col-lg-10\", \"col-12\", 3, \"formGroup\"], [1, \"col-lg-10\", \"col-12\", \"mb-3\"], [1, \"input-group\"], [\"for\", \"name\", 1, \"visually-hidden\"], [\"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"type name to search...\", 1, \"form-control\", \"rounded-custom\", \"pe-5\"], [1, \"ai-search\", \"position-absolute\", \"top-50\", \"end-0\", \"translate-middle-y\", \"me-3\"], [1, \"col-lg-3\", \"col-12\", \"mb-3\"], [\"for\", \"categoryId\", 1, \"visually-hidden\"], [\"formControlName\", \"categoryId\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Category\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"change\"], [\"class\", \"row mb-5 mt-3 align-items-center\", 4, \"ngIf\", \"ngIfElse\"], [\"elseTemplate\", \"\"], [\"class\", \"row text-center\", 4, \"ngIf\"], [1, \"row\", \"mb-5\", \"mt-3\", \"align-items-center\"], [\"class\", \"col-md-4 px-1 my-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"px-1\", \"my-1\"], [\"routerLinkActive\", \"router-link-active\", 1, \"card\", \"border-0\", \"card-floating-position-custom\", \"shadow\", \"mx-1\", \"my-1\", 3, \"routerLink\"], [1, \"card\", \"ac-style-1\"], [\"alt\", \"Maldives\", 1, \"card-img-top\", \"ac.style-2\", 3, \"src\"], [1, \"card-body\", \"ac-style-3\"], [1, \"col-md-12\"], [1, \"text-start\", \"text-dark\"], [1, \"col-6\"], [1, \"d-inline-block\", \"fw-normal\", \"bg-faded-black\", \"text-white\", \"px-2\", \"py-1\", \"rounded-1\"], [1, \"fa\", \"fa-star\", \"text-gold\"], [1, \"d-inline-block\", \"fw-normal\", \"bg-faded-black\", \"text-white\", \"px-2\", \"py-1\", \"rounded-1\", \"float-end\"], [1, \"py-2\"], [1, \"row\", \"text-center\"], [1, \"col-md-3\", \"col-xs-12\"], [4, \"ngIf\"], [1, \"col-md-9\", \"col-xs-12\"], [1, \"align-items-center\"], [3, \"pageChange\"]],\n  template: function AvailableCoursesComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h1\", 4);\n      i0.ɵɵtext(6, \"Available Courses\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(7, \"hr\", 5);\n      i0.ɵɵelementStart(8, \"div\", 6);\n      i0.ɵɵelementStart(9, \"form\", 7);\n      i0.ɵɵelementStart(10, \"div\", 6);\n      i0.ɵɵelementStart(11, \"div\", 8);\n      i0.ɵɵelementStart(12, \"div\", 9);\n      i0.ɵɵelementStart(13, \"label\", 10);\n      i0.ɵɵtext(14, \"Name\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(15, \"input\", 11);\n      i0.ɵɵelement(16, \"i\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"div\", 13);\n      i0.ɵɵelementStart(18, \"label\", 14);\n      i0.ɵɵtext(19, \"Category\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"ng-select\", 15);\n      i0.ɵɵlistener(\"change\", function AvailableCoursesComponent_Template_ng_select_change_20_listener() {\n        return ctx.getList();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(21, AvailableCoursesComponent_div_21_Template, 3, 8, \"div\", 16);\n      i0.ɵɵtemplate(22, AvailableCoursesComponent_ng_template_22_Template, 2, 0, \"ng-template\", null, 17, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(24, AvailableCoursesComponent_div_24_Template, 6, 1, \"div\", 18);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(23);\n\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(11);\n      i0.ɵɵproperty(\"clearable\", true)(\"clearOnBackspace\", true)(\"items\", ctx.categoryList);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.courseList.length > 0)(\"ngIfElse\", _r1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.courseList.length > 0);\n    }\n  },\n  directives: [i6.BlockUIComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i7.NgSelectComponent, i8.NgIf, i8.NgForOf, i2.RouterLinkWithHref, i2.RouterLinkActive, i9.PaginationControlsComponent],\n  pipes: [i9.PaginatePipe],\n  styles: [\".card-floating-div[_ngcontent-%COMP%]{position:absolute;bottom:80%;left:5%;right:5%;z-index:1}.ac-style-1[_ngcontent-%COMP%]{border:none}.ac.style-2[_ngcontent-%COMP%]{height:190px}.ac-style-3[_ngcontent-%COMP%]{padding:8px;background-color:#f0f8ff;border-radius:0 0 15px 15px}\"]\n});\n\n__decorate([BlockUI()], AvailableCoursesComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}