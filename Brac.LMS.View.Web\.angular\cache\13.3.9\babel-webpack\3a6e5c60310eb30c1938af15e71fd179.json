{"ast": null, "code": "import { Observable } from '../Observable';\nimport { EMPTY } from './empty';\nexport function range(start, count, scheduler) {\n  if (count == null) {\n    count = start;\n    start = 0;\n  }\n\n  if (count <= 0) {\n    return EMPTY;\n  }\n\n  const end = count + start;\n  return new Observable(scheduler ? subscriber => {\n    let n = start;\n    return scheduler.schedule(function () {\n      if (n < end) {\n        subscriber.next(n++);\n        this.schedule();\n      } else {\n        subscriber.complete();\n      }\n    });\n  } : subscriber => {\n    let n = start;\n\n    while (n < end && !subscriber.closed) {\n      subscriber.next(n++);\n    }\n\n    subscriber.complete();\n  });\n} //# sourceMappingURL=range.js.map", "map": null, "metadata": {}, "sourceType": "module"}