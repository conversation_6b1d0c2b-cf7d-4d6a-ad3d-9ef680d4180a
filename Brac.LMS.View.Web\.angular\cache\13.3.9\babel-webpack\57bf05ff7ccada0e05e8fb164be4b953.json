{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { DashboardRoutes } from './dashboard.routing';\nimport { SlickCarouselModule } from 'ngx-slick-carousel';\nimport { NgxSmartModalModule } from 'ngx-smart-modal';\nimport { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-smart-modal\";\nexport let DashboardModule = /*#__PURE__*/(() => {\n  class DashboardModule {}\n\n  DashboardModule.ɵfac = function DashboardModule_Factory(t) {\n    return new (t || DashboardModule)();\n  };\n\n  DashboardModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: DashboardModule\n  });\n  DashboardModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(DashboardRoutes), SharedModule, WebLayoutModule, SlickCarouselModule, NgxSmartModalModule.forRoot(), NgxExtendedPdfViewerModule]]\n  });\n  return DashboardModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}