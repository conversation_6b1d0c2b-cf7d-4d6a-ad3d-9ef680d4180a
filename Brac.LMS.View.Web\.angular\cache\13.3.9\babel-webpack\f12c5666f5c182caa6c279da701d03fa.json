{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { LearningHoursRoutes } from './learning-hours.routing';\nimport { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';\nimport { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-smart-modal\";\nexport let LearningHoursModule = /*#__PURE__*/(() => {\n  class LearningHoursModule {}\n\n  LearningHoursModule.ɵfac = function LearningHoursModule_Factory(t) {\n    return new (t || LearningHoursModule)();\n  };\n\n  LearningHoursModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: LearningHoursModule\n  });\n  LearningHoursModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [NgxSmartModalService],\n    imports: [[CommonModule, RouterModule.forChild(LearningHoursRoutes), SharedModule, WebLayoutModule, NgxSmartModalModule.forRoot(), NgxExtendedPdfViewerModule]]\n  });\n  return LearningHoursModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}