{"ast": null, "code": "import { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function distinctUntilChanged(comparator, keySelector = identity) {\n  comparator = comparator !== null && comparator !== void 0 ? comparator : defaultCompare;\n  return operate((source, subscriber) => {\n    let previousKey;\n    let first = true;\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      const currentKey = keySelector(value);\n\n      if (first || !comparator(previousKey, currentKey)) {\n        first = false;\n        previousKey = currentKey;\n        subscriber.next(value);\n      }\n    }));\n  });\n}\n\nfunction defaultCompare(a, b) {\n  return a === b;\n} //# sourceMappingURL=distinctUntilChanged.js.map", "map": null, "metadata": {}, "sourceType": "module"}