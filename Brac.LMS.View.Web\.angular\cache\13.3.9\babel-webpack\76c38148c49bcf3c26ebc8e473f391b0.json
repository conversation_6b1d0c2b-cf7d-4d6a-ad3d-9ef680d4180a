{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { ReportsRoutes } from './reports.routing';\nimport { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';\nimport { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-smart-modal\";\nexport let ReportsModule = /*#__PURE__*/(() => {\n  class ReportsModule {}\n\n  ReportsModule.ɵfac = function ReportsModule_Factory(t) {\n    return new (t || ReportsModule)();\n  };\n\n  ReportsModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ReportsModule\n  });\n  ReportsModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [NgxSmartModalService],\n    imports: [[CommonModule, RouterModule.forChild(ReportsRoutes), SharedModule, WebLayoutModule, NgxSmartModalModule.forRoot(), NgxExtendedPdfViewerModule]]\n  });\n  return ReportsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}