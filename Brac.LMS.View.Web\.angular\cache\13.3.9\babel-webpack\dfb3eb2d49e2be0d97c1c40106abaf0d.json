{"ast": null, "code": "//import { AllCoursesComponent } from './all-courses.component';\nimport AllCoursesComponent from './all-courses.component'; // const routes: Routes = [\n//   {  },\n// ];\n//export const AllCoursesRoutes = RouterModule.forChild(routes);\n\nexport const AllCoursesRoutes = [{\n  path: '',\n  component: AllCoursesComponent,\n  data: {\n    breadcrumb: 'All Courses',\n    icon: 'fa fa-book-open bg-c-blue',\n    status: false\n  }\n}];", "map": null, "metadata": {}, "sourceType": "module"}