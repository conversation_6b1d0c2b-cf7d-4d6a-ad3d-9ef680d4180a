{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { TemplateRef } from '@angular/core';\nimport { environment } from 'src/environments/environment';\nimport { BlockUI } from 'ng-block-ui';\nimport { debounceTime } from 'rxjs';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { Page } from '../_models/page';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../app.component\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../_services/common.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"ngx-smart-modal\";\nimport * as i6 from \"ng-block-ui\";\nimport * as i7 from \"@ng-select/ng-select\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"ngx-pagination\";\nimport * as i10 from \"ngx-extended-pdf-viewer\";\n\nfunction ELibraryComponent_div_35_div_1_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const content_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", content_r12.MaterialType, \" \");\n  }\n}\n\nfunction ELibraryComponent_div_35_div_1_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const content_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", content_r12.MaterialType, \"\");\n  }\n}\n\nfunction ELibraryComponent_div_35_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelementStart(1, \"a\", 39);\n    i0.ɵɵlistener(\"click\", function ELibraryComponent_div_35_div_1_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r19);\n      const content_r12 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return ctx_r18.openModal(content_r12);\n    });\n    i0.ɵɵelementStart(2, \"div\", 40);\n    i0.ɵɵelement(3, \"img\", 41);\n    i0.ɵɵelementStart(4, \"div\", 42);\n    i0.ɵɵelementStart(5, \"div\", 43);\n    i0.ɵɵelementStart(6, \"container-element\", 44);\n    i0.ɵɵtemplate(7, ELibraryComponent_div_35_div_1_span_7_Template, 3, 1, \"span\", 45);\n    i0.ɵɵtemplate(8, ELibraryComponent_div_35_div_1_span_8_Template, 3, 1, \"span\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 46);\n    i0.ɵɵelementStart(10, \"div\", 9);\n    i0.ɵɵelementStart(11, \"div\", 7);\n    i0.ɵɵelementStart(12, \"p\", 47);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const content_r12 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate2(\"src\", \"\", ctx_r11.baseUrl, \"\", content_r12.ImagePath, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngSwitch\", content_r12.MaterialType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Video\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Document\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", content_r12.Title, \" \");\n  }\n}\n\nconst _c0 = function (a0, a1, a2) {\n  return {\n    itemsPerPage: a0,\n    currentPage: a1,\n    totalItems: a2\n  };\n};\n\nfunction ELibraryComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, ELibraryComponent_div_35_div_1_Template, 14, 6, \"div\", 37);\n    i0.ɵɵpipe(2, \"paginate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(2, 1, ctx_r1.learningHoursContentList, i0.ɵɵpureFunction3(4, _c0, ctx_r1.page.size, ctx_r1.page.pageNumber, ctx_r1.page.totalElements)));\n  }\n}\n\nfunction ELibraryComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h6\", 50);\n    i0.ɵɵtext(1, \"No Item Found\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ELibraryComponent_div_38_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r20.page.showingResult());\n  }\n}\n\nfunction ELibraryComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelementStart(1, \"div\", 52);\n    i0.ɵɵtemplate(2, ELibraryComponent_div_38_p_2_Template, 2, 1, \"p\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 54);\n    i0.ɵɵelementStart(4, \"nav\", 55);\n    i0.ɵɵelementStart(5, \"pagination-controls\", 56);\n    i0.ɵɵlistener(\"pageChange\", function ELibraryComponent_div_38_Template_pagination_controls_pageChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return ctx_r21.setPage($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.page);\n  }\n}\n\nfunction ELibraryComponent_h4_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.selectedContent.Title);\n  }\n}\n\nfunction ELibraryComponent_video_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"video\", 58);\n    i0.ɵɵelement(1, \"source\", 59);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"src\", \"\", ctx_r7.mediaBaseUrl, \"\", ctx_r7.selectedContent.FilePath, \"\", i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction ELibraryComponent_h4_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r9.selectedContent.Title);\n  }\n}\n\nfunction ELibraryComponent_div_48_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelementStart(1, \"ngx-extended-pdf-viewer\", 64);\n    i0.ɵɵlistener(\"srcChange\", function ELibraryComponent_div_48_div_1_Template_ngx_extended_pdf_viewer_srcChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return ctx_r25.pdfSrc = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r23.pdfSrc)(\"zoom\", \"page-width\")(\"showPrintButton\", false)(\"showRotateButton\", false)(\"showOpenFileButton\", false)(\"showDownloadButton\", ctx_r23.docObj.CanDownload)(\"showBookmarkButton\", true)(\"showSecondaryToolbarButton\", false)(\"useBrowserLocale\", true);\n  }\n}\n\nfunction ELibraryComponent_div_48_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelementStart(1, \"div\", 66);\n    i0.ɵɵelementStart(2, \"p\", 67);\n    i0.ɵɵtext(3, \" This document can't preview here. You may download the document. Please click below button to download the document. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function ELibraryComponent_div_48_div_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return ctx_r27.downloadDoc();\n    });\n    i0.ɵɵelement(5, \"i\", 69);\n    i0.ɵɵtext(6, \" Download \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ELibraryComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, ELibraryComponent_div_48_div_1_Template, 2, 9, \"div\", 61);\n    i0.ɵɵtemplate(2, ELibraryComponent_div_48_div_2_Template, 7, 0, \"div\", 62);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.docObj.PDF);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.docObj.PDF);\n  }\n}\n\nexport class ELibraryComponent {\n  constructor(appComponent, formBuilder, _service, toastr, ngxSmartModalService) {\n    this.appComponent = appComponent;\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.toastr = toastr;\n    this.ngxSmartModalService = ngxSmartModalService;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.timestamp = new Date().getTime();\n    this.selectedContent = null;\n    this.learningHoursContentList = [];\n    this.page = new Page();\n    this.docObj = null;\n    this.pdfSrc = null;\n    this.categoryList = [];\n    this.page.startsFrom = 1;\n    this.page.pageNumber = 1;\n    this.page.size = 9;\n  }\n\n  handleSelectClick(selectElement) {\n    this.appComponent.handleSelectClick(selectElement);\n  }\n\n  ngOnInit() {\n    this.filterForm = this.formBuilder.group({\n      name: [null],\n      categoryId: [null]\n    });\n    this.filterForm.get(\"name\").valueChanges.pipe(debounceTime(700)).subscribe(() => {\n      this.getList();\n    });\n    this.getList();\n    this.getCategoryList();\n  }\n\n  getCategoryList() {\n    this._service.get('library-category/dropdown-list').subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.categoryList = res.Data;\n    }, err => {});\n  }\n\n  setPage(pageNumber) {\n    this.page.pageNumber = pageNumber;\n    this.getList();\n  }\n\n  getList() {\n    let obj = {\n      name: this.filterForm.value.name ? this.filterForm.value.name.trim() : null,\n      categoryId: this.filterForm.value.categoryId,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber - 1\n    };\n    this.blockUI.start('Getting data. Please wait...');\n\n    this._service.get('library/list', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.learningHoursContentList = res.Data.Records;\n        this.page.pageTotalElements = res.Data.Records.length;\n        this.page.totalElements = res.Data.Total;\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  openModal(item) {\n    // this.modalRefVideo = this.modalService.show(template, this.modalConfigVideo);\n    this.selectedContent = item;\n\n    switch (item.MaterialType) {\n      case \"Video\":\n        this.ngxSmartModalService.create('videoModal', this.tpl).open();\n        break;\n\n      case \"Document\":\n        this.onDocClick(item);\n        this.ngxSmartModalService.create('docModal', this.tpl).open();\n        break;\n    }\n  }\n\n  onDocClick(doc) {\n    this.docObj = null; //  if (doc.Restricted) return;\n\n    this.docObj = doc;\n\n    if (doc.FilePath.split('.').pop() === 'pdf') {\n      this.openPdf(doc.FilePath);\n      this.docObj.PDF = true;\n    } else {\n      this.docObj.PDF = false;\n    }\n  }\n\n  openPdf(path) {\n    this._service.getPDFFile(this.mediaBaseUrl + '/api/course/download-document-file?partialPath=' + path).subscribe(res => {\n      this.pdfSrc = res;\n    });\n  }\n\n  downloadDoc() {\n    var link = document.createElement('a');\n    link.href = this.mediaBaseUrl + this.docObj.FilePath;\n    link.target = '_blank';\n    link.rel = 'noopener';\n    link.download = this.docObj.Title + '.' + this.docObj.FilePath.split('.').pop();\n    link.click();\n    link.remove();\n  }\n\n  modalClose() {\n    this.selectedContent = null;\n  }\n\n}\n\nELibraryComponent.ɵfac = function ELibraryComponent_Factory(t) {\n  return new (t || ELibraryComponent)(i0.ɵɵdirectiveInject(i1.AppComponent), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.CommonService), i0.ɵɵdirectiveInject(i4.ToastrService), i0.ɵɵdirectiveInject(i5.NgxSmartModalService));\n};\n\nELibraryComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ELibraryComponent,\n  selectors: [[\"app-e-library\"]],\n  viewQuery: function ELibraryComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tpl = _t.first);\n    }\n  },\n  decls: 49,\n  vars: 11,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"mt-1\", \"mb-4\"], [1, \"row\", \"mb-5\"], [1, \"col-md-12\"], [1, \"bg-primary-light\", \"rounded-3\"], [1, \"row\"], [1, \"col-md-8\", \"pt-5\", \"px-4\", \"pb-4\", \"p-sm-5\"], [1, \"pb-2\", \"line-height-h3\"], [1, \"mb-1\"], [1, \"mb-0\"], [1, \"col-md-4\"], [\"src\", \"assets/img/demo/library.png\", 1, \"img-fluid\", \"mb-2\"], [\"autocomplete\", \"off\", 1, \"col-lg-12\", \"col-12\", 3, \"formGroup\"], [1, \"col-lg-8\", \"col-12\", \"mb-3\"], [1, \"input-group\"], [\"for\", \"name\", 1, \"visually-hidden\"], [\"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Type name to search...\", 1, \"form-control\", \"rounded-custom\", \"pe-5\"], [1, \"ai-search\", \"position-absolute\", \"top-50\", \"end-0\", \"translate-middle-y\", \"me-3\"], [1, \"col-lg-4\", \"col-12\", \"mb-3\"], [\"for\", \"categoryId\", 1, \"visually-hidden\"], [\"formControlName\", \"categoryId\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Category\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\", \"change\"], [\"selectElement\", \"\"], [\"class\", \"row mb-5 mt-3 align-items-center\", 4, \"ngIf\", \"ngIfElse\"], [\"elseTemplate\", \"\"], [\"class\", \"row text-center\", 4, \"ngIf\"], [\"identifier\", \"videoModal\", \"customClass\", \"nsm-dialog-animation-btt modal-lg\", 3, \"onAnyCloseEventFinished\"], [\"videoModal\", \"\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"w-100\", \"controls\", \"\", 4, \"ngIf\"], [\"identifier\", \"docModal\", \"customClass\", \"nsm-dialog-animation-btt modal-lg\", 3, \"onAnyCloseEventFinished\"], [\"docModal\", \"\"], [\"class\", \"p-1\", 4, \"ngIf\"], [1, \"row\", \"mb-5\", \"mt-3\", \"align-items-center\"], [\"class\", \"col-md-4 px-1 my-1 div-pointer\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"px-1\", \"my-1\", \"div-pointer\"], [1, \"card\", \"border-0\", \"card-floating-position-custom\", \"shadow\", \"mx-1\", \"my-1\", 3, \"click\"], [1, \"card-img-top\", \"card-img-bottom\"], [\"alt\", \"Maldives\", 3, \"src\"], [1, \"card-floating-div-top-right\"], [1, \"d-inline-block\", \"fw-normal\", \"bg-faded-black\", \"text-white\", \"text-white\", \"px-2\", \"py-1\", \"rounded-1\", \"float-end\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [1, \"card-floating-div\"], [1, \"text-start\", \"text-white\", \"text-shadow-custom\", \"bg-faded-black\", \"text-white\", \"px-2\", \"py-1\", \"mb-0\", \"rounded-1\"], [1, \"ai-film\", \"mt--1-c\", \"px-1\"], [1, \"ai-file\", \"ai-file\", \"mt--1-c\", \"px-1\"], [1, \"py-2\"], [1, \"row\", \"text-center\"], [1, \"col-md-3\", \"col-xs-12\"], [4, \"ngIf\"], [1, \"col-md-9\", \"col-xs-12\"], [1, \"align-items-center\"], [3, \"pageChange\"], [1, \"text-center\"], [\"controls\", \"\", 1, \"w-100\"], [\"type\", \"video/mp4\", 3, \"src\"], [1, \"p-1\"], [\"class\", \"w-100 elc-height-605\", 4, \"ngIf\"], [\"class\", \"card py-3 mb-2 elc-height-205\", 4, \"ngIf\"], [1, \"w-100\", \"elc-height-605\"], [3, \"src\", \"zoom\", \"showPrintButton\", \"showRotateButton\", \"showOpenFileButton\", \"showDownloadButton\", \"showBookmarkButton\", \"showSecondaryToolbarButton\", \"useBrowserLocale\", \"srcChange\"], [1, \"card\", \"py-3\", \"mb-2\", \"elc-height-205\"], [1, \"card-body\"], [1, \"font-size-20\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"mt-2\", 3, \"click\"], [1, \"fa\", \"fa-download\"]],\n  template: function ELibraryComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r29 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h1\", 4);\n      i0.ɵɵtext(6, \"E-Library\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(7, \"hr\", 5);\n      i0.ɵɵelementStart(8, \"div\", 6);\n      i0.ɵɵelementStart(9, \"div\", 7);\n      i0.ɵɵelementStart(10, \"div\", 8);\n      i0.ɵɵelementStart(11, \"div\", 9);\n      i0.ɵɵelementStart(12, \"div\", 10);\n      i0.ɵɵelementStart(13, \"h1\", 11);\n      i0.ɵɵtext(14, \"BRAC Bank E-Library\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"p\", 12);\n      i0.ɵɵtext(16, \"Enriching life with the knowledge of the world.\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"p\", 13);\n      i0.ɵɵtext(18, \"Read, Learn, Inspire.\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"div\", 14);\n      i0.ɵɵelement(20, \"img\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"div\", 9);\n      i0.ɵɵelementStart(22, \"form\", 16);\n      i0.ɵɵelementStart(23, \"div\", 9);\n      i0.ɵɵelementStart(24, \"div\", 17);\n      i0.ɵɵelementStart(25, \"div\", 18);\n      i0.ɵɵelementStart(26, \"label\", 19);\n      i0.ɵɵtext(27, \"Name\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(28, \"input\", 20);\n      i0.ɵɵelement(29, \"i\", 21);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"div\", 22);\n      i0.ɵɵelementStart(31, \"label\", 23);\n      i0.ɵɵtext(32, \"Category\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(33, \"ng-select\", 24, 25);\n      i0.ɵɵlistener(\"click\", function ELibraryComponent_Template_ng_select_click_33_listener() {\n        i0.ɵɵrestoreView(_r29);\n\n        const _r0 = i0.ɵɵreference(34);\n\n        return ctx.handleSelectClick(_r0);\n      })(\"change\", function ELibraryComponent_Template_ng_select_change_33_listener() {\n        return ctx.getList();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(35, ELibraryComponent_div_35_Template, 3, 8, \"div\", 26);\n      i0.ɵɵtemplate(36, ELibraryComponent_ng_template_36_Template, 2, 0, \"ng-template\", null, 27, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(38, ELibraryComponent_div_38_Template, 6, 1, \"div\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(39, \"ngx-smart-modal\", 29, 30);\n      i0.ɵɵlistener(\"onAnyCloseEventFinished\", function ELibraryComponent_Template_ngx_smart_modal_onAnyCloseEventFinished_39_listener() {\n        return ctx.modalClose();\n      });\n      i0.ɵɵtemplate(41, ELibraryComponent_h4_41_Template, 2, 1, \"h4\", 31);\n      i0.ɵɵelementStart(42, \"div\");\n      i0.ɵɵtemplate(43, ELibraryComponent_video_43_Template, 2, 2, \"video\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(44, \"ngx-smart-modal\", 33, 34);\n      i0.ɵɵlistener(\"onAnyCloseEventFinished\", function ELibraryComponent_Template_ngx_smart_modal_onAnyCloseEventFinished_44_listener() {\n        return ctx.modalClose();\n      });\n      i0.ɵɵtemplate(46, ELibraryComponent_h4_46_Template, 2, 1, \"h4\", 31);\n      i0.ɵɵelementStart(47, \"div\");\n      i0.ɵɵtemplate(48, ELibraryComponent_div_48_Template, 3, 2, \"div\", 35);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      const _r2 = i0.ɵɵreference(37);\n\n      i0.ɵɵadvance(22);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(11);\n      i0.ɵɵproperty(\"clearable\", true)(\"clearOnBackspace\", true)(\"items\", ctx.categoryList);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.learningHoursContentList.length > 0)(\"ngIfElse\", _r2);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.learningHoursContentList.length > 0);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedContent);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedContent);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedContent);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.docObj);\n    }\n  },\n  directives: [i6.BlockUIComponent, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.DefaultValueAccessor, i2.NgControlStatus, i2.FormControlName, i7.NgSelectComponent, i8.NgIf, i8.NgForOf, i8.NgSwitch, i8.NgSwitchCase, i9.PaginationControlsComponent, i5.NgxSmartModalComponent, i10.NgxExtendedPdfViewerComponent],\n  pipes: [i9.PaginatePipe],\n  styles: [\".mt--1-c{margin-top:-1px!important}.elc-height-605{height:605px}.elc-height-205{height:205px}\\n\"],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], ELibraryComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}