{"ast": null, "code": "import { Subject } from './Subject';\nexport class BehaviorSubject extends Subject {\n  constructor(_value) {\n    super();\n    this._value = _value;\n  }\n\n  get value() {\n    return this.getValue();\n  }\n\n  _subscribe(subscriber) {\n    const subscription = super._subscribe(subscriber);\n\n    !subscription.closed && subscriber.next(this._value);\n    return subscription;\n  }\n\n  getValue() {\n    const {\n      hasError,\n      thrownError,\n      _value\n    } = this;\n\n    if (hasError) {\n      throw thrownError;\n    }\n\n    this._throwIfClosed();\n\n    return _value;\n  }\n\n  next(value) {\n    super.next(this._value = value);\n  }\n\n} //# sourceMappingURL=BehaviorSubject.js.map", "map": null, "metadata": {}, "sourceType": "module"}