{"ast": null, "code": "'use strict';\n/**\n * @license Angular v12.0.0-next.0\n * (c) 2010-2020 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst Zone$1 = function (global) {\n  const performance = global['performance'];\n\n  function mark(name) {\n    performance && performance['mark'] && performance['mark'](name);\n  }\n\n  function performanceMeasure(name, label) {\n    performance && performance['measure'] && performance['measure'](name, label);\n  }\n\n  mark('Zone'); // Initialize before it's accessed below.\n  // __Zone_symbol_prefix global can be used to override the default zone\n  // symbol prefix with a custom one if needed.\n\n  const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n\n  function __symbol__(name) {\n    return symbolPrefix + name;\n  }\n\n  const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n\n  if (global['Zone']) {\n    // if global['Zone'] already exists (maybe zone.js was already loaded or\n    // some other lib also registered a global object named Zone), we may need\n    // to throw an error, but sometimes user may not want this error.\n    // For example,\n    // we have two web pages, page1 includes zone.js, page2 doesn't.\n    // and the 1st time user load page1 and page2, everything work fine,\n    // but when user load page2 again, error occurs because global['Zone'] already exists.\n    // so we add a flag to let user choose whether to throw this error or not.\n    // By default, if existing Zone is from zone.js, we will not throw the error.\n    if (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function') {\n      throw new Error('Zone already loaded.');\n    } else {\n      return global['Zone'];\n    }\n  }\n\n  let Zone = /*#__PURE__*/(() => {\n    class Zone {\n      constructor(parent, zoneSpec) {\n        this._parent = parent;\n        this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n        this._properties = zoneSpec && zoneSpec.properties || {};\n        this._zoneDelegate = new ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n      }\n\n      static assertZonePatched() {\n        if (global['Promise'] !== patches['ZoneAwarePromise']) {\n          throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' + 'has been overwritten.\\n' + 'Most likely cause is that a Promise polyfill has been loaded ' + 'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' + 'If you must load one, do so before loading zone.js.)');\n        }\n      }\n\n      static get root() {\n        let zone = Zone.current;\n\n        while (zone.parent) {\n          zone = zone.parent;\n        }\n\n        return zone;\n      }\n\n      static get current() {\n        return _currentZoneFrame.zone;\n      }\n\n      static get currentTask() {\n        return _currentTask;\n      } // tslint:disable-next-line:require-internal-with-underscore\n\n\n      static __load_patch(name, fn, ignoreDuplicate = false) {\n        if (patches.hasOwnProperty(name)) {\n          // `checkDuplicate` option is defined from global variable\n          // so it works for all modules.\n          // `ignoreDuplicate` can work for the specified module\n          if (!ignoreDuplicate && checkDuplicate) {\n            throw Error('Already loaded patch: ' + name);\n          }\n        } else if (!global['__Zone_disable_' + name]) {\n          const perfName = 'Zone:' + name;\n          mark(perfName);\n          patches[name] = fn(global, Zone, _api);\n          performanceMeasure(perfName, perfName);\n        }\n      }\n\n      get parent() {\n        return this._parent;\n      }\n\n      get name() {\n        return this._name;\n      }\n\n      get(key) {\n        const zone = this.getZoneWith(key);\n        if (zone) return zone._properties[key];\n      }\n\n      getZoneWith(key) {\n        let current = this;\n\n        while (current) {\n          if (current._properties.hasOwnProperty(key)) {\n            return current;\n          }\n\n          current = current._parent;\n        }\n\n        return null;\n      }\n\n      fork(zoneSpec) {\n        if (!zoneSpec) throw new Error('ZoneSpec required!');\n        return this._zoneDelegate.fork(this, zoneSpec);\n      }\n\n      wrap(callback, source) {\n        if (typeof callback !== 'function') {\n          throw new Error('Expecting function got: ' + callback);\n        }\n\n        const _callback = this._zoneDelegate.intercept(this, callback, source);\n\n        const zone = this;\n        return function () {\n          return zone.runGuarded(_callback, this, arguments, source);\n        };\n      }\n\n      run(callback, applyThis, applyArgs, source) {\n        _currentZoneFrame = {\n          parent: _currentZoneFrame,\n          zone: this\n        };\n\n        try {\n          return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n        } finally {\n          _currentZoneFrame = _currentZoneFrame.parent;\n        }\n      }\n\n      runGuarded(callback, applyThis = null, applyArgs, source) {\n        _currentZoneFrame = {\n          parent: _currentZoneFrame,\n          zone: this\n        };\n\n        try {\n          try {\n            return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n          } catch (error) {\n            if (this._zoneDelegate.handleError(this, error)) {\n              throw error;\n            }\n          }\n        } finally {\n          _currentZoneFrame = _currentZoneFrame.parent;\n        }\n      }\n\n      runTask(task, applyThis, applyArgs) {\n        if (task.zone != this) {\n          throw new Error('A task can only be run in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n        } // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n        // will run in notScheduled(canceled) state, we should not try to\n        // run such kind of task but just return\n\n\n        if (task.state === notScheduled && (task.type === eventTask || task.type === macroTask)) {\n          return;\n        }\n\n        const reEntryGuard = task.state != running;\n        reEntryGuard && task._transitionTo(running, scheduled);\n        task.runCount++;\n        const previousTask = _currentTask;\n        _currentTask = task;\n        _currentZoneFrame = {\n          parent: _currentZoneFrame,\n          zone: this\n        };\n\n        try {\n          if (task.type == macroTask && task.data && !task.data.isPeriodic) {\n            task.cancelFn = undefined;\n          }\n\n          try {\n            return this._zoneDelegate.invokeTask(this, task, applyThis, applyArgs);\n          } catch (error) {\n            if (this._zoneDelegate.handleError(this, error)) {\n              throw error;\n            }\n          }\n        } finally {\n          // if the task's state is notScheduled or unknown, then it has already been cancelled\n          // we should not reset the state to scheduled\n          if (task.state !== notScheduled && task.state !== unknown) {\n            if (task.type == eventTask || task.data && task.data.isPeriodic) {\n              reEntryGuard && task._transitionTo(scheduled, running);\n            } else {\n              task.runCount = 0;\n\n              this._updateTaskCount(task, -1);\n\n              reEntryGuard && task._transitionTo(notScheduled, running, notScheduled);\n            }\n          }\n\n          _currentZoneFrame = _currentZoneFrame.parent;\n          _currentTask = previousTask;\n        }\n      }\n\n      scheduleTask(task) {\n        if (task.zone && task.zone !== this) {\n          // check if the task was rescheduled, the newZone\n          // should not be the children of the original zone\n          let newZone = this;\n\n          while (newZone) {\n            if (newZone === task.zone) {\n              throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n            }\n\n            newZone = newZone.parent;\n          }\n        }\n\n        task._transitionTo(scheduling, notScheduled);\n\n        const zoneDelegates = [];\n        task._zoneDelegates = zoneDelegates;\n        task._zone = this;\n\n        try {\n          task = this._zoneDelegate.scheduleTask(this, task);\n        } catch (err) {\n          // should set task's state to unknown when scheduleTask throw error\n          // because the err may from reschedule, so the fromState maybe notScheduled\n          task._transitionTo(unknown, scheduling, notScheduled); // TODO: @JiaLiPassion, should we check the result from handleError?\n\n\n          this._zoneDelegate.handleError(this, err);\n\n          throw err;\n        }\n\n        if (task._zoneDelegates === zoneDelegates) {\n          // we have to check because internally the delegate can reschedule the task.\n          this._updateTaskCount(task, 1);\n        }\n\n        if (task.state == scheduling) {\n          task._transitionTo(scheduled, scheduling);\n        }\n\n        return task;\n      }\n\n      scheduleMicroTask(source, callback, data, customSchedule) {\n        return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n      }\n\n      scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n        return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n      }\n\n      scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n        return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n      }\n\n      cancelTask(task) {\n        if (task.zone != this) throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n\n        task._transitionTo(canceling, scheduled, running);\n\n        try {\n          this._zoneDelegate.cancelTask(this, task);\n        } catch (err) {\n          // if error occurs when cancelTask, transit the state to unknown\n          task._transitionTo(unknown, canceling);\n\n          this._zoneDelegate.handleError(this, err);\n\n          throw err;\n        }\n\n        this._updateTaskCount(task, -1);\n\n        task._transitionTo(notScheduled, canceling);\n\n        task.runCount = 0;\n        return task;\n      }\n\n      _updateTaskCount(task, count) {\n        const zoneDelegates = task._zoneDelegates;\n\n        if (count == -1) {\n          task._zoneDelegates = null;\n        }\n\n        for (let i = 0; i < zoneDelegates.length; i++) {\n          zoneDelegates[i]._updateTaskCount(task.type, count);\n        }\n      }\n\n    }\n\n    // tslint:disable-next-line:require-internal-with-underscore\n    Zone.__symbol__ = __symbol__;\n    return Zone;\n  })();\n  const DELEGATE_ZS = {\n    name: '',\n    onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n    onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n    onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n    onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task)\n  };\n\n  class ZoneDelegate {\n    constructor(zone, parentDelegate, zoneSpec) {\n      this._taskCounts = {\n        'microTask': 0,\n        'macroTask': 0,\n        'eventTask': 0\n      };\n      this.zone = zone;\n      this._parentDelegate = parentDelegate;\n      this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n      this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n      this._forkCurrZone = zoneSpec && (zoneSpec.onFork ? this.zone : parentDelegate._forkCurrZone);\n      this._interceptZS = zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n      this._interceptDlgt = zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n      this._interceptCurrZone = zoneSpec && (zoneSpec.onIntercept ? this.zone : parentDelegate._interceptCurrZone);\n      this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n      this._invokeDlgt = zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n      this._invokeCurrZone = zoneSpec && (zoneSpec.onInvoke ? this.zone : parentDelegate._invokeCurrZone);\n      this._handleErrorZS = zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n      this._handleErrorDlgt = zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n      this._handleErrorCurrZone = zoneSpec && (zoneSpec.onHandleError ? this.zone : parentDelegate._handleErrorCurrZone);\n      this._scheduleTaskZS = zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n      this._scheduleTaskDlgt = zoneSpec && (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n      this._scheduleTaskCurrZone = zoneSpec && (zoneSpec.onScheduleTask ? this.zone : parentDelegate._scheduleTaskCurrZone);\n      this._invokeTaskZS = zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n      this._invokeTaskDlgt = zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n      this._invokeTaskCurrZone = zoneSpec && (zoneSpec.onInvokeTask ? this.zone : parentDelegate._invokeTaskCurrZone);\n      this._cancelTaskZS = zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n      this._cancelTaskDlgt = zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n      this._cancelTaskCurrZone = zoneSpec && (zoneSpec.onCancelTask ? this.zone : parentDelegate._cancelTaskCurrZone);\n      this._hasTaskZS = null;\n      this._hasTaskDlgt = null;\n      this._hasTaskDlgtOwner = null;\n      this._hasTaskCurrZone = null;\n      const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n      const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n\n      if (zoneSpecHasTask || parentHasTask) {\n        // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n        // a case all task related interceptors must go through this ZD. We can't short circuit it.\n        this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n        this._hasTaskDlgt = parentDelegate;\n        this._hasTaskDlgtOwner = this;\n        this._hasTaskCurrZone = zone;\n\n        if (!zoneSpec.onScheduleTask) {\n          this._scheduleTaskZS = DELEGATE_ZS;\n          this._scheduleTaskDlgt = parentDelegate;\n          this._scheduleTaskCurrZone = this.zone;\n        }\n\n        if (!zoneSpec.onInvokeTask) {\n          this._invokeTaskZS = DELEGATE_ZS;\n          this._invokeTaskDlgt = parentDelegate;\n          this._invokeTaskCurrZone = this.zone;\n        }\n\n        if (!zoneSpec.onCancelTask) {\n          this._cancelTaskZS = DELEGATE_ZS;\n          this._cancelTaskDlgt = parentDelegate;\n          this._cancelTaskCurrZone = this.zone;\n        }\n      }\n    }\n\n    fork(targetZone, zoneSpec) {\n      return this._forkZS ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec) : new Zone(targetZone, zoneSpec);\n    }\n\n    intercept(targetZone, callback, source) {\n      return this._interceptZS ? this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source) : callback;\n    }\n\n    invoke(targetZone, callback, applyThis, applyArgs, source) {\n      return this._invokeZS ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source) : callback.apply(applyThis, applyArgs);\n    }\n\n    handleError(targetZone, error) {\n      return this._handleErrorZS ? this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error) : true;\n    }\n\n    scheduleTask(targetZone, task) {\n      let returnTask = task;\n\n      if (this._scheduleTaskZS) {\n        if (this._hasTaskZS) {\n          returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n        } // clang-format off\n\n\n        returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task); // clang-format on\n\n        if (!returnTask) returnTask = task;\n      } else {\n        if (task.scheduleFn) {\n          task.scheduleFn(task);\n        } else if (task.type == microTask) {\n          scheduleMicroTask(task);\n        } else {\n          throw new Error('Task is missing scheduleFn.');\n        }\n      }\n\n      return returnTask;\n    }\n\n    invokeTask(targetZone, task, applyThis, applyArgs) {\n      return this._invokeTaskZS ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs) : task.callback.apply(applyThis, applyArgs);\n    }\n\n    cancelTask(targetZone, task) {\n      let value;\n\n      if (this._cancelTaskZS) {\n        value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n      } else {\n        if (!task.cancelFn) {\n          throw Error('Task is not cancelable');\n        }\n\n        value = task.cancelFn(task);\n      }\n\n      return value;\n    }\n\n    hasTask(targetZone, isEmpty) {\n      // hasTask should not throw error so other ZoneDelegate\n      // can still trigger hasTask callback\n      try {\n        this._hasTaskZS && this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n      } catch (err) {\n        this.handleError(targetZone, err);\n      }\n    } // tslint:disable-next-line:require-internal-with-underscore\n\n\n    _updateTaskCount(type, count) {\n      const counts = this._taskCounts;\n      const prev = counts[type];\n      const next = counts[type] = prev + count;\n\n      if (next < 0) {\n        throw new Error('More tasks executed then were scheduled.');\n      }\n\n      if (prev == 0 || next == 0) {\n        const isEmpty = {\n          microTask: counts['microTask'] > 0,\n          macroTask: counts['macroTask'] > 0,\n          eventTask: counts['eventTask'] > 0,\n          change: type\n        };\n        this.hasTask(this.zone, isEmpty);\n      }\n    }\n\n  }\n\n  class ZoneTask {\n    constructor(type, source, callback, options, scheduleFn, cancelFn) {\n      // tslint:disable-next-line:require-internal-with-underscore\n      this._zone = null;\n      this.runCount = 0; // tslint:disable-next-line:require-internal-with-underscore\n\n      this._zoneDelegates = null; // tslint:disable-next-line:require-internal-with-underscore\n\n      this._state = 'notScheduled';\n      this.type = type;\n      this.source = source;\n      this.data = options;\n      this.scheduleFn = scheduleFn;\n      this.cancelFn = cancelFn;\n\n      if (!callback) {\n        throw new Error('callback is not defined');\n      }\n\n      this.callback = callback;\n      const self = this; // TODO: @JiaLiPassion options should have interface\n\n      if (type === eventTask && options && options.useG) {\n        this.invoke = ZoneTask.invokeTask;\n      } else {\n        this.invoke = function () {\n          return ZoneTask.invokeTask.call(global, self, this, arguments);\n        };\n      }\n    }\n\n    static invokeTask(task, target, args) {\n      if (!task) {\n        task = this;\n      }\n\n      _numberOfNestedTaskFrames++;\n\n      try {\n        task.runCount++;\n        return task.zone.runTask(task, target, args);\n      } finally {\n        if (_numberOfNestedTaskFrames == 1) {\n          drainMicroTaskQueue();\n        }\n\n        _numberOfNestedTaskFrames--;\n      }\n    }\n\n    get zone() {\n      return this._zone;\n    }\n\n    get state() {\n      return this._state;\n    }\n\n    cancelScheduleRequest() {\n      this._transitionTo(notScheduled, scheduling);\n    } // tslint:disable-next-line:require-internal-with-underscore\n\n\n    _transitionTo(toState, fromState1, fromState2) {\n      if (this._state === fromState1 || this._state === fromState2) {\n        this._state = toState;\n\n        if (toState == notScheduled) {\n          this._zoneDelegates = null;\n        }\n      } else {\n        throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? ' or \\'' + fromState2 + '\\'' : ''}, was '${this._state}'.`);\n      }\n    }\n\n    toString() {\n      if (this.data && typeof this.data.handleId !== 'undefined') {\n        return this.data.handleId.toString();\n      } else {\n        return Object.prototype.toString.call(this);\n      }\n    } // add toJSON method to prevent cyclic error when\n    // call JSON.stringify(zoneTask)\n\n\n    toJSON() {\n      return {\n        type: this.type,\n        state: this.state,\n        source: this.source,\n        zone: this.zone.name,\n        runCount: this.runCount\n      };\n    }\n\n  } //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  ///  MICROTASK QUEUE\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n\n\n  const symbolSetTimeout = __symbol__('setTimeout');\n\n  const symbolPromise = __symbol__('Promise');\n\n  const symbolThen = __symbol__('then');\n\n  let _microTaskQueue = [];\n  let _isDrainingMicrotaskQueue = false;\n  let nativeMicroTaskQueuePromise;\n\n  function scheduleMicroTask(task) {\n    // if we are not running in any task, and there has not been anything scheduled\n    // we must bootstrap the initial task creation by manually scheduling the drain\n    if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n      // We are not running in Task, so we need to kickstart the microtask queue.\n      if (!nativeMicroTaskQueuePromise) {\n        if (global[symbolPromise]) {\n          nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n        }\n      }\n\n      if (nativeMicroTaskQueuePromise) {\n        let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n\n        if (!nativeThen) {\n          // native Promise is not patchable, we need to use `then` directly\n          // issue 1078\n          nativeThen = nativeMicroTaskQueuePromise['then'];\n        }\n\n        nativeThen.call(nativeMicroTaskQueuePromise, drainMicroTaskQueue);\n      } else {\n        global[symbolSetTimeout](drainMicroTaskQueue, 0);\n      }\n    }\n\n    task && _microTaskQueue.push(task);\n  }\n\n  function drainMicroTaskQueue() {\n    if (!_isDrainingMicrotaskQueue) {\n      _isDrainingMicrotaskQueue = true;\n\n      while (_microTaskQueue.length) {\n        const queue = _microTaskQueue;\n        _microTaskQueue = [];\n\n        for (let i = 0; i < queue.length; i++) {\n          const task = queue[i];\n\n          try {\n            task.zone.runTask(task, null, null);\n          } catch (error) {\n            _api.onUnhandledError(error);\n          }\n        }\n      }\n\n      _api.microtaskDrainDone();\n\n      _isDrainingMicrotaskQueue = false;\n    }\n  } //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  ///  BOOTSTRAP\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n\n\n  const NO_ZONE = {\n    name: 'NO ZONE'\n  };\n  const notScheduled = 'notScheduled',\n        scheduling = 'scheduling',\n        scheduled = 'scheduled',\n        running = 'running',\n        canceling = 'canceling',\n        unknown = 'unknown';\n  const microTask = 'microTask',\n        macroTask = 'macroTask',\n        eventTask = 'eventTask';\n  const patches = {};\n  const _api = {\n    symbol: __symbol__,\n    currentZoneFrame: () => _currentZoneFrame,\n    onUnhandledError: noop,\n    microtaskDrainDone: noop,\n    scheduleMicroTask: scheduleMicroTask,\n    showUncaughtError: () => !Zone[__symbol__('ignoreConsoleErrorUncaughtError')],\n    patchEventTarget: () => [],\n    patchOnProperties: noop,\n    patchMethod: () => noop,\n    bindArguments: () => [],\n    patchThen: () => noop,\n    patchMacroTask: () => noop,\n    patchEventPrototype: () => noop,\n    isIEOrEdge: () => false,\n    getGlobalObjects: () => undefined,\n    ObjectDefineProperty: () => noop,\n    ObjectGetOwnPropertyDescriptor: () => undefined,\n    ObjectCreate: () => undefined,\n    ArraySlice: () => [],\n    patchClass: () => noop,\n    wrapWithCurrentZone: () => noop,\n    filterProperties: () => [],\n    attachOriginToPatched: () => noop,\n    _redefineProperty: () => noop,\n    patchCallbacks: () => noop\n  };\n  let _currentZoneFrame = {\n    parent: null,\n    zone: new Zone(null, null)\n  };\n  let _currentTask = null;\n  let _numberOfNestedTaskFrames = 0;\n\n  function noop() {}\n\n  performanceMeasure('Zone', 'Zone');\n  return global['Zone'] = Zone;\n}(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n/// <reference types=\"node\"/>\n// issue #989, to reduce bundle size, use short name\n\n/** Object.getOwnPropertyDescriptor */\n\n\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\n\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\n\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\n\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\n\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\n\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\n\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\n\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = Zone.__symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\n\n\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = Zone.__symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\n\n\nconst TRUE_STR = 'true';\n/** false string const */\n\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\n\nconst ZONE_SYMBOL_PREFIX = Zone.__symbol__('');\n\nfunction wrapWithCurrentZone(callback, source) {\n  return Zone.current.wrap(callback, source);\n}\n\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n  return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\n\nconst zoneSymbol = Zone.__symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\n\nconst _global = isWindowExists && internalWindow || typeof self === 'object' && self || global;\n\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\nconst NULL_ON_PROP_VALUE = [null];\n\nfunction bindArguments(args, source) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (typeof args[i] === 'function') {\n      args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n    }\n  }\n\n  return args;\n}\n\nfunction patchPrototype(prototype, fnNames) {\n  const source = prototype.constructor['name'];\n\n  for (let i = 0; i < fnNames.length; i++) {\n    const name = fnNames[i];\n    const delegate = prototype[name];\n\n    if (delegate) {\n      const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n\n      if (!isPropertyWritable(prototypeDesc)) {\n        continue;\n      }\n\n      prototype[name] = (delegate => {\n        const patched = function () {\n          return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n        };\n\n        attachOriginToPatched(patched, delegate);\n        return patched;\n      })(delegate);\n    }\n  }\n}\n\nfunction isPropertyWritable(propertyDesc) {\n  if (!propertyDesc) {\n    return true;\n  }\n\n  if (propertyDesc.writable === false) {\n    return false;\n  }\n\n  return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\n\nconst isWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope; // Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\n\nconst isNode = !('nw' in _global) && typeof _global.process !== 'undefined' && {}.toString.call(_global.process) === '[object process]';\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']); // we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\n\nconst isMix = typeof _global.process !== 'undefined' && {}.toString.call(_global.process) === '[object process]' && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames = {};\n\nconst wrapFn = function (event) {\n  // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n  // event will be undefined, so we need to use window.event\n  event = event || _global.event;\n\n  if (!event) {\n    return;\n  }\n\n  let eventNameSymbol = zoneSymbolEventNames[event.type];\n\n  if (!eventNameSymbol) {\n    eventNameSymbol = zoneSymbolEventNames[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n  }\n\n  const target = this || event.target || _global;\n  const listener = target[eventNameSymbol];\n  let result;\n\n  if (isBrowser && target === internalWindow && event.type === 'error') {\n    // window.onerror have different signiture\n    // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n    // and onerror callback will prevent default when callback return true\n    const errorEvent = event;\n    result = listener && listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n\n    if (result === true) {\n      event.preventDefault();\n    }\n  } else {\n    result = listener && listener.apply(this, arguments);\n\n    if (result != undefined && !result) {\n      event.preventDefault();\n    }\n  }\n\n  return result;\n};\n\nfunction patchProperty(obj, prop, prototype) {\n  let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n\n  if (!desc && prototype) {\n    // when patch window object, use prototype to check prop exist or not\n    const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n\n    if (prototypeDesc) {\n      desc = {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  } // if the descriptor not exists or is not configurable\n  // just return\n\n\n  if (!desc || !desc.configurable) {\n    return;\n  }\n\n  const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n\n  if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n    return;\n  } // A property descriptor cannot have getter/setter and be writable\n  // deleting the writable and value properties avoids this error:\n  //\n  // TypeError: property descriptors must not specify a value or be writable when a\n  // getter or setter has been specified\n\n\n  delete desc.writable;\n  delete desc.value;\n  const originalDescGet = desc.get;\n  const originalDescSet = desc.set; // substr(2) cuz 'onclick' -> 'click', etc\n\n  const eventName = prop.substr(2);\n  let eventNameSymbol = zoneSymbolEventNames[eventName];\n\n  if (!eventNameSymbol) {\n    eventNameSymbol = zoneSymbolEventNames[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n  }\n\n  desc.set = function (newValue) {\n    // in some of windows's onproperty callback, this is undefined\n    // so we need to check it\n    let target = this;\n\n    if (!target && obj === _global) {\n      target = _global;\n    }\n\n    if (!target) {\n      return;\n    }\n\n    let previousValue = target[eventNameSymbol];\n\n    if (previousValue) {\n      target.removeEventListener(eventName, wrapFn);\n    } // issue #978, when onload handler was added before loading zone.js\n    // we should remove it with originalDescSet\n\n\n    if (originalDescSet) {\n      originalDescSet.apply(target, NULL_ON_PROP_VALUE);\n    }\n\n    if (typeof newValue === 'function') {\n      target[eventNameSymbol] = newValue;\n      target.addEventListener(eventName, wrapFn, false);\n    } else {\n      target[eventNameSymbol] = null;\n    }\n  }; // The getter would return undefined for unassigned properties but the default value of an\n  // unassigned property is null\n\n\n  desc.get = function () {\n    // in some of windows's onproperty callback, this is undefined\n    // so we need to check it\n    let target = this;\n\n    if (!target && obj === _global) {\n      target = _global;\n    }\n\n    if (!target) {\n      return null;\n    }\n\n    const listener = target[eventNameSymbol];\n\n    if (listener) {\n      return listener;\n    } else if (originalDescGet) {\n      // result will be null when use inline event attribute,\n      // such as <button onclick=\"func();\">OK</button>\n      // because the onclick function is internal raw uncompiled handler\n      // the onclick will be evaluated when first time event was triggered or\n      // the property is accessed, https://github.com/angular/zone.js/issues/525\n      // so we should use original native get to retrieve the handler\n      let value = originalDescGet && originalDescGet.call(this);\n\n      if (value) {\n        desc.set.call(this, value);\n\n        if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n          target.removeAttribute(prop);\n        }\n\n        return value;\n      }\n    }\n\n    return null;\n  };\n\n  ObjectDefineProperty(obj, prop, desc);\n  obj[onPropPatchedSymbol] = true;\n}\n\nfunction patchOnProperties(obj, properties, prototype) {\n  if (properties) {\n    for (let i = 0; i < properties.length; i++) {\n      patchProperty(obj, 'on' + properties[i], prototype);\n    }\n  } else {\n    const onProperties = [];\n\n    for (const prop in obj) {\n      if (prop.substr(0, 2) == 'on') {\n        onProperties.push(prop);\n      }\n    }\n\n    for (let j = 0; j < onProperties.length; j++) {\n      patchProperty(obj, onProperties[j], prototype);\n    }\n  }\n}\n\nconst originalInstanceKey = zoneSymbol('originalInstance'); // wrap some native API on `window`\n\nfunction patchClass(className) {\n  const OriginalClass = _global[className];\n  if (!OriginalClass) return; // keep original class in global\n\n  _global[zoneSymbol(className)] = OriginalClass;\n\n  _global[className] = function () {\n    const a = bindArguments(arguments, className);\n\n    switch (a.length) {\n      case 0:\n        this[originalInstanceKey] = new OriginalClass();\n        break;\n\n      case 1:\n        this[originalInstanceKey] = new OriginalClass(a[0]);\n        break;\n\n      case 2:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n        break;\n\n      case 3:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n        break;\n\n      case 4:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n        break;\n\n      default:\n        throw new Error('Arg list too long.');\n    }\n  }; // attach original delegate to patched function\n\n\n  attachOriginToPatched(_global[className], OriginalClass);\n  const instance = new OriginalClass(function () {});\n  let prop;\n\n  for (prop in instance) {\n    // https://bugs.webkit.org/show_bug.cgi?id=44721\n    if (className === 'XMLHttpRequest' && prop === 'responseBlob') continue;\n\n    (function (prop) {\n      if (typeof instance[prop] === 'function') {\n        _global[className].prototype[prop] = function () {\n          return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n        };\n      } else {\n        ObjectDefineProperty(_global[className].prototype, prop, {\n          set: function (fn) {\n            if (typeof fn === 'function') {\n              this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop); // keep callback in wrapped function so we can\n              // use it in Function.prototype.toString to return\n              // the native one.\n\n              attachOriginToPatched(this[originalInstanceKey][prop], fn);\n            } else {\n              this[originalInstanceKey][prop] = fn;\n            }\n          },\n          get: function () {\n            return this[originalInstanceKey][prop];\n          }\n        });\n      }\n    })(prop);\n  }\n\n  for (prop in OriginalClass) {\n    if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n      _global[className][prop] = OriginalClass[prop];\n    }\n  }\n}\n\nfunction patchMethod(target, name, patchFn) {\n  let proto = target;\n\n  while (proto && !proto.hasOwnProperty(name)) {\n    proto = ObjectGetPrototypeOf(proto);\n  }\n\n  if (!proto && target[name]) {\n    // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n    proto = target;\n  }\n\n  const delegateName = zoneSymbol(name);\n  let delegate = null;\n\n  if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n    delegate = proto[delegateName] = proto[name]; // check whether proto[name] is writable\n    // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n\n    const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n\n    if (isPropertyWritable(desc)) {\n      const patchDelegate = patchFn(delegate, delegateName, name);\n\n      proto[name] = function () {\n        return patchDelegate(this, arguments);\n      };\n\n      attachOriginToPatched(proto[name], delegate);\n    }\n  }\n\n  return delegate;\n} // TODO: @JiaLiPassion, support cancel task later if necessary\n\n\nfunction patchMacroTask(obj, funcName, metaCreator) {\n  let setNative = null;\n\n  function scheduleTask(task) {\n    const data = task.data;\n\n    data.args[data.cbIdx] = function () {\n      task.invoke.apply(this, arguments);\n    };\n\n    setNative.apply(data.target, data.args);\n    return task;\n  }\n\n  setNative = patchMethod(obj, funcName, delegate => function (self, args) {\n    const meta = metaCreator(self, args);\n\n    if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n      return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(self, args);\n    }\n  });\n}\n\nfunction attachOriginToPatched(patched, original) {\n  patched[zoneSymbol('OriginalDelegate')] = original;\n}\n\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\n\nfunction isIE() {\n  try {\n    const ua = internalWindow.navigator.userAgent;\n\n    if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n      return true;\n    }\n  } catch (error) {}\n\n  return false;\n}\n\nfunction isIEOrEdge() {\n  if (isDetectedIEOrEdge) {\n    return ieOrEdge;\n  }\n\n  isDetectedIEOrEdge = true;\n\n  try {\n    const ua = internalWindow.navigator.userAgent;\n\n    if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n      ieOrEdge = true;\n    }\n  } catch (error) {}\n\n  return ieOrEdge;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nZone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n  const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n  const ObjectDefineProperty = Object.defineProperty;\n\n  function readableObjectToString(obj) {\n    if (obj && obj.toString === Object.prototype.toString) {\n      const className = obj.constructor && obj.constructor.name;\n      return (className ? className : '') + ': ' + JSON.stringify(obj);\n    }\n\n    return obj ? obj.toString() : Object.prototype.toString.call(obj);\n  }\n\n  const __symbol__ = api.symbol;\n  const _uncaughtPromiseErrors = [];\n  const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] === true;\n\n  const symbolPromise = __symbol__('Promise');\n\n  const symbolThen = __symbol__('then');\n\n  const creationTrace = '__creationTrace__';\n\n  api.onUnhandledError = e => {\n    if (api.showUncaughtError()) {\n      const rejection = e && e.rejection;\n\n      if (rejection) {\n        console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n      } else {\n        console.error(e);\n      }\n    }\n  };\n\n  api.microtaskDrainDone = () => {\n    while (_uncaughtPromiseErrors.length) {\n      const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n\n      try {\n        uncaughtPromiseError.zone.runGuarded(() => {\n          if (uncaughtPromiseError.throwOriginal) {\n            throw uncaughtPromiseError.rejection;\n          }\n\n          throw uncaughtPromiseError;\n        });\n      } catch (error) {\n        handleUnhandledRejection(error);\n      }\n    }\n  };\n\n  const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n\n  function handleUnhandledRejection(e) {\n    api.onUnhandledError(e);\n\n    try {\n      const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n\n      if (typeof handler === 'function') {\n        handler.call(this, e);\n      }\n    } catch (err) {}\n  }\n\n  function isThenable(value) {\n    return value && value.then;\n  }\n\n  function forwardResolution(value) {\n    return value;\n  }\n\n  function forwardRejection(rejection) {\n    return ZoneAwarePromise.reject(rejection);\n  }\n\n  const symbolState = __symbol__('state');\n\n  const symbolValue = __symbol__('value');\n\n  const symbolFinally = __symbol__('finally');\n\n  const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n\n  const symbolParentPromiseState = __symbol__('parentPromiseState');\n\n  const source = 'Promise.then';\n  const UNRESOLVED = null;\n  const RESOLVED = true;\n  const REJECTED = false;\n  const REJECTED_NO_CATCH = 0;\n\n  function makeResolver(promise, state) {\n    return v => {\n      try {\n        resolvePromise(promise, state, v);\n      } catch (err) {\n        resolvePromise(promise, false, err);\n      } // Do not return value or you will break the Promise spec.\n\n    };\n  }\n\n  const once = function () {\n    let wasCalled = false;\n    return function wrapper(wrappedFunction) {\n      return function () {\n        if (wasCalled) {\n          return;\n        }\n\n        wasCalled = true;\n        wrappedFunction.apply(null, arguments);\n      };\n    };\n  };\n\n  const TYPE_ERROR = 'Promise resolved with itself';\n\n  const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace'); // Promise Resolution\n\n\n  function resolvePromise(promise, state, value) {\n    const onceWrapper = once();\n\n    if (promise === value) {\n      throw new TypeError(TYPE_ERROR);\n    }\n\n    if (promise[symbolState] === UNRESOLVED) {\n      // should only get value.then once based on promise spec.\n      let then = null;\n\n      try {\n        if (typeof value === 'object' || typeof value === 'function') {\n          then = value && value.then;\n        }\n      } catch (err) {\n        onceWrapper(() => {\n          resolvePromise(promise, false, err);\n        })();\n        return promise;\n      } // if (value instanceof ZoneAwarePromise) {\n\n\n      if (state !== REJECTED && value instanceof ZoneAwarePromise && value.hasOwnProperty(symbolState) && value.hasOwnProperty(symbolValue) && value[symbolState] !== UNRESOLVED) {\n        clearRejectedNoCatch(value);\n        resolvePromise(promise, value[symbolState], value[symbolValue]);\n      } else if (state !== REJECTED && typeof then === 'function') {\n        try {\n          then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n        } catch (err) {\n          onceWrapper(() => {\n            resolvePromise(promise, false, err);\n          })();\n        }\n      } else {\n        promise[symbolState] = state;\n        const queue = promise[symbolValue];\n        promise[symbolValue] = value;\n\n        if (promise[symbolFinally] === symbolFinally) {\n          // the promise is generated by Promise.prototype.finally\n          if (state === RESOLVED) {\n            // the state is resolved, should ignore the value\n            // and use parent promise value\n            promise[symbolState] = promise[symbolParentPromiseState];\n            promise[symbolValue] = promise[symbolParentPromiseValue];\n          }\n        } // record task information in value when error occurs, so we can\n        // do some additional work such as render longStackTrace\n\n\n        if (state === REJECTED && value instanceof Error) {\n          // check if longStackTraceZone is here\n          const trace = Zone.currentTask && Zone.currentTask.data && Zone.currentTask.data[creationTrace];\n\n          if (trace) {\n            // only keep the long stack trace into error when in longStackTraceZone\n            ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, {\n              configurable: true,\n              enumerable: false,\n              writable: true,\n              value: trace\n            });\n          }\n        }\n\n        for (let i = 0; i < queue.length;) {\n          scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n        }\n\n        if (queue.length == 0 && state == REJECTED) {\n          promise[symbolState] = REJECTED_NO_CATCH;\n          let uncaughtPromiseError = value;\n\n          try {\n            // Here we throws a new Error to print more readable error log\n            // and if the value is not an error, zone.js builds an `Error`\n            // Object here to attach the stack information.\n            throw new Error('Uncaught (in promise): ' + readableObjectToString(value) + (value && value.stack ? '\\n' + value.stack : ''));\n          } catch (err) {\n            uncaughtPromiseError = err;\n          }\n\n          if (isDisableWrappingUncaughtPromiseRejection) {\n            // If disable wrapping uncaught promise reject\n            // use the value instead of wrapping it.\n            uncaughtPromiseError.throwOriginal = true;\n          }\n\n          uncaughtPromiseError.rejection = value;\n          uncaughtPromiseError.promise = promise;\n          uncaughtPromiseError.zone = Zone.current;\n          uncaughtPromiseError.task = Zone.currentTask;\n\n          _uncaughtPromiseErrors.push(uncaughtPromiseError);\n\n          api.scheduleMicroTask(); // to make sure that it is running\n        }\n      }\n    } // Resolving an already resolved promise is a noop.\n\n\n    return promise;\n  }\n\n  const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n\n  function clearRejectedNoCatch(promise) {\n    if (promise[symbolState] === REJECTED_NO_CATCH) {\n      // if the promise is rejected no catch status\n      // and queue.length > 0, means there is a error handler\n      // here to handle the rejected promise, we should trigger\n      // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n      // eventHandler\n      try {\n        const handler = Zone[REJECTION_HANDLED_HANDLER];\n\n        if (handler && typeof handler === 'function') {\n          handler.call(this, {\n            rejection: promise[symbolValue],\n            promise: promise\n          });\n        }\n      } catch (err) {}\n\n      promise[symbolState] = REJECTED;\n\n      for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n        if (promise === _uncaughtPromiseErrors[i].promise) {\n          _uncaughtPromiseErrors.splice(i, 1);\n        }\n      }\n    }\n  }\n\n  function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n    clearRejectedNoCatch(promise);\n    const promiseState = promise[symbolState];\n    const delegate = promiseState ? typeof onFulfilled === 'function' ? onFulfilled : forwardResolution : typeof onRejected === 'function' ? onRejected : forwardRejection;\n    zone.scheduleMicroTask(source, () => {\n      try {\n        const parentPromiseValue = promise[symbolValue];\n        const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n\n        if (isFinallyPromise) {\n          // if the promise is generated from finally call, keep parent promise's state and value\n          chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n          chainPromise[symbolParentPromiseState] = promiseState;\n        } // should not pass value to finally callback\n\n\n        const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution ? [] : [parentPromiseValue]);\n        resolvePromise(chainPromise, true, value);\n      } catch (error) {\n        // if error occurs, should always return this error\n        resolvePromise(chainPromise, false, error);\n      }\n    }, chainPromise);\n  }\n\n  const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n\n  const noop = function () {};\n\n  class ZoneAwarePromise {\n    static toString() {\n      return ZONE_AWARE_PROMISE_TO_STRING;\n    }\n\n    static resolve(value) {\n      return resolvePromise(new this(null), RESOLVED, value);\n    }\n\n    static reject(error) {\n      return resolvePromise(new this(null), REJECTED, error);\n    }\n\n    static race(values) {\n      let resolve;\n      let reject;\n      let promise = new this((res, rej) => {\n        resolve = res;\n        reject = rej;\n      });\n\n      function onResolve(value) {\n        resolve(value);\n      }\n\n      function onReject(error) {\n        reject(error);\n      }\n\n      for (let value of values) {\n        if (!isThenable(value)) {\n          value = this.resolve(value);\n        }\n\n        value.then(onResolve, onReject);\n      }\n\n      return promise;\n    }\n\n    static all(values) {\n      return ZoneAwarePromise.allWithCallback(values);\n    }\n\n    static allSettled(values) {\n      const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n      return P.allWithCallback(values, {\n        thenCallback: value => ({\n          status: 'fulfilled',\n          value\n        }),\n        errorCallback: err => ({\n          status: 'rejected',\n          reason: err\n        })\n      });\n    }\n\n    static allWithCallback(values, callback) {\n      let resolve;\n      let reject;\n      let promise = new this((res, rej) => {\n        resolve = res;\n        reject = rej;\n      }); // Start at 2 to prevent prematurely resolving if .then is called immediately.\n\n      let unresolvedCount = 2;\n      let valueIndex = 0;\n      const resolvedValues = [];\n\n      for (let value of values) {\n        if (!isThenable(value)) {\n          value = this.resolve(value);\n        }\n\n        const curValueIndex = valueIndex;\n\n        try {\n          value.then(value => {\n            resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n            unresolvedCount--;\n\n            if (unresolvedCount === 0) {\n              resolve(resolvedValues);\n            }\n          }, err => {\n            if (!callback) {\n              reject(err);\n            } else {\n              resolvedValues[curValueIndex] = callback.errorCallback(err);\n              unresolvedCount--;\n\n              if (unresolvedCount === 0) {\n                resolve(resolvedValues);\n              }\n            }\n          });\n        } catch (thenErr) {\n          reject(thenErr);\n        }\n\n        unresolvedCount++;\n        valueIndex++;\n      } // Make the unresolvedCount zero-based again.\n\n\n      unresolvedCount -= 2;\n\n      if (unresolvedCount === 0) {\n        resolve(resolvedValues);\n      }\n\n      return promise;\n    }\n\n    constructor(executor) {\n      const promise = this;\n\n      if (!(promise instanceof ZoneAwarePromise)) {\n        throw new Error('Must be an instanceof Promise.');\n      }\n\n      promise[symbolState] = UNRESOLVED;\n      promise[symbolValue] = []; // queue;\n\n      try {\n        executor && executor(makeResolver(promise, RESOLVED), makeResolver(promise, REJECTED));\n      } catch (error) {\n        resolvePromise(promise, false, error);\n      }\n    }\n\n    get [Symbol.toStringTag]() {\n      return 'Promise';\n    }\n\n    get [Symbol.species]() {\n      return ZoneAwarePromise;\n    }\n\n    then(onFulfilled, onRejected) {\n      let C = this.constructor[Symbol.species];\n\n      if (!C || typeof C !== 'function') {\n        C = this.constructor || ZoneAwarePromise;\n      }\n\n      const chainPromise = new C(noop);\n      const zone = Zone.current;\n\n      if (this[symbolState] == UNRESOLVED) {\n        this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n      } else {\n        scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n      }\n\n      return chainPromise;\n    }\n\n    catch(onRejected) {\n      return this.then(null, onRejected);\n    }\n\n    finally(onFinally) {\n      let C = this.constructor[Symbol.species];\n\n      if (!C || typeof C !== 'function') {\n        C = ZoneAwarePromise;\n      }\n\n      const chainPromise = new C(noop);\n      chainPromise[symbolFinally] = symbolFinally;\n      const zone = Zone.current;\n\n      if (this[symbolState] == UNRESOLVED) {\n        this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n      } else {\n        scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n      }\n\n      return chainPromise;\n    }\n\n  } // Protect against aggressive optimizers dropping seemingly unused properties.\n  // E.g. Closure Compiler in advanced mode.\n\n\n  ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n  ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n  ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n  ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n  const NativePromise = global[symbolPromise] = global['Promise'];\n  global['Promise'] = ZoneAwarePromise;\n\n  const symbolThenPatched = __symbol__('thenPatched');\n\n  function patchThen(Ctor) {\n    const proto = Ctor.prototype;\n    const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n\n    if (prop && (prop.writable === false || !prop.configurable)) {\n      // check Ctor.prototype.then propertyDescriptor is writable or not\n      // in meteor env, writable is false, we should ignore such case\n      return;\n    }\n\n    const originalThen = proto.then; // Keep a reference to the original method.\n\n    proto[symbolThen] = originalThen;\n\n    Ctor.prototype.then = function (onResolve, onReject) {\n      const wrapped = new ZoneAwarePromise((resolve, reject) => {\n        originalThen.call(this, resolve, reject);\n      });\n      return wrapped.then(onResolve, onReject);\n    };\n\n    Ctor[symbolThenPatched] = true;\n  }\n\n  api.patchThen = patchThen;\n\n  function zoneify(fn) {\n    return function (self, args) {\n      let resultPromise = fn.apply(self, args);\n\n      if (resultPromise instanceof ZoneAwarePromise) {\n        return resultPromise;\n      }\n\n      let ctor = resultPromise.constructor;\n\n      if (!ctor[symbolThenPatched]) {\n        patchThen(ctor);\n      }\n\n      return resultPromise;\n    };\n  }\n\n  if (NativePromise) {\n    patchThen(NativePromise);\n    patchMethod(global, 'fetch', delegate => zoneify(delegate));\n  } // This is not part of public API, but it is useful for tests, so we expose it.\n\n\n  Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n  return ZoneAwarePromise;\n});\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// override Function.prototype.toString to make zone.js patched function\n// look like native function\n\n\nZone.__load_patch('toString', global => {\n  // patch Func.prototype.toString to let them look like native\n  const originalFunctionToString = Function.prototype.toString;\n  const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n  const PROMISE_SYMBOL = zoneSymbol('Promise');\n  const ERROR_SYMBOL = zoneSymbol('Error');\n\n  const newFunctionToString = function toString() {\n    if (typeof this === 'function') {\n      const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n\n      if (originalDelegate) {\n        if (typeof originalDelegate === 'function') {\n          return originalFunctionToString.call(originalDelegate);\n        } else {\n          return Object.prototype.toString.call(originalDelegate);\n        }\n      }\n\n      if (this === Promise) {\n        const nativePromise = global[PROMISE_SYMBOL];\n\n        if (nativePromise) {\n          return originalFunctionToString.call(nativePromise);\n        }\n      }\n\n      if (this === Error) {\n        const nativeError = global[ERROR_SYMBOL];\n\n        if (nativeError) {\n          return originalFunctionToString.call(nativeError);\n        }\n      }\n    }\n\n    return originalFunctionToString.call(this);\n  };\n\n  newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n  Function.prototype.toString = newFunctionToString; // patch Object.prototype.toString to let them look like native\n\n  const originalObjectToString = Object.prototype.toString;\n  const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n\n  Object.prototype.toString = function () {\n    if (typeof Promise === 'function' && this instanceof Promise) {\n      return PROMISE_OBJECT_TO_STRING;\n    }\n\n    return originalObjectToString.call(this);\n  };\n});\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet passiveSupported = false;\n\nif (typeof window !== 'undefined') {\n  try {\n    const options = Object.defineProperty({}, 'passive', {\n      get: function () {\n        passiveSupported = true;\n      }\n    });\n    window.addEventListener('test', options, options);\n    window.removeEventListener('test', options, options);\n  } catch (err) {\n    passiveSupported = false;\n  }\n} // an identifier to tell ZoneTask do not create a new invoke closure\n\n\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n  useG: true\n};\nconst zoneSymbolEventNames$1 = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\n\nfunction prepareEventNames(eventName, eventNameToString) {\n  const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n  const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n  const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n  const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n  zoneSymbolEventNames$1[eventName] = {};\n  zoneSymbolEventNames$1[eventName][FALSE_STR] = symbol;\n  zoneSymbolEventNames$1[eventName][TRUE_STR] = symbolCapture;\n}\n\nfunction patchEventTarget(_global, apis, patchOptions) {\n  const ADD_EVENT_LISTENER = patchOptions && patchOptions.add || ADD_EVENT_LISTENER_STR;\n  const REMOVE_EVENT_LISTENER = patchOptions && patchOptions.rm || REMOVE_EVENT_LISTENER_STR;\n  const LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.listeners || 'eventListeners';\n  const REMOVE_ALL_LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.rmAll || 'removeAllListeners';\n  const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n  const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n  const PREPEND_EVENT_LISTENER = 'prependListener';\n  const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n\n  const invokeTask = function (task, target, event) {\n    // for better performance, check isRemoved which is set\n    // by removeEventListener\n    if (task.isRemoved) {\n      return;\n    }\n\n    const delegate = task.callback;\n\n    if (typeof delegate === 'object' && delegate.handleEvent) {\n      // create the bind version of handleEvent when invoke\n      task.callback = event => delegate.handleEvent(event);\n\n      task.originalDelegate = delegate;\n    } // invoke static task.invoke\n\n\n    task.invoke(task, target, [event]);\n    const options = task.options;\n\n    if (options && typeof options === 'object' && options.once) {\n      // if options.once is true, after invoke once remove listener here\n      // only browser need to do this, nodejs eventEmitter will cal removeListener\n      // inside EventEmitter.once\n      const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n      target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n    }\n  }; // global shared zoneAwareCallback to handle all event callback with capture = false\n\n\n  const globalZoneAwareCallback = function (event) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n\n    if (!event) {\n      return;\n    } // event.target is needed for Samsung TV and SourceBuffer\n    // || global is needed https://github.com/angular/zone.js/issues/190\n\n\n    const target = this || event.target || _global;\n    const tasks = target[zoneSymbolEventNames$1[event.type][FALSE_STR]];\n\n    if (tasks) {\n      // invoke all tasks which attached to current target with given event.type and capture = false\n      // for performance concern, if task.length === 1, just invoke\n      if (tasks.length === 1) {\n        invokeTask(tasks[0], target, event);\n      } else {\n        // https://github.com/angular/zone.js/issues/836\n        // copy the tasks array before invoke, to avoid\n        // the callback will remove itself or other listener\n        const copyTasks = tasks.slice();\n\n        for (let i = 0; i < copyTasks.length; i++) {\n          if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n            break;\n          }\n\n          invokeTask(copyTasks[i], target, event);\n        }\n      }\n    }\n  }; // global shared zoneAwareCallback to handle all event callback with capture = true\n\n\n  const globalZoneAwareCaptureCallback = function (event) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n\n    if (!event) {\n      return;\n    } // event.target is needed for Samsung TV and SourceBuffer\n    // || global is needed https://github.com/angular/zone.js/issues/190\n\n\n    const target = this || event.target || _global;\n    const tasks = target[zoneSymbolEventNames$1[event.type][TRUE_STR]];\n\n    if (tasks) {\n      // invoke all tasks which attached to current target with given event.type and capture = false\n      // for performance concern, if task.length === 1, just invoke\n      if (tasks.length === 1) {\n        invokeTask(tasks[0], target, event);\n      } else {\n        // https://github.com/angular/zone.js/issues/836\n        // copy the tasks array before invoke, to avoid\n        // the callback will remove itself or other listener\n        const copyTasks = tasks.slice();\n\n        for (let i = 0; i < copyTasks.length; i++) {\n          if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n            break;\n          }\n\n          invokeTask(copyTasks[i], target, event);\n        }\n      }\n    }\n  };\n\n  function patchEventTargetMethods(obj, patchOptions) {\n    if (!obj) {\n      return false;\n    }\n\n    let useGlobalCallback = true;\n\n    if (patchOptions && patchOptions.useG !== undefined) {\n      useGlobalCallback = patchOptions.useG;\n    }\n\n    const validateHandler = patchOptions && patchOptions.vh;\n    let checkDuplicate = true;\n\n    if (patchOptions && patchOptions.chkDup !== undefined) {\n      checkDuplicate = patchOptions.chkDup;\n    }\n\n    let returnTarget = false;\n\n    if (patchOptions && patchOptions.rt !== undefined) {\n      returnTarget = patchOptions.rt;\n    }\n\n    let proto = obj;\n\n    while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n      proto = ObjectGetPrototypeOf(proto);\n    }\n\n    if (!proto && obj[ADD_EVENT_LISTENER]) {\n      // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n      proto = obj;\n    }\n\n    if (!proto) {\n      return false;\n    }\n\n    if (proto[zoneSymbolAddEventListener]) {\n      return false;\n    }\n\n    const eventNameToString = patchOptions && patchOptions.eventNameToString; // a shared global taskData to pass data for scheduleEventTask\n    // so we do not need to create a new object just for pass some data\n\n    const taskData = {};\n    const nativeAddEventListener = proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER];\n    const nativeRemoveEventListener = proto[zoneSymbol(REMOVE_EVENT_LISTENER)] = proto[REMOVE_EVENT_LISTENER];\n    const nativeListeners = proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] = proto[LISTENERS_EVENT_LISTENER];\n    const nativeRemoveAllListeners = proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] = proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];\n    let nativePrependEventListener;\n\n    if (patchOptions && patchOptions.prepend) {\n      nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] = proto[patchOptions.prepend];\n    }\n    /**\n     * This util function will build an option object with passive option\n     * to handle all possible input from the user.\n     */\n\n\n    function buildEventListenerOptions(options, passive) {\n      if (!passiveSupported && typeof options === 'object' && options) {\n        // doesn't support passive but user want to pass an object as options.\n        // this will not work on some old browser, so we just pass a boolean\n        // as useCapture parameter\n        return !!options.capture;\n      }\n\n      if (!passiveSupported || !passive) {\n        return options;\n      }\n\n      if (typeof options === 'boolean') {\n        return {\n          capture: options,\n          passive: true\n        };\n      }\n\n      if (!options) {\n        return {\n          passive: true\n        };\n      }\n\n      if (typeof options === 'object' && options.passive !== false) {\n        return Object.assign(Object.assign({}, options), {\n          passive: true\n        });\n      }\n\n      return options;\n    }\n\n    const customScheduleGlobal = function (task) {\n      // if there is already a task for the eventName + capture,\n      // just return, because we use the shared globalZoneAwareCallback here.\n      if (taskData.isExisting) {\n        return;\n      }\n\n      return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n    };\n\n    const customCancelGlobal = function (task) {\n      // if task is not marked as isRemoved, this call is directly\n      // from Zone.prototype.cancelTask, we should remove the task\n      // from tasksList of target first\n      if (!task.isRemoved) {\n        const symbolEventNames = zoneSymbolEventNames$1[task.eventName];\n        let symbolEventName;\n\n        if (symbolEventNames) {\n          symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n        }\n\n        const existingTasks = symbolEventName && task.target[symbolEventName];\n\n        if (existingTasks) {\n          for (let i = 0; i < existingTasks.length; i++) {\n            const existingTask = existingTasks[i];\n\n            if (existingTask === task) {\n              existingTasks.splice(i, 1); // set isRemoved to data for faster invokeTask check\n\n              task.isRemoved = true;\n\n              if (existingTasks.length === 0) {\n                // all tasks for the eventName + capture have gone,\n                // remove globalZoneAwareCallback and remove the task cache from target\n                task.allRemoved = true;\n                task.target[symbolEventName] = null;\n              }\n\n              break;\n            }\n          }\n        }\n      } // if all tasks for the eventName + capture have gone,\n      // we will really remove the global event callback,\n      // if not, return\n\n\n      if (!task.allRemoved) {\n        return;\n      }\n\n      return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n    };\n\n    const customScheduleNonGlobal = function (task) {\n      return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n    };\n\n    const customSchedulePrepend = function (task) {\n      return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n    };\n\n    const customCancelNonGlobal = function (task) {\n      return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n    };\n\n    const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n    const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n\n    const compareTaskCallbackVsDelegate = function (task, delegate) {\n      const typeOfDelegate = typeof delegate;\n      return typeOfDelegate === 'function' && task.callback === delegate || typeOfDelegate === 'object' && task.originalDelegate === delegate;\n    };\n\n    const compare = patchOptions && patchOptions.diff ? patchOptions.diff : compareTaskCallbackVsDelegate;\n    const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n\n    const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n\n    const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n      return function () {\n        const target = this || _global;\n        let eventName = arguments[0];\n\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n\n        let delegate = arguments[1];\n\n        if (!delegate) {\n          return nativeListener.apply(this, arguments);\n        }\n\n        if (isNode && eventName === 'uncaughtException') {\n          // don't patch uncaughtException of nodejs to prevent endless loop\n          return nativeListener.apply(this, arguments);\n        } // don't create the bind delegate function for handleEvent\n        // case here to improve addEventListener performance\n        // we will create the bind delegate when invoke\n\n\n        let isHandleEvent = false;\n\n        if (typeof delegate !== 'function') {\n          if (!delegate.handleEvent) {\n            return nativeListener.apply(this, arguments);\n          }\n\n          isHandleEvent = true;\n        }\n\n        if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n          return;\n        }\n\n        const passive = passiveSupported && !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n        const options = buildEventListenerOptions(arguments[2], passive);\n\n        if (unpatchedEvents) {\n          // check upatched list\n          for (let i = 0; i < unpatchedEvents.length; i++) {\n            if (eventName === unpatchedEvents[i]) {\n              if (passive) {\n                return nativeListener.call(target, eventName, delegate, options);\n              } else {\n                return nativeListener.apply(this, arguments);\n              }\n            }\n          }\n        }\n\n        const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n        const once = options && typeof options === 'object' ? options.once : false;\n        const zone = Zone.current;\n        let symbolEventNames = zoneSymbolEventNames$1[eventName];\n\n        if (!symbolEventNames) {\n          prepareEventNames(eventName, eventNameToString);\n          symbolEventNames = zoneSymbolEventNames$1[eventName];\n        }\n\n        const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n        let existingTasks = target[symbolEventName];\n        let isExisting = false;\n\n        if (existingTasks) {\n          // already have task registered\n          isExisting = true;\n\n          if (checkDuplicate) {\n            for (let i = 0; i < existingTasks.length; i++) {\n              if (compare(existingTasks[i], delegate)) {\n                // same callback, same capture, same event name, just return\n                return;\n              }\n            }\n          }\n        } else {\n          existingTasks = target[symbolEventName] = [];\n        }\n\n        let source;\n        const constructorName = target.constructor['name'];\n        const targetSource = globalSources[constructorName];\n\n        if (targetSource) {\n          source = targetSource[eventName];\n        }\n\n        if (!source) {\n          source = constructorName + addSource + (eventNameToString ? eventNameToString(eventName) : eventName);\n        } // do not create a new object as task.data to pass those things\n        // just use the global shared one\n\n\n        taskData.options = options;\n\n        if (once) {\n          // if addEventListener with once options, we don't pass it to\n          // native addEventListener, instead we keep the once setting\n          // and handle ourselves.\n          taskData.options.once = false;\n        }\n\n        taskData.target = target;\n        taskData.capture = capture;\n        taskData.eventName = eventName;\n        taskData.isExisting = isExisting;\n        const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined; // keep taskData into data to allow onScheduleEventTask to access the task information\n\n        if (data) {\n          data.taskData = taskData;\n        }\n\n        const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn); // should clear taskData.target to avoid memory leak\n        // issue, https://github.com/angular/angular/issues/20442\n\n        taskData.target = null; // need to clear up taskData because it is a global object\n\n        if (data) {\n          data.taskData = null;\n        } // have to save those information to task in case\n        // application may call task.zone.cancelTask() directly\n\n\n        if (once) {\n          options.once = true;\n        }\n\n        if (!(!passiveSupported && typeof task.options === 'boolean')) {\n          // if not support passive, and we pass an option object\n          // to addEventListener, we should save the options to task\n          task.options = options;\n        }\n\n        task.target = target;\n        task.capture = capture;\n        task.eventName = eventName;\n\n        if (isHandleEvent) {\n          // save original delegate for compare to check duplicate\n          task.originalDelegate = delegate;\n        }\n\n        if (!prepend) {\n          existingTasks.push(task);\n        } else {\n          existingTasks.unshift(task);\n        }\n\n        if (returnTarget) {\n          return target;\n        }\n      };\n    };\n\n    proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n\n    if (nativePrependEventListener) {\n      proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n    }\n\n    proto[REMOVE_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n\n      if (patchOptions && patchOptions.transferEventName) {\n        eventName = patchOptions.transferEventName(eventName);\n      }\n\n      const options = arguments[2];\n      const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n      const delegate = arguments[1];\n\n      if (!delegate) {\n        return nativeRemoveEventListener.apply(this, arguments);\n      }\n\n      if (validateHandler && !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n        return;\n      }\n\n      const symbolEventNames = zoneSymbolEventNames$1[eventName];\n      let symbolEventName;\n\n      if (symbolEventNames) {\n        symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n      }\n\n      const existingTasks = symbolEventName && target[symbolEventName];\n\n      if (existingTasks) {\n        for (let i = 0; i < existingTasks.length; i++) {\n          const existingTask = existingTasks[i];\n\n          if (compare(existingTask, delegate)) {\n            existingTasks.splice(i, 1); // set isRemoved to data for faster invokeTask check\n\n            existingTask.isRemoved = true;\n\n            if (existingTasks.length === 0) {\n              // all tasks for the eventName + capture have gone,\n              // remove globalZoneAwareCallback and remove the task cache from target\n              existingTask.allRemoved = true;\n              target[symbolEventName] = null; // in the target, we have an event listener which is added by on_property\n              // such as target.onclick = function() {}, so we need to clear this internal\n              // property too if all delegates all removed\n\n              if (typeof eventName === 'string') {\n                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                target[onPropertySymbol] = null;\n              }\n            }\n\n            existingTask.zone.cancelTask(existingTask);\n\n            if (returnTarget) {\n              return target;\n            }\n\n            return;\n          }\n        }\n      } // issue 930, didn't find the event name or callback\n      // from zone kept existingTasks, the callback maybe\n      // added outside of zone, we need to call native removeEventListener\n      // to try to remove it.\n\n\n      return nativeRemoveEventListener.apply(this, arguments);\n    };\n\n    proto[LISTENERS_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n\n      if (patchOptions && patchOptions.transferEventName) {\n        eventName = patchOptions.transferEventName(eventName);\n      }\n\n      const listeners = [];\n      const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n\n      for (let i = 0; i < tasks.length; i++) {\n        const task = tasks[i];\n        let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n        listeners.push(delegate);\n      }\n\n      return listeners;\n    };\n\n    proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n\n      if (!eventName) {\n        const keys = Object.keys(target);\n\n        for (let i = 0; i < keys.length; i++) {\n          const prop = keys[i];\n          const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n          let evtName = match && match[1]; // in nodejs EventEmitter, removeListener event is\n          // used for monitoring the removeListener call,\n          // so just keep removeListener eventListener until\n          // all other eventListeners are removed\n\n          if (evtName && evtName !== 'removeListener') {\n            this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n          }\n        } // remove removeListener listener finally\n\n\n        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n      } else {\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n\n        const symbolEventNames = zoneSymbolEventNames$1[eventName];\n\n        if (symbolEventNames) {\n          const symbolEventName = symbolEventNames[FALSE_STR];\n          const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n          const tasks = target[symbolEventName];\n          const captureTasks = target[symbolCaptureEventName];\n\n          if (tasks) {\n            const removeTasks = tasks.slice();\n\n            for (let i = 0; i < removeTasks.length; i++) {\n              const task = removeTasks[i];\n              let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n              this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n            }\n          }\n\n          if (captureTasks) {\n            const removeTasks = captureTasks.slice();\n\n            for (let i = 0; i < removeTasks.length; i++) {\n              const task = removeTasks[i];\n              let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n              this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n            }\n          }\n        }\n      }\n\n      if (returnTarget) {\n        return this;\n      }\n    }; // for native toString patch\n\n\n    attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n    attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n\n    if (nativeRemoveAllListeners) {\n      attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n    }\n\n    if (nativeListeners) {\n      attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n    }\n\n    return true;\n  }\n\n  let results = [];\n\n  for (let i = 0; i < apis.length; i++) {\n    results[i] = patchEventTargetMethods(apis[i], patchOptions);\n  }\n\n  return results;\n}\n\nfunction findEventTasks(target, eventName) {\n  if (!eventName) {\n    const foundTasks = [];\n\n    for (let prop in target) {\n      const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n      let evtName = match && match[1];\n\n      if (evtName && (!eventName || evtName === eventName)) {\n        const tasks = target[prop];\n\n        if (tasks) {\n          for (let i = 0; i < tasks.length; i++) {\n            foundTasks.push(tasks[i]);\n          }\n        }\n      }\n    }\n\n    return foundTasks;\n  }\n\n  let symbolEventName = zoneSymbolEventNames$1[eventName];\n\n  if (!symbolEventName) {\n    prepareEventNames(eventName);\n    symbolEventName = zoneSymbolEventNames$1[eventName];\n  }\n\n  const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n  const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n\n  if (!captureFalseTasks) {\n    return captureTrueTasks ? captureTrueTasks.slice() : [];\n  } else {\n    return captureTrueTasks ? captureFalseTasks.concat(captureTrueTasks) : captureFalseTasks.slice();\n  }\n}\n\nfunction patchEventPrototype(global, api) {\n  const Event = global['Event'];\n\n  if (Event && Event.prototype) {\n    api.patchMethod(Event.prototype, 'stopImmediatePropagation', delegate => function (self, args) {\n      self[IMMEDIATE_PROPAGATION_SYMBOL] = true; // we need to call the native stopImmediatePropagation\n      // in case in some hybrid application, some part of\n      // application will be controlled by zone, some are not\n\n      delegate && delegate.apply(self, args);\n    });\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n  const symbol = Zone.__symbol__(method);\n\n  if (target[symbol]) {\n    return;\n  }\n\n  const nativeDelegate = target[symbol] = target[method];\n\n  target[method] = function (name, opts, options) {\n    if (opts && opts.prototype) {\n      callbacks.forEach(function (callback) {\n        const source = `${targetName}.${method}::` + callback;\n        const prototype = opts.prototype;\n\n        if (prototype.hasOwnProperty(callback)) {\n          const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n\n          if (descriptor && descriptor.value) {\n            descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n\n            api._redefineProperty(opts.prototype, callback, descriptor);\n          } else if (prototype[callback]) {\n            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n          }\n        } else if (prototype[callback]) {\n          prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n        }\n      });\n    }\n\n    return nativeDelegate.call(target, name, opts, options);\n  };\n\n  api.attachOriginToPatched(target[method], nativeDelegate);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst globalEventHandlersEventNames = ['abort', 'animationcancel', 'animationend', 'animationiteration', 'auxclick', 'beforeinput', 'blur', 'cancel', 'canplay', 'canplaythrough', 'change', 'compositionstart', 'compositionupdate', 'compositionend', 'cuechange', 'click', 'close', 'contextmenu', 'curechange', 'dblclick', 'drag', 'dragend', 'dragenter', 'dragexit', 'dragleave', 'dragover', 'drop', 'durationchange', 'emptied', 'ended', 'error', 'focus', 'focusin', 'focusout', 'gotpointercapture', 'input', 'invalid', 'keydown', 'keypress', 'keyup', 'load', 'loadstart', 'loadeddata', 'loadedmetadata', 'lostpointercapture', 'mousedown', 'mouseenter', 'mouseleave', 'mousemove', 'mouseout', 'mouseover', 'mouseup', 'mousewheel', 'orientationchange', 'pause', 'play', 'playing', 'pointercancel', 'pointerdown', 'pointerenter', 'pointerleave', 'pointerlockchange', 'mozpointerlockchange', 'webkitpointerlockerchange', 'pointerlockerror', 'mozpointerlockerror', 'webkitpointerlockerror', 'pointermove', 'pointout', 'pointerover', 'pointerup', 'progress', 'ratechange', 'reset', 'resize', 'scroll', 'seeked', 'seeking', 'select', 'selectionchange', 'selectstart', 'show', 'sort', 'stalled', 'submit', 'suspend', 'timeupdate', 'volumechange', 'touchcancel', 'touchmove', 'touchstart', 'touchend', 'transitioncancel', 'transitionend', 'waiting', 'wheel'];\nconst documentEventNames = ['afterscriptexecute', 'beforescriptexecute', 'DOMContentLoaded', 'freeze', 'fullscreenchange', 'mozfullscreenchange', 'webkitfullscreenchange', 'msfullscreenchange', 'fullscreenerror', 'mozfullscreenerror', 'webkitfullscreenerror', 'msfullscreenerror', 'readystatechange', 'visibilitychange', 'resume'];\nconst windowEventNames = ['absolutedeviceorientation', 'afterinput', 'afterprint', 'appinstalled', 'beforeinstallprompt', 'beforeprint', 'beforeunload', 'devicelight', 'devicemotion', 'deviceorientation', 'deviceorientationabsolute', 'deviceproximity', 'hashchange', 'languagechange', 'message', 'mozbeforepaint', 'offline', 'online', 'paint', 'pageshow', 'pagehide', 'popstate', 'rejectionhandled', 'storage', 'unhandledrejection', 'unload', 'userproximity', 'vrdisplayconnected', 'vrdisplaydisconnected', 'vrdisplaypresentchange'];\nconst htmlElementEventNames = ['beforecopy', 'beforecut', 'beforepaste', 'copy', 'cut', 'paste', 'dragstart', 'loadend', 'animationstart', 'search', 'transitionrun', 'transitionstart', 'webkitanimationend', 'webkitanimationiteration', 'webkitanimationstart', 'webkittransitionend'];\nconst mediaElementEventNames = ['encrypted', 'waitingforkey', 'msneedkey', 'mozinterruptbegin', 'mozinterruptend'];\nconst ieElementEventNames = ['activate', 'afterupdate', 'ariarequest', 'beforeactivate', 'beforedeactivate', 'beforeeditfocus', 'beforeupdate', 'cellchange', 'controlselect', 'dataavailable', 'datasetchanged', 'datasetcomplete', 'errorupdate', 'filterchange', 'layoutcomplete', 'losecapture', 'move', 'moveend', 'movestart', 'propertychange', 'resizeend', 'resizestart', 'rowenter', 'rowexit', 'rowsdelete', 'rowsinserted', 'command', 'compassneedscalibration', 'deactivate', 'help', 'mscontentzoom', 'msmanipulationstatechanged', 'msgesturechange', 'msgesturedoubletap', 'msgestureend', 'msgesturehold', 'msgesturestart', 'msgesturetap', 'msgotpointercapture', 'msinertiastart', 'mslostpointercapture', 'mspointercancel', 'mspointerdown', 'mspointerenter', 'mspointerhover', 'mspointerleave', 'mspointermove', 'mspointerout', 'mspointerover', 'mspointerup', 'pointerout', 'mssitemodejumplistitemremoved', 'msthumbnailclick', 'stop', 'storagecommit'];\nconst webglEventNames = ['webglcontextrestored', 'webglcontextlost', 'webglcontextcreationerror'];\nconst formEventNames = ['autocomplete', 'autocompleteerror'];\nconst detailEventNames = ['toggle'];\nconst frameEventNames = ['load'];\nconst frameSetEventNames = ['blur', 'error', 'focus', 'load', 'resize', 'scroll', 'messageerror'];\nconst marqueeEventNames = ['bounce', 'finish', 'start'];\nconst XMLHttpRequestEventNames = ['loadstart', 'progress', 'abort', 'error', 'load', 'progress', 'timeout', 'loadend', 'readystatechange'];\nconst IDBIndexEventNames = ['upgradeneeded', 'complete', 'abort', 'success', 'error', 'blocked', 'versionchange', 'close'];\nconst websocketEventNames = ['close', 'error', 'open', 'message'];\nconst workerEventNames = ['error', 'message'];\nconst eventNames = globalEventHandlersEventNames.concat(webglEventNames, formEventNames, detailEventNames, documentEventNames, windowEventNames, htmlElementEventNames, ieElementEventNames);\n\nfunction filterProperties(target, onProperties, ignoreProperties) {\n  if (!ignoreProperties || ignoreProperties.length === 0) {\n    return onProperties;\n  }\n\n  const tip = ignoreProperties.filter(ip => ip.target === target);\n\n  if (!tip || tip.length === 0) {\n    return onProperties;\n  }\n\n  const targetIgnoreProperties = tip[0].ignoreProperties;\n  return onProperties.filter(op => targetIgnoreProperties.indexOf(op) === -1);\n}\n\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n  // check whether target is available, sometimes target will be undefined\n  // because different browser or some 3rd party plugin.\n  if (!target) {\n    return;\n  }\n\n  const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n  patchOnProperties(target, filteredProperties, prototype);\n}\n\nfunction propertyDescriptorPatch(api, _global) {\n  if (isNode && !isMix) {\n    return;\n  }\n\n  if (Zone[api.symbol('patchEvents')]) {\n    // events are already been patched by legacy patch.\n    return;\n  }\n\n  const supportsWebSocket = typeof WebSocket !== 'undefined';\n  const ignoreProperties = _global['__Zone_ignore_on_properties']; // for browsers that we can patch the descriptor:  Chrome & Firefox\n\n  if (isBrowser) {\n    const internalWindow = window;\n    const ignoreErrorProperties = isIE() ? [{\n      target: internalWindow,\n      ignoreProperties: ['error']\n    }] : []; // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n    // so we need to pass WindowPrototype to check onProp exist or not\n\n    patchFilteredProperties(internalWindow, eventNames.concat(['messageerror']), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n    patchFilteredProperties(Document.prototype, eventNames, ignoreProperties);\n\n    if (typeof internalWindow['SVGElement'] !== 'undefined') {\n      patchFilteredProperties(internalWindow['SVGElement'].prototype, eventNames, ignoreProperties);\n    }\n\n    patchFilteredProperties(Element.prototype, eventNames, ignoreProperties);\n    patchFilteredProperties(HTMLElement.prototype, eventNames, ignoreProperties);\n    patchFilteredProperties(HTMLMediaElement.prototype, mediaElementEventNames, ignoreProperties);\n    patchFilteredProperties(HTMLFrameSetElement.prototype, windowEventNames.concat(frameSetEventNames), ignoreProperties);\n    patchFilteredProperties(HTMLBodyElement.prototype, windowEventNames.concat(frameSetEventNames), ignoreProperties);\n    patchFilteredProperties(HTMLFrameElement.prototype, frameEventNames, ignoreProperties);\n    patchFilteredProperties(HTMLIFrameElement.prototype, frameEventNames, ignoreProperties);\n    const HTMLMarqueeElement = internalWindow['HTMLMarqueeElement'];\n\n    if (HTMLMarqueeElement) {\n      patchFilteredProperties(HTMLMarqueeElement.prototype, marqueeEventNames, ignoreProperties);\n    }\n\n    const Worker = internalWindow['Worker'];\n\n    if (Worker) {\n      patchFilteredProperties(Worker.prototype, workerEventNames, ignoreProperties);\n    }\n  }\n\n  const XMLHttpRequest = _global['XMLHttpRequest'];\n\n  if (XMLHttpRequest) {\n    // XMLHttpRequest is not available in ServiceWorker, so we need to check here\n    patchFilteredProperties(XMLHttpRequest.prototype, XMLHttpRequestEventNames, ignoreProperties);\n  }\n\n  const XMLHttpRequestEventTarget = _global['XMLHttpRequestEventTarget'];\n\n  if (XMLHttpRequestEventTarget) {\n    patchFilteredProperties(XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype, XMLHttpRequestEventNames, ignoreProperties);\n  }\n\n  if (typeof IDBIndex !== 'undefined') {\n    patchFilteredProperties(IDBIndex.prototype, IDBIndexEventNames, ignoreProperties);\n    patchFilteredProperties(IDBRequest.prototype, IDBIndexEventNames, ignoreProperties);\n    patchFilteredProperties(IDBOpenDBRequest.prototype, IDBIndexEventNames, ignoreProperties);\n    patchFilteredProperties(IDBDatabase.prototype, IDBIndexEventNames, ignoreProperties);\n    patchFilteredProperties(IDBTransaction.prototype, IDBIndexEventNames, ignoreProperties);\n    patchFilteredProperties(IDBCursor.prototype, IDBIndexEventNames, ignoreProperties);\n  }\n\n  if (supportsWebSocket) {\n    patchFilteredProperties(WebSocket.prototype, websocketEventNames, ignoreProperties);\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nZone.__load_patch('util', (global, Zone, api) => {\n  api.patchOnProperties = patchOnProperties;\n  api.patchMethod = patchMethod;\n  api.bindArguments = bindArguments;\n  api.patchMacroTask = patchMacroTask; // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS` to\n  // define which events will not be patched by `Zone.js`.\n  // In newer version (>=0.9.0), we change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep\n  // the name consistent with angular repo.\n  // The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be supported for\n  // backwards compatibility.\n\n  const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n\n  const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n\n  if (global[SYMBOL_UNPATCHED_EVENTS]) {\n    global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n  }\n\n  if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n    Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] = global[SYMBOL_BLACK_LISTED_EVENTS];\n  }\n\n  api.patchEventPrototype = patchEventPrototype;\n  api.patchEventTarget = patchEventTarget;\n  api.isIEOrEdge = isIEOrEdge;\n  api.ObjectDefineProperty = ObjectDefineProperty;\n  api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n  api.ObjectCreate = ObjectCreate;\n  api.ArraySlice = ArraySlice;\n  api.patchClass = patchClass;\n  api.wrapWithCurrentZone = wrapWithCurrentZone;\n  api.filterProperties = filterProperties;\n  api.attachOriginToPatched = attachOriginToPatched;\n  api._redefineProperty = Object.defineProperty;\n  api.patchCallbacks = patchCallbacks;\n\n  api.getGlobalObjects = () => ({\n    globalSources,\n    zoneSymbolEventNames: zoneSymbolEventNames$1,\n    eventNames,\n    isBrowser,\n    isMix,\n    isNode,\n    TRUE_STR,\n    FALSE_STR,\n    ZONE_SYMBOL_PREFIX,\n    ADD_EVENT_LISTENER_STR,\n    REMOVE_EVENT_LISTENER_STR\n  });\n});\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst taskSymbol = zoneSymbol('zoneTask');\n\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n  let setNative = null;\n  let clearNative = null;\n  setName += nameSuffix;\n  cancelName += nameSuffix;\n  const tasksByHandleId = {};\n\n  function scheduleTask(task) {\n    const data = task.data;\n\n    data.args[0] = function () {\n      return task.invoke.apply(this, arguments);\n    };\n\n    data.handleId = setNative.apply(window, data.args);\n    return task;\n  }\n\n  function clearTask(task) {\n    return clearNative.call(window, task.data.handleId);\n  }\n\n  setNative = patchMethod(window, setName, delegate => function (self, args) {\n    if (typeof args[0] === 'function') {\n      const options = {\n        isPeriodic: nameSuffix === 'Interval',\n        delay: nameSuffix === 'Timeout' || nameSuffix === 'Interval' ? args[1] || 0 : undefined,\n        args: args\n      };\n      const callback = args[0];\n\n      args[0] = function timer() {\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          // issue-934, task will be cancelled\n          // even it is a periodic task such as\n          // setInterval\n          // https://github.com/angular/angular/issues/40387\n          // Cleanup tasksByHandleId should be handled before scheduleTask\n          // Since some zoneSpec may intercept and doesn't trigger\n          // scheduleFn(scheduleTask) provided here.\n          if (!options.isPeriodic) {\n            if (typeof options.handleId === 'number') {\n              // in non-nodejs env, we remove timerId\n              // from local cache\n              delete tasksByHandleId[options.handleId];\n            } else if (options.handleId) {\n              // Node returns complex objects as handleIds\n              // we remove task reference from timer object\n              options.handleId[taskSymbol] = null;\n            }\n          }\n        }\n      };\n\n      const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n\n      if (!task) {\n        return task;\n      } // Node.js must additionally support the ref and unref functions.\n\n\n      const handle = task.data.handleId;\n\n      if (typeof handle === 'number') {\n        // for non nodejs env, we save handleId: task\n        // mapping in local cache for clearTimeout\n        tasksByHandleId[handle] = task;\n      } else if (handle) {\n        // for nodejs env, we save task\n        // reference in timerId Object for clearTimeout\n        handle[taskSymbol] = task;\n      } // check whether handle is null, because some polyfill or browser\n      // may return undefined from setTimeout/setInterval/setImmediate/requestAnimationFrame\n\n\n      if (handle && handle.ref && handle.unref && typeof handle.ref === 'function' && typeof handle.unref === 'function') {\n        task.ref = handle.ref.bind(handle);\n        task.unref = handle.unref.bind(handle);\n      }\n\n      if (typeof handle === 'number' || handle) {\n        return handle;\n      }\n\n      return task;\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(window, args);\n    }\n  });\n  clearNative = patchMethod(window, cancelName, delegate => function (self, args) {\n    const id = args[0];\n    let task;\n\n    if (typeof id === 'number') {\n      // non nodejs env.\n      task = tasksByHandleId[id];\n    } else {\n      // nodejs env.\n      task = id && id[taskSymbol]; // other environments.\n\n      if (!task) {\n        task = id;\n      }\n    }\n\n    if (task && typeof task.type === 'string') {\n      if (task.state !== 'notScheduled' && (task.cancelFn && task.data.isPeriodic || task.runCount === 0)) {\n        if (typeof id === 'number') {\n          delete tasksByHandleId[id];\n        } else if (id) {\n          id[taskSymbol] = null;\n        } // Do not cancel already canceled functions\n\n\n        task.zone.cancelTask(task);\n      }\n    } else {\n      // cause an error by calling it directly.\n      delegate.apply(window, args);\n    }\n  });\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction patchCustomElements(_global, api) {\n  const {\n    isBrowser,\n    isMix\n  } = api.getGlobalObjects();\n\n  if (!isBrowser && !isMix || !_global['customElements'] || !('customElements' in _global)) {\n    return;\n  }\n\n  const callbacks = ['connectedCallback', 'disconnectedCallback', 'adoptedCallback', 'attributeChangedCallback'];\n  api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction eventTargetPatch(_global, api) {\n  if (Zone[api.symbol('patchEventTarget')]) {\n    // EventTarget is already patched.\n    return;\n  }\n\n  const {\n    eventNames,\n    zoneSymbolEventNames,\n    TRUE_STR,\n    FALSE_STR,\n    ZONE_SYMBOL_PREFIX\n  } = api.getGlobalObjects(); //  predefine all __zone_symbol__ + eventName + true/false string\n\n  for (let i = 0; i < eventNames.length; i++) {\n    const eventName = eventNames[i];\n    const falseEventName = eventName + FALSE_STR;\n    const trueEventName = eventName + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n  }\n\n  const EVENT_TARGET = _global['EventTarget'];\n\n  if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n    return;\n  }\n\n  api.patchEventTarget(_global, [EVENT_TARGET && EVENT_TARGET.prototype]);\n  return true;\n}\n\nfunction patchEvent(global, api) {\n  api.patchEventPrototype(global, api);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nZone.__load_patch('legacy', global => {\n  const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n\n  if (legacyPatch) {\n    legacyPatch();\n  }\n});\n\nZone.__load_patch('queueMicrotask', (global, Zone, api) => {\n  api.patchMethod(global, 'queueMicrotask', delegate => {\n    return function (self, args) {\n      Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n    };\n  });\n});\n\nZone.__load_patch('timers', global => {\n  const set = 'set';\n  const clear = 'clear';\n  patchTimer(global, set, clear, 'Timeout');\n  patchTimer(global, set, clear, 'Interval');\n  patchTimer(global, set, clear, 'Immediate');\n});\n\nZone.__load_patch('requestAnimationFrame', global => {\n  patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n  patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n  patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n});\n\nZone.__load_patch('blocking', (global, Zone) => {\n  const blockingMethods = ['alert', 'prompt', 'confirm'];\n\n  for (let i = 0; i < blockingMethods.length; i++) {\n    const name = blockingMethods[i];\n    patchMethod(global, name, (delegate, symbol, name) => {\n      return function (s, args) {\n        return Zone.current.run(delegate, global, args, name);\n      };\n    });\n  }\n});\n\nZone.__load_patch('EventTarget', (global, Zone, api) => {\n  patchEvent(global, api);\n  eventTargetPatch(global, api); // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n\n  const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n\n  if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n    api.patchEventTarget(global, [XMLHttpRequestEventTarget.prototype]);\n  }\n});\n\nZone.__load_patch('MutationObserver', (global, Zone, api) => {\n  patchClass('MutationObserver');\n  patchClass('WebKitMutationObserver');\n});\n\nZone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n  patchClass('IntersectionObserver');\n});\n\nZone.__load_patch('FileReader', (global, Zone, api) => {\n  patchClass('FileReader');\n});\n\nZone.__load_patch('on_property', (global, Zone, api) => {\n  propertyDescriptorPatch(api, global);\n});\n\nZone.__load_patch('customElements', (global, Zone, api) => {\n  patchCustomElements(global, api);\n});\n\nZone.__load_patch('XHR', (global, Zone) => {\n  // Treat XMLHttpRequest as a macrotask.\n  patchXHR(global);\n  const XHR_TASK = zoneSymbol('xhrTask');\n  const XHR_SYNC = zoneSymbol('xhrSync');\n  const XHR_LISTENER = zoneSymbol('xhrListener');\n  const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n  const XHR_URL = zoneSymbol('xhrURL');\n  const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n\n  function patchXHR(window) {\n    const XMLHttpRequest = window['XMLHttpRequest'];\n\n    if (!XMLHttpRequest) {\n      // XMLHttpRequest is not available in service worker\n      return;\n    }\n\n    const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n\n    function findPendingTask(target) {\n      return target[XHR_TASK];\n    }\n\n    let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n    let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n\n    if (!oriAddListener) {\n      const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n\n      if (XMLHttpRequestEventTarget) {\n        const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n        oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n        oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n      }\n    }\n\n    const READY_STATE_CHANGE = 'readystatechange';\n    const SCHEDULED = 'scheduled';\n\n    function scheduleTask(task) {\n      const data = task.data;\n      const target = data.target;\n      target[XHR_SCHEDULED] = false;\n      target[XHR_ERROR_BEFORE_SCHEDULED] = false; // remove existing event listener\n\n      const listener = target[XHR_LISTENER];\n\n      if (!oriAddListener) {\n        oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n        oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n      }\n\n      if (listener) {\n        oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n      }\n\n      const newListener = target[XHR_LISTENER] = () => {\n        if (target.readyState === target.DONE) {\n          // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n          // readyState=4 multiple times, so we need to check task state here\n          if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n            // check whether the xhr has registered onload listener\n            // if that is the case, the task should invoke after all\n            // onload listeners finish.\n            // Also if the request failed without response (status = 0), the load event handler\n            // will not be triggered, in that case, we should also invoke the placeholder callback\n            // to close the XMLHttpRequest::send macroTask.\n            // https://github.com/angular/angular/issues/38795\n            const loadTasks = target[Zone.__symbol__('loadfalse')];\n\n            if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n              const oriInvoke = task.invoke;\n\n              task.invoke = function () {\n                // need to load the tasks again, because in other\n                // load listener, they may remove themselves\n                const loadTasks = target[Zone.__symbol__('loadfalse')];\n\n                for (let i = 0; i < loadTasks.length; i++) {\n                  if (loadTasks[i] === task) {\n                    loadTasks.splice(i, 1);\n                  }\n                }\n\n                if (!data.aborted && task.state === SCHEDULED) {\n                  oriInvoke.call(task);\n                }\n              };\n\n              loadTasks.push(task);\n            } else {\n              task.invoke();\n            }\n          } else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n            // error occurs when xhr.send()\n            target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n          }\n        }\n      };\n\n      oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n      const storedTask = target[XHR_TASK];\n\n      if (!storedTask) {\n        target[XHR_TASK] = task;\n      }\n\n      sendNative.apply(target, data.args);\n      target[XHR_SCHEDULED] = true;\n      return task;\n    }\n\n    function placeholderCallback() {}\n\n    function clearTask(task) {\n      const data = task.data; // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n      // to prevent it from firing. So instead, we store info for the event listener.\n\n      data.aborted = true;\n      return abortNative.apply(data.target, data.args);\n    }\n\n    const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n      self[XHR_SYNC] = args[2] == false;\n      self[XHR_URL] = args[1];\n      return openNative.apply(self, args);\n    });\n    const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n    const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n    const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n    const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n      if (Zone.current[fetchTaskScheduling] === true) {\n        // a fetch is scheduling, so we are using xhr to polyfill fetch\n        // and because we already schedule macroTask for fetch, we should\n        // not schedule a macroTask for xhr again\n        return sendNative.apply(self, args);\n      }\n\n      if (self[XHR_SYNC]) {\n        // if the XHR is sync there is no task to schedule, just execute the code.\n        return sendNative.apply(self, args);\n      } else {\n        const options = {\n          target: self,\n          url: self[XHR_URL],\n          isPeriodic: false,\n          args: args,\n          aborted: false\n        };\n        const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n\n        if (self && self[XHR_ERROR_BEFORE_SCHEDULED] === true && !options.aborted && task.state === SCHEDULED) {\n          // xhr request throw error when send\n          // we should invoke task instead of leaving a scheduled\n          // pending macroTask\n          task.invoke();\n        }\n      }\n    });\n    const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n      const task = findPendingTask(self);\n\n      if (task && typeof task.type == 'string') {\n        // If the XHR has already completed, do nothing.\n        // If the XHR has already been aborted, do nothing.\n        // Fix #569, call abort multiple times before done will cause\n        // macroTask task count be negative number\n        if (task.cancelFn == null || task.data && task.data.aborted) {\n          return;\n        }\n\n        task.zone.cancelTask(task);\n      } else if (Zone.current[fetchTaskAborting] === true) {\n        // the abort is called from fetch polyfill, we need to call native abort of XHR.\n        return abortNative.apply(self, args);\n      } // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n      // task\n      // to cancel. Do nothing.\n\n    });\n  }\n});\n\nZone.__load_patch('geolocation', global => {\n  /// GEO_LOCATION\n  if (global['navigator'] && global['navigator'].geolocation) {\n    patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n  }\n});\n\nZone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n  // handle unhandled promise rejection\n  function findPromiseRejectionHandler(evtName) {\n    return function (e) {\n      const eventTasks = findEventTasks(global, evtName);\n      eventTasks.forEach(eventTask => {\n        // windows has added unhandledrejection event listener\n        // trigger the event listener\n        const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n\n        if (PromiseRejectionEvent) {\n          const evt = new PromiseRejectionEvent(evtName, {\n            promise: e.promise,\n            reason: e.rejection\n          });\n          eventTask.invoke(evt);\n        }\n      });\n    };\n  }\n\n  if (global['PromiseRejectionEvent']) {\n    Zone[zoneSymbol('unhandledPromiseRejectionHandler')] = findPromiseRejectionHandler('unhandledrejection');\n    Zone[zoneSymbol('rejectionHandledHandler')] = findPromiseRejectionHandler('rejectionhandled');\n  }\n});", "map": null, "metadata": {}, "sourceType": "script"}