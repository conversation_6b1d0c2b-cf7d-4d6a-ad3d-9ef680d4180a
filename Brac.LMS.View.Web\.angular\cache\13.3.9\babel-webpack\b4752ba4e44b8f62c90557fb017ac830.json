{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { TemplateRef, EventEmitter } from '@angular/core';\nimport { environment } from 'src/environments/environment';\nimport { NavigationEnd } from '@angular/router';\nimport { BlockUI } from 'ng-block-ui';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"../_services/authentication.service\";\nimport * as i5 from \"ngx-smart-modal\";\nimport * as i6 from \"ng-block-ui\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"ngx-slick-carousel\";\nimport * as i9 from \"ngx-bootstrap/tooltip\";\nimport * as i10 from \"ngx-extended-pdf-viewer\";\n\nconst _c0 = function () {\n  return [\"/my-courses\"];\n};\n\nconst _c1 = function () {\n  return [\"/bookmarks\"];\n};\n\nconst _c2 = function () {\n  return [\"/certifications\", \"My Achivement\"];\n};\n\nconst _c3 = function () {\n  return [\"/certifications\", \"My Exam\"];\n};\n\nfunction DashboardComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵelementStart(2, \"div\", 16);\n    i0.ɵɵelementStart(3, \"a\");\n    i0.ɵɵelementStart(4, \"div\", 17);\n    i0.ɵɵelement(5, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 19);\n    i0.ɵɵelementStart(7, \"div\", 20);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 21);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 15);\n    i0.ɵɵelementStart(12, \"div\", 16);\n    i0.ɵɵelementStart(13, \"a\");\n    i0.ɵɵelementStart(14, \"div\", 22);\n    i0.ɵɵelement(15, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 24);\n    i0.ɵɵelementStart(17, \"div\", 20);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 21);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 15);\n    i0.ɵɵelementStart(22, \"div\", 16);\n    i0.ɵɵelementStart(23, \"a\");\n    i0.ɵɵelementStart(24, \"div\", 25);\n    i0.ɵɵelement(25, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 27);\n    i0.ɵɵelementStart(27, \"div\", 20);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 21);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 15);\n    i0.ɵɵelementStart(32, \"div\", 16);\n    i0.ɵɵelementStart(33, \"a\");\n    i0.ɵɵelementStart(34, \"div\", 28);\n    i0.ɵɵelement(35, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 30);\n    i0.ɵɵelementStart(37, \"div\", 20);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 21);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(12, _c0));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.counts.Enrollments > 1 ? \"Enrolled Courses\" : \"Enrolled Course\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.counts.Enrollments);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(13, _c1));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.counts.Bookmarks > 1 ? \"Bookmarked Courses\" : \"Bookmarked Course\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.counts.Bookmarks);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(14, _c2));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.counts.Certificates > 1 ? \"My Achivements\" : \"My Achivement\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.counts.Certificates);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(15, _c3));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.counts.ExamTaken > 1 ? \"My Exams\" : \"My Exam\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.counts.ExamTaken);\n  }\n}\n\nfunction DashboardComponent_div_8_div_6_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const course_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", course_r11.NoOfContentsStudied, \" / \", course_r11.NoOfContents, \" \", course_r11.NoOfContents > 1 ? \"Lectures\" : \"Lecture\", \" \");\n  }\n}\n\nfunction DashboardComponent_div_8_div_6_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1, \" Completed \");\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c4 = function (a1) {\n  return [\"/course-details/\", a1];\n};\n\nfunction DashboardComponent_div_8_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelementStart(1, \"a\", 38);\n    i0.ɵɵelementStart(2, \"div\", 39);\n    i0.ɵɵelement(3, \"img\", 40);\n    i0.ɵɵelementStart(4, \"div\", 41);\n    i0.ɵɵelementStart(5, \"div\", 1);\n    i0.ɵɵelement(6, \"div\", 42);\n    i0.ɵɵelementStart(7, \"div\", 42);\n    i0.ɵɵelementStart(8, \"div\", 43);\n    i0.ɵɵtext(9, \" Enrolled \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 44);\n    i0.ɵɵelementStart(11, \"div\", 1);\n    i0.ɵɵelementStart(12, \"div\", 45);\n    i0.ɵɵelementStart(13, \"h4\", 46);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 1);\n    i0.ɵɵelementStart(16, \"div\", 42);\n    i0.ɵɵelementStart(17, \"div\", 47);\n    i0.ɵɵtext(18);\n    i0.ɵɵelement(19, \"i\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 42);\n    i0.ɵɵtemplate(21, DashboardComponent_div_8_div_6_div_21_Template, 2, 3, \"div\", 49);\n    i0.ɵɵtemplate(22, DashboardComponent_div_8_div_6_div_22_Template, 3, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const course_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(7, _c4, course_r11.Id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r10.mediaBaseUrl + course_r11.ImagePath, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(7);\n    i0.ɵɵpropertyInterpolate(\"tooltip\", course_r11.Title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", course_r11.Title, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", course_r11.Rating, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", course_r11.NoOfContentsStudied != course_r11.NoOfContents);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", course_r11.NoOfContentsStudied == course_r11.NoOfContents);\n  }\n}\n\nfunction DashboardComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelementStart(1, \"h3\", 32);\n    i0.ɵɵtext(2, \"My Courses\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33);\n    i0.ɵɵelementStart(4, \"ngx-slick-carousel\", 34, 35);\n    i0.ɵɵtemplate(6, DashboardComponent_div_8_div_6_Template, 23, 9, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"config\", ctx_r1.myCourseSlideConfig);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.myCourseList);\n  }\n}\n\nconst _c5 = function (a1) {\n  return [\"/course-details-preview/\", a1];\n};\n\nfunction DashboardComponent_div_9_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelementStart(1, \"a\", 38);\n    i0.ɵɵelementStart(2, \"div\", 52);\n    i0.ɵɵelement(3, \"img\", 40);\n    i0.ɵɵelementStart(4, \"div\", 53);\n    i0.ɵɵelementStart(5, \"div\", 1);\n    i0.ɵɵelementStart(6, \"div\", 45);\n    i0.ɵɵelementStart(7, \"h5\", 54);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 1);\n    i0.ɵɵelementStart(10, \"div\", 42);\n    i0.ɵɵelementStart(11, \"div\", 55);\n    i0.ɵɵtext(12);\n    i0.ɵɵelement(13, \"i\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 42);\n    i0.ɵɵelementStart(15, \"div\", 50);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const course_r17 = ctx.$implicit;\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(7, _c5, course_r17.Id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r16.mediaBaseUrl + course_r17.ImagePath, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"tooltip\", course_r17.Title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", course_r17.Title, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", course_r17.Rating, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", course_r17.NoOfContents, \" \", course_r17.NoOfContents > 1 ? \"Lectures\" : \"Lecture\", \" \");\n  }\n}\n\nfunction DashboardComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelementStart(1, \"h3\", 32);\n    i0.ɵɵtext(2, \"Available Courses\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33);\n    i0.ɵɵelementStart(4, \"ngx-slick-carousel\", 34, 35);\n    i0.ɵɵtemplate(6, DashboardComponent_div_9_div_6_Template, 17, 9, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"config\", ctx_r2.availableCourseSlideConfig);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableCourseList);\n  }\n}\n\nfunction DashboardComponent_h4_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedContent.Title);\n  }\n}\n\nfunction DashboardComponent_video_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"video\", 57);\n    i0.ɵɵelement(1, \"source\", 58);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"src\", \"\", ctx_r5.mediaBaseUrl, \"\", ctx_r5.selectedContent.FilePath, \"\", i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction DashboardComponent_h4_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedContent.Title);\n  }\n}\n\nfunction DashboardComponent_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"ngx-extended-pdf-viewer\", 63);\n    i0.ɵɵlistener(\"srcChange\", function DashboardComponent_div_19_div_1_Template_ngx_extended_pdf_viewer_srcChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return ctx_r20.pdfSrc = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r18.pdfSrc)(\"zoom\", \"page-width\")(\"showPrintButton\", false)(\"showRotateButton\", false)(\"showOpenFileButton\", false)(\"showDownloadButton\", false)(\"showBookmarkButton\", true)(\"showSecondaryToolbarButton\", false)(\"useBrowserLocale\", true);\n  }\n}\n\nfunction DashboardComponent_div_19_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelementStart(1, \"div\", 65);\n    i0.ɵɵelementStart(2, \"p\", 66);\n    i0.ɵɵtext(3, \" This document can't preview here. You may download the document. Please click below button to download the document. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_19_div_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return ctx_r22.downloadDoc();\n    });\n    i0.ɵɵelement(5, \"i\", 68);\n    i0.ɵɵtext(6, \" Download \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DashboardComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, DashboardComponent_div_19_div_1_Template, 2, 9, \"div\", 60);\n    i0.ɵɵtemplate(2, DashboardComponent_div_19_div_2_Template, 7, 0, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.docObj.PDF);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.docObj.PDF);\n  }\n} // import { Injectable, EventEmitter, Output } from '@angular/core';\n\n\nexport class DashboardComponent {\n  constructor(router, _service, toastr, authService, ngxSmartModalService, commonService) {\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.authService = authService;\n    this.ngxSmartModalService = ngxSmartModalService;\n    this.commonService = commonService;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.currentUser = null;\n    this.counts = null;\n    this.timestamp = new Date().getTime();\n    this.myCoursePerSlide = 2;\n    this.availableCoursePerSlide = 2;\n    this.learningHourPerSlide = 3;\n    this.newItemEvent = new EventEmitter();\n    this.myCourseList = [];\n    this.availableCourseList = [];\n    this.learningHoursContentList = []; // [\n    //     { Id: 1, title: \"Digital Construction 1\", img: \"assets/img/courses/digital-construction-1.jpg\", rating: \"4.3\", lectures: \"12\" },\n    //     { Id: 2, title: \"Digital Construction 2\", img: \"assets/img/courses/digital-construction-2.jpg\", rating: \"4.0\", lectures: \"13\" },\n    //     { Id: 3, title: \"Digital Construction 3\", img: \"assets/img/courses/digital-construction-3.jpg\", rating: \"4.2\", lectures: \"11\" },\n    //     { Id: 4, title: \"Digital Construction 4\", img: \"assets/img/courses/digital-construction-4.jpg\", rating: \"4.4\", lectures: \"16\" },\n    // ];\n\n    this.docObj = null;\n    this.pdfSrc = null;\n    this.selectedContent = null;\n    this.router.events.subscribe(evt => {\n      if (!(evt instanceof NavigationEnd)) {\n        return;\n      }\n\n      window.scrollTo(0, 0);\n    });\n\n    if (window.innerWidth >= 940) {\n      this.myCoursePerSlide = 2;\n      this.availableCoursePerSlide = 2;\n      this.learningHourPerSlide = 3;\n    } else if (window.innerWidth >= 740) {\n      this.myCoursePerSlide = 3;\n      this.availableCoursePerSlide = 2;\n      this.learningHourPerSlide = 3;\n    } else if (window.innerWidth >= 400) {\n      this.myCoursePerSlide = 2;\n      this.availableCoursePerSlide = 2;\n      this.learningHourPerSlide = 2;\n    } else {\n      this.myCoursePerSlide = 1;\n      this.availableCoursePerSlide = 1;\n    }\n\n    window.onresize = () => {\n      if (window.innerWidth >= 940) {\n        this.myCoursePerSlide = 2;\n        this.availableCoursePerSlide = 2;\n        this.learningHourPerSlide = 3;\n      } else if (window.innerWidth >= 740) {\n        this.myCoursePerSlide = 2;\n        this.availableCoursePerSlide = 2;\n        this.learningHourPerSlide = 3;\n      } else if (window.innerWidth >= 400) {\n        this.myCoursePerSlide = 2;\n        this.availableCoursePerSlide = 2;\n        this.learningHourPerSlide = 2;\n      } else {\n        this.myCoursePerSlide = 1;\n        this.availableCoursePerSlide = 1;\n      }\n\n      this.myCourseSlideConfig = {\n        \"slidesToShow\": this.myCoursePerSlide,\n        \"slidesToScroll\": this.myCoursePerSlide,\n        arrows: true,\n        dots: true\n      };\n      this.availableCourseSlideConfig = {\n        \"slidesToShow\": this.availableCoursePerSlide,\n        \"slidesToScroll\": this.availableCoursePerSlide,\n        arrows: true,\n        dots: true\n      };\n      this.learningHourSlideConfig = {\n        \"slidesToShow\": this.learningHourPerSlide,\n        \"slidesToScroll\": this.learningHourPerSlide,\n        arrows: true\n      };\n    };\n\n    this.authService.getCurrentUser().subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  ngOnInit() {\n    this.getCounts();\n    this.getMyCourseList();\n    if (this.currentUser.Roles.indexOf('Trainee') !== -1) this.getLearningHoursContentList();\n    this.getAvailableCourseList();\n    this.myCourseSlideConfig = {\n      \"slidesToShow\": this.myCoursePerSlide,\n      \"slidesToScroll\": this.myCoursePerSlide,\n      arrows: true,\n      dots: true\n    };\n    this.availableCourseSlideConfig = {\n      \"slidesToShow\": this.availableCoursePerSlide,\n      \"slidesToScroll\": this.availableCoursePerSlide,\n      arrows: true,\n      dots: true\n    };\n    this.learningHourSlideConfig = {\n      \"slidesToShow\": this.learningHourPerSlide,\n      \"slidesToScroll\": this.learningHourPerSlide,\n      arrows: true\n    };\n  }\n\n  getCounts() {\n    this._service.get('dashboard/get-count-for-trainee').subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.counts = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  getMyCourseList() {\n    this.blockUI.start('loading...');\n\n    this._service.get('course/get-my-courses/' + this.myCoursePerSlide * 2).subscribe({\n      // this._service.get('course/get-my-courses/').subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.myCourseList = res.Data.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  getAvailableCourseList() {\n    this.blockUI.start('loading...');\n\n    this._service.get('course/get-available-courses/' + this.availableCoursePerSlide * 2).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.availableCourseList = res.Data.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  getLearningHoursContentList() {\n    this.blockUI.start('loading...');\n\n    this._service.get('course/get-learning-hour-contents/' + this.learningHourPerSlide * 2).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.learningHoursContentList = res.Data.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n      },\n      complete: () => {}\n    });\n  }\n\n  openModal(item) {\n    // this.modalRefVideo = this.modalService.show(template, this.modalConfigVideo);\n    this.selectedContent = item;\n\n    switch (item.Type) {\n      case \"Video\":\n        this.ngxSmartModalService.create('videoModal', this.tpl).open();\n        break;\n\n      case \"Document\":\n        this.onDocClick(item);\n        this.ngxSmartModalService.create('docModal', this.tpl).open();\n        break;\n\n      case \"Exam\":\n        this.router.navigate(['evaluation-test/' + item.Id]);\n        break;\n    }\n  }\n\n  onDocClick(doc) {\n    this.docObj = null; //  if (doc.Restricted) return;\n\n    this.docObj = doc;\n\n    if (doc.FilePath.split('.').pop() === 'pdf') {\n      this.openPdf(doc.FilePath);\n      this.docObj.PDF = true;\n    } else {\n      this.docObj.PDF = false;\n    }\n  }\n\n  openPdf(path) {\n    this._service.getPDFFile(this.mediaBaseUrl + '/api/course/download-document-file?partialPath=' + path).subscribe(res => {\n      this.pdfSrc = res;\n    });\n  }\n\n  downloadDoc() {\n    var link = document.createElement('a');\n    link.href = this.mediaBaseUrl + this.docObj.FilePath;\n    link.target = '_blank';\n    link.rel = 'noopener';\n    link.download = this.docObj.Title + '.' + this.docObj.FilePath.split('.').pop();\n    link.click();\n    link.remove();\n  }\n\n  modalClose() {\n    this.selectedContent = null;\n  }\n\n}\n\nDashboardComponent.ɵfac = function DashboardComponent_Factory(t) {\n  return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i4.AuthenticationService), i0.ɵɵdirectiveInject(i5.NgxSmartModalService), i0.ɵɵdirectiveInject(i2.CommonService));\n};\n\nDashboardComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: DashboardComponent,\n  selectors: [[\"app-dashboard\"]],\n  viewQuery: function DashboardComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tpl = _t.first);\n    }\n  },\n  outputs: {\n    newItemEvent: \"newItemEvent\"\n  },\n  decls: 20,\n  vars: 7,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"row\"], [1, \"col-lg-12\", \"p-5\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\", \"p-4\"], [1, \"pt-2\", \"p-md-3\"], [1, \"container\", \"bootstrap\", \"snippets\", \"bootdey\"], [\"class\", \"row\", 4, \"ngIf\"], [\"class\", \"row mt-3\", 4, \"ngIf\"], [\"identifier\", \"videoModal\", \"customClass\", \"nsm-dialog-animation-btt modal-lg\", 3, \"onAnyCloseEventFinished\"], [\"videoModal\", \"\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"w-100\", \"controls\", \"\", 4, \"ngIf\"], [\"identifier\", \"docModal\", \"customClass\", \"nsm-dialog-animation-btt modal-lg\", 3, \"onAnyCloseEventFinished\"], [\"docModal\", \"\"], [\"class\", \"p-1\", 4, \"ngIf\"], [\"routerLinkActive\", \"router-link-active\", 1, \"col-lg-3\", \"col-md-6\", \"col-sm-12\", \"px-2\", \"mb-3\", \"cursor-pointer\", 3, \"routerLink\"], [1, \"circle-tile\"], [1, \"circle-tile-heading\", \"dark-blue\"], [1, \"fa\", \"fa-book\", \"fa-fw\", \"fa-3x\"], [1, \"circle-tile-content\", \"dark-blue\"], [1, \"circle-tile-description\", \"text-white\", \"garage-title\"], [1, \"circle-tile-number\", \"text-white\"], [1, \"circle-tile-heading\", \"blue\"], [1, \"fa\", \"fa-bookmark\", \"fa-fw\", \"fa-3x\"], [1, \"circle-tile-content\", \"blue\"], [1, \"circle-tile-heading\", \"green\"], [1, \"fa\", \"fa-award\", \"fa-fw\", \"fa-3x\"], [1, \"circle-tile-content\", \"green\"], [1, \"circle-tile-heading\", \"navy\"], [1, \"fa\", \"fa-graduation-cap\", \"fa-fw\", \"fa-3x\"], [1, \"circle-tile-content\", \"navy\"], [1, \"row\", \"mt-3\"], [1, \"mb-3\"], [1, \"align-items-center\"], [1, \"carousel\", 3, \"config\"], [\"slickModal\", \"slick-carousel\"], [\"class\", \"col-md-5\", \"ngxSlickItem\", \"\", 4, \"ngFor\", \"ngForOf\"], [\"ngxSlickItem\", \"\", 1, \"col-md-5\"], [\"routerLinkActive\", \"router-link-active\", \"href\", \"javascript:;\", 1, \"card\", \"border-0\", \"card-floating-position-custom\", \"shadow\", \"mx-1\", \"my-1\", 3, \"routerLink\"], [1, \"card-img-top\", \"card-img-bottom\"], [\"alt\", \"Maldives\", 1, \"card-img-top\", \"style-height-190\", 3, \"src\"], [1, \"card-floating-div\"], [1, \"col-6\"], [1, \"d-inline-block\", \"fw-normal\", \"brac-button\", \"btn-primary\", \"px-2\", \"py-1\", \"rounded-1\", \"float-end\"], [1, \"card-body\", \"style-1\", 3, \"tooltip\"], [1, \"col-md-12\"], [1, \"text-start\", \"text-dark\", \"limit-text-lines\"], [1, \"d-inline-block\", \"fw-normal\", \"bg-faded-black\", \"text-white\", \"px-2\", \"py-1\", \"rounded-1\"], [1, \"fa\", \"fa-star\", \"text-gold\"], [\"class\", \"d-inline-block fw-normal bg-faded-black text-white px-2 py-1 rounded-1 float-end\", 4, \"ngIf\"], [1, \"d-inline-block\", \"fw-normal\", \"bg-faded-black\", \"text-white\", \"px-2\", \"py-1\", \"rounded-1\", \"float-end\"], [1, \"ai-check\", \"fw-bold\", \"fs-3\", \"text-white\"], [1, \"card-img-top\", \"card-img-bottom\", \"style-2\"], [1, \"card-body\", \"style-3\", 3, \"tooltip\"], [1, \"text-start\", \"text-dark\", \"text-dark\", \"limit-text-lines\"], [1, \"d-inline-block\", \"fw-normal\", \"bg-faded-black\", \"text-white\", \"px-2\", \"py-2\", \"rounded-1\"], [1, \"text-center\"], [\"controls\", \"\", 1, \"w-100\"], [\"type\", \"video/mp4\", 3, \"src\"], [1, \"p-1\"], [\"class\", \"w-100 style-height-605\", 4, \"ngIf\"], [\"class\", \"card py-3 mb-2 style-height-205\", 4, \"ngIf\"], [1, \"w-100\", \"style-height-605\"], [3, \"src\", \"zoom\", \"showPrintButton\", \"showRotateButton\", \"showOpenFileButton\", \"showDownloadButton\", \"showBookmarkButton\", \"showSecondaryToolbarButton\", \"useBrowserLocale\", \"srcChange\"], [1, \"card\", \"py-3\", \"mb-2\", \"style-height-205\"], [1, \"card-body\"], [1, \"font-size-20\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"mt-2\", 3, \"click\"], [1, \"fa\", \"fa-download\"]],\n  template: function DashboardComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵelementStart(6, \"div\", 5);\n      i0.ɵɵtemplate(7, DashboardComponent_div_7_Template, 41, 16, \"div\", 6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(8, DashboardComponent_div_8_Template, 7, 2, \"div\", 7);\n      i0.ɵɵtemplate(9, DashboardComponent_div_9_Template, 7, 2, \"div\", 7);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(10, \"ngx-smart-modal\", 8, 9);\n      i0.ɵɵlistener(\"onAnyCloseEventFinished\", function DashboardComponent_Template_ngx_smart_modal_onAnyCloseEventFinished_10_listener() {\n        return ctx.modalClose();\n      });\n      i0.ɵɵtemplate(12, DashboardComponent_h4_12_Template, 2, 1, \"h4\", 10);\n      i0.ɵɵelementStart(13, \"div\");\n      i0.ɵɵtemplate(14, DashboardComponent_video_14_Template, 2, 2, \"video\", 11);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"ngx-smart-modal\", 12, 13);\n      i0.ɵɵlistener(\"onAnyCloseEventFinished\", function DashboardComponent_Template_ngx_smart_modal_onAnyCloseEventFinished_15_listener() {\n        return ctx.modalClose();\n      });\n      i0.ɵɵtemplate(17, DashboardComponent_h4_17_Template, 2, 1, \"h4\", 10);\n      i0.ɵɵelementStart(18, \"div\");\n      i0.ɵɵtemplate(19, DashboardComponent_div_19_Template, 3, 2, \"div\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngIf\", ctx.counts);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.myCourseList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.availableCourseList.length > 0);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedContent);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedContent);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedContent);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.docObj);\n    }\n  },\n  directives: [i6.BlockUIComponent, i7.NgIf, i1.RouterLinkActive, i1.RouterLink, i8.SlickCarouselComponent, i7.NgForOf, i8.SlickItemDirective, i1.RouterLinkWithHref, i9.TooltipDirective, i5.NgxSmartModalComponent, i10.NgxExtendedPdfViewerComponent],\n  styles: [\"app-dashboard .slick-track{margin-left:0}.card-floating-div{position:absolute;bottom:80%;left:5%;right:5%;z-index:1}.bg-faded-orange{border:1px solid rgba(34,33,42,.6)!important;background-color:#99821b99!important}.fs-3{font-size:1.4rem!important}.text-primary{color:#218d4b!important}.fw-bold{font-weight:200!important}.circle-tile{margin-bottom:15px;text-align:center}.card-border{border-radius:60%}.circle-tile-heading{border:3px solid rgba(255,255,255,.3);border-radius:100%;color:#fff;height:80px;margin:0 auto -40px;position:relative;transition:all .3s ease-in-out 0s;width:80px}.circle-tile-heading .fa{line-height:80px}.circle-tile-content{padding-top:50px}.circle-tile-number{font-size:26px;font-weight:700;line-height:1;padding:5px 0 15px}.circle-tile-description{text-transform:uppercase}.circle-tile-footer{background-color:#0000001a;color:#ffffff80;display:block;padding:5px;transition:all .3s ease-in-out 0s}.circle-tile-footer:hover{background-color:#0003;color:#ffffff80;text-decoration:none}.circle-tile-heading.dark-blue:hover{background-color:#2e4154}.circle-tile-heading.green:hover{background-color:#138f77}.circle-tile-heading.orange:hover{background-color:#da8c10}.circle-tile-heading.blue:hover{background-color:#2473a6}.circle-tile-heading.red:hover{background-color:#cf4435}.circle-tile-heading.purple:hover{background-color:#7f3d9b}.tile-img{text-shadow:2px 2px 3px rgba(0,0,0,.9)}.dark-blue{background-color:#34495e}.green{background-color:#16a085}.blue{background-color:#2980b9}.orange{background-color:#f39c12}.red{background-color:#e74c3c}.purple{background-color:#8e44ad}.dark-gray{background-color:#7f8c8d}.gray{background-color:#95a5a6}.navy{background-color:#2d30b8}.light-gray{background-color:#bdc3c7}.yellow{background-color:#f1c40f}.text-dark-blue{color:#34495e}.text-green{color:#16a085}.text-blue{color:#2980b9}.text-orange{color:#f39c12}.text-red{color:#e74c3c}.text-purple{color:#8e44ad}.text-faded{color:#ffffffb3}.brac-button{color:#f5c253!important;border:1px solid #F5C253!important}.garage-title{clear:both;display:inline-block;overflow:hidden;white-space:nowrap;font-size:small}.limit-text-lines{display:-webkit-box;height:2.6em;-webkit-line-clamp:2;-webkit-box-orient:vertical;text-overflow:ellipsis;overflow:hidden;white-space:pre-wrap;position:relative}.style-60-80{width:60px;height:80px}.style-70-100{width:70px;height:100px}.style-80-80{width:80px;height:80px}.style-height-190{height:190px}.style-1{padding:5px;background-color:#f0f8ff;border-radius:0 0 15px 15px}.style-2{height:20rem}.style-3{padding:8px;background-color:#f0f8ff;border-radius:0 0 15px 15px}.style-height-605{height:605px}.style-height-205{height:205px}\\n\"],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], DashboardComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}