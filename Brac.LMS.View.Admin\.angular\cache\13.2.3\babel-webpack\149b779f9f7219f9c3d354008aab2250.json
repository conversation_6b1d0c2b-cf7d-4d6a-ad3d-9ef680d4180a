{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ColumnMode } from \"@swimlane/ngx-datatable\";\nimport { Validators } from \"@angular/forms\";\nimport { BlockUI } from \"ng-block-ui\";\nimport * as moment from \"moment-timezone\";\nimport { trigger, transition, style, animate } from \"@angular/animations\";\nimport { ResponseStatus } from \"../_models/enum\";\nimport { debounceTime } from \"rxjs/operators\";\nimport { environment } from \"src/environments/environment\";\nimport { Editor } from \"ngx-editor\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../app.component\";\nimport * as i2 from \"ngx-bootstrap/modal\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../_services/common.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i8 from \"ng-block-ui\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/flex-layout/extended\";\nimport * as i11 from \"@ng-select/ng-select\";\nimport * as i12 from \"../_helpers/numbers-only\";\nimport * as i13 from \"ngx-editor\";\nimport * as i14 from \"@swimlane/ngx-datatable\";\nimport * as i15 from \"ngx-ui-switch\";\n\nfunction EvaluationTestEntryComponent_div_17_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 88);\n    i0.ɵɵtext(1, \" Exam name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵtemplate(1, EvaluationTestEntryComponent_div_17_span_1_Template, 2, 0, \"span\", 87);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.exam.errors.required);\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_31_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 88);\n    i0.ɵɵtext(1, \"Category is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵtemplate(1, EvaluationTestEntryComponent_div_31_span_1_Template, 2, 0, \"span\", 87);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f.categoryId.errors.required);\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_36_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 88);\n    i0.ɵɵtext(1, \" Exam duration is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵtemplate(1, EvaluationTestEntryComponent_div_36_span_1_Template, 2, 0, \"span\", 87);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.f.duration.errors.required);\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_41_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 88);\n    i0.ɵɵtext(1, \" quota is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵtemplate(1, EvaluationTestEntryComponent_div_41_span_1_Template, 2, 0, \"span\", 87);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.f.quota.errors.required);\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelementStart(1, \"label\", 10);\n    i0.ɵɵtext(2, \"Division \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ng-select\", 89, 90);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_47_Template_ng_select_click_3_listener() {\n      i0.ɵɵrestoreView(_r53);\n\n      const _r51 = i0.ɵɵreference(4);\n\n      const ctx_r52 = i0.ɵɵnextContext();\n      return ctx_r52.handleSelectClick(_r51);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r6.divisionList);\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelementStart(1, \"label\", 10);\n    i0.ɵɵtext(2, \"Department \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ng-select\", 91, 92);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_48_Template_ng_select_click_3_listener() {\n      i0.ɵɵrestoreView(_r56);\n\n      const _r54 = i0.ɵɵreference(4);\n\n      const ctx_r55 = i0.ɵɵnextContext();\n      return ctx_r55.handleSelectClick(_r54);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r7.departmentList);\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelementStart(1, \"label\", 10);\n    i0.ɵɵtext(2, \"Unit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ng-select\", 93, 94);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_49_Template_ng_select_click_3_listener() {\n      i0.ɵɵrestoreView(_r59);\n\n      const _r57 = i0.ɵɵreference(4);\n\n      const ctx_r58 = i0.ɵɵnextContext();\n      return ctx_r58.handleSelectClick(_r57);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r8.unitList);\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 95);\n    i0.ɵɵelementStart(1, \"a\", 96);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_50_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext();\n\n      const _r45 = i0.ɵɵreference(172);\n\n      return ctx_r60.openTraineeModal(_r45);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r9.allowedTrainees.length, \" trainee(s) selected \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelementStart(1, \"label\", 10);\n    i0.ɵɵtext(2, \" No. of True/False Q. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 97);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelementStart(1, \"label\", 13);\n    i0.ɵɵtext(2, \" No. of Fill In the Gap Q. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 98);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelementStart(1, \"label\", 13);\n    i0.ɵɵtext(2, \" No. of Matching Q. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 99);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelementStart(1, \"label\", 13);\n    i0.ɵɵtext(2, \" No. of Written Q. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 100);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_img_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 101);\n  }\n}\n\nfunction EvaluationTestEntryComponent_img_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 102);\n  }\n\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r15.imageUrl, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction EvaluationTestEntryComponent_button_104_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_button_104_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return ctx_r62.onFormSubmit();\n    });\n    i0.ɵɵelement(1, \"i\", 104);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.btnSaveText, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_button_113_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_button_113_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return ctx_r64.downloadQuestionList(\"MCQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 106);\n    i0.ɵɵtext(2, \" Download Excel \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r66 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r66);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r66, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r67 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r67);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r67, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r68 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r68);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r68, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r69 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r69);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r69, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r70 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r70);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r70, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_131_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r71 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r71);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r71, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r72 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r72);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r72, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_135_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r75 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_135_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r75);\n      const row_r73 = restoredCtx.row;\n      const ctx_r74 = i0.ɵɵnextContext();\n      return ctx_r74.deleteQuestion(row_r73.Id, \"MCQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 109);\n    i0.ɵɵtext(2, \" Delete \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_button_143_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r77 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_button_143_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return ctx_r76.downloadQuestionList(\"TFQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 106);\n    i0.ɵɵtext(2, \" Download Excel \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_151_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r78 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r78);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r78, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_153_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r79 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r79 ? \"TRUE\" : \"FALSE\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r79 ? \"TRUE\" : \"FALSE\", \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_155_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r80 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r80);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r80, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_157_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r83 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_157_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r83);\n      const row_r81 = restoredCtx.row;\n      const ctx_r82 = i0.ɵɵnextContext();\n      return ctx_r82.deleteQuestion(row_r81.Id, \"TFQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 110);\n    i0.ɵɵtext(2, \"Delete \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_158_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r90 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_158_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r90);\n      const ctx_r89 = i0.ɵɵnextContext(2);\n      return ctx_r89.downloadQuestionList(\"FIGQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 106);\n    i0.ɵɵtext(2, \" Download Excel \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_158_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r91 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r91);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r91, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_158_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r92 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r92);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r92, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_158_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r93 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r93);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r93, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_158_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r96 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_158_ng_template_21_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r96);\n      const row_r94 = restoredCtx.row;\n      const ctx_r95 = i0.ɵɵnextContext(2);\n      return ctx_r95.deleteQuestion(row_r94.Id, \"FIGQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 110);\n    i0.ɵɵtext(2, \"Delete \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_158_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r98 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 111);\n    i0.ɵɵelementStart(1, \"div\", 4);\n    i0.ɵɵelementStart(2, \"div\", 0);\n    i0.ɵɵelementStart(3, \"div\", 58);\n    i0.ɵɵelementStart(4, \"h5\");\n    i0.ɵɵtext(5, \"Fill in the gaps Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 59);\n    i0.ɵɵtemplate(7, EvaluationTestEntryComponent_div_158_button_7_Template, 3, 0, \"button\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 61);\n    i0.ɵɵelementStart(9, \"button\", 112);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_158_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r98);\n      const ctx_r97 = i0.ɵɵnextContext();\n\n      const _r39 = i0.ɵɵreference(166);\n\n      return ctx_r97.openFIGModal(_r39);\n    });\n    i0.ɵɵelement(10, \"i\", 63);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 6);\n    i0.ɵɵelementStart(13, \"ngx-datatable\", 64);\n    i0.ɵɵelementStart(14, \"ngx-datatable-column\", 65);\n    i0.ɵɵtemplate(15, EvaluationTestEntryComponent_div_158_ng_template_15_Template, 2, 2, \"ng-template\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ngx-datatable-column\", 76);\n    i0.ɵɵtemplate(17, EvaluationTestEntryComponent_div_158_ng_template_17_Template, 2, 2, \"ng-template\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"ngx-datatable-column\", 72);\n    i0.ɵɵtemplate(19, EvaluationTestEntryComponent_div_158_ng_template_19_Template, 2, 2, \"ng-template\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"ngx-datatable-column\", 73);\n    i0.ɵɵtemplate(21, EvaluationTestEntryComponent_div_158_ng_template_21_Template, 3, 0, \"ng-template\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.figList.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r32.figList.length === 0 ? \"Add\" : \"Edit\", \" Fill in the gaps Questions \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"rows\", ctx_r32.figList)(\"loadingIndicator\", ctx_r32.loadingFIG)(\"columnMode\", ctx_r32.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"limit\", 5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"width\", 200)(\"draggable\", false)(\"sortable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_159_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r105 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_159_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r105);\n      const ctx_r104 = i0.ɵɵnextContext(2);\n      return ctx_r104.downloadQuestionList(\"MatchingQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 106);\n    i0.ɵɵtext(2, \" Download Excel \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_159_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r106 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r106);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r106, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_159_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r107 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r107);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r107, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_159_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r108 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r108);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r108, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_159_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r111 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_159_ng_template_21_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r111);\n      const row_r109 = restoredCtx.row;\n      const ctx_r110 = i0.ɵɵnextContext(2);\n      return ctx_r110.deleteQuestion(row_r109.Id, \"MatchingQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 110);\n    i0.ɵɵtext(2, \"Delete \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_159_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r113 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 113);\n    i0.ɵɵelementStart(1, \"div\", 4);\n    i0.ɵɵelementStart(2, \"div\", 0);\n    i0.ɵɵelementStart(3, \"div\", 58);\n    i0.ɵɵelementStart(4, \"h5\");\n    i0.ɵɵtext(5, \"Matching Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 59);\n    i0.ɵɵtemplate(7, EvaluationTestEntryComponent_div_159_button_7_Template, 3, 0, \"button\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 61);\n    i0.ɵɵelementStart(9, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_159_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r113);\n      const ctx_r112 = i0.ɵɵnextContext();\n\n      const _r41 = i0.ɵɵreference(168);\n\n      return ctx_r112.openMatchingModal(_r41);\n    });\n    i0.ɵɵelement(10, \"i\", 63);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 6);\n    i0.ɵɵelementStart(13, \"ngx-datatable\", 64);\n    i0.ɵɵelementStart(14, \"ngx-datatable-column\", 115);\n    i0.ɵɵtemplate(15, EvaluationTestEntryComponent_div_159_ng_template_15_Template, 2, 2, \"ng-template\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ngx-datatable-column\", 116);\n    i0.ɵɵtemplate(17, EvaluationTestEntryComponent_div_159_ng_template_17_Template, 2, 2, \"ng-template\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"ngx-datatable-column\", 72);\n    i0.ɵɵtemplate(19, EvaluationTestEntryComponent_div_159_ng_template_19_Template, 2, 2, \"ng-template\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"ngx-datatable-column\", 73);\n    i0.ɵɵtemplate(21, EvaluationTestEntryComponent_div_159_ng_template_21_Template, 3, 0, \"ng-template\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.matchingList.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r33.matchingList.length === 0 ? \"Add\" : \"Edit\", \" Matching Questions \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"rows\", ctx_r33.matchingList)(\"loadingIndicator\", ctx_r33.loadingMatching)(\"columnMode\", ctx_r33.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"limit\", 5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"width\", 175)(\"draggable\", false)(\"sortable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"width\", 175)(\"draggable\", false)(\"sortable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_160_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r119 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_160_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r119);\n      const ctx_r118 = i0.ɵɵnextContext(2);\n      return ctx_r118.downloadQuestionList(\"WQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 106);\n    i0.ɵɵtext(2, \" Download Excel \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_160_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r120 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r120);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r120, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_160_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r121 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r121);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r121, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_160_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r124 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_160_ng_template_19_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r124);\n      const row_r122 = restoredCtx.row;\n      const ctx_r123 = i0.ɵɵnextContext(2);\n      return ctx_r123.deleteQuestion(row_r122.Id, \"WQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 110);\n    i0.ɵɵtext(2, \"Delete \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_div_160_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r126 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵelementStart(1, \"div\", 4);\n    i0.ɵɵelementStart(2, \"div\", 0);\n    i0.ɵɵelementStart(3, \"div\", 58);\n    i0.ɵɵelementStart(4, \"h5\");\n    i0.ɵɵtext(5, \"Written Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 59);\n    i0.ɵɵtemplate(7, EvaluationTestEntryComponent_div_160_button_7_Template, 3, 0, \"button\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 61);\n    i0.ɵɵelementStart(9, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_div_160_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r125 = i0.ɵɵnextContext();\n\n      const _r43 = i0.ɵɵreference(170);\n\n      return ctx_r125.openWrittenModal(_r43);\n    });\n    i0.ɵɵelement(10, \"i\", 63);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 6);\n    i0.ɵɵelementStart(13, \"ngx-datatable\", 64);\n    i0.ɵɵelementStart(14, \"ngx-datatable-column\", 119);\n    i0.ɵɵtemplate(15, EvaluationTestEntryComponent_div_160_ng_template_15_Template, 2, 2, \"ng-template\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ngx-datatable-column\", 72);\n    i0.ɵɵtemplate(17, EvaluationTestEntryComponent_div_160_ng_template_17_Template, 2, 2, \"ng-template\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"ngx-datatable-column\", 73);\n    i0.ɵɵtemplate(19, EvaluationTestEntryComponent_div_160_ng_template_19_Template, 3, 0, \"ng-template\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r34.writtenList.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r34.writtenList.length === 0 ? \"Add\" : \"Edit\", \" Written Questions \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"rows\", ctx_r34.writtenList)(\"loadingIndicator\", ctx_r34.loadingWritten)(\"columnMode\", ctx_r34.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"limit\", 5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_161_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r131 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_161_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r131);\n      i0.ɵɵnextContext();\n\n      const _r127 = i0.ɵɵreference(12);\n\n      const ctx_r130 = i0.ɵɵnextContext();\n      return ctx_r130.resetMCQFile(_r127);\n    });\n    i0.ɵɵtext(1, \" remove \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_161_div_25_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"span\", 88);\n    i0.ɵɵtext(2, \" Question is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function () {\n  return {\n    standalone: true\n  };\n};\n\nfunction EvaluationTestEntryComponent_ng_template_161_div_25_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r140 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 153);\n    i0.ɵɵelementStart(1, \"div\", 143);\n    i0.ɵɵelementStart(2, \"span\", 151);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 154);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_161_div_25_div_14_Template_input_ngModelChange_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r140);\n      const option_r137 = restoredCtx.$implicit;\n      return option_r137.Text = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 151);\n    i0.ɵɵelementStart(6, \"input\", 155);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_161_div_25_div_14_Template_input_ngModelChange_6_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r140);\n      const option_r137 = restoredCtx.$implicit;\n      return option_r137.Selected = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const option_r137 = ctx.$implicit;\n    const oi_r138 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(oi_r138 + 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"placeholder\", \"Option \", oi_r138 + 1, \"\");\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(7, _c0))(\"ngModel\", option_r137.Text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", option_r137.Selected)(\"disabled\", !option_r137.Text)(\"ngModel\", option_r137.Selected);\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_161_div_25_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"span\", 88);\n    i0.ɵɵtext(2, \" Mark is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_161_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r143 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵelementStart(1, \"div\", 142);\n    i0.ɵɵelementStart(2, \"div\", 0);\n    i0.ɵɵelementStart(3, \"div\", 142);\n    i0.ɵɵelementStart(4, \"div\", 143);\n    i0.ɵɵelementStart(5, \"span\", 144);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 145);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_161_div_25_Template_textarea_ngModelChange_7_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r143);\n      const item_r132 = restoredCtx.$implicit;\n      return item_r132.Question = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 146);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_161_div_25_Template_div_click_8_listener() {\n      i0.ɵɵrestoreView(_r143);\n      const ctx_r144 = i0.ɵɵnextContext(2);\n      return ctx_r144.addNewMCQ();\n    });\n    i0.ɵɵelement(9, \"i\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 146);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_161_div_25_Template_div_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r143);\n      const i_r133 = restoredCtx.index;\n      const ctx_r145 = i0.ɵɵnextContext(2);\n      return ctx_r145.deleteMCQ(i_r133);\n    });\n    i0.ɵɵelement(11, \"i\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, EvaluationTestEntryComponent_ng_template_161_div_25_div_12_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 125);\n    i0.ɵɵtemplate(14, EvaluationTestEntryComponent_ng_template_161_div_25_div_14_Template, 7, 8, \"div\", 149);\n    i0.ɵɵelementStart(15, \"div\", 150);\n    i0.ɵɵelementStart(16, \"div\", 143);\n    i0.ɵɵelementStart(17, \"span\", 151);\n    i0.ɵɵtext(18, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"input\", 152);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_161_div_25_Template_input_ngModelChange_19_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r143);\n      const item_r132 = restoredCtx.$implicit;\n      return item_r132.Mark = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, EvaluationTestEntryComponent_ng_template_161_div_25_div_20_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"hr\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r132 = ctx.$implicit;\n    const i_r133 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Q\", i_r133 + 1, \":\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", item_r132.Question)(\"ngModelOptions\", i0.ɵɵpureFunction0(8, _c0));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !item_r132.Question);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r132.Options);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", item_r132.Mark);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r132.Mark);\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_161_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r148 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 120);\n    i0.ɵɵelement(1, \"h4\", 121);\n    i0.ɵɵelementStart(2, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_161_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r148);\n      const ctx_r147 = i0.ɵɵnextContext();\n      return ctx_r147.modalMCQHide();\n    });\n    i0.ɵɵelement(3, \"i\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 124);\n    i0.ɵɵelementStart(5, \"div\", 57);\n    i0.ɵɵelementStart(6, \"div\", 4);\n    i0.ɵɵelementStart(7, \"h5\");\n    i0.ɵɵtext(8, \"Bulk Upload MCQ\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 125);\n    i0.ɵɵelementStart(10, \"div\", 126);\n    i0.ɵɵelementStart(11, \"input\", 127, 128);\n    i0.ɵɵlistener(\"change\", function EvaluationTestEntryComponent_ng_template_161_Template_input_change_11_listener() {\n      i0.ɵɵrestoreView(_r148);\n\n      const _r127 = i0.ɵɵreference(12);\n\n      const ctx_r149 = i0.ɵɵnextContext();\n      return ctx_r149.loadMCQFile(_r127.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 129);\n    i0.ɵɵtemplate(14, EvaluationTestEntryComponent_ng_template_161_button_14_Template, 2, 0, \"button\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 131);\n    i0.ɵɵelementStart(16, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_161_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r148);\n      const ctx_r150 = i0.ɵɵnextContext();\n      return ctx_r150.downloadSampleMCQ();\n    });\n    i0.ɵɵelement(17, \"i\", 133);\n    i0.ɵɵtext(18, \" Download Sample File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 134);\n    i0.ɵɵelementStart(20, \"h5\");\n    i0.ɵɵelementStart(21, \"b\");\n    i0.ɵɵtext(22, \"Manual Entry MCQ\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(23, \"br\");\n    i0.ɵɵelementStart(24, \"div\", 135);\n    i0.ɵɵtemplate(25, EvaluationTestEntryComponent_ng_template_161_div_25_Template, 22, 9, \"div\", 136);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 137);\n    i0.ɵɵelementStart(27, \"div\", 138);\n    i0.ɵɵelementStart(28, \"button\", 139);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_161_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r148);\n      const ctx_r151 = i0.ɵɵnextContext();\n      return ctx_r151.modalMCQHide();\n    });\n    i0.ɵɵelement(29, \"i\", 123);\n    i0.ɵɵtext(30, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 140);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_161_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r148);\n      const ctx_r152 = i0.ɵɵnextContext();\n      return ctx_r152.onMCQFormSubmit();\n    });\n    i0.ɵɵelement(32, \"i\", 104);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r36.modalMCQTitle, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r36.mcqFile);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r36.mcqQuestionList);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r36.btnMCQSaveText, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_163_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r157 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_163_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r157);\n      i0.ɵɵnextContext();\n\n      const _r153 = i0.ɵɵreference(12);\n\n      const ctx_r156 = i0.ɵɵnextContext();\n      return ctx_r156.resetTFQFile(_r153);\n    });\n    i0.ɵɵtext(1, \" remove \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_163_div_25_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"span\", 88);\n    i0.ɵɵtext(2, \" Question is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_163_div_25_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"span\", 88);\n    i0.ɵɵtext(2, \" Mark is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_163_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r163 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵelementStart(1, \"div\", 142);\n    i0.ɵɵelementStart(2, \"div\", 160);\n    i0.ɵɵelementStart(3, \"div\", 142);\n    i0.ɵɵelementStart(4, \"div\", 143);\n    i0.ɵɵelementStart(5, \"span\", 144);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 145);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_163_div_25_Template_textarea_ngModelChange_7_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r163);\n      const item_r158 = restoredCtx.$implicit;\n      return item_r158.Question = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 146);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_163_div_25_Template_div_click_8_listener() {\n      i0.ɵɵrestoreView(_r163);\n      const ctx_r164 = i0.ɵɵnextContext(2);\n      return ctx_r164.addNewTFQ();\n    });\n    i0.ɵɵelement(9, \"i\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 146);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_163_div_25_Template_div_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r163);\n      const i_r159 = restoredCtx.index;\n      const ctx_r165 = i0.ɵɵnextContext(2);\n      return ctx_r165.deleteTFQ(i_r159);\n    });\n    i0.ɵɵelement(11, \"i\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, EvaluationTestEntryComponent_ng_template_163_div_25_div_12_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 160);\n    i0.ɵɵelementStart(14, \"div\", 61);\n    i0.ɵɵelementStart(15, \"ui-switch\", 161);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_163_div_25_Template_ui_switch_ngModelChange_15_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r163);\n      const item_r158 = restoredCtx.$implicit;\n      return item_r158.Answer = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 61);\n    i0.ɵɵelementStart(17, \"div\", 143);\n    i0.ɵɵelementStart(18, \"span\", 151);\n    i0.ɵɵtext(19, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 152);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_163_div_25_Template_input_ngModelChange_20_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r163);\n      const item_r158 = restoredCtx.$implicit;\n      return item_r158.Mark = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, EvaluationTestEntryComponent_ng_template_163_div_25_div_21_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"hr\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r158 = ctx.$implicit;\n    const i_r159 = ctx.index;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Q\", i_r159 + 1, \":\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", item_r158.Question)(\"ngModelOptions\", i0.ɵɵpureFunction0(7, _c0));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !item_r158.Question);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", item_r158.Answer);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", item_r158.Mark);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r158.Mark);\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_163_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r169 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 120);\n    i0.ɵɵelement(1, \"h4\", 121);\n    i0.ɵɵelementStart(2, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_163_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r169);\n      const ctx_r168 = i0.ɵɵnextContext();\n      return ctx_r168.modalTFQHide();\n    });\n    i0.ɵɵelement(3, \"i\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 156);\n    i0.ɵɵelementStart(5, \"div\", 57);\n    i0.ɵɵelementStart(6, \"div\", 4);\n    i0.ɵɵelementStart(7, \"h5\");\n    i0.ɵɵtext(8, \"Bulk Upload True/False QUestions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 125);\n    i0.ɵɵelementStart(10, \"div\", 126);\n    i0.ɵɵelementStart(11, \"input\", 127, 157);\n    i0.ɵɵlistener(\"change\", function EvaluationTestEntryComponent_ng_template_163_Template_input_change_11_listener() {\n      i0.ɵɵrestoreView(_r169);\n\n      const _r153 = i0.ɵɵreference(12);\n\n      const ctx_r170 = i0.ɵɵnextContext();\n      return ctx_r170.loadTFQFile(_r153.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 129);\n    i0.ɵɵtemplate(14, EvaluationTestEntryComponent_ng_template_163_button_14_Template, 2, 0, \"button\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 131);\n    i0.ɵɵelementStart(16, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_163_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r169);\n      const ctx_r171 = i0.ɵɵnextContext();\n      return ctx_r171.downloadSampleTFQ();\n    });\n    i0.ɵɵelement(17, \"i\", 133);\n    i0.ɵɵtext(18, \" Download Sample File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 134);\n    i0.ɵɵelementStart(20, \"h5\");\n    i0.ɵɵelementStart(21, \"b\");\n    i0.ɵɵtext(22, \"Manual Entry True/False Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(23, \"br\");\n    i0.ɵɵelementStart(24, \"div\", 135);\n    i0.ɵɵtemplate(25, EvaluationTestEntryComponent_ng_template_163_div_25_Template, 23, 8, \"div\", 136);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 137);\n    i0.ɵɵelementStart(27, \"div\", 138);\n    i0.ɵɵelementStart(28, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_163_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r169);\n      const ctx_r172 = i0.ɵɵnextContext();\n      return ctx_r172.modalTFQHide();\n    });\n    i0.ɵɵelement(29, \"i\", 123);\n    i0.ɵɵtext(30, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 159);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_163_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r169);\n      const ctx_r173 = i0.ɵɵnextContext();\n      return ctx_r173.onTFQFormSubmit();\n    });\n    i0.ɵɵelement(32, \"i\", 104);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r38.modalTFTitle, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r38.tfqFile);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r38.tfQuestionList);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r38.btnTFQSaveText, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_165_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r178 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_165_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r178);\n      i0.ɵɵnextContext();\n\n      const _r174 = i0.ɵɵreference(12);\n\n      const ctx_r177 = i0.ɵɵnextContext();\n      return ctx_r177.resetFIGQFile(_r174);\n    });\n    i0.ɵɵtext(1, \" remove \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_165_div_25_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"span\", 88);\n    i0.ɵɵtext(2, \" Question is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_165_div_25_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"span\", 88);\n    i0.ɵɵtext(2, \" Answer is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_165_div_25_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"span\", 88);\n    i0.ɵɵtext(2, \" Mark is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_165_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r185 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵelementStart(1, \"div\", 142);\n    i0.ɵɵelementStart(2, \"div\", 160);\n    i0.ɵɵelementStart(3, \"div\", 142);\n    i0.ɵɵelementStart(4, \"div\", 143);\n    i0.ɵɵelementStart(5, \"span\", 144);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 145);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_165_div_25_Template_textarea_ngModelChange_7_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r185);\n      const item_r179 = restoredCtx.$implicit;\n      return item_r179.Question = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 146);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_165_div_25_Template_div_click_8_listener() {\n      i0.ɵɵrestoreView(_r185);\n      const ctx_r186 = i0.ɵɵnextContext(2);\n      return ctx_r186.addNewFIG();\n    });\n    i0.ɵɵelement(9, \"i\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 146);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_165_div_25_Template_div_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r185);\n      const i_r180 = restoredCtx.index;\n      const ctx_r187 = i0.ɵɵnextContext(2);\n      return ctx_r187.deleteFIG(i_r180);\n    });\n    i0.ɵɵelement(11, \"i\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, EvaluationTestEntryComponent_ng_template_165_div_25_div_12_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 160);\n    i0.ɵɵelementStart(14, \"div\", 153);\n    i0.ɵɵelementStart(15, \"div\", 143);\n    i0.ɵɵelementStart(16, \"span\", 151);\n    i0.ɵɵtext(17, \"Answer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 164);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_165_div_25_Template_input_ngModelChange_18_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r185);\n      const item_r179 = restoredCtx.$implicit;\n      return item_r179.Answer = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, EvaluationTestEntryComponent_ng_template_165_div_25_div_19_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 61);\n    i0.ɵɵelementStart(21, \"div\", 143);\n    i0.ɵɵelementStart(22, \"span\", 151);\n    i0.ɵɵtext(23, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"input\", 152);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_165_div_25_Template_input_ngModelChange_24_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r185);\n      const item_r179 = restoredCtx.$implicit;\n      return item_r179.Mark = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, EvaluationTestEntryComponent_ng_template_165_div_25_div_25_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"hr\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r179 = ctx.$implicit;\n    const i_r180 = ctx.index;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Q\", i_r180 + 1, \":\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", item_r179.Question)(\"ngModelOptions\", i0.ɵɵpureFunction0(8, _c0));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !item_r179.Question);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", item_r179.Answer);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r179.Answer);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", item_r179.Mark);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r179.Mark);\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_165_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r191 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 120);\n    i0.ɵɵelement(1, \"h4\", 162);\n    i0.ɵɵelementStart(2, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_165_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r191);\n      const ctx_r190 = i0.ɵɵnextContext();\n      return ctx_r190.modalFIGHide();\n    });\n    i0.ɵɵelement(3, \"i\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 156);\n    i0.ɵɵelementStart(5, \"div\", 57);\n    i0.ɵɵelementStart(6, \"div\", 4);\n    i0.ɵɵelementStart(7, \"h5\");\n    i0.ɵɵtext(8, \"Bulk Upload Fill In The Gap Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 125);\n    i0.ɵɵelementStart(10, \"div\", 126);\n    i0.ɵɵelementStart(11, \"input\", 127, 163);\n    i0.ɵɵlistener(\"change\", function EvaluationTestEntryComponent_ng_template_165_Template_input_change_11_listener() {\n      i0.ɵɵrestoreView(_r191);\n\n      const _r174 = i0.ɵɵreference(12);\n\n      const ctx_r192 = i0.ɵɵnextContext();\n      return ctx_r192.loadFIGQFile(_r174.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 129);\n    i0.ɵɵtemplate(14, EvaluationTestEntryComponent_ng_template_165_button_14_Template, 2, 0, \"button\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 131);\n    i0.ɵɵelementStart(16, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_165_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r191);\n      const ctx_r193 = i0.ɵɵnextContext();\n      return ctx_r193.downloadSampleFIGQ();\n    });\n    i0.ɵɵelement(17, \"i\", 133);\n    i0.ɵɵtext(18, \" Download Sample File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 134);\n    i0.ɵɵelementStart(20, \"h5\");\n    i0.ɵɵelementStart(21, \"b\");\n    i0.ɵɵtext(22, \"Manual Entry Fill In The Gap Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(23, \"br\");\n    i0.ɵɵelementStart(24, \"div\", 135);\n    i0.ɵɵtemplate(25, EvaluationTestEntryComponent_ng_template_165_div_25_Template, 27, 9, \"div\", 136);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 137);\n    i0.ɵɵelementStart(27, \"div\", 138);\n    i0.ɵɵelementStart(28, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_165_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r191);\n      const ctx_r194 = i0.ɵɵnextContext();\n      return ctx_r194.modalFIGHide();\n    });\n    i0.ɵɵelement(29, \"i\", 123);\n    i0.ɵɵtext(30, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 159);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_165_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r191);\n      const ctx_r195 = i0.ɵɵnextContext();\n      return ctx_r195.onFIGFormSubmit();\n    });\n    i0.ɵɵelement(32, \"i\", 104);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r40.modalFIGTitle, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r40.figqFile);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r40.figQuestionList);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r40.btnFIGSaveText, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_167_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r200 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_167_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r200);\n      i0.ɵɵnextContext();\n\n      const _r196 = i0.ɵɵreference(12);\n\n      const ctx_r199 = i0.ɵɵnextContext();\n      return ctx_r199.resetMatchingQFile(_r196);\n    });\n    i0.ɵɵtext(1, \" remove \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_167_div_25_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"span\", 88);\n    i0.ɵɵtext(2, \" Left hand side is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_167_div_25_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"span\", 88);\n    i0.ɵɵtext(2, \" Right hand side is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_167_div_25_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"span\", 88);\n    i0.ɵɵtext(2, \" Mark is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_167_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r207 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵelementStart(1, \"div\", 142);\n    i0.ɵɵelementStart(2, \"div\", 160);\n    i0.ɵɵelementStart(3, \"div\", 142);\n    i0.ɵɵelementStart(4, \"div\", 143);\n    i0.ɵɵelementStart(5, \"span\", 144);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 167);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_167_div_25_Template_textarea_ngModelChange_7_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r207);\n      const item_r201 = restoredCtx.$implicit;\n      return item_r201.LeftSide = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 146);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_167_div_25_Template_div_click_8_listener() {\n      i0.ɵɵrestoreView(_r207);\n      const ctx_r208 = i0.ɵɵnextContext(2);\n      return ctx_r208.addNewMatching();\n    });\n    i0.ɵɵelement(9, \"i\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 146);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_167_div_25_Template_div_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r207);\n      const i_r202 = restoredCtx.index;\n      const ctx_r209 = i0.ɵɵnextContext(2);\n      return ctx_r209.deleteMatching(i_r202);\n    });\n    i0.ɵɵelement(11, \"i\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, EvaluationTestEntryComponent_ng_template_167_div_25_div_12_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 160);\n    i0.ɵɵelementStart(14, \"div\", 168);\n    i0.ɵɵelementStart(15, \"div\", 143);\n    i0.ɵɵelementStart(16, \"span\", 151);\n    i0.ɵɵtext(17, \"RHS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"textarea\", 169);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_167_div_25_Template_textarea_ngModelChange_18_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r207);\n      const item_r201 = restoredCtx.$implicit;\n      return item_r201.RightSide = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, EvaluationTestEntryComponent_ng_template_167_div_25_div_19_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 61);\n    i0.ɵɵelementStart(21, \"div\", 143);\n    i0.ɵɵelementStart(22, \"span\", 151);\n    i0.ɵɵtext(23, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"input\", 152);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_167_div_25_Template_input_ngModelChange_24_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r207);\n      const item_r201 = restoredCtx.$implicit;\n      return item_r201.Mark = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, EvaluationTestEntryComponent_ng_template_167_div_25_div_25_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"hr\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r201 = ctx.$implicit;\n    const i_r202 = ctx.index;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"LHS\", i_r202 + 1, \":\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", item_r201.LeftSide)(\"ngModelOptions\", i0.ɵɵpureFunction0(8, _c0));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !item_r201.LeftSide);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", item_r201.RightSide);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r201.RightSide);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", item_r201.Mark);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r201.Mark);\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_167_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r213 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 120);\n    i0.ɵɵelement(1, \"h4\", 162);\n    i0.ɵɵelementStart(2, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_167_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r213);\n      const ctx_r212 = i0.ɵɵnextContext();\n      return ctx_r212.modalMatchingHide();\n    });\n    i0.ɵɵelement(3, \"i\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 156);\n    i0.ɵɵelementStart(5, \"div\", 57);\n    i0.ɵɵelementStart(6, \"div\", 4);\n    i0.ɵɵelementStart(7, \"h5\");\n    i0.ɵɵtext(8, \"Bulk Upload Matching Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 125);\n    i0.ɵɵelementStart(10, \"div\", 126);\n    i0.ɵɵelementStart(11, \"input\", 127, 165);\n    i0.ɵɵlistener(\"change\", function EvaluationTestEntryComponent_ng_template_167_Template_input_change_11_listener() {\n      i0.ɵɵrestoreView(_r213);\n\n      const _r196 = i0.ɵɵreference(12);\n\n      const ctx_r214 = i0.ɵɵnextContext();\n      return ctx_r214.loadMatchingQFile(_r196.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 129);\n    i0.ɵɵtemplate(14, EvaluationTestEntryComponent_ng_template_167_button_14_Template, 2, 0, \"button\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 131);\n    i0.ɵɵelementStart(16, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_167_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r213);\n      const ctx_r215 = i0.ɵɵnextContext();\n      return ctx_r215.downloadSampleMatchingQ();\n    });\n    i0.ɵɵelement(17, \"i\", 133);\n    i0.ɵɵtext(18, \" Download Sample File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 134);\n    i0.ɵɵelementStart(20, \"h5\");\n    i0.ɵɵelementStart(21, \"b\");\n    i0.ɵɵtext(22, \"Manual Entry Matching Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(23, \"br\");\n    i0.ɵɵelementStart(24, \"div\", 135);\n    i0.ɵɵtemplate(25, EvaluationTestEntryComponent_ng_template_167_div_25_Template, 27, 9, \"div\", 136);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 166);\n    i0.ɵɵelementStart(27, \"div\", 138);\n    i0.ɵɵelementStart(28, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_167_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r213);\n      const ctx_r216 = i0.ɵɵnextContext();\n      return ctx_r216.modalMatchingHide();\n    });\n    i0.ɵɵelement(29, \"i\", 123);\n    i0.ɵɵtext(30, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 159);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_167_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r213);\n      const ctx_r217 = i0.ɵɵnextContext();\n      return ctx_r217.onMatchingFormSubmit();\n    });\n    i0.ɵɵelement(32, \"i\", 104);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r42.modalMatchingTitle, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r42.matchingqFile);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r42.matchingQuestionList);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r42.btnMatchingSaveText, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_169_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r222 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_169_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r222);\n      i0.ɵɵnextContext();\n\n      const _r218 = i0.ɵɵreference(12);\n\n      const ctx_r221 = i0.ɵɵnextContext();\n      return ctx_r221.resetWQFile(_r218);\n    });\n    i0.ɵɵtext(1, \" remove \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_169_div_25_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"span\", 88);\n    i0.ɵɵtext(2, \" Question is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_169_div_25_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"span\", 88);\n    i0.ɵɵtext(2, \" Mark is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_169_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r228 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵelementStart(1, \"div\", 142);\n    i0.ɵɵelementStart(2, \"div\", 160);\n    i0.ɵɵelementStart(3, \"div\", 142);\n    i0.ɵɵelementStart(4, \"div\", 143);\n    i0.ɵɵelementStart(5, \"span\", 144);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 145);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_169_div_25_Template_textarea_ngModelChange_7_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r228);\n      const item_r223 = restoredCtx.$implicit;\n      return item_r223.Question = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 146);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_169_div_25_Template_div_click_8_listener() {\n      i0.ɵɵrestoreView(_r228);\n      const ctx_r229 = i0.ɵɵnextContext(2);\n      return ctx_r229.addNewWritten();\n    });\n    i0.ɵɵelement(9, \"i\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 146);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_169_div_25_Template_div_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r228);\n      const i_r224 = restoredCtx.index;\n      const ctx_r230 = i0.ɵɵnextContext(2);\n      return ctx_r230.deleteWritten(i_r224);\n    });\n    i0.ɵɵelement(11, \"i\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, EvaluationTestEntryComponent_ng_template_169_div_25_div_12_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 160);\n    i0.ɵɵelementStart(14, \"div\", 153);\n    i0.ɵɵelementStart(15, \"div\", 143);\n    i0.ɵɵelementStart(16, \"span\", 151);\n    i0.ɵɵtext(17, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 152);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_169_div_25_Template_input_ngModelChange_18_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r228);\n      const item_r223 = restoredCtx.$implicit;\n      return item_r223.Mark = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, EvaluationTestEntryComponent_ng_template_169_div_25_div_19_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"hr\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r223 = ctx.$implicit;\n    const i_r224 = ctx.index;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Q\", i_r224 + 1, \":\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", item_r223.Question)(\"ngModelOptions\", i0.ɵɵpureFunction0(6, _c0));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !item_r223.Question);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", item_r223.Mark);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r223.Mark);\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_169_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r233 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 120);\n    i0.ɵɵelement(1, \"h4\", 162);\n    i0.ɵɵelementStart(2, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_169_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r233);\n      const ctx_r232 = i0.ɵɵnextContext();\n      return ctx_r232.modalWrittenHide();\n    });\n    i0.ɵɵelement(3, \"i\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 156);\n    i0.ɵɵelementStart(5, \"div\", 57);\n    i0.ɵɵelementStart(6, \"div\", 4);\n    i0.ɵɵelementStart(7, \"h5\");\n    i0.ɵɵtext(8, \"Bulk Upload Written Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 125);\n    i0.ɵɵelementStart(10, \"div\", 126);\n    i0.ɵɵelementStart(11, \"input\", 127, 170);\n    i0.ɵɵlistener(\"change\", function EvaluationTestEntryComponent_ng_template_169_Template_input_change_11_listener() {\n      i0.ɵɵrestoreView(_r233);\n\n      const _r218 = i0.ɵɵreference(12);\n\n      const ctx_r234 = i0.ɵɵnextContext();\n      return ctx_r234.loadWQFile(_r218.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 129);\n    i0.ɵɵtemplate(14, EvaluationTestEntryComponent_ng_template_169_button_14_Template, 2, 0, \"button\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 131);\n    i0.ɵɵelementStart(16, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_169_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r233);\n      const ctx_r235 = i0.ɵɵnextContext();\n      return ctx_r235.downloadSampleWQ();\n    });\n    i0.ɵɵelement(17, \"i\", 133);\n    i0.ɵɵtext(18, \" Download Sample File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 134);\n    i0.ɵɵelementStart(20, \"h5\");\n    i0.ɵɵelementStart(21, \"b\");\n    i0.ɵɵtext(22, \"Manual Entry Written Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(23, \"br\");\n    i0.ɵɵelementStart(24, \"div\", 135);\n    i0.ɵɵtemplate(25, EvaluationTestEntryComponent_ng_template_169_div_25_Template, 21, 7, \"div\", 136);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 137);\n    i0.ɵɵelementStart(27, \"div\", 138);\n    i0.ɵɵelementStart(28, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_169_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r233);\n      const ctx_r236 = i0.ɵɵnextContext();\n      return ctx_r236.modalWrittenHide();\n    });\n    i0.ɵɵelement(29, \"i\", 123);\n    i0.ɵɵtext(30, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 159);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_169_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r233);\n      const ctx_r237 = i0.ɵɵnextContext();\n      return ctx_r237.onWrittenFormSubmit();\n    });\n    i0.ɵɵelement(32, \"i\", 104);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r44.modalWrittenTitle, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r44.wqFile);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r44.writtenQuestionList);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r44.btnWrittenSaveText, \" \");\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_171_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 194);\n    i0.ɵɵelementStart(1, \"label\", 10);\n    i0.ɵɵtext(2, \" Type trainee's PIN or name...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 195);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_171_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r247 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 194);\n    i0.ɵɵelementStart(1, \"div\", 196);\n    i0.ɵɵelementStart(2, \"input\", 197, 198);\n    i0.ɵɵlistener(\"change\", function EvaluationTestEntryComponent_ng_template_171_div_26_Template_input_change_2_listener() {\n      i0.ɵɵrestoreView(_r247);\n\n      const _r245 = i0.ɵɵreference(3);\n\n      const ctx_r246 = i0.ɵɵnextContext(2);\n      return ctx_r246.loadAttachment(_r245.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 199);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_171_div_26_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r247);\n      const ctx_r248 = i0.ɵɵnextContext(2);\n      return ctx_r248.getTraineesByExcel();\n    });\n    i0.ɵɵtext(5, \"Get Trainees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 200);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_171_div_26_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r247);\n      const ctx_r249 = i0.ɵɵnextContext(2);\n      return ctx_r249.downloadSampleFile();\n    });\n    i0.ɵɵtext(7, \"Download sample file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_171_h5_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h5\", 201);\n    i0.ɵɵtext(1, \"Select Trainees\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_171_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r251 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 202);\n    i0.ɵɵelementStart(1, \"input\", 203);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_171_div_28_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r251);\n      const ctx_r250 = i0.ɵɵnextContext(2);\n      return ctx_r250.select_All = $event;\n    })(\"change\", function EvaluationTestEntryComponent_ng_template_171_div_28_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r251);\n      const ctx_r252 = i0.ɵɵnextContext(2);\n      return ctx_r252.selectAll($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 204);\n    i0.ɵɵtext(3, \" Select All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r241 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r241.select_All)(\"ngModelOptions\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_171_div_29_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r256 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 208);\n    i0.ɵɵelementStart(1, \"div\", 209);\n    i0.ɵɵelementStart(2, \"input\", 210);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestEntryComponent_ng_template_171_div_29_li_2_Template_input_ngModelChange_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r256);\n      const item_r254 = restoredCtx.$implicit;\n      return item_r254.selected = $event;\n    })(\"change\", function EvaluationTestEntryComponent_ng_template_171_div_29_li_2_Template_input_change_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r256);\n      const item_r254 = restoredCtx.$implicit;\n      const ctx_r257 = i0.ɵɵnextContext(3);\n      return ctx_r257.onChangeTraineeSelection(item_r254);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 211);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 212);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r254 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"name\", item_r254.Id);\n    i0.ɵɵpropertyInterpolate(\"id\", item_r254.Id);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r254.Id);\n    i0.ɵɵproperty(\"ngModel\", item_r254.selected)(\"ngModelOptions\", i0.ɵɵpureFunction0(9, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"for\", item_r254.Id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", item_r254.Name, \" - \", item_r254.PIN, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r254.Position);\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_171_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 205);\n    i0.ɵɵelementStart(1, \"ul\", 206);\n    i0.ɵɵtemplate(2, EvaluationTestEntryComponent_ng_template_171_div_29_li_2_Template, 7, 10, \"li\", 207);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r242 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r242.traineeList);\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_171_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 183);\n    i0.ɵɵelementStart(1, \"h5\", 201);\n    i0.ɵɵtext(2, \"Selected Trainees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_171_div_32_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r261 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 208);\n    i0.ɵɵelementStart(1, \"div\", 209);\n    i0.ɵɵelementStart(2, \"input\", 214);\n    i0.ɵɵlistener(\"change\", function EvaluationTestEntryComponent_ng_template_171_div_32_li_2_Template_input_change_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r261);\n      const item_r259 = restoredCtx.$implicit;\n      const ctx_r260 = i0.ɵɵnextContext(3);\n      return ctx_r260.removeSelected(item_r259);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 211);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 212);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r259 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"name\", item_r259.Id);\n    i0.ɵɵpropertyInterpolate(\"id\", item_r259.Id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"for\", item_r259.Id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", item_r259.Name, \" - \", item_r259.PIN, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r259.Position);\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_171_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 213);\n    i0.ɵɵelementStart(1, \"ul\", 206);\n    i0.ɵɵtemplate(2, EvaluationTestEntryComponent_ng_template_171_div_32_li_2_Template, 7, 6, \"li\", 207);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r244 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r244.selectedTraineeList);\n  }\n}\n\nfunction EvaluationTestEntryComponent_ng_template_171_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r263 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 120);\n    i0.ɵɵelementStart(1, \"h4\", 171);\n    i0.ɵɵtext(2, \"Select Trainees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_171_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r263);\n      const ctx_r262 = i0.ɵɵnextContext();\n      return ctx_r262.modalTraineeHide();\n    });\n    i0.ɵɵelement(4, \"i\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 124);\n    i0.ɵɵelementStart(6, \"form\", 172);\n    i0.ɵɵelementStart(7, \"div\", 0);\n    i0.ɵɵelementStart(8, \"div\", 173);\n    i0.ɵɵelementStart(9, \"div\", 174);\n    i0.ɵɵelementStart(10, \"div\", 175);\n    i0.ɵɵelementStart(11, \"div\", 176);\n    i0.ɵɵelementStart(12, \"label\", 10);\n    i0.ɵɵtext(13, \" Find trainees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 177);\n    i0.ɵɵelementStart(15, \"div\", 178);\n    i0.ɵɵelement(16, \"input\", 179);\n    i0.ɵɵelementStart(17, \"label\", 180);\n    i0.ɵɵtext(18, \"By Selection\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 178);\n    i0.ɵɵelement(20, \"input\", 181);\n    i0.ɵɵelementStart(21, \"label\", 182);\n    i0.ɵɵtext(22, \"By Excel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 174);\n    i0.ɵɵelementStart(24, \"div\", 183);\n    i0.ɵɵtemplate(25, EvaluationTestEntryComponent_ng_template_171_div_25_Template, 4, 0, \"div\", 184);\n    i0.ɵɵtemplate(26, EvaluationTestEntryComponent_ng_template_171_div_26_Template, 8, 0, \"div\", 184);\n    i0.ɵɵtemplate(27, EvaluationTestEntryComponent_ng_template_171_h5_27_Template, 2, 0, \"h5\", 185);\n    i0.ɵɵtemplate(28, EvaluationTestEntryComponent_ng_template_171_div_28_Template, 4, 3, \"div\", 186);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, EvaluationTestEntryComponent_ng_template_171_div_29_Template, 3, 1, \"div\", 187);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 188);\n    i0.ɵɵtemplate(31, EvaluationTestEntryComponent_ng_template_171_div_31_Template, 3, 0, \"div\", 189);\n    i0.ɵɵtemplate(32, EvaluationTestEntryComponent_ng_template_171_div_32_Template, 3, 1, \"div\", 190);\n    i0.ɵɵelementStart(33, \"div\", 191);\n    i0.ɵɵelementStart(34, \"strong\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 192);\n    i0.ɵɵelementStart(37, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_171_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r263);\n      const ctx_r264 = i0.ɵɵnextContext();\n      return ctx_r264.modalTraineeHide();\n    });\n    i0.ɵɵelement(38, \"i\", 123);\n    i0.ɵɵtext(39, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"button\", 140);\n    i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_ng_template_171_Template_button_click_40_listener() {\n      i0.ɵɵrestoreView(_r263);\n      const ctx_r265 = i0.ɵɵnextContext();\n      return ctx_r265.onTraineeSelectionComplete();\n    });\n    i0.ɵɵelement(41, \"i\", 193);\n    i0.ɵɵtext(42, \" Done \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r46.traineeForm);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.tf.selectionType.value && ctx_r46.tf.selectionType.value === \"BySelection\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.tf.selectionType.value && ctx_r46.tf.selectionType.value === \"ByExcel\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.traineeList.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.traineeList.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.traineeList.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.selectedTraineeList.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.selectedTraineeList.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r46.selectedTraineeList.length, \" trainee(s) selected\");\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nconst _c2 = function () {\n  return [\"/evaluation-test-list\"];\n};\n\nexport class EvaluationTestEntryComponent {\n  constructor(appComponent, modalService, formBuilder, _service, toastr, route, config) {\n    this.appComponent = appComponent;\n    this.modalService = modalService;\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.toastr = toastr;\n    this.route = route;\n    this.submitted = false;\n    this.formTitle = \"Evaluation Test Entry\";\n    this.btnSaveText = \"Save\";\n    this.is_per = true;\n    this.ExamExists = false;\n    this.ExamExistsMsg = \"\";\n    this.modalTitle = \"Set Time\";\n    this.rows = [];\n    this.loadingIndicator = false;\n    this.ColumnMode = ColumnMode;\n    this.isEdit = false;\n    this.mcqList = [];\n    this.loadingMCQ = false;\n    this.btnMCQSaveText = \"Save\";\n    this.modalMCQTitle = \"Add MCQ Questions\";\n    this.truFalseList = [];\n    this.tfQuestionList = [];\n    this.loadingTFQ = false;\n    this.btnTFQSaveText = \"Save\";\n    this.modalTFTitle = \"Add True/False Questions\";\n    this.figList = [];\n    this.figQuestionList = [];\n    this.loadingFIG = false;\n    this.btnFIGSaveText = \"Save\";\n    this.modalFIGTitle = \"Add fill in the gaps Questions\";\n    this.matchingList = [];\n    this.matchingQuestionList = [];\n    this.loadingMatching = false;\n    this.btnMatchingSaveText = \"Save\";\n    this.modalMatchingTitle = \"Add Left Right Matching Questions\";\n    this.writtenList = [];\n    this.writtenQuestionList = [];\n    this.loadingWritten = false;\n    this.btnWrittenSaveText = \"Save\";\n    this.modalWrittenTitle = \"Add Written Questions\";\n    this.modalConfig = {\n      class: \"gray modal-lg\",\n      backdrop: \"static\"\n    };\n    this.courseList = [];\n    this.examList = [];\n    this.setList = [];\n    this.mcqQuestionList = [];\n    this.traineeList = [];\n    this.selectedTraineeList = [];\n    this.allowedTrainees = [];\n    this.traineeSelected = false;\n    this.categoryList = [];\n    this.departmentList = [];\n    this.departmentSelected = false;\n    this.divisionList = [];\n    this.divisionSelected = false;\n    this.unitList = [];\n    this.unitSelected = false;\n    this.allowList = [\"All\", \"Division\", \"Department\", \"Unit\", \"Trainee\"];\n    this.select_All = false;\n    this.modalxLConfig = {\n      class: \"gray modal-xl\",\n      backdrop: \"static\"\n    };\n    this.toolbar = [[\"bold\", \"italic\"], [\"underline\", \"strike\"], [\"ordered_list\", \"bullet_list\"], [{\n      heading: [\"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\"]\n    }], [\"text_color\", \"background_color\"], [\"align_left\", \"align_center\", \"align_right\", \"align_justify\"]];\n    this.scrollBarHorizontal = window.innerWidth < 1200;\n\n    window.onresize = () => {\n      this.scrollBarHorizontal = window.innerWidth < 1200;\n    };\n\n    this.bsConfig = Object.assign({}, {\n      containerClass: \"theme-blue\"\n    });\n    config.seconds = false;\n    config.spinners = false;\n\n    if (this.route.snapshot.queryParamMap.has(\"id\")) {\n      this.id = this.route.snapshot.queryParamMap.get(\"id\");\n      this.isEdit = true;\n      this.getMarks();\n      this.getItem();\n    }\n\n    console.log(moment.tz.guess());\n  }\n\n  handleSelectClick(selectElement) {\n    this.appComponent.handleSelectClick(selectElement);\n  }\n\n  ngOnInit() {\n    this.editor = new Editor();\n    this.entryForm = this.formBuilder.group({\n      id: [this.id],\n      instructions: [null],\n      exam: [null, [Validators.required]],\n      duration: [null, [Validators.required]],\n      categoryId: [true, [Validators.required]],\n      mcqOnly: [true],\n      random: [false],\n      publish: [false],\n      quota: [null, [Validators.required]],\n      no_of_mcq: [null, [Validators.required]],\n      no_of_tfq: [null],\n      no_of_figq: [null],\n      no_of_mq: [null],\n      no_of_wq: [null],\n      startDate: [null],\n      endDate: [null],\n      allowFor: [null, [Validators.required]],\n      division: [null],\n      department: [null],\n      unit: [null],\n      pin: [null],\n      active: true,\n      order: [null]\n    });\n    if (!this.isEdit) this.getMarks();\n    this.traineeForm = this.formBuilder.group({\n      selectionType: [null],\n      pin: [null],\n      file: [null]\n    });\n    this.traineeForm.get(\"pin\").valueChanges.pipe(debounceTime(700)).subscribe(value => {\n      this.getTraineelList(value);\n    });\n    this.getCategoryList();\n  }\n\n  get f() {\n    return this.entryForm.controls;\n  }\n\n  get tf() {\n    return this.traineeForm.controls;\n  }\n\n  getCategoryList() {\n    this._service.get(\"learning-hour-category/dropdown-list\").subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.categoryList = res.Data;\n    }, err => {});\n  }\n\n  getMarks() {\n    this._service.get(\"configuration/get-exam-data\").subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.entryForm.controls[\"instructions\"].setValue(res.Data.ExamInstruction);\n      this.MCQMark = res.Data.MCQMark;\n      this.TrueFalseMark = res.Data.TrueFalseMark;\n      this.FIGMark = res.Data.FIGMark;\n      this.MatchingMark = res.Data.MatchingMark;\n      this.WrittenMark = res.Data.WrittenMark;\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  formatAMPM(date) {\n    var hours = date.getHours();\n    var minutes = date.getMinutes();\n    var ampm = hours >= 12 ? \"PM\" : \"AM\";\n    hours = hours % 12;\n    hours = hours ? hours : 12; // the hour '0' should be '12'\n\n    minutes = minutes < 10 ? \"0\" + minutes : minutes;\n    var strTime = hours + \":\" + minutes + \" \" + ampm;\n    return strTime;\n  }\n\n  getItem() {\n    this.blockUI.start(\"Getting data. Please wait ...\");\n\n    this._service.get(\"evaluation-exam/get/\" + this.id).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } // let startDate, endDate, startTime, endTime;\n      // if (res.Data.StartDate && res.Data.EndDate) {\n      //   startDate = new Date(res.Data.StartDate);\n      //   startDate.setMinutes(startDate.getMinutes());\n      //   endDate = new Date(res.Data.EndDate);\n      //   endDate.setMinutes(endDate.getMinutes());\n      //   startTime = this.formatAMPM(startDate);\n      //   endTime = this.formatAMPM(endDate);\n      // }\n\n\n      this.entryForm.controls[\"exam\"].setValue(res.Data.ExamName);\n      this.entryForm.controls[\"instructions\"].setValue(res.Data.ExamInstructions);\n      this.entryForm.controls[\"categoryId\"].setValue(res.Data.CategoryId);\n      this.entryForm.controls[\"quota\"].setValue(res.Data.Quota);\n      this.entryForm.controls[\"random\"].setValue(res.Data.Random);\n      this.entryForm.controls[\"publish\"].setValue(res.Data.Publish); // this.entryForm.controls['minPercentage'].setValue(res.Data.MinPercentageForEvaluation);\n      // this.entryForm.controls['minMarks'].setValue(res.Data.MinMarksForEvaluation);\n\n      this.entryForm.controls[\"duration\"].setValue(res.Data.DurationMnt);\n      this.entryForm.controls[\"no_of_mcq\"].setValue(res.Data.ExamMCQNo);\n      this.entryForm.controls[\"no_of_tfq\"].setValue(res.Data.ExamTrueFalseNo);\n      this.entryForm.controls[\"no_of_figq\"].setValue(res.Data.ExamFIGNo);\n      this.entryForm.controls[\"no_of_mq\"].setValue(res.Data.ExamMatchingNo);\n      this.entryForm.controls[\"no_of_wq\"].setValue(res.Data.ExamWritingNo);\n      this.entryForm.controls[\"mcqOnly\"].setValue(res.Data.MCQOnly);\n      this.entryForm.controls[\"allowFor\"].setValue(res.Data.AllowFor);\n      this.entryForm.controls[\"active\"].setValue(res.Data.Active);\n      this.entryForm.controls[\"order\"].setValue(res.Data.Order);\n      this.imageUrl = environment.baseUrl + res.Data.ImagePath;\n      this.editChangeAllowedFor(res.Data.AllowFor, res.Data.DivisionId, res.Data.DepartmentId, res.Data.UnitId, res.Data.Trainees); // **FIX: Don't double-convert time - backend already returns local time**\n      // WHY: Backend now stores local time, so no need to convert from UTC\n      // BENEFIT: Prevents time shifting issues when editing exams\n\n      if (res.Data.StartDate) this.entryForm.controls[\"startDate\"].setValue(moment(res.Data.StartDate).format(\"yyyy-MM-DDTHH:mm:ss\"));\n      if (res.Data.EndDate) this.entryForm.controls[\"endDate\"].setValue(moment(res.Data.EndDate).format(\"yyyy-MM-DDTHH:mm:ss\"));\n      this.btnSaveText = \"Update\";\n      this.getMCQList();\n      this.getTFQList();\n      this.geWrittenList();\n      this.geFIGIGList();\n      this.getMatchingList();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n\n    this.submitted = false;\n  }\n\n  onFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    if (!this.entryForm.value.duration) {\n      this.toastr.warning(\"Please insert exam duration for this exam\", \"WARNING!\", {\n        timeOut: 3000\n      });\n      return;\n    }\n\n    if (!this.entryForm.value.id && !this.imageUrl) {\n      this.toastr.warning(\"Please upload a cover photo\", \"Warning!\");\n      return;\n    }\n\n    if (this.entryForm.value.allowFor === \"Trainee\" && this.allowedTrainees.length === 0) {\n      this.toastr.warning(\"Please select at least one trainee \", \"Warning!\");\n      return;\n    }\n\n    this.blockUI.start(\"Saving...\");\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      ExamName: this.entryForm.value.exam,\n      ExamInstructions: this.entryForm.value.instructions ? this.entryForm.value.instructions.trim() : \"\",\n      Quota: Number(this.entryForm.value.quota),\n      Random: Number(this.entryForm.value.random),\n      Publish: Number(this.entryForm.value.publish),\n      ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,\n      ExamTrueFalseNo: this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,\n      ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,\n      ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,\n      ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,\n      CategoryId: this.entryForm.value.categoryId,\n      MCQOnly: this.entryForm.value.mcqOnly,\n      DurationMnt: Number(this.entryForm.value.duration),\n      QuesType: \"NoQues\",\n      StartDate: this.entryForm.value.startDate ? moment(this.entryForm.value.startDate).toISOString() : \"\",\n      EndDate: this.entryForm.value.endDate ? moment(this.entryForm.value.endDate).toISOString() : \"\",\n      AllowFor: this.entryForm.value.allowFor,\n      DivisionId: this.entryForm.value.division,\n      DepartmentId: this.entryForm.value.department,\n      UnitId: this.entryForm.value.unit,\n      Trainees: this.allowedTrainees.map(x => x.Id),\n      Active: this.entryForm.value.active,\n      Order: this.entryForm.value.order\n    };\n    const formdata = new FormData();\n    formdata.append(\"Model\", JSON.stringify(obj));\n    if (this.imageFile) formdata.append(\"Image\", this.imageFile);\n    console.log(\"obj\", obj);\n\n    this._service.post(\"evaluation-exam/create-or-update\", formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, \"Success!\", {\n        timeOut: 2000\n      });\n      this.entryForm.controls[\"id\"].setValue(res.Data);\n      this.btnSaveText = \"Update\";\n      this.id = res.Data;\n      this.isEdit = true;\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  clearForm() {\n    this.entryForm.reset();\n    this.submitted = false;\n    this.formTitle = \"Create Evaluation Test\";\n    this.btnSaveText = \"Save\";\n    this.entryForm.controls[\"mcqOnly\"].setValue(true);\n  }\n\n  deleteQuestion(id, type) {\n    let url = \"\";\n\n    switch (type) {\n      case \"MCQ\":\n        url = \"evaluation-exam/delete-mcq/\" + id;\n        break;\n\n      case \"TFQ\":\n        url = \"evaluation-exam/delete-true-false/\" + id;\n        break;\n\n      case \"FIGQ\":\n        url = \"evaluation-exam/delete-fill-in-the-gap/\" + id;\n        break;\n\n      case \"WQ\":\n        url = \"evaluation-exam/delete-written/\" + id;\n        break;\n\n      case \"MatchingQ\":\n        url = \"evaluation-exam/delete-matching/\" + id;\n        break;\n    }\n\n    this.blockUI.start(\"Deleting data. Please wait ...\");\n\n    this._service.get(url).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, \"SUCCESS!\", {\n        timeOut: 2000\n      });\n\n      switch (type) {\n        case \"MCQ\":\n          this.getMCQList();\n          break;\n\n        case \"TFQ\":\n          this.getTFQList();\n          break;\n\n        case \"FIGQ\":\n          this.geFIGIGList();\n          break;\n\n        case \"MatchingQ\":\n          this.getMatchingList();\n          break;\n\n        case \"WQ\":\n          this.geWrittenList();\n          break;\n      }\n    }, err => {\n      this.toastr.error(err.message || err, \"Error!\", {\n        timeOut: 10000\n      });\n      this.blockUI.stop();\n    });\n  }\n\n  downloadQuestionList(type) {\n    let url = \"\",\n        title = \"\";\n    let timeZoneOffset = new Date().getTimezoneOffset();\n\n    switch (type) {\n      case \"MCQ\":\n        url = \"evaluation-exam/get-mcq-list-excel/\" + this.id + \"/\" + timeZoneOffset;\n        title = \"evaluation_test_mcq_file.xlsx\";\n        break;\n\n      case \"TFQ\":\n        url = \"evaluation-exam/get-true-false-list-excel/\" + this.id + \"/\" + timeZoneOffset;\n        title = \"evaluation_test_true_false_file.xlsx\";\n        break;\n\n      case \"FIGQ\":\n        url = \"evaluation-exam/get-fill-in=the=gap-list-excel/\" + this.id + \"/\" + timeZoneOffset;\n        title = \"evaluation_test_fill_in_the_gap_file.xlsx\";\n        break;\n\n      case \"MatchingQ\":\n        url = \"evaluation-exam/get-matching-list-excel/\" + this.id + \"/\" + timeZoneOffset;\n        title = \"evaluation_test_left_right_matching_file.xlsx\";\n        break;\n\n      case \"WQ\":\n        url = \"evaluation-exam/get-written-list-excel/\" + this.id + \"/\" + timeZoneOffset;\n        title = \"evaluation_test_written_file.xlsx\";\n        break;\n\n      default:\n        break;\n    }\n\n    this.blockUI.start(\"Generating excel file. Please wait ...\");\n    return this._service.downloadFile(url).subscribe(res => {\n      this.blockUI.stop();\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement(\"a\");\n      link.href = url;\n      link.download = title;\n      link.click();\n    }, error => {\n      this.toastr.error(error.message || error, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  } // ============== MCQ Question ====================\n\n\n  getMCQList() {\n    const obj = {\n      examId: this.id // examId: this.entryForm.value.examId,\n      // setId: this.entryForm.value.setId\n\n    };\n    this.loadingMCQ = true;\n\n    this._service.get(\"evaluation-exam/get-mcq-list/\" + this.id).subscribe(res => {\n      setTimeout(() => {\n        this.loadingMCQ = false;\n      }, 1000);\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.mcqList = res.Data;\n      console.log(\"res.Data\", res.Data); // let answers = [];\n      // res.Data.forEach(element => {\n      //   answers = element.Answers.split(',');\n      //   this.mcqList.push({\n      //     Question: element.Question,\n      //     Options: [{ Text: element.Option1, Selected: answers.indexOf('0') !== -1 },\n      //     { Text: element.Option2, Selected: answers.indexOf('1') !== -1 },\n      //     { Text: element.Option3, Selected: answers.indexOf('2') !== -1 },\n      //     { Text: element.Option4, Selected: answers.indexOf('3') !== -1 }],\n      //     Mark: element.Mark\n      //   });\n      // });\n    }, err => {\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      setTimeout(() => {\n        this.loadingMCQ = false;\n      }, 1000);\n    });\n  }\n\n  openMCQModal(template) {\n    let answers = [];\n\n    if (this.mcqList.length === 0) {\n      this.btnMCQSaveText = \"Save\";\n      this.addNewMCQ();\n    } else {\n      this.mcqList.forEach(element => {\n        answers = element.Answers.split(\",\");\n        this.mcqQuestionList.push({\n          Question: element.Question,\n          Options: [{\n            Text: element.Option1,\n            Selected: answers.indexOf(\"1\") !== -1\n          }, {\n            Text: element.Option2,\n            Selected: answers.indexOf(\"2\") !== -1\n          }, {\n            Text: element.Option3,\n            Selected: answers.indexOf(\"3\") !== -1\n          }, {\n            Text: element.Option4,\n            Selected: answers.indexOf(\"4\") !== -1\n          }],\n          Mark: element.Mark\n        });\n      });\n    }\n\n    this.modalMCQRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  modalMCQHide() {\n    this.modalMCQRef.hide();\n    this.mcqQuestionList = [];\n    this.mcqFile = null;\n  }\n\n  deleteMCQ(index) {\n    this.mcqQuestionList.splice(index, 1);\n  }\n\n  addNewMCQ() {\n    this.mcqQuestionList.push({\n      Question: null,\n      Options: [{\n        Text: null,\n        Selected: false\n      }, {\n        Text: null,\n        Selected: false\n      }, {\n        Text: null,\n        Selected: false\n      }, {\n        Text: null,\n        Selected: false\n      }],\n      Mark: this.MCQMark\n    });\n  }\n\n  downloadSampleMCQ() {\n    return this._service.downloadFile(\"exam/download-sample-question\", {\n      quesType: \"MCQ\"\n    }).subscribe(res => {\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement(\"a\");\n      link.href = url;\n      link.download = \"evaluation_test_mcq_sample_file.xlsx\";\n      link.click();\n    }, error => {\n      this.toastr.error(error.message || error, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  onMCQFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      this.toastr.warning(\"Please fill up all the details of the exam first\", \"WARNING!\", {\n        timeOut: 3000\n      });\n      return;\n    }\n\n    if (!this.entryForm.value.duration) {\n      this.toastr.warning(\"Please insert exam duration for this exam\", \"WARNING!\", {\n        timeOut: 3000\n      });\n      return;\n    }\n\n    let questions = [];\n\n    for (let i = 0; i < this.mcqQuestionList.length; i++) {\n      const element = this.mcqQuestionList[i];\n\n      if (!element.Question) {\n        this.toastr.error(\"Please Enter Question for Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      } // if (!element.Options[0].Text || !element.Options[1].Text || !element.Options[2].Text || !element.Options[3].Text) {\n\n\n      if (element.Options.filter(x => x.Text === null || x.Text === \"\").length > 0) {\n        this.toastr.error(\"Value missing for an option of Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      } // if (!element.Option1.Selected && !element.Option1.Selected && !element.Option1.Selected && !element.Option1.Selected) {\n\n\n      if (element.Options.filter(x => x.Selected).length === 0) {\n        this.toastr.error(\"No option has been selected for answer of Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      if (!element.Mark) {\n        this.toastr.error(\"Please enter mark for Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      questions.push({\n        Question: element.Question,\n        Mark: element.Mark,\n        Option1: element.Options[0].Text,\n        Option2: element.Options[1].Text,\n        Option3: element.Options[2].Text,\n        Option4: element.Options[3].Text,\n        Answers: element.Options.map(function (x, i) {\n          if (x.Selected) return i + 1;else return -1;\n        }).filter(x => x >= 0).join()\n      });\n    }\n\n    if (!this.entryForm.value.id && !this.imageUrl) {\n      this.toastr.warning(\"Please upload a cover photo\", \"Warning!\");\n      return;\n    }\n\n    if (this.entryForm.value.allowFor === \"Trainee\" && this.allowedTrainees.length === 0) {\n      this.toastr.warning(\"Please select at least one trainee \", \"Warning!\");\n      return;\n    }\n\n    this.blockUI.start(\"Saving...\");\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      ExamName: this.entryForm.value.exam,\n      ExamInstructions: this.entryForm.value.instructions ? this.entryForm.value.instructions.trim() : \"\",\n      Quota: Number(this.entryForm.value.quota),\n      Random: Number(this.entryForm.value.random),\n      Publish: Number(this.entryForm.value.publish),\n      ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,\n      ExamTrueFalseNo: this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,\n      ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,\n      ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,\n      ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,\n      CategoryId: this.entryForm.value.categoryId,\n      MCQOnly: this.entryForm.value.mcqOnly,\n      // MinPercentageForEvaluation: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,\n      // MinMarksForEvaluation: Number(this.entryForm.value.minMarks),\n      DurationMnt: Number(this.entryForm.value.duration),\n      MCQs: questions,\n      QuesType: \"MCQ\",\n      StartDate: this.entryForm.value.startDate ? moment(this.entryForm.value.startDate).toISOString() : \"\",\n      EndDate: this.entryForm.value.endDate ? moment(this.entryForm.value.endDate).toISOString() : \"\",\n      AllowFor: this.entryForm.value.allowFor,\n      DivisionId: this.entryForm.value.division,\n      DepartmentId: this.entryForm.value.department,\n      UnitId: this.entryForm.value.unit,\n      Trainees: this.allowedTrainees.map(x => x.Id)\n    };\n    console.log(\"obj\", obj);\n    const formdata = new FormData();\n    formdata.append(\"Model\", JSON.stringify(obj));\n    if (this.imageFile) formdata.append(\"Image\", this.imageFile);\n\n    if (this.mcqFile) {\n      formdata.append(\"File\", this.mcqFile);\n    }\n\n    this._service.post(\"evaluation-exam/create-or-update\", formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, \"Success!\", {\n        timeOut: 2000\n      });\n      this.entryForm.controls[\"id\"].setValue(res.Data);\n      this.btnSaveText = \"Update\";\n      this.id = res.Data;\n      this.isEdit = true;\n      this.getMCQList();\n      this.modalMCQHide();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  loadMCQFile(files) {\n    if (files.length === 0) return;\n    this.mcqFile = files[0];\n  }\n\n  resetMCQFile(element) {\n    element.value = \"\";\n    this.mcqFile = null;\n  } // ============== True/False Question ====================\n\n\n  getTFQList() {\n    const obj = {\n      examId: this.id\n    };\n    this.loadingTFQ = true;\n\n    this._service.get(\"evaluation-exam/get-true-false-list/\" + this.id).subscribe(res => {\n      setTimeout(() => {\n        this.loadingTFQ = false;\n      }, 1000);\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.truFalseList = res.Data;\n    }, err => {\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      setTimeout(() => {\n        this.loadingTFQ = false;\n      }, 1000);\n    });\n  }\n\n  openTFQModal(template) {\n    if (this.truFalseList.length === 0) {\n      this.btnTFQSaveText = \"Save\";\n      this.addNewTFQ();\n    } else {\n      this.truFalseList.forEach(element => {\n        this.tfQuestionList.push({\n          Question: element.Question,\n          Answer: element.Answer,\n          CorrectAnswer: element.CorrectAnswer,\n          Mark: element.Mark\n        });\n      });\n    }\n\n    this.modalTFRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  modalTFQHide() {\n    this.modalTFRef.hide();\n    this.tfQuestionList = [];\n    this.tfqFile = null;\n  }\n\n  deleteTFQ(index) {\n    this.tfQuestionList.splice(index, 1);\n  }\n\n  addNewTFQ() {\n    this.tfQuestionList.push({\n      Id: null,\n      Question: null,\n      Answer: true,\n      CorrectAnswer: null,\n      Mark: this.TrueFalseMark\n    });\n  }\n\n  onTFQFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    for (let i = 0; i < this.tfQuestionList.length; i++) {\n      const element = this.tfQuestionList[i];\n\n      if (!element.Question) {\n        this.toastr.error(\"Please Enter Question for Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      } // if (!element.Answer && !element.CorrectAnswer) {\n      // if (!element.Answer && !element.CorrectAnswer) {\n      //   console.log('element.Answer',element.Answer);\n      //   console.log('element.CorrectAnswer',element.CorrectAnswer);\n      //   this.toastr.error(\n      //     \"Please enter correct answer for Question: \" + ++i,\n      //     \"WARNING!\",\n      //     { timeOut: 4000 }\n      //   );\n      //   return false;\n      // }\n\n\n      if (!element.Mark) {\n        this.toastr.error(\"Please enter mark for Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      }\n    }\n\n    if (!this.entryForm.value.id && !this.imageUrl) {\n      this.toastr.warning(\"Please upload a cover photo\", \"Warning!\");\n      return;\n    }\n\n    if (this.entryForm.value.allowFor === \"Trainee\" && this.allowedTrainees.length === 0) {\n      this.toastr.warning(\"Please select at least one trainee \", \"Warning!\");\n      return;\n    }\n\n    this.blockUI.start(\"Saving...\");\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      ExamName: this.entryForm.value.exam,\n      ExamInstructions: this.entryForm.value.instructions ? this.entryForm.value.instructions.trim() : \"\",\n      Quota: Number(this.entryForm.value.quota),\n      Random: Number(this.entryForm.value.random),\n      Publish: Number(this.entryForm.value.publish),\n      ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,\n      ExamTrueFalseNo: this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,\n      ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,\n      ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,\n      ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,\n      CategoryId: this.entryForm.value.categoryId,\n      MCQOnly: this.entryForm.value.mcqOnly,\n      // MinPercentageForEvaluation: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,\n      // MinMarksForEvaluation: Number(this.entryForm.value.minMarks),\n      DurationMnt: Number(this.entryForm.value.duration),\n      TruFalseQs: this.tfQuestionList,\n      QuesType: \"TrueFalse\",\n      StartDate: this.entryForm.value.startDate ? moment(this.entryForm.value.startDate).toISOString() : \"\",\n      EndDate: this.entryForm.value.endDate ? moment(this.entryForm.value.endDate).toISOString() : \"\",\n      AllowFor: this.entryForm.value.allowFor,\n      DivisionId: this.entryForm.value.division,\n      DepartmentId: this.entryForm.value.department,\n      UnitId: this.entryForm.value.unit,\n      Trainees: this.allowedTrainees.map(x => x.Id)\n    };\n    const formdata = new FormData();\n    formdata.append(\"Model\", JSON.stringify(obj));\n    if (this.imageFile) formdata.append(\"Image\", this.imageFile);\n\n    if (this.tfqFile) {\n      formdata.append(\"File\", this.tfqFile);\n    }\n\n    this._service.post(\"evaluation-exam/create-or-update\", formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, \"Success!\", {\n        timeOut: 2000\n      });\n      this.entryForm.controls[\"id\"].setValue(res.Data);\n      this.btnSaveText = \"Update\";\n      this.id = res.Data;\n      this.isEdit = true;\n      this.getTFQList();\n      this.modalTFQHide();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  downloadSampleTFQ() {\n    return this._service.downloadFile(\"exam/download-sample-question\", {\n      quesType: \"TrueFalse\"\n    }).subscribe(res => {\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement(\"a\");\n      link.href = url;\n      link.download = \"evaluation_test_true_false_sample_file.xlsx\";\n      link.click();\n    }, error => {\n      this.toastr.error(error.message || error, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  loadTFQFile(files) {\n    if (files.length === 0) return;\n    this.tfqFile = files[0];\n  }\n\n  resetTFQFile(element) {\n    element.value = \"\";\n    this.tfqFile = null;\n  } // ============== Fill in the gaps Question ====================\n\n\n  geFIGIGList() {\n    const obj = {\n      examId: this.id\n    };\n    this.loadingFIG = true;\n\n    this._service.get(\"evaluation-exam/get-fill-in-the-gap-list/\" + this.id).subscribe(res => {\n      setTimeout(() => {\n        this.loadingFIG = false;\n      }, 1000);\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.figList = res.Data;\n    }, err => {\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      setTimeout(() => {\n        this.loadingFIG = false;\n      }, 1000);\n    });\n  }\n\n  openFIGModal(template) {\n    if (this.figList.length === 0) {\n      this.btnFIGSaveText = \"Save\";\n      this.addNewFIG();\n    } else {\n      this.figList.forEach(element => {\n        this.figQuestionList.push({\n          Question: element.Question,\n          Answer: element.Answer,\n          //Serial: element.Serial,\n          Mark: element.Mark\n        });\n      });\n    }\n\n    this.modalFIGRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  modalFIGHide() {\n    this.modalFIGRef.hide();\n    this.figQuestionList = [];\n    this.figqFile = null;\n  }\n\n  deleteFIG(index) {\n    this.figQuestionList.splice(index, 1);\n  }\n\n  addNewFIG() {\n    this.figQuestionList.push({\n      Question: null,\n      Answer: null,\n      //Serial: null,\n      Mark: this.FIGMark\n    });\n  }\n\n  onFIGFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    for (let i = 0; i < this.figQuestionList.length; i++) {\n      const element = this.figQuestionList[i];\n\n      if (!element.Question) {\n        this.toastr.error(\"Please Enter Question for Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      if (!element.Answer) {\n        this.toastr.error(\"Please enter answer for Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      if (!element.Mark) {\n        this.toastr.error(\"Please enter mark for Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      }\n    }\n\n    if (!this.entryForm.value.id && !this.imageUrl) {\n      this.toastr.warning(\"Please upload a cover photo\", \"Warning!\");\n      return;\n    }\n\n    if (this.entryForm.value.allowFor === \"Trainee\" && this.allowedTrainees.length === 0) {\n      this.toastr.warning(\"Please select at least one trainee \", \"Warning!\");\n      return;\n    }\n\n    this.blockUI.start(\"Saving...\");\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      ExamName: this.entryForm.value.exam,\n      ExamInstructions: this.entryForm.value.instructions ? this.entryForm.value.instructions.trim() : \"\",\n      Quota: Number(this.entryForm.value.quota),\n      Random: Number(this.entryForm.value.random),\n      Publish: Number(this.entryForm.value.publish),\n      ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,\n      ExamTrueFalseNo: this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,\n      ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,\n      ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,\n      ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,\n      CategoryId: this.entryForm.value.categoryId,\n      MCQOnly: this.entryForm.value.mcqOnly,\n      // MinPercentageForEvaluation: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,\n      // MinMarksForEvaluation: Number(this.entryForm.value.minMarks),\n      DurationMnt: Number(this.entryForm.value.duration),\n      FIGQs: this.figQuestionList,\n      QuesType: \"FIG\",\n      StartDate: this.entryForm.value.startDate ? moment(this.entryForm.value.startDate).toISOString() : \"\",\n      EndDate: this.entryForm.value.endDate ? moment(this.entryForm.value.endDate).toISOString() : \"\",\n      AllowFor: this.entryForm.value.allowFor,\n      DivisionId: this.entryForm.value.division,\n      DepartmentId: this.entryForm.value.department,\n      UnitId: this.entryForm.value.unit,\n      Trainees: this.allowedTrainees.map(x => x.Id) // Active:this.entryForm.value.active\n\n    };\n    const formdata = new FormData();\n    formdata.append(\"Model\", JSON.stringify(obj));\n    if (this.imageFile) formdata.append(\"Image\", this.imageFile);\n\n    if (this.figqFile) {\n      formdata.append(\"File\", this.figqFile);\n    }\n\n    this._service.post(\"evaluation-exam/create-or-update\", formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, \"Success!\", {\n        timeOut: 2000\n      });\n      this.entryForm.controls[\"id\"].setValue(res.Data);\n      this.btnSaveText = \"Update\";\n      this.id = res.Data;\n      this.isEdit = true;\n      this.geFIGIGList();\n      this.modalFIGHide();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  downloadSampleFIGQ() {\n    return this._service.downloadFile(\"exam/download-sample-question\", {\n      quesType: \"FIG\"\n    }).subscribe(res => {\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement(\"a\");\n      link.href = url;\n      link.download = \"evaluation_test_fill_in_the_gap_sample_file.xlsx\";\n      link.click();\n    }, error => {\n      this.toastr.error(error.message || error, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  loadFIGQFile(files) {\n    if (files.length === 0) return;\n    this.figqFile = files[0];\n  }\n\n  resetFIGQFile(element) {\n    element.value = \"\";\n    this.figqFile = null;\n  } // ============== Matching Question ====================\n\n\n  getMatchingList() {\n    const obj = {\n      examId: this.id\n    };\n    this.loadingMatching = true;\n\n    this._service.get(\"evaluation-exam/get-matching-list/\" + this.id).subscribe(res => {\n      setTimeout(() => {\n        this.loadingMatching = false;\n      }, 1000);\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.matchingList = res.Data;\n    }, err => {\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      setTimeout(() => {\n        this.loadingMatching = false;\n      }, 1000);\n    });\n  }\n\n  openMatchingModal(template) {\n    if (this.matchingList.length === 0) {\n      this.btnMatchingSaveText = \"Save\";\n      this.addNewMatching();\n    } else {\n      this.matchingList.forEach(element => {\n        this.matchingQuestionList.push({\n          LeftSide: element.LeftSide,\n          RightSide: element.RightSide,\n          Mark: element.Mark\n        });\n      });\n    }\n\n    this.modalMatchingRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  modalMatchingHide() {\n    this.modalMatchingRef.hide();\n    this.matchingQuestionList = [];\n    this.matchingqFile = null;\n  }\n\n  deleteMatching(index) {\n    this.matchingQuestionList.splice(index, 1);\n  }\n\n  addNewMatching() {\n    this.matchingQuestionList.push({\n      LeftSide: null,\n      RightSide: null,\n      Mark: this.MatchingMark\n    });\n  }\n\n  onMatchingFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    for (let i = 0; i < this.matchingQuestionList.length; i++) {\n      const element = this.matchingQuestionList[i];\n\n      if (!element.LeftSide) {\n        this.toastr.error(\"Please Enter Question for Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      if (!element.RightSide) {\n        this.toastr.error(\"Please enter right hand side for left hand side: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      if (!element.Mark) {\n        this.toastr.error(\"Please enter mark for Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      }\n    }\n\n    if (!this.entryForm.value.id && !this.imageUrl) {\n      this.toastr.warning(\"Please upload a cover photo\", \"Warning!\");\n      return;\n    }\n\n    if (this.entryForm.value.allowFor === \"Trainee\" && this.allowedTrainees.length === 0) {\n      this.toastr.warning(\"Please select at least one trainee \", \"Warning!\");\n      return;\n    }\n\n    this.blockUI.start(\"Saving...\");\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      ExamName: this.entryForm.value.exam,\n      ExamInstructions: this.entryForm.value.instructions ? this.entryForm.value.instructions.trim() : \"\",\n      Quota: Number(this.entryForm.value.quota),\n      Random: Number(this.entryForm.value.random),\n      Publish: Number(this.entryForm.value.publish),\n      ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,\n      ExamTrueFalseNo: this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,\n      ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,\n      ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,\n      ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,\n      CategoryId: this.entryForm.value.categoryId,\n      MCQOnly: this.entryForm.value.mcqOnly,\n      // MinPercentageForEvaluation: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,\n      // MinMarksForEvaluation: Number(this.entryForm.value.minMarks),\n      DurationMnt: Number(this.entryForm.value.duration),\n      MatchingQs: this.matchingQuestionList,\n      QuesType: \"Matching\",\n      StartDate: this.entryForm.value.startDate ? moment(this.entryForm.value.startDate).toISOString() : \"\",\n      EndDate: this.entryForm.value.endDate ? moment(this.entryForm.value.endDate).toISOString() : \"\",\n      AllowFor: this.entryForm.value.allowFor,\n      DivisionId: this.entryForm.value.division,\n      DepartmentId: this.entryForm.value.department,\n      UnitId: this.entryForm.value.unit,\n      Trainees: this.allowedTrainees.map(x => x.Id)\n    };\n    const formdata = new FormData();\n    formdata.append(\"Model\", JSON.stringify(obj));\n    if (this.imageFile) formdata.append(\"Image\", this.imageFile);\n\n    if (this.matchingqFile) {\n      formdata.append(\"File\", this.matchingqFile);\n    }\n\n    this._service.post(\"evaluation-exam/create-or-update\", formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, \"Success!\", {\n        timeOut: 2000\n      });\n      this.entryForm.controls[\"id\"].setValue(res.Data);\n      this.btnSaveText = \"Update\";\n      this.id = res.Data;\n      this.isEdit = true;\n      this.getMatchingList();\n      this.modalMatchingHide();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  downloadSampleMatchingQ() {\n    return this._service.downloadFile(\"exam/download-sample-question\", {\n      quesType: \"Matching\"\n    }).subscribe(res => {\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement(\"a\");\n      link.href = url;\n      link.download = \"evaluation_test_left_right_matching_sample_file.xlsx\";\n      link.click();\n    }, error => {\n      this.toastr.error(error.message || error, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  loadMatchingQFile(files) {\n    if (files.length === 0) return;\n    this.matchingqFile = files[0];\n  }\n\n  resetMatchingQFile(element) {\n    element.value = \"\";\n    this.matchingqFile = null;\n  } // ============== Written Question ====================\n\n\n  geWrittenList() {\n    this.loadingWritten = true;\n\n    this._service.get(\"evaluation-exam/get-written-list/\" + this.id).subscribe(res => {\n      setTimeout(() => {\n        this.loadingWritten = false;\n      }, 1000);\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.writtenList = res.Data;\n    }, err => {\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      setTimeout(() => {\n        this.loadingWritten = false;\n      }, 1000);\n    });\n  }\n\n  openWrittenModal(template) {\n    if (this.writtenList.length === 0) {\n      this.btnWrittenSaveText = \"Save\";\n      this.addNewWritten();\n    } else {\n      this.writtenList.forEach(element => {\n        this.writtenQuestionList.push({\n          Question: element.Question,\n          Mark: element.Mark\n        });\n      });\n    }\n\n    this.modalWrittenRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  modalWrittenHide() {\n    this.modalWrittenRef.hide();\n    this.writtenQuestionList = [];\n    this.wqFile = null;\n  }\n\n  deleteWritten(index) {\n    this.writtenQuestionList.splice(index, 1);\n  }\n\n  addNewWritten() {\n    this.writtenQuestionList.push({\n      Question: null,\n      Mark: this.WrittenMark\n    });\n  }\n\n  onWrittenFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    for (let i = 0; i < this.writtenQuestionList.length; i++) {\n      const element = this.writtenQuestionList[i];\n\n      if (!element.Question) {\n        this.toastr.error(\"Please Enter Question for Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      if (!element.Mark) {\n        this.toastr.error(\"Please enter mark for Question: \" + ++i, \"WARNING!\", {\n          timeOut: 4000\n        });\n        return false;\n      }\n    }\n\n    if (!this.entryForm.value.id && !this.imageUrl) {\n      this.toastr.warning(\"Please upload a cover photo\", \"Warning!\");\n      return;\n    }\n\n    if (this.entryForm.value.allowFor === \"Trainee\" && this.allowedTrainees.length === 0) {\n      this.toastr.warning(\"Please select at least one trainee \", \"Warning!\");\n      return;\n    }\n\n    this.blockUI.start(\"Saving...\");\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      ExamName: this.entryForm.value.exam,\n      ExamInstructions: this.entryForm.value.instructions ? this.entryForm.value.instructions.trim() : \"\",\n      Quota: Number(this.entryForm.value.quota),\n      Random: Number(this.entryForm.value.random),\n      Publish: Number(this.entryForm.value.publish),\n      ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,\n      ExamTrueFalseNo: this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,\n      ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,\n      ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,\n      ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,\n      CategoryId: this.entryForm.value.categoryId,\n      MCQOnly: this.entryForm.value.mcqOnly,\n      // MinPercentageForEvaluation: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,\n      // MinMarksForEvaluation: Number(this.entryForm.value.minMarks),\n      DurationMnt: Number(this.entryForm.value.duration),\n      WrittenQs: this.writtenQuestionList,\n      QuesType: \"Written\",\n      StartDate: this.entryForm.value.startDate ? moment(this.entryForm.value.startDate).toISOString() : \"\",\n      EndDate: this.entryForm.value.endDate ? moment(this.entryForm.value.endDate).toISOString() : \"\",\n      AllowFor: this.entryForm.value.allowFor,\n      DivisionId: this.entryForm.value.division,\n      DepartmentId: this.entryForm.value.department,\n      UnitId: this.entryForm.value.unit,\n      Trainees: this.allowedTrainees.map(x => x.Id)\n    };\n    const formdata = new FormData();\n    formdata.append(\"Model\", JSON.stringify(obj));\n    if (this.imageFile) formdata.append(\"Image\", this.imageFile);\n\n    if (this.wqFile) {\n      formdata.append(\"File\", this.wqFile);\n    }\n\n    this._service.post(\"evaluation-exam/create-or-update\", formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, \"Success!\", {\n        timeOut: 2000\n      });\n      this.entryForm.controls[\"id\"].setValue(res.Data);\n      this.btnSaveText = \"Update\";\n      this.id = res.Data;\n      this.isEdit = true;\n      this.geWrittenList();\n      this.modalWrittenHide();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  downloadSampleWQ() {\n    return this._service.downloadFile(\"exam/download-sample-question\", {\n      quesType: \"Written\"\n    }).subscribe(res => {\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement(\"a\");\n      link.href = url;\n      link.download = \"evaluation_test_written_question_sample_file.xlsx\";\n      link.click();\n    }, error => {\n      this.toastr.error(error.message || error, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  loadWQFile(files) {\n    if (files.length === 0) return;\n    this.wqFile = files[0];\n  }\n\n  resetWQFile(element) {\n    element.value = \"\";\n    this.wqFile = null;\n  }\n\n  modalHide() {\n    this.modalRef.hide();\n    this.submitted = false;\n  }\n\n  openModal(template) {\n    // this.entryForm.controls['isActive'].setValue(true);\n    this.modalRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  changeAllowedFor(event) {\n    if (event === \"Division\") {\n      if (this.divisionList.length <= 0) {\n        this.getDivisionList();\n      }\n\n      this.divisionSelected = true;\n      this.departmentSelected = false;\n      this.unitSelected = false;\n      this.traineeSelected = false;\n      return;\n    }\n\n    if (event === \"Department\") {\n      if (this.departmentList.length <= 0) {\n        this.getDepartmentList();\n      }\n\n      this.divisionSelected = false;\n      this.departmentSelected = true;\n      this.unitSelected = false;\n      this.traineeSelected = false;\n      return;\n    }\n\n    if (event === \"Unit\") {\n      if (this.unitList.length <= 0) {\n        this.getUnitList();\n      }\n\n      this.divisionSelected = false;\n      this.departmentSelected = false;\n      this.unitSelected = true;\n      this.traineeSelected = false;\n      return;\n    }\n\n    if (event === \"Trainee\") {\n      // if (this.traineeList.length <= 0) {\n      //   this.getTraineelList();\n      // }\n      this.divisionSelected = false;\n      this.departmentSelected = false;\n      this.unitSelected = false;\n      this.traineeSelected = true;\n      return;\n    }\n\n    if (event === \"All\") {\n      this.divisionSelected = false;\n      this.departmentSelected = false;\n      this.unitSelected = false;\n      this.traineeSelected = false;\n      return;\n    }\n  }\n\n  editChangeAllowedFor(event, divisionId, departmentId, unitId, trainees) {\n    if (event === \"Division\") {\n      if (this.divisionList.length <= 0) {\n        this.getDivisionList();\n      }\n\n      this.entryForm.controls[\"division\"].setValue(divisionId);\n      this.divisionSelected = true;\n      this.departmentSelected = false;\n      this.unitSelected = false;\n      this.traineeSelected = false;\n      return;\n    }\n\n    if (event === \"Department\") {\n      if (this.departmentList.length <= 0) {\n        this.getDepartmentList();\n      }\n\n      this.entryForm.controls[\"department\"].setValue(departmentId);\n      this.divisionSelected = false;\n      this.departmentSelected = true;\n      this.unitSelected = false;\n      this.traineeSelected = false;\n      return;\n    }\n\n    if (event === \"Unit\") {\n      if (this.unitList.length <= 0) {\n        this.getUnitList();\n      }\n\n      this.entryForm.controls[\"unit\"].setValue(unitId);\n      this.divisionSelected = false;\n      this.departmentSelected = false;\n      this.unitSelected = true;\n      this.traineeSelected = false;\n      return;\n    }\n\n    if (event === \"Trainee\") {\n      this.allowedTrainees = trainees;\n      this.divisionSelected = false;\n      this.departmentSelected = false;\n      this.unitSelected = false;\n      this.traineeSelected = true;\n      return;\n    }\n\n    if (event === \"All\") {\n      this.divisionSelected = false;\n      this.departmentSelected = false;\n      this.unitSelected = false;\n      this.traineeSelected = false;\n      return;\n    }\n  }\n\n  getDivisionList() {\n    this._service.get(\"division/dropdown-list\").subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.divisionList = res.Data;\n    }, err => {\n      this.toastr.warning(err.message || err, \"warning!\", {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  getDepartmentList() {\n    this._service.get(\"department/dropdown-list\").subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.departmentList = res.Data;\n    }, err => {\n      this.toastr.warning(err.message || err, \"warning!\", {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  getUnitList() {\n    this._service.get(\"unit/dropdown-list\").subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.unitList = res.Data;\n    }, err => {\n      this.toastr.warning(err.message || err, \"warning!\", {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  selectAll(event) {\n    this.traineeList.forEach(element => {\n      element.selected = event.target.checked;\n    });\n\n    if (event.target.checked) {\n      this.traineeList.forEach(elem => {\n        if (elem.selected && !this.selectedTraineeList.find(x => x.Id === elem.Id)) this.selectedTraineeList.push(elem);\n      });\n    } else {\n      this.traineeList.forEach(element => {\n        element.selected = event.target.checked;\n\n        if (!element.selected) {\n          this.selectedTraineeList = this.selectedTraineeList.filter(obj => obj !== element);\n        }\n      });\n    }\n  }\n\n  getTraineelList(name) {\n    this.select_All = false;\n    if (!name) return;\n\n    this._service.get(\"trainee/query/10/\" + name).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.traineeList = res.Data;\n      let arr = this.selectedTraineeList.map(x => x.Id);\n      this.traineeList.forEach(element => {\n        element.selected = arr.indexOf(element.Id) !== -1;\n      });\n    }, () => {});\n  }\n\n  onChangeTraineeSelection(item) {\n    if (item.Selected) this.selectedTraineeList = this.selectedTraineeList.filter(obj => obj.Id !== item.Id);else this.selectedTraineeList.push(item);\n  }\n\n  removeSelected(item) {\n    this.selectedTraineeList = this.selectedTraineeList.filter(obj => obj !== item);\n  }\n\n  clearLists() {\n    this.divisionList = [];\n    this.departmentList = [];\n    this.unitList = [];\n    this.traineeList = [];\n    this.selectedTraineeList = [];\n    this.divisionSelected = false;\n    this.departmentSelected = false;\n    this.unitSelected = false;\n    this.traineeSelected = false;\n    this.select_All = false;\n  }\n\n  openTraineeModal(template) {\n    this.selectedTraineeList = this.allowedTrainees;\n    this.modalTraineeRef = this.modalService.show(template, this.modalxLConfig);\n  }\n\n  modalTraineeHide() {\n    this.selectedTraineeList = [];\n    this.traineeList = [];\n    this.traineeForm.reset();\n    this.modalTraineeRef.hide();\n  }\n\n  onTraineeSelectionComplete() {\n    this.allowedTrainees = this.selectedTraineeList;\n    this.modalTraineeHide();\n  }\n\n  readURL(input) {\n    if (!input || input.files.length === 0) {\n      this.imageUrl = null;\n      this.imageFile = null;\n      return;\n    }\n\n    var mimeType = input.files[0].type;\n\n    if (mimeType.match(/image\\/*/) == null) {\n      this.toastr.warning(\"Only images are supported\", \"Warning!!\");\n      return;\n    }\n\n    var reader = new FileReader();\n    this.imageFile = input.files[0];\n    reader.readAsDataURL(input.files[0]);\n\n    reader.onload = _event => {\n      this.imageUrl = reader.result;\n    };\n  }\n\n  loadAttachment(files) {\n    if (files.length === 0) return;\n    this.traineeForm.controls[\"file\"].setValue(files[0]);\n  }\n\n  getTraineesByExcel() {\n    if (!this.traineeForm.value.file) {\n      this.toastr.warning(\"No excel file chosen\", \"WARNING!\");\n      return;\n    }\n\n    const formData = new FormData();\n    formData.append(\"File\", this.traineeForm.value.file);\n    this.blockUI.start(\"Getting data. Please wait...\");\n\n    this._service.post(\"trainee/query-by-excel\", formData).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, \"Warning!\", {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, \"Error!\", {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        if (res.Message) this.toastr.warning(\"PINs not found: \" + res.Message, \"WARNING!\");\n        this.traineeList = res.Data;\n        let arr = this.selectedTraineeList.map(x => x.Id);\n        this.traineeList.forEach(element => {\n          element.selected = arr.indexOf(element.Id) !== -1;\n        });\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.error(err.message || err, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  downloadSampleFile() {\n    return this._service.downloadFile(\"course/download-course-enrollment-sample-file\").subscribe({\n      next: res => {\n        const url = window.URL.createObjectURL(res);\n        var link = document.createElement(\"a\");\n        link.href = url;\n        link.download = \"trainee_sample_file.xlsx\";\n        link.click();\n      },\n      error: error => {\n        this.toastr.error(error.message || error, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => {}\n    });\n  }\n\n}\n\nEvaluationTestEntryComponent.ɵfac = function EvaluationTestEntryComponent_Factory(t) {\n  return new (t || EvaluationTestEntryComponent)(i0.ɵɵdirectiveInject(i1.AppComponent), i0.ɵɵdirectiveInject(i2.BsModalService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.CommonService), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i7.NgbTimepickerConfig));\n};\n\nEvaluationTestEntryComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: EvaluationTestEntryComponent,\n  selectors: [[\"app-evaluation-test-entry\"]],\n  decls: 173,\n  vars: 95,\n  consts: [[1, \"row\"], [1, \"col-lg-12\"], [1, \"col-sm-12\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", 3, \"innerText\"], [1, \"card-body\"], [\"autocomplete\", \"off\", 1, \"custom-inline-form\", 3, \"formGroup\"], [1, \"col-lg-9\", \"col-md-9\", \"col-12\"], [1, \"mb-3\", \"col-lg-4\", \"col-12\"], [1, \"col-form-label\", \"col-form-label-sm\", \"required\"], [\"type\", \"text\", \"formControlName\", \"exam\", \"type\", \"text\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"class\", \"error-text\", 4, \"ngIf\"], [1, \"col-form-label\", \"col-form-label-sm\"], [\"placeholder\", \"Set Start Date & Time\", \"type\", \"datetime-local\", \"formControlName\", \"startDate\", 1, \"form-control\", \"form-control-sm\"], [\"placeholder\", \"Set End Date & Time\", \"type\", \"datetime-local\", \"formControlName\", \"endDate\", 1, \"form-control\", \"form-control-sm\"], [1, \"mb-3\", \"col-lg-3\", \"col-12\"], [\"formControlName\", \"categoryId\", \"placeholder\", \"Select Category\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"ngClass\", \"click\"], [\"selectElement\", \"\"], [\"type\", \"text\", \"formControlName\", \"duration\", \"numeric\", \"\", \"numericType\", \"number\", \"type\", \"text\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"quota\", \"numeric\", \"\", \"numericType\", \"number\", \"type\", \"text\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"formControlName\", \"allowFor\", \"placeholder\", \"--Select--\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\", \"change\"], [\"selectElementA\", \"\"], [\"class\", \"mb-3 col-lg-3 col-12\", 4, \"ngIf\"], [\"class\", \"mb-3 col-lg-3 col-12 form-link\", 4, \"ngIf\"], [1, \"mb-3\", \"col-lg-3\", \"col-md-3\", \"col-12\"], [\"type\", \"text\", \"formControlName\", \"no_of_mcq\", \"numeric\", \"\", \"numericType\", \"number\", \"type\", \"text\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"mb-3 col-lg-3 col-md-3 col-12\", 4, \"ngIf\"], [1, \"col-lg-10\", \"col-md-9\", \"col-12\"], [\"type\", \"number\", \"formControlName\", \"order\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"checkbox\", \"formControlName\", \"active\", 1, \"form-check-input\"], [1, \"mb-3\", \"col-12\"], [1, \"NgxEditor__Wrapper\"], [3, \"editor\", \"toolbar\"], [\"formControlName\", \"instructions\", 3, \"editor\"], [1, \"col-lg-3\", \"col-md-3\", \"col-12\"], [1, \"mb-1\", \"row\"], [1, \"\"], [1, \"card\", \"bg-light\", \"pic-upload\", \"mb-0\"], [1, \"card-body\", \"bg-white\", \"parent-container\"], [\"src\", \"https://via.placeholder.com/200x104?text=No+Image\", 4, \"ngIf\"], [3, \"src\", 4, \"ngIf\"], [\"accept\", \"image/png,image/jpeg\", \"type\", \"file\", 1, \"btn-img-upload\", \"mt-2\", 3, \"change\"], [\"pictureInput\", \"\"], [1, \"mb-1\", \"mt-2\", \"col-12\"], [1, \"form-check\", \"mt-0\"], [\"type\", \"checkbox\", \"formControlName\", \"mcqOnly\", \"id\", \"mcqOnly\", \"type\", \"checkbox\", 1, \"form-check-input\"], [\"for\", \"mcqOnly\", 1, \"form-check-label\"], [1, \"mb-1\", \"col-12\"], [\"type\", \"checkbox\", \"formControlName\", \"random\", \"id\", \"random\", \"type\", \"checkbox\", 1, \"form-check-input\"], [\"for\", \"random\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"formControlName\", \"publish\", \"id\", \"publish\", \"type\", \"checkbox\", 1, \"form-check-input\"], [\"for\", \"publish\", 1, \"form-check-label\"], [1, \"col-12\", \"mt-3\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"me-2\", \"mt-0\", 3, \"routerLink\"], [1, \"feather\", \"icon-arrow-left\"], [\"class\", \"btn btn-theme btn-sm me-2 mt-0\", 3, \"click\", 4, \"ngIf\"], [1, \"card\", \"card-border-info\"], [1, \"col-sm-7\", \"col-12\"], [1, \"col-sm-2\", \"col-12\"], [\"type\", \"button\", \"class\", \"btn btn-outline-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"col-sm-3\", \"col-12\"], [\"type\", \"button\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"btn-block\", \"float-end\", 3, \"click\"], [1, \"feather\", \"icon-plus\"], [\"rowHeight\", \"auto\", 1, \"material\", 3, \"rows\", \"loadingIndicator\", \"columnMode\", \"headerHeight\", \"footerHeight\", \"limit\"], [\"name\", \"Question\", \"prop\", \"Question\", 3, \"width\", \"draggable\", \"sortable\"], [\"ngx-datatable-cell-template\", \"\"], [\"name\", \"Option 1\", \"prop\", \"Option1\", 3, \"width\", \"draggable\", \"sortable\"], [\"name\", \"Option 2\", \"prop\", \"Option2\", 3, \"width\", \"draggable\", \"sortable\"], [\"name\", \"Option 3\", \"prop\", \"Option3\", 3, \"width\", \"draggable\", \"sortable\"], [\"name\", \"Option 4\", \"prop\", \"Option4\", 3, \"width\", \"draggable\", \"sortable\"], [\"prop\", \"Answers\", 3, \"width\", \"draggable\", \"sortable\"], [\"prop\", \"Mark\", \"name\", \"Mark\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Action\", \"prop\", \"Id\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [1, \"card\", \"card-border-warning\"], [\"type\", \"button\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"btn-block\", \"float-end\", 3, \"click\"], [\"name\", \"Answer\", \"prop\", \"Answer\", 3, \"width\", \"draggable\", \"sortable\"], [\"class\", \"card card-border-danger\", 4, \"ngIf\"], [\"class\", \"card card-border-primary\", 4, \"ngIf\"], [\"class\", \"card card-border-inverse\", 4, \"ngIf\"], [\"templateMCQ\", \"\"], [\"templateTrueFalse\", \"\"], [\"templateFIG\", \"\"], [\"templateMatching\", \"\"], [\"templateWritten\", \"\"], [\"templateTrainee\", \"\"], [1, \"error-text\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [\"formControlName\", \"division\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select a division\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\"], [\"selectElementD\", \"\"], [\"formControlName\", \"department\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select a department\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\"], [\"selectElementDp\", \"\"], [\"formControlName\", \"unit\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select a unit\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\"], [\"selectElementU\", \"\"], [1, \"mb-3\", \"col-lg-3\", \"col-12\", \"form-link\"], [\"href\", \"javascript:\", 1, \"text-primary\", \"fw-bold\", \"etce-style-5\", 3, \"click\"], [\"type\", \"text\", \"formControlName\", \"no_of_tfq\", \"numeric\", \"\", \"numericType\", \"number\", \"type\", \"text\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"no_of_figq\", \"numeric\", \"\", \"numericType\", \"number\", \"type\", \"text\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"no_of_mq\", \"numeric\", \"\", \"numericType\", \"number\", \"type\", \"text\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"no_of_wq\", \"numeric\", \"\", \"numericType\", \"number\", \"type\", \"text\", 1, \"form-control\", \"form-control-sm\"], [\"src\", \"https://via.placeholder.com/200x104?text=No+Image\"], [3, \"src\"], [1, \"btn\", \"btn-theme\", \"btn-sm\", \"me-2\", \"mt-0\", 3, \"click\"], [1, \"feather\", \"icon-save\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-file-excel\"], [3, \"title\"], [1, \"btn\", \"btn-outline-danger\", \"btn-mini\", 3, \"click\"], [1, \"feather\", \"icon-trash\"], [1, \"feather\", \"icon-trash-2\"], [1, \"card\", \"card-border-danger\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-block\", \"float-end\", 3, \"click\"], [1, \"card\", \"card-border-primary\"], [\"type\", \"button\", 1, \"btn\", \"btn-theme\", \"btn-sm\", \"btn-block\", \"float-end\", 3, \"click\"], [\"name\", \"Left Side\", \"prop\", \"LeftSide\", 3, \"width\", \"draggable\", \"sortable\"], [\"name\", \"Right Side\", \"prop\", \"RightSide\", 3, \"width\", \"draggable\", \"sortable\"], [1, \"card\", \"card-border-inverse\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"btn-testz\", \"btn-sm\", \"btn-block\", \"float-end\", 3, \"click\"], [\"name\", \"Question\", \"prop\", \"Question\", 3, \"draggable\", \"sortable\"], [1, \"modal-header\"], [\"id\", \"modalTitle\", 1, \"modal-title\", \"float-start\", 3, \"innerHtml\"], [\"type\", \"button \", \"aria-label\", \"Close \", 1, \"close\", \"float-end\", \"btn\", \"btn-mini\", \"btn-danger\", \"btn\", \"btn-mini\", \"btn-outline-danger\", 3, \"click\"], [1, \"feather\", \"icon-x\"], [1, \"modal-body\"], [1, \"row\", \"mt-2\"], [1, \"col-lg-5\", \"col-12\"], [\"accept\", \".xls,.xlsx\", \"type\", \"file\", 1, \"p-1\", 3, \"change\"], [\"mcqFileInput\", \"\"], [1, \"col-lg-3\", \"col-12\"], [\"type\", \"file\", \"class\", \"btn btn-warning btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"col-lg-4\", \"col-12\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"float-end\", \"me-2\", 3, \"click\"], [1, \"feather\", \"icon-download\"], [1, \"card-body\", \"pb-0\"], [1, \"question-list\"], [\"class\", \"row\", 4, \"ngFor\", \"ngForOf\"], [1, \"modal-footer\", \"pt-0\"], [1, \"pe-4\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-theme\", \"btn-sm\", \"me-2\", 3, \"click\"], [\"type\", \"file\", 1, \"btn\", \"btn-warning\", \"btn-sm\", 3, \"click\"], [1, \"col-12\"], [1, \"input-group\", \"input-group-sm\"], [1, \"input-group-text\", \"fw-bold\"], [\"rows\", \"2\", \"cols\", \"5\", \"placeholder\", \"Enter Question\", 1, \"form-control\", \"form-control-sm\", \"etec-color-black\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"input-group-text\", 3, \"click\"], [1, \"feather\", \"icon-plus\", \"text-success\"], [1, \"feather\", \"icon-x\", \"text-danger\"], [\"class\", \"col-sm-6 col-12\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"mt-2\", \"mb-3\"], [1, \"input-group-text\"], [\"type\", \"text\", \"numeric\", \"\", \"numericType\", \"decimal\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-sm-6\", \"col-12\"], [\"type\", \"text\", 1, \"form-control\", \"form-control-sm\", 3, \"placeholder\", \"ngModelOptions\", \"ngModel\", \"ngModelChange\"], [\"type\", \"checkbox\", 3, \"checked\", \"disabled\", \"ngModel\", \"ngModelChange\"], [1, \"modal-body\", \"pb-0\"], [\"tfqFileInput\", \"\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"row\", \"mb-2\"], [\"uncheckedLabel\", \"Selected False\", \"checkedLabel\", \"Selected True\", \"size\", \"small\", \"defaultBgColor\", \"#E82D4C\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-title\", \"float-start\", 3, \"innerHtml\"], [\"figqFileInput\", \"\"], [\"type\", \"text\", \"placeholder\", \"Enter answer\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"matchingqFileInput\", \"\"], [1, \"modal-footer\"], [\"rows\", \"2\", \"cols\", \"5\", \"placeholder\", \"Enter left hand side\", 1, \"form-control\", \"form-control-sm\", \"etec-color-black\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"col-sm-9\", \"col-12\"], [\"rows\", \"2\", \"cols\", \"5\", \"placeholder\", \"Enter right hand side\", 1, \"form-control\", \"form-control-sm\", \"etec-color-black\", 3, \"ngModel\", \"ngModelChange\"], [\"wqFileInput\", \"\"], [1, \"modal-title\", \"float-start\"], [\"autocomplete\", \"off\", 1, \"col-sm-12\", 3, \"formGroup\"], [1, \"col-lg-6\", \"col-12\", \"border-end\"], [1, \"card\", \"mb-0\"], [1, \"card-body\", \"pb-0\", \"row\"], [1, \"col-lg-4\", \"col-12\", \"mb-3\"], [1, \"col-lg-8\", \"col-12\", \"mb-3\"], [1, \"form-check\", \"form-check-inline\"], [\"type\", \"radio\", \"formControlName\", \"selectionType\", \"name\", \"selectionType\", \"id\", \"BySelection\", \"value\", \"BySelection\", 1, \"form-check-input\"], [\"for\", \"BySelection\", 1, \"form-check-label\"], [\"type\", \"radio\", \"formControlName\", \"selectionType\", \"name\", \"selectionType\", \"id\", \"ByExcel\", \"value\", \"ByExcel\", 1, \"form-check-input\"], [\"for\", \"ByExcel\", 1, \"form-check-label\"], [1, \"card-header\", \"p-2\"], [\"class\", \"col-12 mb-3\", 4, \"ngIf\"], [\"class\", \"card-title\", 4, \"ngIf\"], [\"class\", \"custom-control custom-checkbox float-end\", 4, \"ngIf\"], [\"class\", \"card-body p-2 etce-style-3\", 4, \"ngIf\"], [1, \"card\", \"mb-0\", \"col-lg-6\", \"col-12\"], [\"class\", \"card-header p-2\", 4, \"ngIf\"], [\"class\", \"card-body p-2 etce-style-4\", 4, \"ngIf\"], [1, \"card-footer\", \"p-0\"], [1, \"modal-footer\", \"py-1\"], [1, \"feather\", \"icon-check\"], [1, \"col-12\", \"mb-3\"], [\"type\", \"text\", \"formControlName\", \"pin\", \"placeholder\", \"Please enter 3 or more characters\", 1, \"form-control\", \"form-control-sm\"], [1, \"input-group\"], [\"type\", \"file\", \"accept\", \".xls,.xlsx\", 1, \"form-control\", 3, \"change\"], [\"attachment\", \"\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-link\", \"mt-3\", 3, \"click\"], [1, \"card-title\"], [1, \"custom-control\", \"custom-checkbox\", \"float-end\"], [\"name\", \"selectAll\", \"id\", \"selectAll\", \"type\", \"checkbox\", 1, \"custom-control-input\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\", \"change\"], [\"for\", \"selectAll\", 1, \"custom-control-label\"], [1, \"card-body\", \"p-2\", \"etce-style-3\"], [1, \"list-group\"], [\"class\", \"list-group-item d-flex justify-content-between align-items-center list-group-item-action\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"list-group-item-action\"], [1, \"custom-control\", \"custom-checkbox\"], [\"type\", \"checkbox\", 1, \"custom-control-input\", 3, \"name\", \"id\", \"ngModel\", \"ngModelOptions\", \"value\", \"ngModelChange\", \"change\"], [1, \"custom-control-label\", 3, \"for\"], [1, \"text-muted\", \"mb-0\"], [1, \"card-body\", \"p-2\", \"etce-style-4\"], [\"type\", \"checkbox\", \"checked\", \"\", 1, \"custom-control-input\", 3, \"name\", \"id\", \"change\"]],\n  template: function EvaluationTestEntryComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r266 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 0);\n      i0.ɵɵelementStart(4, \"div\", 2);\n      i0.ɵɵelementStart(5, \"div\", 3);\n      i0.ɵɵelementStart(6, \"div\", 4);\n      i0.ɵɵelement(7, \"h5\", 5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(8, \"div\", 6);\n      i0.ɵɵelementStart(9, \"form\", 7);\n      i0.ɵɵelementStart(10, \"div\", 0);\n      i0.ɵɵelementStart(11, \"div\", 8);\n      i0.ɵɵelementStart(12, \"div\", 0);\n      i0.ɵɵelementStart(13, \"div\", 9);\n      i0.ɵɵelementStart(14, \"label\", 10);\n      i0.ɵɵtext(15, \"Exam Name\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(16, \"input\", 11);\n      i0.ɵɵtemplate(17, EvaluationTestEntryComponent_div_17_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"div\", 9);\n      i0.ɵɵelementStart(19, \"label\", 13);\n      i0.ɵɵtext(20, \"Start Date & Time \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(21, \"input\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"div\", 9);\n      i0.ɵɵelementStart(23, \"label\", 13);\n      i0.ɵɵtext(24, \"End Date & Time \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(25, \"input\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(26, \"div\", 16);\n      i0.ɵɵelementStart(27, \"label\", 10);\n      i0.ɵɵtext(28, \" Category \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(29, \"ng-select\", 17, 18);\n      i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_Template_ng_select_click_29_listener() {\n        i0.ɵɵrestoreView(_r266);\n\n        const _r1 = i0.ɵɵreference(30);\n\n        return ctx.handleSelectClick(_r1);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(31, EvaluationTestEntryComponent_div_31_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(32, \"div\", 16);\n      i0.ɵɵelementStart(33, \"label\", 10);\n      i0.ɵɵtext(34, \" Exam Duration (Min) \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(35, \"input\", 19);\n      i0.ɵɵtemplate(36, EvaluationTestEntryComponent_div_36_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(37, \"div\", 16);\n      i0.ɵɵelementStart(38, \"label\", 10);\n      i0.ɵɵtext(39, \" Quota \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(40, \"input\", 20);\n      i0.ɵɵtemplate(41, EvaluationTestEntryComponent_div_41_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(42, \"div\", 16);\n      i0.ɵɵelementStart(43, \"label\", 10);\n      i0.ɵɵtext(44, \"Allow For \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(45, \"ng-select\", 21, 22);\n      i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_Template_ng_select_click_45_listener() {\n        i0.ɵɵrestoreView(_r266);\n\n        const _r5 = i0.ɵɵreference(46);\n\n        return ctx.handleSelectClick(_r5);\n      })(\"change\", function EvaluationTestEntryComponent_Template_ng_select_change_45_listener($event) {\n        return ctx.changeAllowedFor($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(47, EvaluationTestEntryComponent_div_47_Template, 5, 3, \"div\", 23);\n      i0.ɵɵtemplate(48, EvaluationTestEntryComponent_div_48_Template, 5, 3, \"div\", 23);\n      i0.ɵɵtemplate(49, EvaluationTestEntryComponent_div_49_Template, 5, 3, \"div\", 23);\n      i0.ɵɵtemplate(50, EvaluationTestEntryComponent_div_50_Template, 3, 1, \"div\", 24);\n      i0.ɵɵelementStart(51, \"div\", 25);\n      i0.ɵɵelementStart(52, \"label\", 10);\n      i0.ɵɵtext(53, \" No. of MCQ \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(54, \"input\", 26);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(55, EvaluationTestEntryComponent_div_55_Template, 4, 0, \"div\", 27);\n      i0.ɵɵtemplate(56, EvaluationTestEntryComponent_div_56_Template, 4, 0, \"div\", 27);\n      i0.ɵɵtemplate(57, EvaluationTestEntryComponent_div_57_Template, 4, 0, \"div\", 27);\n      i0.ɵɵtemplate(58, EvaluationTestEntryComponent_div_58_Template, 4, 0, \"div\", 27);\n      i0.ɵɵelementStart(59, \"div\", 25);\n      i0.ɵɵelementStart(60, \"label\", 13);\n      i0.ɵɵtext(61, \"Order \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(62, \"div\", 28);\n      i0.ɵɵelement(63, \"input\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(64, \"div\", 25);\n      i0.ɵɵelementStart(65, \"label\", 13);\n      i0.ɵɵtext(66, \"Active \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(67, \"div\", 28);\n      i0.ɵɵelement(68, \"input\", 30);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(69, \"div\", 31);\n      i0.ɵɵelementStart(70, \"label\", 13);\n      i0.ɵɵtext(71, \"Instructions \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(72, \"div\", 32);\n      i0.ɵɵelement(73, \"ngx-editor-menu\", 33);\n      i0.ɵɵelement(74, \"ngx-editor\", 34);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(75, \"div\", 35);\n      i0.ɵɵelementStart(76, \"div\", 36);\n      i0.ɵɵelementStart(77, \"label\", 37);\n      i0.ɵɵtext(78, \"Cover Photo (400x210)\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(79, \"div\", 38);\n      i0.ɵɵelementStart(80, \"div\", 39);\n      i0.ɵɵtemplate(81, EvaluationTestEntryComponent_img_81_Template, 1, 0, \"img\", 40);\n      i0.ɵɵtemplate(82, EvaluationTestEntryComponent_img_82_Template, 1, 1, \"img\", 41);\n      i0.ɵɵelementStart(83, \"input\", 42, 43);\n      i0.ɵɵlistener(\"change\", function EvaluationTestEntryComponent_Template_input_change_83_listener() {\n        i0.ɵɵrestoreView(_r266);\n\n        const _r16 = i0.ɵɵreference(84);\n\n        return ctx.readURL(_r16);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(85, \"div\", 44);\n      i0.ɵɵelementStart(86, \"div\", 45);\n      i0.ɵɵelement(87, \"input\", 46);\n      i0.ɵɵelementStart(88, \"label\", 47);\n      i0.ɵɵtext(89, \" Auto Marking \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(90, \"div\", 48);\n      i0.ɵɵelementStart(91, \"div\", 45);\n      i0.ɵɵelement(92, \"input\", 49);\n      i0.ɵɵelementStart(93, \"label\", 50);\n      i0.ɵɵtext(94, \" Question Randomization \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(95, \"div\", 48);\n      i0.ɵɵelementStart(96, \"div\", 45);\n      i0.ɵɵelement(97, \"input\", 51);\n      i0.ɵɵelementStart(98, \"label\", 52);\n      i0.ɵɵtext(99, \" Publish Immediate Result \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(100, \"div\", 53);\n      i0.ɵɵelementStart(101, \"button\", 54);\n      i0.ɵɵelement(102, \"i\", 55);\n      i0.ɵɵtext(103, \" Go Back \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(104, EvaluationTestEntryComponent_button_104_Template, 3, 1, \"button\", 56);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(105, \"div\");\n      i0.ɵɵelementStart(106, \"div\", 57);\n      i0.ɵɵelementStart(107, \"div\", 4);\n      i0.ɵɵelementStart(108, \"div\", 0);\n      i0.ɵɵelementStart(109, \"div\", 58);\n      i0.ɵɵelementStart(110, \"h5\");\n      i0.ɵɵtext(111, \"MCQ Questions\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(112, \"div\", 59);\n      i0.ɵɵtemplate(113, EvaluationTestEntryComponent_button_113_Template, 3, 0, \"button\", 60);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(114, \"div\", 61);\n      i0.ɵɵelementStart(115, \"button\", 62);\n      i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_Template_button_click_115_listener() {\n        i0.ɵɵrestoreView(_r266);\n\n        const _r35 = i0.ɵɵreference(162);\n\n        return ctx.openMCQModal(_r35);\n      });\n      i0.ɵɵelement(116, \"i\", 63);\n      i0.ɵɵtext(117);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(118, \"div\", 6);\n      i0.ɵɵelementStart(119, \"ngx-datatable\", 64);\n      i0.ɵɵelementStart(120, \"ngx-datatable-column\", 65);\n      i0.ɵɵtemplate(121, EvaluationTestEntryComponent_ng_template_121_Template, 2, 2, \"ng-template\", 66);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(122, \"ngx-datatable-column\", 67);\n      i0.ɵɵtemplate(123, EvaluationTestEntryComponent_ng_template_123_Template, 2, 2, \"ng-template\", 66);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(124, \"ngx-datatable-column\", 68);\n      i0.ɵɵtemplate(125, EvaluationTestEntryComponent_ng_template_125_Template, 2, 2, \"ng-template\", 66);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(126, \"ngx-datatable-column\", 69);\n      i0.ɵɵtemplate(127, EvaluationTestEntryComponent_ng_template_127_Template, 2, 2, \"ng-template\", 66);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(128, \"ngx-datatable-column\", 70);\n      i0.ɵɵtemplate(129, EvaluationTestEntryComponent_ng_template_129_Template, 2, 2, \"ng-template\", 66);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(130, \"ngx-datatable-column\", 71);\n      i0.ɵɵtemplate(131, EvaluationTestEntryComponent_ng_template_131_Template, 2, 2, \"ng-template\", 66);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(132, \"ngx-datatable-column\", 72);\n      i0.ɵɵtemplate(133, EvaluationTestEntryComponent_ng_template_133_Template, 2, 2, \"ng-template\", 66);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(134, \"ngx-datatable-column\", 73);\n      i0.ɵɵtemplate(135, EvaluationTestEntryComponent_ng_template_135_Template, 3, 0, \"ng-template\", 66);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(136, \"div\", 74);\n      i0.ɵɵelementStart(137, \"div\", 4);\n      i0.ɵɵelementStart(138, \"div\", 0);\n      i0.ɵɵelementStart(139, \"div\", 58);\n      i0.ɵɵelementStart(140, \"h5\");\n      i0.ɵɵtext(141, \"True/False Questions\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(142, \"div\", 59);\n      i0.ɵɵtemplate(143, EvaluationTestEntryComponent_button_143_Template, 3, 0, \"button\", 60);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(144, \"div\", 61);\n      i0.ɵɵelementStart(145, \"button\", 75);\n      i0.ɵɵlistener(\"click\", function EvaluationTestEntryComponent_Template_button_click_145_listener() {\n        i0.ɵɵrestoreView(_r266);\n\n        const _r37 = i0.ɵɵreference(164);\n\n        return ctx.openTFQModal(_r37);\n      });\n      i0.ɵɵelement(146, \"i\", 63);\n      i0.ɵɵtext(147);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(148, \"div\", 6);\n      i0.ɵɵelementStart(149, \"ngx-datatable\", 64);\n      i0.ɵɵelementStart(150, \"ngx-datatable-column\", 65);\n      i0.ɵɵtemplate(151, EvaluationTestEntryComponent_ng_template_151_Template, 2, 2, \"ng-template\", 66);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(152, \"ngx-datatable-column\", 76);\n      i0.ɵɵtemplate(153, EvaluationTestEntryComponent_ng_template_153_Template, 2, 2, \"ng-template\", 66);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(154, \"ngx-datatable-column\", 72);\n      i0.ɵɵtemplate(155, EvaluationTestEntryComponent_ng_template_155_Template, 2, 2, \"ng-template\", 66);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(156, \"ngx-datatable-column\", 73);\n      i0.ɵɵtemplate(157, EvaluationTestEntryComponent_ng_template_157_Template, 3, 0, \"ng-template\", 66);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(158, EvaluationTestEntryComponent_div_158_Template, 22, 20, \"div\", 77);\n      i0.ɵɵtemplate(159, EvaluationTestEntryComponent_div_159_Template, 22, 20, \"div\", 78);\n      i0.ɵɵtemplate(160, EvaluationTestEntryComponent_div_160_Template, 20, 16, \"div\", 79);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(161, EvaluationTestEntryComponent_ng_template_161_Template, 34, 4, \"ng-template\", null, 80, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(163, EvaluationTestEntryComponent_ng_template_163_Template, 34, 4, \"ng-template\", null, 81, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(165, EvaluationTestEntryComponent_ng_template_165_Template, 34, 4, \"ng-template\", null, 82, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(167, EvaluationTestEntryComponent_ng_template_167_Template, 34, 4, \"ng-template\", null, 83, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(169, EvaluationTestEntryComponent_ng_template_169_Template, 34, 4, \"ng-template\", null, 84, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(171, EvaluationTestEntryComponent_ng_template_171_Template, 43, 9, \"ng-template\", null, 85, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"innerText\", ctx.formTitle);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.entryForm);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(86, _c1, ctx.submitted && ctx.f.exam.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.exam.errors);\n      i0.ɵɵadvance(12);\n      i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx.categoryList)(\"ngClass\", i0.ɵɵpureFunction1(88, _c1, ctx.submitted && ctx.f.categoryId.errors));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.categoryId.errors);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(90, _c1, ctx.submitted && ctx.f.duration.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.duration.errors);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(92, _c1, ctx.submitted && ctx.f.quota.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.quota.errors);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx.allowList);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.divisionList.length > 0 && ctx.divisionSelected);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.departmentList.length > 0 && ctx.departmentSelected);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.unitList.length > 0 && ctx.unitSelected);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.traineeSelected);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.f.mcqOnly.value && !ctx.ExamExists);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.f.mcqOnly.value && !ctx.ExamExists);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.f.mcqOnly.value && !ctx.ExamExists);\n      i0.ɵɵadvance(15);\n      i0.ɵɵproperty(\"editor\", ctx.editor)(\"toolbar\", ctx.toolbar);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"editor\", ctx.editor);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngIf\", !ctx.imageUrl);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.imageUrl);\n      i0.ɵɵadvance(19);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(94, _c2));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.id);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngIf\", ctx.mcqList.length > 0);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate1(\" \", ctx.mcqList.length === 0 ? \"Add\" : \"Edit\", \" MCQ Questions \");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"rows\", ctx.mcqList)(\"loadingIndicator\", ctx.loadingMCQ)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"limit\", 5);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngIf\", ctx.truFalseList.length > 0);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate1(\" \", ctx.truFalseList.length === 0 ? \"Add\" : \"Edit\", \" True/False Questions \");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"rows\", ctx.truFalseList)(\"loadingIndicator\", ctx.loadingTFQ)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"limit\", 5);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.f.mcqOnly.value);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.f.mcqOnly.value);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.f.mcqOnly.value);\n    }\n  },\n  directives: [i8.BlockUIComponent, i3.ɵNgNoValidate, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.DefaultValueAccessor, i3.NgControlStatus, i3.FormControlName, i9.NgClass, i10.DefaultClassDirective, i9.NgIf, i11.NgSelectComponent, i12.NumericDirective, i3.NumberValueAccessor, i3.CheckboxControlValueAccessor, i13.MenuComponent, i13.NgxEditorComponent, i6.RouterLink, i14.DatatableComponent, i14.DataTableColumnDirective, i14.DataTableColumnCellDirective, i9.NgForOf, i3.NgModel, i15.UiSwitchComponent, i3.RadioControlValueAccessor],\n  encapsulation: 2,\n  data: {\n    animation: [trigger(\"inOutAnimation\", [transition(\":enter\", [style({\n      height: 0,\n      opacity: 0\n    }), animate(\"1s ease-out\", style({\n      height: 300,\n      opacity: 1\n    }))]), transition(\":leave\", [animate(\"1s ease-out\", style({\n      height: 0,\n      opacity: 0\n    }))])])]\n  }\n});\n\n__decorate([BlockUI()], EvaluationTestEntryComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}