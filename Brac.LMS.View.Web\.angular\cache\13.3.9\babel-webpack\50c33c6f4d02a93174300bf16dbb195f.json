{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { environment } from 'src/environments/environment';\nimport { Validators } from '@angular/forms';\nimport { NavigationEnd } from '@angular/router';\nimport { BlockUI } from 'ng-block-ui';\nimport $ from 'jquery';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { ReturnStatement } from '@angular/compiler';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../app.component\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../_services/common.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"ng-block-ui\";\nimport * as i8 from \"@ng-select/ng-select\";\n\nfunction ForumPostComponent_div_24_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1, \" category is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumPostComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, ForumPostComponent_div_24_span_1_Template, 2, 0, \"span\", 30);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"category\"].errors[\"required\"]);\n  }\n}\n\nfunction ForumPostComponent_div_34_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1, \"Tags required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumPostComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, ForumPostComponent_div_34_span_1_Template, 2, 0, \"span\", 30);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.f[\"tag\"].errors[\"required\"]);\n  }\n}\n\nfunction ForumPostComponent_div_44_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1, \" Title is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumPostComponent_div_44_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1, \" Title must be within 250 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumPostComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, ForumPostComponent_div_44_span_1_Template, 2, 0, \"span\", 30);\n    i0.ɵɵtemplate(2, ForumPostComponent_div_44_span_2_Template, 2, 0, \"span\", 30);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.f[\"title\"].errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.f[\"title\"].errors[\"maxlength\"]);\n  }\n}\n\nfunction ForumPostComponent_div_52_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1, \" Description is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumPostComponent_div_52_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1, \" Description must be within 2000 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumPostComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, ForumPostComponent_div_52_span_1_Template, 2, 0, \"span\", 30);\n    i0.ɵɵtemplate(2, ForumPostComponent_div_52_span_2_Template, 2, 0, \"span\", 30);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.f[\"description\"].errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.f[\"description\"].errors[\"maxlength\"]);\n  }\n}\n\nconst _c0 = function (a1) {\n  return [\"/forum-details\", a1];\n};\n\nfunction ForumPostComponent_div_58_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"h4\", 37);\n    i0.ɵɵelementStart(3, \"a\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r16 = ctx.$implicit;\n    const i_r17 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, item_r16.Id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", i_r17 + 1, \". \", item_r16.Title, \"\");\n  }\n}\n\nfunction ForumPostComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelementStart(1, \"h2\", 33);\n    i0.ɵɵtext(2, \"My posts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ForumPostComponent_div_58_div_3_Template, 5, 5, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.myTopicList);\n  }\n}\n\nfunction ForumPostComponent_hr_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\", 39);\n  }\n}\n\nfunction ForumPostComponent_div_60_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"h4\", 37);\n    i0.ɵɵelementStart(3, \"a\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r19 = ctx.$implicit;\n    const i_r20 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, item_r19.Id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", i_r20 + 1, \". \", item_r19.Title, \"\");\n  }\n}\n\nfunction ForumPostComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelementStart(1, \"h2\", 33);\n    i0.ɵɵtext(2, \"Popular Post\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ForumPostComponent_div_60_div_3_Template, 5, 5, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.popularList);\n  }\n}\n\nexport class ForumPostComponent {\n  constructor(appComponent, formBuilder, router, _service, toastr, route, _location) {\n    this.appComponent = appComponent;\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.route = route;\n    this._location = _location;\n    this.submitted = false;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.timestamp = new Date().getTime();\n    this.categoryList = [];\n    this.tagList = [];\n    this.myTopicList = [];\n    this.popularList = [];\n    this.modules = {\n      syntax: false,\n      toolbar: [['bold', 'italic', 'underline', 'strike'], ['blockquote', 'code-block'], [{\n        header: 1\n      }, {\n        header: 2\n      }], [{\n        list: 'ordered'\n      }, {\n        list: 'bullet'\n      }], [{\n        script: 'sub'\n      }, {\n        script: 'super'\n      }], [{\n        indent: '-1'\n      }, {\n        indent: '+1'\n      }], [{\n        direction: 'rtl'\n      }], [{\n        size: ['small', false, 'large', 'huge']\n      }], // [{ header: [1, 2, 3, 4, 5, 6, false] }],\n      [{\n        color: []\n      }, {\n        background: []\n      }], [{\n        font: []\n      }], [{\n        align: []\n      }], ['clean'], ['link']]\n    };\n    this.router.events.subscribe(evt => {\n      if (!(evt instanceof NavigationEnd)) {\n        return;\n      }\n\n      window.scrollTo(0, 0);\n    });\n  }\n\n  handleSelectClick(selectElement) {\n    this.appComponent.handleSelectClick(selectElement);\n  }\n\n  ngOnInit() {\n    this.entryForm = this.formBuilder.group({\n      id: [null],\n      title: [null, [Validators.required, Validators.maxLength(250)]],\n      category: [null, [Validators.required]],\n      tag: [null, [Validators.required]],\n      description: [null, [Validators.required, Validators.maxLength(2000)]]\n    });\n    this.getPostCategory();\n    this.getMyTopicList();\n    this.getPopularTopicList();\n  }\n\n  get f() {\n    return this.entryForm.controls;\n  }\n\n  loadJquery() {\n    (function ($) {})($);\n  }\n\n  backClicked() {\n    this._location.back();\n  }\n\n  getPostCategory() {\n    this.categoryList = [];\n\n    this._service.get('forum/my-favorite-categories/get').subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          ReturnStatement;\n        }\n\n        this.categoryList = res.Data.filter(x => x.Selected);\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  getCategoryTags(id) {\n    this.tagList = [];\n\n    this._service.get('forum-category/tags/get/' + id).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.tagList = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => console.log('complete')\n    });\n  }\n\n  onCategoryChange(e) {\n    if (e) {\n      this.getCategoryTags(e.Id);\n    }\n  }\n\n  onFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    this.blockUI.start('Saving...');\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      Title: this.entryForm.value.title.trim(),\n      CategoryId: this.entryForm.value.category,\n      Description: this.entryForm.value.description ? this.entryForm.value.description.trim() : null,\n      Tags: this.entryForm.value.tag\n    };\n\n    const request = this._service.post('forum/topic/create-or-update', obj);\n\n    request.subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, 'Success!', {\n          closeButton: true,\n          timeOut: 15000\n        });\n        this.entryForm.reset();\n        this.router.navigate(['forum']);\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  getMyTopicList() {\n    this._service.get('forum/get-my-topic-titles/5').subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.myTopicList = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => console.log('complete')\n    });\n  }\n\n  getPopularTopicList() {\n    this._service.get('forum/get-popular-topic-titles/5').subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.popularList = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => console.log('complete')\n    });\n  }\n\n}\n\nForumPostComponent.ɵfac = function ForumPostComponent_Factory(t) {\n  return new (t || ForumPostComponent)(i0.ɵɵdirectiveInject(i1.AppComponent), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.CommonService), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i6.Location));\n};\n\nForumPostComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ForumPostComponent,\n  selectors: [[\"app-forum-post\"]],\n  decls: 61,\n  vars: 15,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"btn\", \"btn-link\", \"text-decoration-none\", \"fs-4\", \"fw-bold\", \"btn-sm\", \"mb-2\", 3, \"click\"], [1, \"fs-4\", \"ai-arrow-left\", \"fs-base\", \"me-2\"], [1, \"mt-1\", \"mb-4\"], [1, \"row\"], [1, \"col-lg-8\", \"border-end\"], [\"autocomplete\", \"off\", 3, \"formGroup\"], [1, \"col-md-6\", \"mb-3\"], [1, \"form-label\", \"ps-0\"], [1, \"text-danger\", \"ms-1\"], [\"formControlName\", \"category\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Category\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\", \"change\"], [\"selectCategory\", \"\"], [1, \"fs-xs\", \"text-muted\", \"mb-0\"], [\"class\", \"error-text\", 4, \"ngIf\"], [\"formControlName\", \"tag\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select tags\", 3, \"multiple\", \"clearable\", \"clearOnBackspace\", \"items\", \"click\"], [\"selectTag\", \"\"], [1, \"col-md-12\", \"mb-3\"], [\"formControlName\", \"title\", \"type\", \"text\", \"placeholder\", \"Write your post title..\", 1, \"form-control\"], [\"rows\", \"5\", \"formControlName\", \"description\", \"placeholder\", \"Write your post description..\", 1, \"form-control\"], [1, \"row\", \"mb-5\", \"mt-3\"], [1, \"col-md-12\"], [1, \"btn\", \"btn-primary\", \"d-block\", \"w-100\", 3, \"click\"], [1, \"col-lg-4\", \"sidebar\", \"bg-secondary\", \"pt-5\", \"ps-lg-4\", \"pb-md-2\", \"border-end\"], [\"class\", \"widget mt-n1\", 4, \"ngIf\"], [\"class\", \"my-4\", 4, \"ngIf\"], [1, \"error-text\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [1, \"widget\", \"mt-n1\"], [1, \"h3\", \"pb-1\"], [\"class\", \"d-flex align-items-center pb-1 mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\", \"pb-1\", \"mb-3\"], [1, \"ms-1\"], [1, \"fs-md\", \"nav-heading\", \"mb-1\"], [\"routerLinkActive\", \"router-link-active\", 1, \"fw-medium\", \"fs-5\", 3, \"routerLink\"], [1, \"my-4\"]],\n  template: function ForumPostComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r21 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h1\", 4);\n      i0.ɵɵtext(6, \"Forum Post\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"a\", 5);\n      i0.ɵɵlistener(\"click\", function ForumPostComponent_Template_a_click_7_listener() {\n        return ctx.backClicked();\n      });\n      i0.ɵɵelement(8, \"i\", 6);\n      i0.ɵɵtext(9, \"Go Back \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(10, \"hr\", 7);\n      i0.ɵɵelementStart(11, \"div\", 8);\n      i0.ɵɵelementStart(12, \"div\", 9);\n      i0.ɵɵelementStart(13, \"form\", 10);\n      i0.ɵɵelementStart(14, \"div\", 8);\n      i0.ɵɵelementStart(15, \"div\", 11);\n      i0.ɵɵelementStart(16, \"label\", 12);\n      i0.ɵɵtext(17, \"Post Category \");\n      i0.ɵɵelementStart(18, \"sup\", 13);\n      i0.ɵɵtext(19, \"*\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"ng-select\", 14, 15);\n      i0.ɵɵlistener(\"click\", function ForumPostComponent_Template_ng_select_click_20_listener() {\n        i0.ɵɵrestoreView(_r21);\n\n        const _r0 = i0.ɵɵreference(21);\n\n        return ctx.handleSelectClick(_r0);\n      })(\"change\", function ForumPostComponent_Template_ng_select_change_20_listener($event) {\n        return ctx.onCategoryChange($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"p\", 16);\n      i0.ɵɵtext(23, \" Select a category which is related to your post \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(24, ForumPostComponent_div_24_Template, 2, 1, \"div\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(25, \"div\", 11);\n      i0.ɵɵelementStart(26, \"label\", 12);\n      i0.ɵɵtext(27, \"Post Tags \");\n      i0.ɵɵelementStart(28, \"sup\", 13);\n      i0.ɵɵtext(29, \"*\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"ng-select\", 18, 19);\n      i0.ɵɵlistener(\"click\", function ForumPostComponent_Template_ng_select_click_30_listener() {\n        i0.ɵɵrestoreView(_r21);\n\n        const _r2 = i0.ɵɵreference(31);\n\n        return ctx.handleSelectClick(_r2);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(32, \"p\", 16);\n      i0.ɵɵtext(33, \" Select tags which are related to your post \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(34, ForumPostComponent_div_34_Template, 2, 1, \"div\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(35, \"div\", 8);\n      i0.ɵɵelementStart(36, \"div\", 20);\n      i0.ɵɵelementStart(37, \"label\", 12);\n      i0.ɵɵtext(38, \"Post Title \");\n      i0.ɵɵelementStart(39, \"sup\", 13);\n      i0.ɵɵtext(40, \"*\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(41, \"input\", 21);\n      i0.ɵɵelementStart(42, \"p\", 16);\n      i0.ɵɵtext(43, \" Be specific and imagine you\\u2019re asking a question to another person \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(44, ForumPostComponent_div_44_Template, 3, 2, \"div\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(45, \"label\", 12);\n      i0.ɵɵtext(46, \"Post Description \");\n      i0.ɵɵelementStart(47, \"sup\", 13);\n      i0.ɵɵtext(48, \"*\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(49, \"textarea\", 22);\n      i0.ɵɵelementStart(50, \"p\", 16);\n      i0.ɵɵtext(51, \" Include all the information someone would need to answer your question \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(52, ForumPostComponent_div_52_Template, 3, 2, \"div\", 17);\n      i0.ɵɵelementStart(53, \"div\", 23);\n      i0.ɵɵelementStart(54, \"div\", 24);\n      i0.ɵɵelementStart(55, \"button\", 25);\n      i0.ɵɵlistener(\"click\", function ForumPostComponent_Template_button_click_55_listener() {\n        return ctx.onFormSubmit();\n      });\n      i0.ɵɵtext(56, \"Post Now\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(57, \"div\", 26);\n      i0.ɵɵtemplate(58, ForumPostComponent_div_58_Template, 4, 1, \"div\", 27);\n      i0.ɵɵtemplate(59, ForumPostComponent_hr_59_Template, 1, 0, \"hr\", 28);\n      i0.ɵɵtemplate(60, ForumPostComponent_div_60_Template, 4, 1, \"div\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"formGroup\", ctx.entryForm);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx.categoryList);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"category\"].errors);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"multiple\", true)(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx.tagList);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"tag\"].errors);\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"title\"].errors);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"description\"].errors);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngIf\", ctx.myTopicList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.myTopicList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.popularList.length > 0);\n    }\n  },\n  directives: [i7.BlockUIComponent, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i8.NgSelectComponent, i2.NgControlStatus, i2.FormControlName, i6.NgIf, i2.DefaultValueAccessor, i6.NgForOf, i3.RouterLinkWithHref, i3.RouterLinkActive],\n  styles: [\"\"]\n});\n\n__decorate([BlockUI()], ForumPostComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}