{"ast": null, "code": "export let LocalStorageHelper = /*#__PURE__*/(() => {\n  class LocalStorageHelper {}\n\n  LocalStorageHelper.setWithExpiry = function (key, value, days) {\n    let now = new Date();\n    now.setDate(now.getDate() + days); // `item` is an object which contains the original value\n    // as well as the time when it's supposed to expire\n\n    const item = {\n      value: value,\n      expiry: now.getTime()\n    };\n    localStorage.setItem(key, JSON.stringify(item));\n  };\n\n  LocalStorageHelper.get = function (key) {\n    const itemStr = localStorage.getItem(key); // if the item doesn't exist, return null\n\n    if (!itemStr) {\n      return null;\n    }\n\n    const item = JSON.parse(itemStr);\n    const now = new Date(); // compare the expiry time of the item with the current time\n\n    if (now.getTime() > item.expiry) {\n      // If the item is expired, delete the item from storage\n      // and return null\n      localStorage.removeItem(key);\n      return null;\n    }\n\n    return item.value;\n  };\n\n  LocalStorageHelper.deleteExpiredItems = function (keys) {\n    const now = new Date();\n    keys.forEach(key => {\n      const item = JSON.parse(localStorage.getItem(key));\n\n      if (now.getTime() > item.expiry) {\n        // If the item is expired, delete the item from storage\n        // and return null\n        localStorage.removeItem(key);\n      }\n    });\n  };\n\n  LocalStorageHelper.getAllKeys = function (partialKey) {\n    let arr = [];\n\n    for (var key in localStorage) {\n      if (key.indexOf(partialKey) !== -1) arr.push(key);\n    }\n\n    return arr;\n  };\n\n  return LocalStorageHelper;\n})();", "map": null, "metadata": {}, "sourceType": "module"}