{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { mixinColor, mixinDisabled, mixinDisableRipple, MatRipple, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport * as i1 from '@angular/cdk/a11y';\n/** Default color palette for round buttons (mat-fab and mat-mini-fab) */\n\nconst _c0 = [\"mat-button\", \"\"];\nconst _c1 = [\"*\"];\nconst _c2 = \".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}._mat-animation-noopable.mat-raised-button{transition:none;animation:none}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}._mat-animation-noopable.mat-fab{transition:none;animation:none}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}._mat-animation-noopable.mat-mini-fab{transition:none;animation:none}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}.cdk-high-contrast-active .mat-button-base.cdk-keyboard-focused,.cdk-high-contrast-active .mat-button-base.cdk-program-focused{outline:solid 3px}\\n\";\nconst DEFAULT_ROUND_BUTTON_COLOR = 'accent';\n/**\n * List of classes to add to MatButton instances based on host attributes to\n * style as different variants.\n */\n\nconst BUTTON_HOST_ATTRIBUTES = ['mat-button', 'mat-flat-button', 'mat-icon-button', 'mat-raised-button', 'mat-stroked-button', 'mat-mini-fab', 'mat-fab']; // Boilerplate for applying mixins to MatButton.\n\nconst _MatButtonBase = /*#__PURE__*/mixinColor( /*#__PURE__*/mixinDisabled( /*#__PURE__*/mixinDisableRipple(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n\n})));\n/**\n * Material design button.\n */\n\n\nlet MatButton = /*#__PURE__*/(() => {\n  class MatButton extends _MatButtonBase {\n    constructor(elementRef, _focusMonitor, _animationMode) {\n      super(elementRef);\n      this._focusMonitor = _focusMonitor;\n      this._animationMode = _animationMode;\n      /** Whether the button is round. */\n\n      this.isRoundButton = this._hasHostAttributes('mat-fab', 'mat-mini-fab');\n      /** Whether the button is icon button. */\n\n      this.isIconButton = this._hasHostAttributes('mat-icon-button'); // For each of the variant selectors that is present in the button's host\n      // attributes, add the correct corresponding class.\n\n      for (const attr of BUTTON_HOST_ATTRIBUTES) {\n        if (this._hasHostAttributes(attr)) {\n          this._getHostElement().classList.add(attr);\n        }\n      } // Add a class that applies to all buttons. This makes it easier to target if somebody\n      // wants to target all Material buttons. We do it here rather than `host` to ensure that\n      // the class is applied to derived classes.\n\n\n      elementRef.nativeElement.classList.add('mat-button-base');\n\n      if (this.isRoundButton) {\n        this.color = DEFAULT_ROUND_BUTTON_COLOR;\n      }\n    }\n\n    ngAfterViewInit() {\n      this._focusMonitor.monitor(this._elementRef, true);\n    }\n\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    /** Focuses the button. */\n\n\n    focus(origin, options) {\n      if (origin) {\n        this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n      } else {\n        this._getHostElement().focus(options);\n      }\n    }\n\n    _getHostElement() {\n      return this._elementRef.nativeElement;\n    }\n\n    _isRippleDisabled() {\n      return this.disableRipple || this.disabled;\n    }\n    /** Gets whether the button has one of the given attributes. */\n\n\n    _hasHostAttributes(...attributes) {\n      return attributes.some(attribute => this._getHostElement().hasAttribute(attribute));\n    }\n\n  }\n\n  MatButton.ɵfac = function MatButton_Factory(t) {\n    return new (t || MatButton)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  MatButton.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatButton,\n    selectors: [[\"button\", \"mat-button\", \"\"], [\"button\", \"mat-raised-button\", \"\"], [\"button\", \"mat-icon-button\", \"\"], [\"button\", \"mat-fab\", \"\"], [\"button\", \"mat-mini-fab\", \"\"], [\"button\", \"mat-stroked-button\", \"\"], [\"button\", \"mat-flat-button\", \"\"]],\n    viewQuery: function MatButton_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatRipple, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ripple = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-focus-indicator\"],\n    hostVars: 5,\n    hostBindings: function MatButton_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"disabled\", ctx.disabled || null);\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-button-disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      disabled: \"disabled\",\n      disableRipple: \"disableRipple\",\n      color: \"color\"\n    },\n    exportAs: [\"matButton\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c0,\n    ngContentSelectors: _c1,\n    decls: 4,\n    vars: 5,\n    consts: [[1, \"mat-button-wrapper\"], [\"matRipple\", \"\", 1, \"mat-button-ripple\", 3, \"matRippleDisabled\", \"matRippleCentered\", \"matRippleTrigger\"], [1, \"mat-button-focus-overlay\"]],\n    template: function MatButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(2, \"span\", 1);\n        i0.ɵɵelement(3, \"span\", 2);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"mat-button-ripple-round\", ctx.isRoundButton || ctx.isIconButton);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", ctx.isIconButton)(\"matRippleTrigger\", ctx._getHostElement());\n      }\n    },\n    directives: [i2.MatRipple],\n    styles: [\".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}._mat-animation-noopable.mat-raised-button{transition:none;animation:none}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}._mat-animation-noopable.mat-fab{transition:none;animation:none}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}._mat-animation-noopable.mat-mini-fab{transition:none;animation:none}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}.cdk-high-contrast-active .mat-button-base.cdk-keyboard-focused,.cdk-high-contrast-active .mat-button-base.cdk-program-focused{outline:solid 3px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return MatButton;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Material design anchor button.\n */\n\n\nlet MatAnchor = /*#__PURE__*/(() => {\n  class MatAnchor extends MatButton {\n    constructor(focusMonitor, elementRef, animationMode,\n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    _ngZone) {\n      super(elementRef, focusMonitor, animationMode);\n      this._ngZone = _ngZone;\n\n      this._haltDisabledEvents = event => {\n        // A disabled button shouldn't apply any actions\n        if (this.disabled) {\n          event.preventDefault();\n          event.stopImmediatePropagation();\n        }\n      };\n    }\n\n    ngAfterViewInit() {\n      super.ngAfterViewInit();\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => {\n          this._elementRef.nativeElement.addEventListener('click', this._haltDisabledEvents);\n        });\n      } else {\n        this._elementRef.nativeElement.addEventListener('click', this._haltDisabledEvents);\n      }\n    }\n\n    ngOnDestroy() {\n      super.ngOnDestroy();\n\n      this._elementRef.nativeElement.removeEventListener('click', this._haltDisabledEvents);\n    }\n\n  }\n\n  MatAnchor.ɵfac = function MatAnchor_Factory(t) {\n    return new (t || MatAnchor)(i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(i0.NgZone, 8));\n  };\n\n  MatAnchor.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatAnchor,\n    selectors: [[\"a\", \"mat-button\", \"\"], [\"a\", \"mat-raised-button\", \"\"], [\"a\", \"mat-icon-button\", \"\"], [\"a\", \"mat-fab\", \"\"], [\"a\", \"mat-mini-fab\", \"\"], [\"a\", \"mat-stroked-button\", \"\"], [\"a\", \"mat-flat-button\", \"\"]],\n    hostAttrs: [1, \"mat-focus-indicator\"],\n    hostVars: 7,\n    hostBindings: function MatAnchor_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex || 0)(\"disabled\", ctx.disabled || null)(\"aria-disabled\", ctx.disabled.toString());\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-button-disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      disabled: \"disabled\",\n      disableRipple: \"disableRipple\",\n      color: \"color\",\n      tabIndex: \"tabIndex\"\n    },\n    exportAs: [\"matButton\", \"matAnchor\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c0,\n    ngContentSelectors: _c1,\n    decls: 4,\n    vars: 5,\n    consts: [[1, \"mat-button-wrapper\"], [\"matRipple\", \"\", 1, \"mat-button-ripple\", 3, \"matRippleDisabled\", \"matRippleCentered\", \"matRippleTrigger\"], [1, \"mat-button-focus-overlay\"]],\n    template: function MatAnchor_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(2, \"span\", 1);\n        i0.ɵɵelement(3, \"span\", 2);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"mat-button-ripple-round\", ctx.isRoundButton || ctx.isIconButton);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", ctx.isIconButton)(\"matRippleTrigger\", ctx._getHostElement());\n      }\n    },\n    directives: [i2.MatRipple],\n    styles: [_c2],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return MatAnchor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet MatButtonModule = /*#__PURE__*/(() => {\n  class MatButtonModule {}\n\n  MatButtonModule.ɵfac = function MatButtonModule_Factory(t) {\n    return new (t || MatButtonModule)();\n  };\n\n  MatButtonModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatButtonModule\n  });\n  MatButtonModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[MatRippleModule, MatCommonModule], MatCommonModule]\n  });\n  return MatButtonModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MatAnchor, MatButton, MatButtonModule }; //# sourceMappingURL=button.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}