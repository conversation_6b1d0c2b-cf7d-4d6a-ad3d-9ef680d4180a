{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { APP_BOOTSTRAP_LISTENER, PLATFORM_ID, NgModule, Injectable, InjectionToken, Inject, inject, Directive } from '@angular/core';\nimport { isPlatformBrowser, DOCUMENT, isPlatformServer } from '@angular/common';\nimport { BehaviorSubject, Observable, merge, Subject, asapScheduler, of, fromEvent } from 'rxjs';\nimport { applyCssPrefixes, extendObject, buildLayoutCSS } from '@angular/flex-layout/_private-utils';\nimport { filter, tap, map, debounceTime, switchMap, distinctUntilChanged, takeUntil, take } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Find all of the server-generated stylings, if any, and remove them\n * This will be in the form of inline classes and the style block in the\n * head of the DOM\n */\n\nfunction removeStyles(_document, platformId) {\n  return () => {\n    if (isPlatformBrowser(platformId)) {\n      const elements = Array.from(_document.querySelectorAll(`[class*=${CLASS_NAME}]`)); // RegExp constructor should only be used if passing a variable to the constructor.\n      // When using static regular expression it is more performant to use reg exp literal.\n      // This is also needed to provide Safari 9 compatibility, please see\n      // https://stackoverflow.com/questions/37919802 for more discussion.\n\n      const classRegex = /\\bflex-layout-.+?\\b/g;\n      elements.forEach(el => {\n        el.classList.contains(`${CLASS_NAME}ssr`) && el.parentNode ? el.parentNode.removeChild(el) : el.className.replace(classRegex, '');\n      });\n    }\n  };\n}\n/**\n *  Provider to remove SSR styles on the browser\n */\n\n\nconst BROWSER_PROVIDER = {\n  provide: APP_BOOTSTRAP_LISTENER,\n  useFactory: removeStyles,\n  deps: [DOCUMENT, PLATFORM_ID],\n  multi: true\n};\nconst CLASS_NAME = 'flex-layout-';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * *****************************************************************\n * Define module for common Angular Layout utilities\n * *****************************************************************\n */\n\nlet CoreModule = /*#__PURE__*/(() => {\n  class CoreModule {}\n\n  CoreModule.ɵfac = function CoreModule_Factory(t) {\n    return new (t || CoreModule)();\n  };\n\n  CoreModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CoreModule\n  });\n  CoreModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [BROWSER_PROVIDER]\n  });\n  return CoreModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Class instances emitted [to observers] for each mql notification\n */\n\n\nclass MediaChange {\n  /**\n   * @param matches whether the mediaQuery is currently activated\n   * @param mediaQuery e.g. (min-width: 600px) and (max-width: 959px)\n   * @param mqAlias e.g. gt-sm, md, gt-lg\n   * @param suffix e.g. GtSM, Md, GtLg\n   * @param priority the priority of activation for the given breakpoint\n   */\n  constructor(matches = false, mediaQuery = 'all', mqAlias = '', suffix = '', priority = 0) {\n    this.matches = matches;\n    this.mediaQuery = mediaQuery;\n    this.mqAlias = mqAlias;\n    this.suffix = suffix;\n    this.priority = priority;\n    this.property = '';\n  }\n  /** Create an exact copy of the MediaChange */\n\n\n  clone() {\n    return new MediaChange(this.matches, this.mediaQuery, this.mqAlias, this.suffix);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Utility to emulate a CSS stylesheet\n *\n * This utility class stores all of the styles for a given HTML element\n * as a readonly `stylesheet` map.\n */\n\n\nlet StylesheetMap = /*#__PURE__*/(() => {\n  class StylesheetMap {\n    constructor() {\n      this.stylesheet = new Map();\n    }\n    /**\n     * Add an individual style to an HTML element\n     */\n\n\n    addStyleToElement(element, style, value) {\n      const stylesheet = this.stylesheet.get(element);\n\n      if (stylesheet) {\n        stylesheet.set(style, value);\n      } else {\n        this.stylesheet.set(element, new Map([[style, value]]));\n      }\n    }\n    /**\n     * Clear the virtual stylesheet\n     */\n\n\n    clearStyles() {\n      this.stylesheet.clear();\n    }\n    /**\n     * Retrieve a given style for an HTML element\n     */\n\n\n    getStyleForElement(el, styleName) {\n      const styles = this.stylesheet.get(el);\n      let value = '';\n\n      if (styles) {\n        const style = styles.get(styleName);\n\n        if (typeof style === 'number' || typeof style === 'string') {\n          value = style + '';\n        }\n      }\n\n      return value;\n    }\n\n  }\n\n  StylesheetMap.ɵfac = function StylesheetMap_Factory(t) {\n    return new (t || StylesheetMap)();\n  };\n\n  StylesheetMap.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: StylesheetMap,\n    factory: StylesheetMap.ɵfac,\n    providedIn: 'root'\n  });\n  return StylesheetMap;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_CONFIG = {\n  addFlexToParent: true,\n  addOrientationBps: false,\n  disableDefaultBps: false,\n  disableVendorPrefixes: false,\n  serverLoaded: false,\n  useColumnBasisZero: true,\n  printWithBreakpoints: [],\n  mediaTriggerAutoRestore: true,\n  ssrObserveBreakpoints: [],\n  // This is disabled by default because otherwise the multiplier would\n  // run for all users, regardless of whether they're using this feature.\n  // Instead, we disable it by default, which requires this ugly cast.\n  multiplier: undefined,\n  defaultUnit: 'px',\n  detectLayoutDisplay: false\n};\nconst LAYOUT_CONFIG = /*#__PURE__*/new InjectionToken('Flex Layout token, config options for the library', {\n  providedIn: 'root',\n  factory: () => DEFAULT_CONFIG\n});\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Token that is provided to tell whether the FlexLayoutServerModule\n * has been included in the bundle\n *\n * NOTE: This can be manually provided to disable styles when using SSR\n */\n\nconst SERVER_TOKEN = /*#__PURE__*/new InjectionToken('FlexLayoutServerLoaded', {\n  providedIn: 'root',\n  factory: () => false\n});\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst BREAKPOINT = /*#__PURE__*/new InjectionToken('Flex Layout token, collect all breakpoints into one provider', {\n  providedIn: 'root',\n  factory: () => null\n});\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * For the specified MediaChange, make sure it contains the breakpoint alias\n * and suffix (if available).\n */\n\nfunction mergeAlias(dest, source) {\n  dest = dest ? dest.clone() : new MediaChange();\n\n  if (source) {\n    dest.mqAlias = source.alias;\n    dest.mediaQuery = source.mediaQuery;\n    dest.suffix = source.suffix;\n    dest.priority = source.priority;\n  }\n\n  return dest;\n}\n/** A class that encapsulates CSS style generation for common directives */\n\n\nclass StyleBuilder {\n  constructor() {\n    /** Whether to cache the generated output styles */\n    this.shouldCache = true;\n  }\n  /**\n   * Run a side effect computation given the input string and the computed styles\n   * from the build task and the host configuration object\n   * NOTE: This should be a no-op unless an algorithm is provided in a subclass\n   */\n\n\n  sideEffect(_input, _styles, _parent) {}\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet StyleUtils = /*#__PURE__*/(() => {\n  class StyleUtils {\n    constructor(_serverStylesheet, _serverModuleLoaded, _platformId, layoutConfig) {\n      this._serverStylesheet = _serverStylesheet;\n      this._serverModuleLoaded = _serverModuleLoaded;\n      this._platformId = _platformId;\n      this.layoutConfig = layoutConfig;\n    }\n    /**\n     * Applies styles given via string pair or object map to the directive element\n     */\n\n\n    applyStyleToElement(element, style, value = null) {\n      let styles = {};\n\n      if (typeof style === 'string') {\n        styles[style] = value;\n        style = styles;\n      }\n\n      styles = this.layoutConfig.disableVendorPrefixes ? style : applyCssPrefixes(style);\n\n      this._applyMultiValueStyleToElement(styles, element);\n    }\n    /**\n     * Applies styles given via string pair or object map to the directive's element\n     */\n\n\n    applyStyleToElements(style, elements = []) {\n      const styles = this.layoutConfig.disableVendorPrefixes ? style : applyCssPrefixes(style);\n      elements.forEach(el => {\n        this._applyMultiValueStyleToElement(styles, el);\n      });\n    }\n    /**\n     * Determine the DOM element's Flexbox flow (flex-direction)\n     *\n     * Check inline style first then check computed (stylesheet) style\n     */\n\n\n    getFlowDirection(target) {\n      const query = 'flex-direction';\n      let value = this.lookupStyle(target, query);\n      const hasInlineValue = this.lookupInlineStyle(target, query) || isPlatformServer(this._platformId) && this._serverModuleLoaded ? value : '';\n      return [value || 'row', hasInlineValue];\n    }\n\n    hasWrap(target) {\n      const query = 'flex-wrap';\n      return this.lookupStyle(target, query) === 'wrap';\n    }\n    /**\n     * Find the DOM element's raw attribute value (if any)\n     */\n\n\n    lookupAttributeValue(element, attribute) {\n      var _a;\n\n      return (_a = element.getAttribute(attribute)) !== null && _a !== void 0 ? _a : '';\n    }\n    /**\n     * Find the DOM element's inline style value (if any)\n     */\n\n\n    lookupInlineStyle(element, styleName) {\n      return isPlatformBrowser(this._platformId) ? element.style.getPropertyValue(styleName) : getServerStyle(element, styleName);\n    }\n    /**\n     * Determine the inline or inherited CSS style\n     * NOTE: platform-server has no implementation for getComputedStyle\n     */\n\n\n    lookupStyle(element, styleName, inlineOnly = false) {\n      let value = '';\n\n      if (element) {\n        let immediateValue = value = this.lookupInlineStyle(element, styleName);\n\n        if (!immediateValue) {\n          if (isPlatformBrowser(this._platformId)) {\n            if (!inlineOnly) {\n              value = getComputedStyle(element).getPropertyValue(styleName);\n            }\n          } else {\n            if (this._serverModuleLoaded) {\n              value = this._serverStylesheet.getStyleForElement(element, styleName);\n            }\n          }\n        }\n      } // Note: 'inline' is the default of all elements, unless UA stylesheet overrides;\n      //       in which case getComputedStyle() should determine a valid value.\n\n\n      return value ? value.trim() : '';\n    }\n    /**\n     * Applies the styles to the element. The styles object map may contain an array of values\n     * Each value will be added as element style\n     * Keys are sorted to add prefixed styles (like -webkit-x) first, before the standard ones\n     */\n\n\n    _applyMultiValueStyleToElement(styles, element) {\n      Object.keys(styles).sort().forEach(key => {\n        const el = styles[key];\n        const values = Array.isArray(el) ? el : [el];\n        values.sort();\n\n        for (let value of values) {\n          value = value ? value + '' : '';\n\n          if (isPlatformBrowser(this._platformId) || !this._serverModuleLoaded) {\n            isPlatformBrowser(this._platformId) ? element.style.setProperty(key, value) : setServerStyle(element, key, value);\n          } else {\n            this._serverStylesheet.addStyleToElement(element, key, value);\n          }\n        }\n      });\n    }\n\n  }\n\n  StyleUtils.ɵfac = function StyleUtils_Factory(t) {\n    return new (t || StyleUtils)(i0.ɵɵinject(StylesheetMap), i0.ɵɵinject(SERVER_TOKEN), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(LAYOUT_CONFIG));\n  };\n\n  StyleUtils.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: StyleUtils,\n    factory: StyleUtils.ɵfac,\n    providedIn: 'root'\n  });\n  return StyleUtils;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction getServerStyle(element, styleName) {\n  var _a;\n\n  const styleMap = readStyleAttribute(element);\n  return (_a = styleMap[styleName]) !== null && _a !== void 0 ? _a : '';\n}\n\nfunction setServerStyle(element, styleName, styleValue) {\n  styleName = styleName.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n  const styleMap = readStyleAttribute(element);\n  styleMap[styleName] = styleValue !== null && styleValue !== void 0 ? styleValue : '';\n  writeStyleAttribute(element, styleMap);\n}\n\nfunction writeStyleAttribute(element, styleMap) {\n  let styleAttrValue = '';\n\n  for (const key in styleMap) {\n    const newValue = styleMap[key];\n\n    if (newValue) {\n      styleAttrValue += `${key}:${styleMap[key]};`;\n    }\n  }\n\n  element.setAttribute('style', styleAttrValue);\n}\n\nfunction readStyleAttribute(element) {\n  const styleMap = {};\n  const styleAttribute = element.getAttribute('style');\n\n  if (styleAttribute) {\n    const styleList = styleAttribute.split(/;+/g);\n\n    for (let i = 0; i < styleList.length; i++) {\n      const style = styleList[i].trim();\n\n      if (style.length > 0) {\n        const colonIndex = style.indexOf(':');\n\n        if (colonIndex === -1) {\n          throw new Error(`Invalid CSS style: ${style}`);\n        }\n\n        const name = style.substr(0, colonIndex).trim();\n        styleMap[name] = style.substr(colonIndex + 1).trim();\n      }\n    }\n  }\n\n  return styleMap;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** HOF to sort the breakpoints by descending priority */\n\n\nfunction sortDescendingPriority(a, b) {\n  const priorityA = a ? a.priority || 0 : 0;\n  const priorityB = b ? b.priority || 0 : 0;\n  return priorityB - priorityA;\n}\n/** HOF to sort the breakpoints by ascending priority */\n\n\nfunction sortAscendingPriority(a, b) {\n  const pA = a.priority || 0;\n  const pB = b.priority || 0;\n  return pA - pB;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * MediaMonitor configures listeners to mediaQuery changes and publishes an Observable facade to\n * convert mediaQuery change callbacks to subscriber notifications. These notifications will be\n * performed within the ng Zone to trigger change detections and component updates.\n *\n * NOTE: both mediaQuery activations and de-activations are announced in notifications\n */\n\n\nlet MatchMedia = /*#__PURE__*/(() => {\n  class MatchMedia {\n    constructor(_zone, _platformId, _document) {\n      this._zone = _zone;\n      this._platformId = _platformId;\n      this._document = _document;\n      /** Initialize source with 'all' so all non-responsive APIs trigger style updates */\n\n      this.source = new BehaviorSubject(new MediaChange(true));\n      this.registry = new Map();\n      this.pendingRemoveListenerFns = [];\n      this._observable$ = this.source.asObservable();\n    }\n    /**\n     * Publish list of all current activations\n     */\n\n\n    get activations() {\n      const results = [];\n      this.registry.forEach((mql, key) => {\n        if (mql.matches) {\n          results.push(key);\n        }\n      });\n      return results;\n    }\n    /**\n     * For the specified mediaQuery?\n     */\n\n\n    isActive(mediaQuery) {\n      var _a;\n\n      const mql = this.registry.get(mediaQuery);\n      return (_a = mql === null || mql === void 0 ? void 0 : mql.matches) !== null && _a !== void 0 ? _a : this.registerQuery(mediaQuery).some(m => m.matches);\n    }\n    /**\n     * External observers can watch for all (or a specific) mql changes.\n     * Typically used by the MediaQueryAdaptor; optionally available to components\n     * who wish to use the MediaMonitor as mediaMonitor$ observable service.\n     *\n     * Use deferred registration process to register breakpoints only on subscription\n     * This logic also enforces logic to register all mediaQueries BEFORE notify\n     * subscribers of notifications.\n     */\n\n\n    observe(mqList, filterOthers = false) {\n      if (mqList && mqList.length) {\n        const matchMedia$ = this._observable$.pipe(filter(change => !filterOthers ? true : mqList.indexOf(change.mediaQuery) > -1));\n\n        const registration$ = new Observable(observer => {\n          const matches = this.registerQuery(mqList);\n\n          if (matches.length) {\n            const lastChange = matches.pop();\n            matches.forEach(e => {\n              observer.next(e);\n            });\n            this.source.next(lastChange); // last match is cached\n          }\n\n          observer.complete();\n        });\n        return merge(registration$, matchMedia$);\n      }\n\n      return this._observable$;\n    }\n    /**\n     * Based on the BreakPointRegistry provider, register internal listeners for each unique\n     * mediaQuery. Each listener emits specific MediaChange data to observers\n     */\n\n\n    registerQuery(mediaQuery) {\n      const list = Array.isArray(mediaQuery) ? mediaQuery : [mediaQuery];\n      const matches = [];\n      buildQueryCss(list, this._document);\n      list.forEach(query => {\n        const onMQLEvent = e => {\n          this._zone.run(() => this.source.next(new MediaChange(e.matches, query)));\n        };\n\n        let mql = this.registry.get(query);\n\n        if (!mql) {\n          mql = this.buildMQL(query);\n          mql.addListener(onMQLEvent);\n          this.pendingRemoveListenerFns.push(() => mql.removeListener(onMQLEvent));\n          this.registry.set(query, mql);\n        }\n\n        if (mql.matches) {\n          matches.push(new MediaChange(true, query));\n        }\n      });\n      return matches;\n    }\n\n    ngOnDestroy() {\n      let fn;\n\n      while (fn = this.pendingRemoveListenerFns.pop()) {\n        fn();\n      }\n    }\n    /**\n     * Call window.matchMedia() to build a MediaQueryList; which\n     * supports 0..n listeners for activation/deactivation\n     */\n\n\n    buildMQL(query) {\n      return constructMql(query, isPlatformBrowser(this._platformId));\n    }\n\n  }\n\n  MatchMedia.ɵfac = function MatchMedia_Factory(t) {\n    return new (t || MatchMedia)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(DOCUMENT));\n  };\n\n  MatchMedia.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatchMedia,\n    factory: MatchMedia.ɵfac,\n    providedIn: 'root'\n  });\n  return MatchMedia;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Private global registry for all dynamically-created, injected style tags\n * @see prepare(query)\n */\n\n\nconst ALL_STYLES = {};\n/**\n * For Webkit engines that only trigger the MediaQueryList Listener\n * when there is at least one CSS selector for the respective media query.\n *\n * @param mediaQueries\n * @param _document\n */\n\nfunction buildQueryCss(mediaQueries, _document) {\n  const list = mediaQueries.filter(it => !ALL_STYLES[it]);\n\n  if (list.length > 0) {\n    const query = list.join(', ');\n\n    try {\n      const styleEl = _document.createElement('style');\n\n      styleEl.setAttribute('type', 'text/css');\n\n      if (!styleEl.styleSheet) {\n        const cssText = `\n/*\n  @angular/flex-layout - workaround for possible browser quirk with mediaQuery listeners\n  see http://bit.ly/2sd4HMP\n*/\n@media ${query} {.fx-query-test{ }}\n`;\n        styleEl.appendChild(_document.createTextNode(cssText));\n      }\n\n      _document.head.appendChild(styleEl); // Store in private global registry\n\n\n      list.forEach(mq => ALL_STYLES[mq] = styleEl);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n}\n\nfunction constructMql(query, isBrowser) {\n  const canListen = isBrowser && !!window.matchMedia('all').addListener;\n  return canListen ? window.matchMedia(query) : {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {},\n    onchange: null,\n\n    addEventListener() {},\n\n    removeEventListener() {},\n\n    dispatchEvent() {\n      return false;\n    }\n\n  };\n}\n/**\n * NOTE: Smaller ranges have HIGHER priority since the match is more specific\n */\n\n\nconst DEFAULT_BREAKPOINTS = [{\n  alias: 'xs',\n  mediaQuery: 'screen and (min-width: 0px) and (max-width: 599.98px)',\n  priority: 1000\n}, {\n  alias: 'sm',\n  mediaQuery: 'screen and (min-width: 600px) and (max-width: 959.98px)',\n  priority: 900\n}, {\n  alias: 'md',\n  mediaQuery: 'screen and (min-width: 960px) and (max-width: 1279.98px)',\n  priority: 800\n}, {\n  alias: 'lg',\n  mediaQuery: 'screen and (min-width: 1280px) and (max-width: 1919.98px)',\n  priority: 700\n}, {\n  alias: 'xl',\n  mediaQuery: 'screen and (min-width: 1920px) and (max-width: 4999.98px)',\n  priority: 600\n}, {\n  alias: 'lt-sm',\n  overlapping: true,\n  mediaQuery: 'screen and (max-width: 599.98px)',\n  priority: 950\n}, {\n  alias: 'lt-md',\n  overlapping: true,\n  mediaQuery: 'screen and (max-width: 959.98px)',\n  priority: 850\n}, {\n  alias: 'lt-lg',\n  overlapping: true,\n  mediaQuery: 'screen and (max-width: 1279.98px)',\n  priority: 750\n}, {\n  alias: 'lt-xl',\n  overlapping: true,\n  priority: 650,\n  mediaQuery: 'screen and (max-width: 1919.98px)'\n}, {\n  alias: 'gt-xs',\n  overlapping: true,\n  mediaQuery: 'screen and (min-width: 600px)',\n  priority: -950\n}, {\n  alias: 'gt-sm',\n  overlapping: true,\n  mediaQuery: 'screen and (min-width: 960px)',\n  priority: -850\n}, {\n  alias: 'gt-md',\n  overlapping: true,\n  mediaQuery: 'screen and (min-width: 1280px)',\n  priority: -750\n}, {\n  alias: 'gt-lg',\n  overlapping: true,\n  mediaQuery: 'screen and (min-width: 1920px)',\n  priority: -650\n}];\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/* tslint:disable */\n\nconst HANDSET_PORTRAIT = '(orientation: portrait) and (max-width: 599.98px)';\nconst HANDSET_LANDSCAPE = '(orientation: landscape) and (max-width: 959.98px)';\nconst TABLET_PORTRAIT = '(orientation: portrait) and (min-width: 600px) and (max-width: 839.98px)';\nconst TABLET_LANDSCAPE = '(orientation: landscape) and (min-width: 960px) and (max-width: 1279.98px)';\nconst WEB_PORTRAIT = '(orientation: portrait) and (min-width: 840px)';\nconst WEB_LANDSCAPE = '(orientation: landscape) and (min-width: 1280px)';\nconst ScreenTypes = {\n  'HANDSET': `${HANDSET_PORTRAIT}, ${HANDSET_LANDSCAPE}`,\n  'TABLET': `${TABLET_PORTRAIT} , ${TABLET_LANDSCAPE}`,\n  'WEB': `${WEB_PORTRAIT}, ${WEB_LANDSCAPE} `,\n  'HANDSET_PORTRAIT': `${HANDSET_PORTRAIT}`,\n  'TABLET_PORTRAIT': `${TABLET_PORTRAIT} `,\n  'WEB_PORTRAIT': `${WEB_PORTRAIT}`,\n  'HANDSET_LANDSCAPE': `${HANDSET_LANDSCAPE}`,\n  'TABLET_LANDSCAPE': `${TABLET_LANDSCAPE}`,\n  'WEB_LANDSCAPE': `${WEB_LANDSCAPE}`\n};\n/**\n * Extended Breakpoints for handset/tablets with landscape or portrait orientations\n */\n\nconst ORIENTATION_BREAKPOINTS = [{\n  'alias': 'handset',\n  priority: 2000,\n  'mediaQuery': ScreenTypes.HANDSET\n}, {\n  'alias': 'handset.landscape',\n  priority: 2000,\n  'mediaQuery': ScreenTypes.HANDSET_LANDSCAPE\n}, {\n  'alias': 'handset.portrait',\n  priority: 2000,\n  'mediaQuery': ScreenTypes.HANDSET_PORTRAIT\n}, {\n  'alias': 'tablet',\n  priority: 2100,\n  'mediaQuery': ScreenTypes.TABLET\n}, {\n  'alias': 'tablet.landscape',\n  priority: 2100,\n  'mediaQuery': ScreenTypes.TABLET_LANDSCAPE\n}, {\n  'alias': 'tablet.portrait',\n  priority: 2100,\n  'mediaQuery': ScreenTypes.TABLET_PORTRAIT\n}, {\n  'alias': 'web',\n  priority: 2200,\n  'mediaQuery': ScreenTypes.WEB,\n  overlapping: true\n}, {\n  'alias': 'web.landscape',\n  priority: 2200,\n  'mediaQuery': ScreenTypes.WEB_LANDSCAPE,\n  overlapping: true\n}, {\n  'alias': 'web.portrait',\n  priority: 2200,\n  'mediaQuery': ScreenTypes.WEB_PORTRAIT,\n  overlapping: true\n}];\nconst ALIAS_DELIMITERS = /(\\.|-|_)/g;\n\nfunction firstUpperCase(part) {\n  let first = part.length > 0 ? part.charAt(0) : '';\n  let remainder = part.length > 1 ? part.slice(1) : '';\n  return first.toUpperCase() + remainder;\n}\n/**\n * Converts snake-case to SnakeCase.\n * @param name Text to UpperCamelCase\n */\n\n\nfunction camelCase(name) {\n  return name.replace(ALIAS_DELIMITERS, '|').split('|').map(firstUpperCase).join('');\n}\n/**\n * For each breakpoint, ensure that a Suffix is defined;\n * fallback to UpperCamelCase the unique Alias value\n */\n\n\nfunction validateSuffixes(list) {\n  list.forEach(bp => {\n    if (!bp.suffix) {\n      bp.suffix = camelCase(bp.alias); // create Suffix value based on alias\n\n      bp.overlapping = !!bp.overlapping; // ensure default value\n    }\n  });\n  return list;\n}\n/**\n * Merge a custom breakpoint list with the default list based on unique alias values\n *  - Items are added if the alias is not in the default list\n *  - Items are merged with the custom override if the alias exists in the default list\n */\n\n\nfunction mergeByAlias(defaults, custom = []) {\n  const dict = {};\n  defaults.forEach(bp => {\n    dict[bp.alias] = bp;\n  }); // Merge custom breakpoints\n\n  custom.forEach(bp => {\n    if (dict[bp.alias]) {\n      extendObject(dict[bp.alias], bp);\n    } else {\n      dict[bp.alias] = bp;\n    }\n  });\n  return validateSuffixes(Object.keys(dict).map(k => dict[k]));\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n *  Injection token unique to the flex-layout library.\n *  Use this token when build a custom provider (see below).\n */\n\n\nconst BREAKPOINTS = /*#__PURE__*/new InjectionToken('Token (@angular/flex-layout) Breakpoints', {\n  providedIn: 'root',\n  factory: () => {\n    const breakpoints = inject(BREAKPOINT);\n    const layoutConfig = inject(LAYOUT_CONFIG);\n    const bpFlattenArray = [].concat.apply([], (breakpoints || []).map(v => Array.isArray(v) ? v : [v]));\n    const builtIns = (layoutConfig.disableDefaultBps ? [] : DEFAULT_BREAKPOINTS).concat(layoutConfig.addOrientationBps ? ORIENTATION_BREAKPOINTS : []);\n    return mergeByAlias(builtIns, bpFlattenArray);\n  }\n});\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Registry of 1..n MediaQuery breakpoint ranges\n * This is published as a provider and may be overridden from custom, application-specific ranges\n *\n */\n\nlet BreakPointRegistry = /*#__PURE__*/(() => {\n  class BreakPointRegistry {\n    constructor(list) {\n      /**\n       * Memoized BreakPoint Lookups\n       */\n      this.findByMap = new Map();\n      this.items = [...list].sort(sortAscendingPriority);\n    }\n    /**\n     * Search breakpoints by alias (e.g. gt-xs)\n     */\n\n\n    findByAlias(alias) {\n      return !alias ? null : this.findWithPredicate(alias, bp => bp.alias === alias);\n    }\n\n    findByQuery(query) {\n      return this.findWithPredicate(query, bp => bp.mediaQuery === query);\n    }\n    /**\n     * Get all the breakpoints whose ranges could overlapping `normal` ranges;\n     * e.g. gt-sm overlaps md, lg, and xl\n     */\n\n\n    get overlappings() {\n      return this.items.filter(it => it.overlapping);\n    }\n    /**\n     * Get list of all registered (non-empty) breakpoint aliases\n     */\n\n\n    get aliases() {\n      return this.items.map(it => it.alias);\n    }\n    /**\n     * Aliases are mapped to properties using suffixes\n     * e.g.  'gt-sm' for property 'layout'  uses suffix 'GtSm'\n     * for property layoutGtSM.\n     */\n\n\n    get suffixes() {\n      return this.items.map(it => {\n        var _a;\n\n        return (_a = it === null || it === void 0 ? void 0 : it.suffix) !== null && _a !== void 0 ? _a : '';\n      });\n    }\n    /**\n     * Memoized lookup using custom predicate function\n     */\n\n\n    findWithPredicate(key, searchFn) {\n      var _a;\n\n      let response = this.findByMap.get(key);\n\n      if (!response) {\n        response = (_a = this.items.find(searchFn)) !== null && _a !== void 0 ? _a : null;\n        this.findByMap.set(key, response);\n      }\n\n      return response !== null && response !== void 0 ? response : null;\n    }\n\n  }\n\n  BreakPointRegistry.ɵfac = function BreakPointRegistry_Factory(t) {\n    return new (t || BreakPointRegistry)(i0.ɵɵinject(BREAKPOINTS));\n  };\n\n  BreakPointRegistry.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BreakPointRegistry,\n    factory: BreakPointRegistry.ɵfac,\n    providedIn: 'root'\n  });\n  return BreakPointRegistry;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst PRINT = 'print';\nconst BREAKPOINT_PRINT = {\n  alias: PRINT,\n  mediaQuery: PRINT,\n  priority: 1000\n};\n/**\n * PrintHook - Use to intercept print MediaQuery activations and force\n *             layouts to render with the specified print alias/breakpoint\n *\n * Used in MediaMarshaller and MediaObserver\n */\n\nlet PrintHook = /*#__PURE__*/(() => {\n  class PrintHook {\n    constructor(breakpoints, layoutConfig, _document) {\n      this.breakpoints = breakpoints;\n      this.layoutConfig = layoutConfig;\n      this._document = _document; // registeredBeforeAfterPrintHooks tracks if we registered the `beforeprint`\n      //  and `afterprint` event listeners.\n\n      this.registeredBeforeAfterPrintHooks = false; // isPrintingBeforeAfterEvent is used to track if we are printing from within\n      // a `beforeprint` event handler. This prevents the typical `stopPrinting`\n      // form `interceptEvents` so that printing is not stopped while the dialog\n      // is still open. This is an extension of the `isPrinting` property on\n      // browsers which support `beforeprint` and `afterprint` events.\n\n      this.isPrintingBeforeAfterEvent = false;\n      this.beforePrintEventListeners = [];\n      this.afterPrintEventListeners = [];\n      this.formerActivations = null; // Is this service currently in print mode\n\n      this.isPrinting = false;\n      this.queue = new PrintQueue();\n      this.deactivations = [];\n    }\n    /** Add 'print' mediaQuery: to listen for matchMedia activations */\n\n\n    withPrintQuery(queries) {\n      return [...queries, PRINT];\n    }\n    /** Is the MediaChange event for any 'print' @media */\n\n\n    isPrintEvent(e) {\n      return e.mediaQuery.startsWith(PRINT);\n    }\n    /** What is the desired mqAlias to use while printing? */\n\n\n    get printAlias() {\n      var _a;\n\n      return [...((_a = this.layoutConfig.printWithBreakpoints) !== null && _a !== void 0 ? _a : [])];\n    }\n    /** Lookup breakpoints associated with print aliases. */\n\n\n    get printBreakPoints() {\n      return this.printAlias.map(alias => this.breakpoints.findByAlias(alias)).filter(bp => bp !== null);\n    }\n    /** Lookup breakpoint associated with mediaQuery */\n\n\n    getEventBreakpoints({\n      mediaQuery\n    }) {\n      const bp = this.breakpoints.findByQuery(mediaQuery);\n      const list = bp ? [...this.printBreakPoints, bp] : this.printBreakPoints;\n      return list.sort(sortDescendingPriority);\n    }\n    /** Update event with printAlias mediaQuery information */\n\n\n    updateEvent(event) {\n      var _a;\n\n      let bp = this.breakpoints.findByQuery(event.mediaQuery);\n\n      if (this.isPrintEvent(event)) {\n        // Reset from 'print' to first (highest priority) print breakpoint\n        bp = this.getEventBreakpoints(event)[0];\n        event.mediaQuery = (_a = bp === null || bp === void 0 ? void 0 : bp.mediaQuery) !== null && _a !== void 0 ? _a : '';\n      }\n\n      return mergeAlias(event, bp);\n    } // registerBeforeAfterPrintHooks registers a `beforeprint` event hook so we can\n    // trigger print styles synchronously and apply proper layout styles.\n    // It is a noop if the hooks have already been registered or if the document's\n    // `defaultView` is not available.\n\n\n    registerBeforeAfterPrintHooks(target) {\n      // `defaultView` may be null when rendering on the server or in other contexts.\n      if (!this._document.defaultView || this.registeredBeforeAfterPrintHooks) {\n        return;\n      }\n\n      this.registeredBeforeAfterPrintHooks = true;\n\n      const beforePrintListener = () => {\n        // If we aren't already printing, start printing and update the styles as\n        // if there was a regular print `MediaChange`(from matchMedia).\n        if (!this.isPrinting) {\n          this.isPrintingBeforeAfterEvent = true;\n          this.startPrinting(target, this.getEventBreakpoints(new MediaChange(true, PRINT)));\n          target.updateStyles();\n        }\n      };\n\n      const afterPrintListener = () => {\n        // If we aren't already printing, start printing and update the styles as\n        // if there was a regular print `MediaChange`(from matchMedia).\n        this.isPrintingBeforeAfterEvent = false;\n\n        if (this.isPrinting) {\n          this.stopPrinting(target);\n          target.updateStyles();\n        }\n      }; // Could we have teardown logic to remove if there are no print listeners being used?\n\n\n      this._document.defaultView.addEventListener('beforeprint', beforePrintListener);\n\n      this._document.defaultView.addEventListener('afterprint', afterPrintListener);\n\n      this.beforePrintEventListeners.push(beforePrintListener);\n      this.afterPrintEventListeners.push(afterPrintListener);\n    }\n    /**\n     * Prepare RxJS tap operator with partial application\n     * @return pipeable tap predicate\n     */\n\n\n    interceptEvents(target) {\n      return event => {\n        if (this.isPrintEvent(event)) {\n          if (event.matches && !this.isPrinting) {\n            this.startPrinting(target, this.getEventBreakpoints(event));\n            target.updateStyles();\n          } else if (!event.matches && this.isPrinting && !this.isPrintingBeforeAfterEvent) {\n            this.stopPrinting(target);\n            target.updateStyles();\n          }\n\n          return;\n        }\n\n        this.collectActivations(target, event);\n      };\n    }\n    /** Stop mediaChange event propagation in event streams */\n\n\n    blockPropagation() {\n      return event => {\n        return !(this.isPrinting || this.isPrintEvent(event));\n      };\n    }\n    /**\n     * Save current activateBreakpoints (for later restore)\n     * and substitute only the printAlias breakpoint\n     */\n\n\n    startPrinting(target, bpList) {\n      this.isPrinting = true;\n      this.formerActivations = target.activatedBreakpoints;\n      target.activatedBreakpoints = this.queue.addPrintBreakpoints(bpList);\n    }\n    /** For any print de-activations, reset the entire print queue */\n\n\n    stopPrinting(target) {\n      target.activatedBreakpoints = this.deactivations;\n      this.deactivations = [];\n      this.formerActivations = null;\n      this.queue.clear();\n      this.isPrinting = false;\n    }\n    /**\n     * To restore pre-Print Activations, we must capture the proper\n     * list of breakpoint activations BEFORE print starts. OnBeforePrint()\n     * is supported; so 'print' mediaQuery activations are used as a fallback\n     * in browsers without `beforeprint` support.\n     *\n     * >  But activated breakpoints are deactivated BEFORE 'print' activation.\n     *\n     * Let's capture all de-activations using the following logic:\n     *\n     *  When not printing:\n     *    - clear cache when activating non-print breakpoint\n     *    - update cache (and sort) when deactivating\n     *\n     *  When printing:\n     *    - sort and save when starting print\n     *    - restore as activatedTargets and clear when stop printing\n     */\n\n\n    collectActivations(target, event) {\n      if (!this.isPrinting || this.isPrintingBeforeAfterEvent) {\n        if (!this.isPrintingBeforeAfterEvent) {\n          // Only clear deactivations if we aren't printing from a `beforeprint` event.\n          // Otherwise, this will clear before `stopPrinting()` is called to restore\n          // the pre-Print Activations.\n          this.deactivations = [];\n          return;\n        }\n\n        if (!event.matches) {\n          const bp = this.breakpoints.findByQuery(event.mediaQuery); // Deactivating a breakpoint\n\n          if (bp) {\n            const hasFormerBp = this.formerActivations && this.formerActivations.includes(bp);\n            const wasActivated = !this.formerActivations && target.activatedBreakpoints.includes(bp);\n            const shouldDeactivate = hasFormerBp || wasActivated;\n\n            if (shouldDeactivate) {\n              this.deactivations.push(bp);\n              this.deactivations.sort(sortDescendingPriority);\n            }\n          }\n        }\n      }\n    }\n    /** Teardown logic for the service. */\n\n\n    ngOnDestroy() {\n      if (this._document.defaultView) {\n        this.beforePrintEventListeners.forEach(l => this._document.defaultView.removeEventListener('beforeprint', l));\n        this.afterPrintEventListeners.forEach(l => this._document.defaultView.removeEventListener('afterprint', l));\n      }\n    }\n\n  }\n\n  PrintHook.ɵfac = function PrintHook_Factory(t) {\n    return new (t || PrintHook)(i0.ɵɵinject(BreakPointRegistry), i0.ɵɵinject(LAYOUT_CONFIG), i0.ɵɵinject(DOCUMENT));\n  };\n\n  PrintHook.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PrintHook,\n    factory: PrintHook.ɵfac,\n    providedIn: 'root'\n  });\n  return PrintHook;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})(); // ************************************************************************\n// Internal Utility class 'PrintQueue'\n// ************************************************************************\n\n/**\n * Utility class to manage print breakpoints + activatedBreakpoints\n * with correct sorting WHILE printing\n */\n\n\nclass PrintQueue {\n  constructor() {\n    /** Sorted queue with prioritized print breakpoints */\n    this.printBreakpoints = [];\n  }\n\n  addPrintBreakpoints(bpList) {\n    bpList.push(BREAKPOINT_PRINT);\n    bpList.sort(sortDescendingPriority);\n    bpList.forEach(bp => this.addBreakpoint(bp));\n    return this.printBreakpoints;\n  }\n  /** Add Print breakpoint to queue */\n\n\n  addBreakpoint(bp) {\n    if (!!bp) {\n      const bpInList = this.printBreakpoints.find(it => it.mediaQuery === bp.mediaQuery);\n\n      if (bpInList === undefined) {\n        // If this is a `printAlias` breakpoint, then append. If a true 'print' breakpoint,\n        // register as highest priority in the queue\n        this.printBreakpoints = isPrintBreakPoint(bp) ? [bp, ...this.printBreakpoints] : [...this.printBreakpoints, bp];\n      }\n    }\n  }\n  /** Restore original activated breakpoints and clear internal caches */\n\n\n  clear() {\n    this.printBreakpoints = [];\n  }\n\n} // ************************************************************************\n// Internal Utility methods\n// ************************************************************************\n\n/** Only support intercept queueing if the Breakpoint is a print @media query */\n\n\nfunction isPrintBreakPoint(bp) {\n  var _a;\n\n  return (_a = bp === null || bp === void 0 ? void 0 : bp.mediaQuery.startsWith(PRINT)) !== null && _a !== void 0 ? _a : false;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * MediaMarshaller - register responsive values from directives and\n *                   trigger them based on media query events\n */\n\n\nlet MediaMarshaller = /*#__PURE__*/(() => {\n  class MediaMarshaller {\n    constructor(matchMedia, breakpoints, hook) {\n      this.matchMedia = matchMedia;\n      this.breakpoints = breakpoints;\n      this.hook = hook;\n      this._useFallbacks = true;\n      this._activatedBreakpoints = [];\n      this.elementMap = new Map();\n      this.elementKeyMap = new WeakMap();\n      this.watcherMap = new WeakMap(); // special triggers to update elements\n\n      this.updateMap = new WeakMap(); // callback functions to update styles\n\n      this.clearMap = new WeakMap(); // callback functions to clear styles\n\n      this.subject = new Subject();\n      this.observeActivations();\n    }\n\n    get activatedAlias() {\n      var _a, _b;\n\n      return (_b = (_a = this.activatedBreakpoints[0]) === null || _a === void 0 ? void 0 : _a.alias) !== null && _b !== void 0 ? _b : '';\n    }\n\n    set activatedBreakpoints(bps) {\n      this._activatedBreakpoints = [...bps];\n    }\n\n    get activatedBreakpoints() {\n      return [...this._activatedBreakpoints];\n    }\n\n    set useFallbacks(value) {\n      this._useFallbacks = value;\n    }\n    /**\n     * Update styles on breakpoint activates or deactivates\n     * @param mc\n     */\n\n\n    onMediaChange(mc) {\n      const bp = this.findByQuery(mc.mediaQuery);\n\n      if (bp) {\n        mc = mergeAlias(mc, bp);\n        const bpIndex = this.activatedBreakpoints.indexOf(bp);\n\n        if (mc.matches && bpIndex === -1) {\n          this._activatedBreakpoints.push(bp);\n\n          this._activatedBreakpoints.sort(sortDescendingPriority);\n\n          this.updateStyles();\n        } else if (!mc.matches && bpIndex !== -1) {\n          // Remove the breakpoint when it's deactivated\n          this._activatedBreakpoints.splice(bpIndex, 1);\n\n          this._activatedBreakpoints.sort(sortDescendingPriority);\n\n          this.updateStyles();\n        }\n      }\n    }\n    /**\n     * initialize the marshaller with necessary elements for delegation on an element\n     * @param element\n     * @param key\n     * @param updateFn optional callback so that custom bp directives don't have to re-provide this\n     * @param clearFn optional callback so that custom bp directives don't have to re-provide this\n     * @param extraTriggers other triggers to force style updates (e.g. layout, directionality, etc)\n     */\n\n\n    init(element, key, updateFn, clearFn, extraTriggers = []) {\n      initBuilderMap(this.updateMap, element, key, updateFn);\n      initBuilderMap(this.clearMap, element, key, clearFn);\n      this.buildElementKeyMap(element, key);\n      this.watchExtraTriggers(element, key, extraTriggers);\n    }\n    /**\n     * get the value for an element and key and optionally a given breakpoint\n     * @param element\n     * @param key\n     * @param bp\n     */\n\n\n    getValue(element, key, bp) {\n      const bpMap = this.elementMap.get(element);\n\n      if (bpMap) {\n        const values = bp !== undefined ? bpMap.get(bp) : this.getActivatedValues(bpMap, key);\n\n        if (values) {\n          return values.get(key);\n        }\n      }\n\n      return undefined;\n    }\n    /**\n     * whether the element has values for a given key\n     * @param element\n     * @param key\n     */\n\n\n    hasValue(element, key) {\n      const bpMap = this.elementMap.get(element);\n\n      if (bpMap) {\n        const values = this.getActivatedValues(bpMap, key);\n\n        if (values) {\n          return values.get(key) !== undefined || false;\n        }\n      }\n\n      return false;\n    }\n    /**\n     * Set the value for an input on a directive\n     * @param element the element in question\n     * @param key the type of the directive (e.g. flex, layout-gap, etc)\n     * @param bp the breakpoint suffix (empty string = default)\n     * @param val the value for the breakpoint\n     */\n\n\n    setValue(element, key, val, bp) {\n      var _a;\n\n      let bpMap = this.elementMap.get(element);\n\n      if (!bpMap) {\n        bpMap = new Map().set(bp, new Map().set(key, val));\n        this.elementMap.set(element, bpMap);\n      } else {\n        const values = ((_a = bpMap.get(bp)) !== null && _a !== void 0 ? _a : new Map()).set(key, val);\n        bpMap.set(bp, values);\n        this.elementMap.set(element, bpMap);\n      }\n\n      const value = this.getValue(element, key);\n\n      if (value !== undefined) {\n        this.updateElement(element, key, value);\n      }\n    }\n    /** Track element value changes for a specific key */\n\n\n    trackValue(element, key) {\n      return this.subject.asObservable().pipe(filter(v => v.element === element && v.key === key));\n    }\n    /** update all styles for all elements on the current breakpoint */\n\n\n    updateStyles() {\n      this.elementMap.forEach((bpMap, el) => {\n        const keyMap = new Set(this.elementKeyMap.get(el));\n        let valueMap = this.getActivatedValues(bpMap);\n\n        if (valueMap) {\n          valueMap.forEach((v, k) => {\n            this.updateElement(el, k, v);\n            keyMap.delete(k);\n          });\n        }\n\n        keyMap.forEach(k => {\n          valueMap = this.getActivatedValues(bpMap, k);\n\n          if (valueMap) {\n            const value = valueMap.get(k);\n            this.updateElement(el, k, value);\n          } else {\n            this.clearElement(el, k);\n          }\n        });\n      });\n    }\n    /**\n     * clear the styles for a given element\n     * @param element\n     * @param key\n     */\n\n\n    clearElement(element, key) {\n      const builders = this.clearMap.get(element);\n\n      if (builders) {\n        const clearFn = builders.get(key);\n\n        if (!!clearFn) {\n          clearFn();\n          this.subject.next({\n            element,\n            key,\n            value: ''\n          });\n        }\n      }\n    }\n    /**\n     * update a given element with the activated values for a given key\n     * @param element\n     * @param key\n     * @param value\n     */\n\n\n    updateElement(element, key, value) {\n      const builders = this.updateMap.get(element);\n\n      if (builders) {\n        const updateFn = builders.get(key);\n\n        if (!!updateFn) {\n          updateFn(value);\n          this.subject.next({\n            element,\n            key,\n            value\n          });\n        }\n      }\n    }\n    /**\n     * release all references to a given element\n     * @param element\n     */\n\n\n    releaseElement(element) {\n      const watcherMap = this.watcherMap.get(element);\n\n      if (watcherMap) {\n        watcherMap.forEach(s => s.unsubscribe());\n        this.watcherMap.delete(element);\n      }\n\n      const elementMap = this.elementMap.get(element);\n\n      if (elementMap) {\n        elementMap.forEach((_, s) => elementMap.delete(s));\n        this.elementMap.delete(element);\n      }\n    }\n    /**\n     * trigger an update for a given element and key (e.g. layout)\n     * @param element\n     * @param key\n     */\n\n\n    triggerUpdate(element, key) {\n      const bpMap = this.elementMap.get(element);\n\n      if (bpMap) {\n        const valueMap = this.getActivatedValues(bpMap, key);\n\n        if (valueMap) {\n          if (key) {\n            this.updateElement(element, key, valueMap.get(key));\n          } else {\n            valueMap.forEach((v, k) => this.updateElement(element, k, v));\n          }\n        }\n      }\n    }\n    /** Cross-reference for HTMLElement with directive key */\n\n\n    buildElementKeyMap(element, key) {\n      let keyMap = this.elementKeyMap.get(element);\n\n      if (!keyMap) {\n        keyMap = new Set();\n        this.elementKeyMap.set(element, keyMap);\n      }\n\n      keyMap.add(key);\n    }\n    /**\n     * Other triggers that should force style updates:\n     * - directionality\n     * - layout changes\n     * - mutationobserver updates\n     */\n\n\n    watchExtraTriggers(element, key, triggers) {\n      if (triggers && triggers.length) {\n        let watchers = this.watcherMap.get(element);\n\n        if (!watchers) {\n          watchers = new Map();\n          this.watcherMap.set(element, watchers);\n        }\n\n        const subscription = watchers.get(key);\n\n        if (!subscription) {\n          const newSubscription = merge(...triggers).subscribe(() => {\n            const currentValue = this.getValue(element, key);\n            this.updateElement(element, key, currentValue);\n          });\n          watchers.set(key, newSubscription);\n        }\n      }\n    }\n    /** Breakpoint locator by mediaQuery */\n\n\n    findByQuery(query) {\n      return this.breakpoints.findByQuery(query);\n    }\n    /**\n     * get the fallback breakpoint for a given element, starting with the current breakpoint\n     * @param bpMap\n     * @param key\n     */\n\n\n    getActivatedValues(bpMap, key) {\n      for (let i = 0; i < this.activatedBreakpoints.length; i++) {\n        const activatedBp = this.activatedBreakpoints[i];\n        const valueMap = bpMap.get(activatedBp.alias);\n\n        if (valueMap) {\n          if (key === undefined || valueMap.has(key) && valueMap.get(key) != null) {\n            return valueMap;\n          }\n        }\n      } // On the server, we explicitly have an \"all\" section filled in to begin with.\n      // So we don't need to aggressively find a fallback if no explicit value exists.\n\n\n      if (!this._useFallbacks) {\n        return undefined;\n      }\n\n      const lastHope = bpMap.get('');\n      return key === undefined || lastHope && lastHope.has(key) ? lastHope : undefined;\n    }\n    /**\n     * Watch for mediaQuery breakpoint activations\n     */\n\n\n    observeActivations() {\n      const queries = this.breakpoints.items.map(bp => bp.mediaQuery);\n      this.hook.registerBeforeAfterPrintHooks(this);\n      this.matchMedia.observe(this.hook.withPrintQuery(queries)).pipe(tap(this.hook.interceptEvents(this)), filter(this.hook.blockPropagation())).subscribe(this.onMediaChange.bind(this));\n    }\n\n  }\n\n  MediaMarshaller.ɵfac = function MediaMarshaller_Factory(t) {\n    return new (t || MediaMarshaller)(i0.ɵɵinject(MatchMedia), i0.ɵɵinject(BreakPointRegistry), i0.ɵɵinject(PrintHook));\n  };\n\n  MediaMarshaller.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MediaMarshaller,\n    factory: MediaMarshaller.ɵfac,\n    providedIn: 'root'\n  });\n  return MediaMarshaller;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction initBuilderMap(map, element, key, input) {\n  var _a;\n\n  if (input !== undefined) {\n    const oldMap = (_a = map.get(element)) !== null && _a !== void 0 ? _a : new Map();\n    oldMap.set(key, input);\n    map.set(element, oldMap);\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet BaseDirective2 = /*#__PURE__*/(() => {\n  class BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal) {\n      this.elementRef = elementRef;\n      this.styleBuilder = styleBuilder;\n      this.styler = styler;\n      this.marshal = marshal;\n      this.DIRECTIVE_KEY = '';\n      this.inputs = [];\n      /** The most recently used styles for the builder */\n\n      this.mru = {};\n      this.destroySubject = new Subject();\n      /** Cache map for style computation */\n\n      this.styleCache = new Map();\n    }\n    /** Access to host element's parent DOM node */\n\n\n    get parentElement() {\n      return this.elementRef.nativeElement.parentElement;\n    }\n    /** Access to the HTMLElement for the directive */\n\n\n    get nativeElement() {\n      return this.elementRef.nativeElement;\n    }\n    /** Access to the activated value for the directive */\n\n\n    get activatedValue() {\n      return this.marshal.getValue(this.nativeElement, this.DIRECTIVE_KEY);\n    }\n\n    set activatedValue(value) {\n      this.marshal.setValue(this.nativeElement, this.DIRECTIVE_KEY, value, this.marshal.activatedAlias);\n    }\n    /** For @Input changes */\n\n\n    ngOnChanges(changes) {\n      Object.keys(changes).forEach(key => {\n        if (this.inputs.indexOf(key) !== -1) {\n          const bp = key.split('.').slice(1).join('.');\n          const val = changes[key].currentValue;\n          this.setValue(val, bp);\n        }\n      });\n    }\n\n    ngOnDestroy() {\n      this.destroySubject.next();\n      this.destroySubject.complete();\n      this.marshal.releaseElement(this.nativeElement);\n    }\n    /** Register with central marshaller service */\n\n\n    init(extraTriggers = []) {\n      this.marshal.init(this.elementRef.nativeElement, this.DIRECTIVE_KEY, this.updateWithValue.bind(this), this.clearStyles.bind(this), extraTriggers);\n    }\n    /** Add styles to the element using predefined style builder */\n\n\n    addStyles(input, parent) {\n      const builder = this.styleBuilder;\n      const useCache = builder.shouldCache;\n      let genStyles = this.styleCache.get(input);\n\n      if (!genStyles || !useCache) {\n        genStyles = builder.buildStyles(input, parent);\n\n        if (useCache) {\n          this.styleCache.set(input, genStyles);\n        }\n      }\n\n      this.mru = Object.assign({}, genStyles);\n      this.applyStyleToElement(genStyles);\n      builder.sideEffect(input, genStyles, parent);\n    }\n    /** Remove generated styles from an element using predefined style builder */\n\n\n    clearStyles() {\n      Object.keys(this.mru).forEach(k => {\n        this.mru[k] = '';\n      });\n      this.applyStyleToElement(this.mru);\n      this.mru = {};\n      this.currentValue = undefined;\n    }\n    /** Force trigger style updates on DOM element */\n\n\n    triggerUpdate() {\n      this.marshal.triggerUpdate(this.nativeElement, this.DIRECTIVE_KEY);\n    }\n    /**\n     * Determine the DOM element's Flexbox flow (flex-direction).\n     *\n     * Check inline style first then check computed (stylesheet) style.\n     * And optionally add the flow value to element's inline style.\n     */\n\n\n    getFlexFlowDirection(target, addIfMissing = false) {\n      if (target) {\n        const [value, hasInlineValue] = this.styler.getFlowDirection(target);\n\n        if (!hasInlineValue && addIfMissing) {\n          const style = buildLayoutCSS(value);\n          const elements = [target];\n          this.styler.applyStyleToElements(style, elements);\n        }\n\n        return value.trim();\n      }\n\n      return 'row';\n    }\n\n    hasWrap(target) {\n      return this.styler.hasWrap(target);\n    }\n    /** Applies styles given via string pair or object map to the directive element */\n\n\n    applyStyleToElement(style, value, element = this.nativeElement) {\n      this.styler.applyStyleToElement(element, style, value);\n    }\n\n    setValue(val, bp) {\n      this.marshal.setValue(this.nativeElement, this.DIRECTIVE_KEY, val, bp);\n    }\n\n    updateWithValue(input) {\n      if (this.currentValue !== input) {\n        this.addStyles(input);\n        this.currentValue = input;\n      }\n    }\n\n  }\n\n  BaseDirective2.ɵfac = function BaseDirective2_Factory(t) {\n    return new (t || BaseDirective2)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(StyleBuilder), i0.ɵɵdirectiveInject(StyleUtils), i0.ɵɵdirectiveInject(MediaMarshaller));\n  };\n\n  BaseDirective2.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BaseDirective2,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return BaseDirective2;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * MockMatchMedia mocks calls to the Window API matchMedia with a build of a simulated\n * MockMediaQueryListener. Methods are available to simulate an activation of a mediaQuery\n * range and to clearAll mediaQuery listeners.\n */\n\n\nlet MockMatchMedia = /*#__PURE__*/(() => {\n  class MockMatchMedia extends MatchMedia {\n    constructor(_zone, _platformId, _document, _breakpoints) {\n      super(_zone, _platformId, _document);\n      this._breakpoints = _breakpoints;\n      this.autoRegisterQueries = true; // Used for testing BreakPoint registrations\n\n      this.useOverlaps = false; // Allow fallback to overlapping mediaQueries\n    }\n    /** Easy method to clear all listeners for all mediaQueries */\n\n\n    clearAll() {\n      this.registry.forEach(mql => {\n        mql.destroy();\n      });\n      this.registry.clear();\n      this.useOverlaps = false;\n    }\n    /** Feature to support manual, simulated activation of a mediaQuery. */\n\n\n    activate(mediaQuery, useOverlaps = this.useOverlaps) {\n      mediaQuery = this._validateQuery(mediaQuery);\n\n      if (useOverlaps || !this.isActive(mediaQuery)) {\n        this._deactivateAll();\n\n        this._registerMediaQuery(mediaQuery);\n\n        this._activateWithOverlaps(mediaQuery, useOverlaps);\n      }\n\n      return this.hasActivated;\n    }\n    /** Converts an optional mediaQuery alias to a specific, valid mediaQuery */\n\n\n    _validateQuery(queryOrAlias) {\n      var _a;\n\n      const bp = this._breakpoints.findByAlias(queryOrAlias);\n\n      return (_a = bp === null || bp === void 0 ? void 0 : bp.mediaQuery) !== null && _a !== void 0 ? _a : queryOrAlias;\n    }\n    /**\n     * Manually onMediaChange any overlapping mediaQueries to simulate\n     * similar functionality in the window.matchMedia()\n     */\n\n\n    _activateWithOverlaps(mediaQuery, useOverlaps) {\n      var _a;\n\n      if (useOverlaps) {\n        const bp = this._breakpoints.findByQuery(mediaQuery);\n\n        const alias = (_a = bp === null || bp === void 0 ? void 0 : bp.alias) !== null && _a !== void 0 ? _a : 'unknown'; // Simulate activation of overlapping lt-<XXX> ranges\n\n        switch (alias) {\n          case 'lg':\n            this._activateByAlias(['lt-xl']);\n\n            break;\n\n          case 'md':\n            this._activateByAlias(['lt-xl', 'lt-lg']);\n\n            break;\n\n          case 'sm':\n            this._activateByAlias(['lt-xl', 'lt-lg', 'lt-md']);\n\n            break;\n\n          case 'xs':\n            this._activateByAlias(['lt-xl', 'lt-lg', 'lt-md', 'lt-sm']);\n\n            break;\n        } // Simulate activation of overlapping gt-<xxxx> mediaQuery ranges\n\n\n        switch (alias) {\n          case 'xl':\n            this._activateByAlias(['gt-lg', 'gt-md', 'gt-sm', 'gt-xs']);\n\n            break;\n\n          case 'lg':\n            this._activateByAlias(['gt-md', 'gt-sm', 'gt-xs']);\n\n            break;\n\n          case 'md':\n            this._activateByAlias(['gt-sm', 'gt-xs']);\n\n            break;\n\n          case 'sm':\n            this._activateByAlias(['gt-xs']);\n\n            break;\n        }\n      } // Activate last since the responsiveActivation is watching *this* mediaQuery\n\n\n      return this._activateByQuery(mediaQuery);\n    }\n    /**\n     *\n     */\n\n\n    _activateByAlias(aliases) {\n      const activate = alias => {\n        var _a;\n\n        const bp = this._breakpoints.findByAlias(alias);\n\n        this._activateByQuery((_a = bp === null || bp === void 0 ? void 0 : bp.mediaQuery) !== null && _a !== void 0 ? _a : alias);\n      };\n\n      aliases.forEach(activate);\n    }\n    /**\n     *\n     */\n\n\n    _activateByQuery(mediaQuery) {\n      if (!this.registry.has(mediaQuery) && this.autoRegisterQueries) {\n        this._registerMediaQuery(mediaQuery);\n      }\n\n      const mql = this.registry.get(mediaQuery);\n\n      if (mql && !this.isActive(mediaQuery)) {\n        this.registry.set(mediaQuery, mql.activate());\n      }\n\n      return this.hasActivated;\n    }\n    /** Deactivate all current MQLs and reset the buffer */\n\n\n    _deactivateAll() {\n      this.registry.forEach(it => {\n        it.deactivate();\n      });\n      return this;\n    }\n    /** Insure the mediaQuery is registered with MatchMedia */\n\n\n    _registerMediaQuery(mediaQuery) {\n      if (!this.registry.has(mediaQuery) && this.autoRegisterQueries) {\n        this.registerQuery(mediaQuery);\n      }\n    }\n    /**\n     * Call window.matchMedia() to build a MediaQueryList; which\n     * supports 0..n listeners for activation/deactivation\n     */\n\n\n    buildMQL(query) {\n      return new MockMediaQueryList(query);\n    }\n\n    get hasActivated() {\n      return this.activations.length > 0;\n    }\n\n  }\n\n  MockMatchMedia.ɵfac = function MockMatchMedia_Factory(t) {\n    return new (t || MockMatchMedia)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(BreakPointRegistry));\n  };\n\n  MockMatchMedia.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MockMatchMedia,\n    factory: MockMatchMedia.ɵfac\n  });\n  return MockMatchMedia;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Special internal class to simulate a MediaQueryList and\n * - supports manual activation to simulate mediaQuery matching\n * - manages listeners\n */\n\n\nclass MockMediaQueryList {\n  constructor(_mediaQuery) {\n    this._mediaQuery = _mediaQuery;\n    this._isActive = false;\n    this._listeners = [];\n    this.onchange = null;\n  }\n\n  get matches() {\n    return this._isActive;\n  }\n\n  get media() {\n    return this._mediaQuery;\n  }\n  /**\n   * Destroy the current list by deactivating the\n   * listeners and clearing the internal list\n   */\n\n\n  destroy() {\n    this.deactivate();\n    this._listeners = [];\n  }\n  /** Notify all listeners that 'matches === TRUE' */\n\n\n  activate() {\n    if (!this._isActive) {\n      this._isActive = true;\n\n      this._listeners.forEach(callback => {\n        const cb = callback;\n        cb.call(this, {\n          matches: this.matches,\n          media: this.media\n        });\n      });\n    }\n\n    return this;\n  }\n  /** Notify all listeners that 'matches === false' */\n\n\n  deactivate() {\n    if (this._isActive) {\n      this._isActive = false;\n\n      this._listeners.forEach(callback => {\n        const cb = callback;\n        cb.call(this, {\n          matches: this.matches,\n          media: this.media\n        });\n      });\n    }\n\n    return this;\n  }\n  /** Add a listener to our internal list to activate later */\n\n\n  addListener(listener) {\n    if (this._listeners.indexOf(listener) === -1) {\n      this._listeners.push(listener);\n    }\n\n    if (this._isActive) {\n      const cb = listener;\n      cb.call(this, {\n        matches: this.matches,\n        media: this.media\n      });\n    }\n  }\n  /** Don't need to remove listeners in the testing environment */\n\n\n  removeListener(_) {}\n\n  addEventListener(_, __, ___) {}\n\n  removeEventListener(_, __, ___) {}\n\n  dispatchEvent(_) {\n    return false;\n  }\n\n}\n/**\n * Pre-configured provider for MockMatchMedia\n */\n\n\nconst MockMatchMediaProvider = {\n  provide: MatchMedia,\n  useClass: MockMatchMedia\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Wraps the provided value in an array, unless the provided value is an array. */\n\nfunction coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * MediaObserver enables applications to listen for 1..n mediaQuery activations and to determine\n * if a mediaQuery is currently activated.\n *\n * Since a breakpoint change will first deactivate 1...n mediaQueries and then possibly activate\n * 1..n mediaQueries, the MediaObserver will debounce notifications and report ALL *activations*\n * in 1 event notification. The reported activations will be sorted in descending priority order.\n *\n * This class uses the BreakPoint Registry to inject alias information into the raw MediaChange\n * notification. For custom mediaQuery notifications, alias information will not be injected and\n * those fields will be ''.\n *\n * Note: Developers should note that only mediaChange activations (not de-activations)\n *       are announced by the MediaObserver.\n *\n *  @usage\n *\n *  // RxJS\n *  import { filter } from 'rxjs/operators';\n *  import { MediaObserver } from '@angular/flex-layout';\n *\n *  @Component({ ... })\n *  export class AppComponent {\n *    status: string = '';\n *\n *    constructor(mediaObserver: MediaObserver) {\n *      const media$ = mediaObserver.asObservable().pipe(\n *        filter((changes: MediaChange[]) => true)   // silly noop filter\n *      );\n *\n *      media$.subscribe((changes: MediaChange[]) => {\n *        let status = '';\n *        changes.forEach( change => {\n *          status += `'${change.mqAlias}' = (${change.mediaQuery}) <br/>` ;\n *        });\n *        this.status = status;\n *     });\n *\n *    }\n *  }\n */\n\n\nlet MediaObserver = /*#__PURE__*/(() => {\n  class MediaObserver {\n    constructor(breakpoints, matchMedia, hook) {\n      this.breakpoints = breakpoints;\n      this.matchMedia = matchMedia;\n      this.hook = hook;\n      /** Filter MediaChange notifications for overlapping breakpoints */\n\n      this.filterOverlaps = false;\n      this.destroyed$ = new Subject();\n      this._media$ = this.watchActivations();\n      this.media$ = this._media$.pipe(filter(changes => changes.length > 0), map(changes => changes[0]));\n    }\n    /**\n     * Completes the active subject, signalling to all complete for all\n     * MediaObserver subscribers\n     */\n\n\n    ngOnDestroy() {\n      this.destroyed$.next();\n      this.destroyed$.complete();\n    } // ************************************************\n    // Public Methods\n    // ************************************************\n\n    /**\n     * Observe changes to current activation 'list'\n     */\n\n\n    asObservable() {\n      return this._media$;\n    }\n    /**\n     * Allow programmatic query to determine if one or more media query/alias match\n     * the current viewport size.\n     * @param value One or more media queries (or aliases) to check.\n     * @returns Whether any of the media queries match.\n     */\n\n\n    isActive(value) {\n      const aliases = splitQueries(coerceArray(value));\n      return aliases.some(alias => {\n        const query = toMediaQuery(alias, this.breakpoints);\n        return query !== null && this.matchMedia.isActive(query);\n      });\n    } // ************************************************\n    // Internal Methods\n    // ************************************************\n\n    /**\n     * Register all the mediaQueries registered in the BreakPointRegistry\n     * This is needed so subscribers can be auto-notified of all standard, registered\n     * mediaQuery activations\n     */\n\n\n    watchActivations() {\n      const queries = this.breakpoints.items.map(bp => bp.mediaQuery);\n      return this.buildObservable(queries);\n    }\n    /**\n     * Only pass/announce activations (not de-activations)\n     *\n     * Since multiple-mediaQueries can be activation in a cycle,\n     * gather all current activations into a single list of changes to observers\n     *\n     * Inject associated (if any) alias information into the MediaChange event\n     * - Exclude mediaQuery activations for overlapping mQs. List bounded mQ ranges only\n     * - Exclude print activations that do not have an associated mediaQuery\n     *\n     * NOTE: the raw MediaChange events [from MatchMedia] do not\n     *       contain important alias information; as such this info\n     *       must be injected into the MediaChange\n     */\n\n\n    buildObservable(mqList) {\n      const hasChanges = changes => {\n        const isValidQuery = change => change.mediaQuery.length > 0;\n\n        return changes.filter(isValidQuery).length > 0;\n      };\n\n      const excludeOverlaps = changes => {\n        return !this.filterOverlaps ? changes : changes.filter(change => {\n          var _a;\n\n          const bp = this.breakpoints.findByQuery(change.mediaQuery);\n          return (_a = bp === null || bp === void 0 ? void 0 : bp.overlapping) !== null && _a !== void 0 ? _a : true;\n        });\n      };\n\n      const ignoreDuplicates = (previous, current) => {\n        if (previous.length !== current.length) {\n          return false;\n        }\n\n        const previousMqs = previous.map(mc => mc.mediaQuery);\n        const currentMqs = new Set(current.map(mc => mc.mediaQuery));\n        const difference = new Set(previousMqs.filter(mq => !currentMqs.has(mq)));\n        return difference.size === 0;\n      };\n      /**\n       */\n\n\n      return this.matchMedia.observe(this.hook.withPrintQuery(mqList)).pipe(filter(change => change.matches), debounceTime(0, asapScheduler), switchMap(_ => of(this.findAllActivations())), map(excludeOverlaps), filter(hasChanges), distinctUntilChanged(ignoreDuplicates), takeUntil(this.destroyed$));\n    }\n    /**\n     * Find all current activations and prepare single list of activations\n     * sorted by descending priority.\n     */\n\n\n    findAllActivations() {\n      const mergeMQAlias = change => {\n        const bp = this.breakpoints.findByQuery(change.mediaQuery);\n        return mergeAlias(change, bp);\n      };\n\n      const replaceWithPrintAlias = change => {\n        return this.hook.isPrintEvent(change) ? this.hook.updateEvent(change) : change;\n      };\n\n      return this.matchMedia.activations.map(query => new MediaChange(true, query)).map(replaceWithPrintAlias).map(mergeMQAlias).sort(sortDescendingPriority);\n    }\n\n  }\n\n  MediaObserver.ɵfac = function MediaObserver_Factory(t) {\n    return new (t || MediaObserver)(i0.ɵɵinject(BreakPointRegistry), i0.ɵɵinject(MatchMedia), i0.ɵɵinject(PrintHook));\n  };\n\n  MediaObserver.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MediaObserver,\n    factory: MediaObserver.ɵfac,\n    providedIn: 'root'\n  });\n  return MediaObserver;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Find associated breakpoint (if any)\n */\n\n\nfunction toMediaQuery(query, locator) {\n  var _a, _b;\n\n  const bp = (_a = locator.findByAlias(query)) !== null && _a !== void 0 ? _a : locator.findByQuery(query);\n  return (_b = bp === null || bp === void 0 ? void 0 : bp.mediaQuery) !== null && _b !== void 0 ? _b : null;\n}\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\n\n\nfunction splitQueries(queries) {\n  return queries.map(query => query.split(',')).reduce((a1, a2) => a1.concat(a2)).map(query => query.trim());\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Class\n */\n\n\nlet MediaTrigger = /*#__PURE__*/(() => {\n  class MediaTrigger {\n    constructor(breakpoints, matchMedia, layoutConfig, _platformId, _document) {\n      this.breakpoints = breakpoints;\n      this.matchMedia = matchMedia;\n      this.layoutConfig = layoutConfig;\n      this._platformId = _platformId;\n      this._document = _document;\n      this.hasCachedRegistryMatches = false;\n      this.originalActivations = [];\n      this.originalRegistry = new Map();\n    }\n    /**\n     * Manually activate range of breakpoints\n     * @param list array of mediaQuery or alias strings\n     */\n\n\n    activate(list) {\n      list = list.map(it => it.trim()); // trim queries\n\n      this.saveActivations();\n      this.deactivateAll();\n      this.setActivations(list);\n      this.prepareAutoRestore();\n    }\n    /**\n     * Restore original, 'real' breakpoints and emit events\n     * to trigger stream notification\n     */\n\n\n    restore() {\n      if (this.hasCachedRegistryMatches) {\n        const extractQuery = change => change.mediaQuery;\n\n        const list = this.originalActivations.map(extractQuery);\n\n        try {\n          this.deactivateAll();\n          this.restoreRegistryMatches();\n          this.setActivations(list);\n        } finally {\n          this.originalActivations = [];\n\n          if (this.resizeSubscription) {\n            this.resizeSubscription.unsubscribe();\n          }\n        }\n      }\n    } // ************************************************\n    // Internal Methods\n    // ************************************************\n\n    /**\n     * Whenever window resizes, immediately auto-restore original\n     * activations (if we are simulating activations)\n     */\n\n\n    prepareAutoRestore() {\n      const isBrowser = isPlatformBrowser(this._platformId) && this._document;\n\n      const enableAutoRestore = isBrowser && this.layoutConfig.mediaTriggerAutoRestore;\n\n      if (enableAutoRestore) {\n        const resize$ = fromEvent(window, 'resize').pipe(take(1));\n        this.resizeSubscription = resize$.subscribe(this.restore.bind(this));\n      }\n    }\n    /**\n     * Notify all matchMedia subscribers of de-activations\n     *\n     * Note: we must force 'matches' updates for\n     *       future matchMedia::activation lookups\n     */\n\n\n    deactivateAll() {\n      const list = this.currentActivations;\n      this.forceRegistryMatches(list, false);\n      this.simulateMediaChanges(list, false);\n    }\n    /**\n     * Cache current activations as sorted, prioritized list of MediaChanges\n     */\n\n\n    saveActivations() {\n      if (!this.hasCachedRegistryMatches) {\n        const toMediaChange = query => new MediaChange(true, query);\n\n        const mergeMQAlias = change => {\n          const bp = this.breakpoints.findByQuery(change.mediaQuery);\n          return mergeAlias(change, bp);\n        };\n\n        this.originalActivations = this.currentActivations.map(toMediaChange).map(mergeMQAlias).sort(sortDescendingPriority);\n        this.cacheRegistryMatches();\n      }\n    }\n    /**\n     * Force set manual activations for specified mediaQuery list\n     */\n\n\n    setActivations(list) {\n      if (!!this.originalRegistry) {\n        this.forceRegistryMatches(list, true);\n      }\n\n      this.simulateMediaChanges(list);\n    }\n    /**\n     * For specified mediaQuery list manually simulate activations or deactivations\n     */\n\n\n    simulateMediaChanges(queries, matches = true) {\n      const toMediaQuery = query => {\n        const locator = this.breakpoints;\n        const bp = locator.findByAlias(query) || locator.findByQuery(query);\n        return bp ? bp.mediaQuery : query;\n      };\n\n      const emitChangeEvent = query => this.emitChangeEvent(matches, query);\n\n      queries.map(toMediaQuery).forEach(emitChangeEvent);\n    }\n    /**\n     * Replace current registry with simulated registry...\n     * Note: this is required since MediaQueryList::matches is 'readOnly'\n     */\n\n\n    forceRegistryMatches(queries, matches) {\n      const registry = new Map();\n      queries.forEach(query => {\n        registry.set(query, {\n          matches\n        });\n      });\n      this.matchMedia.registry = registry;\n    }\n    /**\n     * Save current MatchMedia::registry items.\n     */\n\n\n    cacheRegistryMatches() {\n      const target = this.originalRegistry;\n      target.clear();\n      this.matchMedia.registry.forEach((value, key) => {\n        target.set(key, value);\n      });\n      this.hasCachedRegistryMatches = true;\n    }\n    /**\n     * Restore original, 'true' registry\n     */\n\n\n    restoreRegistryMatches() {\n      const target = this.matchMedia.registry;\n      target.clear();\n      this.originalRegistry.forEach((value, key) => {\n        target.set(key, value);\n      });\n      this.originalRegistry.clear();\n      this.hasCachedRegistryMatches = false;\n    }\n    /**\n     * Manually emit a MediaChange event via the MatchMedia to MediaMarshaller and MediaObserver\n     */\n\n\n    emitChangeEvent(matches, query) {\n      this.matchMedia.source.next(new MediaChange(matches, query));\n    }\n\n    get currentActivations() {\n      return this.matchMedia.activations;\n    }\n\n  }\n\n  MediaTrigger.ɵfac = function MediaTrigger_Factory(t) {\n    return new (t || MediaTrigger)(i0.ɵɵinject(BreakPointRegistry), i0.ɵɵinject(MatchMedia), i0.ɵɵinject(LAYOUT_CONFIG), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(DOCUMENT));\n  };\n\n  MediaTrigger.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MediaTrigger,\n    factory: MediaTrigger.ɵfac,\n    providedIn: 'root'\n  });\n  return MediaTrigger;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n* The flex API permits 3 or 1 parts of the value:\n*    - `flex-grow flex-shrink flex-basis`, or\n*    - `flex-basis`\n*/\n\n\nfunction validateBasis(basis, grow = '1', shrink = '1') {\n  let parts = [grow, shrink, basis];\n  let j = basis.indexOf('calc');\n\n  if (j > 0) {\n    parts[2] = _validateCalcValue(basis.substring(j).trim());\n    let matches = basis.substr(0, j).trim().split(' ');\n\n    if (matches.length == 2) {\n      parts[0] = matches[0];\n      parts[1] = matches[1];\n    }\n  } else if (j == 0) {\n    parts[2] = _validateCalcValue(basis.trim());\n  } else {\n    let matches = basis.split(' ');\n    parts = matches.length === 3 ? matches : [grow, shrink, basis];\n  }\n\n  return parts;\n}\n/**\n * Calc expressions require whitespace before & after any expression operators\n * This is a simple, crude whitespace padding solution.\n *   - '3 3 calc(15em + 20px)'\n *   - calc(100% / 7 * 2)\n *   - 'calc(15em + 20px)'\n *   - 'calc(15em+20px)'\n *   - '37px'\n *   = '43%'\n */\n\n\nfunction _validateCalcValue(calc) {\n  return calc.replace(/[\\s]/g, '').replace(/[\\/\\*\\+\\-]/g, ' $& ');\n}\n\nconst MULTIPLIER_SUFFIX = 'x';\n\nfunction multiply(value, multiplier) {\n  if (multiplier === undefined) {\n    return value;\n  }\n\n  const transformValue = possibleValue => {\n    const numberValue = +possibleValue.slice(0, -MULTIPLIER_SUFFIX.length);\n\n    if (value.endsWith(MULTIPLIER_SUFFIX) && !isNaN(numberValue)) {\n      return `${numberValue * multiplier.value}${multiplier.unit}`;\n    }\n\n    return value;\n  };\n\n  return value.includes(' ') ? value.split(' ').map(transformValue).join(' ') : transformValue(value);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BREAKPOINT, BREAKPOINTS, BREAKPOINT_PRINT, BROWSER_PROVIDER, BaseDirective2, BreakPointRegistry, CLASS_NAME, CoreModule, DEFAULT_BREAKPOINTS, DEFAULT_CONFIG, LAYOUT_CONFIG, MediaChange, MediaMarshaller, MediaObserver, MediaTrigger, ORIENTATION_BREAKPOINTS, PrintHook, SERVER_TOKEN, ScreenTypes, StyleBuilder, StyleUtils, StylesheetMap, coerceArray, mergeAlias, removeStyles, sortAscendingPriority, sortDescendingPriority, validateBasis, MatchMedia as ɵMatchMedia, MockMatchMedia as ɵMockMatchMedia, MockMatchMediaProvider as ɵMockMatchMediaProvider, multiply as ɵmultiply }; //# sourceMappingURL=angular-flex-layout-core.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}