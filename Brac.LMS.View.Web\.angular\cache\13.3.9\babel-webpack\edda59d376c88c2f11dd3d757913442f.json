{"ast": null, "code": "import { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function skipWhile(predicate) {\n  return operate((source, subscriber) => {\n    let taking = false;\n    let index = 0;\n    source.subscribe(new OperatorSubscriber(subscriber, value => (taking || (taking = !predicate(value, index++))) && subscriber.next(value)));\n  });\n} //# sourceMappingURL=skipWhile.js.map", "map": null, "metadata": {}, "sourceType": "module"}