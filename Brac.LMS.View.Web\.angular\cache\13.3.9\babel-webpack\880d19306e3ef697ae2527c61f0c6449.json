{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Marathi [mr]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/kalehv\n//! author : <PERSON><PERSON><PERSON> : https://github.com/vnathalye\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var symbolMap = {\n    1: '१',\n    2: '२',\n    3: '३',\n    4: '४',\n    5: '५',\n    6: '६',\n    7: '७',\n    8: '८',\n    9: '९',\n    0: '०'\n  },\n      numberMap = {\n    '१': '1',\n    '२': '2',\n    '३': '3',\n    '४': '4',\n    '५': '5',\n    '६': '6',\n    '७': '7',\n    '८': '8',\n    '९': '9',\n    '०': '0'\n  };\n\n  function relativeTimeMr(number, withoutSuffix, string, isFuture) {\n    var output = '';\n\n    if (withoutSuffix) {\n      switch (string) {\n        case 's':\n          output = 'काही सेकंद';\n          break;\n\n        case 'ss':\n          output = '%d सेकंद';\n          break;\n\n        case 'm':\n          output = 'एक मिनिट';\n          break;\n\n        case 'mm':\n          output = '%d मिनिटे';\n          break;\n\n        case 'h':\n          output = 'एक तास';\n          break;\n\n        case 'hh':\n          output = '%d तास';\n          break;\n\n        case 'd':\n          output = 'एक दिवस';\n          break;\n\n        case 'dd':\n          output = '%d दिवस';\n          break;\n\n        case 'M':\n          output = 'एक महिना';\n          break;\n\n        case 'MM':\n          output = '%d महिने';\n          break;\n\n        case 'y':\n          output = 'एक वर्ष';\n          break;\n\n        case 'yy':\n          output = '%d वर्षे';\n          break;\n      }\n    } else {\n      switch (string) {\n        case 's':\n          output = 'काही सेकंदां';\n          break;\n\n        case 'ss':\n          output = '%d सेकंदां';\n          break;\n\n        case 'm':\n          output = 'एका मिनिटा';\n          break;\n\n        case 'mm':\n          output = '%d मिनिटां';\n          break;\n\n        case 'h':\n          output = 'एका तासा';\n          break;\n\n        case 'hh':\n          output = '%d तासां';\n          break;\n\n        case 'd':\n          output = 'एका दिवसा';\n          break;\n\n        case 'dd':\n          output = '%d दिवसां';\n          break;\n\n        case 'M':\n          output = 'एका महिन्या';\n          break;\n\n        case 'MM':\n          output = '%d महिन्यां';\n          break;\n\n        case 'y':\n          output = 'एका वर्षा';\n          break;\n\n        case 'yy':\n          output = '%d वर्षां';\n          break;\n      }\n    }\n\n    return output.replace(/%d/i, number);\n  }\n\n  var mr = moment.defineLocale('mr', {\n    months: 'जानेवारी_फेब्रुवारी_मार्च_एप्रिल_मे_जून_जुलै_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर'.split('_'),\n    monthsShort: 'जाने._फेब्रु._मार्च._एप्रि._मे._जून._जुलै._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'रविवार_सोमवार_मंगळवार_बुधवार_गुरूवार_शुक्रवार_शनिवार'.split('_'),\n    weekdaysShort: 'रवि_सोम_मंगळ_बुध_गुरू_शुक्र_शनि'.split('_'),\n    weekdaysMin: 'र_सो_मं_बु_गु_शु_श'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm वाजता',\n      LTS: 'A h:mm:ss वाजता',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm वाजता',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm वाजता'\n    },\n    calendar: {\n      sameDay: '[आज] LT',\n      nextDay: '[उद्या] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[काल] LT',\n      lastWeek: '[मागील] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%sमध्ये',\n      past: '%sपूर्वी',\n      s: relativeTimeMr,\n      ss: relativeTimeMr,\n      m: relativeTimeMr,\n      mm: relativeTimeMr,\n      h: relativeTimeMr,\n      hh: relativeTimeMr,\n      d: relativeTimeMr,\n      dd: relativeTimeMr,\n      M: relativeTimeMr,\n      MM: relativeTimeMr,\n      y: relativeTimeMr,\n      yy: relativeTimeMr\n    },\n    preparse: function (string) {\n      return string.replace(/[१२३४५६७८९०]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    meridiemParse: /पहाटे|सकाळी|दुपारी|सायंकाळी|रात्री/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n\n      if (meridiem === 'पहाटे' || meridiem === 'सकाळी') {\n        return hour;\n      } else if (meridiem === 'दुपारी' || meridiem === 'सायंकाळी' || meridiem === 'रात्री') {\n        return hour >= 12 ? hour : hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour >= 0 && hour < 6) {\n        return 'पहाटे';\n      } else if (hour < 12) {\n        return 'सकाळी';\n      } else if (hour < 17) {\n        return 'दुपारी';\n      } else if (hour < 20) {\n        return 'सायंकाळी';\n      } else {\n        return 'रात्री';\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n\n    }\n  });\n  return mr;\n});", "map": null, "metadata": {}, "sourceType": "script"}