{"ast": null, "code": "import { __decorate } from 'tslib';\nimport { Injectable, Input, Component, ViewEncapsulation, TemplateRef, ComponentRef, ComponentFactoryResolver, ChangeDetectorRef, ViewChild, ViewContainerRef, Renderer2, Directive, InjectionToken, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReplaySubject } from 'rxjs';\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/common';\nconst _c0 = [\"*\"];\nconst _c1 = [\"templateOutlet\"];\n\nfunction BlockUIContentComponent_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 6);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r2.message || ctx_r2.defaultMessage, \" \");\n  }\n}\n\nfunction BlockUIContentComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 3);\n    ɵngcc0.ɵɵelement(1, \"div\", 4);\n    ɵngcc0.ɵɵtemplate(2, BlockUIContentComponent_div_1_div_2_Template, 2, 1, \"div\", 5);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r0.message || ctx_r0.defaultMessage);\n  }\n}\n\nfunction BlockUIContentComponent_2_ng_template_0_Template(rf, ctx) {}\n\nfunction BlockUIContentComponent_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, BlockUIContentComponent_2_ng_template_0_Template, 0, 0, \"ng-template\", null, 7, ɵngcc0.ɵɵtemplateRefExtractor);\n  }\n}\n\nconst _c2 = function (a0) {\n  return {\n    \"active\": a0\n  };\n};\n\nlet BlockUIActions = /*#__PURE__*/(() => {\n  class BlockUIActions {}\n\n  BlockUIActions.START = 'start';\n  BlockUIActions.STOP = 'stop';\n  BlockUIActions.UPDATE = 'update';\n  BlockUIActions.RESET = 'reset';\n  BlockUIActions.RESET_GLOBAL = 'reset_global';\n  BlockUIActions.UNSUBSCRIBE = 'unsubscribe';\n  return BlockUIActions;\n})();\nconst BlockUIDefaultName = 'block-ui-main';\nlet BlockUIInstanceService = /*#__PURE__*/(() => {\n  let BlockUIInstanceService = class BlockUIInstanceService {\n    constructor() {\n      this.blockUISettings = {};\n      this.blockUIInstances = {};\n      this.blockUISubject = new ReplaySubject(1);\n      this.blockUIObservable = this.blockUISubject.asObservable();\n      this.blockUIObservable.subscribe(this.blockUIMiddleware.bind(this));\n    }\n\n    getSettings() {\n      return this.blockUISettings;\n    }\n\n    updateSettings(settings = {}) {\n      this.blockUISettings = Object.assign(Object.assign({}, this.blockUISettings), settings);\n    }\n\n    decorate(name = BlockUIDefaultName) {\n      const blockUI = {\n        name,\n        isActive: false,\n        blockCount: 0,\n        start: this.dispatch(this.blockUISubject, BlockUIActions.START, name),\n        update: this.dispatch(this.blockUISubject, BlockUIActions.UPDATE, name),\n        stop: this.dispatch(this.blockUISubject, BlockUIActions.STOP, name),\n        reset: this.dispatch(this.blockUISubject, BlockUIActions.RESET, name),\n        resetGlobal: this.dispatch(this.blockUISubject, BlockUIActions.RESET_GLOBAL, name),\n        unsubscribe: this.dispatch(this.blockUISubject, BlockUIActions.UNSUBSCRIBE, name)\n      };\n      this.blockUIInstances[name] = this.blockUIInstances[name] || blockUI;\n      return blockUI;\n    }\n\n    observe() {\n      return this.blockUIObservable;\n    }\n\n    clearInstance(instanceName) {\n      this.dispatch(this.blockUISubject, BlockUIActions.RESET, instanceName);\n    }\n\n    blockUIMiddleware({\n      action,\n      name\n    }) {\n      let isActive = null;\n\n      switch (action) {\n        case BlockUIActions.START:\n          isActive = true;\n          break;\n\n        case BlockUIActions.STOP:\n        case BlockUIActions.RESET:\n          isActive = false;\n          break;\n      }\n\n      if (isActive !== null) {\n        this.blockUIInstances[name].isActive = isActive;\n      }\n    }\n\n    dispatch(subject, action, name = BlockUIDefaultName) {\n      return message => {\n        subject.next({\n          name,\n          action,\n          message\n        });\n      };\n    }\n\n  };\n\n  BlockUIInstanceService.ɵfac = function BlockUIInstanceService_Factory(t) {\n    return new (t || BlockUIInstanceService)();\n  };\n\n  BlockUIInstanceService.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n    token: BlockUIInstanceService,\n    factory: function (t) {\n      return BlockUIInstanceService.ɵfac(t);\n    }\n  });\n  return BlockUIInstanceService;\n})();\nlet BlockUIComponent = /*#__PURE__*/(() => {\n  let BlockUIComponent = class BlockUIComponent {\n    constructor(blockUI) {\n      this.blockUI = blockUI;\n    }\n\n    ngOnInit() {\n      this.name = this.name || BlockUIDefaultName;\n      this.template = this.template || this.blockUI.blockUISettings.template;\n    }\n\n  };\n\n  BlockUIComponent.ɵfac = function BlockUIComponent_Factory(t) {\n    return new (t || BlockUIComponent)(ɵngcc0.ɵɵdirectiveInject(BlockUIInstanceService));\n  };\n\n  BlockUIComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: BlockUIComponent,\n    selectors: [[\"block-ui\"]],\n    inputs: {\n      name: \"name\",\n      template: \"template\",\n      message: \"message\",\n      delayStart: \"delayStart\",\n      delayStop: \"delayStop\"\n    },\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 5,\n    consts: [[3, \"name\", \"message\", \"template\", \"delayStart\", \"delayStop\"]],\n    template: function BlockUIComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵprojection(0);\n        ɵngcc0.ɵɵelement(1, \"block-ui-content\", 0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"name\", ctx.name)(\"message\", ctx.message)(\"template\", ctx.template)(\"delayStart\", ctx.delayStart)(\"delayStop\", ctx.delayStop);\n      }\n    },\n    directives: function () {\n      return [BlockUIContentComponent];\n    },\n    encapsulation: 2\n  });\n\n  __decorate([Input()], BlockUIComponent.prototype, \"name\", void 0);\n\n  __decorate([Input()], BlockUIComponent.prototype, \"message\", void 0);\n\n  __decorate([Input()], BlockUIComponent.prototype, \"delayStart\", void 0);\n\n  __decorate([Input()], BlockUIComponent.prototype, \"delayStop\", void 0);\n\n  __decorate([Input()], BlockUIComponent.prototype, \"template\", void 0); // Spinner style - https://github.com/lukehaas/css-loaders\n\n\n  return BlockUIComponent;\n})();\nconst styles = `\n.block-ui-wrapper {\n  display: none;\n  position: fixed;\n  height: 100%;\n  width: 100%;\n  top: 0;\n  left: 0;\n  background: rgba(0, 0, 0, 0.70);\n  z-index: 30000;\n  cursor: wait;\n}\n\n.block-ui-wrapper.block-ui-wrapper--element {\n  position: absolute;\n}\n\n.block-ui-wrapper.active {\n  display: block;\n}\n\n.block-ui-wrapper.block-ui-main {\n  position: fixed;\n}\n\n.block-ui-spinner,\n.block-ui-template {\n  position: absolute;\n  top: 40%;\n  margin: 0 auto;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n}\n\n.block-ui-spinner > .message {\n  font-size: 1.3em;\n  text-align: center;\n  color: #fff;\n}\n\n.block-ui__element {\n  position: relative;\n}\n\n.loader,\n.loader:after {\n  border-radius: 50%;\n  width: 10em;\n  height: 10em;\n}\n.loader {\n  margin: 7px auto;\n  font-size: 5px;\n  position: relative;\n  text-indent: -9999em;\n  border-top: 1.1em solid rgba(255, 255, 255, 0.2);\n  border-right: 1.1em solid rgba(255, 255, 255, 0.2);\n  border-bottom: 1.1em solid rgba(255, 255, 255, 0.2);\n  border-left: 1.1em solid #ffffff;\n  -webkit-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-animation: load8 1.1s infinite linear;\n  animation: load8 1.1s infinite linear;\n}\n\n@-webkit-keyframes load8 {\n  0% {\n    -webkit-transform: rotate(0deg);\n    transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes load8 {\n  0% {\n    -webkit-transform: rotate(0deg);\n    transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n`;\nconst template = `\n<div class=\"block-ui-wrapper {{name}} {{className}}\" [ngClass]=\"{ 'active': state.blockCount > 0 }\">\n  <div class=\"block-ui-spinner\" *ngIf=\"!templateCmp\">\n    <div class=\"loader\"></div>\n    <div *ngIf=\"message || defaultMessage\" class=\"message\">\n      {{ message || defaultMessage }}\n    </div>\n  </div>\n  <ng-template *ngIf=\"templateCmp\" #templateOutlet></ng-template>\n</div>\n`;\nlet BlockUIContentComponent = /*#__PURE__*/(() => {\n  let BlockUIContentComponent = class BlockUIContentComponent {\n    constructor(blockUI, resolver, changeDetectionRef) {\n      this.blockUI = blockUI;\n      this.resolver = resolver;\n      this.changeDetectionRef = changeDetectionRef;\n      this.name = BlockUIDefaultName;\n      this.defaultBlockState = {\n        startTimeouts: [],\n        stopTimeouts: [],\n        updateTimeouts: [],\n        blockCount: 0,\n        startCallCount: 0,\n        stopCallCount: 0\n      };\n      this.state = Object.assign({}, this.defaultBlockState);\n    }\n\n    ngOnInit() {\n      this.settings = this.blockUI.getSettings();\n      this.blockUISubscription = this.subscribeToBlockUI(this.blockUI.observe());\n    }\n\n    ngAfterViewInit() {\n      try {\n        if (!this.templateCmp) {\n          return false;\n        }\n\n        if (this.templateCmp instanceof TemplateRef) {\n          this.templateOutlet.createEmbeddedView(this.templateCmp);\n        } else {\n          const templateComp = this.resolver.resolveComponentFactory(this.templateCmp);\n          this.templateCompRef = this.templateOutlet.createComponent(templateComp);\n          this.updateBlockTemplate(this.message);\n        }\n      } catch (error) {\n        console.error('ng-block-ui:', error);\n      }\n    }\n\n    ngAfterViewChecked() {\n      this.detectChanges();\n    }\n\n    subscribeToBlockUI(blockUI$) {\n      return blockUI$.subscribe(event => this.onDispatchedEvent(event));\n    }\n\n    onDispatchedEvent(event) {\n      switch (event.action) {\n        case BlockUIActions.START:\n          this.onStart(event);\n          break;\n\n        case BlockUIActions.STOP:\n          this.onStop(event);\n          break;\n\n        case BlockUIActions.UPDATE:\n          this.onUpdate(event);\n          break;\n\n        case BlockUIActions.RESET:\n          this.onReset(event);\n          break;\n\n        case BlockUIActions.RESET_GLOBAL:\n          this.resetState();\n          break;\n\n        case BlockUIActions.UNSUBSCRIBE:\n          this.onStop(event);\n          this.onUnsubscribe(event.name);\n          break;\n      }\n    }\n\n    onStart({\n      name,\n      message\n    }) {\n      if (name === this.name) {\n        const delay = this.delayStart || this.settings.delayStart || 0;\n        this.state.startCallCount += 1;\n        const startTimeout = setTimeout(() => {\n          this.state.blockCount += 1;\n          this.showBlock(message);\n          this.updateInstanceBlockCount();\n        }, delay);\n        this.state.startTimeouts.push(startTimeout);\n      }\n    }\n\n    onStop({\n      name\n    }) {\n      if (name === this.name) {\n        const stopCount = this.state.stopCallCount + 1;\n\n        if (this.state.startCallCount - stopCount >= 0) {\n          const delay = this.delayStop || this.settings.delayStop || 0;\n          this.state.stopCallCount = stopCount;\n          const stopTimeout = setTimeout(() => {\n            this.state.blockCount -= 1;\n            this.updateInstanceBlockCount();\n            this.detectChanges();\n          }, delay);\n          this.state.stopTimeouts.push(stopTimeout);\n        }\n      }\n    }\n\n    onUpdate({\n      name,\n      message\n    }) {\n      if (name === this.name) {\n        const delay = this.delayStart || this.settings.delayStart || 0;\n        clearTimeout(this.state.updateTimeouts[0]);\n        const updateTimeout = setTimeout(() => {\n          this.updateMessage(message);\n        }, delay);\n        this.state.updateTimeouts.push(updateTimeout);\n      }\n    }\n\n    onReset({\n      name\n    }) {\n      if (name === this.name) {\n        this.resetState();\n      }\n    }\n\n    updateMessage(message) {\n      this.showBlock(message);\n    }\n\n    showBlock(message) {\n      this.message = message || this.defaultMessage || this.settings.message;\n      this.updateBlockTemplate(this.message);\n      this.detectChanges();\n    }\n\n    updateBlockTemplate(msg) {\n      if (this.templateCompRef && this.templateCompRef instanceof ComponentRef) {\n        this.templateCompRef.instance.message = msg;\n      }\n    }\n\n    resetState() {\n      [...this.state.startTimeouts, ...this.state.stopTimeouts, ...this.state.updateTimeouts].forEach(clearTimeout);\n      this.state = Object.assign({}, this.defaultBlockState);\n      this.updateInstanceBlockCount();\n      this.detectChanges();\n    }\n\n    onUnsubscribe(name) {\n      if (this.blockUISubscription && name === this.name) {\n        this.blockUISubscription.unsubscribe();\n      }\n    }\n\n    updateInstanceBlockCount() {\n      if (this.blockUI.blockUIInstances[this.name]) {\n        const {\n          blockCount\n        } = this.state;\n        this.blockUI.blockUIInstances[this.name].blockCount = blockCount;\n      }\n    }\n\n    detectChanges() {\n      if (!this.changeDetectionRef['destroyed']) {\n        this.changeDetectionRef.detectChanges();\n      }\n    }\n\n    ngOnDestroy() {\n      this.resetState();\n      this.onUnsubscribe(this.name);\n      this.blockUI.clearInstance(this.name);\n    }\n\n  };\n\n  BlockUIContentComponent.ɵfac = function BlockUIContentComponent_Factory(t) {\n    return new (t || BlockUIContentComponent)(ɵngcc0.ɵɵdirectiveInject(BlockUIInstanceService), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ComponentFactoryResolver), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef));\n  };\n\n  BlockUIContentComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: BlockUIContentComponent,\n    selectors: [[\"block-ui-content\"]],\n    viewQuery: function BlockUIContentComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵviewQuery(_c1, 5, ViewContainerRef);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.templateOutlet = _t.first);\n      }\n    },\n    inputs: {\n      name: \"name\",\n      delayStart: \"delayStart\",\n      delayStop: \"delayStop\",\n      defaultMessage: [\"message\", \"defaultMessage\"],\n      templateCmp: [\"template\", \"templateCmp\"]\n    },\n    decls: 3,\n    vars: 9,\n    consts: [[3, \"ngClass\"], [\"class\", \"block-ui-spinner\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"block-ui-spinner\"], [1, \"loader\"], [\"class\", \"message\", 4, \"ngIf\"], [1, \"message\"], [\"templateOutlet\", \"\"]],\n    template: function BlockUIContentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"div\", 0);\n        ɵngcc0.ɵɵtemplate(1, BlockUIContentComponent_div_1_Template, 3, 1, \"div\", 1);\n        ɵngcc0.ɵɵtemplate(2, BlockUIContentComponent_2_Template, 2, 0, undefined, 2);\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassMapInterpolate2(\"block-ui-wrapper \", ctx.name, \" \", ctx.className, \"\");\n        ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpureFunction1(7, _c2, ctx.state.blockCount > 0));\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", !ctx.templateCmp);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.templateCmp);\n      }\n    },\n    directives: [ɵngcc1.NgClass, ɵngcc1.NgIf],\n    styles: [\"\\n.block-ui-wrapper {\\n  display: none;\\n  position: fixed;\\n  height: 100%;\\n  width: 100%;\\n  top: 0;\\n  left: 0;\\n  background: rgba(0, 0, 0, 0.70);\\n  z-index: 30000;\\n  cursor: wait;\\n}\\n\\n.block-ui-wrapper.block-ui-wrapper--element {\\n  position: absolute;\\n}\\n\\n.block-ui-wrapper.active {\\n  display: block;\\n}\\n\\n.block-ui-wrapper.block-ui-main {\\n  position: fixed;\\n}\\n\\n.block-ui-spinner,\\n.block-ui-template {\\n  position: absolute;\\n  top: 40%;\\n  margin: 0 auto;\\n  left: 0;\\n  right: 0;\\n  transform: translateY(-50%);\\n}\\n\\n.block-ui-spinner > .message {\\n  font-size: 1.3em;\\n  text-align: center;\\n  color: #fff;\\n}\\n\\n.block-ui__element {\\n  position: relative;\\n}\\n\\n.loader,\\n.loader:after {\\n  border-radius: 50%;\\n  width: 10em;\\n  height: 10em;\\n}\\n.loader {\\n  margin: 7px auto;\\n  font-size: 5px;\\n  position: relative;\\n  text-indent: -9999em;\\n  border-top: 1.1em solid rgba(255, 255, 255, 0.2);\\n  border-right: 1.1em solid rgba(255, 255, 255, 0.2);\\n  border-bottom: 1.1em solid rgba(255, 255, 255, 0.2);\\n  border-left: 1.1em solid #ffffff;\\n  -webkit-transform: translateZ(0);\\n  -ms-transform: translateZ(0);\\n  transform: translateZ(0);\\n  -webkit-animation: load8 1.1s infinite linear;\\n  animation: load8 1.1s infinite linear;\\n}\\n\\n@-webkit-keyframes load8 {\\n  0% {\\n    -webkit-transform: rotate(0deg);\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes load8 {\\n  0% {\\n    -webkit-transform: rotate(0deg);\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\"],\n    encapsulation: 2\n  });\n\n  __decorate([Input()], BlockUIContentComponent.prototype, \"name\", void 0);\n\n  __decorate([Input()], BlockUIContentComponent.prototype, \"delayStart\", void 0);\n\n  __decorate([Input()], BlockUIContentComponent.prototype, \"delayStop\", void 0);\n\n  __decorate([Input('message')], BlockUIContentComponent.prototype, \"defaultMessage\", void 0);\n\n  __decorate([Input('template')], BlockUIContentComponent.prototype, \"templateCmp\", void 0);\n\n  __decorate([ViewChild('templateOutlet', {\n    read: ViewContainerRef\n  })], BlockUIContentComponent.prototype, \"templateOutlet\", void 0);\n\n  return BlockUIContentComponent;\n})();\nlet BlockUIService = /*#__PURE__*/(() => {\n  let BlockUIService = class BlockUIService {\n    constructor(blockUIInstance) {\n      this.blockUIInstance = blockUIInstance;\n      this.globalDispatch = this.blockUIInstance.decorate();\n    }\n    /**\n    * Starts blocking for given BlockUI instance or instances\n    */\n\n\n    start(target, message) {\n      this.dispatch(target, BlockUIActions.START, message);\n    }\n    /**\n    * Stops blocking for given BlockUI instance or instances\n    */\n\n\n    stop(target) {\n      this.dispatch(target, BlockUIActions.STOP);\n    }\n    /**\n    * Reset blocking for given BlockUI instance or instances\n    */\n\n\n    reset(target) {\n      this.dispatch(target, BlockUIActions.RESET);\n    }\n    /**\n    * Reset blocking for all BlockUI instances\n    */\n\n\n    resetGlobal() {\n      this.globalDispatch.resetGlobal();\n    }\n    /**\n    * Updates message for given BlockUI instance or instances\n    */\n\n\n    update(target, message) {\n      this.dispatch(target, BlockUIActions.UPDATE, message);\n    }\n    /**\n    * Unsubscribes for given BlockUI instance or instances\n    */\n\n\n    unsubscribe(target) {\n      this.dispatch(target, BlockUIActions.UNSUBSCRIBE);\n    }\n    /**\n    * Checks if BlockUI is actively blocking\n    */\n\n\n    isActive(target = null) {\n      const targets = target ? this.toArray(target) : null;\n      const instances = this.blockUIInstance.blockUIInstances;\n      return Object.keys(instances).some(key => {\n        if (!targets) {\n          return instances[key].isActive;\n        }\n\n        return targets.indexOf(instances[key].name) >= 0 && instances[key].isActive;\n      });\n    }\n\n    dispatch(target = [], type, message) {\n      const instances = this.toArray(target);\n      instances.forEach(i => this.blockUIInstance.decorate(i)[type](message));\n    }\n\n    toArray(target = []) {\n      return typeof target === 'string' ? [target] : target;\n    }\n\n  };\n\n  BlockUIService.ɵfac = function BlockUIService_Factory(t) {\n    return new (t || BlockUIService)(ɵngcc0.ɵɵinject(BlockUIInstanceService));\n  };\n\n  BlockUIService.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n    token: BlockUIService,\n    factory: function (t) {\n      return BlockUIService.ɵfac(t);\n    }\n  });\n  return BlockUIService;\n})();\nlet BlockUIDirective = /*#__PURE__*/(() => {\n  let BlockUIDirective = class BlockUIDirective {\n    constructor(blockUIService, blockUIInstanceService, viewRef, templateRef, renderer, componentFactoryResolver) {\n      this.blockUIService = blockUIService;\n      this.blockUIInstanceService = blockUIInstanceService;\n      this.viewRef = viewRef;\n      this.templateRef = templateRef;\n      this.renderer = renderer;\n      this.componentFactoryResolver = componentFactoryResolver;\n    }\n\n    set blockUI(name) {\n      this.blockTarget = name;\n    }\n\n    set blockUIMessage(message) {\n      this.message = message;\n    }\n\n    set blockUITemplate(template) {\n      this.template = template;\n    }\n\n    set blockUIDelayStart(delayStart) {\n      this.delayStart = delayStart ? Number(delayStart) : null;\n    }\n\n    set blockUIDelayStop(delayStop) {\n      this.delayStop = delayStop ? Number(delayStop) : null;\n    }\n\n    ngOnInit() {\n      try {\n        this.viewRef.createEmbeddedView(this.templateRef);\n        const parentElement = this.getParentElement();\n\n        if (parentElement && !this.isComponentInTemplate(parentElement)) {\n          this.renderer.addClass(parentElement, 'block-ui__element');\n          this.blockUIComponentRef = this.createComponent();\n          let blockUIContent = this.findContentNode(this.viewRef.element.nativeElement);\n\n          if (blockUIContent) {\n            const settings = this.blockUIInstanceService.getSettings();\n            parentElement.appendChild(blockUIContent);\n            this.blockUIComponentRef.instance.className = 'block-ui-wrapper--element';\n            this.blockUIComponentRef.instance.name = this.blockTarget || BlockUIDefaultName;\n            if (this.message) this.blockUIComponentRef.instance.defaultMessage = this.message;\n            if (this.delayStart) this.blockUIComponentRef.instance.delayStart = this.delayStart;\n            if (this.delayStop) this.blockUIComponentRef.instance.delayStop = this.delayStop;\n            if (this.template || settings.template) this.blockUIComponentRef.instance.templateCmp = this.template || settings.template;\n          }\n        }\n      } catch (error) {\n        console.error('ng-block-ui:', error);\n      }\n    }\n\n    isComponentInTemplate(element) {\n      // Needed because of https://github.com/microsoft/TypeScript/issues/26235\n      const targetElement = element || {};\n      let {\n        children\n      } = targetElement;\n      children = Array.from(children || []).reverse();\n      return children.some(el => el && el.localName === 'block-ui');\n    }\n\n    getParentElement() {\n      const embeddedView = this.viewRef.get(0);\n      return embeddedView.rootNodes[0];\n    } // Needed for IE (#17)\n\n\n    findContentNode(element) {\n      const nextSibling = element.nextSibling || {};\n      const previousSibling = element.previousSibling || {};\n      return [nextSibling, nextSibling.nextSibling, previousSibling, previousSibling.previousSibling].find(e => e && e.localName === 'block-ui-content');\n    }\n\n    createComponent() {\n      const resolvedBlockUIComponent = this.componentFactoryResolver.resolveComponentFactory(BlockUIContentComponent);\n      return this.viewRef.createComponent(resolvedBlockUIComponent);\n    }\n\n    ngOnDestroy() {\n      if (this.blockTarget) {\n        this.blockUIService.reset(this.blockTarget);\n      }\n    }\n\n  };\n\n  BlockUIDirective.ɵfac = function BlockUIDirective_Factory(t) {\n    return new (t || BlockUIDirective)(ɵngcc0.ɵɵdirectiveInject(BlockUIService), ɵngcc0.ɵɵdirectiveInject(BlockUIInstanceService), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ViewContainerRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.TemplateRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ComponentFactoryResolver));\n  };\n\n  BlockUIDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: BlockUIDirective,\n    selectors: [[\"\", \"blockUI\", \"\"]],\n    inputs: {\n      blockUI: \"blockUI\",\n      blockUIMessage: \"blockUIMessage\",\n      blockUITemplate: \"blockUITemplate\",\n      blockUIDelayStart: \"blockUIDelayStart\",\n      blockUIDelayStop: \"blockUIDelayStop\"\n    }\n  });\n\n  __decorate([Input()], BlockUIDirective.prototype, \"blockUI\", null);\n\n  __decorate([Input()], BlockUIDirective.prototype, \"blockUIMessage\", null);\n\n  __decorate([Input()], BlockUIDirective.prototype, \"blockUITemplate\", null);\n\n  __decorate([Input()], BlockUIDirective.prototype, \"blockUIDelayStart\", null);\n\n  __decorate([Input()], BlockUIDirective.prototype, \"blockUIDelayStop\", null);\n\n  return BlockUIDirective;\n})();\nvar BlockUIModule_1;\nconst BlockUIServiceInstance = new BlockUIInstanceService(); // Needed for AOT compiling\n\nconst BlockUIModuleSettings = new InjectionToken('BlockUIModuleSettings');\n\nfunction provideInstance(settings) {\n  BlockUIServiceInstance.updateSettings(settings);\n  return BlockUIServiceInstance;\n}\n\nlet BlockUIModule = BlockUIModule_1 = class BlockUIModule {\n  static forRoot(settings = {}) {\n    return {\n      ngModule: BlockUIModule_1,\n      providers: [{\n        provide: BlockUIModuleSettings,\n        useValue: settings\n      }, {\n        provide: BlockUIInstanceService,\n        useFactory: provideInstance,\n        deps: [BlockUIModuleSettings]\n      }, BlockUIService]\n    };\n  }\n\n};\n\nBlockUIModule.ɵfac = function BlockUIModule_Factory(t) {\n  return new (t || BlockUIModule)();\n};\n\nBlockUIModule.ɵmod = /*@__PURE__*/ɵngcc0.ɵɵdefineNgModule({\n  type: BlockUIModule\n});\nBlockUIModule.ɵinj = /*@__PURE__*/ɵngcc0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(BlockUIModule, {\n    declarations: function () {\n      return [BlockUIComponent, BlockUIDirective, BlockUIContentComponent];\n    },\n    imports: function () {\n      return [CommonModule];\n    },\n    exports: function () {\n      return [BlockUIComponent, BlockUIDirective, BlockUIContentComponent];\n    }\n  });\n})();\n\nlet blockInstanceGuid = 1;\n\nfunction BlockUI(blockName, settings = {}) {\n  if (!settings.scopeToInstance) {\n    return function (target, propertyKey) {\n      target[propertyKey] = BlockUIServiceInstance.decorate(blockName);\n    };\n  }\n\n  return function (target, key) {\n    const secret = `_${key}-block-ui`;\n    Object.defineProperty(target, key, {\n      get: function () {\n        if (this[secret]) {\n          return this[secret];\n        }\n\n        const instanceName = `${blockName}-${blockInstanceGuid++}`;\n        this[secret] = BlockUIServiceInstance.decorate(instanceName);\n        return this[secret];\n      },\n      set: function (value) {\n        this[secret] = value;\n      }\n    });\n  };\n}\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BlockUIDefaultName as BLOCKUI_DEFAULT, BlockUI, BlockUIComponent, BlockUIContentComponent, BlockUIModule, BlockUIService, BlockUIModuleSettings as ɵa, provideInstance as ɵb, BlockUIInstanceService as ɵc, template as ɵd, styles as ɵe, BlockUIDirective as ɵf }; //# sourceMappingURL=ng-block-ui.js.map", "map": null, "metadata": {}, "sourceType": "module"}