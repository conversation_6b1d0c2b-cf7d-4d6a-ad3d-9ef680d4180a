{"ast": null, "code": "import { isFunction } from './util/isFunction';\nimport { isSubscription, Subscription } from './Subscription';\nimport { config } from './config';\nimport { reportUnhandledError } from './util/reportUnhandledError';\nimport { noop } from './util/noop';\nimport { nextNotification, errorNotification, COMPLETE_NOTIFICATION } from './NotificationFactories';\nimport { timeoutProvider } from './scheduler/timeoutProvider';\nimport { captureError } from './util/errorContext';\nexport class Subscriber extends Subscription {\n  constructor(destination) {\n    super();\n    this.isStopped = false;\n\n    if (destination) {\n      this.destination = destination;\n\n      if (isSubscription(destination)) {\n        destination.add(this);\n      }\n    } else {\n      this.destination = EMPTY_OBSERVER;\n    }\n  }\n\n  static create(next, error, complete) {\n    return new SafeSubscriber(next, error, complete);\n  }\n\n  next(value) {\n    if (this.isStopped) {\n      handleStoppedNotification(nextNotification(value), this);\n    } else {\n      this._next(value);\n    }\n  }\n\n  error(err) {\n    if (this.isStopped) {\n      handleStoppedNotification(errorNotification(err), this);\n    } else {\n      this.isStopped = true;\n\n      this._error(err);\n    }\n  }\n\n  complete() {\n    if (this.isStopped) {\n      handleStoppedNotification(COMPLETE_NOTIFICATION, this);\n    } else {\n      this.isStopped = true;\n\n      this._complete();\n    }\n  }\n\n  unsubscribe() {\n    if (!this.closed) {\n      this.isStopped = true;\n      super.unsubscribe();\n      this.destination = null;\n    }\n  }\n\n  _next(value) {\n    this.destination.next(value);\n  }\n\n  _error(err) {\n    try {\n      this.destination.error(err);\n    } finally {\n      this.unsubscribe();\n    }\n  }\n\n  _complete() {\n    try {\n      this.destination.complete();\n    } finally {\n      this.unsubscribe();\n    }\n  }\n\n}\nexport class SafeSubscriber extends Subscriber {\n  constructor(observerOrNext, error, complete) {\n    super();\n    let next;\n\n    if (isFunction(observerOrNext)) {\n      next = observerOrNext;\n    } else if (observerOrNext) {\n      ({\n        next,\n        error,\n        complete\n      } = observerOrNext);\n      let context;\n\n      if (this && config.useDeprecatedNextContext) {\n        context = Object.create(observerOrNext);\n\n        context.unsubscribe = () => this.unsubscribe();\n      } else {\n        context = observerOrNext;\n      }\n\n      next = next === null || next === void 0 ? void 0 : next.bind(context);\n      error = error === null || error === void 0 ? void 0 : error.bind(context);\n      complete = complete === null || complete === void 0 ? void 0 : complete.bind(context);\n    }\n\n    this.destination = {\n      next: next ? wrapForErrorHandling(next, this) : noop,\n      error: wrapForErrorHandling(error !== null && error !== void 0 ? error : defaultErrorHandler, this),\n      complete: complete ? wrapForErrorHandling(complete, this) : noop\n    };\n  }\n\n}\n\nfunction wrapForErrorHandling(handler, instance) {\n  return (...args) => {\n    try {\n      handler(...args);\n    } catch (err) {\n      if (config.useDeprecatedSynchronousErrorHandling) {\n        captureError(err);\n      } else {\n        reportUnhandledError(err);\n      }\n    }\n  };\n}\n\nfunction defaultErrorHandler(err) {\n  throw err;\n}\n\nfunction handleStoppedNotification(notification, subscriber) {\n  const {\n    onStoppedNotification\n  } = config;\n  onStoppedNotification && timeoutProvider.setTimeout(() => onStoppedNotification(notification, subscriber));\n}\n\nexport const EMPTY_OBSERVER = {\n  closed: true,\n  next: noop,\n  error: defaultErrorHandler,\n  complete: noop\n}; //# sourceMappingURL=Subscriber.js.map", "map": null, "metadata": {}, "sourceType": "module"}