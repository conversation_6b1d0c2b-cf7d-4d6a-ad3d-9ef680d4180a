{"ast": null, "code": "export class Page {\n  constructor() {\n    // The number of elements in the page\n    this.size = 0; // The total number of elements\n\n    this.totalElements = 0; // The total number of pages\n\n    this.totalPages = 0; // The current page number\n\n    this.pageNumber = 0; // The current page total number of elements\n\n    this.pageTotalElements = 0; // The pagination starts from 0 or 1\n\n    this.startsFrom = 0;\n  }\n\n  pages() {\n    const array = [];\n\n    for (let index = 0; index < this.totalPages; index++) {\n      array.push(index);\n    }\n\n    return array;\n  }\n\n  showingResult() {\n    const start = this.totalElements > 0 ? (this.pageNumber - this.startsFrom) * this.size + 1 : 0;\n    const end = this.totalElements > 0 ? start + this.pageTotalElements - 1 : 0;\n    return 'Showing ' + start + '–' + end + ' of ' + this.totalElements + ' results';\n  }\n\n}", "map": null, "metadata": {}, "sourceType": "module"}