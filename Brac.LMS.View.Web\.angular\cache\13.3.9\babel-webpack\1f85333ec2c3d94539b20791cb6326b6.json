{"ast": null, "code": "import { config } from '../config';\nimport { timeoutProvider } from '../scheduler/timeoutProvider';\nexport function reportUnhandledError(err) {\n  timeoutProvider.setTimeout(() => {\n    const {\n      onUnhandledError\n    } = config;\n\n    if (onUnhandledError) {\n      onUnhandledError(err);\n    } else {\n      throw err;\n    }\n  });\n} //# sourceMappingURL=reportUnhandledError.js.map", "map": null, "metadata": {}, "sourceType": "module"}