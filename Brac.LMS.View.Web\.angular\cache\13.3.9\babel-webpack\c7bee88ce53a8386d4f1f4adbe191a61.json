{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, forwardRef, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, HostListener, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n/** Default values provider for rating */\n\nfunction RatingComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const value_r3 = ctx.value;\n    const index_r4 = ctx.index;\n    i0.ɵɵtextInterpolate(index_r4 < value_r3 ? \"\\u2605\" : \"\\u2606\");\n  }\n}\n\nfunction RatingComponent_ng_template_3_ng_template_3_Template(rf, ctx) {}\n\nconst _c0 = function (a0, a1) {\n  return {\n    index: a0,\n    value: a1\n  };\n};\n\nfunction RatingComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 4);\n    i0.ɵɵlistener(\"mouseenter\", function RatingComponent_ng_template_3_Template_span_mouseenter_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const index_r6 = restoredCtx.index;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return ctx_r8.enter(index_r6 + 1);\n    })(\"click\", function RatingComponent_ng_template_3_Template_span_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const index_r6 = restoredCtx.index;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return ctx_r10.rate(index_r6 + 1);\n    });\n    i0.ɵɵtemplate(3, RatingComponent_ng_template_3_ng_template_3_Template, 0, 0, \"ng-template\", 5);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const r_r5 = ctx.$implicit;\n    const index_r6 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n\n    const _r0 = i0.ɵɵreference(2);\n\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", index_r6 < ctx_r2.value ? \"*\" : \" \", \")\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"cursor\", ctx_r2.readonly ? \"default\" : \"pointer\");\n    i0.ɵɵclassProp(\"active\", index_r6 < ctx_r2.value);\n    i0.ɵɵproperty(\"title\", r_r5.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.customTemplate || _r0)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(8, _c0, index_r6, ctx_r2.value));\n  }\n}\n\nlet RatingConfig = /*#__PURE__*/(() => {\n  class RatingConfig {\n    constructor() {\n      /** aria label for rating */\n      this.ariaLabel = 'rating';\n    }\n\n  }\n\n  RatingConfig.ɵfac = function RatingConfig_Factory(t) {\n    return new (t || RatingConfig)();\n  };\n\n  RatingConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RatingConfig,\n    factory: RatingConfig.ɵfac,\n    providedIn: 'root'\n  });\n  return RatingConfig;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst RATING_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => RatingComponent),\n  multi: true\n};\nlet RatingComponent = /*#__PURE__*/(() => {\n  class RatingComponent {\n    constructor(changeDetection, config) {\n      this.changeDetection = changeDetection;\n      /** number of icons */\n\n      this.max = 5;\n      /** if true will not react on any user events */\n\n      this.readonly = false;\n      /** array of icons titles, default: ([\"one\", \"two\", \"three\", \"four\", \"five\"]) */\n\n      this.titles = [];\n      /** fired when icon selected, $event:number equals to selected rating */\n\n      this.onHover = new EventEmitter();\n      /** fired when icon selected, $event:number equals to previous rating value */\n\n      this.onLeave = new EventEmitter();\n      this.onChange = Function.prototype;\n      this.onTouched = Function.prototype;\n      /** aria label for rating */\n\n      this.ariaLabel = 'rating';\n      this.range = [];\n      this.value = 0;\n      Object.assign(this, config);\n    }\n\n    onKeydown(event) {\n      if ([37, 38, 39, 40].indexOf(event.which) === -1) {\n        return;\n      }\n\n      event.preventDefault();\n      event.stopPropagation();\n      const sign = event.which === 38 || event.which === 39 ? 1 : -1;\n      this.rate(this.value + sign);\n    }\n\n    ngOnInit() {\n      this.max = this.max || 5;\n      this.titles = typeof this.titles !== 'undefined' && this.titles.length > 0 ? this.titles : [];\n      this.range = this.buildTemplateObjects(this.max);\n    } // model -> view\n\n\n    writeValue(value) {\n      if (value % 1 !== value) {\n        this.value = Math.round(value);\n        this.preValue = value;\n        this.changeDetection.markForCheck();\n        return;\n      }\n\n      this.preValue = value;\n      this.value = value;\n      this.changeDetection.markForCheck();\n    }\n\n    enter(value) {\n      if (!this.readonly) {\n        this.value = value;\n        this.changeDetection.markForCheck();\n        this.onHover.emit(value);\n      }\n    }\n\n    reset() {\n      if (typeof this.preValue === 'number') {\n        this.value = Math.round(this.preValue);\n        this.changeDetection.markForCheck();\n        this.onLeave.emit(this.value);\n      }\n    }\n\n    registerOnChange(fn) {\n      this.onChange = fn;\n    }\n\n    registerOnTouched(fn) {\n      this.onTouched = fn;\n    }\n\n    rate(value) {\n      if (!this.readonly && this.range && value >= 0 && value <= this.range.length) {\n        this.writeValue(value);\n        this.onChange(value);\n      }\n    }\n\n    buildTemplateObjects(max) {\n      const result = [];\n\n      for (let i = 0; i < max; i++) {\n        result.push({\n          index: i,\n          title: this.titles[i] || i + 1\n        });\n      }\n\n      return result;\n    }\n\n  }\n\n  RatingComponent.ɵfac = function RatingComponent_Factory(t) {\n    return new (t || RatingComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(RatingConfig));\n  };\n\n  RatingComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: RatingComponent,\n    selectors: [[\"rating\"]],\n    hostBindings: function RatingComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function RatingComponent_keydown_HostBindingHandler($event) {\n          return ctx.onKeydown($event);\n        });\n      }\n    },\n    inputs: {\n      max: \"max\",\n      readonly: \"readonly\",\n      titles: \"titles\",\n      customTemplate: \"customTemplate\"\n    },\n    outputs: {\n      onHover: \"onHover\",\n      onLeave: \"onLeave\"\n    },\n    features: [i0.ɵɵProvidersFeature([RATING_CONTROL_VALUE_ACCESSOR])],\n    decls: 4,\n    vars: 4,\n    consts: [[\"tabindex\", \"0\", \"role\", \"slider\", \"aria-valuemin\", \"0\", 3, \"mouseleave\", \"keydown\"], [\"star\", \"\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [1, \"sr-only\", \"visually-hidden\"], [1, \"bs-rating-star\", 3, \"title\", \"mouseenter\", \"click\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n    template: function RatingComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"span\", 0);\n        i0.ɵɵlistener(\"mouseleave\", function RatingComponent_Template_span_mouseleave_0_listener() {\n          return ctx.reset();\n        })(\"keydown\", function RatingComponent_Template_span_keydown_0_listener($event) {\n          return ctx.onKeydown($event);\n        });\n        i0.ɵɵtemplate(1, RatingComponent_ng_template_1_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(3, RatingComponent_ng_template_3_Template, 4, 11, \"ng-template\", 2);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-valuemax\", ctx.range.length)(\"aria-valuenow\", ctx.value);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.range);\n      }\n    },\n    directives: [i2.NgForOf, i2.NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return RatingComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet RatingModule = /*#__PURE__*/(() => {\n  class RatingModule {\n    static forRoot() {\n      return {\n        ngModule: RatingModule,\n        providers: []\n      };\n    }\n\n  }\n\n  RatingModule.ɵfac = function RatingModule_Factory(t) {\n    return new (t || RatingModule)();\n  };\n\n  RatingModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: RatingModule\n  });\n  RatingModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule]]\n  });\n  return RatingModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { RatingComponent, RatingConfig, RatingModule }; //# sourceMappingURL=ngx-bootstrap-rating.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}