{"ast": null, "code": "import { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function audit(durationSelector) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let lastValue = null;\n    let durationSubscriber = null;\n    let isComplete = false;\n\n    const endDuration = () => {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      durationSubscriber = null;\n\n      if (hasValue) {\n        hasValue = false;\n        const value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n\n      isComplete && subscriber.complete();\n    };\n\n    const cleanupDuration = () => {\n      durationSubscriber = null;\n      isComplete && subscriber.complete();\n    };\n\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      lastValue = value;\n\n      if (!durationSubscriber) {\n        innerFrom(durationSelector(value)).subscribe(durationSubscriber = new OperatorSubscriber(subscriber, endDuration, cleanupDuration));\n      }\n    }, () => {\n      isComplete = true;\n      (!hasValue || !durationSubscriber || durationSubscriber.closed) && subscriber.complete();\n    }));\n  });\n} //# sourceMappingURL=audit.js.map", "map": null, "metadata": {}, "sourceType": "module"}