{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./_services/nonce.service\";\nimport * as i2 from \"@angular/router\";\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    //window.trustedTypes\n    constructor(nonceService) {\n      //   globalThis.window['trustedTypes'].createPolicy('default', {\n      //     createHTML: (string, sink) => string,\n      // });\n      this.nonceService = nonceService;\n      this.title = 'brac-elearning-web';\n      this.isOpen = false;\n    }\n\n    handleSelectClick(select) {\n      if (!this.isOpen) {\n        this.isOpen = true;\n      } else {\n        select.close();\n        this.isOpen = false;\n      }\n    }\n\n    ngAfterViewInit() {//this.nonceService.addNonceToStyles(\"51e0b292-2bab-4ee4-9616-499d3e69b53d\")\n    }\n\n  }\n\n  AppComponent.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.NonceService));\n  };\n\n  AppComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 2,\n    vars: 0,\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"router-outlet\");\n        i0.ɵɵelement(1, \"app-spinner\");\n        i0.ɵɵelementEnd();\n      }\n    },\n    directives: [i2.RouterOutlet],\n    encapsulation: 2\n  });\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}