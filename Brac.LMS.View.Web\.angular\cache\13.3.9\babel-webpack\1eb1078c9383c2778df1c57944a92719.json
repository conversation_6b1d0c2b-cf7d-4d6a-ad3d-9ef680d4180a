{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { BlockUIModule } from 'ng-block-ui';\nimport { UploadService } from './../_services/upload.service';\nimport { TabsModule } from 'ngx-bootstrap/tabs';\nimport { RatingModule } from 'ngx-bootstrap/rating';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { MomentModule } from 'ngx-moment';\nimport { AccordionModule } from 'ngx-bootstrap/accordion';\nimport { NgxPaginationModule } from 'ngx-pagination';\nimport { ModalModule } from 'ngx-bootstrap/modal';\nimport { NgxDatatableModule } from '@swimlane/ngx-datatable';\nimport { TooltipModule } from 'ngx-bootstrap/tooltip';\nimport { BlockTemplateCmp } from '../_helpers/block-ui-template/block-ui-template';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ng-block-ui\";\nimport * as i2 from \"ngx-bootstrap/tabs\";\nimport * as i3 from \"ngx-bootstrap/rating\";\nimport * as i4 from \"ngx-bootstrap/modal\";\nimport * as i5 from \"ngx-bootstrap/tooltip\";\nimport * as i6 from \"ngx-bootstrap/accordion\";\nexport let SharedModule = /*#__PURE__*/(() => {\n  class SharedModule {}\n\n  SharedModule.ɵfac = function SharedModule_Factory(t) {\n    return new (t || SharedModule)();\n  };\n\n  SharedModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SharedModule\n  });\n  SharedModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [UploadService],\n    imports: [[CommonModule, FormsModule, ReactiveFormsModule, BlockUIModule.forRoot({\n      template: BlockTemplateCmp\n    }), TabsModule.forRoot(), RatingModule.forRoot(), ModalModule.forRoot(), TooltipModule.forRoot(), NgSelectModule, FlexLayoutModule, MatDialogModule, MatButtonModule, MomentModule, AccordionModule.forRoot(), NgxPaginationModule, NgxDatatableModule], FormsModule, ReactiveFormsModule, BlockUIModule, TabsModule, RatingModule, ModalModule, TooltipModule, NgSelectModule, FlexLayoutModule, MatDialogModule, MatButtonModule, MomentModule, AccordionModule, NgxPaginationModule, NgxDatatableModule]\n  });\n  return SharedModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}