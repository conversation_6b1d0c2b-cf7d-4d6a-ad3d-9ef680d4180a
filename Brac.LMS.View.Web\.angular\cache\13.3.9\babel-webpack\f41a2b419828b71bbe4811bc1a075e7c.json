{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { MyCoursesRoutes } from './my-courses.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let MyCoursesModule = /*#__PURE__*/(() => {\n  class MyCoursesModule {}\n\n  MyCoursesModule.ɵfac = function MyCoursesModule_Factory(t) {\n    return new (t || MyCoursesModule)();\n  };\n\n  MyCoursesModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: MyCoursesModule\n  });\n  MyCoursesModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(MyCoursesRoutes), SharedModule, WebLayoutModule]]\n  });\n  return MyCoursesModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}