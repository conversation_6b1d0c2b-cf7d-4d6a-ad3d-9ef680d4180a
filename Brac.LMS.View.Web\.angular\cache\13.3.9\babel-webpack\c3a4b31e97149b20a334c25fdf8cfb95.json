{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { LoginRoutes } from './login.routing';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { BlockUIModule } from 'ng-block-ui';\nimport { BlockTemplateCmp } from 'src/app/_helpers/block-ui-template/block-ui-template';\nimport { NgOtpInputModule } from 'ng-otp-input';\nimport { NgxSmartModalModule } from 'ngx-smart-modal';\nimport { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ng-block-ui\";\nimport * as i3 from \"ngx-smart-modal\";\nexport let LoginModule = /*#__PURE__*/(() => {\n  class LoginModule {}\n\n  LoginModule.ɵfac = function LoginModule_Factory(t) {\n    return new (t || LoginModule)();\n  };\n\n  LoginModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: LoginModule\n  });\n  LoginModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(LoginRoutes), FormsModule, ReactiveFormsModule, BlockUIModule.forRoot({\n      template: BlockTemplateCmp\n    }), NgOtpInputModule, NgxSmartModalModule.forRoot(), NgxExtendedPdfViewerModule]]\n  });\n  return LoginModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}