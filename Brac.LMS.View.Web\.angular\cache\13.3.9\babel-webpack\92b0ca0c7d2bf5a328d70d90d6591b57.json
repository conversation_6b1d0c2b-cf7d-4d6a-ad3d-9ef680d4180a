{"ast": null, "code": "export function executeSchedule(parentSubscription, scheduler, work, delay = 0, repeat = false) {\n  const scheduleSubscription = scheduler.schedule(function () {\n    work();\n\n    if (repeat) {\n      parentSubscription.add(this.schedule(null, delay));\n    } else {\n      this.unsubscribe();\n    }\n  }, delay);\n  parentSubscription.add(scheduleSubscription);\n\n  if (!repeat) {\n    return scheduleSubscription;\n  }\n} //# sourceMappingURL=executeSchedule.js.map", "map": null, "metadata": {}, "sourceType": "module"}