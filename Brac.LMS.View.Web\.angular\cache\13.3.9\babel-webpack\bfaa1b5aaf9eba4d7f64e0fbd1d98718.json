{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Occitan, lengadocian dialecte [oc-lnc]\n//! author : <PERSON> : https://github.com/Quenty31\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var ocLnc = moment.defineLocale('oc-lnc', {\n    months: {\n      standalone: 'genièr_febrièr_març_abril_mai_junh_julhet_agost_setembre_octòbre_novembre_decembre'.split('_'),\n      format: \"de genièr_de febrièr_de març_d'abril_de mai_de junh_de julhet_d'agost_de setembre_d'octòbre_de novembre_de decembre\".split('_'),\n      isFormat: /D[oD]?(\\s)+MMMM/\n    },\n    monthsShort: 'gen._febr._març_abr._mai_junh_julh._ago._set._oct._nov._dec.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'dimenge_diluns_dimars_dimècres_dijòus_divendres_dissabte'.split('_'),\n    weekdaysShort: 'dg._dl._dm._dc._dj._dv._ds.'.split('_'),\n    weekdaysMin: 'dg_dl_dm_dc_dj_dv_ds'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM [de] YYYY',\n      ll: 'D MMM YYYY',\n      LLL: 'D MMMM [de] YYYY [a] H:mm',\n      lll: 'D MMM YYYY, H:mm',\n      LLLL: 'dddd D MMMM [de] YYYY [a] H:mm',\n      llll: 'ddd D MMM YYYY, H:mm'\n    },\n    calendar: {\n      sameDay: '[uèi a] LT',\n      nextDay: '[deman a] LT',\n      nextWeek: 'dddd [a] LT',\n      lastDay: '[ièr a] LT',\n      lastWeek: 'dddd [passat a] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: \"d'aquí %s\",\n      past: 'fa %s',\n      s: 'unas segondas',\n      ss: '%d segondas',\n      m: 'una minuta',\n      mm: '%d minutas',\n      h: 'una ora',\n      hh: '%d oras',\n      d: 'un jorn',\n      dd: '%d jorns',\n      M: 'un mes',\n      MM: '%d meses',\n      y: 'un an',\n      yy: '%d ans'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(r|n|t|è|a)/,\n    ordinal: function (number, period) {\n      var output = number === 1 ? 'r' : number === 2 ? 'n' : number === 3 ? 'r' : number === 4 ? 't' : 'è';\n\n      if (period === 'w' || period === 'W') {\n        output = 'a';\n      }\n\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4\n    }\n  });\n  return ocLnc;\n});", "map": null, "metadata": {}, "sourceType": "script"}