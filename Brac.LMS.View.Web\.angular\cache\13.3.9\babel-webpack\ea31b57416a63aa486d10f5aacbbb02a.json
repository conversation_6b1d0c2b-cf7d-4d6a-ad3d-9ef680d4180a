{"ast": null, "code": "import { EMPTY } from './observable/empty';\nimport { of } from './observable/of';\nimport { throwError } from './observable/throwError';\nimport { isFunction } from './util/isFunction';\nexport var NotificationKind = /*#__PURE__*/(() => {\n  (function (NotificationKind) {\n    NotificationKind[\"NEXT\"] = \"N\";\n    NotificationKind[\"ERROR\"] = \"E\";\n    NotificationKind[\"COMPLETE\"] = \"C\";\n  })(NotificationKind || (NotificationKind = {}));\n\n  return NotificationKind;\n})();\nexport class Notification {\n  constructor(kind, value, error) {\n    this.kind = kind;\n    this.value = value;\n    this.error = error;\n    this.hasValue = kind === 'N';\n  }\n\n  observe(observer) {\n    return observeNotification(this, observer);\n  }\n\n  do(nextHandler, errorHandler, completeHandler) {\n    const {\n      kind,\n      value,\n      error\n    } = this;\n    return kind === 'N' ? nextHandler === null || nextHandler === void 0 ? void 0 : nextHandler(value) : kind === 'E' ? errorHandler === null || errorHandler === void 0 ? void 0 : errorHandler(error) : completeHandler === null || completeHandler === void 0 ? void 0 : completeHandler();\n  }\n\n  accept(nextOrObserver, error, complete) {\n    var _a;\n\n    return isFunction((_a = nextOrObserver) === null || _a === void 0 ? void 0 : _a.next) ? this.observe(nextOrObserver) : this.do(nextOrObserver, error, complete);\n  }\n\n  toObservable() {\n    const {\n      kind,\n      value,\n      error\n    } = this;\n    const result = kind === 'N' ? of(value) : kind === 'E' ? throwError(() => error) : kind === 'C' ? EMPTY : 0;\n\n    if (!result) {\n      throw new TypeError(`Unexpected notification kind ${kind}`);\n    }\n\n    return result;\n  }\n\n  static createNext(value) {\n    return new Notification('N', value);\n  }\n\n  static createError(err) {\n    return new Notification('E', undefined, err);\n  }\n\n  static createComplete() {\n    return Notification.completeNotification;\n  }\n\n}\nNotification.completeNotification = new Notification('C');\nexport function observeNotification(notification, observer) {\n  var _a, _b, _c;\n\n  const {\n    kind,\n    value,\n    error\n  } = notification;\n\n  if (typeof kind !== 'string') {\n    throw new TypeError('Invalid notification, missing \"kind\"');\n  }\n\n  kind === 'N' ? (_a = observer.next) === null || _a === void 0 ? void 0 : _a.call(observer, value) : kind === 'E' ? (_b = observer.error) === null || _b === void 0 ? void 0 : _b.call(observer, error) : (_c = observer.complete) === null || _c === void 0 ? void 0 : _c.call(observer);\n} //# sourceMappingURL=Notification.js.map", "map": null, "metadata": {}, "sourceType": "module"}