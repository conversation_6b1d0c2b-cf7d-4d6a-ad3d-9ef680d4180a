{"ast": null, "code": "import { __asyncGenerator, __await } from \"tslib\";\nimport { isFunction } from './isFunction';\nexport function readableStreamLikeToAsyncGenerator(readableStream) {\n  return __asyncGenerator(this, arguments, function* readableStreamLikeToAsyncGenerator_1() {\n    const reader = readableStream.getReader();\n\n    try {\n      while (true) {\n        const {\n          value,\n          done\n        } = yield __await(reader.read());\n\n        if (done) {\n          return yield __await(void 0);\n        }\n\n        yield yield __await(value);\n      }\n    } finally {\n      reader.releaseLock();\n    }\n  });\n}\nexport function isReadableStreamLike(obj) {\n  return isFunction(obj === null || obj === void 0 ? void 0 : obj.getReader);\n} //# sourceMappingURL=isReadableStreamLike.js.map", "map": null, "metadata": {}, "sourceType": "module"}