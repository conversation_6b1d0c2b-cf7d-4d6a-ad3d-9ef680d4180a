{"ast": null, "code": "import { EMPTY } from './empty';\nimport { onErrorResumeNext as onErrorResumeNextWith } from '../operators/onErrorResumeNext';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nexport function onErrorResumeNext(...sources) {\n  return onErrorResumeNextWith(argsOrArgArray(sources))(EMPTY);\n} //# sourceMappingURL=onErrorResumeNext.js.map", "map": null, "metadata": {}, "sourceType": "module"}