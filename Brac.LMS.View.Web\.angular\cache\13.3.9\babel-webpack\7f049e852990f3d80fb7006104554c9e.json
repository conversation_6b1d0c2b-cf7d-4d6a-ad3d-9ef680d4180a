{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Hungarian [hu]\n//! author : <PERSON> : https://github.com/adambrunner\n//! author : <PERSON>  : https://github.com/passatgt\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var weekEndings = 'vasárnap hétfőn kedden szerdán csütörtökön pénteken szombaton'.split(' ');\n\n  function translate(number, withoutSuffix, key, isFuture) {\n    var num = number;\n\n    switch (key) {\n      case 's':\n        return isFuture || withoutSuffix ? 'néhány másodperc' : 'néhány más<PERSON>';\n\n      case 'ss':\n        return num + (isFuture || withoutSuffix) ? ' másodperc' : ' másodperce';\n\n      case 'm':\n        return 'egy' + (isFuture || withoutSuffix ? ' perc' : ' perce');\n\n      case 'mm':\n        return num + (isFuture || withoutSuffix ? ' perc' : ' perce');\n\n      case 'h':\n        return 'egy' + (isFuture || withoutSuffix ? ' óra' : ' órája');\n\n      case 'hh':\n        return num + (isFuture || withoutSuffix ? ' óra' : ' órája');\n\n      case 'd':\n        return 'egy' + (isFuture || withoutSuffix ? ' nap' : ' napja');\n\n      case 'dd':\n        return num + (isFuture || withoutSuffix ? ' nap' : ' napja');\n\n      case 'M':\n        return 'egy' + (isFuture || withoutSuffix ? ' hónap' : ' hónapja');\n\n      case 'MM':\n        return num + (isFuture || withoutSuffix ? ' hónap' : ' hónapja');\n\n      case 'y':\n        return 'egy' + (isFuture || withoutSuffix ? ' év' : ' éve');\n\n      case 'yy':\n        return num + (isFuture || withoutSuffix ? ' év' : ' éve');\n    }\n\n    return '';\n  }\n\n  function week(isFuture) {\n    return (isFuture ? '' : '[múlt] ') + '[' + weekEndings[this.day()] + '] LT[-kor]';\n  }\n\n  var hu = moment.defineLocale('hu', {\n    months: 'január_február_március_április_május_június_július_augusztus_szeptember_október_november_december'.split('_'),\n    monthsShort: 'jan._feb._márc._ápr._máj._jún._júl._aug._szept._okt._nov._dec.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat'.split('_'),\n    weekdaysShort: 'vas_hét_kedd_sze_csüt_pén_szo'.split('_'),\n    weekdaysMin: 'v_h_k_sze_cs_p_szo'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'YYYY.MM.DD.',\n      LL: 'YYYY. MMMM D.',\n      LLL: 'YYYY. MMMM D. H:mm',\n      LLLL: 'YYYY. MMMM D., dddd H:mm'\n    },\n    meridiemParse: /de|du/i,\n    isPM: function (input) {\n      return input.charAt(1).toLowerCase() === 'u';\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 12) {\n        return isLower === true ? 'de' : 'DE';\n      } else {\n        return isLower === true ? 'du' : 'DU';\n      }\n    },\n    calendar: {\n      sameDay: '[ma] LT[-kor]',\n      nextDay: '[holnap] LT[-kor]',\n      nextWeek: function () {\n        return week.call(this, true);\n      },\n      lastDay: '[tegnap] LT[-kor]',\n      lastWeek: function () {\n        return week.call(this, false);\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s múlva',\n      past: '%s',\n      s: translate,\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: translate,\n      hh: translate,\n      d: translate,\n      dd: translate,\n      M: translate,\n      MM: translate,\n      y: translate,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return hu;\n});", "map": null, "metadata": {}, "sourceType": "script"}