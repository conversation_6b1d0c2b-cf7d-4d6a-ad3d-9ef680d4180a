{"ast": null, "code": "import { of, BehaviorSubject } from 'rxjs';\nimport { map, catchError, tap, shareReplay } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AuthenticationService = /*#__PURE__*/(() => {\n  class AuthenticationService {\n    constructor(http) {\n      this.http = http;\n      this.currentUserSubject = new BehaviorSubject(null);\n      this.currentUser = this.currentUserSubject.asObservable();\n      this.LOGIN_PATH = '/';\n      this.INITIAL_PATH = '/dashboard';\n      this.profileRequesting = false;\n    }\n\n    login(param) {\n      var data = {\n        Username: param.UserName,\n        Password: encodeURIComponent(param.Password),\n        grant_type: 'password'\n      };\n      return this.http.post(environment.apiUrl + 'account/trainee/login', data, {\n        withCredentials: true\n      }).pipe(map(data => {\n        // this.doLoginUser(data);\n        return true;\n      }), catchError(err => {\n        console.log(err);\n        return of(null);\n      }));\n    }\n\n    logout(hostname) {\n      return this.http.post(environment.apiUrl + 'account/logout', null, {\n        withCredentials: true\n      }).pipe(tap(() => this.doLogoutUser()));\n    }\n\n    registerSystemAdmin(url, params) {\n      return this.http.post(environment.apiUrl + url, params).pipe(map(res => {\n        return res;\n      }));\n    }\n\n    isLoggedIn() {\n      return this.getCurrentUser().pipe(map(user => !!user), catchError(() => of(false)));\n    }\n\n    doLoginUser(user) {\n      this.loggedUser = user;\n    }\n\n    doLogoutUser() {\n      this.loggedUser = undefined;\n      this.currentUserSubject.next(this.loggedUser);\n    }\n\n    getCurrentUser() {\n      if (this.loggedUser) {\n        return of(this.loggedUser);\n      } else {\n        if (!this.profileRequesting) {\n          this.profileRequesting = true;\n          this.profileRequest = this.http.get(environment.apiUrl + 'account/get-trainee-profile', {\n            withCredentials: true\n          }).pipe(shareReplay(1), tap(user => {\n            this.loggedUser = user;\n            this.currentUserSubject.next(this.loggedUser);\n            this.profileRequesting = false;\n          }));\n        }\n\n        return this.profileRequest;\n      }\n    }\n\n    updateImage(ImagePath) {\n      if (this.loggedUser) this.loggedUser.ImagePath = ImagePath;\n    }\n\n  }\n\n  AuthenticationService.ɵfac = function AuthenticationService_Factory(t) {\n    return new (t || AuthenticationService)(i0.ɵɵinject(i1.HttpClient));\n  };\n\n  AuthenticationService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthenticationService,\n    factory: AuthenticationService.ɵfac,\n    providedIn: 'root'\n  });\n  return AuthenticationService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}