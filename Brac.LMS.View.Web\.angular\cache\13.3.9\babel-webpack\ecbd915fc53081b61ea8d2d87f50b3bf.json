{"ast": null, "code": "import { Scheduler } from '../Scheduler';\nexport class AsyncScheduler extends Scheduler {\n  constructor(SchedulerAction, now = Scheduler.now) {\n    super(SchedulerAction, now);\n    this.actions = [];\n    this._active = false;\n    this._scheduled = undefined;\n  }\n\n  flush(action) {\n    const {\n      actions\n    } = this;\n\n    if (this._active) {\n      actions.push(action);\n      return;\n    }\n\n    let error;\n    this._active = true;\n\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while (action = actions.shift());\n\n    this._active = false;\n\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n\n      throw error;\n    }\n  }\n\n} //# sourceMappingURL=AsyncScheduler.js.map", "map": null, "metadata": {}, "sourceType": "module"}