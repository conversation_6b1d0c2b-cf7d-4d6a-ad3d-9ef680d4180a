{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { GhooriLearningRoutes } from './ghoori-learning.routing';\nimport { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let GhooriLearningModule = /*#__PURE__*/(() => {\n  class GhooriLearningModule {}\n\n  GhooriLearningModule.ɵfac = function GhooriLearningModule_Factory(t) {\n    return new (t || GhooriLearningModule)();\n  };\n\n  GhooriLearningModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: GhooriLearningModule\n  });\n  GhooriLearningModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(GhooriLearningRoutes), SharedModule, WebLayoutModule, NgxExtendedPdfViewerModule]]\n  });\n  return GhooriLearningModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}