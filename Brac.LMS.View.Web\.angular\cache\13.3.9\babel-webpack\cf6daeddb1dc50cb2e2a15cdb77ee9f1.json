{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { CourseDetailsPreviewRoutes } from './course-details-preview.routing';\nimport { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let CourseDetailsPreviewModule = /*#__PURE__*/(() => {\n  class CourseDetailsPreviewModule {}\n\n  CourseDetailsPreviewModule.ɵfac = function CourseDetailsPreviewModule_Factory(t) {\n    return new (t || CourseDetailsPreviewModule)();\n  };\n\n  CourseDetailsPreviewModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CourseDetailsPreviewModule\n  });\n  CourseDetailsPreviewModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(CourseDetailsPreviewRoutes), SharedModule, WebLayoutModule, NgxExtendedPdfViewerModule]]\n  });\n  return CourseDetailsPreviewModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}