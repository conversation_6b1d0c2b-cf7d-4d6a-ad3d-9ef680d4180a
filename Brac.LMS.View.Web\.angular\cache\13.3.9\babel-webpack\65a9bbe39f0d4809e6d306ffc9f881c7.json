{"ast": null, "code": "import { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function filter(predicate, thisArg) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(new OperatorSubscriber(subscriber, value => predicate.call(thisArg, value, index++) && subscriber.next(value)));\n  });\n} //# sourceMappingURL=filter.js.map", "map": null, "metadata": {}, "sourceType": "module"}