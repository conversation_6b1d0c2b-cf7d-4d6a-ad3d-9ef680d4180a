{"ast": null, "code": "import { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nimport { arrRemove } from './util/arrRemove';\nexport class Subscription {\n  constructor(initialTeardown) {\n    this.initialTeardown = initialTeardown;\n    this.closed = false;\n    this._parentage = null;\n    this._teardowns = null;\n  }\n\n  unsubscribe() {\n    let errors;\n\n    if (!this.closed) {\n      this.closed = true;\n      const {\n        _parentage\n      } = this;\n\n      if (_parentage) {\n        this._parentage = null;\n\n        if (Array.isArray(_parentage)) {\n          for (const parent of _parentage) {\n            parent.remove(this);\n          }\n        } else {\n          _parentage.remove(this);\n        }\n      }\n\n      const {\n        initialTeardown\n      } = this;\n\n      if (isFunction(initialTeardown)) {\n        try {\n          initialTeardown();\n        } catch (e) {\n          errors = e instanceof UnsubscriptionError ? e.errors : [e];\n        }\n      }\n\n      const {\n        _teardowns\n      } = this;\n\n      if (_teardowns) {\n        this._teardowns = null;\n\n        for (const teardown of _teardowns) {\n          try {\n            execTeardown(teardown);\n          } catch (err) {\n            errors = errors !== null && errors !== void 0 ? errors : [];\n\n            if (err instanceof UnsubscriptionError) {\n              errors = [...errors, ...err.errors];\n            } else {\n              errors.push(err);\n            }\n          }\n        }\n      }\n\n      if (errors) {\n        throw new UnsubscriptionError(errors);\n      }\n    }\n  }\n\n  add(teardown) {\n    var _a;\n\n    if (teardown && teardown !== this) {\n      if (this.closed) {\n        execTeardown(teardown);\n      } else {\n        if (teardown instanceof Subscription) {\n          if (teardown.closed || teardown._hasParent(this)) {\n            return;\n          }\n\n          teardown._addParent(this);\n        }\n\n        (this._teardowns = (_a = this._teardowns) !== null && _a !== void 0 ? _a : []).push(teardown);\n      }\n    }\n  }\n\n  _hasParent(parent) {\n    const {\n      _parentage\n    } = this;\n    return _parentage === parent || Array.isArray(_parentage) && _parentage.includes(parent);\n  }\n\n  _addParent(parent) {\n    const {\n      _parentage\n    } = this;\n    this._parentage = Array.isArray(_parentage) ? (_parentage.push(parent), _parentage) : _parentage ? [_parentage, parent] : parent;\n  }\n\n  _removeParent(parent) {\n    const {\n      _parentage\n    } = this;\n\n    if (_parentage === parent) {\n      this._parentage = null;\n    } else if (Array.isArray(_parentage)) {\n      arrRemove(_parentage, parent);\n    }\n  }\n\n  remove(teardown) {\n    const {\n      _teardowns\n    } = this;\n    _teardowns && arrRemove(_teardowns, teardown);\n\n    if (teardown instanceof Subscription) {\n      teardown._removeParent(this);\n    }\n  }\n\n}\n\nSubscription.EMPTY = (() => {\n  const empty = new Subscription();\n  empty.closed = true;\n  return empty;\n})();\n\nexport const EMPTY_SUBSCRIPTION = Subscription.EMPTY;\nexport function isSubscription(value) {\n  return value instanceof Subscription || value && 'closed' in value && isFunction(value.remove) && isFunction(value.add) && isFunction(value.unsubscribe);\n}\n\nfunction execTeardown(teardown) {\n  if (isFunction(teardown)) {\n    teardown();\n  } else {\n    teardown.unsubscribe();\n  }\n} //# sourceMappingURL=Subscription.js.map", "map": null, "metadata": {}, "sourceType": "module"}