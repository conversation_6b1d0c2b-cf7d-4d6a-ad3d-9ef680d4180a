{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { ForumRoutes } from './forum.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let ForumModule = /*#__PURE__*/(() => {\n  class ForumModule {}\n\n  ForumModule.ɵfac = function ForumModule_Factory(t) {\n    return new (t || ForumModule)();\n  };\n\n  ForumModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ForumModule\n  });\n  ForumModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(ForumRoutes), SharedModule, WebLayoutModule]]\n  });\n  return ForumModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}