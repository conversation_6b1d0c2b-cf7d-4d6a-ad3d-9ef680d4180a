{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { executeSchedule } from '../util/executeSchedule';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function mergeInternals(source, subscriber, project, concurrent, onBeforeNext, expand, innerSubScheduler, additionalTeardown) {\n  const buffer = [];\n  let active = 0;\n  let index = 0;\n  let isComplete = false;\n\n  const checkComplete = () => {\n    if (isComplete && !buffer.length && !active) {\n      subscriber.complete();\n    }\n  };\n\n  const outerNext = value => active < concurrent ? doInnerSub(value) : buffer.push(value);\n\n  const doInnerSub = value => {\n    expand && subscriber.next(value);\n    active++;\n    let innerComplete = false;\n    innerFrom(project(value, index++)).subscribe(new OperatorSubscriber(subscriber, innerValue => {\n      onBeforeNext === null || onBeforeNext === void 0 ? void 0 : onBeforeNext(innerValue);\n\n      if (expand) {\n        outerNext(innerValue);\n      } else {\n        subscriber.next(innerValue);\n      }\n    }, () => {\n      innerComplete = true;\n    }, undefined, () => {\n      if (innerComplete) {\n        try {\n          active--;\n\n          while (buffer.length && active < concurrent) {\n            const bufferedValue = buffer.shift();\n\n            if (innerSubScheduler) {\n              executeSchedule(subscriber, innerSubScheduler, () => doInnerSub(bufferedValue));\n            } else {\n              doInnerSub(bufferedValue);\n            }\n          }\n\n          checkComplete();\n        } catch (err) {\n          subscriber.error(err);\n        }\n      }\n    }));\n  };\n\n  source.subscribe(new OperatorSubscriber(subscriber, outerNext, () => {\n    isComplete = true;\n    checkComplete();\n  }));\n  return () => {\n    additionalTeardown === null || additionalTeardown === void 0 ? void 0 : additionalTeardown();\n  };\n} //# sourceMappingURL=mergeInternals.js.map", "map": null, "metadata": {}, "sourceType": "module"}