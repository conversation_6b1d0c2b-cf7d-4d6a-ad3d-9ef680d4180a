{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function switchMap(project, resultSelector) {\n  return operate((source, subscriber) => {\n    let innerSubscriber = null;\n    let index = 0;\n    let isComplete = false;\n\n    const checkComplete = () => isComplete && !innerSubscriber && subscriber.complete();\n\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      innerSubscriber === null || innerSubscriber === void 0 ? void 0 : innerSubscriber.unsubscribe();\n      let innerIndex = 0;\n      const outerIndex = index++;\n      innerFrom(project(value, outerIndex)).subscribe(innerSubscriber = new OperatorSubscriber(subscriber, innerValue => subscriber.next(resultSelector ? resultSelector(value, innerValue, outerIndex, innerIndex++) : innerValue), () => {\n        innerSubscriber = null;\n        checkComplete();\n      }));\n    }, () => {\n      isComplete = true;\n      checkComplete();\n    }));\n  });\n} //# sourceMappingURL=switchMap.js.map", "map": null, "metadata": {}, "sourceType": "module"}