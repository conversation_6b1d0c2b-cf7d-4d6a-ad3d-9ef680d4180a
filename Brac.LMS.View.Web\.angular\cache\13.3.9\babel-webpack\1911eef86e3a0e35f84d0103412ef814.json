{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Input, Injectable, Component, HostBinding, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nfunction TabsetComponent_li_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵlistener(\"click\", function TabsetComponent_li_1_span_4_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const tabz_r1 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      $event.preventDefault();\n      return ctx_r4.removeTab(tabz_r1);\n    });\n    i0.ɵɵtext(1, \" \\u274C\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function (a1) {\n  return [\"nav-item\", a1];\n};\n\nfunction TabsetComponent_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 3);\n    i0.ɵɵlistener(\"keydown\", function TabsetComponent_li_1_Template_li_keydown_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r8);\n      const i_r2 = restoredCtx.index;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return ctx_r7.keyNavActions($event, i_r2);\n    });\n    i0.ɵɵelementStart(1, \"a\", 4);\n    i0.ɵɵlistener(\"click\", function TabsetComponent_li_1_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r8);\n      const tabz_r1 = restoredCtx.$implicit;\n      return tabz_r1.active = true;\n    });\n    i0.ɵɵelementStart(2, \"span\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TabsetComponent_li_1_span_4_Template, 2, 0, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tabz_r1 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", tabz_r1.active)(\"disabled\", tabz_r1.disabled);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c0, tabz_r1.customClass || \"\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", tabz_r1.active)(\"disabled\", tabz_r1.disabled);\n    i0.ɵɵattribute(\"aria-controls\", tabz_r1.id ? tabz_r1.id : \"\")(\"aria-selected\", !!tabz_r1.active)(\"id\", tabz_r1.id ? tabz_r1.id + \"-link\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTransclude\", tabz_r1.headingRef);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(tabz_r1.heading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", tabz_r1.removable);\n  }\n}\n\nconst _c1 = [\"*\"];\nlet NgTranscludeDirective = /*#__PURE__*/(() => {\n  class NgTranscludeDirective {\n    constructor(viewRef) {\n      this.viewRef = viewRef;\n    }\n\n    set ngTransclude(templateRef) {\n      this._ngTransclude = templateRef;\n\n      if (templateRef) {\n        this.viewRef.createEmbeddedView(templateRef);\n      }\n    } // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\n    get ngTransclude() {\n      return this._ngTransclude;\n    }\n\n  }\n\n  NgTranscludeDirective.ɵfac = function NgTranscludeDirective_Factory(t) {\n    return new (t || NgTranscludeDirective)(i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n  };\n\n  NgTranscludeDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgTranscludeDirective,\n    selectors: [[\"\", \"ngTransclude\", \"\"]],\n    inputs: {\n      ngTransclude: \"ngTransclude\"\n    }\n  });\n  return NgTranscludeDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet TabsetConfig = /*#__PURE__*/(() => {\n  class TabsetConfig {\n    constructor() {\n      /** provides default navigation context class: 'tabs' or 'pills' */\n      this.type = 'tabs';\n      /** provides possibility to set keyNavigations enable or disable, by default is enable */\n\n      this.isKeysAllowed = true;\n      /** aria label for tab list */\n\n      this.ariaLabel = 'Tabs';\n    }\n\n  }\n\n  TabsetConfig.ɵfac = function TabsetConfig_Factory(t) {\n    return new (t || TabsetConfig)();\n  };\n\n  TabsetConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TabsetConfig,\n    factory: TabsetConfig.ɵfac,\n    providedIn: 'root'\n  });\n  return TabsetConfig;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})(); // todo: add active event to tab\n// todo: fix? mixing static and dynamic tabs position tabs in order of creation\n\n\nlet TabsetComponent = /*#__PURE__*/(() => {\n  class TabsetComponent {\n    constructor(config, renderer, elementRef) {\n      this.renderer = renderer;\n      this.elementRef = elementRef;\n      this.clazz = true;\n      this.tabs = [];\n      this.classMap = {};\n      /** aria label for tab list */\n\n      this.ariaLabel = 'Tabs';\n      this.isDestroyed = false;\n      this._vertical = false;\n      this._justified = false;\n      this._type = 'tabs';\n      this._isKeysAllowed = true;\n      Object.assign(this, config);\n    }\n    /** if true tabs will be placed vertically */\n\n\n    get vertical() {\n      return this._vertical;\n    }\n\n    set vertical(value) {\n      this._vertical = value;\n      this.setClassMap();\n    }\n    /** if true tabs fill the container and have a consistent width */\n\n\n    get justified() {\n      return this._justified;\n    }\n\n    set justified(value) {\n      this._justified = value;\n      this.setClassMap();\n    }\n    /** navigation context class: 'tabs' or 'pills' */\n\n\n    get type() {\n      return this._type;\n    }\n\n    set type(value) {\n      this._type = value;\n      this.setClassMap();\n    }\n\n    get isKeysAllowed() {\n      return this._isKeysAllowed;\n    }\n\n    set isKeysAllowed(value) {\n      this._isKeysAllowed = value;\n    }\n\n    ngOnDestroy() {\n      this.isDestroyed = true;\n    }\n\n    addTab(tab) {\n      this.tabs.push(tab);\n      tab.active = this.tabs.length === 1 && !tab.active;\n    }\n\n    removeTab(tab, options = {\n      reselect: true,\n      emit: true\n    }) {\n      const index = this.tabs.indexOf(tab);\n\n      if (index === -1 || this.isDestroyed) {\n        return;\n      } // Select a new tab if the tab to be removed is selected and not destroyed\n\n\n      if (options.reselect && tab.active && this.hasAvailableTabs(index)) {\n        const newActiveIndex = this.getClosestTabIndex(index);\n        this.tabs[newActiveIndex].active = true;\n      }\n\n      if (options.emit) {\n        tab.removed.emit(tab);\n      }\n\n      this.tabs.splice(index, 1);\n\n      if (tab.elementRef.nativeElement.parentNode) {\n        this.renderer.removeChild(tab.elementRef.nativeElement.parentNode, tab.elementRef.nativeElement);\n      }\n    }\n\n    keyNavActions(event, index) {\n      if (!this.isKeysAllowed) {\n        return;\n      }\n\n      const list = Array.from(this.elementRef.nativeElement.querySelectorAll('.nav-link')); // const activeElList = list.filter((el: HTMLElement) => !el.classList.contains('disabled'));\n\n      if (event.keyCode === 13 || event.key === 'Enter' || event.keyCode === 32 || event.key === 'Space') {\n        event.preventDefault();\n        const currentTab = list[index % list.length];\n        currentTab.click();\n        return;\n      }\n\n      if (event.keyCode === 39 || event.key === 'RightArrow') {\n        let nextTab;\n        let shift = 1;\n\n        do {\n          nextTab = list[(index + shift) % list.length];\n          shift++;\n        } while (nextTab.classList.contains('disabled'));\n\n        nextTab.focus();\n        return;\n      }\n\n      if (event.keyCode === 37 || event.key === 'LeftArrow') {\n        let previousTab;\n        let shift = 1;\n        let i = index;\n\n        do {\n          if (i - shift < 0) {\n            i = list.length - 1;\n            previousTab = list[i];\n            shift = 0;\n          } else {\n            previousTab = list[i - shift];\n          }\n\n          shift++;\n        } while (previousTab.classList.contains('disabled'));\n\n        previousTab.focus();\n        return;\n      }\n\n      if (event.keyCode === 36 || event.key === 'Home') {\n        event.preventDefault();\n        let firstTab;\n        let shift = 0;\n\n        do {\n          firstTab = list[shift % list.length];\n          shift++;\n        } while (firstTab.classList.contains('disabled'));\n\n        firstTab.focus();\n        return;\n      }\n\n      if (event.keyCode === 35 || event.key === 'End') {\n        event.preventDefault();\n        let lastTab;\n        let shift = 1;\n        let i = index;\n\n        do {\n          if (i - shift < 0) {\n            i = list.length - 1;\n            lastTab = list[i];\n            shift = 0;\n          } else {\n            lastTab = list[i - shift];\n          }\n\n          shift++;\n        } while (lastTab.classList.contains('disabled'));\n\n        lastTab.focus();\n        return;\n      }\n\n      if (event.keyCode === 46 || event.key === 'Delete') {\n        if (this.tabs[index].removable) {\n          this.removeTab(this.tabs[index]);\n\n          if (list[index + 1]) {\n            list[(index + 1) % list.length].focus();\n            return;\n          }\n\n          if (list[list.length - 1]) {\n            list[0].focus();\n          }\n        }\n      }\n    }\n\n    getClosestTabIndex(index) {\n      const tabsLength = this.tabs.length;\n\n      if (!tabsLength) {\n        return -1;\n      }\n\n      for (let step = 1; step <= tabsLength; step += 1) {\n        const prevIndex = index - step;\n        const nextIndex = index + step;\n\n        if (this.tabs[prevIndex] && !this.tabs[prevIndex].disabled) {\n          return prevIndex;\n        }\n\n        if (this.tabs[nextIndex] && !this.tabs[nextIndex].disabled) {\n          return nextIndex;\n        }\n      }\n\n      return -1;\n    }\n\n    hasAvailableTabs(index) {\n      const tabsLength = this.tabs.length;\n\n      if (!tabsLength) {\n        return false;\n      }\n\n      for (let i = 0; i < tabsLength; i += 1) {\n        if (!this.tabs[i].disabled && i !== index) {\n          return true;\n        }\n      }\n\n      return false;\n    }\n\n    setClassMap() {\n      this.classMap = {\n        'nav-stacked': this.vertical,\n        'flex-column': this.vertical,\n        'nav-justified': this.justified,\n        [`nav-${this.type}`]: true\n      };\n    }\n\n  }\n\n  TabsetComponent.ɵfac = function TabsetComponent_Factory(t) {\n    return new (t || TabsetComponent)(i0.ɵɵdirectiveInject(TabsetConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n\n  TabsetComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TabsetComponent,\n    selectors: [[\"tabset\"]],\n    hostVars: 2,\n    hostBindings: function TabsetComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"tab-container\", ctx.clazz);\n      }\n    },\n    inputs: {\n      vertical: \"vertical\",\n      justified: \"justified\",\n      type: \"type\"\n    },\n    ngContentSelectors: _c1,\n    decls: 4,\n    vars: 3,\n    consts: [[\"role\", \"tablist\", 1, \"nav\", 3, \"ngClass\", \"click\"], [3, \"ngClass\", \"active\", \"disabled\", \"keydown\", 4, \"ngFor\", \"ngForOf\"], [1, \"tab-content\"], [3, \"ngClass\", \"keydown\"], [\"href\", \"javascript:void(0);\", \"role\", \"tab\", 1, \"nav-link\", 3, \"click\"], [3, \"ngTransclude\"], [\"class\", \"bs-remove-tab\", 3, \"click\", 4, \"ngIf\"], [1, \"bs-remove-tab\", 3, \"click\"]],\n    template: function TabsetComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"ul\", 0);\n        i0.ɵɵlistener(\"click\", function TabsetComponent_Template_ul_click_0_listener($event) {\n          return $event.preventDefault();\n        });\n        i0.ɵɵtemplate(1, TabsetComponent_li_1_Template, 5, 17, \"li\", 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵprojection(3);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", ctx.classMap);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n      }\n    },\n    directives: [i2.NgClass, i2.NgForOf, NgTranscludeDirective, i2.NgIf],\n    styles: [\"[_nghost-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-item.disabled[_ngcontent-%COMP%]   a.disabled[_ngcontent-%COMP%]{cursor:default}\"]\n  });\n  return TabsetComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet TabDirective = /*#__PURE__*/(() => {\n  class TabDirective {\n    constructor(tabset, elementRef, renderer) {\n      this.elementRef = elementRef;\n      this.renderer = renderer;\n      /** if true tab can not be activated */\n\n      this.disabled = false;\n      /** if true tab can be removable, additional button will appear */\n\n      this.removable = false;\n      /** fired when tab became active, $event:Tab equals to selected instance of Tab component */\n\n      this.selectTab = new EventEmitter();\n      /** fired when tab became inactive, $event:Tab equals to deselected instance of Tab component */\n\n      this.deselect = new EventEmitter();\n      /** fired before tab will be removed, $event:Tab equals to instance of removed tab */\n\n      this.removed = new EventEmitter();\n      this.addClass = true;\n      this.role = 'tabpanel';\n      this._active = false;\n      this._customClass = '';\n      this.tabset = tabset;\n      this.tabset.addTab(this);\n    }\n    /** if set, will be added to the tab's class attribute. Multiple classes are supported. */\n\n\n    get customClass() {\n      return this._customClass;\n    }\n\n    set customClass(customClass) {\n      if (this.customClass) {\n        this.customClass.split(' ').forEach(cssClass => {\n          this.renderer.removeClass(this.elementRef.nativeElement, cssClass);\n        });\n      }\n\n      this._customClass = customClass ? customClass.trim() : '';\n\n      if (this.customClass) {\n        this.customClass.split(' ').forEach(cssClass => {\n          this.renderer.addClass(this.elementRef.nativeElement, cssClass);\n        });\n      }\n    }\n    /** tab active state toggle */\n\n\n    get active() {\n      return this._active;\n    }\n\n    set active(active) {\n      if (this._active === active) {\n        return;\n      }\n\n      if (this.disabled && active || !active) {\n        if (this._active && !active) {\n          this.deselect.emit(this);\n          this._active = active;\n        }\n\n        return;\n      }\n\n      this._active = active;\n      this.selectTab.emit(this);\n      this.tabset.tabs.forEach(tab => {\n        if (tab !== this) {\n          tab.active = false;\n        }\n      });\n    }\n\n    get ariaLabelledby() {\n      return this.id ? `${this.id}-link` : '';\n    }\n\n    ngOnInit() {\n      this.removable = !!this.removable;\n    }\n\n    ngOnDestroy() {\n      this.tabset.removeTab(this, {\n        reselect: false,\n        emit: false\n      });\n    }\n\n  }\n\n  TabDirective.ɵfac = function TabDirective_Factory(t) {\n    return new (t || TabDirective)(i0.ɵɵdirectiveInject(TabsetComponent), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n\n  TabDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TabDirective,\n    selectors: [[\"tab\"], [\"\", \"tab\", \"\"]],\n    hostVars: 7,\n    hostBindings: function TabDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx.id)(\"role\", ctx.role)(\"aria-labelledby\", ctx.ariaLabelledby);\n        i0.ɵɵclassProp(\"active\", ctx.active)(\"tab-pane\", ctx.addClass);\n      }\n    },\n    inputs: {\n      heading: \"heading\",\n      id: \"id\",\n      disabled: \"disabled\",\n      removable: \"removable\",\n      customClass: \"customClass\",\n      active: \"active\"\n    },\n    outputs: {\n      selectTab: \"selectTab\",\n      deselect: \"deselect\",\n      removed: \"removed\"\n    },\n    exportAs: [\"tab\"]\n  });\n  return TabDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Should be used to mark <ng-template> element as a template for tab heading */\n\n\nlet TabHeadingDirective = /*#__PURE__*/(() => {\n  class TabHeadingDirective {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    constructor(templateRef, tab) {\n      tab.headingRef = templateRef;\n    }\n\n  }\n\n  TabHeadingDirective.ɵfac = function TabHeadingDirective_Factory(t) {\n    return new (t || TabHeadingDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(TabDirective));\n  };\n\n  TabHeadingDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TabHeadingDirective,\n    selectors: [[\"\", \"tabHeading\", \"\"]]\n  });\n  return TabHeadingDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet TabsModule = /*#__PURE__*/(() => {\n  class TabsModule {\n    static forRoot() {\n      return {\n        ngModule: TabsModule,\n        providers: []\n      };\n    }\n\n  }\n\n  TabsModule.ɵfac = function TabsModule_Factory(t) {\n    return new (t || TabsModule)();\n  };\n\n  TabsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TabsModule\n  });\n  TabsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule]]\n  });\n  return TabsModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { NgTranscludeDirective, TabDirective, TabHeadingDirective, TabsModule, TabsetComponent, TabsetConfig }; //# sourceMappingURL=ngx-bootstrap-tabs.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}