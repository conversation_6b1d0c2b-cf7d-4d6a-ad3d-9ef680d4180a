{"ast": null, "code": "import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function sample(notifier) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let lastValue = null;\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      lastValue = value;\n    }));\n\n    const emit = () => {\n      if (hasValue) {\n        hasValue = false;\n        const value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n\n    notifier.subscribe(new OperatorSubscriber(subscriber, emit, noop));\n  });\n} //# sourceMappingURL=sample.js.map", "map": null, "metadata": {}, "sourceType": "module"}