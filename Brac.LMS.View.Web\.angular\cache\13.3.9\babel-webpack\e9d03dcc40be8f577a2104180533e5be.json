{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { ExternalCoursesRoutes } from './external-courses.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let ExternalCoursesModule = /*#__PURE__*/(() => {\n  class ExternalCoursesModule {}\n\n  ExternalCoursesModule.ɵfac = function ExternalCoursesModule_Factory(t) {\n    return new (t || ExternalCoursesModule)();\n  };\n\n  ExternalCoursesModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ExternalCoursesModule\n  });\n  ExternalCoursesModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(ExternalCoursesRoutes), SharedModule, WebLayoutModule]]\n  });\n  return ExternalCoursesModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}