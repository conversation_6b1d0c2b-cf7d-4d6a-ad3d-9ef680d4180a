{"ast": null, "code": "import { Observable } from '../Observable';\nexport const EMPTY = new Observable(subscriber => subscriber.complete());\nexport function empty(scheduler) {\n  return scheduler ? emptyScheduled(scheduler) : EMPTY;\n}\n\nfunction emptyScheduled(scheduler) {\n  return new Observable(subscriber => scheduler.schedule(() => subscriber.complete()));\n} //# sourceMappingURL=empty.js.map", "map": null, "metadata": {}, "sourceType": "module"}