{"ast": null, "code": "import { <PERSON><PERSON>, EventEmitter, ChangeDetectorRef, NgZone, InjectionToken, Optional, Inject, NgModule } from '@angular/core';\nimport * as moment from 'moment';\nimport { isMoment, duration, relativeTimeThreshold, unix, utc, parseZone, isDate, locale } from 'moment';\n/* ngx-moment (c) 2015, 2016 Uri Shaked / MIT Licence */\n\nimport * as ɵngcc0 from '@angular/core';\nconst momentConstructor = moment;\nlet AddPipe = /*#__PURE__*/(() => {\n  class AddPipe {\n    transform(value, amount, unit) {\n      if (typeof amount === 'undefined' || typeof amount === 'number' && typeof unit === 'undefined') {\n        throw new Error('AddPipe: missing required arguments');\n      }\n\n      return momentConstructor(value).add(amount, unit);\n    }\n\n  }\n\n  AddPipe.ɵfac = function AddPipe_Factory(t) {\n    return new (t || AddPipe)();\n  };\n\n  AddPipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amAdd\",\n    type: AddPipe,\n    pure: true\n  });\n  return AddPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/* ngx-moment (c) 2015, 2016 Uri Shaked / MIT Licence */\n\n\nconst momentConstructor$1 = moment;\nlet CalendarPipe = /*#__PURE__*/(() => {\n  class CalendarPipe {\n    constructor(cdRef, ngZone) {\n      this.cdRef = cdRef;\n      this.ngZone = ngZone; // using a single static timer for all instances of this pipe for performance reasons\n\n      CalendarPipe.initTimer(ngZone);\n      CalendarPipe.refs++; // values such as Today will need to be replaced with Yesterday after midnight,\n      // so make sure we subscribe to an EventEmitter that we set up to emit at midnight\n\n      this.midnightSub = CalendarPipe.midnight.subscribe(() => {\n        this.ngZone.run(() => this.cdRef.markForCheck());\n      });\n    }\n\n    transform(value, ...args) {\n      let formats = null;\n      let referenceTime = null;\n\n      for (let i = 0, len = args.length; i < len; i++) {\n        if (args[i] !== null) {\n          if (typeof args[i] === 'object' && !isMoment(args[i])) {\n            formats = args[i];\n          } else {\n            referenceTime = momentConstructor$1(args[i]);\n          }\n        }\n      }\n\n      return momentConstructor$1(value).calendar(referenceTime, formats);\n    }\n\n    ngOnDestroy() {\n      if (CalendarPipe.refs > 0) {\n        CalendarPipe.refs--;\n      }\n\n      if (CalendarPipe.refs === 0) {\n        CalendarPipe.removeTimer();\n      }\n\n      this.midnightSub.unsubscribe();\n    }\n\n    static initTimer(ngZone) {\n      // initialize the timer\n      if (!CalendarPipe.midnight) {\n        CalendarPipe.midnight = new EventEmitter();\n\n        if (typeof window !== 'undefined') {\n          const timeToUpdate = CalendarPipe._getMillisecondsUntilUpdate();\n\n          CalendarPipe.timer = ngZone.runOutsideAngular(() => {\n            return window.setTimeout(() => {\n              // emit the current date\n              CalendarPipe.midnight.emit(new Date()); // refresh the timer\n\n              CalendarPipe.removeTimer();\n              CalendarPipe.initTimer(ngZone);\n            }, timeToUpdate);\n          });\n        }\n      }\n    }\n\n    static removeTimer() {\n      if (CalendarPipe.timer) {\n        window.clearTimeout(CalendarPipe.timer);\n        CalendarPipe.timer = null;\n        CalendarPipe.midnight = null;\n      }\n    }\n\n    static _getMillisecondsUntilUpdate() {\n      const now = momentConstructor$1();\n      const tomorrow = momentConstructor$1().startOf('day').add(1, 'days');\n      const timeToMidnight = tomorrow.valueOf() - now.valueOf();\n      return timeToMidnight + 1000; // 1 second after midnight\n    }\n\n  }\n\n  CalendarPipe.ɵfac = function CalendarPipe_Factory(t) {\n    return new (t || CalendarPipe)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef, 16), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.NgZone, 16));\n  };\n\n  CalendarPipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amCalendar\",\n    type: CalendarPipe,\n    pure: false\n  });\n  /**\r\n   * Internal reference counter, so we can clean up when no instances are in use\r\n   */\n\n  CalendarPipe.refs = 0;\n  CalendarPipe.timer = null;\n  CalendarPipe.midnight = null;\n  return CalendarPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/* ngx-moment (c) 2015, 2016 Uri Shaked / MIT Licence */\n\n\nconst momentConstructor$2 = moment;\nlet DateFormatPipe = /*#__PURE__*/(() => {\n  class DateFormatPipe {\n    transform(value, ...args) {\n      if (!value) {\n        return '';\n      }\n\n      return momentConstructor$2(value).format(args[0]);\n    }\n\n  }\n\n  DateFormatPipe.ɵfac = function DateFormatPipe_Factory(t) {\n    return new (t || DateFormatPipe)();\n  };\n\n  DateFormatPipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amDateFormat\",\n    type: DateFormatPipe,\n    pure: true\n  });\n  return DateFormatPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/* ngx-moment (c) 2015, 2016 Uri Shaked / MIT Licence */\n\n\nconst momentConstructor$3 = moment;\nlet DifferencePipe = /*#__PURE__*/(() => {\n  class DifferencePipe {\n    transform(value, otherValue, unit, precision) {\n      const date = momentConstructor$3(value);\n      const date2 = otherValue !== null ? momentConstructor$3(otherValue) : momentConstructor$3();\n      return date.diff(date2, unit, precision);\n    }\n\n  }\n\n  DifferencePipe.ɵfac = function DifferencePipe_Factory(t) {\n    return new (t || DifferencePipe)();\n  };\n\n  DifferencePipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amDifference\",\n    type: DifferencePipe,\n    pure: true\n  });\n  return DifferencePipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst NGX_MOMENT_OPTIONS = new InjectionToken('NGX_MOMENT_OPTIONS');\nlet DurationPipe = /*#__PURE__*/(() => {\n  class DurationPipe {\n    constructor(momentOptions) {\n      this.allowedUnits = ['ss', 's', 'm', 'h', 'd', 'M'];\n\n      this._applyOptions(momentOptions);\n    }\n\n    transform(value, ...args) {\n      if (typeof args === 'undefined' || args.length !== 1) {\n        throw new Error('DurationPipe: missing required time unit argument');\n      }\n\n      return duration(value, args[0]).humanize();\n    }\n\n    _applyOptions(momentOptions) {\n      if (!momentOptions) {\n        return;\n      }\n\n      if (!!momentOptions.relativeTimeThresholdOptions) {\n        const units = Object.keys(momentOptions.relativeTimeThresholdOptions);\n        const filteredUnits = units.filter(unit => this.allowedUnits.indexOf(unit) !== -1);\n        filteredUnits.forEach(unit => {\n          relativeTimeThreshold(unit, momentOptions.relativeTimeThresholdOptions[unit]);\n        });\n      }\n    }\n\n  }\n\n  DurationPipe.ɵfac = function DurationPipe_Factory(t) {\n    return new (t || DurationPipe)(ɵngcc0.ɵɵdirectiveInject(NGX_MOMENT_OPTIONS, 24));\n  };\n\n  DurationPipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amDuration\",\n    type: DurationPipe,\n    pure: true\n  });\n  return DurationPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/* ngx-moment (c) 2015, 2016 Uri Shaked / MIT Licence */\n\n\nlet FromUnixPipe = /*#__PURE__*/(() => {\n  class FromUnixPipe {\n    transform(value, ...args) {\n      return typeof value === 'string' ? unix(parseInt(value, 10)) : unix(value);\n    }\n\n  }\n\n  FromUnixPipe.ɵfac = function FromUnixPipe_Factory(t) {\n    return new (t || FromUnixPipe)();\n  };\n\n  FromUnixPipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amFromUnix\",\n    type: FromUnixPipe,\n    pure: true\n  });\n  return FromUnixPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst momentConstructor$4 = moment;\nlet ParsePipe = /*#__PURE__*/(() => {\n  class ParsePipe {\n    transform(value, formats) {\n      return momentConstructor$4(value, formats);\n    }\n\n  }\n\n  ParsePipe.ɵfac = function ParsePipe_Factory(t) {\n    return new (t || ParsePipe)();\n  };\n\n  ParsePipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amParse\",\n    type: ParsePipe,\n    pure: true\n  });\n  return ParsePipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/* ngx-moment (c) 2015, 2016 Uri Shaked / MIT Licence */\n\n\nlet FromUtcPipe = /*#__PURE__*/(() => {\n  class FromUtcPipe {\n    transform(value, formats, ...args) {\n      return formats ? utc(value, formats) : utc(value);\n    }\n\n  }\n\n  FromUtcPipe.ɵfac = function FromUtcPipe_Factory(t) {\n    return new (t || FromUtcPipe)();\n  };\n\n  FromUtcPipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amFromUtc\",\n    type: FromUtcPipe,\n    pure: true\n  });\n  return FromUtcPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst momentConstructor$5 = moment;\nlet IsAfterPipe = /*#__PURE__*/(() => {\n  class IsAfterPipe {\n    transform(value, otherValue, unit) {\n      return momentConstructor$5(value).isAfter(momentConstructor$5(otherValue), unit);\n    }\n\n  }\n\n  IsAfterPipe.ɵfac = function IsAfterPipe_Factory(t) {\n    return new (t || IsAfterPipe)();\n  };\n\n  IsAfterPipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amIsAfter\",\n    type: IsAfterPipe,\n    pure: true\n  });\n  return IsAfterPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst momentConstructor$6 = moment;\nlet IsBeforePipe = /*#__PURE__*/(() => {\n  class IsBeforePipe {\n    transform(value, otherValue, unit) {\n      return momentConstructor$6(value).isBefore(momentConstructor$6(otherValue), unit);\n    }\n\n  }\n\n  IsBeforePipe.ɵfac = function IsBeforePipe_Factory(t) {\n    return new (t || IsBeforePipe)();\n  };\n\n  IsBeforePipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amIsBefore\",\n    type: IsBeforePipe,\n    pure: true\n  });\n  return IsBeforePipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst momentConstructor$7 = moment;\nlet LocalTimePipe = /*#__PURE__*/(() => {\n  class LocalTimePipe {\n    transform(value) {\n      return momentConstructor$7(value).local();\n    }\n\n  }\n\n  LocalTimePipe.ɵfac = function LocalTimePipe_Factory(t) {\n    return new (t || LocalTimePipe)();\n  };\n\n  LocalTimePipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amLocal\",\n    type: LocalTimePipe,\n    pure: true\n  });\n  return LocalTimePipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})(); // See https://github.com/ng-packagr/ng-packagr/issues/217 for why this is needed:\n\n\nconst momentConstructor$8 = moment;\nlet LocalePipe = /*#__PURE__*/(() => {\n  class LocalePipe {\n    transform(value, locale) {\n      return momentConstructor$8(value).locale(locale);\n    }\n\n  }\n\n  LocalePipe.ɵfac = function LocalePipe_Factory(t) {\n    return new (t || LocalePipe)();\n  };\n\n  LocalePipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amLocale\",\n    type: LocalePipe,\n    pure: true\n  });\n  return LocalePipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet ParseZonePipe = /*#__PURE__*/(() => {\n  class ParseZonePipe {\n    transform(value) {\n      return parseZone(value);\n    }\n\n  }\n\n  ParseZonePipe.ɵfac = function ParseZonePipe_Factory(t) {\n    return new (t || ParseZonePipe)();\n  };\n\n  ParseZonePipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amParseZone\",\n    type: ParseZonePipe,\n    pure: true\n  });\n  return ParseZonePipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/* ngx-moment (c) 2015, 2016 Uri Shaked / MIT Licence */\n\n\nconst momentConstructor$9 = moment;\nlet SubtractPipe = /*#__PURE__*/(() => {\n  class SubtractPipe {\n    transform(value, amount, unit) {\n      if (typeof amount === 'undefined' || typeof amount === 'number' && typeof unit === 'undefined') {\n        throw new Error('SubtractPipe: missing required arguments');\n      }\n\n      return momentConstructor$9(value).subtract(amount, unit);\n    }\n\n  }\n\n  SubtractPipe.ɵfac = function SubtractPipe_Factory(t) {\n    return new (t || SubtractPipe)();\n  };\n\n  SubtractPipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amSubtract\",\n    type: SubtractPipe,\n    pure: true\n  });\n  return SubtractPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/* ngx-moment (c) 2015, 2016 Uri Shaked / MIT Licence */\n\n\nconst momentConstructor$a = moment;\nlet TimeAgoPipe = /*#__PURE__*/(() => {\n  class TimeAgoPipe {\n    constructor(cdRef, ngZone) {\n      this.cdRef = cdRef;\n      this.ngZone = ngZone;\n    }\n\n    format(m) {\n      return m.from(momentConstructor$a(), this.lastOmitSuffix);\n    }\n\n    transform(value, omitSuffix, formatFn) {\n      if (this.hasChanged(value, omitSuffix)) {\n        this.lastTime = this.getTime(value);\n        this.lastValue = value;\n        this.lastOmitSuffix = omitSuffix;\n        this.lastLocale = this.getLocale(value);\n        this.formatFn = formatFn || this.format.bind(this);\n        this.removeTimer();\n        this.createTimer();\n        this.lastText = this.formatFn(momentConstructor$a(value));\n      } else {\n        this.createTimer();\n      }\n\n      return this.lastText;\n    }\n\n    ngOnDestroy() {\n      this.removeTimer();\n    }\n\n    createTimer() {\n      if (this.currentTimer) {\n        return;\n      }\n\n      const momentInstance = momentConstructor$a(this.lastValue);\n      const timeToUpdate = this.getSecondsUntilUpdate(momentInstance) * 1000;\n      this.currentTimer = this.ngZone.runOutsideAngular(() => {\n        if (typeof window !== 'undefined') {\n          return window.setTimeout(() => {\n            this.lastText = this.formatFn(momentConstructor$a(this.lastValue));\n            this.currentTimer = null;\n            this.ngZone.run(() => this.cdRef.markForCheck());\n          }, timeToUpdate);\n        } else {\n          return null;\n        }\n      });\n    }\n\n    removeTimer() {\n      if (this.currentTimer) {\n        window.clearTimeout(this.currentTimer);\n        this.currentTimer = null;\n      }\n    }\n\n    getSecondsUntilUpdate(momentInstance) {\n      const howOld = Math.abs(momentConstructor$a().diff(momentInstance, 'minute'));\n\n      if (howOld < 1) {\n        return 1;\n      } else if (howOld < 60) {\n        return 30;\n      } else if (howOld < 180) {\n        return 300;\n      } else {\n        return 3600;\n      }\n    }\n\n    hasChanged(value, omitSuffix) {\n      return this.getTime(value) !== this.lastTime || this.getLocale(value) !== this.lastLocale || omitSuffix !== this.lastOmitSuffix;\n    }\n\n    getTime(value) {\n      if (isDate(value)) {\n        return value.getTime();\n      } else if (isMoment(value)) {\n        return value.valueOf();\n      } else {\n        return momentConstructor$a(value).valueOf();\n      }\n    }\n\n    getLocale(value) {\n      return isMoment(value) ? value.locale() : locale();\n    }\n\n  }\n\n  TimeAgoPipe.ɵfac = function TimeAgoPipe_Factory(t) {\n    return new (t || TimeAgoPipe)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef, 16), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.NgZone, 16));\n  };\n\n  TimeAgoPipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amTimeAgo\",\n    type: TimeAgoPipe,\n    pure: false\n  });\n  return TimeAgoPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst momentConstructor$b = moment;\nlet UtcPipe = /*#__PURE__*/(() => {\n  class UtcPipe {\n    transform(value) {\n      return momentConstructor$b(value).utc();\n    }\n\n  }\n\n  UtcPipe.ɵfac = function UtcPipe_Factory(t) {\n    return new (t || UtcPipe)();\n  };\n\n  UtcPipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"amUtc\",\n    type: UtcPipe,\n    pure: true\n  });\n  return UtcPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst ANGULAR_MOMENT_PIPES = [AddPipe, CalendarPipe, DateFormatPipe, DifferencePipe, DurationPipe, FromUnixPipe, ParsePipe, SubtractPipe, TimeAgoPipe, UtcPipe, FromUtcPipe, LocalTimePipe, LocalePipe, ParseZonePipe, IsBeforePipe, IsAfterPipe];\nlet MomentModule = /*#__PURE__*/(() => {\n  class MomentModule {\n    static forRoot(options) {\n      return {\n        ngModule: MomentModule,\n        providers: [{\n          provide: NGX_MOMENT_OPTIONS,\n          useValue: Object.assign({}, options)\n        }]\n      };\n    }\n\n  }\n\n  MomentModule.ɵfac = function MomentModule_Factory(t) {\n    return new (t || MomentModule)();\n  };\n\n  MomentModule.ɵmod = /*@__PURE__*/ɵngcc0.ɵɵdefineNgModule({\n    type: MomentModule\n  });\n  MomentModule.ɵinj = /*@__PURE__*/ɵngcc0.ɵɵdefineInjector({});\n  return MomentModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(MomentModule, {\n    declarations: [AddPipe, CalendarPipe, DateFormatPipe, DifferencePipe, DurationPipe, FromUnixPipe, ParsePipe, SubtractPipe, TimeAgoPipe, UtcPipe, FromUtcPipe, LocalTimePipe, LocalePipe, ParseZonePipe, IsBeforePipe, IsAfterPipe],\n    exports: [AddPipe, CalendarPipe, DateFormatPipe, DifferencePipe, DurationPipe, FromUnixPipe, ParsePipe, SubtractPipe, TimeAgoPipe, UtcPipe, FromUtcPipe, LocalTimePipe, LocalePipe, ParseZonePipe, IsBeforePipe, IsAfterPipe]\n  });\n})();\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\n\nexport { AddPipe, CalendarPipe, DateFormatPipe, DifferencePipe, DurationPipe, FromUnixPipe, FromUtcPipe, IsAfterPipe, IsBeforePipe, LocalTimePipe, LocalePipe, MomentModule, NGX_MOMENT_OPTIONS, ParsePipe, ParseZonePipe, SubtractPipe, TimeAgoPipe, UtcPipe }; //# sourceMappingURL=ngx-moment.js.map", "map": null, "metadata": {}, "sourceType": "module"}