{"ast": null, "code": "/**\n * @license Angular v13.1.3\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport * as i0 from '@angular/core';\nimport { ViewEncapsulation, Injectable, Inject, InjectionToken, RendererFactory2, NgZone, NgModule } from '@angular/core';\nimport { ɵDomRendererFactory2, BrowserModule } from '@angular/platform-browser';\nimport { AnimationBuilder, sequence, AnimationFactory } from '@angular/animations';\nimport * as i1 from '@angular/animations/browser';\nimport { ɵAnimationEngine, ɵsupportsWebAnimations, ɵWebAnimationsDriver, ɵCssKeyframesDriver, ɵWebAnimationsStyleNormalizer, ɵAnimationStyleNormalizer, AnimationDriver, ɵNoopAnimationDriver } from '@angular/animations/browser';\nimport { DOCUMENT } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nlet BrowserAnimationBuilder = /*#__PURE__*/(() => {\n  class BrowserAnimationBuilder extends AnimationBuilder {\n    constructor(rootRenderer, doc) {\n      super();\n      this._nextAnimationId = 0;\n      const typeData = {\n        id: '0',\n        encapsulation: ViewEncapsulation.None,\n        styles: [],\n        data: {\n          animation: []\n        }\n      };\n      this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n    }\n\n    build(animation) {\n      const id = this._nextAnimationId.toString();\n\n      this._nextAnimationId++;\n      const entry = Array.isArray(animation) ? sequence(animation) : animation;\n      issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n      return new BrowserAnimationFactory(id, this._renderer);\n    }\n\n  }\n\n  BrowserAnimationBuilder.ɵfac = function BrowserAnimationBuilder_Factory(t) {\n    return new (t || BrowserAnimationBuilder)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(DOCUMENT));\n  };\n\n  BrowserAnimationBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BrowserAnimationBuilder,\n    factory: BrowserAnimationBuilder.ɵfac\n  });\n  return BrowserAnimationBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nclass BrowserAnimationFactory extends AnimationFactory {\n  constructor(_id, _renderer) {\n    super();\n    this._id = _id;\n    this._renderer = _renderer;\n  }\n\n  create(element, options) {\n    return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n  }\n\n}\n\nclass RendererAnimationPlayer {\n  constructor(id, element, options, _renderer) {\n    this.id = id;\n    this.element = element;\n    this._renderer = _renderer;\n    this.parentPlayer = null;\n    this._started = false;\n    this.totalTime = 0;\n\n    this._command('create', options);\n  }\n\n  _listen(eventName, callback) {\n    return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n  }\n\n  _command(command, ...args) {\n    return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n  }\n\n  onDone(fn) {\n    this._listen('done', fn);\n  }\n\n  onStart(fn) {\n    this._listen('start', fn);\n  }\n\n  onDestroy(fn) {\n    this._listen('destroy', fn);\n  }\n\n  init() {\n    this._command('init');\n  }\n\n  hasStarted() {\n    return this._started;\n  }\n\n  play() {\n    this._command('play');\n\n    this._started = true;\n  }\n\n  pause() {\n    this._command('pause');\n  }\n\n  restart() {\n    this._command('restart');\n  }\n\n  finish() {\n    this._command('finish');\n  }\n\n  destroy() {\n    this._command('destroy');\n  }\n\n  reset() {\n    this._command('reset');\n\n    this._started = false;\n  }\n\n  setPosition(p) {\n    this._command('setPosition', p);\n  }\n\n  getPosition() {\n    var _a, _b;\n\n    return (_b = (_a = this._renderer.engine.players[+this.id]) === null || _a === void 0 ? void 0 : _a.getPosition()) !== null && _b !== void 0 ? _b : 0;\n  }\n\n}\n\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n  return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nlet AnimationRendererFactory = /*#__PURE__*/(() => {\n  class AnimationRendererFactory {\n    constructor(delegate, engine, _zone) {\n      this.delegate = delegate;\n      this.engine = engine;\n      this._zone = _zone;\n      this._currentId = 0;\n      this._microtaskId = 1;\n      this._animationCallbacksBuffer = [];\n      this._rendererCache = new Map();\n      this._cdRecurDepth = 0;\n      this.promise = Promise.resolve(0);\n\n      engine.onRemovalComplete = (element, delegate) => {\n        // Note: if a component element has a leave animation, and a host leave animation,\n        // the view engine will call `removeChild` for the parent\n        // component renderer as well as for the child component renderer.\n        // Therefore, we need to check if we already removed the element.\n        const parentNode = delegate === null || delegate === void 0 ? void 0 : delegate.parentNode(element);\n\n        if (parentNode) {\n          delegate.removeChild(parentNode, element);\n        }\n      };\n    }\n\n    createRenderer(hostElement, type) {\n      const EMPTY_NAMESPACE_ID = ''; // cache the delegates to find out which cached delegate can\n      // be used by which cached renderer\n\n      const delegate = this.delegate.createRenderer(hostElement, type);\n\n      if (!hostElement || !type || !type.data || !type.data['animation']) {\n        let renderer = this._rendererCache.get(delegate);\n\n        if (!renderer) {\n          renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine); // only cache this result when the base renderer is used\n\n          this._rendererCache.set(delegate, renderer);\n        }\n\n        return renderer;\n      }\n\n      const componentId = type.id;\n      const namespaceId = type.id + '-' + this._currentId;\n      this._currentId++;\n      this.engine.register(namespaceId, hostElement);\n\n      const registerTrigger = trigger => {\n        if (Array.isArray(trigger)) {\n          trigger.forEach(registerTrigger);\n        } else {\n          this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n        }\n      };\n\n      const animationTriggers = type.data['animation'];\n      animationTriggers.forEach(registerTrigger);\n      return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n    }\n\n    begin() {\n      this._cdRecurDepth++;\n\n      if (this.delegate.begin) {\n        this.delegate.begin();\n      }\n    }\n\n    _scheduleCountTask() {\n      // always use promise to schedule microtask instead of use Zone\n      this.promise.then(() => {\n        this._microtaskId++;\n      });\n    }\n    /** @internal */\n\n\n    scheduleListenerCallback(count, fn, data) {\n      if (count >= 0 && count < this._microtaskId) {\n        this._zone.run(() => fn(data));\n\n        return;\n      }\n\n      if (this._animationCallbacksBuffer.length == 0) {\n        Promise.resolve(null).then(() => {\n          this._zone.run(() => {\n            this._animationCallbacksBuffer.forEach(tuple => {\n              const [fn, data] = tuple;\n              fn(data);\n            });\n\n            this._animationCallbacksBuffer = [];\n          });\n        });\n      }\n\n      this._animationCallbacksBuffer.push([fn, data]);\n    }\n\n    end() {\n      this._cdRecurDepth--; // this is to prevent animations from running twice when an inner\n      // component does CD when a parent component instead has inserted it\n\n      if (this._cdRecurDepth == 0) {\n        this._zone.runOutsideAngular(() => {\n          this._scheduleCountTask();\n\n          this.engine.flush(this._microtaskId);\n        });\n      }\n\n      if (this.delegate.end) {\n        this.delegate.end();\n      }\n    }\n\n    whenRenderingDone() {\n      return this.engine.whenRenderingDone();\n    }\n\n  }\n\n  AnimationRendererFactory.ɵfac = function AnimationRendererFactory_Factory(t) {\n    return new (t || AnimationRendererFactory)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(i1.ɵAnimationEngine), i0.ɵɵinject(i0.NgZone));\n  };\n\n  AnimationRendererFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AnimationRendererFactory,\n    factory: AnimationRendererFactory.ɵfac\n  });\n  return AnimationRendererFactory;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nclass BaseAnimationRenderer {\n  constructor(namespaceId, delegate, engine) {\n    this.namespaceId = namespaceId;\n    this.delegate = delegate;\n    this.engine = engine;\n    this.destroyNode = this.delegate.destroyNode ? n => delegate.destroyNode(n) : null;\n  }\n\n  get data() {\n    return this.delegate.data;\n  }\n\n  destroy() {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.delegate.destroy();\n  }\n\n  createElement(name, namespace) {\n    return this.delegate.createElement(name, namespace);\n  }\n\n  createComment(value) {\n    return this.delegate.createComment(value);\n  }\n\n  createText(value) {\n    return this.delegate.createText(value);\n  }\n\n  appendChild(parent, newChild) {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n\n  insertBefore(parent, newChild, refChild, isMove = true) {\n    this.delegate.insertBefore(parent, newChild, refChild); // If `isMove` true than we should animate this insert.\n\n    this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n  }\n\n  removeChild(parent, oldChild, isHostElement) {\n    this.engine.onRemove(this.namespaceId, oldChild, this.delegate, isHostElement);\n  }\n\n  selectRootElement(selectorOrNode, preserveContent) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n\n  parentNode(node) {\n    return this.delegate.parentNode(node);\n  }\n\n  nextSibling(node) {\n    return this.delegate.nextSibling(node);\n  }\n\n  setAttribute(el, name, value, namespace) {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n\n  removeAttribute(el, name, namespace) {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n\n  addClass(el, name) {\n    this.delegate.addClass(el, name);\n  }\n\n  removeClass(el, name) {\n    this.delegate.removeClass(el, name);\n  }\n\n  setStyle(el, style, value, flags) {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n\n  removeStyle(el, style, flags) {\n    this.delegate.removeStyle(el, style, flags);\n  }\n\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n      this.disableAnimations(el, !!value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n\n  setValue(node, value) {\n    this.delegate.setValue(node, value);\n  }\n\n  listen(target, eventName, callback) {\n    return this.delegate.listen(target, eventName, callback);\n  }\n\n  disableAnimations(element, value) {\n    this.engine.disableAnimations(element, value);\n  }\n\n}\n\nclass AnimationRenderer extends BaseAnimationRenderer {\n  constructor(factory, namespaceId, delegate, engine) {\n    super(namespaceId, delegate, engine);\n    this.factory = factory;\n    this.namespaceId = namespaceId;\n  }\n\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX) {\n      if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n        value = value === undefined ? true : !!value;\n        this.disableAnimations(el, value);\n      } else {\n        this.engine.process(this.namespaceId, el, name.substr(1), value);\n      }\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n\n  listen(target, eventName, callback) {\n    if (eventName.charAt(0) == ANIMATION_PREFIX) {\n      const element = resolveElementFromTarget(target);\n      let name = eventName.substr(1);\n      let phase = ''; // @listener.phase is for trigger animation callbacks\n      // @@listener is for animation builder callbacks\n\n      if (name.charAt(0) != ANIMATION_PREFIX) {\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const countId = event['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n\n    return this.delegate.listen(target, eventName, callback);\n  }\n\n}\n\nfunction resolveElementFromTarget(target) {\n  switch (target) {\n    case 'body':\n      return document.body;\n\n    case 'document':\n      return document;\n\n    case 'window':\n      return window;\n\n    default:\n      return target;\n  }\n}\n\nfunction parseTriggerCallbackName(triggerName) {\n  const dotIndex = triggerName.indexOf('.');\n  const trigger = triggerName.substring(0, dotIndex);\n  const phase = triggerName.substr(dotIndex + 1);\n  return [trigger, phase];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet InjectableAnimationEngine = /*#__PURE__*/(() => {\n  class InjectableAnimationEngine extends ɵAnimationEngine {\n    constructor(doc, driver, normalizer) {\n      super(doc.body, driver, normalizer);\n    }\n\n    ngOnDestroy() {\n      this.flush();\n    }\n\n  }\n\n  InjectableAnimationEngine.ɵfac = function InjectableAnimationEngine_Factory(t) {\n    return new (t || InjectableAnimationEngine)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.AnimationDriver), i0.ɵɵinject(i1.ɵAnimationStyleNormalizer));\n  };\n\n  InjectableAnimationEngine.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InjectableAnimationEngine,\n    factory: InjectableAnimationEngine.ɵfac\n  });\n  return InjectableAnimationEngine;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction instantiateSupportedAnimationDriver() {\n  return ɵsupportsWebAnimations() ? new ɵWebAnimationsDriver() : new ɵCssKeyframesDriver();\n}\n\nfunction instantiateDefaultStyleNormalizer() {\n  return new ɵWebAnimationsStyleNormalizer();\n}\n\nfunction instantiateRendererFactory(renderer, engine, zone) {\n  return new AnimationRendererFactory(renderer, engine, zone);\n}\n/**\n * @publicApi\n */\n\n\nconst ANIMATION_MODULE_TYPE = /*#__PURE__*/new InjectionToken('AnimationModuleType');\nconst SHARED_ANIMATION_PROVIDERS = [{\n  provide: AnimationBuilder,\n  useClass: BrowserAnimationBuilder\n}, {\n  provide: ɵAnimationStyleNormalizer,\n  useFactory: instantiateDefaultStyleNormalizer\n}, {\n  provide: ɵAnimationEngine,\n  useClass: InjectableAnimationEngine\n}, {\n  provide: RendererFactory2,\n  useFactory: instantiateRendererFactory,\n  deps: [ɵDomRendererFactory2, ɵAnimationEngine, NgZone]\n}];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\n\nconst BROWSER_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useFactory: instantiateSupportedAnimationDriver\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'BrowserAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\n\nconst BROWSER_NOOP_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useClass: ɵNoopAnimationDriver\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'NoopAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Exports `BrowserModule` with additional [dependency-injection providers](guide/glossary#provider)\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\n\nlet BrowserAnimationsModule = /*#__PURE__*/(() => {\n  class BrowserAnimationsModule {\n    /**\n     * Configures the module based on the specified object.\n     *\n     * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n     * @see `BrowserAnimationsModuleConfig`\n     *\n     * @usageNotes\n     * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n     * function as follows:\n     * ```\n     * @NgModule({\n     *   imports: [BrowserAnimationsModule.withConfig(config)]\n     * })\n     * class MyNgModule {}\n     * ```\n     */\n    static withConfig(config) {\n      return {\n        ngModule: BrowserAnimationsModule,\n        providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS : BROWSER_ANIMATIONS_PROVIDERS\n      };\n    }\n\n  }\n\n  BrowserAnimationsModule.ɵfac = function BrowserAnimationsModule_Factory(t) {\n    return new (t || BrowserAnimationsModule)();\n  };\n\n  BrowserAnimationsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BrowserAnimationsModule\n  });\n  BrowserAnimationsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: BROWSER_ANIMATIONS_PROVIDERS,\n    imports: [BrowserModule]\n  });\n  return BrowserAnimationsModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\n\n\nlet NoopAnimationsModule = /*#__PURE__*/(() => {\n  class NoopAnimationsModule {}\n\n  NoopAnimationsModule.ɵfac = function NoopAnimationsModule_Factory(t) {\n    return new (t || NoopAnimationsModule)();\n  };\n\n  NoopAnimationsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NoopAnimationsModule\n  });\n  NoopAnimationsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n    imports: [BrowserModule]\n  });\n  return NoopAnimationsModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ANIMATION_MODULE_TYPE, BrowserAnimationsModule, NoopAnimationsModule, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, BrowserAnimationBuilder as ɵBrowserAnimationBuilder, BrowserAnimationFactory as ɵBrowserAnimationFactory, InjectableAnimationEngine as ɵInjectableAnimationEngine }; //# sourceMappingURL=animations.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}