{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { ForumDetailsRoutes } from './forum-details.routing';\nimport { CollapseModule } from 'ngx-bootstrap/collapse';\nimport { PopoverModule } from 'ngx-bootstrap/popover';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-bootstrap/popover\";\nexport let ForumDetailsModule = /*#__PURE__*/(() => {\n  class ForumDetailsModule {}\n\n  ForumDetailsModule.ɵfac = function ForumDetailsModule_Factory(t) {\n    return new (t || ForumDetailsModule)();\n  };\n\n  ForumDetailsModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ForumDetailsModule\n  });\n  ForumDetailsModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(ForumDetailsRoutes), SharedModule, WebLayoutModule, CollapseModule, PopoverModule.forRoot()]]\n  });\n  return ForumDetailsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}