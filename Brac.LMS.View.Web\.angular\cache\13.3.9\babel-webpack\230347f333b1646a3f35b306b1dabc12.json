{"ast": null, "code": "import { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\nexport function skipUntil(notifier) {\n  return operate((source, subscriber) => {\n    let taking = false;\n    const skipSubscriber = new OperatorSubscriber(subscriber, () => {\n      skipSubscriber === null || skipSubscriber === void 0 ? void 0 : skipSubscriber.unsubscribe();\n      taking = true;\n    }, noop);\n    innerFrom(notifier).subscribe(skipSubscriber);\n    source.subscribe(new OperatorSubscriber(subscriber, value => taking && subscriber.next(value)));\n  });\n} //# sourceMappingURL=skipUntil.js.map", "map": null, "metadata": {}, "sourceType": "module"}