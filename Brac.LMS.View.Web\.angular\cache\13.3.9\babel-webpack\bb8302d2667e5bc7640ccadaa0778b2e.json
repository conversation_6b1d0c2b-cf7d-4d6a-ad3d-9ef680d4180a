{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { defaultThrottleConfig, throttle } from './throttle';\nimport { timer } from '../observable/timer';\nexport function throttleTime(duration, scheduler = asyncScheduler, config = defaultThrottleConfig) {\n  const duration$ = timer(duration, scheduler);\n  return throttle(() => duration$, config);\n} //# sourceMappingURL=throttleTime.js.map", "map": null, "metadata": {}, "sourceType": "module"}