{"ast": null, "code": "import { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../_services/authentication.service\";\nexport let AuthGuard = /*#__PURE__*/(() => {\n  class AuthGuard {\n    constructor(router, authService) {\n      this.router = router;\n      this.authService = authService;\n    }\n\n    canActivate() {\n      return this.authService.isLoggedIn().pipe(tap(isLoggedIn => {\n        if (!isLoggedIn) {\n          this.router.navigate([this.authService.LOGIN_PATH]);\n        }\n      }));\n    }\n\n  }\n\n  AuthGuard.ɵfac = function AuthGuard_Factory(t) {\n    return new (t || AuthGuard)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.AuthenticationService));\n  };\n\n  AuthGuard.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthGuard,\n    factory: AuthGuard.ɵfac,\n    providedIn: 'root'\n  });\n  return AuthGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module"}