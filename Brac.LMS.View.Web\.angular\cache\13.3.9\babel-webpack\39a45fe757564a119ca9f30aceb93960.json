{"ast": null, "code": "import { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function onErrorResumeNext(...sources) {\n  const nextSources = argsOrArgArray(sources);\n  return operate((source, subscriber) => {\n    const remaining = [source, ...nextSources];\n\n    const subscribeNext = () => {\n      if (!subscriber.closed) {\n        if (remaining.length > 0) {\n          let nextSource;\n\n          try {\n            nextSource = innerFrom(remaining.shift());\n          } catch (err) {\n            subscribeNext();\n            return;\n          }\n\n          const innerSub = new OperatorSubscriber(subscriber, undefined, noop, noop);\n          subscriber.add(nextSource.subscribe(innerSub));\n          innerSub.add(subscribeNext);\n        } else {\n          subscriber.complete();\n        }\n      }\n    };\n\n    subscribeNext();\n  });\n} //# sourceMappingURL=onErrorResumeNext.js.map", "map": null, "metadata": {}, "sourceType": "module"}