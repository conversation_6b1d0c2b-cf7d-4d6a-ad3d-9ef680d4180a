{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Finnish [fi]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/bleadof\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var numbersPast = 'nolla yksi kaksi kolme neljä viisi kuusi seitsemän kahdeksan yhdeksän'.split(' '),\n      numbersFuture = ['nolla', 'yhden', 'kahden', 'kolmen', 'neljän', 'viiden', 'kuuden', numbersPast[7], numbersPast[8], numbersPast[9]];\n\n  function translate(number, withoutSuffix, key, isFuture) {\n    var result = '';\n\n    switch (key) {\n      case 's':\n        return isFuture ? 'muutaman sekunnin' : 'muutama sekunti';\n\n      case 'ss':\n        result = isFuture ? 'sekunnin' : 'sekuntia';\n        break;\n\n      case 'm':\n        return isFuture ? 'minuutin' : 'minuutti';\n\n      case 'mm':\n        result = isFuture ? 'minuutin' : 'minuuttia';\n        break;\n\n      case 'h':\n        return isFuture ? 'tunnin' : 'tunti';\n\n      case 'hh':\n        result = isFuture ? 'tunnin' : 'tuntia';\n        break;\n\n      case 'd':\n        return isFuture ? 'päivän' : 'päivä';\n\n      case 'dd':\n        result = isFuture ? 'päivän' : 'päivää';\n        break;\n\n      case 'M':\n        return isFuture ? 'kuukauden' : 'kuukausi';\n\n      case 'MM':\n        result = isFuture ? 'kuukauden' : 'kuukautta';\n        break;\n\n      case 'y':\n        return isFuture ? 'vuoden' : 'vuosi';\n\n      case 'yy':\n        result = isFuture ? 'vuoden' : 'vuotta';\n        break;\n    }\n\n    result = verbalNumber(number, isFuture) + ' ' + result;\n    return result;\n  }\n\n  function verbalNumber(number, isFuture) {\n    return number < 10 ? isFuture ? numbersFuture[number] : numbersPast[number] : number;\n  }\n\n  var fi = moment.defineLocale('fi', {\n    months: 'tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kesäkuu_heinäkuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu'.split('_'),\n    monthsShort: 'tammi_helmi_maalis_huhti_touko_kesä_heinä_elo_syys_loka_marras_joulu'.split('_'),\n    weekdays: 'sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai'.split('_'),\n    weekdaysShort: 'su_ma_ti_ke_to_pe_la'.split('_'),\n    weekdaysMin: 'su_ma_ti_ke_to_pe_la'.split('_'),\n    longDateFormat: {\n      LT: 'HH.mm',\n      LTS: 'HH.mm.ss',\n      L: 'DD.MM.YYYY',\n      LL: 'Do MMMM[ta] YYYY',\n      LLL: 'Do MMMM[ta] YYYY, [klo] HH.mm',\n      LLLL: 'dddd, Do MMMM[ta] YYYY, [klo] HH.mm',\n      l: 'D.M.YYYY',\n      ll: 'Do MMM YYYY',\n      lll: 'Do MMM YYYY, [klo] HH.mm',\n      llll: 'ddd, Do MMM YYYY, [klo] HH.mm'\n    },\n    calendar: {\n      sameDay: '[tänään] [klo] LT',\n      nextDay: '[huomenna] [klo] LT',\n      nextWeek: 'dddd [klo] LT',\n      lastDay: '[eilen] [klo] LT',\n      lastWeek: '[viime] dddd[na] [klo] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s päästä',\n      past: '%s sitten',\n      s: translate,\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: translate,\n      hh: translate,\n      d: translate,\n      dd: translate,\n      M: translate,\n      MM: translate,\n      y: translate,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return fi;\n});", "map": null, "metadata": {}, "sourceType": "script"}