{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Version, PLATFORM_ID, NgModule, Inject } from '@angular/core';\nimport { LAYOUT_CONFIG, DEFAULT_CONFIG, BREAKPOINT, SERVER_TOKEN } from '@angular/flex-layout/core';\nexport * from '@angular/flex-layout/core';\nimport { ExtendedModule } from '@angular/flex-layout/extended';\nexport * from '@angular/flex-layout/extended';\nimport { FlexModule } from '@angular/flex-layout/flex';\nexport * from '@angular/flex-layout/flex';\nimport { GridModule } from '@angular/flex-layout/grid';\nexport * from '@angular/flex-layout/grid';\nimport { isPlatformServer } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Current version of Angular Flex-Layout. */\n\nconst VERSION = /*#__PURE__*/new Version('13.0.0-beta.38');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * FlexLayoutModule -- the main import for all utilities in the Angular Layout library\n * * Will automatically provide Flex, Grid, and Extended modules for use in the application\n * * Can be configured using the static withConfig method, options viewable on the Wiki's\n *   Configuration page\n */\n\nlet FlexLayoutModule = /*#__PURE__*/(() => {\n  class FlexLayoutModule {\n    constructor(serverModuleLoaded, platformId) {\n      if (isPlatformServer(platformId) && !serverModuleLoaded) {\n        console.warn('Warning: Flex Layout loaded on the server without FlexLayoutServerModule');\n      }\n    }\n    /**\n     * Initialize the FlexLayoutModule with a set of config options,\n     * which sets the corresponding tokens accordingly\n     */\n\n\n    static withConfig(configOptions, // tslint:disable-next-line:max-line-length\n    breakpoints = []) {\n      return {\n        ngModule: FlexLayoutModule,\n        providers: configOptions.serverLoaded ? [{\n          provide: LAYOUT_CONFIG,\n          useValue: Object.assign(Object.assign({}, DEFAULT_CONFIG), configOptions)\n        }, {\n          provide: BREAKPOINT,\n          useValue: breakpoints,\n          multi: true\n        }, {\n          provide: SERVER_TOKEN,\n          useValue: true\n        }] : [{\n          provide: LAYOUT_CONFIG,\n          useValue: Object.assign(Object.assign({}, DEFAULT_CONFIG), configOptions)\n        }, {\n          provide: BREAKPOINT,\n          useValue: breakpoints,\n          multi: true\n        }]\n      };\n    }\n\n  }\n\n  FlexLayoutModule.ɵfac = function FlexLayoutModule_Factory(t) {\n    return new (t || FlexLayoutModule)(i0.ɵɵinject(SERVER_TOKEN), i0.ɵɵinject(PLATFORM_ID));\n  };\n\n  FlexLayoutModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FlexLayoutModule\n  });\n  FlexLayoutModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[FlexModule, ExtendedModule, GridModule], FlexModule, ExtendedModule, GridModule]\n  });\n  return FlexLayoutModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { FlexLayoutModule, VERSION };", "map": null, "metadata": {}, "sourceType": "module"}