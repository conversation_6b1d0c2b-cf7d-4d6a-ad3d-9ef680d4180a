{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { EvaluationTestResultRoutes } from './evaluation-test-result.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let EvaluationTestResultModule = /*#__PURE__*/(() => {\n  class EvaluationTestResultModule {}\n\n  EvaluationTestResultModule.ɵfac = function EvaluationTestResultModule_Factory(t) {\n    return new (t || EvaluationTestResultModule)();\n  };\n\n  EvaluationTestResultModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: EvaluationTestResultModule\n  });\n  EvaluationTestResultModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(EvaluationTestResultRoutes), SharedModule, WebLayoutModule]]\n  });\n  return EvaluationTestResultModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}