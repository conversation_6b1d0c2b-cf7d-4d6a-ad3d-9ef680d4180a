{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Component, HostListener, EventEmitter, Directive, Optional, Inject, Input, Output, NgModule } from '@angular/core';\nimport { isBs3, Utils, document as document$1, window as window$1 } from 'ngx-bootstrap/utils';\nimport * as i2 from 'ngx-bootstrap/focus-trap';\nimport { FocusTrapModule } from 'ngx-bootstrap/focus-trap';\nimport * as i1 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport { PositioningService } from 'ngx-bootstrap/positioning'; // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\nconst _c0 = [\"*\"];\nlet BsModalRef = /*#__PURE__*/(() => {\n  class BsModalRef {\n    constructor() {\n      /**\n       * Hides the modal\n       */\n      this.hide = () => void 0;\n      /**\n       * Sets new class to modal window\n       */\n\n\n      this.setClass = () => void 0;\n    }\n\n  }\n\n  BsModalRef.ɵfac = function BsModalRef_Factory(t) {\n    return new (t || BsModalRef)();\n  };\n\n  BsModalRef.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BsModalRef,\n    factory: BsModalRef.ɵfac,\n    providedIn: 'platform'\n  });\n  return BsModalRef;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nclass ModalBackdropOptions {\n  constructor(options) {\n    this.animate = true;\n    Object.assign(this, options);\n  }\n\n}\n\nlet ModalOptions = /*#__PURE__*/(() => {\n  class ModalOptions {}\n\n  ModalOptions.ɵfac = function ModalOptions_Factory(t) {\n    return new (t || ModalOptions)();\n  };\n\n  ModalOptions.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ModalOptions,\n    factory: ModalOptions.ɵfac,\n    providedIn: 'platform'\n  });\n  return ModalOptions;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst modalConfigDefaults = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: false,\n  ignoreBackdropClick: false,\n  class: '',\n  animated: true,\n  initialState: {},\n  closeInterceptor: void 0\n};\nconst MODAL_CONFIG_DEFAULT_OVERRIDE = new InjectionToken('override-default-config');\nconst CLASS_NAME = {\n  SCROLLBAR_MEASURER: 'modal-scrollbar-measure',\n  BACKDROP: 'modal-backdrop',\n  OPEN: 'modal-open',\n  FADE: 'fade',\n  IN: 'in',\n  SHOW: 'show' // bs4\n\n};\nconst SELECTOR = {\n  DIALOG: '.modal-dialog',\n  DATA_TOGGLE: '[data-toggle=\"modal\"]',\n  DATA_DISMISS: '[data-dismiss=\"modal\"]',\n  FIXED_CONTENT: '.navbar-fixed-top, .navbar-fixed-bottom, .is-fixed'\n};\nconst TRANSITION_DURATIONS = {\n  MODAL: 300,\n  BACKDROP: 150\n};\nconst DISMISS_REASONS = {\n  BACKRDOP: 'backdrop-click',\n  ESC: 'esc',\n  BACK: 'browser-back-navigation-clicked'\n};\nlet ModalContainerComponent = /*#__PURE__*/(() => {\n  class ModalContainerComponent {\n    constructor(options, _element, _renderer) {\n      this._element = _element;\n      this._renderer = _renderer;\n      this.isShown = false;\n      this.isAnimated = false;\n      this.isModalHiding = false;\n      this.clickStartedInContent = false;\n      this.config = Object.assign({}, options);\n    }\n\n    ngOnInit() {\n      if (this.isAnimated) {\n        this._renderer.addClass(this._element.nativeElement, CLASS_NAME.FADE);\n      }\n\n      this._renderer.setStyle(this._element.nativeElement, 'display', 'block');\n\n      setTimeout(() => {\n        this.isShown = true;\n\n        this._renderer.addClass(this._element.nativeElement, isBs3() ? CLASS_NAME.IN : CLASS_NAME.SHOW);\n      }, this.isAnimated ? TRANSITION_DURATIONS.BACKDROP : 0);\n\n      if (document && document.body) {\n        if (this.bsModalService && this.bsModalService.getModalsCount() === 1) {\n          this.bsModalService.checkScrollbar();\n          this.bsModalService.setScrollbar();\n        }\n\n        this._renderer.addClass(document.body, CLASS_NAME.OPEN);\n\n        this._renderer.setStyle(document.body, 'overflow-y', 'hidden');\n      }\n\n      if (this._element.nativeElement) {\n        this._element.nativeElement.focus();\n      }\n    }\n\n    onClickStarted(event) {\n      this.clickStartedInContent = event.target !== this._element.nativeElement;\n    }\n\n    onClickStop(event) {\n      var _a;\n\n      const clickedInBackdrop = event.target === this._element.nativeElement && !this.clickStartedInContent;\n\n      if (this.config.ignoreBackdropClick || this.config.backdrop === 'static' || !clickedInBackdrop) {\n        this.clickStartedInContent = false;\n        return;\n      }\n\n      (_a = this.bsModalService) === null || _a === void 0 ? void 0 : _a.setDismissReason(DISMISS_REASONS.BACKRDOP);\n      this.hide();\n    }\n\n    onPopState() {\n      var _a;\n\n      (_a = this.bsModalService) === null || _a === void 0 ? void 0 : _a.setDismissReason(DISMISS_REASONS.BACK);\n      this.hide();\n    }\n\n    onEsc(event) {\n      var _a, _b;\n\n      if (!this.isShown) {\n        return;\n      }\n\n      if (event.keyCode === 27 || event.key === 'Escape') {\n        event.preventDefault();\n      }\n\n      if (this.config.keyboard && this.level === ((_a = this.bsModalService) === null || _a === void 0 ? void 0 : _a.getModalsCount())) {\n        (_b = this.bsModalService) === null || _b === void 0 ? void 0 : _b.setDismissReason(DISMISS_REASONS.ESC);\n        this.hide();\n      }\n    }\n\n    ngOnDestroy() {\n      if (this.isShown) {\n        this._hide();\n      }\n    }\n\n    hide() {\n      if (this.isModalHiding || !this.isShown) {\n        return;\n      }\n\n      if (this.config.closeInterceptor) {\n        this.config.closeInterceptor().then(() => this._hide(), () => undefined);\n        return;\n      }\n\n      this._hide();\n    }\n\n    _hide() {\n      this.isModalHiding = true;\n\n      this._renderer.removeClass(this._element.nativeElement, isBs3() ? CLASS_NAME.IN : CLASS_NAME.SHOW);\n\n      setTimeout(() => {\n        var _a, _b;\n\n        this.isShown = false;\n\n        if (document && document.body && ((_a = this.bsModalService) === null || _a === void 0 ? void 0 : _a.getModalsCount()) === 1) {\n          this._renderer.removeClass(document.body, CLASS_NAME.OPEN);\n\n          this._renderer.setStyle(document.body, 'overflow-y', '');\n        }\n\n        (_b = this.bsModalService) === null || _b === void 0 ? void 0 : _b.hide(this.config.id);\n        this.isModalHiding = false;\n      }, this.isAnimated ? TRANSITION_DURATIONS.MODAL : 0);\n    }\n\n  }\n\n  ModalContainerComponent.ɵfac = function ModalContainerComponent_Factory(t) {\n    return new (t || ModalContainerComponent)(i0.ɵɵdirectiveInject(ModalOptions), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n\n  ModalContainerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ModalContainerComponent,\n    selectors: [[\"modal-container\"]],\n    hostAttrs: [\"role\", \"dialog\", \"tabindex\", \"-1\", 1, \"modal\"],\n    hostVars: 3,\n    hostBindings: function ModalContainerComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"mousedown\", function ModalContainerComponent_mousedown_HostBindingHandler($event) {\n          return ctx.onClickStarted($event);\n        })(\"click\", function ModalContainerComponent_click_HostBindingHandler($event) {\n          return ctx.onClickStop($event);\n        })(\"popstate\", function ModalContainerComponent_popstate_HostBindingHandler() {\n          return ctx.onPopState();\n        }, false, i0.ɵɵresolveWindow)(\"keydown.esc\", function ModalContainerComponent_keydown_esc_HostBindingHandler($event) {\n          return ctx.onEsc($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-modal\", true)(\"aria-labelledby\", ctx.config.ariaLabelledBy)(\"aria-describedby\", ctx.config.ariaDescribedby);\n      }\n    },\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 2,\n    consts: [[\"role\", \"document\", \"focusTrap\", \"\"], [1, \"modal-content\"]],\n    template: function ModalContainerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵclassMap(\"modal-dialog\" + (ctx.config.class ? \" \" + ctx.config.class : \"\"));\n      }\n    },\n    directives: [i2.FocusTrapDirective],\n    encapsulation: 2\n  });\n  return ModalContainerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** This component will be added as background layout for modals if enabled */\n\n\nlet ModalBackdropComponent = /*#__PURE__*/(() => {\n  class ModalBackdropComponent {\n    constructor(element, renderer) {\n      this._isAnimated = false;\n      this._isShown = false;\n      this.element = element;\n      this.renderer = renderer;\n    }\n\n    get isAnimated() {\n      return this._isAnimated;\n    }\n\n    set isAnimated(value) {\n      this._isAnimated = value;\n    }\n\n    get isShown() {\n      return this._isShown;\n    }\n\n    set isShown(value) {\n      this._isShown = value;\n\n      if (value) {\n        this.renderer.addClass(this.element.nativeElement, `${CLASS_NAME.IN}`);\n      } else {\n        this.renderer.removeClass(this.element.nativeElement, `${CLASS_NAME.IN}`);\n      }\n\n      if (!isBs3()) {\n        if (value) {\n          this.renderer.addClass(this.element.nativeElement, `${CLASS_NAME.SHOW}`);\n        } else {\n          this.renderer.removeClass(this.element.nativeElement, `${CLASS_NAME.SHOW}`);\n        }\n      }\n    }\n\n    ngOnInit() {\n      if (this.isAnimated) {\n        this.renderer.addClass(this.element.nativeElement, `${CLASS_NAME.FADE}`);\n        Utils.reflow(this.element.nativeElement);\n      }\n\n      this.isShown = true;\n    }\n\n  }\n\n  ModalBackdropComponent.ɵfac = function ModalBackdropComponent_Factory(t) {\n    return new (t || ModalBackdropComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n\n  ModalBackdropComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ModalBackdropComponent,\n    selectors: [[\"bs-modal-backdrop\"]],\n    hostAttrs: [1, \"modal-backdrop\"],\n    decls: 0,\n    vars: 0,\n    template: function ModalBackdropComponent_Template(rf, ctx) {},\n    encapsulation: 2\n  });\n  return ModalBackdropComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})(); // todo: should we support enforce focus in?\n\n\nconst TRANSITION_DURATION = 300;\nconst BACKDROP_TRANSITION_DURATION = 150;\n/** Mark any code with directive to show it's content in modal */\n\nlet ModalDirective = /*#__PURE__*/(() => {\n  class ModalDirective {\n    constructor(_element, _viewContainerRef, _renderer, clf, modalDefaultOption) {\n      this._element = _element;\n      this._renderer = _renderer;\n      /** This event fires immediately when the `show` instance method is called. */\n\n      this.onShow = new EventEmitter();\n      /** This event is fired when the modal has been made visible to the user\n       * (will wait for CSS transitions to complete)\n       */\n\n      this.onShown = new EventEmitter();\n      /** This event is fired immediately when\n       * the hide instance method has been called.\n       */\n\n      this.onHide = new EventEmitter();\n      /** This event is fired when the modal has finished being\n       * hidden from the user (will wait for CSS transitions to complete).\n       */\n\n      this.onHidden = new EventEmitter();\n      this._isShown = false;\n      this.isBodyOverflowing = false;\n      this.originalBodyPadding = 0;\n      this.scrollbarWidth = 0;\n      this.timerHideModal = 0;\n      this.timerRmBackDrop = 0;\n      this.isNested = false;\n      this.clickStartedInContent = false;\n      this._backdrop = clf.createLoader(_element, _viewContainerRef, _renderer);\n      this._config = modalDefaultOption || modalConfigDefaults;\n    }\n    /** allows to set modal configuration via element property */\n\n\n    set config(conf) {\n      this._config = this.getConfig(conf);\n    }\n\n    get config() {\n      return this._config;\n    }\n\n    get isShown() {\n      return this._isShown;\n    }\n\n    onClickStarted(event) {\n      this.clickStartedInContent = event.target !== this._element.nativeElement;\n    }\n\n    onClickStop(event) {\n      const clickedInBackdrop = event.target === this._element.nativeElement && !this.clickStartedInContent;\n\n      if (this.config.ignoreBackdropClick || this.config.backdrop === 'static' || !clickedInBackdrop) {\n        this.clickStartedInContent = false;\n        return;\n      }\n\n      this.dismissReason = DISMISS_REASONS.BACKRDOP;\n      this.hide(event);\n    } // todo: consider preventing default and stopping propagation\n\n\n    onEsc(event) {\n      if (!this._isShown) {\n        return;\n      }\n\n      if (event.keyCode === 27 || event.key === 'Escape') {\n        event.preventDefault();\n      }\n\n      if (this.config.keyboard) {\n        this.dismissReason = DISMISS_REASONS.ESC;\n        this.hide();\n      }\n    }\n\n    ngOnDestroy() {\n      if (this._isShown) {\n        this._isShown = false;\n        this.hideModal();\n\n        this._backdrop.dispose();\n      }\n    }\n\n    ngOnInit() {\n      this._config = this._config || this.getConfig();\n      setTimeout(() => {\n        if (this._config.show) {\n          this.show();\n        }\n      }, 0);\n    }\n    /* Public methods */\n\n    /** Allows to manually toggle modal visibility */\n\n\n    toggle() {\n      return this._isShown ? this.hide() : this.show();\n    }\n    /** Allows to manually open modal */\n\n\n    show() {\n      this.dismissReason = void 0;\n      this.onShow.emit(this);\n\n      if (this._isShown) {\n        return;\n      }\n\n      clearTimeout(this.timerHideModal);\n      clearTimeout(this.timerRmBackDrop);\n      this._isShown = true;\n      this.checkScrollbar();\n      this.setScrollbar();\n\n      if (document$1 && document$1.body) {\n        if (document$1.body.classList.contains(CLASS_NAME.OPEN)) {\n          this.isNested = true;\n        } else {\n          this._renderer.addClass(document$1.body, CLASS_NAME.OPEN);\n\n          this._renderer.setStyle(document$1.body, 'overflow-y', 'hidden');\n        }\n      }\n\n      this.showBackdrop(() => {\n        this.showElement();\n      });\n    }\n    /** Check if we can close the modal */\n\n\n    hide(event) {\n      if (!this._isShown) {\n        return;\n      }\n\n      if (event) {\n        event.preventDefault();\n      }\n\n      if (this.config.closeInterceptor) {\n        this.config.closeInterceptor().then(() => this._hide(), () => undefined);\n        return;\n      }\n\n      this._hide();\n    }\n    /** Private methods @internal */\n\n    /**\n     *  Manually close modal\n     *  @internal\n     */\n\n\n    _hide() {\n      this.onHide.emit(this);\n      window$1.clearTimeout(this.timerHideModal);\n      window$1.clearTimeout(this.timerRmBackDrop);\n      this._isShown = false;\n\n      this._renderer.removeClass(this._element.nativeElement, CLASS_NAME.IN);\n\n      if (!isBs3()) {\n        this._renderer.removeClass(this._element.nativeElement, CLASS_NAME.SHOW);\n      } // this._addClassIn = false;\n\n\n      if (this._config.animated) {\n        this.timerHideModal = window$1.setTimeout(() => this.hideModal(), TRANSITION_DURATION);\n      } else {\n        this.hideModal();\n      }\n    }\n\n    getConfig(config) {\n      return Object.assign({}, this._config, config);\n    }\n    /**\n     *  Show dialog\n     *  @internal\n     */\n\n\n    showElement() {\n      // todo: replace this with component loader usage\n      if (!this._element.nativeElement.parentNode || this._element.nativeElement.parentNode.nodeType !== Node.ELEMENT_NODE) {\n        // don't move modals dom position\n        if (document$1 && document$1.body) {\n          document$1.body.appendChild(this._element.nativeElement);\n        }\n      }\n\n      this._renderer.setAttribute(this._element.nativeElement, 'aria-hidden', 'false');\n\n      this._renderer.setAttribute(this._element.nativeElement, 'aria-modal', 'true');\n\n      this._renderer.setStyle(this._element.nativeElement, 'display', 'block');\n\n      this._renderer.setProperty(this._element.nativeElement, 'scrollTop', 0);\n\n      if (this._config.animated) {\n        Utils.reflow(this._element.nativeElement);\n      } // this._addClassIn = true;\n\n\n      this._renderer.addClass(this._element.nativeElement, CLASS_NAME.IN);\n\n      if (!isBs3()) {\n        this._renderer.addClass(this._element.nativeElement, CLASS_NAME.SHOW);\n      }\n\n      const transitionComplete = () => {\n        if (this._config.focus) {\n          this._element.nativeElement.focus();\n        }\n\n        this.onShown.emit(this);\n      };\n\n      if (this._config.animated) {\n        setTimeout(transitionComplete, TRANSITION_DURATION);\n      } else {\n        transitionComplete();\n      }\n    }\n    /** @internal */\n\n\n    hideModal() {\n      this._renderer.setAttribute(this._element.nativeElement, 'aria-hidden', 'true');\n\n      this._renderer.setStyle(this._element.nativeElement, 'display', 'none');\n\n      this.showBackdrop(() => {\n        if (!this.isNested) {\n          if (document$1 && document$1.body) {\n            this._renderer.removeClass(document$1.body, CLASS_NAME.OPEN);\n\n            this._renderer.setStyle(document$1.body, 'overflow-y', '');\n          }\n\n          this.resetScrollbar();\n        }\n\n        this.resetAdjustments();\n        this.focusOtherModal();\n        this.onHidden.emit(this);\n      });\n    } // todo: original show was calling a callback when done, but we can use\n    // promise\n\n    /** @internal */\n\n\n    showBackdrop(callback) {\n      if (this._isShown && this.config.backdrop && (!this.backdrop || !this.backdrop.instance.isShown)) {\n        this.removeBackdrop();\n\n        this._backdrop.attach(ModalBackdropComponent).to('body').show({\n          isAnimated: this._config.animated\n        });\n\n        this.backdrop = this._backdrop._componentRef;\n\n        if (!callback) {\n          return;\n        }\n\n        if (!this._config.animated) {\n          callback();\n          return;\n        }\n\n        setTimeout(callback, BACKDROP_TRANSITION_DURATION);\n      } else if (!this._isShown && this.backdrop) {\n        this.backdrop.instance.isShown = false;\n\n        const callbackRemove = () => {\n          this.removeBackdrop();\n\n          if (callback) {\n            callback();\n          }\n        };\n\n        if (this.backdrop.instance.isAnimated) {\n          this.timerRmBackDrop = window$1.setTimeout(callbackRemove, BACKDROP_TRANSITION_DURATION);\n        } else {\n          callbackRemove();\n        }\n      } else if (callback) {\n        callback();\n      }\n    }\n    /** @internal */\n\n\n    removeBackdrop() {\n      this._backdrop.hide();\n    }\n    /** Events tricks */\n    // no need for it\n    // protected setEscapeEvent():void {\n    //   if (this._isShown && this._config.keyboard) {\n    //     $(this._element).on(Event.KEYDOWN_DISMISS, (event) => {\n    //       if (event.which === 27) {\n    //         this.hide()\n    //       }\n    //     })\n    //\n    //   } else if (!this._isShown) {\n    //     $(this._element).off(Event.KEYDOWN_DISMISS)\n    //   }\n    // }\n    // protected setResizeEvent():void {\n    // console.log(this.renderer.listenGlobal('', Event.RESIZE));\n    // if (this._isShown) {\n    //   $(window).on(Event.RESIZE, $.proxy(this._handleUpdate, this))\n    // } else {\n    //   $(window).off(Event.RESIZE)\n    // }\n    // }\n\n\n    focusOtherModal() {\n      if (this._element.nativeElement.parentElement == null) {\n        return;\n      }\n\n      const otherOpenedModals = this._element.nativeElement.parentElement.querySelectorAll('.in[bsModal]');\n\n      if (!otherOpenedModals.length) {\n        return;\n      }\n\n      otherOpenedModals[otherOpenedModals.length - 1].focus();\n    }\n    /** @internal */\n\n\n    resetAdjustments() {\n      this._renderer.setStyle(this._element.nativeElement, 'paddingLeft', '');\n\n      this._renderer.setStyle(this._element.nativeElement, 'paddingRight', '');\n    }\n    /** Scroll bar tricks */\n\n    /** @internal */\n\n\n    checkScrollbar() {\n      this.isBodyOverflowing = document$1.body.clientWidth < window$1.innerWidth;\n      this.scrollbarWidth = this.getScrollbarWidth();\n    }\n\n    setScrollbar() {\n      if (!document$1) {\n        return;\n      }\n\n      this.originalBodyPadding = parseInt(window$1.getComputedStyle(document$1.body).getPropertyValue('padding-right') || 0, 10);\n\n      if (this.isBodyOverflowing) {\n        document$1.body.style.paddingRight = `${this.originalBodyPadding + this.scrollbarWidth}px`;\n      }\n    }\n\n    resetScrollbar() {\n      document$1.body.style.paddingRight = `${this.originalBodyPadding}px`;\n    } // thx d.walsh\n\n\n    getScrollbarWidth() {\n      const scrollDiv = this._renderer.createElement('div');\n\n      this._renderer.addClass(scrollDiv, CLASS_NAME.SCROLLBAR_MEASURER);\n\n      this._renderer.appendChild(document$1.body, scrollDiv);\n\n      const scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n\n      this._renderer.removeChild(document$1.body, scrollDiv);\n\n      return scrollbarWidth;\n    }\n\n  }\n\n  ModalDirective.ɵfac = function ModalDirective_Factory(t) {\n    return new (t || ModalDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.ComponentLoaderFactory), i0.ɵɵdirectiveInject(MODAL_CONFIG_DEFAULT_OVERRIDE, 8));\n  };\n\n  ModalDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ModalDirective,\n    selectors: [[\"\", \"bsModal\", \"\"]],\n    hostBindings: function ModalDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"mousedown\", function ModalDirective_mousedown_HostBindingHandler($event) {\n          return ctx.onClickStarted($event);\n        })(\"mouseup\", function ModalDirective_mouseup_HostBindingHandler($event) {\n          return ctx.onClickStop($event);\n        })(\"keydown.esc\", function ModalDirective_keydown_esc_HostBindingHandler($event) {\n          return ctx.onEsc($event);\n        });\n      }\n    },\n    inputs: {\n      config: \"config\",\n      closeInterceptor: \"closeInterceptor\"\n    },\n    outputs: {\n      onShow: \"onShow\",\n      onShown: \"onShown\",\n      onHide: \"onHide\",\n      onHidden: \"onHidden\"\n    },\n    exportAs: [\"bs-modal\"]\n  });\n  return ModalDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet currentId = 1;\nlet BsModalService = /*#__PURE__*/(() => {\n  class BsModalService {\n    constructor(rendererFactory, clf, modalDefaultOption) {\n      this.clf = clf;\n      this.modalDefaultOption = modalDefaultOption;\n      this.onShow = new EventEmitter();\n      this.onShown = new EventEmitter();\n      this.onHide = new EventEmitter();\n      this.onHidden = new EventEmitter();\n      this.isBodyOverflowing = false;\n      this.originalBodyPadding = 0;\n      this.scrollbarWidth = 0;\n      this.modalsCount = 0;\n      this.loaders = [];\n      this._backdropLoader = this.clf.createLoader();\n      this._renderer = rendererFactory.createRenderer(null, null);\n      this.config = modalDefaultOption ? Object.assign({}, modalConfigDefaults, modalDefaultOption) : modalConfigDefaults;\n    }\n    /** Shows a modal */\n\n\n    show( // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    content, config) {\n      this.modalsCount++;\n\n      this._createLoaders(); // must be different per every show() call\n\n\n      const id = (config === null || config === void 0 ? void 0 : config.id) || currentId++;\n      this.config = this.modalDefaultOption ? Object.assign({}, modalConfigDefaults, this.modalDefaultOption, config) : Object.assign({}, modalConfigDefaults, config);\n      this.config.id = id;\n\n      this._showBackdrop();\n\n      this.lastDismissReason = void 0;\n      return this._showModal(content);\n    }\n\n    hide(id) {\n      if (this.modalsCount === 1 || id == null) {\n        this._hideBackdrop();\n\n        this.resetScrollbar();\n      }\n\n      this.modalsCount = this.modalsCount >= 1 && id != null ? this.modalsCount - 1 : 0;\n      setTimeout(() => {\n        this._hideModal(id);\n\n        this.removeLoaders(id);\n      }, this.config.animated ? TRANSITION_DURATIONS.BACKDROP : 0);\n    }\n\n    _showBackdrop() {\n      const isBackdropEnabled = this.config.backdrop === true || this.config.backdrop === 'static';\n      const isBackdropInDOM = !this.backdropRef || !this.backdropRef.instance.isShown;\n\n      if (this.modalsCount === 1) {\n        this.removeBackdrop();\n\n        if (isBackdropEnabled && isBackdropInDOM) {\n          this._backdropLoader.attach(ModalBackdropComponent).to('body').show({\n            isAnimated: this.config.animated\n          });\n\n          this.backdropRef = this._backdropLoader._componentRef;\n        }\n      }\n    }\n\n    _hideBackdrop() {\n      if (!this.backdropRef) {\n        return;\n      }\n\n      this.backdropRef.instance.isShown = false;\n      const duration = this.config.animated ? TRANSITION_DURATIONS.BACKDROP : 0;\n      setTimeout(() => this.removeBackdrop(), duration);\n    } // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\n    _showModal(content) {\n      var _a;\n\n      const modalLoader = this.loaders[this.loaders.length - 1];\n\n      if (this.config && this.config.providers) {\n        for (const provider of this.config.providers) {\n          modalLoader.provide(provider);\n        }\n      } // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\n      const bsModalRef = new BsModalRef();\n      const modalContainerRef = modalLoader.provide({\n        provide: ModalOptions,\n        useValue: this.config\n      }).provide({\n        provide: BsModalRef,\n        useValue: bsModalRef\n      }).attach(ModalContainerComponent).to('body');\n\n      bsModalRef.hide = () => {\n        var _a;\n\n        return (_a = modalContainerRef.instance) === null || _a === void 0 ? void 0 : _a.hide();\n      };\n\n      bsModalRef.setClass = newClass => {\n        if (modalContainerRef.instance) {\n          modalContainerRef.instance.config.class = newClass;\n        }\n      };\n\n      bsModalRef.onHidden = new EventEmitter();\n      bsModalRef.onHide = new EventEmitter();\n      this.copyEvent(modalLoader.onBeforeHide, bsModalRef.onHide);\n      this.copyEvent(modalLoader.onHidden, bsModalRef.onHidden); // call 'show' method after assign setClass in bsModalRef.\n      // it makes modal component's bsModalRef available to call setClass method\n\n      modalContainerRef.show({\n        content,\n        isAnimated: this.config.animated,\n        initialState: this.config.initialState,\n        bsModalService: this,\n        id: this.config.id\n      });\n\n      if (modalContainerRef.instance) {\n        modalContainerRef.instance.level = this.getModalsCount();\n        bsModalRef.content = modalLoader.getInnerComponent();\n        bsModalRef.id = (_a = modalContainerRef.instance.config) === null || _a === void 0 ? void 0 : _a.id;\n      }\n\n      return bsModalRef;\n    }\n\n    _hideModal(id) {\n      if (id != null) {\n        const indexToRemove = this.loaders.findIndex(loader => {\n          var _a;\n\n          return ((_a = loader.instance) === null || _a === void 0 ? void 0 : _a.config.id) === id;\n        });\n        const modalLoader = this.loaders[indexToRemove];\n\n        if (modalLoader) {\n          modalLoader.hide(id);\n        }\n      } else {\n        this.loaders.forEach(loader => {\n          if (loader.instance) {\n            loader.hide(loader.instance.config.id);\n          }\n        });\n      }\n    }\n\n    getModalsCount() {\n      return this.modalsCount;\n    }\n\n    setDismissReason(reason) {\n      this.lastDismissReason = reason;\n    }\n\n    removeBackdrop() {\n      this._renderer.removeClass(document.body, CLASS_NAME.OPEN);\n\n      this._renderer.setStyle(document.body, 'overflow-y', '');\n\n      this._backdropLoader.hide();\n\n      this.backdropRef = void 0;\n    }\n    /** Checks if the body is overflowing and sets scrollbar width */\n\n    /** @internal */\n\n\n    checkScrollbar() {\n      this.isBodyOverflowing = document.body.clientWidth < window.innerWidth;\n      this.scrollbarWidth = this.getScrollbarWidth();\n    }\n\n    setScrollbar() {\n      if (!document) {\n        return;\n      }\n\n      this.originalBodyPadding = parseInt(window.getComputedStyle(document.body).getPropertyValue('padding-right') || '0', 10);\n\n      if (this.isBodyOverflowing) {\n        document.body.style.paddingRight = `${this.originalBodyPadding + this.scrollbarWidth}px`;\n      }\n    }\n\n    resetScrollbar() {\n      document.body.style.paddingRight = `${this.originalBodyPadding}px`;\n    } // thx d.walsh\n\n\n    getScrollbarWidth() {\n      const scrollDiv = this._renderer.createElement('div');\n\n      this._renderer.addClass(scrollDiv, CLASS_NAME.SCROLLBAR_MEASURER);\n\n      this._renderer.appendChild(document.body, scrollDiv);\n\n      const scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n\n      this._renderer.removeChild(document.body, scrollDiv);\n\n      return scrollbarWidth;\n    }\n\n    _createLoaders() {\n      const loader = this.clf.createLoader();\n      this.copyEvent(loader.onBeforeShow, this.onShow);\n      this.copyEvent(loader.onShown, this.onShown);\n      this.copyEvent(loader.onBeforeHide, this.onHide);\n      this.copyEvent(loader.onHidden, this.onHidden);\n      this.loaders.push(loader);\n    }\n\n    removeLoaders(id) {\n      if (id != null) {\n        const indexToRemove = this.loaders.findIndex(loader => {\n          var _a;\n\n          return ((_a = loader.instance) === null || _a === void 0 ? void 0 : _a.config.id) === id;\n        });\n\n        if (indexToRemove >= 0) {\n          this.loaders.splice(indexToRemove, 1);\n          this.loaders.forEach((loader, i) => {\n            if (loader.instance) {\n              loader.instance.level = i + 1;\n            }\n          });\n        }\n      } else {\n        this.loaders.splice(0, this.loaders.length);\n      }\n    }\n\n    copyEvent(from, to) {\n      from.subscribe(data => {\n        to.emit(this.lastDismissReason || data);\n      });\n    }\n\n  }\n\n  BsModalService.ɵfac = function BsModalService_Factory(t) {\n    return new (t || BsModalService)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(i1.ComponentLoaderFactory), i0.ɵɵinject(MODAL_CONFIG_DEFAULT_OVERRIDE, 8));\n  };\n\n  BsModalService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BsModalService,\n    factory: BsModalService.ɵfac,\n    providedIn: 'platform'\n  });\n  return BsModalService;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst focusTrapModule = FocusTrapModule.forRoot();\nlet ModalModule = /*#__PURE__*/(() => {\n  class ModalModule {\n    static forRoot() {\n      return {\n        ngModule: ModalModule,\n        providers: [BsModalService, ComponentLoaderFactory, PositioningService]\n      };\n    }\n\n    static forChild() {\n      return {\n        ngModule: ModalModule,\n        providers: [BsModalService, ComponentLoaderFactory, PositioningService]\n      };\n    }\n\n  }\n\n  ModalModule.ɵfac = function ModalModule_Factory(t) {\n    return new (t || ModalModule)();\n  };\n\n  ModalModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ModalModule\n  });\n  ModalModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[FocusTrapModule]]\n  });\n  return ModalModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BsModalRef, BsModalService, MODAL_CONFIG_DEFAULT_OVERRIDE, ModalBackdropComponent, ModalBackdropOptions, ModalContainerComponent, ModalDirective, ModalModule, ModalOptions }; //# sourceMappingURL=ngx-bootstrap-modal.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}