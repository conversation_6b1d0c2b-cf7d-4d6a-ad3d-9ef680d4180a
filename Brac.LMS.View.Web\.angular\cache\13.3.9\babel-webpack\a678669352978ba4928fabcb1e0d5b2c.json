{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Output, HostBinding, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/animations';\nimport { style, animate } from '@angular/animations';\nconst COLLAPSE_ANIMATION_TIMING = '400ms cubic-bezier(0.4,0.0,0.2,1)';\nconst expandAnimation = [style({\n  height: 0,\n  visibility: 'hidden'\n}), animate(COLLAPSE_ANIMATION_TIMING, style({\n  height: '*',\n  visibility: 'visible'\n}))];\nconst collapseAnimation = [style({\n  height: '*',\n  visibility: 'visible'\n}), animate(COLLAPSE_ANIMATION_TIMING, style({\n  height: 0,\n  visibility: 'hidden'\n}))];\nlet CollapseDirective = /*#__PURE__*/(() => {\n  class CollapseDirective {\n    constructor(_el, _renderer, _builder) {\n      this._el = _el;\n      this._renderer = _renderer;\n      /** This event fires as soon as content collapses */\n\n      this.collapsed = new EventEmitter();\n      /** This event fires when collapsing is started */\n\n      this.collapses = new EventEmitter();\n      /** This event fires as soon as content becomes visible */\n\n      this.expanded = new EventEmitter();\n      /** This event fires when expansion is started */\n\n      this.expands = new EventEmitter(); // shown\n\n      this.isExpanded = true;\n      this.collapseNewValue = true; // hidden\n\n      this.isCollapsed = false; // stale state\n\n      this.isCollapse = true; // animation state\n\n      this.isCollapsing = false;\n      /** turn on/off animation */\n\n      this.isAnimated = false;\n      this._display = 'block';\n      this._stylesLoaded = false;\n      this._COLLAPSE_ACTION_NAME = 'collapse';\n      this._EXPAND_ACTION_NAME = 'expand';\n      this._factoryCollapseAnimation = _builder.build(collapseAnimation);\n      this._factoryExpandAnimation = _builder.build(expandAnimation);\n    }\n\n    set display(value) {\n      this._display = value;\n\n      if (value === 'none') {\n        this.hide();\n        return;\n      }\n\n      this.isAnimated ? this.toggle() : this.show();\n    }\n    /** A flag indicating visibility of content (shown or hidden) */\n\n\n    set collapse(value) {\n      this.collapseNewValue = value;\n\n      if (!this._player || this._isAnimationDone) {\n        this.isExpanded = value;\n        this.toggle();\n      }\n    }\n\n    get collapse() {\n      return this.isExpanded;\n    }\n\n    ngAfterViewChecked() {\n      this._stylesLoaded = true;\n\n      if (!this._player || !this._isAnimationDone) {\n        return;\n      }\n\n      this._player.reset();\n\n      this._renderer.setStyle(this._el.nativeElement, 'height', '*');\n    }\n    /** allows to manually toggle content visibility */\n\n\n    toggle() {\n      if (this.isExpanded) {\n        this.hide();\n      } else {\n        this.show();\n      }\n    }\n    /** allows to manually hide content */\n\n\n    hide() {\n      this.isCollapsing = true;\n      this.isExpanded = false;\n      this.isCollapsed = true;\n      this.isCollapsing = false;\n      this.collapses.emit(this);\n      this._isAnimationDone = false;\n      this.animationRun(this.isAnimated, this._COLLAPSE_ACTION_NAME)(() => {\n        this._isAnimationDone = true;\n\n        if (this.collapseNewValue !== this.isCollapsed && this.isAnimated) {\n          this.show();\n          return;\n        }\n\n        this.collapsed.emit(this);\n\n        this._renderer.setStyle(this._el.nativeElement, 'display', 'none');\n      });\n    }\n    /** allows to manually show collapsed content */\n\n\n    show() {\n      this._renderer.setStyle(this._el.nativeElement, 'display', this._display);\n\n      this.isCollapsing = true;\n      this.isExpanded = true;\n      this.isCollapsed = false;\n      this.isCollapsing = false;\n      this.expands.emit(this);\n      this._isAnimationDone = false;\n      this.animationRun(this.isAnimated, this._EXPAND_ACTION_NAME)(() => {\n        this._isAnimationDone = true;\n\n        if (this.collapseNewValue !== this.isCollapsed && this.isAnimated) {\n          this.hide();\n          return;\n        }\n\n        this.expanded.emit(this);\n\n        this._renderer.removeStyle(this._el.nativeElement, 'overflow');\n      });\n    }\n\n    animationRun(isAnimated, action) {\n      if (!isAnimated || !this._stylesLoaded) {\n        return callback => callback();\n      }\n\n      this._renderer.setStyle(this._el.nativeElement, 'overflow', 'hidden');\n\n      this._renderer.addClass(this._el.nativeElement, 'collapse');\n\n      const factoryAnimation = action === this._EXPAND_ACTION_NAME ? this._factoryExpandAnimation : this._factoryCollapseAnimation;\n\n      if (this._player) {\n        this._player.destroy();\n      }\n\n      this._player = factoryAnimation.create(this._el.nativeElement);\n\n      this._player.play();\n\n      return callback => {\n        var _a;\n\n        return (_a = this._player) === null || _a === void 0 ? void 0 : _a.onDone(callback);\n      };\n    }\n\n  }\n\n  CollapseDirective.ɵfac = function CollapseDirective_Factory(t) {\n    return new (t || CollapseDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.AnimationBuilder));\n  };\n\n  CollapseDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CollapseDirective,\n    selectors: [[\"\", \"collapse\", \"\"]],\n    hostVars: 9,\n    hostBindings: function CollapseDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-hidden\", ctx.isCollapsed);\n        i0.ɵɵclassProp(\"collapse\", ctx.isCollapse)(\"in\", ctx.isExpanded)(\"show\", ctx.isExpanded)(\"collapsing\", ctx.isCollapsing);\n      }\n    },\n    inputs: {\n      display: \"display\",\n      isAnimated: \"isAnimated\",\n      collapse: \"collapse\"\n    },\n    outputs: {\n      collapsed: \"collapsed\",\n      collapses: \"collapses\",\n      expanded: \"expanded\",\n      expands: \"expands\"\n    },\n    exportAs: [\"bs-collapse\"]\n  });\n  return CollapseDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet CollapseModule = /*#__PURE__*/(() => {\n  class CollapseModule {\n    static forRoot() {\n      return {\n        ngModule: CollapseModule,\n        providers: []\n      };\n    }\n\n  }\n\n  CollapseModule.ɵfac = function CollapseModule_Factory(t) {\n    return new (t || CollapseModule)();\n  };\n\n  CollapseModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CollapseModule\n  });\n  CollapseModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return CollapseModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CollapseDirective, CollapseModule }; //# sourceMappingURL=ngx-bootstrap-collapse.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}