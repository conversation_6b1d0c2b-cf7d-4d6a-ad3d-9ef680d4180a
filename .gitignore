# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Visual Studio and JetBrains IDE files
.vscode/
.vs/
.idea/

# Compiled output folders (case-insensitive)
[Bb]in/
[Oo]bj/
[Tt]arget/
[Dd]ist/

# Node.js
node_modules/
dist/

# Compiled files
*.dll
*.pdb
*.cache
*.class
*.py[cod]
# Log files
*.log

# Package files
*.jar
*.war

# Unit test reports
TEST-*.xml

# macOS and Windows system files
.DS_Store
Thumbs.db

# Executables
*.app
*.exe

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv
/Brac.LMS.App.API/[Bb]in/
/Brac.LMS.App.API/[Oo]bj/
/Brac.LMS.App.API/[Bb]in - Copy
/Brac.LMS.App.API/Brac.LMS.App.API.csproj.user
/Brac.LMS.App.API/Brac.LMS.App.API.csproj.user
Videos/
Images/
/ERP.Sync.Manager/publish
/ERP.Sync.Manager/publish
/ERP.Sync.Manager/publish
/packages
*.user
/Brac.LMS.App.API/Properties/PublishProfiles
*.user
*.user
