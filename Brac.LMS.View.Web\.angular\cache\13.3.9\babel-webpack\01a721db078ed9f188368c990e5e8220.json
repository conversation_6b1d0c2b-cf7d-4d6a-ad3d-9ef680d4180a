{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Breton [br]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/jbleduigou\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  function relativeTimeWithMutation(number, withoutSuffix, key) {\n    var format = {\n      mm: 'munutenn',\n      MM: 'miz',\n      dd: 'devezh'\n    };\n    return number + ' ' + mutation(format[key], number);\n  }\n\n  function specialMutationForYears(number) {\n    switch (lastNumber(number)) {\n      case 1:\n      case 3:\n      case 4:\n      case 5:\n      case 9:\n        return number + ' bloaz';\n\n      default:\n        return number + ' vloaz';\n    }\n  }\n\n  function lastNumber(number) {\n    if (number > 9) {\n      return lastNumber(number % 10);\n    }\n\n    return number;\n  }\n\n  function mutation(text, number) {\n    if (number === 2) {\n      return softMutation(text);\n    }\n\n    return text;\n  }\n\n  function softMutation(text) {\n    var mutationTable = {\n      m: 'v',\n      b: 'v',\n      d: 'z'\n    };\n\n    if (mutationTable[text.charAt(0)] === undefined) {\n      return text;\n    }\n\n    return mutationTable[text.charAt(0)] + text.substring(1);\n  }\n\n  var monthsParse = [/^gen/i, /^c[ʼ\\']hwe/i, /^meu/i, /^ebr/i, /^mae/i, /^(mez|eve)/i, /^gou/i, /^eos/i, /^gwe/i, /^her/i, /^du/i, /^ker/i],\n      monthsRegex = /^(genver|c[ʼ\\']hwevrer|meurzh|ebrel|mae|mezheven|gouere|eost|gwengolo|here|du|kerzu|gen|c[ʼ\\']hwe|meu|ebr|mae|eve|gou|eos|gwe|her|du|ker)/i,\n      monthsStrictRegex = /^(genver|c[ʼ\\']hwevrer|meurzh|ebrel|mae|mezheven|gouere|eost|gwengolo|here|du|kerzu)/i,\n      monthsShortStrictRegex = /^(gen|c[ʼ\\']hwe|meu|ebr|mae|eve|gou|eos|gwe|her|du|ker)/i,\n      fullWeekdaysParse = [/^sul/i, /^lun/i, /^meurzh/i, /^merc[ʼ\\']her/i, /^yaou/i, /^gwener/i, /^sadorn/i],\n      shortWeekdaysParse = [/^Sul/i, /^Lun/i, /^Meu/i, /^Mer/i, /^Yao/i, /^Gwe/i, /^Sad/i],\n      minWeekdaysParse = [/^Su/i, /^Lu/i, /^Me([^r]|$)/i, /^Mer/i, /^Ya/i, /^Gw/i, /^Sa/i];\n  var br = moment.defineLocale('br', {\n    months: 'Genver_Cʼhwevrer_Meurzh_Ebrel_Mae_Mezheven_Gouere_Eost_Gwengolo_Here_Du_Kerzu'.split('_'),\n    monthsShort: 'Gen_Cʼhwe_Meu_Ebr_Mae_Eve_Gou_Eos_Gwe_Her_Du_Ker'.split('_'),\n    weekdays: 'Sul_Lun_Meurzh_Mercʼher_Yaou_Gwener_Sadorn'.split('_'),\n    weekdaysShort: 'Sul_Lun_Meu_Mer_Yao_Gwe_Sad'.split('_'),\n    weekdaysMin: 'Su_Lu_Me_Mer_Ya_Gw_Sa'.split('_'),\n    weekdaysParse: minWeekdaysParse,\n    fullWeekdaysParse: fullWeekdaysParse,\n    shortWeekdaysParse: shortWeekdaysParse,\n    minWeekdaysParse: minWeekdaysParse,\n    monthsRegex: monthsRegex,\n    monthsShortRegex: monthsRegex,\n    monthsStrictRegex: monthsStrictRegex,\n    monthsShortStrictRegex: monthsShortStrictRegex,\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: monthsParse,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D [a viz] MMMM YYYY',\n      LLL: 'D [a viz] MMMM YYYY HH:mm',\n      LLLL: 'dddd, D [a viz] MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Hiziv da] LT',\n      nextDay: '[Warcʼhoazh da] LT',\n      nextWeek: 'dddd [da] LT',\n      lastDay: '[Decʼh da] LT',\n      lastWeek: 'dddd [paset da] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'a-benn %s',\n      past: '%s ʼzo',\n      s: 'un nebeud segondennoù',\n      ss: '%d eilenn',\n      m: 'ur vunutenn',\n      mm: relativeTimeWithMutation,\n      h: 'un eur',\n      hh: '%d eur',\n      d: 'un devezh',\n      dd: relativeTimeWithMutation,\n      M: 'ur miz',\n      MM: relativeTimeWithMutation,\n      y: 'ur bloaz',\n      yy: specialMutationForYears\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(añ|vet)/,\n    ordinal: function (number) {\n      var output = number === 1 ? 'añ' : 'vet';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    },\n    meridiemParse: /a.m.|g.m./,\n    // goude merenn | a-raok merenn\n    isPM: function (token) {\n      return token === 'g.m.';\n    },\n    meridiem: function (hour, minute, isLower) {\n      return hour < 12 ? 'a.m.' : 'g.m.';\n    }\n  });\n  return br;\n});", "map": null, "metadata": {}, "sourceType": "script"}