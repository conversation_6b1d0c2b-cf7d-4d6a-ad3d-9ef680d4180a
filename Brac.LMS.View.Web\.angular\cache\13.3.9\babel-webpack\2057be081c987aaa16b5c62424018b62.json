{"ast": null, "code": "/**\n * @license Angular v13.1.3\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport * as i0 from '@angular/core';\nimport { ɵisObservable, ɵisPromise, EventEmitter, Directive, Attribute, Output, Component, NgModuleRef, InjectionToken, InjectFlags, NgModuleFactory, ɵConsole, NgZone, Injectable, Input, HostListener, HostBinding, Optional, ContentChildren, Injector, Compiler, NgProbeToken, ANALYZE_FOR_ENTRY_COMPONENTS, SkipSelf, Inject, APP_INITIALIZER, APP_BOOTSTRAP_LISTENER, NgModule, ApplicationRef, Version } from '@angular/core';\nimport { from, of, BehaviorSubject, combineLatest, Observable, EmptyError, concat, defer, EMPTY, ConnectableObservable, Subject } from 'rxjs';\nimport { map, switchMap, take, startWith, scan, filter, catchError, concatMap, last as last$1, first, mergeMap, tap, takeLast, refCount, finalize, mergeAll } from 'rxjs/operators';\nimport * as i3 from '@angular/common';\nimport { Location, LocationStrategy, PlatformLocation, APP_BASE_HREF, ViewportScroller, HashLocationStrategy, PathLocationStrategy, LOCATION_INITIALIZED } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Base for events the router goes through, as opposed to events tied to a specific\n * route. Fired one time for any given navigation.\n *\n * The following code shows how a class subscribes to router events.\n *\n * ```ts\n * import {Event, RouterEvent, Router} from '@angular/router';\n *\n * class MyService {\n *   constructor(public router: Router) {\n *     router.events.pipe(\n *        filter((e: Event): e is RouterEvent => e instanceof RouterEvent)\n *     ).subscribe((e: RouterEvent) => {\n *       // Do something\n *     });\n *   }\n * }\n * ```\n *\n * @see `Event`\n * @see [Router events summary](guide/router-reference#router-events)\n * @publicApi\n */\n\nclass RouterEvent {\n  constructor(\n  /** A unique ID that the router assigns to every router navigation. */\n  id,\n  /** The URL that is the destination for this navigation. */\n  url) {\n    this.id = id;\n    this.url = url;\n  }\n\n}\n/**\n * An event triggered when a navigation starts.\n *\n * @publicApi\n */\n\n\nclass NavigationStart extends RouterEvent {\n  constructor(\n  /** @docsNotRequired */\n  id,\n  /** @docsNotRequired */\n  url,\n  /** @docsNotRequired */\n  navigationTrigger = 'imperative',\n  /** @docsNotRequired */\n  restoredState = null) {\n    super(id, url);\n    this.navigationTrigger = navigationTrigger;\n    this.restoredState = restoredState;\n  }\n  /** @docsNotRequired */\n\n\n  toString() {\n    return `NavigationStart(id: ${this.id}, url: '${this.url}')`;\n  }\n\n}\n/**\n * An event triggered when a navigation ends successfully.\n *\n * @see `NavigationStart`\n * @see `NavigationCancel`\n * @see `NavigationError`\n *\n * @publicApi\n */\n\n\nclass NavigationEnd extends RouterEvent {\n  constructor(\n  /** @docsNotRequired */\n  id,\n  /** @docsNotRequired */\n  url,\n  /** @docsNotRequired */\n  urlAfterRedirects) {\n    super(id, url);\n    this.urlAfterRedirects = urlAfterRedirects;\n  }\n  /** @docsNotRequired */\n\n\n  toString() {\n    return `NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`;\n  }\n\n}\n/**\n * An event triggered when a navigation is canceled, directly or indirectly.\n * This can happen for several reasons including when a route guard\n * returns `false` or initiates a redirect by returning a `UrlTree`.\n *\n * @see `NavigationStart`\n * @see `NavigationEnd`\n * @see `NavigationError`\n *\n * @publicApi\n */\n\n\nclass NavigationCancel extends RouterEvent {\n  constructor(\n  /** @docsNotRequired */\n  id,\n  /** @docsNotRequired */\n  url,\n  /** @docsNotRequired */\n  reason) {\n    super(id, url);\n    this.reason = reason;\n  }\n  /** @docsNotRequired */\n\n\n  toString() {\n    return `NavigationCancel(id: ${this.id}, url: '${this.url}')`;\n  }\n\n}\n/**\n * An event triggered when a navigation fails due to an unexpected error.\n *\n * @see `NavigationStart`\n * @see `NavigationEnd`\n * @see `NavigationCancel`\n *\n * @publicApi\n */\n\n\nclass NavigationError extends RouterEvent {\n  constructor(\n  /** @docsNotRequired */\n  id,\n  /** @docsNotRequired */\n  url,\n  /** @docsNotRequired */\n  error) {\n    super(id, url);\n    this.error = error;\n  }\n  /** @docsNotRequired */\n\n\n  toString() {\n    return `NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`;\n  }\n\n}\n/**\n * An event triggered when routes are recognized.\n *\n * @publicApi\n */\n\n\nclass RoutesRecognized extends RouterEvent {\n  constructor(\n  /** @docsNotRequired */\n  id,\n  /** @docsNotRequired */\n  url,\n  /** @docsNotRequired */\n  urlAfterRedirects,\n  /** @docsNotRequired */\n  state) {\n    super(id, url);\n    this.urlAfterRedirects = urlAfterRedirects;\n    this.state = state;\n  }\n  /** @docsNotRequired */\n\n\n  toString() {\n    return `RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`;\n  }\n\n}\n/**\n * An event triggered at the start of the Guard phase of routing.\n *\n * @see `GuardsCheckEnd`\n *\n * @publicApi\n */\n\n\nclass GuardsCheckStart extends RouterEvent {\n  constructor(\n  /** @docsNotRequired */\n  id,\n  /** @docsNotRequired */\n  url,\n  /** @docsNotRequired */\n  urlAfterRedirects,\n  /** @docsNotRequired */\n  state) {\n    super(id, url);\n    this.urlAfterRedirects = urlAfterRedirects;\n    this.state = state;\n  }\n\n  toString() {\n    return `GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`;\n  }\n\n}\n/**\n * An event triggered at the end of the Guard phase of routing.\n *\n * @see `GuardsCheckStart`\n *\n * @publicApi\n */\n\n\nclass GuardsCheckEnd extends RouterEvent {\n  constructor(\n  /** @docsNotRequired */\n  id,\n  /** @docsNotRequired */\n  url,\n  /** @docsNotRequired */\n  urlAfterRedirects,\n  /** @docsNotRequired */\n  state,\n  /** @docsNotRequired */\n  shouldActivate) {\n    super(id, url);\n    this.urlAfterRedirects = urlAfterRedirects;\n    this.state = state;\n    this.shouldActivate = shouldActivate;\n  }\n\n  toString() {\n    return `GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`;\n  }\n\n}\n/**\n * An event triggered at the start of the Resolve phase of routing.\n *\n * Runs in the \"resolve\" phase whether or not there is anything to resolve.\n * In future, may change to only run when there are things to be resolved.\n *\n * @see `ResolveEnd`\n *\n * @publicApi\n */\n\n\nclass ResolveStart extends RouterEvent {\n  constructor(\n  /** @docsNotRequired */\n  id,\n  /** @docsNotRequired */\n  url,\n  /** @docsNotRequired */\n  urlAfterRedirects,\n  /** @docsNotRequired */\n  state) {\n    super(id, url);\n    this.urlAfterRedirects = urlAfterRedirects;\n    this.state = state;\n  }\n\n  toString() {\n    return `ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`;\n  }\n\n}\n/**\n * An event triggered at the end of the Resolve phase of routing.\n * @see `ResolveStart`.\n *\n * @publicApi\n */\n\n\nclass ResolveEnd extends RouterEvent {\n  constructor(\n  /** @docsNotRequired */\n  id,\n  /** @docsNotRequired */\n  url,\n  /** @docsNotRequired */\n  urlAfterRedirects,\n  /** @docsNotRequired */\n  state) {\n    super(id, url);\n    this.urlAfterRedirects = urlAfterRedirects;\n    this.state = state;\n  }\n\n  toString() {\n    return `ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`;\n  }\n\n}\n/**\n * An event triggered before lazy loading a route configuration.\n *\n * @see `RouteConfigLoadEnd`\n *\n * @publicApi\n */\n\n\nclass RouteConfigLoadStart {\n  constructor(\n  /** @docsNotRequired */\n  route) {\n    this.route = route;\n  }\n\n  toString() {\n    return `RouteConfigLoadStart(path: ${this.route.path})`;\n  }\n\n}\n/**\n * An event triggered when a route has been lazy loaded.\n *\n * @see `RouteConfigLoadStart`\n *\n * @publicApi\n */\n\n\nclass RouteConfigLoadEnd {\n  constructor(\n  /** @docsNotRequired */\n  route) {\n    this.route = route;\n  }\n\n  toString() {\n    return `RouteConfigLoadEnd(path: ${this.route.path})`;\n  }\n\n}\n/**\n * An event triggered at the start of the child-activation\n * part of the Resolve phase of routing.\n * @see  `ChildActivationEnd`\n * @see `ResolveStart`\n *\n * @publicApi\n */\n\n\nclass ChildActivationStart {\n  constructor(\n  /** @docsNotRequired */\n  snapshot) {\n    this.snapshot = snapshot;\n  }\n\n  toString() {\n    const path = this.snapshot.routeConfig && this.snapshot.routeConfig.path || '';\n    return `ChildActivationStart(path: '${path}')`;\n  }\n\n}\n/**\n * An event triggered at the end of the child-activation part\n * of the Resolve phase of routing.\n * @see `ChildActivationStart`\n * @see `ResolveStart`\n * @publicApi\n */\n\n\nclass ChildActivationEnd {\n  constructor(\n  /** @docsNotRequired */\n  snapshot) {\n    this.snapshot = snapshot;\n  }\n\n  toString() {\n    const path = this.snapshot.routeConfig && this.snapshot.routeConfig.path || '';\n    return `ChildActivationEnd(path: '${path}')`;\n  }\n\n}\n/**\n * An event triggered at the start of the activation part\n * of the Resolve phase of routing.\n * @see `ActivationEnd`\n * @see `ResolveStart`\n *\n * @publicApi\n */\n\n\nclass ActivationStart {\n  constructor(\n  /** @docsNotRequired */\n  snapshot) {\n    this.snapshot = snapshot;\n  }\n\n  toString() {\n    const path = this.snapshot.routeConfig && this.snapshot.routeConfig.path || '';\n    return `ActivationStart(path: '${path}')`;\n  }\n\n}\n/**\n * An event triggered at the end of the activation part\n * of the Resolve phase of routing.\n * @see `ActivationStart`\n * @see `ResolveStart`\n *\n * @publicApi\n */\n\n\nclass ActivationEnd {\n  constructor(\n  /** @docsNotRequired */\n  snapshot) {\n    this.snapshot = snapshot;\n  }\n\n  toString() {\n    const path = this.snapshot.routeConfig && this.snapshot.routeConfig.path || '';\n    return `ActivationEnd(path: '${path}')`;\n  }\n\n}\n/**\n * An event triggered by scrolling.\n *\n * @publicApi\n */\n\n\nclass Scroll {\n  constructor(\n  /** @docsNotRequired */\n  routerEvent,\n  /** @docsNotRequired */\n  position,\n  /** @docsNotRequired */\n  anchor) {\n    this.routerEvent = routerEvent;\n    this.position = position;\n    this.anchor = anchor;\n  }\n\n  toString() {\n    const pos = this.position ? `${this.position[0]}, ${this.position[1]}` : null;\n    return `Scroll(anchor: '${this.anchor}', position: '${pos}')`;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The primary routing outlet.\n *\n * @publicApi\n */\n\n\nconst PRIMARY_OUTLET = 'primary';\n\nclass ParamsAsMap {\n  constructor(params) {\n    this.params = params || {};\n  }\n\n  has(name) {\n    return Object.prototype.hasOwnProperty.call(this.params, name);\n  }\n\n  get(name) {\n    if (this.has(name)) {\n      const v = this.params[name];\n      return Array.isArray(v) ? v[0] : v;\n    }\n\n    return null;\n  }\n\n  getAll(name) {\n    if (this.has(name)) {\n      const v = this.params[name];\n      return Array.isArray(v) ? v : [v];\n    }\n\n    return [];\n  }\n\n  get keys() {\n    return Object.keys(this.params);\n  }\n\n}\n/**\n * Converts a `Params` instance to a `ParamMap`.\n * @param params The instance to convert.\n * @returns The new map instance.\n *\n * @publicApi\n */\n\n\nfunction convertToParamMap(params) {\n  return new ParamsAsMap(params);\n}\n\nconst NAVIGATION_CANCELING_ERROR = 'ngNavigationCancelingError';\n\nfunction navigationCancelingError(message) {\n  const error = Error('NavigationCancelingError: ' + message);\n  error[NAVIGATION_CANCELING_ERROR] = true;\n  return error;\n}\n\nfunction isNavigationCancelingError(error) {\n  return error && error[NAVIGATION_CANCELING_ERROR];\n} // Matches the route configuration (`route`) against the actual URL (`segments`).\n\n\nfunction defaultUrlMatcher(segments, segmentGroup, route) {\n  const parts = route.path.split('/');\n\n  if (parts.length > segments.length) {\n    // The actual URL is shorter than the config, no match\n    return null;\n  }\n\n  if (route.pathMatch === 'full' && (segmentGroup.hasChildren() || parts.length < segments.length)) {\n    // The config is longer than the actual URL but we are looking for a full match, return null\n    return null;\n  }\n\n  const posParams = {}; // Check each config part against the actual URL\n\n  for (let index = 0; index < parts.length; index++) {\n    const part = parts[index];\n    const segment = segments[index];\n    const isParameter = part.startsWith(':');\n\n    if (isParameter) {\n      posParams[part.substring(1)] = segment;\n    } else if (part !== segment.path) {\n      // The actual URL part does not match the config, no match\n      return null;\n    }\n  }\n\n  return {\n    consumed: segments.slice(0, parts.length),\n    posParams\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction shallowEqualArrays(a, b) {\n  if (a.length !== b.length) return false;\n\n  for (let i = 0; i < a.length; ++i) {\n    if (!shallowEqual(a[i], b[i])) return false;\n  }\n\n  return true;\n}\n\nfunction shallowEqual(a, b) {\n  // While `undefined` should never be possible, it would sometimes be the case in IE 11\n  // and pre-chromium Edge. The check below accounts for this edge case.\n  const k1 = a ? Object.keys(a) : undefined;\n  const k2 = b ? Object.keys(b) : undefined;\n\n  if (!k1 || !k2 || k1.length != k2.length) {\n    return false;\n  }\n\n  let key;\n\n  for (let i = 0; i < k1.length; i++) {\n    key = k1[i];\n\n    if (!equalArraysOrString(a[key], b[key])) {\n      return false;\n    }\n  }\n\n  return true;\n}\n/**\n * Test equality for arrays of strings or a string.\n */\n\n\nfunction equalArraysOrString(a, b) {\n  if (Array.isArray(a) && Array.isArray(b)) {\n    if (a.length !== b.length) return false;\n    const aSorted = [...a].sort();\n    const bSorted = [...b].sort();\n    return aSorted.every((val, index) => bSorted[index] === val);\n  } else {\n    return a === b;\n  }\n}\n/**\n * Flattens single-level nested arrays.\n */\n\n\nfunction flatten(arr) {\n  return Array.prototype.concat.apply([], arr);\n}\n/**\n * Return the last element of an array.\n */\n\n\nfunction last(a) {\n  return a.length > 0 ? a[a.length - 1] : null;\n}\n/**\n * Verifys all booleans in an array are `true`.\n */\n\n\nfunction and(bools) {\n  return !bools.some(v => !v);\n}\n\nfunction forEach(map, callback) {\n  for (const prop in map) {\n    if (map.hasOwnProperty(prop)) {\n      callback(map[prop], prop);\n    }\n  }\n}\n\nfunction wrapIntoObservable(value) {\n  if (ɵisObservable(value)) {\n    return value;\n  }\n\n  if (ɵisPromise(value)) {\n    // Use `Promise.resolve()` to wrap promise-like instances.\n    // Required ie when a Resolver returns a AngularJS `$q` promise to correctly trigger the\n    // change detection.\n    return from(Promise.resolve(value));\n  }\n\n  return of(value);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction createEmptyUrlTree() {\n  return new UrlTree(new UrlSegmentGroup([], {}), {}, null);\n}\n\nconst pathCompareMap = {\n  'exact': equalSegmentGroups,\n  'subset': containsSegmentGroup\n};\nconst paramCompareMap = {\n  'exact': equalParams,\n  'subset': containsParams,\n  'ignored': () => true\n};\n\nfunction containsTree(container, containee, options) {\n  return pathCompareMap[options.paths](container.root, containee.root, options.matrixParams) && paramCompareMap[options.queryParams](container.queryParams, containee.queryParams) && !(options.fragment === 'exact' && container.fragment !== containee.fragment);\n}\n\nfunction equalParams(container, containee) {\n  // TODO: This does not handle array params correctly.\n  return shallowEqual(container, containee);\n}\n\nfunction equalSegmentGroups(container, containee, matrixParams) {\n  if (!equalPath(container.segments, containee.segments)) return false;\n\n  if (!matrixParamsMatch(container.segments, containee.segments, matrixParams)) {\n    return false;\n  }\n\n  if (container.numberOfChildren !== containee.numberOfChildren) return false;\n\n  for (const c in containee.children) {\n    if (!container.children[c]) return false;\n    if (!equalSegmentGroups(container.children[c], containee.children[c], matrixParams)) return false;\n  }\n\n  return true;\n}\n\nfunction containsParams(container, containee) {\n  return Object.keys(containee).length <= Object.keys(container).length && Object.keys(containee).every(key => equalArraysOrString(container[key], containee[key]));\n}\n\nfunction containsSegmentGroup(container, containee, matrixParams) {\n  return containsSegmentGroupHelper(container, containee, containee.segments, matrixParams);\n}\n\nfunction containsSegmentGroupHelper(container, containee, containeePaths, matrixParams) {\n  if (container.segments.length > containeePaths.length) {\n    const current = container.segments.slice(0, containeePaths.length);\n    if (!equalPath(current, containeePaths)) return false;\n    if (containee.hasChildren()) return false;\n    if (!matrixParamsMatch(current, containeePaths, matrixParams)) return false;\n    return true;\n  } else if (container.segments.length === containeePaths.length) {\n    if (!equalPath(container.segments, containeePaths)) return false;\n    if (!matrixParamsMatch(container.segments, containeePaths, matrixParams)) return false;\n\n    for (const c in containee.children) {\n      if (!container.children[c]) return false;\n\n      if (!containsSegmentGroup(container.children[c], containee.children[c], matrixParams)) {\n        return false;\n      }\n    }\n\n    return true;\n  } else {\n    const current = containeePaths.slice(0, container.segments.length);\n    const next = containeePaths.slice(container.segments.length);\n    if (!equalPath(container.segments, current)) return false;\n    if (!matrixParamsMatch(container.segments, current, matrixParams)) return false;\n    if (!container.children[PRIMARY_OUTLET]) return false;\n    return containsSegmentGroupHelper(container.children[PRIMARY_OUTLET], containee, next, matrixParams);\n  }\n}\n\nfunction matrixParamsMatch(containerPaths, containeePaths, options) {\n  return containeePaths.every((containeeSegment, i) => {\n    return paramCompareMap[options](containerPaths[i].parameters, containeeSegment.parameters);\n  });\n}\n/**\n * @description\n *\n * Represents the parsed URL.\n *\n * Since a router state is a tree, and the URL is nothing but a serialized state, the URL is a\n * serialized tree.\n * UrlTree is a data structure that provides a lot of affordances in dealing with URLs\n *\n * @usageNotes\n * ### Example\n *\n * ```\n * @Component({templateUrl:'template.html'})\n * class MyComponent {\n *   constructor(router: Router) {\n *     const tree: UrlTree =\n *       router.parseUrl('/team/33/(user/victor//support:help)?debug=true#fragment');\n *     const f = tree.fragment; // return 'fragment'\n *     const q = tree.queryParams; // returns {debug: 'true'}\n *     const g: UrlSegmentGroup = tree.root.children[PRIMARY_OUTLET];\n *     const s: UrlSegment[] = g.segments; // returns 2 segments 'team' and '33'\n *     g.children[PRIMARY_OUTLET].segments; // returns 2 segments 'user' and 'victor'\n *     g.children['support'].segments; // return 1 segment 'help'\n *   }\n * }\n * ```\n *\n * @publicApi\n */\n\n\nclass UrlTree {\n  /** @internal */\n  constructor(\n  /** The root segment group of the URL tree */\n  root,\n  /** The query params of the URL */\n  queryParams,\n  /** The fragment of the URL */\n  fragment) {\n    this.root = root;\n    this.queryParams = queryParams;\n    this.fragment = fragment;\n  }\n\n  get queryParamMap() {\n    if (!this._queryParamMap) {\n      this._queryParamMap = convertToParamMap(this.queryParams);\n    }\n\n    return this._queryParamMap;\n  }\n  /** @docsNotRequired */\n\n\n  toString() {\n    return DEFAULT_SERIALIZER.serialize(this);\n  }\n\n}\n/**\n * @description\n *\n * Represents the parsed URL segment group.\n *\n * See `UrlTree` for more information.\n *\n * @publicApi\n */\n\n\nclass UrlSegmentGroup {\n  constructor(\n  /** The URL segments of this group. See `UrlSegment` for more information */\n  segments,\n  /** The list of children of this group */\n  children) {\n    this.segments = segments;\n    this.children = children;\n    /** The parent node in the url tree */\n\n    this.parent = null;\n    forEach(children, (v, k) => v.parent = this);\n  }\n  /** Whether the segment has child segments */\n\n\n  hasChildren() {\n    return this.numberOfChildren > 0;\n  }\n  /** Number of child segments */\n\n\n  get numberOfChildren() {\n    return Object.keys(this.children).length;\n  }\n  /** @docsNotRequired */\n\n\n  toString() {\n    return serializePaths(this);\n  }\n\n}\n/**\n * @description\n *\n * Represents a single URL segment.\n *\n * A UrlSegment is a part of a URL between the two slashes. It contains a path and the matrix\n * parameters associated with the segment.\n *\n * @usageNotes\n * ### Example\n *\n * ```\n * @Component({templateUrl:'template.html'})\n * class MyComponent {\n *   constructor(router: Router) {\n *     const tree: UrlTree = router.parseUrl('/team;id=33');\n *     const g: UrlSegmentGroup = tree.root.children[PRIMARY_OUTLET];\n *     const s: UrlSegment[] = g.segments;\n *     s[0].path; // returns 'team'\n *     s[0].parameters; // returns {id: 33}\n *   }\n * }\n * ```\n *\n * @publicApi\n */\n\n\nclass UrlSegment {\n  constructor(\n  /** The path part of a URL segment */\n  path,\n  /** The matrix parameters associated with a segment */\n  parameters) {\n    this.path = path;\n    this.parameters = parameters;\n  }\n\n  get parameterMap() {\n    if (!this._parameterMap) {\n      this._parameterMap = convertToParamMap(this.parameters);\n    }\n\n    return this._parameterMap;\n  }\n  /** @docsNotRequired */\n\n\n  toString() {\n    return serializePath(this);\n  }\n\n}\n\nfunction equalSegments(as, bs) {\n  return equalPath(as, bs) && as.every((a, i) => shallowEqual(a.parameters, bs[i].parameters));\n}\n\nfunction equalPath(as, bs) {\n  if (as.length !== bs.length) return false;\n  return as.every((a, i) => a.path === bs[i].path);\n}\n\nfunction mapChildrenIntoArray(segment, fn) {\n  let res = [];\n  forEach(segment.children, (child, childOutlet) => {\n    if (childOutlet === PRIMARY_OUTLET) {\n      res = res.concat(fn(child, childOutlet));\n    }\n  });\n  forEach(segment.children, (child, childOutlet) => {\n    if (childOutlet !== PRIMARY_OUTLET) {\n      res = res.concat(fn(child, childOutlet));\n    }\n  });\n  return res;\n}\n/**\n * @description\n *\n * Serializes and deserializes a URL string into a URL tree.\n *\n * The url serialization strategy is customizable. You can\n * make all URLs case insensitive by providing a custom UrlSerializer.\n *\n * See `DefaultUrlSerializer` for an example of a URL serializer.\n *\n * @publicApi\n */\n\n\nclass UrlSerializer {}\n/**\n * @description\n *\n * A default implementation of the `UrlSerializer`.\n *\n * Example URLs:\n *\n * ```\n * /inbox/33(popup:compose)\n * /inbox/33;open=true/messages/44\n * ```\n *\n * DefaultUrlSerializer uses parentheses to serialize secondary segments (e.g., popup:compose), the\n * colon syntax to specify the outlet, and the ';parameter=value' syntax (e.g., open=true) to\n * specify route specific parameters.\n *\n * @publicApi\n */\n\n\nclass DefaultUrlSerializer {\n  /** Parses a url into a `UrlTree` */\n  parse(url) {\n    const p = new UrlParser(url);\n    return new UrlTree(p.parseRootSegment(), p.parseQueryParams(), p.parseFragment());\n  }\n  /** Converts a `UrlTree` into a url */\n\n\n  serialize(tree) {\n    const segment = `/${serializeSegment(tree.root, true)}`;\n    const query = serializeQueryParams(tree.queryParams);\n    const fragment = typeof tree.fragment === `string` ? `#${encodeUriFragment(tree.fragment)}` : '';\n    return `${segment}${query}${fragment}`;\n  }\n\n}\n\nconst DEFAULT_SERIALIZER = /*#__PURE__*/new DefaultUrlSerializer();\n\nfunction serializePaths(segment) {\n  return segment.segments.map(p => serializePath(p)).join('/');\n}\n\nfunction serializeSegment(segment, root) {\n  if (!segment.hasChildren()) {\n    return serializePaths(segment);\n  }\n\n  if (root) {\n    const primary = segment.children[PRIMARY_OUTLET] ? serializeSegment(segment.children[PRIMARY_OUTLET], false) : '';\n    const children = [];\n    forEach(segment.children, (v, k) => {\n      if (k !== PRIMARY_OUTLET) {\n        children.push(`${k}:${serializeSegment(v, false)}`);\n      }\n    });\n    return children.length > 0 ? `${primary}(${children.join('//')})` : primary;\n  } else {\n    const children = mapChildrenIntoArray(segment, (v, k) => {\n      if (k === PRIMARY_OUTLET) {\n        return [serializeSegment(segment.children[PRIMARY_OUTLET], false)];\n      }\n\n      return [`${k}:${serializeSegment(v, false)}`];\n    }); // use no parenthesis if the only child is a primary outlet route\n\n    if (Object.keys(segment.children).length === 1 && segment.children[PRIMARY_OUTLET] != null) {\n      return `${serializePaths(segment)}/${children[0]}`;\n    }\n\n    return `${serializePaths(segment)}/(${children.join('//')})`;\n  }\n}\n/**\n * Encodes a URI string with the default encoding. This function will only ever be called from\n * `encodeUriQuery` or `encodeUriSegment` as it's the base set of encodings to be used. We need\n * a custom encoding because encodeURIComponent is too aggressive and encodes stuff that doesn't\n * have to be encoded per https://url.spec.whatwg.org.\n */\n\n\nfunction encodeUriString(s) {\n  return encodeURIComponent(s).replace(/%40/g, '@').replace(/%3A/gi, ':').replace(/%24/g, '$').replace(/%2C/gi, ',');\n}\n/**\n * This function should be used to encode both keys and values in a query string key/value. In\n * the following URL, you need to call encodeUriQuery on \"k\" and \"v\":\n *\n * http://www.site.org/html;mk=mv?k=v#f\n */\n\n\nfunction encodeUriQuery(s) {\n  return encodeUriString(s).replace(/%3B/gi, ';');\n}\n/**\n * This function should be used to encode a URL fragment. In the following URL, you need to call\n * encodeUriFragment on \"f\":\n *\n * http://www.site.org/html;mk=mv?k=v#f\n */\n\n\nfunction encodeUriFragment(s) {\n  return encodeURI(s);\n}\n/**\n * This function should be run on any URI segment as well as the key and value in a key/value\n * pair for matrix params. In the following URL, you need to call encodeUriSegment on \"html\",\n * \"mk\", and \"mv\":\n *\n * http://www.site.org/html;mk=mv?k=v#f\n */\n\n\nfunction encodeUriSegment(s) {\n  return encodeUriString(s).replace(/\\(/g, '%28').replace(/\\)/g, '%29').replace(/%26/gi, '&');\n}\n\nfunction decode(s) {\n  return decodeURIComponent(s);\n} // Query keys/values should have the \"+\" replaced first, as \"+\" in a query string is \" \".\n// decodeURIComponent function will not decode \"+\" as a space.\n\n\nfunction decodeQuery(s) {\n  return decode(s.replace(/\\+/g, '%20'));\n}\n\nfunction serializePath(path) {\n  return `${encodeUriSegment(path.path)}${serializeMatrixParams(path.parameters)}`;\n}\n\nfunction serializeMatrixParams(params) {\n  return Object.keys(params).map(key => `;${encodeUriSegment(key)}=${encodeUriSegment(params[key])}`).join('');\n}\n\nfunction serializeQueryParams(params) {\n  const strParams = Object.keys(params).map(name => {\n    const value = params[name];\n    return Array.isArray(value) ? value.map(v => `${encodeUriQuery(name)}=${encodeUriQuery(v)}`).join('&') : `${encodeUriQuery(name)}=${encodeUriQuery(value)}`;\n  }).filter(s => !!s);\n  return strParams.length ? `?${strParams.join('&')}` : '';\n}\n\nconst SEGMENT_RE = /^[^\\/()?;=#]+/;\n\nfunction matchSegments(str) {\n  const match = str.match(SEGMENT_RE);\n  return match ? match[0] : '';\n}\n\nconst QUERY_PARAM_RE = /^[^=?&#]+/; // Return the name of the query param at the start of the string or an empty string\n\nfunction matchQueryParams(str) {\n  const match = str.match(QUERY_PARAM_RE);\n  return match ? match[0] : '';\n}\n\nconst QUERY_PARAM_VALUE_RE = /^[^&#]+/; // Return the value of the query param at the start of the string or an empty string\n\nfunction matchUrlQueryParamValue(str) {\n  const match = str.match(QUERY_PARAM_VALUE_RE);\n  return match ? match[0] : '';\n}\n\nclass UrlParser {\n  constructor(url) {\n    this.url = url;\n    this.remaining = url;\n  }\n\n  parseRootSegment() {\n    this.consumeOptional('/');\n\n    if (this.remaining === '' || this.peekStartsWith('?') || this.peekStartsWith('#')) {\n      return new UrlSegmentGroup([], {});\n    } // The root segment group never has segments\n\n\n    return new UrlSegmentGroup([], this.parseChildren());\n  }\n\n  parseQueryParams() {\n    const params = {};\n\n    if (this.consumeOptional('?')) {\n      do {\n        this.parseQueryParam(params);\n      } while (this.consumeOptional('&'));\n    }\n\n    return params;\n  }\n\n  parseFragment() {\n    return this.consumeOptional('#') ? decodeURIComponent(this.remaining) : null;\n  }\n\n  parseChildren() {\n    if (this.remaining === '') {\n      return {};\n    }\n\n    this.consumeOptional('/');\n    const segments = [];\n\n    if (!this.peekStartsWith('(')) {\n      segments.push(this.parseSegment());\n    }\n\n    while (this.peekStartsWith('/') && !this.peekStartsWith('//') && !this.peekStartsWith('/(')) {\n      this.capture('/');\n      segments.push(this.parseSegment());\n    }\n\n    let children = {};\n\n    if (this.peekStartsWith('/(')) {\n      this.capture('/');\n      children = this.parseParens(true);\n    }\n\n    let res = {};\n\n    if (this.peekStartsWith('(')) {\n      res = this.parseParens(false);\n    }\n\n    if (segments.length > 0 || Object.keys(children).length > 0) {\n      res[PRIMARY_OUTLET] = new UrlSegmentGroup(segments, children);\n    }\n\n    return res;\n  } // parse a segment with its matrix parameters\n  // ie `name;k1=v1;k2`\n\n\n  parseSegment() {\n    const path = matchSegments(this.remaining);\n\n    if (path === '' && this.peekStartsWith(';')) {\n      throw new Error(`Empty path url segment cannot have parameters: '${this.remaining}'.`);\n    }\n\n    this.capture(path);\n    return new UrlSegment(decode(path), this.parseMatrixParams());\n  }\n\n  parseMatrixParams() {\n    const params = {};\n\n    while (this.consumeOptional(';')) {\n      this.parseParam(params);\n    }\n\n    return params;\n  }\n\n  parseParam(params) {\n    const key = matchSegments(this.remaining);\n\n    if (!key) {\n      return;\n    }\n\n    this.capture(key);\n    let value = '';\n\n    if (this.consumeOptional('=')) {\n      const valueMatch = matchSegments(this.remaining);\n\n      if (valueMatch) {\n        value = valueMatch;\n        this.capture(value);\n      }\n    }\n\n    params[decode(key)] = decode(value);\n  } // Parse a single query parameter `name[=value]`\n\n\n  parseQueryParam(params) {\n    const key = matchQueryParams(this.remaining);\n\n    if (!key) {\n      return;\n    }\n\n    this.capture(key);\n    let value = '';\n\n    if (this.consumeOptional('=')) {\n      const valueMatch = matchUrlQueryParamValue(this.remaining);\n\n      if (valueMatch) {\n        value = valueMatch;\n        this.capture(value);\n      }\n    }\n\n    const decodedKey = decodeQuery(key);\n    const decodedVal = decodeQuery(value);\n\n    if (params.hasOwnProperty(decodedKey)) {\n      // Append to existing values\n      let currentVal = params[decodedKey];\n\n      if (!Array.isArray(currentVal)) {\n        currentVal = [currentVal];\n        params[decodedKey] = currentVal;\n      }\n\n      currentVal.push(decodedVal);\n    } else {\n      // Create a new value\n      params[decodedKey] = decodedVal;\n    }\n  } // parse `(a/b//outlet_name:c/d)`\n\n\n  parseParens(allowPrimary) {\n    const segments = {};\n    this.capture('(');\n\n    while (!this.consumeOptional(')') && this.remaining.length > 0) {\n      const path = matchSegments(this.remaining);\n      const next = this.remaining[path.length]; // if is is not one of these characters, then the segment was unescaped\n      // or the group was not closed\n\n      if (next !== '/' && next !== ')' && next !== ';') {\n        throw new Error(`Cannot parse url '${this.url}'`);\n      }\n\n      let outletName = undefined;\n\n      if (path.indexOf(':') > -1) {\n        outletName = path.substr(0, path.indexOf(':'));\n        this.capture(outletName);\n        this.capture(':');\n      } else if (allowPrimary) {\n        outletName = PRIMARY_OUTLET;\n      }\n\n      const children = this.parseChildren();\n      segments[outletName] = Object.keys(children).length === 1 ? children[PRIMARY_OUTLET] : new UrlSegmentGroup([], children);\n      this.consumeOptional('//');\n    }\n\n    return segments;\n  }\n\n  peekStartsWith(str) {\n    return this.remaining.startsWith(str);\n  } // Consumes the prefix when it is present and returns whether it has been consumed\n\n\n  consumeOptional(str) {\n    if (this.peekStartsWith(str)) {\n      this.remaining = this.remaining.substring(str.length);\n      return true;\n    }\n\n    return false;\n  }\n\n  capture(str) {\n    if (!this.consumeOptional(str)) {\n      throw new Error(`Expected \"${str}\".`);\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass Tree {\n  constructor(root) {\n    this._root = root;\n  }\n\n  get root() {\n    return this._root.value;\n  }\n  /**\n   * @internal\n   */\n\n\n  parent(t) {\n    const p = this.pathFromRoot(t);\n    return p.length > 1 ? p[p.length - 2] : null;\n  }\n  /**\n   * @internal\n   */\n\n\n  children(t) {\n    const n = findNode(t, this._root);\n    return n ? n.children.map(t => t.value) : [];\n  }\n  /**\n   * @internal\n   */\n\n\n  firstChild(t) {\n    const n = findNode(t, this._root);\n    return n && n.children.length > 0 ? n.children[0].value : null;\n  }\n  /**\n   * @internal\n   */\n\n\n  siblings(t) {\n    const p = findPath(t, this._root);\n    if (p.length < 2) return [];\n    const c = p[p.length - 2].children.map(c => c.value);\n    return c.filter(cc => cc !== t);\n  }\n  /**\n   * @internal\n   */\n\n\n  pathFromRoot(t) {\n    return findPath(t, this._root).map(s => s.value);\n  }\n\n} // DFS for the node matching the value\n\n\nfunction findNode(value, node) {\n  if (value === node.value) return node;\n\n  for (const child of node.children) {\n    const node = findNode(value, child);\n    if (node) return node;\n  }\n\n  return null;\n} // Return the path to the node with the given value using DFS\n\n\nfunction findPath(value, node) {\n  if (value === node.value) return [node];\n\n  for (const child of node.children) {\n    const path = findPath(value, child);\n\n    if (path.length) {\n      path.unshift(node);\n      return path;\n    }\n  }\n\n  return [];\n}\n\nclass TreeNode {\n  constructor(value, children) {\n    this.value = value;\n    this.children = children;\n  }\n\n  toString() {\n    return `TreeNode(${this.value})`;\n  }\n\n} // Return the list of T indexed by outlet name\n\n\nfunction nodeChildrenAsMap(node) {\n  const map = {};\n\n  if (node) {\n    node.children.forEach(child => map[child.value.outlet] = child);\n  }\n\n  return map;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Represents the state of the router as a tree of activated routes.\n *\n * @usageNotes\n *\n * Every node in the route tree is an `ActivatedRoute` instance\n * that knows about the \"consumed\" URL segments, the extracted parameters,\n * and the resolved data.\n * Use the `ActivatedRoute` properties to traverse the tree from any node.\n *\n * The following fragment shows how a component gets the root node\n * of the current state to establish its own route tree:\n *\n * ```\n * @Component({templateUrl:'template.html'})\n * class MyComponent {\n *   constructor(router: Router) {\n *     const state: RouterState = router.routerState;\n *     const root: ActivatedRoute = state.root;\n *     const child = root.firstChild;\n *     const id: Observable<string> = child.params.map(p => p.id);\n *     //...\n *   }\n * }\n * ```\n *\n * @see `ActivatedRoute`\n * @see [Getting route information](guide/router#getting-route-information)\n *\n * @publicApi\n */\n\n\nclass RouterState extends Tree {\n  /** @internal */\n  constructor(root,\n  /** The current snapshot of the router state */\n  snapshot) {\n    super(root);\n    this.snapshot = snapshot;\n    setRouterState(this, root);\n  }\n\n  toString() {\n    return this.snapshot.toString();\n  }\n\n}\n\nfunction createEmptyState(urlTree, rootComponent) {\n  const snapshot = createEmptyStateSnapshot(urlTree, rootComponent);\n  const emptyUrl = new BehaviorSubject([new UrlSegment('', {})]);\n  const emptyParams = new BehaviorSubject({});\n  const emptyData = new BehaviorSubject({});\n  const emptyQueryParams = new BehaviorSubject({});\n  const fragment = new BehaviorSubject('');\n  const activated = new ActivatedRoute(emptyUrl, emptyParams, emptyQueryParams, fragment, emptyData, PRIMARY_OUTLET, rootComponent, snapshot.root);\n  activated.snapshot = snapshot.root;\n  return new RouterState(new TreeNode(activated, []), snapshot);\n}\n\nfunction createEmptyStateSnapshot(urlTree, rootComponent) {\n  const emptyParams = {};\n  const emptyData = {};\n  const emptyQueryParams = {};\n  const fragment = '';\n  const activated = new ActivatedRouteSnapshot([], emptyParams, emptyQueryParams, fragment, emptyData, PRIMARY_OUTLET, rootComponent, null, urlTree.root, -1, {});\n  return new RouterStateSnapshot('', new TreeNode(activated, []));\n}\n/**\n * Provides access to information about a route associated with a component\n * that is loaded in an outlet.\n * Use to traverse the `RouterState` tree and extract information from nodes.\n *\n * The following example shows how to construct a component using information from a\n * currently activated route.\n *\n * Note: the observables in this class only emit when the current and previous values differ based\n * on shallow equality. For example, changing deeply nested properties in resolved `data` will not\n * cause the `ActivatedRoute.data` `Observable` to emit a new value.\n *\n * {@example router/activated-route/module.ts region=\"activated-route\"\n *     header=\"activated-route.component.ts\"}\n *\n * @see [Getting route information](guide/router#getting-route-information)\n *\n * @publicApi\n */\n\n\nclass ActivatedRoute {\n  /** @internal */\n  constructor(\n  /** An observable of the URL segments matched by this route. */\n  url,\n  /** An observable of the matrix parameters scoped to this route. */\n  params,\n  /** An observable of the query parameters shared by all the routes. */\n  queryParams,\n  /** An observable of the URL fragment shared by all the routes. */\n  fragment,\n  /** An observable of the static and resolved data of this route. */\n  data,\n  /** The outlet name of the route, a constant. */\n  outlet,\n  /** The component of the route, a constant. */\n  // TODO(vsavkin): remove |string\n  component, futureSnapshot) {\n    this.url = url;\n    this.params = params;\n    this.queryParams = queryParams;\n    this.fragment = fragment;\n    this.data = data;\n    this.outlet = outlet;\n    this.component = component;\n    this._futureSnapshot = futureSnapshot;\n  }\n  /** The configuration used to match this route. */\n\n\n  get routeConfig() {\n    return this._futureSnapshot.routeConfig;\n  }\n  /** The root of the router state. */\n\n\n  get root() {\n    return this._routerState.root;\n  }\n  /** The parent of this route in the router state tree. */\n\n\n  get parent() {\n    return this._routerState.parent(this);\n  }\n  /** The first child of this route in the router state tree. */\n\n\n  get firstChild() {\n    return this._routerState.firstChild(this);\n  }\n  /** The children of this route in the router state tree. */\n\n\n  get children() {\n    return this._routerState.children(this);\n  }\n  /** The path from the root of the router state tree to this route. */\n\n\n  get pathFromRoot() {\n    return this._routerState.pathFromRoot(this);\n  }\n  /**\n   * An Observable that contains a map of the required and optional parameters\n   * specific to the route.\n   * The map supports retrieving single and multiple values from the same parameter.\n   */\n\n\n  get paramMap() {\n    if (!this._paramMap) {\n      this._paramMap = this.params.pipe(map(p => convertToParamMap(p)));\n    }\n\n    return this._paramMap;\n  }\n  /**\n   * An Observable that contains a map of the query parameters available to all routes.\n   * The map supports retrieving single and multiple values from the query parameter.\n   */\n\n\n  get queryParamMap() {\n    if (!this._queryParamMap) {\n      this._queryParamMap = this.queryParams.pipe(map(p => convertToParamMap(p)));\n    }\n\n    return this._queryParamMap;\n  }\n\n  toString() {\n    return this.snapshot ? this.snapshot.toString() : `Future(${this._futureSnapshot})`;\n  }\n\n}\n/**\n * Returns the inherited params, data, and resolve for a given route.\n * By default, this only inherits values up to the nearest path-less or component-less route.\n * @internal\n */\n\n\nfunction inheritedParamsDataResolve(route, paramsInheritanceStrategy = 'emptyOnly') {\n  const pathFromRoot = route.pathFromRoot;\n  let inheritingStartingFrom = 0;\n\n  if (paramsInheritanceStrategy !== 'always') {\n    inheritingStartingFrom = pathFromRoot.length - 1;\n\n    while (inheritingStartingFrom >= 1) {\n      const current = pathFromRoot[inheritingStartingFrom];\n      const parent = pathFromRoot[inheritingStartingFrom - 1]; // current route is an empty path => inherits its parent's params and data\n\n      if (current.routeConfig && current.routeConfig.path === '') {\n        inheritingStartingFrom--; // parent is componentless => current route should inherit its params and data\n      } else if (!parent.component) {\n        inheritingStartingFrom--;\n      } else {\n        break;\n      }\n    }\n  }\n\n  return flattenInherited(pathFromRoot.slice(inheritingStartingFrom));\n}\n/** @internal */\n\n\nfunction flattenInherited(pathFromRoot) {\n  return pathFromRoot.reduce((res, curr) => {\n    const params = Object.assign(Object.assign({}, res.params), curr.params);\n    const data = Object.assign(Object.assign({}, res.data), curr.data);\n    const resolve = Object.assign(Object.assign({}, res.resolve), curr._resolvedData);\n    return {\n      params,\n      data,\n      resolve\n    };\n  }, {\n    params: {},\n    data: {},\n    resolve: {}\n  });\n}\n/**\n * @description\n *\n * Contains the information about a route associated with a component loaded in an\n * outlet at a particular moment in time. ActivatedRouteSnapshot can also be used to\n * traverse the router state tree.\n *\n * The following example initializes a component with route information extracted\n * from the snapshot of the root node at the time of creation.\n *\n * ```\n * @Component({templateUrl:'./my-component.html'})\n * class MyComponent {\n *   constructor(route: ActivatedRoute) {\n *     const id: string = route.snapshot.params.id;\n *     const url: string = route.snapshot.url.join('');\n *     const user = route.snapshot.data.user;\n *   }\n * }\n * ```\n *\n * @publicApi\n */\n\n\nclass ActivatedRouteSnapshot {\n  /** @internal */\n  constructor(\n  /** The URL segments matched by this route */\n  url,\n  /**\n   *  The matrix parameters scoped to this route.\n   *\n   *  You can compute all params (or data) in the router state or to get params outside\n   *  of an activated component by traversing the `RouterState` tree as in the following\n   *  example:\n   *  ```\n   *  collectRouteParams(router: Router) {\n   *    let params = {};\n   *    let stack: ActivatedRouteSnapshot[] = [router.routerState.snapshot.root];\n   *    while (stack.length > 0) {\n   *      const route = stack.pop()!;\n   *      params = {...params, ...route.params};\n   *      stack.push(...route.children);\n   *    }\n   *    return params;\n   *  }\n   *  ```\n   */\n  params,\n  /** The query parameters shared by all the routes */\n  queryParams,\n  /** The URL fragment shared by all the routes */\n  fragment,\n  /** The static and resolved data of this route */\n  data,\n  /** The outlet name of the route */\n  outlet,\n  /** The component of the route */\n  component, routeConfig, urlSegment, lastPathIndex, resolve) {\n    this.url = url;\n    this.params = params;\n    this.queryParams = queryParams;\n    this.fragment = fragment;\n    this.data = data;\n    this.outlet = outlet;\n    this.component = component;\n    this.routeConfig = routeConfig;\n    this._urlSegment = urlSegment;\n    this._lastPathIndex = lastPathIndex;\n    this._resolve = resolve;\n  }\n  /** The root of the router state */\n\n\n  get root() {\n    return this._routerState.root;\n  }\n  /** The parent of this route in the router state tree */\n\n\n  get parent() {\n    return this._routerState.parent(this);\n  }\n  /** The first child of this route in the router state tree */\n\n\n  get firstChild() {\n    return this._routerState.firstChild(this);\n  }\n  /** The children of this route in the router state tree */\n\n\n  get children() {\n    return this._routerState.children(this);\n  }\n  /** The path from the root of the router state tree to this route */\n\n\n  get pathFromRoot() {\n    return this._routerState.pathFromRoot(this);\n  }\n\n  get paramMap() {\n    if (!this._paramMap) {\n      this._paramMap = convertToParamMap(this.params);\n    }\n\n    return this._paramMap;\n  }\n\n  get queryParamMap() {\n    if (!this._queryParamMap) {\n      this._queryParamMap = convertToParamMap(this.queryParams);\n    }\n\n    return this._queryParamMap;\n  }\n\n  toString() {\n    const url = this.url.map(segment => segment.toString()).join('/');\n    const matched = this.routeConfig ? this.routeConfig.path : '';\n    return `Route(url:'${url}', path:'${matched}')`;\n  }\n\n}\n/**\n * @description\n *\n * Represents the state of the router at a moment in time.\n *\n * This is a tree of activated route snapshots. Every node in this tree knows about\n * the \"consumed\" URL segments, the extracted parameters, and the resolved data.\n *\n * The following example shows how a component is initialized with information\n * from the snapshot of the root node's state at the time of creation.\n *\n * ```\n * @Component({templateUrl:'template.html'})\n * class MyComponent {\n *   constructor(router: Router) {\n *     const state: RouterState = router.routerState;\n *     const snapshot: RouterStateSnapshot = state.snapshot;\n *     const root: ActivatedRouteSnapshot = snapshot.root;\n *     const child = root.firstChild;\n *     const id: Observable<string> = child.params.map(p => p.id);\n *     //...\n *   }\n * }\n * ```\n *\n * @publicApi\n */\n\n\nclass RouterStateSnapshot extends Tree {\n  /** @internal */\n  constructor(\n  /** The url from which this snapshot was created */\n  url, root) {\n    super(root);\n    this.url = url;\n    setRouterState(this, root);\n  }\n\n  toString() {\n    return serializeNode(this._root);\n  }\n\n}\n\nfunction setRouterState(state, node) {\n  node.value._routerState = state;\n  node.children.forEach(c => setRouterState(state, c));\n}\n\nfunction serializeNode(node) {\n  const c = node.children.length > 0 ? ` { ${node.children.map(serializeNode).join(', ')} } ` : '';\n  return `${node.value}${c}`;\n}\n/**\n * The expectation is that the activate route is created with the right set of parameters.\n * So we push new values into the observables only when they are not the initial values.\n * And we detect that by checking if the snapshot field is set.\n */\n\n\nfunction advanceActivatedRoute(route) {\n  if (route.snapshot) {\n    const currentSnapshot = route.snapshot;\n    const nextSnapshot = route._futureSnapshot;\n    route.snapshot = nextSnapshot;\n\n    if (!shallowEqual(currentSnapshot.queryParams, nextSnapshot.queryParams)) {\n      route.queryParams.next(nextSnapshot.queryParams);\n    }\n\n    if (currentSnapshot.fragment !== nextSnapshot.fragment) {\n      route.fragment.next(nextSnapshot.fragment);\n    }\n\n    if (!shallowEqual(currentSnapshot.params, nextSnapshot.params)) {\n      route.params.next(nextSnapshot.params);\n    }\n\n    if (!shallowEqualArrays(currentSnapshot.url, nextSnapshot.url)) {\n      route.url.next(nextSnapshot.url);\n    }\n\n    if (!shallowEqual(currentSnapshot.data, nextSnapshot.data)) {\n      route.data.next(nextSnapshot.data);\n    }\n  } else {\n    route.snapshot = route._futureSnapshot; // this is for resolved data\n\n    route.data.next(route._futureSnapshot.data);\n  }\n}\n\nfunction equalParamsAndUrlSegments(a, b) {\n  const equalUrlParams = shallowEqual(a.params, b.params) && equalSegments(a.url, b.url);\n  const parentsMismatch = !a.parent !== !b.parent;\n  return equalUrlParams && !parentsMismatch && (!a.parent || equalParamsAndUrlSegments(a.parent, b.parent));\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction createRouterState(routeReuseStrategy, curr, prevState) {\n  const root = createNode(routeReuseStrategy, curr._root, prevState ? prevState._root : undefined);\n  return new RouterState(root, curr);\n}\n\nfunction createNode(routeReuseStrategy, curr, prevState) {\n  // reuse an activated route that is currently displayed on the screen\n  if (prevState && routeReuseStrategy.shouldReuseRoute(curr.value, prevState.value.snapshot)) {\n    const value = prevState.value;\n    value._futureSnapshot = curr.value;\n    const children = createOrReuseChildren(routeReuseStrategy, curr, prevState);\n    return new TreeNode(value, children);\n  } else {\n    if (routeReuseStrategy.shouldAttach(curr.value)) {\n      // retrieve an activated route that is used to be displayed, but is not currently displayed\n      const detachedRouteHandle = routeReuseStrategy.retrieve(curr.value);\n\n      if (detachedRouteHandle !== null) {\n        const tree = detachedRouteHandle.route;\n        tree.value._futureSnapshot = curr.value;\n        tree.children = curr.children.map(c => createNode(routeReuseStrategy, c));\n        return tree;\n      }\n    }\n\n    const value = createActivatedRoute(curr.value);\n    const children = curr.children.map(c => createNode(routeReuseStrategy, c));\n    return new TreeNode(value, children);\n  }\n}\n\nfunction createOrReuseChildren(routeReuseStrategy, curr, prevState) {\n  return curr.children.map(child => {\n    for (const p of prevState.children) {\n      if (routeReuseStrategy.shouldReuseRoute(child.value, p.value.snapshot)) {\n        return createNode(routeReuseStrategy, child, p);\n      }\n    }\n\n    return createNode(routeReuseStrategy, child);\n  });\n}\n\nfunction createActivatedRoute(c) {\n  return new ActivatedRoute(new BehaviorSubject(c.url), new BehaviorSubject(c.params), new BehaviorSubject(c.queryParams), new BehaviorSubject(c.fragment), new BehaviorSubject(c.data), c.outlet, c.component, c);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction createUrlTree(route, urlTree, commands, queryParams, fragment) {\n  if (commands.length === 0) {\n    return tree(urlTree.root, urlTree.root, urlTree, queryParams, fragment);\n  }\n\n  const nav = computeNavigation(commands);\n\n  if (nav.toRoot()) {\n    return tree(urlTree.root, new UrlSegmentGroup([], {}), urlTree, queryParams, fragment);\n  }\n\n  const startingPosition = findStartingPosition(nav, urlTree, route);\n  const segmentGroup = startingPosition.processChildren ? updateSegmentGroupChildren(startingPosition.segmentGroup, startingPosition.index, nav.commands) : updateSegmentGroup(startingPosition.segmentGroup, startingPosition.index, nav.commands);\n  return tree(startingPosition.segmentGroup, segmentGroup, urlTree, queryParams, fragment);\n}\n\nfunction isMatrixParams(command) {\n  return typeof command === 'object' && command != null && !command.outlets && !command.segmentPath;\n}\n/**\n * Determines if a given command has an `outlets` map. When we encounter a command\n * with an outlets k/v map, we need to apply each outlet individually to the existing segment.\n */\n\n\nfunction isCommandWithOutlets(command) {\n  return typeof command === 'object' && command != null && command.outlets;\n}\n\nfunction tree(oldSegmentGroup, newSegmentGroup, urlTree, queryParams, fragment) {\n  let qp = {};\n\n  if (queryParams) {\n    forEach(queryParams, (value, name) => {\n      qp[name] = Array.isArray(value) ? value.map(v => `${v}`) : `${value}`;\n    });\n  }\n\n  if (urlTree.root === oldSegmentGroup) {\n    return new UrlTree(newSegmentGroup, qp, fragment);\n  }\n\n  return new UrlTree(replaceSegment(urlTree.root, oldSegmentGroup, newSegmentGroup), qp, fragment);\n}\n\nfunction replaceSegment(current, oldSegment, newSegment) {\n  const children = {};\n  forEach(current.children, (c, outletName) => {\n    if (c === oldSegment) {\n      children[outletName] = newSegment;\n    } else {\n      children[outletName] = replaceSegment(c, oldSegment, newSegment);\n    }\n  });\n  return new UrlSegmentGroup(current.segments, children);\n}\n\nclass Navigation {\n  constructor(isAbsolute, numberOfDoubleDots, commands) {\n    this.isAbsolute = isAbsolute;\n    this.numberOfDoubleDots = numberOfDoubleDots;\n    this.commands = commands;\n\n    if (isAbsolute && commands.length > 0 && isMatrixParams(commands[0])) {\n      throw new Error('Root segment cannot have matrix parameters');\n    }\n\n    const cmdWithOutlet = commands.find(isCommandWithOutlets);\n\n    if (cmdWithOutlet && cmdWithOutlet !== last(commands)) {\n      throw new Error('{outlets:{}} has to be the last command');\n    }\n  }\n\n  toRoot() {\n    return this.isAbsolute && this.commands.length === 1 && this.commands[0] == '/';\n  }\n\n}\n/** Transforms commands to a normalized `Navigation` */\n\n\nfunction computeNavigation(commands) {\n  if (typeof commands[0] === 'string' && commands.length === 1 && commands[0] === '/') {\n    return new Navigation(true, 0, commands);\n  }\n\n  let numberOfDoubleDots = 0;\n  let isAbsolute = false;\n  const res = commands.reduce((res, cmd, cmdIdx) => {\n    if (typeof cmd === 'object' && cmd != null) {\n      if (cmd.outlets) {\n        const outlets = {};\n        forEach(cmd.outlets, (commands, name) => {\n          outlets[name] = typeof commands === 'string' ? commands.split('/') : commands;\n        });\n        return [...res, {\n          outlets\n        }];\n      }\n\n      if (cmd.segmentPath) {\n        return [...res, cmd.segmentPath];\n      }\n    }\n\n    if (!(typeof cmd === 'string')) {\n      return [...res, cmd];\n    }\n\n    if (cmdIdx === 0) {\n      cmd.split('/').forEach((urlPart, partIndex) => {\n        if (partIndex == 0 && urlPart === '.') {// skip './a'\n        } else if (partIndex == 0 && urlPart === '') {\n          //  '/a'\n          isAbsolute = true;\n        } else if (urlPart === '..') {\n          //  '../a'\n          numberOfDoubleDots++;\n        } else if (urlPart != '') {\n          res.push(urlPart);\n        }\n      });\n      return res;\n    }\n\n    return [...res, cmd];\n  }, []);\n  return new Navigation(isAbsolute, numberOfDoubleDots, res);\n}\n\nclass Position {\n  constructor(segmentGroup, processChildren, index) {\n    this.segmentGroup = segmentGroup;\n    this.processChildren = processChildren;\n    this.index = index;\n  }\n\n}\n\nfunction findStartingPosition(nav, tree, route) {\n  if (nav.isAbsolute) {\n    return new Position(tree.root, true, 0);\n  }\n\n  if (route.snapshot._lastPathIndex === -1) {\n    const segmentGroup = route.snapshot._urlSegment; // Pathless ActivatedRoute has _lastPathIndex === -1 but should not process children\n    // see issue #26224, #13011, #35687\n    // However, if the ActivatedRoute is the root we should process children like above.\n\n    const processChildren = segmentGroup === tree.root;\n    return new Position(segmentGroup, processChildren, 0);\n  }\n\n  const modifier = isMatrixParams(nav.commands[0]) ? 0 : 1;\n  const index = route.snapshot._lastPathIndex + modifier;\n  return createPositionApplyingDoubleDots(route.snapshot._urlSegment, index, nav.numberOfDoubleDots);\n}\n\nfunction createPositionApplyingDoubleDots(group, index, numberOfDoubleDots) {\n  let g = group;\n  let ci = index;\n  let dd = numberOfDoubleDots;\n\n  while (dd > ci) {\n    dd -= ci;\n    g = g.parent;\n\n    if (!g) {\n      throw new Error('Invalid number of \\'../\\'');\n    }\n\n    ci = g.segments.length;\n  }\n\n  return new Position(g, false, ci - dd);\n}\n\nfunction getOutlets(commands) {\n  if (isCommandWithOutlets(commands[0])) {\n    return commands[0].outlets;\n  }\n\n  return {\n    [PRIMARY_OUTLET]: commands\n  };\n}\n\nfunction updateSegmentGroup(segmentGroup, startIndex, commands) {\n  if (!segmentGroup) {\n    segmentGroup = new UrlSegmentGroup([], {});\n  }\n\n  if (segmentGroup.segments.length === 0 && segmentGroup.hasChildren()) {\n    return updateSegmentGroupChildren(segmentGroup, startIndex, commands);\n  }\n\n  const m = prefixedWith(segmentGroup, startIndex, commands);\n  const slicedCommands = commands.slice(m.commandIndex);\n\n  if (m.match && m.pathIndex < segmentGroup.segments.length) {\n    const g = new UrlSegmentGroup(segmentGroup.segments.slice(0, m.pathIndex), {});\n    g.children[PRIMARY_OUTLET] = new UrlSegmentGroup(segmentGroup.segments.slice(m.pathIndex), segmentGroup.children);\n    return updateSegmentGroupChildren(g, 0, slicedCommands);\n  } else if (m.match && slicedCommands.length === 0) {\n    return new UrlSegmentGroup(segmentGroup.segments, {});\n  } else if (m.match && !segmentGroup.hasChildren()) {\n    return createNewSegmentGroup(segmentGroup, startIndex, commands);\n  } else if (m.match) {\n    return updateSegmentGroupChildren(segmentGroup, 0, slicedCommands);\n  } else {\n    return createNewSegmentGroup(segmentGroup, startIndex, commands);\n  }\n}\n\nfunction updateSegmentGroupChildren(segmentGroup, startIndex, commands) {\n  if (commands.length === 0) {\n    return new UrlSegmentGroup(segmentGroup.segments, {});\n  } else {\n    const outlets = getOutlets(commands);\n    const children = {};\n    forEach(outlets, (commands, outlet) => {\n      if (typeof commands === 'string') {\n        commands = [commands];\n      }\n\n      if (commands !== null) {\n        children[outlet] = updateSegmentGroup(segmentGroup.children[outlet], startIndex, commands);\n      }\n    });\n    forEach(segmentGroup.children, (child, childOutlet) => {\n      if (outlets[childOutlet] === undefined) {\n        children[childOutlet] = child;\n      }\n    });\n    return new UrlSegmentGroup(segmentGroup.segments, children);\n  }\n}\n\nfunction prefixedWith(segmentGroup, startIndex, commands) {\n  let currentCommandIndex = 0;\n  let currentPathIndex = startIndex;\n  const noMatch = {\n    match: false,\n    pathIndex: 0,\n    commandIndex: 0\n  };\n\n  while (currentPathIndex < segmentGroup.segments.length) {\n    if (currentCommandIndex >= commands.length) return noMatch;\n    const path = segmentGroup.segments[currentPathIndex];\n    const command = commands[currentCommandIndex]; // Do not try to consume command as part of the prefixing if it has outlets because it can\n    // contain outlets other than the one being processed. Consuming the outlets command would\n    // result in other outlets being ignored.\n\n    if (isCommandWithOutlets(command)) {\n      break;\n    }\n\n    const curr = `${command}`;\n    const next = currentCommandIndex < commands.length - 1 ? commands[currentCommandIndex + 1] : null;\n    if (currentPathIndex > 0 && curr === undefined) break;\n\n    if (curr && next && typeof next === 'object' && next.outlets === undefined) {\n      if (!compare(curr, next, path)) return noMatch;\n      currentCommandIndex += 2;\n    } else {\n      if (!compare(curr, {}, path)) return noMatch;\n      currentCommandIndex++;\n    }\n\n    currentPathIndex++;\n  }\n\n  return {\n    match: true,\n    pathIndex: currentPathIndex,\n    commandIndex: currentCommandIndex\n  };\n}\n\nfunction createNewSegmentGroup(segmentGroup, startIndex, commands) {\n  const paths = segmentGroup.segments.slice(0, startIndex);\n  let i = 0;\n\n  while (i < commands.length) {\n    const command = commands[i];\n\n    if (isCommandWithOutlets(command)) {\n      const children = createNewSegmentChildren(command.outlets);\n      return new UrlSegmentGroup(paths, children);\n    } // if we start with an object literal, we need to reuse the path part from the segment\n\n\n    if (i === 0 && isMatrixParams(commands[0])) {\n      const p = segmentGroup.segments[startIndex];\n      paths.push(new UrlSegment(p.path, stringify(commands[0])));\n      i++;\n      continue;\n    }\n\n    const curr = isCommandWithOutlets(command) ? command.outlets[PRIMARY_OUTLET] : `${command}`;\n    const next = i < commands.length - 1 ? commands[i + 1] : null;\n\n    if (curr && next && isMatrixParams(next)) {\n      paths.push(new UrlSegment(curr, stringify(next)));\n      i += 2;\n    } else {\n      paths.push(new UrlSegment(curr, {}));\n      i++;\n    }\n  }\n\n  return new UrlSegmentGroup(paths, {});\n}\n\nfunction createNewSegmentChildren(outlets) {\n  const children = {};\n  forEach(outlets, (commands, outlet) => {\n    if (typeof commands === 'string') {\n      commands = [commands];\n    }\n\n    if (commands !== null) {\n      children[outlet] = createNewSegmentGroup(new UrlSegmentGroup([], {}), 0, commands);\n    }\n  });\n  return children;\n}\n\nfunction stringify(params) {\n  const res = {};\n  forEach(params, (v, k) => res[k] = `${v}`);\n  return res;\n}\n\nfunction compare(path, params, segment) {\n  return path == segment.path && shallowEqual(params, segment.parameters);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst activateRoutes = (rootContexts, routeReuseStrategy, forwardEvent) => map(t => {\n  new ActivateRoutes(routeReuseStrategy, t.targetRouterState, t.currentRouterState, forwardEvent).activate(rootContexts);\n  return t;\n});\n\nclass ActivateRoutes {\n  constructor(routeReuseStrategy, futureState, currState, forwardEvent) {\n    this.routeReuseStrategy = routeReuseStrategy;\n    this.futureState = futureState;\n    this.currState = currState;\n    this.forwardEvent = forwardEvent;\n  }\n\n  activate(parentContexts) {\n    const futureRoot = this.futureState._root;\n    const currRoot = this.currState ? this.currState._root : null;\n    this.deactivateChildRoutes(futureRoot, currRoot, parentContexts);\n    advanceActivatedRoute(this.futureState.root);\n    this.activateChildRoutes(futureRoot, currRoot, parentContexts);\n  } // De-activate the child route that are not re-used for the future state\n\n\n  deactivateChildRoutes(futureNode, currNode, contexts) {\n    const children = nodeChildrenAsMap(currNode); // Recurse on the routes active in the future state to de-activate deeper children\n\n    futureNode.children.forEach(futureChild => {\n      const childOutletName = futureChild.value.outlet;\n      this.deactivateRoutes(futureChild, children[childOutletName], contexts);\n      delete children[childOutletName];\n    }); // De-activate the routes that will not be re-used\n\n    forEach(children, (v, childName) => {\n      this.deactivateRouteAndItsChildren(v, contexts);\n    });\n  }\n\n  deactivateRoutes(futureNode, currNode, parentContext) {\n    const future = futureNode.value;\n    const curr = currNode ? currNode.value : null;\n\n    if (future === curr) {\n      // Reusing the node, check to see if the children need to be de-activated\n      if (future.component) {\n        // If we have a normal route, we need to go through an outlet.\n        const context = parentContext.getContext(future.outlet);\n\n        if (context) {\n          this.deactivateChildRoutes(futureNode, currNode, context.children);\n        }\n      } else {\n        // if we have a componentless route, we recurse but keep the same outlet map.\n        this.deactivateChildRoutes(futureNode, currNode, parentContext);\n      }\n    } else {\n      if (curr) {\n        // Deactivate the current route which will not be re-used\n        this.deactivateRouteAndItsChildren(currNode, parentContext);\n      }\n    }\n  }\n\n  deactivateRouteAndItsChildren(route, parentContexts) {\n    // If there is no component, the Route is never attached to an outlet (because there is no\n    // component to attach).\n    if (route.value.component && this.routeReuseStrategy.shouldDetach(route.value.snapshot)) {\n      this.detachAndStoreRouteSubtree(route, parentContexts);\n    } else {\n      this.deactivateRouteAndOutlet(route, parentContexts);\n    }\n  }\n\n  detachAndStoreRouteSubtree(route, parentContexts) {\n    const context = parentContexts.getContext(route.value.outlet);\n    const contexts = context && route.value.component ? context.children : parentContexts;\n    const children = nodeChildrenAsMap(route);\n\n    for (const childOutlet of Object.keys(children)) {\n      this.deactivateRouteAndItsChildren(children[childOutlet], contexts);\n    }\n\n    if (context && context.outlet) {\n      const componentRef = context.outlet.detach();\n      const contexts = context.children.onOutletDeactivated();\n      this.routeReuseStrategy.store(route.value.snapshot, {\n        componentRef,\n        route,\n        contexts\n      });\n    }\n  }\n\n  deactivateRouteAndOutlet(route, parentContexts) {\n    const context = parentContexts.getContext(route.value.outlet); // The context could be `null` if we are on a componentless route but there may still be\n    // children that need deactivating.\n\n    const contexts = context && route.value.component ? context.children : parentContexts;\n    const children = nodeChildrenAsMap(route);\n\n    for (const childOutlet of Object.keys(children)) {\n      this.deactivateRouteAndItsChildren(children[childOutlet], contexts);\n    }\n\n    if (context && context.outlet) {\n      // Destroy the component\n      context.outlet.deactivate(); // Destroy the contexts for all the outlets that were in the component\n\n      context.children.onOutletDeactivated(); // Clear the information about the attached component on the context but keep the reference to\n      // the outlet.\n\n      context.attachRef = null;\n      context.resolver = null;\n      context.route = null;\n    }\n  }\n\n  activateChildRoutes(futureNode, currNode, contexts) {\n    const children = nodeChildrenAsMap(currNode);\n    futureNode.children.forEach(c => {\n      this.activateRoutes(c, children[c.value.outlet], contexts);\n      this.forwardEvent(new ActivationEnd(c.value.snapshot));\n    });\n\n    if (futureNode.children.length) {\n      this.forwardEvent(new ChildActivationEnd(futureNode.value.snapshot));\n    }\n  }\n\n  activateRoutes(futureNode, currNode, parentContexts) {\n    const future = futureNode.value;\n    const curr = currNode ? currNode.value : null;\n    advanceActivatedRoute(future); // reusing the node\n\n    if (future === curr) {\n      if (future.component) {\n        // If we have a normal route, we need to go through an outlet.\n        const context = parentContexts.getOrCreateContext(future.outlet);\n        this.activateChildRoutes(futureNode, currNode, context.children);\n      } else {\n        // if we have a componentless route, we recurse but keep the same outlet map.\n        this.activateChildRoutes(futureNode, currNode, parentContexts);\n      }\n    } else {\n      if (future.component) {\n        // if we have a normal route, we need to place the component into the outlet and recurse.\n        const context = parentContexts.getOrCreateContext(future.outlet);\n\n        if (this.routeReuseStrategy.shouldAttach(future.snapshot)) {\n          const stored = this.routeReuseStrategy.retrieve(future.snapshot);\n          this.routeReuseStrategy.store(future.snapshot, null);\n          context.children.onOutletReAttached(stored.contexts);\n          context.attachRef = stored.componentRef;\n          context.route = stored.route.value;\n\n          if (context.outlet) {\n            // Attach right away when the outlet has already been instantiated\n            // Otherwise attach from `RouterOutlet.ngOnInit` when it is instantiated\n            context.outlet.attach(stored.componentRef, stored.route.value);\n          }\n\n          advanceActivatedRoute(stored.route.value);\n          this.activateChildRoutes(futureNode, null, context.children);\n        } else {\n          const config = parentLoadedConfig(future.snapshot);\n          const cmpFactoryResolver = config ? config.module.componentFactoryResolver : null;\n          context.attachRef = null;\n          context.route = future;\n          context.resolver = cmpFactoryResolver;\n\n          if (context.outlet) {\n            // Activate the outlet when it has already been instantiated\n            // Otherwise it will get activated from its `ngOnInit` when instantiated\n            context.outlet.activateWith(future, cmpFactoryResolver);\n          }\n\n          this.activateChildRoutes(futureNode, null, context.children);\n        }\n      } else {\n        // if we have a componentless route, we recurse but keep the same outlet map.\n        this.activateChildRoutes(futureNode, null, parentContexts);\n      }\n    }\n  }\n\n}\n\nfunction parentLoadedConfig(snapshot) {\n  for (let s = snapshot.parent; s; s = s.parent) {\n    const route = s.routeConfig;\n    if (route && route._loadedConfig) return route._loadedConfig;\n    if (route && route.component) return null;\n  }\n\n  return null;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass LoadedRouterConfig {\n  constructor(routes, module) {\n    this.routes = routes;\n    this.module = module;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Simple function check, but generic so type inference will flow. Example:\n *\n * function product(a: number, b: number) {\n *   return a * b;\n * }\n *\n * if (isFunction<product>(fn)) {\n *   return fn(1, 2);\n * } else {\n *   throw \"Must provide the `product` function\";\n * }\n */\n\n\nfunction isFunction(v) {\n  return typeof v === 'function';\n}\n\nfunction isBoolean(v) {\n  return typeof v === 'boolean';\n}\n\nfunction isUrlTree(v) {\n  return v instanceof UrlTree;\n}\n\nfunction isCanLoad(guard) {\n  return guard && isFunction(guard.canLoad);\n}\n\nfunction isCanActivate(guard) {\n  return guard && isFunction(guard.canActivate);\n}\n\nfunction isCanActivateChild(guard) {\n  return guard && isFunction(guard.canActivateChild);\n}\n\nfunction isCanDeactivate(guard) {\n  return guard && isFunction(guard.canDeactivate);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst INITIAL_VALUE = /*#__PURE__*/Symbol('INITIAL_VALUE');\n\nfunction prioritizedGuardValue() {\n  return switchMap(obs => {\n    return combineLatest(obs.map(o => o.pipe(take(1), startWith(INITIAL_VALUE)))).pipe(scan((acc, list) => {\n      let isPending = false;\n      return list.reduce((innerAcc, val, i) => {\n        if (innerAcc !== INITIAL_VALUE) return innerAcc; // Toggle pending flag if any values haven't been set yet\n\n        if (val === INITIAL_VALUE) isPending = true; // Any other return values are only valid if we haven't yet hit a pending\n        // call. This guarantees that in the case of a guard at the bottom of the\n        // tree that returns a redirect, we will wait for the higher priority\n        // guard at the top to finish before performing the redirect.\n\n        if (!isPending) {\n          // Early return when we hit a `false` value as that should always\n          // cancel navigation\n          if (val === false) return val;\n\n          if (i === list.length - 1 || isUrlTree(val)) {\n            return val;\n          }\n        }\n\n        return innerAcc;\n      }, acc);\n    }, INITIAL_VALUE), filter(item => item !== INITIAL_VALUE), map(item => isUrlTree(item) ? item : item === true), //\n    take(1));\n  });\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Store contextual information about a `RouterOutlet`\n *\n * @publicApi\n */\n\n\nclass OutletContext {\n  constructor() {\n    this.outlet = null;\n    this.route = null;\n    this.resolver = null;\n    this.children = new ChildrenOutletContexts();\n    this.attachRef = null;\n  }\n\n}\n/**\n * Store contextual information about the children (= nested) `RouterOutlet`\n *\n * @publicApi\n */\n\n\nclass ChildrenOutletContexts {\n  constructor() {\n    // contexts for child outlets, by name.\n    this.contexts = new Map();\n  }\n  /** Called when a `RouterOutlet` directive is instantiated */\n\n\n  onChildOutletCreated(childName, outlet) {\n    const context = this.getOrCreateContext(childName);\n    context.outlet = outlet;\n    this.contexts.set(childName, context);\n  }\n  /**\n   * Called when a `RouterOutlet` directive is destroyed.\n   * We need to keep the context as the outlet could be destroyed inside a NgIf and might be\n   * re-created later.\n   */\n\n\n  onChildOutletDestroyed(childName) {\n    const context = this.getContext(childName);\n\n    if (context) {\n      context.outlet = null;\n      context.attachRef = null;\n    }\n  }\n  /**\n   * Called when the corresponding route is deactivated during navigation.\n   * Because the component get destroyed, all children outlet are destroyed.\n   */\n\n\n  onOutletDeactivated() {\n    const contexts = this.contexts;\n    this.contexts = new Map();\n    return contexts;\n  }\n\n  onOutletReAttached(contexts) {\n    this.contexts = contexts;\n  }\n\n  getOrCreateContext(childName) {\n    let context = this.getContext(childName);\n\n    if (!context) {\n      context = new OutletContext();\n      this.contexts.set(childName, context);\n    }\n\n    return context;\n  }\n\n  getContext(childName) {\n    return this.contexts.get(childName) || null;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @description\n *\n * Acts as a placeholder that Angular dynamically fills based on the current router state.\n *\n * Each outlet can have a unique name, determined by the optional `name` attribute.\n * The name cannot be set or changed dynamically. If not set, default value is \"primary\".\n *\n * ```\n * <router-outlet></router-outlet>\n * <router-outlet name='left'></router-outlet>\n * <router-outlet name='right'></router-outlet>\n * ```\n *\n * Named outlets can be the targets of secondary routes.\n * The `Route` object for a secondary route has an `outlet` property to identify the target outlet:\n *\n * `{path: <base-path>, component: <component>, outlet: <target_outlet_name>}`\n *\n * Using named outlets and secondary routes, you can target multiple outlets in\n * the same `RouterLink` directive.\n *\n * The router keeps track of separate branches in a navigation tree for each named outlet and\n * generates a representation of that tree in the URL.\n * The URL for a secondary route uses the following syntax to specify both the primary and secondary\n * routes at the same time:\n *\n * `http://base-path/primary-route-path(outlet-name:route-path)`\n *\n * A router outlet emits an activate event when a new component is instantiated,\n * deactivate event when a component is destroyed.\n * An attached event emits when the `RouteReuseStrategy` instructs the outlet to reattach the\n * subtree, and the detached event emits when the `RouteReuseStrategy` instructs the outlet to\n * detach the subtree.\n *\n * ```\n * <router-outlet\n *   (activate)='onActivate($event)'\n *   (deactivate)='onDeactivate($event)'\n *   (attach)='onAttach($event)'\n *   (detach)='onDetach($event)'></router-outlet>\n * ```\n *\n * @see [Routing tutorial](guide/router-tutorial-toh#named-outlets \"Example of a named\n * outlet and secondary route configuration\").\n * @see `RouterLink`\n * @see `Route`\n * @ngModule RouterModule\n *\n * @publicApi\n */\n\n\nlet RouterOutlet = /*#__PURE__*/(() => {\n  class RouterOutlet {\n    constructor(parentContexts, location, resolver, name, changeDetector) {\n      this.parentContexts = parentContexts;\n      this.location = location;\n      this.resolver = resolver;\n      this.changeDetector = changeDetector;\n      this.activated = null;\n      this._activatedRoute = null;\n      this.activateEvents = new EventEmitter();\n      this.deactivateEvents = new EventEmitter();\n      /**\n       * Emits an attached component instance when the `RouteReuseStrategy` instructs to re-attach a\n       * previously detached subtree.\n       **/\n\n      this.attachEvents = new EventEmitter();\n      /**\n       * Emits a detached component instance when the `RouteReuseStrategy` instructs to detach the\n       * subtree.\n       */\n\n      this.detachEvents = new EventEmitter();\n      this.name = name || PRIMARY_OUTLET;\n      parentContexts.onChildOutletCreated(this.name, this);\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      this.parentContexts.onChildOutletDestroyed(this.name);\n    }\n    /** @nodoc */\n\n\n    ngOnInit() {\n      if (!this.activated) {\n        // If the outlet was not instantiated at the time the route got activated we need to populate\n        // the outlet when it is initialized (ie inside a NgIf)\n        const context = this.parentContexts.getContext(this.name);\n\n        if (context && context.route) {\n          if (context.attachRef) {\n            // `attachRef` is populated when there is an existing component to mount\n            this.attach(context.attachRef, context.route);\n          } else {\n            // otherwise the component defined in the configuration is created\n            this.activateWith(context.route, context.resolver || null);\n          }\n        }\n      }\n    }\n\n    get isActivated() {\n      return !!this.activated;\n    }\n    /**\n     * @returns The currently activated component instance.\n     * @throws An error if the outlet is not activated.\n     */\n\n\n    get component() {\n      if (!this.activated) throw new Error('Outlet is not activated');\n      return this.activated.instance;\n    }\n\n    get activatedRoute() {\n      if (!this.activated) throw new Error('Outlet is not activated');\n      return this._activatedRoute;\n    }\n\n    get activatedRouteData() {\n      if (this._activatedRoute) {\n        return this._activatedRoute.snapshot.data;\n      }\n\n      return {};\n    }\n    /**\n     * Called when the `RouteReuseStrategy` instructs to detach the subtree\n     */\n\n\n    detach() {\n      if (!this.activated) throw new Error('Outlet is not activated');\n      this.location.detach();\n      const cmp = this.activated;\n      this.activated = null;\n      this._activatedRoute = null;\n      this.detachEvents.emit(cmp.instance);\n      return cmp;\n    }\n    /**\n     * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n     */\n\n\n    attach(ref, activatedRoute) {\n      this.activated = ref;\n      this._activatedRoute = activatedRoute;\n      this.location.insert(ref.hostView);\n      this.attachEvents.emit(ref.instance);\n    }\n\n    deactivate() {\n      if (this.activated) {\n        const c = this.component;\n        this.activated.destroy();\n        this.activated = null;\n        this._activatedRoute = null;\n        this.deactivateEvents.emit(c);\n      }\n    }\n\n    activateWith(activatedRoute, resolver) {\n      if (this.isActivated) {\n        throw new Error('Cannot activate an already activated outlet');\n      }\n\n      this._activatedRoute = activatedRoute;\n      const snapshot = activatedRoute._futureSnapshot;\n      const component = snapshot.routeConfig.component;\n      resolver = resolver || this.resolver;\n      const factory = resolver.resolveComponentFactory(component);\n      const childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n      const injector = new OutletInjector(activatedRoute, childContexts, this.location.injector);\n      this.activated = this.location.createComponent(factory, this.location.length, injector); // Calling `markForCheck` to make sure we will run the change detection when the\n      // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n\n      this.changeDetector.markForCheck();\n      this.activateEvents.emit(this.activated.instance);\n    }\n\n  }\n\n  RouterOutlet.ɵfac = function RouterOutlet_Factory(t) {\n    return new (t || RouterOutlet)(i0.ɵɵdirectiveInject(ChildrenOutletContexts), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵinjectAttribute('name'), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n\n  RouterOutlet.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterOutlet,\n    selectors: [[\"router-outlet\"]],\n    outputs: {\n      activateEvents: \"activate\",\n      deactivateEvents: \"deactivate\",\n      attachEvents: \"attach\",\n      detachEvents: \"detach\"\n    },\n    exportAs: [\"outlet\"]\n  });\n  return RouterOutlet;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nclass OutletInjector {\n  constructor(route, childContexts, parent) {\n    this.route = route;\n    this.childContexts = childContexts;\n    this.parent = parent;\n  }\n\n  get(token, notFoundValue) {\n    if (token === ActivatedRoute) {\n      return this.route;\n    }\n\n    if (token === ChildrenOutletContexts) {\n      return this.childContexts;\n    }\n\n    return this.parent.get(token, notFoundValue);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * This component is used internally within the router to be a placeholder when an empty\n * router-outlet is needed. For example, with a config such as:\n *\n * `{path: 'parent', outlet: 'nav', children: [...]}`\n *\n * In order to render, there needs to be a component on this config, which will default\n * to this `EmptyOutletComponent`.\n */\n\n\nlet ɵEmptyOutletComponent = /*#__PURE__*/(() => {\n  class ɵEmptyOutletComponent {}\n\n  ɵEmptyOutletComponent.ɵfac = function ɵEmptyOutletComponent_Factory(t) {\n    return new (t || ɵEmptyOutletComponent)();\n  };\n\n  ɵEmptyOutletComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ɵEmptyOutletComponent,\n    selectors: [[\"ng-component\"]],\n    decls: 1,\n    vars: 0,\n    template: function ɵEmptyOutletComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"router-outlet\");\n      }\n    },\n    directives: [RouterOutlet],\n    encapsulation: 2\n  });\n  return ɵEmptyOutletComponent;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction validateConfig(config, parentPath = '') {\n  // forEach doesn't iterate undefined values\n  for (let i = 0; i < config.length; i++) {\n    const route = config[i];\n    const fullPath = getFullPath(parentPath, route);\n    validateNode(route, fullPath);\n  }\n}\n\nfunction validateNode(route, fullPath) {\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    if (!route) {\n      throw new Error(`\n      Invalid configuration of route '${fullPath}': Encountered undefined route.\n      The reason might be an extra comma.\n\n      Example:\n      const routes: Routes = [\n        { path: '', redirectTo: '/dashboard', pathMatch: 'full' },\n        { path: 'dashboard',  component: DashboardComponent },, << two commas\n        { path: 'detail/:id', component: HeroDetailComponent }\n      ];\n    `);\n    }\n\n    if (Array.isArray(route)) {\n      throw new Error(`Invalid configuration of route '${fullPath}': Array cannot be specified`);\n    }\n\n    if (!route.component && !route.children && !route.loadChildren && route.outlet && route.outlet !== PRIMARY_OUTLET) {\n      throw new Error(`Invalid configuration of route '${fullPath}': a componentless route without children or loadChildren cannot have a named outlet set`);\n    }\n\n    if (route.redirectTo && route.children) {\n      throw new Error(`Invalid configuration of route '${fullPath}': redirectTo and children cannot be used together`);\n    }\n\n    if (route.redirectTo && route.loadChildren) {\n      throw new Error(`Invalid configuration of route '${fullPath}': redirectTo and loadChildren cannot be used together`);\n    }\n\n    if (route.children && route.loadChildren) {\n      throw new Error(`Invalid configuration of route '${fullPath}': children and loadChildren cannot be used together`);\n    }\n\n    if (route.redirectTo && route.component) {\n      throw new Error(`Invalid configuration of route '${fullPath}': redirectTo and component cannot be used together`);\n    }\n\n    if (route.redirectTo && route.canActivate) {\n      throw new Error(`Invalid configuration of route '${fullPath}': redirectTo and canActivate cannot be used together. Redirects happen before activation ` + `so canActivate will never be executed.`);\n    }\n\n    if (route.path && route.matcher) {\n      throw new Error(`Invalid configuration of route '${fullPath}': path and matcher cannot be used together`);\n    }\n\n    if (route.redirectTo === void 0 && !route.component && !route.children && !route.loadChildren) {\n      throw new Error(`Invalid configuration of route '${fullPath}'. One of the following must be provided: component, redirectTo, children or loadChildren`);\n    }\n\n    if (route.path === void 0 && route.matcher === void 0) {\n      throw new Error(`Invalid configuration of route '${fullPath}': routes must have either a path or a matcher specified`);\n    }\n\n    if (typeof route.path === 'string' && route.path.charAt(0) === '/') {\n      throw new Error(`Invalid configuration of route '${fullPath}': path cannot start with a slash`);\n    }\n\n    if (route.path === '' && route.redirectTo !== void 0 && route.pathMatch === void 0) {\n      const exp = `The default value of 'pathMatch' is 'prefix', but often the intent is to use 'full'.`;\n      throw new Error(`Invalid configuration of route '{path: \"${fullPath}\", redirectTo: \"${route.redirectTo}\"}': please provide 'pathMatch'. ${exp}`);\n    }\n\n    if (route.pathMatch !== void 0 && route.pathMatch !== 'full' && route.pathMatch !== 'prefix') {\n      throw new Error(`Invalid configuration of route '${fullPath}': pathMatch can only be set to 'prefix' or 'full'`);\n    }\n  }\n\n  if (route.children) {\n    validateConfig(route.children, fullPath);\n  }\n}\n\nfunction getFullPath(parentPath, currentRoute) {\n  if (!currentRoute) {\n    return parentPath;\n  }\n\n  if (!parentPath && !currentRoute.path) {\n    return '';\n  } else if (parentPath && !currentRoute.path) {\n    return `${parentPath}/`;\n  } else if (!parentPath && currentRoute.path) {\n    return currentRoute.path;\n  } else {\n    return `${parentPath}/${currentRoute.path}`;\n  }\n}\n/**\n * Makes a copy of the config and adds any default required properties.\n */\n\n\nfunction standardizeConfig(r) {\n  const children = r.children && r.children.map(standardizeConfig);\n  const c = children ? Object.assign(Object.assign({}, r), {\n    children\n  }) : Object.assign({}, r);\n\n  if (!c.component && (children || c.loadChildren) && c.outlet && c.outlet !== PRIMARY_OUTLET) {\n    c.component = ɵEmptyOutletComponent;\n  }\n\n  return c;\n}\n/** Returns the `route.outlet` or PRIMARY_OUTLET if none exists. */\n\n\nfunction getOutlet(route) {\n  return route.outlet || PRIMARY_OUTLET;\n}\n/**\n * Sorts the `routes` such that the ones with an outlet matching `outletName` come first.\n * The order of the configs is otherwise preserved.\n */\n\n\nfunction sortByMatchingOutlets(routes, outletName) {\n  const sortedConfig = routes.filter(r => getOutlet(r) === outletName);\n  sortedConfig.push(...routes.filter(r => getOutlet(r) !== outletName));\n  return sortedConfig;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst noMatch$1 = {\n  matched: false,\n  consumedSegments: [],\n  lastChild: 0,\n  parameters: {},\n  positionalParamSegments: {}\n};\n\nfunction match(segmentGroup, route, segments) {\n  var _a;\n\n  if (route.path === '') {\n    if (route.pathMatch === 'full' && (segmentGroup.hasChildren() || segments.length > 0)) {\n      return Object.assign({}, noMatch$1);\n    }\n\n    return {\n      matched: true,\n      consumedSegments: [],\n      lastChild: 0,\n      parameters: {},\n      positionalParamSegments: {}\n    };\n  }\n\n  const matcher = route.matcher || defaultUrlMatcher;\n  const res = matcher(segments, segmentGroup, route);\n  if (!res) return Object.assign({}, noMatch$1);\n  const posParams = {};\n  forEach(res.posParams, (v, k) => {\n    posParams[k] = v.path;\n  });\n  const parameters = res.consumed.length > 0 ? Object.assign(Object.assign({}, posParams), res.consumed[res.consumed.length - 1].parameters) : posParams;\n  return {\n    matched: true,\n    consumedSegments: res.consumed,\n    lastChild: res.consumed.length,\n    // TODO(atscott): investigate combining parameters and positionalParamSegments\n    parameters,\n    positionalParamSegments: (_a = res.posParams) !== null && _a !== void 0 ? _a : {}\n  };\n}\n\nfunction split(segmentGroup, consumedSegments, slicedSegments, config, relativeLinkResolution = 'corrected') {\n  if (slicedSegments.length > 0 && containsEmptyPathMatchesWithNamedOutlets(segmentGroup, slicedSegments, config)) {\n    const s = new UrlSegmentGroup(consumedSegments, createChildrenForEmptyPaths(segmentGroup, consumedSegments, config, new UrlSegmentGroup(slicedSegments, segmentGroup.children)));\n    s._sourceSegment = segmentGroup;\n    s._segmentIndexShift = consumedSegments.length;\n    return {\n      segmentGroup: s,\n      slicedSegments: []\n    };\n  }\n\n  if (slicedSegments.length === 0 && containsEmptyPathMatches(segmentGroup, slicedSegments, config)) {\n    const s = new UrlSegmentGroup(segmentGroup.segments, addEmptyPathsToChildrenIfNeeded(segmentGroup, consumedSegments, slicedSegments, config, segmentGroup.children, relativeLinkResolution));\n    s._sourceSegment = segmentGroup;\n    s._segmentIndexShift = consumedSegments.length;\n    return {\n      segmentGroup: s,\n      slicedSegments\n    };\n  }\n\n  const s = new UrlSegmentGroup(segmentGroup.segments, segmentGroup.children);\n  s._sourceSegment = segmentGroup;\n  s._segmentIndexShift = consumedSegments.length;\n  return {\n    segmentGroup: s,\n    slicedSegments\n  };\n}\n\nfunction addEmptyPathsToChildrenIfNeeded(segmentGroup, consumedSegments, slicedSegments, routes, children, relativeLinkResolution) {\n  const res = {};\n\n  for (const r of routes) {\n    if (emptyPathMatch(segmentGroup, slicedSegments, r) && !children[getOutlet(r)]) {\n      const s = new UrlSegmentGroup([], {});\n      s._sourceSegment = segmentGroup;\n\n      if (relativeLinkResolution === 'legacy') {\n        s._segmentIndexShift = segmentGroup.segments.length;\n      } else {\n        s._segmentIndexShift = consumedSegments.length;\n      }\n\n      res[getOutlet(r)] = s;\n    }\n  }\n\n  return Object.assign(Object.assign({}, children), res);\n}\n\nfunction createChildrenForEmptyPaths(segmentGroup, consumedSegments, routes, primarySegment) {\n  const res = {};\n  res[PRIMARY_OUTLET] = primarySegment;\n  primarySegment._sourceSegment = segmentGroup;\n  primarySegment._segmentIndexShift = consumedSegments.length;\n\n  for (const r of routes) {\n    if (r.path === '' && getOutlet(r) !== PRIMARY_OUTLET) {\n      const s = new UrlSegmentGroup([], {});\n      s._sourceSegment = segmentGroup;\n      s._segmentIndexShift = consumedSegments.length;\n      res[getOutlet(r)] = s;\n    }\n  }\n\n  return res;\n}\n\nfunction containsEmptyPathMatchesWithNamedOutlets(segmentGroup, slicedSegments, routes) {\n  return routes.some(r => emptyPathMatch(segmentGroup, slicedSegments, r) && getOutlet(r) !== PRIMARY_OUTLET);\n}\n\nfunction containsEmptyPathMatches(segmentGroup, slicedSegments, routes) {\n  return routes.some(r => emptyPathMatch(segmentGroup, slicedSegments, r));\n}\n\nfunction emptyPathMatch(segmentGroup, slicedSegments, r) {\n  if ((segmentGroup.hasChildren() || slicedSegments.length > 0) && r.pathMatch === 'full') {\n    return false;\n  }\n\n  return r.path === '';\n}\n/**\n * Determines if `route` is a path match for the `rawSegment`, `segments`, and `outlet` without\n * verifying that its children are a full match for the remainder of the `rawSegment` children as\n * well.\n */\n\n\nfunction isImmediateMatch(route, rawSegment, segments, outlet) {\n  // We allow matches to empty paths when the outlets differ so we can match a url like `/(b:b)` to\n  // a config like\n  // * `{path: '', children: [{path: 'b', outlet: 'b'}]}`\n  // or even\n  // * `{path: '', outlet: 'a', children: [{path: 'b', outlet: 'b'}]`\n  //\n  // The exception here is when the segment outlet is for the primary outlet. This would\n  // result in a match inside the named outlet because all children there are written as primary\n  // outlets. So we need to prevent child named outlet matches in a url like `/b` in a config like\n  // * `{path: '', outlet: 'x' children: [{path: 'b'}]}`\n  // This should only match if the url is `/(x:b)`.\n  if (getOutlet(route) !== outlet && (outlet === PRIMARY_OUTLET || !emptyPathMatch(rawSegment, segments, route))) {\n    return false;\n  }\n\n  if (route.path === '**') {\n    return true;\n  }\n\n  return match(rawSegment, route, segments).matched;\n}\n\nfunction noLeftoversInUrl(segmentGroup, segments, outlet) {\n  return segments.length === 0 && !segmentGroup.children[outlet];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass NoMatch$1 {\n  constructor(segmentGroup) {\n    this.segmentGroup = segmentGroup || null;\n  }\n\n}\n\nclass AbsoluteRedirect {\n  constructor(urlTree) {\n    this.urlTree = urlTree;\n  }\n\n}\n\nfunction noMatch(segmentGroup) {\n  return new Observable(obs => obs.error(new NoMatch$1(segmentGroup)));\n}\n\nfunction absoluteRedirect(newTree) {\n  return new Observable(obs => obs.error(new AbsoluteRedirect(newTree)));\n}\n\nfunction namedOutletsRedirect(redirectTo) {\n  return new Observable(obs => obs.error(new Error(`Only absolute redirects can have named outlets. redirectTo: '${redirectTo}'`)));\n}\n\nfunction canLoadFails(route) {\n  return new Observable(obs => obs.error(navigationCancelingError(`Cannot load children because the guard of the route \"path: '${route.path}'\" returned false`)));\n}\n/**\n * Returns the `UrlTree` with the redirection applied.\n *\n * Lazy modules are loaded along the way.\n */\n\n\nfunction applyRedirects$1(moduleInjector, configLoader, urlSerializer, urlTree, config) {\n  return new ApplyRedirects(moduleInjector, configLoader, urlSerializer, urlTree, config).apply();\n}\n\nclass ApplyRedirects {\n  constructor(moduleInjector, configLoader, urlSerializer, urlTree, config) {\n    this.configLoader = configLoader;\n    this.urlSerializer = urlSerializer;\n    this.urlTree = urlTree;\n    this.config = config;\n    this.allowRedirects = true;\n    this.ngModule = moduleInjector.get(NgModuleRef);\n  }\n\n  apply() {\n    const splitGroup = split(this.urlTree.root, [], [], this.config).segmentGroup; // TODO(atscott): creating a new segment removes the _sourceSegment _segmentIndexShift, which is\n    // only necessary to prevent failures in tests which assert exact object matches. The `split` is\n    // now shared between `applyRedirects` and `recognize` but only the `recognize` step needs these\n    // properties. Before the implementations were merged, the `applyRedirects` would not assign\n    // them. We should be able to remove this logic as a \"breaking change\" but should do some more\n    // investigation into the failures first.\n\n    const rootSegmentGroup = new UrlSegmentGroup(splitGroup.segments, splitGroup.children);\n    const expanded$ = this.expandSegmentGroup(this.ngModule, this.config, rootSegmentGroup, PRIMARY_OUTLET);\n    const urlTrees$ = expanded$.pipe(map(rootSegmentGroup => {\n      return this.createUrlTree(squashSegmentGroup(rootSegmentGroup), this.urlTree.queryParams, this.urlTree.fragment);\n    }));\n    return urlTrees$.pipe(catchError(e => {\n      if (e instanceof AbsoluteRedirect) {\n        // After an absolute redirect we do not apply any more redirects!\n        // If this implementation changes, update the documentation note in `redirectTo`.\n        this.allowRedirects = false; // we need to run matching, so we can fetch all lazy-loaded modules\n\n        return this.match(e.urlTree);\n      }\n\n      if (e instanceof NoMatch$1) {\n        throw this.noMatchError(e);\n      }\n\n      throw e;\n    }));\n  }\n\n  match(tree) {\n    const expanded$ = this.expandSegmentGroup(this.ngModule, this.config, tree.root, PRIMARY_OUTLET);\n    const mapped$ = expanded$.pipe(map(rootSegmentGroup => {\n      return this.createUrlTree(squashSegmentGroup(rootSegmentGroup), tree.queryParams, tree.fragment);\n    }));\n    return mapped$.pipe(catchError(e => {\n      if (e instanceof NoMatch$1) {\n        throw this.noMatchError(e);\n      }\n\n      throw e;\n    }));\n  }\n\n  noMatchError(e) {\n    return new Error(`Cannot match any routes. URL Segment: '${e.segmentGroup}'`);\n  }\n\n  createUrlTree(rootCandidate, queryParams, fragment) {\n    const root = rootCandidate.segments.length > 0 ? new UrlSegmentGroup([], {\n      [PRIMARY_OUTLET]: rootCandidate\n    }) : rootCandidate;\n    return new UrlTree(root, queryParams, fragment);\n  }\n\n  expandSegmentGroup(ngModule, routes, segmentGroup, outlet) {\n    if (segmentGroup.segments.length === 0 && segmentGroup.hasChildren()) {\n      return this.expandChildren(ngModule, routes, segmentGroup).pipe(map(children => new UrlSegmentGroup([], children)));\n    }\n\n    return this.expandSegment(ngModule, segmentGroup, routes, segmentGroup.segments, outlet, true);\n  } // Recursively expand segment groups for all the child outlets\n\n\n  expandChildren(ngModule, routes, segmentGroup) {\n    // Expand outlets one at a time, starting with the primary outlet. We need to do it this way\n    // because an absolute redirect from the primary outlet takes precedence.\n    const childOutlets = [];\n\n    for (const child of Object.keys(segmentGroup.children)) {\n      if (child === 'primary') {\n        childOutlets.unshift(child);\n      } else {\n        childOutlets.push(child);\n      }\n    }\n\n    return from(childOutlets).pipe(concatMap(childOutlet => {\n      const child = segmentGroup.children[childOutlet]; // Sort the routes so routes with outlets that match the segment appear\n      // first, followed by routes for other outlets, which might match if they have an\n      // empty path.\n\n      const sortedRoutes = sortByMatchingOutlets(routes, childOutlet);\n      return this.expandSegmentGroup(ngModule, sortedRoutes, child, childOutlet).pipe(map(s => ({\n        segment: s,\n        outlet: childOutlet\n      })));\n    }), scan((children, expandedChild) => {\n      children[expandedChild.outlet] = expandedChild.segment;\n      return children;\n    }, {}), last$1());\n  }\n\n  expandSegment(ngModule, segmentGroup, routes, segments, outlet, allowRedirects) {\n    return from(routes).pipe(concatMap(r => {\n      const expanded$ = this.expandSegmentAgainstRoute(ngModule, segmentGroup, routes, r, segments, outlet, allowRedirects);\n      return expanded$.pipe(catchError(e => {\n        if (e instanceof NoMatch$1) {\n          return of(null);\n        }\n\n        throw e;\n      }));\n    }), first(s => !!s), catchError((e, _) => {\n      if (e instanceof EmptyError || e.name === 'EmptyError') {\n        if (noLeftoversInUrl(segmentGroup, segments, outlet)) {\n          return of(new UrlSegmentGroup([], {}));\n        }\n\n        throw new NoMatch$1(segmentGroup);\n      }\n\n      throw e;\n    }));\n  }\n\n  expandSegmentAgainstRoute(ngModule, segmentGroup, routes, route, paths, outlet, allowRedirects) {\n    if (!isImmediateMatch(route, segmentGroup, paths, outlet)) {\n      return noMatch(segmentGroup);\n    }\n\n    if (route.redirectTo === undefined) {\n      return this.matchSegmentAgainstRoute(ngModule, segmentGroup, route, paths, outlet);\n    }\n\n    if (allowRedirects && this.allowRedirects) {\n      return this.expandSegmentAgainstRouteUsingRedirect(ngModule, segmentGroup, routes, route, paths, outlet);\n    }\n\n    return noMatch(segmentGroup);\n  }\n\n  expandSegmentAgainstRouteUsingRedirect(ngModule, segmentGroup, routes, route, segments, outlet) {\n    if (route.path === '**') {\n      return this.expandWildCardWithParamsAgainstRouteUsingRedirect(ngModule, routes, route, outlet);\n    }\n\n    return this.expandRegularSegmentAgainstRouteUsingRedirect(ngModule, segmentGroup, routes, route, segments, outlet);\n  }\n\n  expandWildCardWithParamsAgainstRouteUsingRedirect(ngModule, routes, route, outlet) {\n    const newTree = this.applyRedirectCommands([], route.redirectTo, {});\n\n    if (route.redirectTo.startsWith('/')) {\n      return absoluteRedirect(newTree);\n    }\n\n    return this.lineralizeSegments(route, newTree).pipe(mergeMap(newSegments => {\n      const group = new UrlSegmentGroup(newSegments, {});\n      return this.expandSegment(ngModule, group, routes, newSegments, outlet, false);\n    }));\n  }\n\n  expandRegularSegmentAgainstRouteUsingRedirect(ngModule, segmentGroup, routes, route, segments, outlet) {\n    const {\n      matched,\n      consumedSegments,\n      lastChild,\n      positionalParamSegments\n    } = match(segmentGroup, route, segments);\n    if (!matched) return noMatch(segmentGroup);\n    const newTree = this.applyRedirectCommands(consumedSegments, route.redirectTo, positionalParamSegments);\n\n    if (route.redirectTo.startsWith('/')) {\n      return absoluteRedirect(newTree);\n    }\n\n    return this.lineralizeSegments(route, newTree).pipe(mergeMap(newSegments => {\n      return this.expandSegment(ngModule, segmentGroup, routes, newSegments.concat(segments.slice(lastChild)), outlet, false);\n    }));\n  }\n\n  matchSegmentAgainstRoute(ngModule, rawSegmentGroup, route, segments, outlet) {\n    if (route.path === '**') {\n      if (route.loadChildren) {\n        const loaded$ = route._loadedConfig ? of(route._loadedConfig) : this.configLoader.load(ngModule.injector, route);\n        return loaded$.pipe(map(cfg => {\n          route._loadedConfig = cfg;\n          return new UrlSegmentGroup(segments, {});\n        }));\n      }\n\n      return of(new UrlSegmentGroup(segments, {}));\n    }\n\n    const {\n      matched,\n      consumedSegments,\n      lastChild\n    } = match(rawSegmentGroup, route, segments);\n    if (!matched) return noMatch(rawSegmentGroup);\n    const rawSlicedSegments = segments.slice(lastChild);\n    const childConfig$ = this.getChildConfig(ngModule, route, segments);\n    return childConfig$.pipe(mergeMap(routerConfig => {\n      const childModule = routerConfig.module;\n      const childConfig = routerConfig.routes;\n      const {\n        segmentGroup: splitSegmentGroup,\n        slicedSegments\n      } = split(rawSegmentGroup, consumedSegments, rawSlicedSegments, childConfig); // See comment on the other call to `split` about why this is necessary.\n\n      const segmentGroup = new UrlSegmentGroup(splitSegmentGroup.segments, splitSegmentGroup.children);\n\n      if (slicedSegments.length === 0 && segmentGroup.hasChildren()) {\n        const expanded$ = this.expandChildren(childModule, childConfig, segmentGroup);\n        return expanded$.pipe(map(children => new UrlSegmentGroup(consumedSegments, children)));\n      }\n\n      if (childConfig.length === 0 && slicedSegments.length === 0) {\n        return of(new UrlSegmentGroup(consumedSegments, {}));\n      }\n\n      const matchedOnOutlet = getOutlet(route) === outlet;\n      const expanded$ = this.expandSegment(childModule, segmentGroup, childConfig, slicedSegments, matchedOnOutlet ? PRIMARY_OUTLET : outlet, true);\n      return expanded$.pipe(map(cs => new UrlSegmentGroup(consumedSegments.concat(cs.segments), cs.children)));\n    }));\n  }\n\n  getChildConfig(ngModule, route, segments) {\n    if (route.children) {\n      // The children belong to the same module\n      return of(new LoadedRouterConfig(route.children, ngModule));\n    }\n\n    if (route.loadChildren) {\n      // lazy children belong to the loaded module\n      if (route._loadedConfig !== undefined) {\n        return of(route._loadedConfig);\n      }\n\n      return this.runCanLoadGuards(ngModule.injector, route, segments).pipe(mergeMap(shouldLoadResult => {\n        if (shouldLoadResult) {\n          return this.configLoader.load(ngModule.injector, route).pipe(map(cfg => {\n            route._loadedConfig = cfg;\n            return cfg;\n          }));\n        }\n\n        return canLoadFails(route);\n      }));\n    }\n\n    return of(new LoadedRouterConfig([], ngModule));\n  }\n\n  runCanLoadGuards(moduleInjector, route, segments) {\n    const canLoad = route.canLoad;\n    if (!canLoad || canLoad.length === 0) return of(true);\n    const canLoadObservables = canLoad.map(injectionToken => {\n      const guard = moduleInjector.get(injectionToken);\n      let guardVal;\n\n      if (isCanLoad(guard)) {\n        guardVal = guard.canLoad(route, segments);\n      } else if (isFunction(guard)) {\n        guardVal = guard(route, segments);\n      } else {\n        throw new Error('Invalid CanLoad guard');\n      }\n\n      return wrapIntoObservable(guardVal);\n    });\n    return of(canLoadObservables).pipe(prioritizedGuardValue(), tap(result => {\n      if (!isUrlTree(result)) return;\n      const error = navigationCancelingError(`Redirecting to \"${this.urlSerializer.serialize(result)}\"`);\n      error.url = result;\n      throw error;\n    }), map(result => result === true));\n  }\n\n  lineralizeSegments(route, urlTree) {\n    let res = [];\n    let c = urlTree.root;\n\n    while (true) {\n      res = res.concat(c.segments);\n\n      if (c.numberOfChildren === 0) {\n        return of(res);\n      }\n\n      if (c.numberOfChildren > 1 || !c.children[PRIMARY_OUTLET]) {\n        return namedOutletsRedirect(route.redirectTo);\n      }\n\n      c = c.children[PRIMARY_OUTLET];\n    }\n  }\n\n  applyRedirectCommands(segments, redirectTo, posParams) {\n    return this.applyRedirectCreatreUrlTree(redirectTo, this.urlSerializer.parse(redirectTo), segments, posParams);\n  }\n\n  applyRedirectCreatreUrlTree(redirectTo, urlTree, segments, posParams) {\n    const newRoot = this.createSegmentGroup(redirectTo, urlTree.root, segments, posParams);\n    return new UrlTree(newRoot, this.createQueryParams(urlTree.queryParams, this.urlTree.queryParams), urlTree.fragment);\n  }\n\n  createQueryParams(redirectToParams, actualParams) {\n    const res = {};\n    forEach(redirectToParams, (v, k) => {\n      const copySourceValue = typeof v === 'string' && v.startsWith(':');\n\n      if (copySourceValue) {\n        const sourceName = v.substring(1);\n        res[k] = actualParams[sourceName];\n      } else {\n        res[k] = v;\n      }\n    });\n    return res;\n  }\n\n  createSegmentGroup(redirectTo, group, segments, posParams) {\n    const updatedSegments = this.createSegments(redirectTo, group.segments, segments, posParams);\n    let children = {};\n    forEach(group.children, (child, name) => {\n      children[name] = this.createSegmentGroup(redirectTo, child, segments, posParams);\n    });\n    return new UrlSegmentGroup(updatedSegments, children);\n  }\n\n  createSegments(redirectTo, redirectToSegments, actualSegments, posParams) {\n    return redirectToSegments.map(s => s.path.startsWith(':') ? this.findPosParam(redirectTo, s, posParams) : this.findOrReturn(s, actualSegments));\n  }\n\n  findPosParam(redirectTo, redirectToUrlSegment, posParams) {\n    const pos = posParams[redirectToUrlSegment.path.substring(1)];\n    if (!pos) throw new Error(`Cannot redirect to '${redirectTo}'. Cannot find '${redirectToUrlSegment.path}'.`);\n    return pos;\n  }\n\n  findOrReturn(redirectToUrlSegment, actualSegments) {\n    let idx = 0;\n\n    for (const s of actualSegments) {\n      if (s.path === redirectToUrlSegment.path) {\n        actualSegments.splice(idx);\n        return s;\n      }\n\n      idx++;\n    }\n\n    return redirectToUrlSegment;\n  }\n\n}\n/**\n * When possible, merges the primary outlet child into the parent `UrlSegmentGroup`.\n *\n * When a segment group has only one child which is a primary outlet, merges that child into the\n * parent. That is, the child segment group's segments are merged into the `s` and the child's\n * children become the children of `s`. Think of this like a 'squash', merging the child segment\n * group into the parent.\n */\n\n\nfunction mergeTrivialChildren(s) {\n  if (s.numberOfChildren === 1 && s.children[PRIMARY_OUTLET]) {\n    const c = s.children[PRIMARY_OUTLET];\n    return new UrlSegmentGroup(s.segments.concat(c.segments), c.children);\n  }\n\n  return s;\n}\n/**\n * Recursively merges primary segment children into their parents and also drops empty children\n * (those which have no segments and no children themselves). The latter prevents serializing a\n * group into something like `/a(aux:)`, where `aux` is an empty child segment.\n */\n\n\nfunction squashSegmentGroup(segmentGroup) {\n  const newChildren = {};\n\n  for (const childOutlet of Object.keys(segmentGroup.children)) {\n    const child = segmentGroup.children[childOutlet];\n    const childCandidate = squashSegmentGroup(child); // don't add empty children\n\n    if (childCandidate.segments.length > 0 || childCandidate.hasChildren()) {\n      newChildren[childOutlet] = childCandidate;\n    }\n  }\n\n  const s = new UrlSegmentGroup(segmentGroup.segments, newChildren);\n  return mergeTrivialChildren(s);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction applyRedirects(moduleInjector, configLoader, urlSerializer, config) {\n  return switchMap(t => applyRedirects$1(moduleInjector, configLoader, urlSerializer, t.extractedUrl, config).pipe(map(urlAfterRedirects => Object.assign(Object.assign({}, t), {\n    urlAfterRedirects\n  }))));\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass CanActivate {\n  constructor(path) {\n    this.path = path;\n    this.route = this.path[this.path.length - 1];\n  }\n\n}\n\nclass CanDeactivate {\n  constructor(component, route) {\n    this.component = component;\n    this.route = route;\n  }\n\n}\n\nfunction getAllRouteGuards(future, curr, parentContexts) {\n  const futureRoot = future._root;\n  const currRoot = curr ? curr._root : null;\n  return getChildRouteGuards(futureRoot, currRoot, parentContexts, [futureRoot.value]);\n}\n\nfunction getCanActivateChild(p) {\n  const canActivateChild = p.routeConfig ? p.routeConfig.canActivateChild : null;\n  if (!canActivateChild || canActivateChild.length === 0) return null;\n  return {\n    node: p,\n    guards: canActivateChild\n  };\n}\n\nfunction getToken(token, snapshot, moduleInjector) {\n  const config = getClosestLoadedConfig(snapshot);\n  const injector = config ? config.module.injector : moduleInjector;\n  return injector.get(token);\n}\n\nfunction getClosestLoadedConfig(snapshot) {\n  if (!snapshot) return null;\n\n  for (let s = snapshot.parent; s; s = s.parent) {\n    const route = s.routeConfig;\n    if (route && route._loadedConfig) return route._loadedConfig;\n  }\n\n  return null;\n}\n\nfunction getChildRouteGuards(futureNode, currNode, contexts, futurePath, checks = {\n  canDeactivateChecks: [],\n  canActivateChecks: []\n}) {\n  const prevChildren = nodeChildrenAsMap(currNode); // Process the children of the future route\n\n  futureNode.children.forEach(c => {\n    getRouteGuards(c, prevChildren[c.value.outlet], contexts, futurePath.concat([c.value]), checks);\n    delete prevChildren[c.value.outlet];\n  }); // Process any children left from the current route (not active for the future route)\n\n  forEach(prevChildren, (v, k) => deactivateRouteAndItsChildren(v, contexts.getContext(k), checks));\n  return checks;\n}\n\nfunction getRouteGuards(futureNode, currNode, parentContexts, futurePath, checks = {\n  canDeactivateChecks: [],\n  canActivateChecks: []\n}) {\n  const future = futureNode.value;\n  const curr = currNode ? currNode.value : null;\n  const context = parentContexts ? parentContexts.getContext(futureNode.value.outlet) : null; // reusing the node\n\n  if (curr && future.routeConfig === curr.routeConfig) {\n    const shouldRun = shouldRunGuardsAndResolvers(curr, future, future.routeConfig.runGuardsAndResolvers);\n\n    if (shouldRun) {\n      checks.canActivateChecks.push(new CanActivate(futurePath));\n    } else {\n      // we need to set the data\n      future.data = curr.data;\n      future._resolvedData = curr._resolvedData;\n    } // If we have a component, we need to go through an outlet.\n\n\n    if (future.component) {\n      getChildRouteGuards(futureNode, currNode, context ? context.children : null, futurePath, checks); // if we have a componentless route, we recurse but keep the same outlet map.\n    } else {\n      getChildRouteGuards(futureNode, currNode, parentContexts, futurePath, checks);\n    }\n\n    if (shouldRun && context && context.outlet && context.outlet.isActivated) {\n      checks.canDeactivateChecks.push(new CanDeactivate(context.outlet.component, curr));\n    }\n  } else {\n    if (curr) {\n      deactivateRouteAndItsChildren(currNode, context, checks);\n    }\n\n    checks.canActivateChecks.push(new CanActivate(futurePath)); // If we have a component, we need to go through an outlet.\n\n    if (future.component) {\n      getChildRouteGuards(futureNode, null, context ? context.children : null, futurePath, checks); // if we have a componentless route, we recurse but keep the same outlet map.\n    } else {\n      getChildRouteGuards(futureNode, null, parentContexts, futurePath, checks);\n    }\n  }\n\n  return checks;\n}\n\nfunction shouldRunGuardsAndResolvers(curr, future, mode) {\n  if (typeof mode === 'function') {\n    return mode(curr, future);\n  }\n\n  switch (mode) {\n    case 'pathParamsChange':\n      return !equalPath(curr.url, future.url);\n\n    case 'pathParamsOrQueryParamsChange':\n      return !equalPath(curr.url, future.url) || !shallowEqual(curr.queryParams, future.queryParams);\n\n    case 'always':\n      return true;\n\n    case 'paramsOrQueryParamsChange':\n      return !equalParamsAndUrlSegments(curr, future) || !shallowEqual(curr.queryParams, future.queryParams);\n\n    case 'paramsChange':\n    default:\n      return !equalParamsAndUrlSegments(curr, future);\n  }\n}\n\nfunction deactivateRouteAndItsChildren(route, context, checks) {\n  const children = nodeChildrenAsMap(route);\n  const r = route.value;\n  forEach(children, (node, childName) => {\n    if (!r.component) {\n      deactivateRouteAndItsChildren(node, context, checks);\n    } else if (context) {\n      deactivateRouteAndItsChildren(node, context.children.getContext(childName), checks);\n    } else {\n      deactivateRouteAndItsChildren(node, null, checks);\n    }\n  });\n\n  if (!r.component) {\n    checks.canDeactivateChecks.push(new CanDeactivate(null, r));\n  } else if (context && context.outlet && context.outlet.isActivated) {\n    checks.canDeactivateChecks.push(new CanDeactivate(context.outlet.component, r));\n  } else {\n    checks.canDeactivateChecks.push(new CanDeactivate(null, r));\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction checkGuards(moduleInjector, forwardEvent) {\n  return mergeMap(t => {\n    const {\n      targetSnapshot,\n      currentSnapshot,\n      guards: {\n        canActivateChecks,\n        canDeactivateChecks\n      }\n    } = t;\n\n    if (canDeactivateChecks.length === 0 && canActivateChecks.length === 0) {\n      return of(Object.assign(Object.assign({}, t), {\n        guardsResult: true\n      }));\n    }\n\n    return runCanDeactivateChecks(canDeactivateChecks, targetSnapshot, currentSnapshot, moduleInjector).pipe(mergeMap(canDeactivate => {\n      return canDeactivate && isBoolean(canDeactivate) ? runCanActivateChecks(targetSnapshot, canActivateChecks, moduleInjector, forwardEvent) : of(canDeactivate);\n    }), map(guardsResult => Object.assign(Object.assign({}, t), {\n      guardsResult\n    })));\n  });\n}\n\nfunction runCanDeactivateChecks(checks, futureRSS, currRSS, moduleInjector) {\n  return from(checks).pipe(mergeMap(check => runCanDeactivate(check.component, check.route, currRSS, futureRSS, moduleInjector)), first(result => {\n    return result !== true;\n  }, true));\n}\n\nfunction runCanActivateChecks(futureSnapshot, checks, moduleInjector, forwardEvent) {\n  return from(checks).pipe(concatMap(check => {\n    return concat(fireChildActivationStart(check.route.parent, forwardEvent), fireActivationStart(check.route, forwardEvent), runCanActivateChild(futureSnapshot, check.path, moduleInjector), runCanActivate(futureSnapshot, check.route, moduleInjector));\n  }), first(result => {\n    return result !== true;\n  }, true));\n}\n/**\n * This should fire off `ActivationStart` events for each route being activated at this\n * level.\n * In other words, if you're activating `a` and `b` below, `path` will contain the\n * `ActivatedRouteSnapshot`s for both and we will fire `ActivationStart` for both. Always\n * return\n * `true` so checks continue to run.\n */\n\n\nfunction fireActivationStart(snapshot, forwardEvent) {\n  if (snapshot !== null && forwardEvent) {\n    forwardEvent(new ActivationStart(snapshot));\n  }\n\n  return of(true);\n}\n/**\n * This should fire off `ChildActivationStart` events for each route being activated at this\n * level.\n * In other words, if you're activating `a` and `b` below, `path` will contain the\n * `ActivatedRouteSnapshot`s for both and we will fire `ChildActivationStart` for both. Always\n * return\n * `true` so checks continue to run.\n */\n\n\nfunction fireChildActivationStart(snapshot, forwardEvent) {\n  if (snapshot !== null && forwardEvent) {\n    forwardEvent(new ChildActivationStart(snapshot));\n  }\n\n  return of(true);\n}\n\nfunction runCanActivate(futureRSS, futureARS, moduleInjector) {\n  const canActivate = futureARS.routeConfig ? futureARS.routeConfig.canActivate : null;\n  if (!canActivate || canActivate.length === 0) return of(true);\n  const canActivateObservables = canActivate.map(c => {\n    return defer(() => {\n      const guard = getToken(c, futureARS, moduleInjector);\n      let observable;\n\n      if (isCanActivate(guard)) {\n        observable = wrapIntoObservable(guard.canActivate(futureARS, futureRSS));\n      } else if (isFunction(guard)) {\n        observable = wrapIntoObservable(guard(futureARS, futureRSS));\n      } else {\n        throw new Error('Invalid CanActivate guard');\n      }\n\n      return observable.pipe(first());\n    });\n  });\n  return of(canActivateObservables).pipe(prioritizedGuardValue());\n}\n\nfunction runCanActivateChild(futureRSS, path, moduleInjector) {\n  const futureARS = path[path.length - 1];\n  const canActivateChildGuards = path.slice(0, path.length - 1).reverse().map(p => getCanActivateChild(p)).filter(_ => _ !== null);\n  const canActivateChildGuardsMapped = canActivateChildGuards.map(d => {\n    return defer(() => {\n      const guardsMapped = d.guards.map(c => {\n        const guard = getToken(c, d.node, moduleInjector);\n        let observable;\n\n        if (isCanActivateChild(guard)) {\n          observable = wrapIntoObservable(guard.canActivateChild(futureARS, futureRSS));\n        } else if (isFunction(guard)) {\n          observable = wrapIntoObservable(guard(futureARS, futureRSS));\n        } else {\n          throw new Error('Invalid CanActivateChild guard');\n        }\n\n        return observable.pipe(first());\n      });\n      return of(guardsMapped).pipe(prioritizedGuardValue());\n    });\n  });\n  return of(canActivateChildGuardsMapped).pipe(prioritizedGuardValue());\n}\n\nfunction runCanDeactivate(component, currARS, currRSS, futureRSS, moduleInjector) {\n  const canDeactivate = currARS && currARS.routeConfig ? currARS.routeConfig.canDeactivate : null;\n  if (!canDeactivate || canDeactivate.length === 0) return of(true);\n  const canDeactivateObservables = canDeactivate.map(c => {\n    const guard = getToken(c, currARS, moduleInjector);\n    let observable;\n\n    if (isCanDeactivate(guard)) {\n      observable = wrapIntoObservable(guard.canDeactivate(component, currARS, currRSS, futureRSS));\n    } else if (isFunction(guard)) {\n      observable = wrapIntoObservable(guard(component, currARS, currRSS, futureRSS));\n    } else {\n      throw new Error('Invalid CanDeactivate guard');\n    }\n\n    return observable.pipe(first());\n  });\n  return of(canDeactivateObservables).pipe(prioritizedGuardValue());\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass NoMatch {}\n\nfunction newObservableError(e) {\n  // TODO(atscott): This pattern is used throughout the router code and can be `throwError` instead.\n  return new Observable(obs => obs.error(e));\n}\n\nfunction recognize$1(rootComponentType, config, urlTree, url, paramsInheritanceStrategy = 'emptyOnly', relativeLinkResolution = 'legacy') {\n  try {\n    const result = new Recognizer(rootComponentType, config, urlTree, url, paramsInheritanceStrategy, relativeLinkResolution).recognize();\n\n    if (result === null) {\n      return newObservableError(new NoMatch());\n    } else {\n      return of(result);\n    }\n  } catch (e) {\n    // Catch the potential error from recognize due to duplicate outlet matches and return as an\n    // `Observable` error instead.\n    return newObservableError(e);\n  }\n}\n\nclass Recognizer {\n  constructor(rootComponentType, config, urlTree, url, paramsInheritanceStrategy, relativeLinkResolution) {\n    this.rootComponentType = rootComponentType;\n    this.config = config;\n    this.urlTree = urlTree;\n    this.url = url;\n    this.paramsInheritanceStrategy = paramsInheritanceStrategy;\n    this.relativeLinkResolution = relativeLinkResolution;\n  }\n\n  recognize() {\n    const rootSegmentGroup = split(this.urlTree.root, [], [], this.config.filter(c => c.redirectTo === undefined), this.relativeLinkResolution).segmentGroup;\n    const children = this.processSegmentGroup(this.config, rootSegmentGroup, PRIMARY_OUTLET);\n\n    if (children === null) {\n      return null;\n    } // Use Object.freeze to prevent readers of the Router state from modifying it outside of a\n    // navigation, resulting in the router being out of sync with the browser.\n\n\n    const root = new ActivatedRouteSnapshot([], Object.freeze({}), Object.freeze(Object.assign({}, this.urlTree.queryParams)), this.urlTree.fragment, {}, PRIMARY_OUTLET, this.rootComponentType, null, this.urlTree.root, -1, {});\n    const rootNode = new TreeNode(root, children);\n    const routeState = new RouterStateSnapshot(this.url, rootNode);\n    this.inheritParamsAndData(routeState._root);\n    return routeState;\n  }\n\n  inheritParamsAndData(routeNode) {\n    const route = routeNode.value;\n    const i = inheritedParamsDataResolve(route, this.paramsInheritanceStrategy);\n    route.params = Object.freeze(i.params);\n    route.data = Object.freeze(i.data);\n    routeNode.children.forEach(n => this.inheritParamsAndData(n));\n  }\n\n  processSegmentGroup(config, segmentGroup, outlet) {\n    if (segmentGroup.segments.length === 0 && segmentGroup.hasChildren()) {\n      return this.processChildren(config, segmentGroup);\n    }\n\n    return this.processSegment(config, segmentGroup, segmentGroup.segments, outlet);\n  }\n  /**\n   * Matches every child outlet in the `segmentGroup` to a `Route` in the config. Returns `null` if\n   * we cannot find a match for _any_ of the children.\n   *\n   * @param config - The `Routes` to match against\n   * @param segmentGroup - The `UrlSegmentGroup` whose children need to be matched against the\n   *     config.\n   */\n\n\n  processChildren(config, segmentGroup) {\n    const children = [];\n\n    for (const childOutlet of Object.keys(segmentGroup.children)) {\n      const child = segmentGroup.children[childOutlet]; // Sort the config so that routes with outlets that match the one being activated appear\n      // first, followed by routes for other outlets, which might match if they have an empty path.\n\n      const sortedConfig = sortByMatchingOutlets(config, childOutlet);\n      const outletChildren = this.processSegmentGroup(sortedConfig, child, childOutlet);\n\n      if (outletChildren === null) {\n        // Configs must match all segment children so because we did not find a match for this\n        // outlet, return `null`.\n        return null;\n      }\n\n      children.push(...outletChildren);\n    } // Because we may have matched two outlets to the same empty path segment, we can have multiple\n    // activated results for the same outlet. We should merge the children of these results so the\n    // final return value is only one `TreeNode` per outlet.\n\n\n    const mergedChildren = mergeEmptyPathMatches(children);\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // This should really never happen - we are only taking the first match for each outlet and\n      // merge the empty path matches.\n      checkOutletNameUniqueness(mergedChildren);\n    }\n\n    sortActivatedRouteSnapshots(mergedChildren);\n    return mergedChildren;\n  }\n\n  processSegment(config, segmentGroup, segments, outlet) {\n    for (const r of config) {\n      const children = this.processSegmentAgainstRoute(r, segmentGroup, segments, outlet);\n\n      if (children !== null) {\n        return children;\n      }\n    }\n\n    if (noLeftoversInUrl(segmentGroup, segments, outlet)) {\n      return [];\n    }\n\n    return null;\n  }\n\n  processSegmentAgainstRoute(route, rawSegment, segments, outlet) {\n    if (route.redirectTo || !isImmediateMatch(route, rawSegment, segments, outlet)) return null;\n    let snapshot;\n    let consumedSegments = [];\n    let rawSlicedSegments = [];\n\n    if (route.path === '**') {\n      const params = segments.length > 0 ? last(segments).parameters : {};\n      snapshot = new ActivatedRouteSnapshot(segments, params, Object.freeze(Object.assign({}, this.urlTree.queryParams)), this.urlTree.fragment, getData(route), getOutlet(route), route.component, route, getSourceSegmentGroup(rawSegment), getPathIndexShift(rawSegment) + segments.length, getResolve(route));\n    } else {\n      const result = match(rawSegment, route, segments);\n\n      if (!result.matched) {\n        return null;\n      }\n\n      consumedSegments = result.consumedSegments;\n      rawSlicedSegments = segments.slice(result.lastChild);\n      snapshot = new ActivatedRouteSnapshot(consumedSegments, result.parameters, Object.freeze(Object.assign({}, this.urlTree.queryParams)), this.urlTree.fragment, getData(route), getOutlet(route), route.component, route, getSourceSegmentGroup(rawSegment), getPathIndexShift(rawSegment) + consumedSegments.length, getResolve(route));\n    }\n\n    const childConfig = getChildConfig(route);\n    const {\n      segmentGroup,\n      slicedSegments\n    } = split(rawSegment, consumedSegments, rawSlicedSegments, // Filter out routes with redirectTo because we are trying to create activated route\n    // snapshots and don't handle redirects here. That should have been done in\n    // `applyRedirects`.\n    childConfig.filter(c => c.redirectTo === undefined), this.relativeLinkResolution);\n\n    if (slicedSegments.length === 0 && segmentGroup.hasChildren()) {\n      const children = this.processChildren(childConfig, segmentGroup);\n\n      if (children === null) {\n        return null;\n      }\n\n      return [new TreeNode(snapshot, children)];\n    }\n\n    if (childConfig.length === 0 && slicedSegments.length === 0) {\n      return [new TreeNode(snapshot, [])];\n    }\n\n    const matchedOnOutlet = getOutlet(route) === outlet; // If we matched a config due to empty path match on a different outlet, we need to continue\n    // passing the current outlet for the segment rather than switch to PRIMARY.\n    // Note that we switch to primary when we have a match because outlet configs look like this:\n    // {path: 'a', outlet: 'a', children: [\n    //  {path: 'b', component: B},\n    //  {path: 'c', component: C},\n    // ]}\n    // Notice that the children of the named outlet are configured with the primary outlet\n\n    const children = this.processSegment(childConfig, segmentGroup, slicedSegments, matchedOnOutlet ? PRIMARY_OUTLET : outlet);\n\n    if (children === null) {\n      return null;\n    }\n\n    return [new TreeNode(snapshot, children)];\n  }\n\n}\n\nfunction sortActivatedRouteSnapshots(nodes) {\n  nodes.sort((a, b) => {\n    if (a.value.outlet === PRIMARY_OUTLET) return -1;\n    if (b.value.outlet === PRIMARY_OUTLET) return 1;\n    return a.value.outlet.localeCompare(b.value.outlet);\n  });\n}\n\nfunction getChildConfig(route) {\n  if (route.children) {\n    return route.children;\n  }\n\n  if (route.loadChildren) {\n    return route._loadedConfig.routes;\n  }\n\n  return [];\n}\n\nfunction hasEmptyPathConfig(node) {\n  const config = node.value.routeConfig;\n  return config && config.path === '' && config.redirectTo === undefined;\n}\n/**\n * Finds `TreeNode`s with matching empty path route configs and merges them into `TreeNode` with the\n * children from each duplicate. This is necessary because different outlets can match a single\n * empty path route config and the results need to then be merged.\n */\n\n\nfunction mergeEmptyPathMatches(nodes) {\n  const result = []; // The set of nodes which contain children that were merged from two duplicate empty path nodes.\n\n  const mergedNodes = new Set();\n\n  for (const node of nodes) {\n    if (!hasEmptyPathConfig(node)) {\n      result.push(node);\n      continue;\n    }\n\n    const duplicateEmptyPathNode = result.find(resultNode => node.value.routeConfig === resultNode.value.routeConfig);\n\n    if (duplicateEmptyPathNode !== undefined) {\n      duplicateEmptyPathNode.children.push(...node.children);\n      mergedNodes.add(duplicateEmptyPathNode);\n    } else {\n      result.push(node);\n    }\n  } // For each node which has children from multiple sources, we need to recompute a new `TreeNode`\n  // by also merging those children. This is necessary when there are multiple empty path configs in\n  // a row. Put another way: whenever we combine children of two nodes, we need to also check if any\n  // of those children can be combined into a single node as well.\n\n\n  for (const mergedNode of mergedNodes) {\n    const mergedChildren = mergeEmptyPathMatches(mergedNode.children);\n    result.push(new TreeNode(mergedNode.value, mergedChildren));\n  }\n\n  return result.filter(n => !mergedNodes.has(n));\n}\n\nfunction checkOutletNameUniqueness(nodes) {\n  const names = {};\n  nodes.forEach(n => {\n    const routeWithSameOutletName = names[n.value.outlet];\n\n    if (routeWithSameOutletName) {\n      const p = routeWithSameOutletName.url.map(s => s.toString()).join('/');\n      const c = n.value.url.map(s => s.toString()).join('/');\n      throw new Error(`Two segments cannot have the same outlet name: '${p}' and '${c}'.`);\n    }\n\n    names[n.value.outlet] = n.value;\n  });\n}\n\nfunction getSourceSegmentGroup(segmentGroup) {\n  let s = segmentGroup;\n\n  while (s._sourceSegment) {\n    s = s._sourceSegment;\n  }\n\n  return s;\n}\n\nfunction getPathIndexShift(segmentGroup) {\n  let s = segmentGroup;\n  let res = s._segmentIndexShift ? s._segmentIndexShift : 0;\n\n  while (s._sourceSegment) {\n    s = s._sourceSegment;\n    res += s._segmentIndexShift ? s._segmentIndexShift : 0;\n  }\n\n  return res - 1;\n}\n\nfunction getData(route) {\n  return route.data || {};\n}\n\nfunction getResolve(route) {\n  return route.resolve || {};\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction recognize(rootComponentType, config, serializer, paramsInheritanceStrategy, relativeLinkResolution) {\n  return mergeMap(t => recognize$1(rootComponentType, config, t.urlAfterRedirects, serializer(t.urlAfterRedirects), paramsInheritanceStrategy, relativeLinkResolution).pipe(map(targetSnapshot => Object.assign(Object.assign({}, t), {\n    targetSnapshot\n  }))));\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction resolveData(paramsInheritanceStrategy, moduleInjector) {\n  return mergeMap(t => {\n    const {\n      targetSnapshot,\n      guards: {\n        canActivateChecks\n      }\n    } = t;\n\n    if (!canActivateChecks.length) {\n      return of(t);\n    }\n\n    let canActivateChecksResolved = 0;\n    return from(canActivateChecks).pipe(concatMap(check => runResolve(check.route, targetSnapshot, paramsInheritanceStrategy, moduleInjector)), tap(() => canActivateChecksResolved++), takeLast(1), mergeMap(_ => canActivateChecksResolved === canActivateChecks.length ? of(t) : EMPTY));\n  });\n}\n\nfunction runResolve(futureARS, futureRSS, paramsInheritanceStrategy, moduleInjector) {\n  const resolve = futureARS._resolve;\n  return resolveNode(resolve, futureARS, futureRSS, moduleInjector).pipe(map(resolvedData => {\n    futureARS._resolvedData = resolvedData;\n    futureARS.data = Object.assign(Object.assign({}, futureARS.data), inheritedParamsDataResolve(futureARS, paramsInheritanceStrategy).resolve);\n    return null;\n  }));\n}\n\nfunction resolveNode(resolve, futureARS, futureRSS, moduleInjector) {\n  const keys = Object.keys(resolve);\n\n  if (keys.length === 0) {\n    return of({});\n  }\n\n  const data = {};\n  return from(keys).pipe(mergeMap(key => getResolver(resolve[key], futureARS, futureRSS, moduleInjector).pipe(tap(value => {\n    data[key] = value;\n  }))), takeLast(1), mergeMap(() => {\n    // Ensure all resolvers returned values, otherwise don't emit any \"next\" and just complete\n    // the chain which will cancel navigation\n    if (Object.keys(data).length === keys.length) {\n      return of(data);\n    }\n\n    return EMPTY;\n  }));\n}\n\nfunction getResolver(injectionToken, futureARS, futureRSS, moduleInjector) {\n  const resolver = getToken(injectionToken, futureARS, moduleInjector);\n  return resolver.resolve ? wrapIntoObservable(resolver.resolve(futureARS, futureRSS)) : wrapIntoObservable(resolver(futureARS, futureRSS));\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Perform a side effect through a switchMap for every emission on the source Observable,\n * but return an Observable that is identical to the source. It's essentially the same as\n * the `tap` operator, but if the side effectful `next` function returns an ObservableInput,\n * it will wait before continuing with the original value.\n */\n\n\nfunction switchTap(next) {\n  return switchMap(v => {\n    const nextResult = next(v);\n\n    if (nextResult) {\n      return from(nextResult).pipe(map(() => v));\n    }\n\n    return of(v);\n  });\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @description\n *\n * Provides a way to customize when activated routes get reused.\n *\n * @publicApi\n */\n\n\nclass RouteReuseStrategy {}\n/**\n * @description\n *\n * This base route reuse strategy only reuses routes when the matched router configs are\n * identical. This prevents components from being destroyed and recreated\n * when just the fragment or query parameters change\n * (that is, the existing component is _reused_).\n *\n * This strategy does not store any routes for later reuse.\n *\n * Angular uses this strategy by default.\n *\n *\n * It can be used as a base class for custom route reuse strategies, i.e. you can create your own\n * class that extends the `BaseRouteReuseStrategy` one.\n * @publicApi\n */\n\n\nclass BaseRouteReuseStrategy {\n  /**\n   * Whether the given route should detach for later reuse.\n   * Always returns false for `BaseRouteReuseStrategy`.\n   * */\n  shouldDetach(route) {\n    return false;\n  }\n  /**\n   * A no-op; the route is never stored since this strategy never detaches routes for later re-use.\n   */\n\n\n  store(route, detachedTree) {}\n  /** Returns `false`, meaning the route (and its subtree) is never reattached */\n\n\n  shouldAttach(route) {\n    return false;\n  }\n  /** Returns `null` because this strategy does not store routes for later re-use. */\n\n\n  retrieve(route) {\n    return null;\n  }\n  /**\n   * Determines if a route should be reused.\n   * This strategy returns `true` when the future route config and current route config are\n   * identical.\n   */\n\n\n  shouldReuseRoute(future, curr) {\n    return future.routeConfig === curr.routeConfig;\n  }\n\n}\n\nclass DefaultRouteReuseStrategy extends BaseRouteReuseStrategy {}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The [DI token](guide/glossary/#di-token) for a router configuration.\n *\n * `ROUTES` is a low level API for router configuration via dependency injection.\n *\n * We recommend that in almost all cases to use higher level APIs such as `RouterModule.forRoot()`,\n * `RouterModule.forChild()`, `provideRoutes`, or `Router.resetConfig()`.\n *\n * @publicApi\n */\n\n\nconst ROUTES = /*#__PURE__*/new InjectionToken('ROUTES');\n\nclass RouterConfigLoader {\n  constructor(injector, compiler, onLoadStartListener, onLoadEndListener) {\n    this.injector = injector;\n    this.compiler = compiler;\n    this.onLoadStartListener = onLoadStartListener;\n    this.onLoadEndListener = onLoadEndListener;\n  }\n\n  load(parentInjector, route) {\n    if (route._loader$) {\n      return route._loader$;\n    }\n\n    if (this.onLoadStartListener) {\n      this.onLoadStartListener(route);\n    }\n\n    const moduleFactory$ = this.loadModuleFactory(route.loadChildren);\n    const loadRunner = moduleFactory$.pipe(map(factory => {\n      if (this.onLoadEndListener) {\n        this.onLoadEndListener(route);\n      }\n\n      const module = factory.create(parentInjector); // When loading a module that doesn't provide `RouterModule.forChild()` preloader\n      // will get stuck in an infinite loop. The child module's Injector will look to\n      // its parent `Injector` when it doesn't find any ROUTES so it will return routes\n      // for it's parent module instead.\n\n      return new LoadedRouterConfig(flatten(module.injector.get(ROUTES, undefined, InjectFlags.Self | InjectFlags.Optional)).map(standardizeConfig), module);\n    }), catchError(err => {\n      route._loader$ = undefined;\n      throw err;\n    })); // Use custom ConnectableObservable as share in runners pipe increasing the bundle size too much\n\n    route._loader$ = new ConnectableObservable(loadRunner, () => new Subject()).pipe(refCount());\n    return route._loader$;\n  }\n\n  loadModuleFactory(loadChildren) {\n    return wrapIntoObservable(loadChildren()).pipe(mergeMap(t => {\n      if (t instanceof NgModuleFactory) {\n        return of(t);\n      } else {\n        return from(this.compiler.compileModuleAsync(t));\n      }\n    }));\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @description\n *\n * Provides a way to migrate AngularJS applications to Angular.\n *\n * @publicApi\n */\n\n\nclass UrlHandlingStrategy {}\n/**\n * @publicApi\n */\n\n\nclass DefaultUrlHandlingStrategy {\n  shouldProcessUrl(url) {\n    return true;\n  }\n\n  extract(url) {\n    return url;\n  }\n\n  merge(newUrlPart, wholeUrl) {\n    return newUrlPart;\n  }\n\n}\n\nfunction defaultErrorHandler(error) {\n  throw error;\n}\n\nfunction defaultMalformedUriErrorHandler(error, urlSerializer, url) {\n  return urlSerializer.parse('/');\n}\n/**\n * @internal\n */\n\n\nfunction defaultRouterHook(snapshot, runExtras) {\n  return of(null);\n}\n/**\n * The equivalent `IsActiveMatchOptions` options for `Router.isActive` is called with `true`\n * (exact = true).\n */\n\n\nconst exactMatchOptions = {\n  paths: 'exact',\n  fragment: 'ignored',\n  matrixParams: 'ignored',\n  queryParams: 'exact'\n};\n/**\n * The equivalent `IsActiveMatchOptions` options for `Router.isActive` is called with `false`\n * (exact = false).\n */\n\nconst subsetMatchOptions = {\n  paths: 'subset',\n  fragment: 'ignored',\n  matrixParams: 'ignored',\n  queryParams: 'subset'\n};\n/**\n * @description\n *\n * A service that provides navigation among views and URL manipulation capabilities.\n *\n * @see `Route`.\n * @see [Routing and Navigation Guide](guide/router).\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\n\nlet Router = /*#__PURE__*/(() => {\n  class Router {\n    /**\n     * Creates the router service.\n     */\n    // TODO: vsavkin make internal after the final is out.\n    constructor(rootComponentType, urlSerializer, rootContexts, location, injector, compiler, config) {\n      this.rootComponentType = rootComponentType;\n      this.urlSerializer = urlSerializer;\n      this.rootContexts = rootContexts;\n      this.location = location;\n      this.config = config;\n      this.lastSuccessfulNavigation = null;\n      this.currentNavigation = null;\n      this.disposed = false;\n      this.navigationId = 0;\n      /**\n       * The id of the currently active page in the router.\n       * Updated to the transition's target id on a successful navigation.\n       *\n       * This is used to track what page the router last activated. When an attempted navigation fails,\n       * the router can then use this to compute how to restore the state back to the previously active\n       * page.\n       */\n\n      this.currentPageId = 0;\n      this.isNgZoneEnabled = false;\n      /**\n       * An event stream for routing events in this NgModule.\n       */\n\n      this.events = new Subject();\n      /**\n       * A handler for navigation errors in this NgModule.\n       */\n\n      this.errorHandler = defaultErrorHandler;\n      /**\n       * A handler for errors thrown by `Router.parseUrl(url)`\n       * when `url` contains an invalid character.\n       * The most common case is a `%` sign\n       * that's not encoded and is not part of a percent encoded sequence.\n       */\n\n      this.malformedUriErrorHandler = defaultMalformedUriErrorHandler;\n      /**\n       * True if at least one navigation event has occurred,\n       * false otherwise.\n       */\n\n      this.navigated = false;\n      this.lastSuccessfulId = -1;\n      /**\n       * Hooks that enable you to pause navigation,\n       * either before or after the preactivation phase.\n       * Used by `RouterModule`.\n       *\n       * @internal\n       */\n\n      this.hooks = {\n        beforePreactivation: defaultRouterHook,\n        afterPreactivation: defaultRouterHook\n      };\n      /**\n       * A strategy for extracting and merging URLs.\n       * Used for AngularJS to Angular migrations.\n       */\n\n      this.urlHandlingStrategy = new DefaultUrlHandlingStrategy();\n      /**\n       * A strategy for re-using routes.\n       */\n\n      this.routeReuseStrategy = new DefaultRouteReuseStrategy();\n      /**\n       * How to handle a navigation request to the current URL. One of:\n       *\n       * - `'ignore'` :  The router ignores the request.\n       * - `'reload'` : The router reloads the URL. Use to implement a \"refresh\" feature.\n       *\n       * Note that this only configures whether the Route reprocesses the URL and triggers related\n       * action and events like redirects, guards, and resolvers. By default, the router re-uses a\n       * component instance when it re-navigates to the same component type without visiting a different\n       * component first. This behavior is configured by the `RouteReuseStrategy`. In order to reload\n       * routed components on same url navigation, you need to set `onSameUrlNavigation` to `'reload'`\n       * _and_ provide a `RouteReuseStrategy` which returns `false` for `shouldReuseRoute`.\n       */\n\n      this.onSameUrlNavigation = 'ignore';\n      /**\n       * How to merge parameters, data, and resolved data from parent to child\n       * routes. One of:\n       *\n       * - `'emptyOnly'` : Inherit parent parameters, data, and resolved data\n       * for path-less or component-less routes.\n       * - `'always'` : Inherit parent parameters, data, and resolved data\n       * for all child routes.\n       */\n\n      this.paramsInheritanceStrategy = 'emptyOnly';\n      /**\n       * Determines when the router updates the browser URL.\n       * By default (`\"deferred\"`), updates the browser URL after navigation has finished.\n       * Set to `'eager'` to update the browser URL at the beginning of navigation.\n       * You can choose to update early so that, if navigation fails,\n       * you can show an error message with the URL that failed.\n       */\n\n      this.urlUpdateStrategy = 'deferred';\n      /**\n       * Enables a bug fix that corrects relative link resolution in components with empty paths.\n       * @see `RouterModule`\n       */\n\n      this.relativeLinkResolution = 'corrected';\n      /**\n       * Configures how the Router attempts to restore state when a navigation is cancelled.\n       *\n       * 'replace' - Always uses `location.replaceState` to set the browser state to the state of the\n       * router before the navigation started. This means that if the URL of the browser is updated\n       * _before_ the navigation is canceled, the Router will simply replace the item in history rather\n       * than trying to restore to the previous location in the session history. This happens most\n       * frequently with `urlUpdateStrategy: 'eager'` and navigations with the browser back/forward\n       * buttons.\n       *\n       * 'computed' - Will attempt to return to the same index in the session history that corresponds\n       * to the Angular route when the navigation gets cancelled. For example, if the browser back\n       * button is clicked and the navigation is cancelled, the Router will trigger a forward navigation\n       * and vice versa.\n       *\n       * Note: the 'computed' option is incompatible with any `UrlHandlingStrategy` which only\n       * handles a portion of the URL because the history restoration navigates to the previous place in\n       * the browser history rather than simply resetting a portion of the URL.\n       *\n       * The default value is `replace`.\n       *\n       */\n\n      this.canceledNavigationResolution = 'replace';\n\n      const onLoadStart = r => this.triggerEvent(new RouteConfigLoadStart(r));\n\n      const onLoadEnd = r => this.triggerEvent(new RouteConfigLoadEnd(r));\n\n      this.ngModule = injector.get(NgModuleRef);\n      this.console = injector.get(ɵConsole);\n      const ngZone = injector.get(NgZone);\n      this.isNgZoneEnabled = ngZone instanceof NgZone && NgZone.isInAngularZone();\n      this.resetConfig(config);\n      this.currentUrlTree = createEmptyUrlTree();\n      this.rawUrlTree = this.currentUrlTree;\n      this.browserUrlTree = this.currentUrlTree;\n      this.configLoader = new RouterConfigLoader(injector, compiler, onLoadStart, onLoadEnd);\n      this.routerState = createEmptyState(this.currentUrlTree, this.rootComponentType);\n      this.transitions = new BehaviorSubject({\n        id: 0,\n        targetPageId: 0,\n        currentUrlTree: this.currentUrlTree,\n        currentRawUrl: this.currentUrlTree,\n        extractedUrl: this.urlHandlingStrategy.extract(this.currentUrlTree),\n        urlAfterRedirects: this.urlHandlingStrategy.extract(this.currentUrlTree),\n        rawUrl: this.currentUrlTree,\n        extras: {},\n        resolve: null,\n        reject: null,\n        promise: Promise.resolve(true),\n        source: 'imperative',\n        restoredState: null,\n        currentSnapshot: this.routerState.snapshot,\n        targetSnapshot: null,\n        currentRouterState: this.routerState,\n        targetRouterState: null,\n        guards: {\n          canActivateChecks: [],\n          canDeactivateChecks: []\n        },\n        guardsResult: null\n      });\n      this.navigations = this.setupNavigations(this.transitions);\n      this.processNavigations();\n    }\n    /**\n     * The ɵrouterPageId of whatever page is currently active in the browser history. This is\n     * important for computing the target page id for new navigations because we need to ensure each\n     * page id in the browser history is 1 more than the previous entry.\n     */\n\n\n    get browserPageId() {\n      var _a;\n\n      return (_a = this.location.getState()) === null || _a === void 0 ? void 0 : _a.ɵrouterPageId;\n    }\n\n    setupNavigations(transitions) {\n      const eventsSubject = this.events;\n      return transitions.pipe(filter(t => t.id !== 0), // Extract URL\n      map(t => Object.assign(Object.assign({}, t), {\n        extractedUrl: this.urlHandlingStrategy.extract(t.rawUrl)\n      })), // Using switchMap so we cancel executing navigations when a new one comes in\n      switchMap(t => {\n        let completed = false;\n        let errored = false;\n        return of(t).pipe( // Store the Navigation object\n        tap(t => {\n          this.currentNavigation = {\n            id: t.id,\n            initialUrl: t.currentRawUrl,\n            extractedUrl: t.extractedUrl,\n            trigger: t.source,\n            extras: t.extras,\n            previousNavigation: this.lastSuccessfulNavigation ? Object.assign(Object.assign({}, this.lastSuccessfulNavigation), {\n              previousNavigation: null\n            }) : null\n          };\n        }), switchMap(t => {\n          const browserUrlTree = this.browserUrlTree.toString();\n          const urlTransition = !this.navigated || t.extractedUrl.toString() !== browserUrlTree || // Navigations which succeed or ones which fail and are cleaned up\n          // correctly should result in `browserUrlTree` and `currentUrlTree`\n          // matching. If this is not the case, assume something went wrong and try\n          // processing the URL again.\n          browserUrlTree !== this.currentUrlTree.toString();\n          const processCurrentUrl = (this.onSameUrlNavigation === 'reload' ? true : urlTransition) && this.urlHandlingStrategy.shouldProcessUrl(t.rawUrl);\n\n          if (processCurrentUrl) {\n            // If the source of the navigation is from a browser event, the URL is\n            // already updated. We already need to sync the internal state.\n            if (isBrowserTriggeredNavigation(t.source)) {\n              this.browserUrlTree = t.extractedUrl;\n            }\n\n            return of(t).pipe( // Fire NavigationStart event\n            switchMap(t => {\n              const transition = this.transitions.getValue();\n              eventsSubject.next(new NavigationStart(t.id, this.serializeUrl(t.extractedUrl), t.source, t.restoredState));\n\n              if (transition !== this.transitions.getValue()) {\n                return EMPTY;\n              } // This delay is required to match old behavior that forced\n              // navigation to always be async\n\n\n              return Promise.resolve(t);\n            }), // ApplyRedirects\n            applyRedirects(this.ngModule.injector, this.configLoader, this.urlSerializer, this.config), // Update the currentNavigation\n            // `urlAfterRedirects` is guaranteed to be set after this point\n            tap(t => {\n              this.currentNavigation = Object.assign(Object.assign({}, this.currentNavigation), {\n                finalUrl: t.urlAfterRedirects\n              });\n            }), // Recognize\n            recognize(this.rootComponentType, this.config, url => this.serializeUrl(url), this.paramsInheritanceStrategy, this.relativeLinkResolution), // Update URL if in `eager` update mode\n            tap(t => {\n              if (this.urlUpdateStrategy === 'eager') {\n                if (!t.extras.skipLocationChange) {\n                  const rawUrl = this.urlHandlingStrategy.merge(t.urlAfterRedirects, t.rawUrl);\n                  this.setBrowserUrl(rawUrl, t);\n                }\n\n                this.browserUrlTree = t.urlAfterRedirects;\n              } // Fire RoutesRecognized\n\n\n              const routesRecognized = new RoutesRecognized(t.id, this.serializeUrl(t.extractedUrl), this.serializeUrl(t.urlAfterRedirects), t.targetSnapshot);\n              eventsSubject.next(routesRecognized);\n            }));\n          } else {\n            const processPreviousUrl = urlTransition && this.rawUrlTree && this.urlHandlingStrategy.shouldProcessUrl(this.rawUrlTree);\n            /* When the current URL shouldn't be processed, but the previous one was,\n             * we handle this \"error condition\" by navigating to the previously\n             * successful URL, but leaving the URL intact.*/\n\n            if (processPreviousUrl) {\n              const {\n                id,\n                extractedUrl,\n                source,\n                restoredState,\n                extras\n              } = t;\n              const navStart = new NavigationStart(id, this.serializeUrl(extractedUrl), source, restoredState);\n              eventsSubject.next(navStart);\n              const targetSnapshot = createEmptyState(extractedUrl, this.rootComponentType).snapshot;\n              return of(Object.assign(Object.assign({}, t), {\n                targetSnapshot,\n                urlAfterRedirects: extractedUrl,\n                extras: Object.assign(Object.assign({}, extras), {\n                  skipLocationChange: false,\n                  replaceUrl: false\n                })\n              }));\n            } else {\n              /* When neither the current or previous URL can be processed, do nothing\n               * other than update router's internal reference to the current \"settled\"\n               * URL. This way the next navigation will be coming from the current URL\n               * in the browser.\n               */\n              this.rawUrlTree = t.rawUrl;\n              t.resolve(null);\n              return EMPTY;\n            }\n          }\n        }), // Before Preactivation\n        switchTap(t => {\n          const {\n            targetSnapshot,\n            id: navigationId,\n            extractedUrl: appliedUrlTree,\n            rawUrl: rawUrlTree,\n            extras: {\n              skipLocationChange,\n              replaceUrl\n            }\n          } = t;\n          return this.hooks.beforePreactivation(targetSnapshot, {\n            navigationId,\n            appliedUrlTree,\n            rawUrlTree,\n            skipLocationChange: !!skipLocationChange,\n            replaceUrl: !!replaceUrl\n          });\n        }), // --- GUARDS ---\n        tap(t => {\n          const guardsStart = new GuardsCheckStart(t.id, this.serializeUrl(t.extractedUrl), this.serializeUrl(t.urlAfterRedirects), t.targetSnapshot);\n          this.triggerEvent(guardsStart);\n        }), map(t => Object.assign(Object.assign({}, t), {\n          guards: getAllRouteGuards(t.targetSnapshot, t.currentSnapshot, this.rootContexts)\n        })), checkGuards(this.ngModule.injector, evt => this.triggerEvent(evt)), tap(t => {\n          if (isUrlTree(t.guardsResult)) {\n            const error = navigationCancelingError(`Redirecting to \"${this.serializeUrl(t.guardsResult)}\"`);\n            error.url = t.guardsResult;\n            throw error;\n          }\n\n          const guardsEnd = new GuardsCheckEnd(t.id, this.serializeUrl(t.extractedUrl), this.serializeUrl(t.urlAfterRedirects), t.targetSnapshot, !!t.guardsResult);\n          this.triggerEvent(guardsEnd);\n        }), filter(t => {\n          if (!t.guardsResult) {\n            this.restoreHistory(t);\n            this.cancelNavigationTransition(t, '');\n            return false;\n          }\n\n          return true;\n        }), // --- RESOLVE ---\n        switchTap(t => {\n          if (t.guards.canActivateChecks.length) {\n            return of(t).pipe(tap(t => {\n              const resolveStart = new ResolveStart(t.id, this.serializeUrl(t.extractedUrl), this.serializeUrl(t.urlAfterRedirects), t.targetSnapshot);\n              this.triggerEvent(resolveStart);\n            }), switchMap(t => {\n              let dataResolved = false;\n              return of(t).pipe(resolveData(this.paramsInheritanceStrategy, this.ngModule.injector), tap({\n                next: () => dataResolved = true,\n                complete: () => {\n                  if (!dataResolved) {\n                    this.restoreHistory(t);\n                    this.cancelNavigationTransition(t, `At least one route resolver didn't emit any value.`);\n                  }\n                }\n              }));\n            }), tap(t => {\n              const resolveEnd = new ResolveEnd(t.id, this.serializeUrl(t.extractedUrl), this.serializeUrl(t.urlAfterRedirects), t.targetSnapshot);\n              this.triggerEvent(resolveEnd);\n            }));\n          }\n\n          return undefined;\n        }), // --- AFTER PREACTIVATION ---\n        switchTap(t => {\n          const {\n            targetSnapshot,\n            id: navigationId,\n            extractedUrl: appliedUrlTree,\n            rawUrl: rawUrlTree,\n            extras: {\n              skipLocationChange,\n              replaceUrl\n            }\n          } = t;\n          return this.hooks.afterPreactivation(targetSnapshot, {\n            navigationId,\n            appliedUrlTree,\n            rawUrlTree,\n            skipLocationChange: !!skipLocationChange,\n            replaceUrl: !!replaceUrl\n          });\n        }), map(t => {\n          const targetRouterState = createRouterState(this.routeReuseStrategy, t.targetSnapshot, t.currentRouterState);\n          return Object.assign(Object.assign({}, t), {\n            targetRouterState\n          });\n        }),\n        /* Once here, we are about to activate syncronously. The assumption is this\n           will succeed, and user code may read from the Router service. Therefore\n           before activation, we need to update router properties storing the current\n           URL and the RouterState, as well as updated the browser URL. All this should\n           happen *before* activating. */\n        tap(t => {\n          this.currentUrlTree = t.urlAfterRedirects;\n          this.rawUrlTree = this.urlHandlingStrategy.merge(t.urlAfterRedirects, t.rawUrl);\n          this.routerState = t.targetRouterState;\n\n          if (this.urlUpdateStrategy === 'deferred') {\n            if (!t.extras.skipLocationChange) {\n              this.setBrowserUrl(this.rawUrlTree, t);\n            }\n\n            this.browserUrlTree = t.urlAfterRedirects;\n          }\n        }), activateRoutes(this.rootContexts, this.routeReuseStrategy, evt => this.triggerEvent(evt)), tap({\n          next() {\n            completed = true;\n          },\n\n          complete() {\n            completed = true;\n          }\n\n        }), finalize(() => {\n          var _a;\n          /* When the navigation stream finishes either through error or success, we\n           * set the `completed` or `errored` flag. However, there are some situations\n           * where we could get here without either of those being set. For instance, a\n           * redirect during NavigationStart. Therefore, this is a catch-all to make\n           * sure the NavigationCancel\n           * event is fired when a navigation gets cancelled but not caught by other\n           * means. */\n\n\n          if (!completed && !errored) {\n            const cancelationReason = `Navigation ID ${t.id} is not equal to the current navigation id ${this.navigationId}`;\n            this.cancelNavigationTransition(t, cancelationReason);\n          } // Only clear current navigation if it is still set to the one that\n          // finalized.\n\n\n          if (((_a = this.currentNavigation) === null || _a === void 0 ? void 0 : _a.id) === t.id) {\n            this.currentNavigation = null;\n          }\n        }), catchError(e => {\n          // TODO(atscott): The NavigationTransition `t` used here does not accurately\n          // reflect the current state of the whole transition because some operations\n          // return a new object rather than modifying the one in the outermost\n          // `switchMap`.\n          //  The fix can likely be to:\n          //  1. Rename the outer `t` variable so it's not shadowed all the time and\n          //  confusing\n          //  2. Keep reassigning to the outer variable after each stage to ensure it\n          //  gets updated. Or change the implementations to not return a copy.\n          // Not changed yet because it affects existing code and would need to be\n          // tested more thoroughly.\n          errored = true;\n          /* This error type is issued during Redirect, and is handled as a\n           * cancellation rather than an error. */\n\n          if (isNavigationCancelingError(e)) {\n            const redirecting = isUrlTree(e.url);\n\n            if (!redirecting) {\n              // Set property only if we're not redirecting. If we landed on a page and\n              // redirect to `/` route, the new navigation is going to see the `/`\n              // isn't a change from the default currentUrlTree and won't navigate.\n              // This is only applicable with initial navigation, so setting\n              // `navigated` only when not redirecting resolves this scenario.\n              this.navigated = true;\n              this.restoreHistory(t, true);\n            }\n\n            const navCancel = new NavigationCancel(t.id, this.serializeUrl(t.extractedUrl), e.message);\n            eventsSubject.next(navCancel); // When redirecting, we need to delay resolving the navigation\n            // promise and push it to the redirect navigation\n\n            if (!redirecting) {\n              t.resolve(false);\n            } else {\n              // setTimeout is required so this navigation finishes with\n              // the return EMPTY below. If it isn't allowed to finish\n              // processing, there can be multiple navigations to the same\n              // URL.\n              setTimeout(() => {\n                const mergedTree = this.urlHandlingStrategy.merge(e.url, this.rawUrlTree);\n                const extras = {\n                  skipLocationChange: t.extras.skipLocationChange,\n                  // The URL is already updated at this point if we have 'eager' URL\n                  // updates or if the navigation was triggered by the browser (back\n                  // button, URL bar, etc). We want to replace that item in history if\n                  // the navigation is rejected.\n                  replaceUrl: this.urlUpdateStrategy === 'eager' || isBrowserTriggeredNavigation(t.source)\n                };\n                this.scheduleNavigation(mergedTree, 'imperative', null, extras, {\n                  resolve: t.resolve,\n                  reject: t.reject,\n                  promise: t.promise\n                });\n              }, 0);\n            }\n            /* All other errors should reset to the router's internal URL reference to\n             * the pre-error state. */\n\n          } else {\n            this.restoreHistory(t, true);\n            const navError = new NavigationError(t.id, this.serializeUrl(t.extractedUrl), e);\n            eventsSubject.next(navError);\n\n            try {\n              t.resolve(this.errorHandler(e));\n            } catch (ee) {\n              t.reject(ee);\n            }\n          }\n\n          return EMPTY;\n        })); // TODO(jasonaden): remove cast once g3 is on updated TypeScript\n      }));\n    }\n    /**\n     * @internal\n     * TODO: this should be removed once the constructor of the router made internal\n     */\n\n\n    resetRootComponentType(rootComponentType) {\n      this.rootComponentType = rootComponentType; // TODO: vsavkin router 4.0 should make the root component set to null\n      // this will simplify the lifecycle of the router.\n\n      this.routerState.root.component = this.rootComponentType;\n    }\n\n    setTransition(t) {\n      this.transitions.next(Object.assign(Object.assign({}, this.transitions.value), t));\n    }\n    /**\n     * Sets up the location change listener and performs the initial navigation.\n     */\n\n\n    initialNavigation() {\n      this.setUpLocationChangeListener();\n\n      if (this.navigationId === 0) {\n        this.navigateByUrl(this.location.path(true), {\n          replaceUrl: true\n        });\n      }\n    }\n    /**\n     * Sets up the location change listener. This listener detects navigations triggered from outside\n     * the Router (the browser back/forward buttons, for example) and schedules a corresponding Router\n     * navigation so that the correct events, guards, etc. are triggered.\n     */\n\n\n    setUpLocationChangeListener() {\n      // Don't need to use Zone.wrap any more, because zone.js\n      // already patch onPopState, so location change callback will\n      // run into ngZone\n      if (!this.locationSubscription) {\n        this.locationSubscription = this.location.subscribe(event => {\n          const source = event['type'] === 'popstate' ? 'popstate' : 'hashchange';\n\n          if (source === 'popstate') {\n            // The `setTimeout` was added in #12160 and is likely to support Angular/AngularJS\n            // hybrid apps.\n            setTimeout(() => {\n              var _a;\n\n              const extras = {\n                replaceUrl: true\n              }; // Navigations coming from Angular router have a navigationId state\n              // property. When this exists, restore the state.\n\n              const state = ((_a = event.state) === null || _a === void 0 ? void 0 : _a.navigationId) ? event.state : null;\n\n              if (state) {\n                const stateCopy = Object.assign({}, state);\n                delete stateCopy.navigationId;\n                delete stateCopy.ɵrouterPageId;\n\n                if (Object.keys(stateCopy).length !== 0) {\n                  extras.state = stateCopy;\n                }\n              }\n\n              const urlTree = this.parseUrl(event['url']);\n              this.scheduleNavigation(urlTree, source, state, extras);\n            }, 0);\n          }\n        });\n      }\n    }\n    /** The current URL. */\n\n\n    get url() {\n      return this.serializeUrl(this.currentUrlTree);\n    }\n    /**\n     * Returns the current `Navigation` object when the router is navigating,\n     * and `null` when idle.\n     */\n\n\n    getCurrentNavigation() {\n      return this.currentNavigation;\n    }\n    /** @internal */\n\n\n    triggerEvent(event) {\n      this.events.next(event);\n    }\n    /**\n     * Resets the route configuration used for navigation and generating links.\n     *\n     * @param config The route array for the new configuration.\n     *\n     * @usageNotes\n     *\n     * ```\n     * router.resetConfig([\n     *  { path: 'team/:id', component: TeamCmp, children: [\n     *    { path: 'simple', component: SimpleCmp },\n     *    { path: 'user/:name', component: UserCmp }\n     *  ]}\n     * ]);\n     * ```\n     */\n\n\n    resetConfig(config) {\n      validateConfig(config);\n      this.config = config.map(standardizeConfig);\n      this.navigated = false;\n      this.lastSuccessfulId = -1;\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      this.dispose();\n    }\n    /** Disposes of the router. */\n\n\n    dispose() {\n      this.transitions.complete();\n\n      if (this.locationSubscription) {\n        this.locationSubscription.unsubscribe();\n        this.locationSubscription = undefined;\n      }\n\n      this.disposed = true;\n    }\n    /**\n     * Appends URL segments to the current URL tree to create a new URL tree.\n     *\n     * @param commands An array of URL fragments with which to construct the new URL tree.\n     * If the path is static, can be the literal URL string. For a dynamic path, pass an array of path\n     * segments, followed by the parameters for each segment.\n     * The fragments are applied to the current URL tree or the one provided  in the `relativeTo`\n     * property of the options object, if supplied.\n     * @param navigationExtras Options that control the navigation strategy.\n     * @returns The new URL tree.\n     *\n     * @usageNotes\n     *\n     * ```\n     * // create /team/33/user/11\n     * router.createUrlTree(['/team', 33, 'user', 11]);\n     *\n     * // create /team/33;expand=true/user/11\n     * router.createUrlTree(['/team', 33, {expand: true}, 'user', 11]);\n     *\n     * // you can collapse static segments like this (this works only with the first passed-in value):\n     * router.createUrlTree(['/team/33/user', userId]);\n     *\n     * // If the first segment can contain slashes, and you do not want the router to split it,\n     * // you can do the following:\n     * router.createUrlTree([{segmentPath: '/one/two'}]);\n     *\n     * // create /team/33/(user/11//right:chat)\n     * router.createUrlTree(['/team', 33, {outlets: {primary: 'user/11', right: 'chat'}}]);\n     *\n     * // remove the right secondary node\n     * router.createUrlTree(['/team', 33, {outlets: {primary: 'user/11', right: null}}]);\n     *\n     * // assuming the current url is `/team/33/user/11` and the route points to `user/11`\n     *\n     * // navigate to /team/33/user/11/details\n     * router.createUrlTree(['details'], {relativeTo: route});\n     *\n     * // navigate to /team/33/user/22\n     * router.createUrlTree(['../22'], {relativeTo: route});\n     *\n     * // navigate to /team/44/user/22\n     * router.createUrlTree(['../../team/44/user/22'], {relativeTo: route});\n     *\n     * Note that a value of `null` or `undefined` for `relativeTo` indicates that the\n     * tree should be created relative to the root.\n     * ```\n     */\n\n\n    createUrlTree(commands, navigationExtras = {}) {\n      const {\n        relativeTo,\n        queryParams,\n        fragment,\n        queryParamsHandling,\n        preserveFragment\n      } = navigationExtras;\n      const a = relativeTo || this.routerState.root;\n      const f = preserveFragment ? this.currentUrlTree.fragment : fragment;\n      let q = null;\n\n      switch (queryParamsHandling) {\n        case 'merge':\n          q = Object.assign(Object.assign({}, this.currentUrlTree.queryParams), queryParams);\n          break;\n\n        case 'preserve':\n          q = this.currentUrlTree.queryParams;\n          break;\n\n        default:\n          q = queryParams || null;\n      }\n\n      if (q !== null) {\n        q = this.removeEmptyProps(q);\n      }\n\n      return createUrlTree(a, this.currentUrlTree, commands, q, f !== null && f !== void 0 ? f : null);\n    }\n    /**\n     * Navigates to a view using an absolute route path.\n     *\n     * @param url An absolute path for a defined route. The function does not apply any delta to the\n     *     current URL.\n     * @param extras An object containing properties that modify the navigation strategy.\n     *\n     * @returns A Promise that resolves to 'true' when navigation succeeds,\n     * to 'false' when navigation fails, or is rejected on error.\n     *\n     * @usageNotes\n     *\n     * The following calls request navigation to an absolute path.\n     *\n     * ```\n     * router.navigateByUrl(\"/team/33/user/11\");\n     *\n     * // Navigate without updating the URL\n     * router.navigateByUrl(\"/team/33/user/11\", { skipLocationChange: true });\n     * ```\n     *\n     * @see [Routing and Navigation guide](guide/router)\n     *\n     */\n\n\n    navigateByUrl(url, extras = {\n      skipLocationChange: false\n    }) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode && this.isNgZoneEnabled && !NgZone.isInAngularZone()) {\n        this.console.warn(`Navigation triggered outside Angular zone, did you forget to call 'ngZone.run()'?`);\n      }\n\n      const urlTree = isUrlTree(url) ? url : this.parseUrl(url);\n      const mergedTree = this.urlHandlingStrategy.merge(urlTree, this.rawUrlTree);\n      return this.scheduleNavigation(mergedTree, 'imperative', null, extras);\n    }\n    /**\n     * Navigate based on the provided array of commands and a starting point.\n     * If no starting route is provided, the navigation is absolute.\n     *\n     * @param commands An array of URL fragments with which to construct the target URL.\n     * If the path is static, can be the literal URL string. For a dynamic path, pass an array of path\n     * segments, followed by the parameters for each segment.\n     * The fragments are applied to the current URL or the one provided  in the `relativeTo` property\n     * of the options object, if supplied.\n     * @param extras An options object that determines how the URL should be constructed or\n     *     interpreted.\n     *\n     * @returns A Promise that resolves to `true` when navigation succeeds, to `false` when navigation\n     *     fails,\n     * or is rejected on error.\n     *\n     * @usageNotes\n     *\n     * The following calls request navigation to a dynamic route path relative to the current URL.\n     *\n     * ```\n     * router.navigate(['team', 33, 'user', 11], {relativeTo: route});\n     *\n     * // Navigate without updating the URL, overriding the default behavior\n     * router.navigate(['team', 33, 'user', 11], {relativeTo: route, skipLocationChange: true});\n     * ```\n     *\n     * @see [Routing and Navigation guide](guide/router)\n     *\n     */\n\n\n    navigate(commands, extras = {\n      skipLocationChange: false\n    }) {\n      validateCommands(commands);\n      return this.navigateByUrl(this.createUrlTree(commands, extras), extras);\n    }\n    /** Serializes a `UrlTree` into a string */\n\n\n    serializeUrl(url) {\n      return this.urlSerializer.serialize(url);\n    }\n    /** Parses a string into a `UrlTree` */\n\n\n    parseUrl(url) {\n      let urlTree;\n\n      try {\n        urlTree = this.urlSerializer.parse(url);\n      } catch (e) {\n        urlTree = this.malformedUriErrorHandler(e, this.urlSerializer, url);\n      }\n\n      return urlTree;\n    }\n\n    isActive(url, matchOptions) {\n      let options;\n\n      if (matchOptions === true) {\n        options = Object.assign({}, exactMatchOptions);\n      } else if (matchOptions === false) {\n        options = Object.assign({}, subsetMatchOptions);\n      } else {\n        options = matchOptions;\n      }\n\n      if (isUrlTree(url)) {\n        return containsTree(this.currentUrlTree, url, options);\n      }\n\n      const urlTree = this.parseUrl(url);\n      return containsTree(this.currentUrlTree, urlTree, options);\n    }\n\n    removeEmptyProps(params) {\n      return Object.keys(params).reduce((result, key) => {\n        const value = params[key];\n\n        if (value !== null && value !== undefined) {\n          result[key] = value;\n        }\n\n        return result;\n      }, {});\n    }\n\n    processNavigations() {\n      this.navigations.subscribe(t => {\n        this.navigated = true;\n        this.lastSuccessfulId = t.id;\n        this.currentPageId = t.targetPageId;\n        this.events.next(new NavigationEnd(t.id, this.serializeUrl(t.extractedUrl), this.serializeUrl(this.currentUrlTree)));\n        this.lastSuccessfulNavigation = this.currentNavigation;\n        t.resolve(true);\n      }, e => {\n        this.console.warn(`Unhandled Navigation Error: ${e}`);\n      });\n    }\n\n    scheduleNavigation(rawUrl, source, restoredState, extras, priorPromise) {\n      var _a, _b, _c;\n\n      if (this.disposed) {\n        return Promise.resolve(false);\n      } // Duplicate navigations may be triggered by attempts to sync AngularJS and\n      // Angular router states. We have the setTimeout in the location listener to\n      // ensure the imperative nav is scheduled before the browser nav.\n\n\n      const lastNavigation = this.transitions.value;\n      const browserNavPrecededByRouterNav = isBrowserTriggeredNavigation(source) && lastNavigation && !isBrowserTriggeredNavigation(lastNavigation.source);\n      const navToSameUrl = lastNavigation.rawUrl.toString() === rawUrl.toString();\n      const lastNavigationInProgress = lastNavigation.id === ((_a = this.currentNavigation) === null || _a === void 0 ? void 0 : _a.id); // We consider duplicates as ones that goes to the same URL while the first\n      // is still processing.\n\n      const isDuplicateNav = navToSameUrl && lastNavigationInProgress;\n\n      if (browserNavPrecededByRouterNav && isDuplicateNav) {\n        return Promise.resolve(true); // return value is not used\n      }\n\n      let resolve;\n      let reject;\n      let promise;\n\n      if (priorPromise) {\n        resolve = priorPromise.resolve;\n        reject = priorPromise.reject;\n        promise = priorPromise.promise;\n      } else {\n        promise = new Promise((res, rej) => {\n          resolve = res;\n          reject = rej;\n        });\n      }\n\n      const id = ++this.navigationId;\n      let targetPageId;\n\n      if (this.canceledNavigationResolution === 'computed') {\n        const isInitialPage = this.currentPageId === 0;\n\n        if (isInitialPage) {\n          restoredState = this.location.getState();\n        } // If the `ɵrouterPageId` exist in the state then `targetpageId` should have the value of\n        // `ɵrouterPageId`. This is the case for something like a page refresh where we assign the\n        // target id to the previously set value for that page.\n\n\n        if (restoredState && restoredState.ɵrouterPageId) {\n          targetPageId = restoredState.ɵrouterPageId;\n        } else {\n          // If we're replacing the URL or doing a silent navigation, we do not want to increment the\n          // page id because we aren't pushing a new entry to history.\n          if (extras.replaceUrl || extras.skipLocationChange) {\n            targetPageId = (_b = this.browserPageId) !== null && _b !== void 0 ? _b : 0;\n          } else {\n            targetPageId = ((_c = this.browserPageId) !== null && _c !== void 0 ? _c : 0) + 1;\n          }\n        }\n      } else {\n        // This is unused when `canceledNavigationResolution` is not computed.\n        targetPageId = 0;\n      }\n\n      this.setTransition({\n        id,\n        targetPageId,\n        source,\n        restoredState,\n        currentUrlTree: this.currentUrlTree,\n        currentRawUrl: this.rawUrlTree,\n        rawUrl,\n        extras,\n        resolve,\n        reject,\n        promise,\n        currentSnapshot: this.routerState.snapshot,\n        currentRouterState: this.routerState\n      }); // Make sure that the error is propagated even though `processNavigations` catch\n      // handler does not rethrow\n\n      return promise.catch(e => {\n        return Promise.reject(e);\n      });\n    }\n\n    setBrowserUrl(url, t) {\n      const path = this.urlSerializer.serialize(url);\n      const state = Object.assign(Object.assign({}, t.extras.state), this.generateNgRouterState(t.id, t.targetPageId));\n\n      if (this.location.isCurrentPathEqualTo(path) || !!t.extras.replaceUrl) {\n        this.location.replaceState(path, '', state);\n      } else {\n        this.location.go(path, '', state);\n      }\n    }\n    /**\n     * Performs the necessary rollback action to restore the browser URL to the\n     * state before the transition.\n     */\n\n\n    restoreHistory(t, restoringFromCaughtError = false) {\n      var _a, _b;\n\n      if (this.canceledNavigationResolution === 'computed') {\n        const targetPagePosition = this.currentPageId - t.targetPageId; // The navigator change the location before triggered the browser event,\n        // so we need to go back to the current url if the navigation is canceled.\n        // Also, when navigation gets cancelled while using url update strategy eager, then we need to\n        // go back. Because, when `urlUpdateSrategy` is `eager`; `setBrowserUrl` method is called\n        // before any verification.\n\n        const browserUrlUpdateOccurred = t.source === 'popstate' || this.urlUpdateStrategy === 'eager' || this.currentUrlTree === ((_a = this.currentNavigation) === null || _a === void 0 ? void 0 : _a.finalUrl);\n\n        if (browserUrlUpdateOccurred && targetPagePosition !== 0) {\n          this.location.historyGo(targetPagePosition);\n        } else if (this.currentUrlTree === ((_b = this.currentNavigation) === null || _b === void 0 ? void 0 : _b.finalUrl) && targetPagePosition === 0) {\n          // We got to the activation stage (where currentUrlTree is set to the navigation's\n          // finalUrl), but we weren't moving anywhere in history (skipLocationChange or replaceUrl).\n          // We still need to reset the router state back to what it was when the navigation started.\n          this.resetState(t); // TODO(atscott): resetting the `browserUrlTree` should really be done in `resetState`.\n          // Investigate if this can be done by running TGP.\n\n          this.browserUrlTree = t.currentUrlTree;\n          this.resetUrlToCurrentUrlTree();\n        } else {// The browser URL and router state was not updated before the navigation cancelled so\n          // there's no restoration needed.\n        }\n      } else if (this.canceledNavigationResolution === 'replace') {\n        // TODO(atscott): It seems like we should _always_ reset the state here. It would be a no-op\n        // for `deferred` navigations that haven't change the internal state yet because guards\n        // reject. For 'eager' navigations, it seems like we also really should reset the state\n        // because the navigation was cancelled. Investigate if this can be done by running TGP.\n        if (restoringFromCaughtError) {\n          this.resetState(t);\n        }\n\n        this.resetUrlToCurrentUrlTree();\n      }\n    }\n\n    resetState(t) {\n      this.routerState = t.currentRouterState;\n      this.currentUrlTree = t.currentUrlTree; // Note here that we use the urlHandlingStrategy to get the reset `rawUrlTree` because it may be\n      // configured to handle only part of the navigation URL. This means we would only want to reset\n      // the part of the navigation handled by the Angular router rather than the whole URL. In\n      // addition, the URLHandlingStrategy may be configured to specifically preserve parts of the URL\n      // when merging, such as the query params so they are not lost on a refresh.\n\n      this.rawUrlTree = this.urlHandlingStrategy.merge(this.currentUrlTree, t.rawUrl);\n    }\n\n    resetUrlToCurrentUrlTree() {\n      this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree), '', this.generateNgRouterState(this.lastSuccessfulId, this.currentPageId));\n    }\n\n    cancelNavigationTransition(t, reason) {\n      const navCancel = new NavigationCancel(t.id, this.serializeUrl(t.extractedUrl), reason);\n      this.triggerEvent(navCancel);\n      t.resolve(false);\n    }\n\n    generateNgRouterState(navigationId, routerPageId) {\n      if (this.canceledNavigationResolution === 'computed') {\n        return {\n          navigationId,\n          ɵrouterPageId: routerPageId\n        };\n      }\n\n      return {\n        navigationId\n      };\n    }\n\n  }\n\n  Router.ɵfac = function Router_Factory(t) {\n    i0.ɵɵinvalidFactory();\n  };\n\n  Router.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Router,\n    factory: Router.ɵfac\n  });\n  return Router;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction validateCommands(commands) {\n  for (let i = 0; i < commands.length; i++) {\n    const cmd = commands[i];\n\n    if (cmd == null) {\n      throw new Error(`The requested path contains ${cmd} segment at index ${i}`);\n    }\n  }\n}\n\nfunction isBrowserTriggeredNavigation(source) {\n  return source !== 'imperative';\n}\n/**\n * @description\n *\n * When applied to an element in a template, makes that element a link\n * that initiates navigation to a route. Navigation opens one or more routed components\n * in one or more `<router-outlet>` locations on the page.\n *\n * Given a route configuration `[{ path: 'user/:name', component: UserCmp }]`,\n * the following creates a static link to the route:\n * `<a routerLink=\"/user/bob\">link to user component</a>`\n *\n * You can use dynamic values to generate the link.\n * For a dynamic link, pass an array of path segments,\n * followed by the params for each segment.\n * For example, `['/team', teamId, 'user', userName, {details: true}]`\n * generates a link to `/team/11/user/bob;details=true`.\n *\n * Multiple static segments can be merged into one term and combined with dynamic segements.\n * For example, `['/team/11/user', userName, {details: true}]`\n *\n * The input that you provide to the link is treated as a delta to the current URL.\n * For instance, suppose the current URL is `/user/(box//aux:team)`.\n * The link `<a [routerLink]=\"['/user/jim']\">Jim</a>` creates the URL\n * `/user/(jim//aux:team)`.\n * See {@link Router#createUrlTree createUrlTree} for more information.\n *\n * @usageNotes\n *\n * You can use absolute or relative paths in a link, set query parameters,\n * control how parameters are handled, and keep a history of navigation states.\n *\n * ### Relative link paths\n *\n * The first segment name can be prepended with `/`, `./`, or `../`.\n * * If the first segment begins with `/`, the router looks up the route from the root of the\n *   app.\n * * If the first segment begins with `./`, or doesn't begin with a slash, the router\n *   looks in the children of the current activated route.\n * * If the first segment begins with `../`, the router goes up one level in the route tree.\n *\n * ### Setting and handling query params and fragments\n *\n * The following link adds a query parameter and a fragment to the generated URL:\n *\n * ```\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" fragment=\"education\">\n *   link to user component\n * </a>\n * ```\n * By default, the directive constructs the new URL using the given query parameters.\n * The example generates the link: `/user/bob?debug=true#education`.\n *\n * You can instruct the directive to handle query parameters differently\n * by specifying the `queryParamsHandling` option in the link.\n * Allowed values are:\n *\n *  - `'merge'`: Merge the given `queryParams` into the current query params.\n *  - `'preserve'`: Preserve the current query params.\n *\n * For example:\n *\n * ```\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" queryParamsHandling=\"merge\">\n *   link to user component\n * </a>\n * ```\n *\n * See {@link UrlCreationOptions.queryParamsHandling UrlCreationOptions#queryParamsHandling}.\n *\n * ### Preserving navigation history\n *\n * You can provide a `state` value to be persisted to the browser's\n * [`History.state` property](https://developer.mozilla.org/en-US/docs/Web/API/History#Properties).\n * For example:\n *\n * ```\n * <a [routerLink]=\"['/user/bob']\" [state]=\"{tracingId: 123}\">\n *   link to user component\n * </a>\n * ```\n *\n * Use {@link Router.getCurrentNavigation() Router#getCurrentNavigation} to retrieve a saved\n * navigation-state value. For example, to capture the `tracingId` during the `NavigationStart`\n * event:\n *\n * ```\n * // Get NavigationStart events\n * router.events.pipe(filter(e => e instanceof NavigationStart)).subscribe(e => {\n *   const navigation = router.getCurrentNavigation();\n *   tracingService.trace({id: navigation.extras.state.tracingId});\n * });\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\n\n\nlet RouterLink = /*#__PURE__*/(() => {\n  class RouterLink {\n    constructor(router, route, tabIndexAttribute, renderer, el) {\n      this.router = router;\n      this.route = route;\n      this.tabIndexAttribute = tabIndexAttribute;\n      this.renderer = renderer;\n      this.el = el;\n      this.commands = null;\n      /** @internal */\n\n      this.onChanges = new Subject();\n      this.setTabIndexIfNotOnNativeEl('0');\n    }\n    /**\n     * Modifies the tab index if there was not a tabindex attribute on the element during\n     * instantiation.\n     */\n\n\n    setTabIndexIfNotOnNativeEl(newTabIndex) {\n      if (this.tabIndexAttribute != null\n      /* both `null` and `undefined` */\n      ) {\n        return;\n      }\n\n      const renderer = this.renderer;\n      const nativeElement = this.el.nativeElement;\n\n      if (newTabIndex !== null) {\n        renderer.setAttribute(nativeElement, 'tabindex', newTabIndex);\n      } else {\n        renderer.removeAttribute(nativeElement, 'tabindex');\n      }\n    }\n    /** @nodoc */\n\n\n    ngOnChanges(changes) {\n      // This is subscribed to by `RouterLinkActive` so that it knows to update when there are changes\n      // to the RouterLinks it's tracking.\n      this.onChanges.next(this);\n    }\n    /**\n     * Commands to pass to {@link Router#createUrlTree Router#createUrlTree}.\n     *   - **array**: commands to pass to {@link Router#createUrlTree Router#createUrlTree}.\n     *   - **string**: shorthand for array of commands with just the string, i.e. `['/route']`\n     *   - **null|undefined**: effectively disables the `routerLink`\n     * @see {@link Router#createUrlTree Router#createUrlTree}\n     */\n\n\n    set routerLink(commands) {\n      if (commands != null) {\n        this.commands = Array.isArray(commands) ? commands : [commands];\n        this.setTabIndexIfNotOnNativeEl('0');\n      } else {\n        this.commands = null;\n        this.setTabIndexIfNotOnNativeEl(null);\n      }\n    }\n    /** @nodoc */\n\n\n    onClick() {\n      if (this.urlTree === null) {\n        return true;\n      }\n\n      const extras = {\n        skipLocationChange: attrBoolValue(this.skipLocationChange),\n        replaceUrl: attrBoolValue(this.replaceUrl),\n        state: this.state\n      };\n      this.router.navigateByUrl(this.urlTree, extras);\n      return true;\n    }\n\n    get urlTree() {\n      if (this.commands === null) {\n        return null;\n      }\n\n      return this.router.createUrlTree(this.commands, {\n        // If the `relativeTo` input is not defined, we want to use `this.route` by default.\n        // Otherwise, we should use the value provided by the user in the input.\n        relativeTo: this.relativeTo !== undefined ? this.relativeTo : this.route,\n        queryParams: this.queryParams,\n        fragment: this.fragment,\n        queryParamsHandling: this.queryParamsHandling,\n        preserveFragment: attrBoolValue(this.preserveFragment)\n      });\n    }\n\n  }\n\n  RouterLink.ɵfac = function RouterLink_Factory(t) {\n    return new (t || RouterLink)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(ActivatedRoute), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n\n  RouterLink.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLink,\n    selectors: [[\"\", \"routerLink\", \"\", 5, \"a\", 5, \"area\"]],\n    hostBindings: function RouterLink_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function RouterLink_click_HostBindingHandler() {\n          return ctx.onClick();\n        });\n      }\n    },\n    inputs: {\n      queryParams: \"queryParams\",\n      fragment: \"fragment\",\n      queryParamsHandling: \"queryParamsHandling\",\n      preserveFragment: \"preserveFragment\",\n      skipLocationChange: \"skipLocationChange\",\n      replaceUrl: \"replaceUrl\",\n      state: \"state\",\n      relativeTo: \"relativeTo\",\n      routerLink: \"routerLink\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return RouterLink;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n *\n * Lets you link to specific routes in your app.\n *\n * See `RouterLink` for more information.\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\n\n\nlet RouterLinkWithHref = /*#__PURE__*/(() => {\n  class RouterLinkWithHref {\n    constructor(router, route, locationStrategy) {\n      this.router = router;\n      this.route = route;\n      this.locationStrategy = locationStrategy;\n      this.commands = null; // the url displayed on the anchor element.\n      // @HostBinding('attr.href') is used rather than @HostBinding() because it removes the\n      // href attribute when it becomes `null`.\n\n      this.href = null;\n      /** @internal */\n\n      this.onChanges = new Subject();\n      this.subscription = router.events.subscribe(s => {\n        if (s instanceof NavigationEnd) {\n          this.updateTargetUrlAndHref();\n        }\n      });\n    }\n    /**\n     * Commands to pass to {@link Router#createUrlTree Router#createUrlTree}.\n     *   - **array**: commands to pass to {@link Router#createUrlTree Router#createUrlTree}.\n     *   - **string**: shorthand for array of commands with just the string, i.e. `['/route']`\n     *   - **null|undefined**: Disables the link by removing the `href`\n     * @see {@link Router#createUrlTree Router#createUrlTree}\n     */\n\n\n    set routerLink(commands) {\n      if (commands != null) {\n        this.commands = Array.isArray(commands) ? commands : [commands];\n      } else {\n        this.commands = null;\n      }\n    }\n    /** @nodoc */\n\n\n    ngOnChanges(changes) {\n      this.updateTargetUrlAndHref();\n      this.onChanges.next(this);\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      this.subscription.unsubscribe();\n    }\n    /** @nodoc */\n\n\n    onClick(button, ctrlKey, shiftKey, altKey, metaKey) {\n      if (button !== 0 || ctrlKey || shiftKey || altKey || metaKey) {\n        return true;\n      }\n\n      if (typeof this.target === 'string' && this.target != '_self' || this.urlTree === null) {\n        return true;\n      }\n\n      const extras = {\n        skipLocationChange: attrBoolValue(this.skipLocationChange),\n        replaceUrl: attrBoolValue(this.replaceUrl),\n        state: this.state\n      };\n      this.router.navigateByUrl(this.urlTree, extras);\n      return false;\n    }\n\n    updateTargetUrlAndHref() {\n      this.href = this.urlTree !== null ? this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.urlTree)) : null;\n    }\n\n    get urlTree() {\n      if (this.commands === null) {\n        return null;\n      }\n\n      return this.router.createUrlTree(this.commands, {\n        // If the `relativeTo` input is not defined, we want to use `this.route` by default.\n        // Otherwise, we should use the value provided by the user in the input.\n        relativeTo: this.relativeTo !== undefined ? this.relativeTo : this.route,\n        queryParams: this.queryParams,\n        fragment: this.fragment,\n        queryParamsHandling: this.queryParamsHandling,\n        preserveFragment: attrBoolValue(this.preserveFragment)\n      });\n    }\n\n  }\n\n  RouterLinkWithHref.ɵfac = function RouterLinkWithHref_Factory(t) {\n    return new (t || RouterLinkWithHref)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(ActivatedRoute), i0.ɵɵdirectiveInject(i3.LocationStrategy));\n  };\n\n  RouterLinkWithHref.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLinkWithHref,\n    selectors: [[\"a\", \"routerLink\", \"\"], [\"area\", \"routerLink\", \"\"]],\n    hostVars: 2,\n    hostBindings: function RouterLinkWithHref_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function RouterLinkWithHref_click_HostBindingHandler($event) {\n          return ctx.onClick($event.button, $event.ctrlKey, $event.shiftKey, $event.altKey, $event.metaKey);\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵattribute(\"target\", ctx.target)(\"href\", ctx.href, i0.ɵɵsanitizeUrl);\n      }\n    },\n    inputs: {\n      target: \"target\",\n      queryParams: \"queryParams\",\n      fragment: \"fragment\",\n      queryParamsHandling: \"queryParamsHandling\",\n      preserveFragment: \"preserveFragment\",\n      skipLocationChange: \"skipLocationChange\",\n      replaceUrl: \"replaceUrl\",\n      state: \"state\",\n      relativeTo: \"relativeTo\",\n      routerLink: \"routerLink\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return RouterLinkWithHref;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction attrBoolValue(s) {\n  return s === '' || !!s;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n *\n * @description\n *\n * Tracks whether the linked route of an element is currently active, and allows you\n * to specify one or more CSS classes to add to the element when the linked route\n * is active.\n *\n * Use this directive to create a visual distinction for elements associated with an active route.\n * For example, the following code highlights the word \"Bob\" when the router\n * activates the associated route:\n *\n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n *\n * Whenever the URL is either '/user' or '/user/bob', the \"active-link\" class is\n * added to the anchor tag. If the URL changes, the class is removed.\n *\n * You can set more than one class using a space-separated string or an array.\n * For example:\n *\n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive=\"class1 class2\">Bob</a>\n * <a routerLink=\"/user/bob\" [routerLinkActive]=\"['class1', 'class2']\">Bob</a>\n * ```\n *\n * To add the classes only when the URL matches the link exactly, add the option `exact: true`:\n *\n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact:\n * true}\">Bob</a>\n * ```\n *\n * To directly check the `isActive` status of the link, assign the `RouterLinkActive`\n * instance to a template variable.\n * For example, the following checks the status without assigning any CSS classes:\n *\n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive #rla=\"routerLinkActive\">\n *   Bob {{ rla.isActive ? '(already open)' : ''}}\n * </a>\n * ```\n *\n * You can apply the `RouterLinkActive` directive to an ancestor of linked elements.\n * For example, the following sets the active-link class on the `<div>`  parent tag\n * when the URL is either '/user/jim' or '/user/bob'.\n *\n * ```\n * <div routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact: true}\">\n *   <a routerLink=\"/user/jim\">Jim</a>\n *   <a routerLink=\"/user/bob\">Bob</a>\n * </div>\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\n\n\nlet RouterLinkActive = /*#__PURE__*/(() => {\n  class RouterLinkActive {\n    constructor(router, element, renderer, cdr, link, linkWithHref) {\n      this.router = router;\n      this.element = element;\n      this.renderer = renderer;\n      this.cdr = cdr;\n      this.link = link;\n      this.linkWithHref = linkWithHref;\n      this.classes = [];\n      this.isActive = false;\n      /**\n       * Options to configure how to determine if the router link is active.\n       *\n       * These options are passed to the `Router.isActive()` function.\n       *\n       * @see Router.isActive\n       */\n\n      this.routerLinkActiveOptions = {\n        exact: false\n      };\n      /**\n       *\n       * You can use the output `isActiveChange` to get notified each time the link becomes\n       * active or inactive.\n       *\n       * Emits:\n       * true  -> Route is active\n       * false -> Route is inactive\n       *\n       * ```\n       * <a\n       *  routerLink=\"/user/bob\"\n       *  routerLinkActive=\"active-link\"\n       *  (isActiveChange)=\"this.onRouterLinkActive($event)\">Bob</a>\n       * ```\n       */\n\n      this.isActiveChange = new EventEmitter();\n      this.routerEventsSubscription = router.events.subscribe(s => {\n        if (s instanceof NavigationEnd) {\n          this.update();\n        }\n      });\n    }\n    /** @nodoc */\n\n\n    ngAfterContentInit() {\n      // `of(null)` is used to force subscribe body to execute once immediately (like `startWith`).\n      of(this.links.changes, this.linksWithHrefs.changes, of(null)).pipe(mergeAll()).subscribe(_ => {\n        this.update();\n        this.subscribeToEachLinkOnChanges();\n      });\n    }\n\n    subscribeToEachLinkOnChanges() {\n      var _a;\n\n      (_a = this.linkInputChangesSubscription) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n      const allLinkChanges = [...this.links.toArray(), ...this.linksWithHrefs.toArray(), this.link, this.linkWithHref].filter(link => !!link).map(link => link.onChanges);\n      this.linkInputChangesSubscription = from(allLinkChanges).pipe(mergeAll()).subscribe(link => {\n        if (this.isActive !== this.isLinkActive(this.router)(link)) {\n          this.update();\n        }\n      });\n    }\n\n    set routerLinkActive(data) {\n      const classes = Array.isArray(data) ? data : data.split(' ');\n      this.classes = classes.filter(c => !!c);\n    }\n    /** @nodoc */\n\n\n    ngOnChanges(changes) {\n      this.update();\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      var _a;\n\n      this.routerEventsSubscription.unsubscribe();\n      (_a = this.linkInputChangesSubscription) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n    }\n\n    update() {\n      if (!this.links || !this.linksWithHrefs || !this.router.navigated) return;\n      Promise.resolve().then(() => {\n        const hasActiveLinks = this.hasActiveLinks();\n\n        if (this.isActive !== hasActiveLinks) {\n          this.isActive = hasActiveLinks;\n          this.cdr.markForCheck();\n          this.classes.forEach(c => {\n            if (hasActiveLinks) {\n              this.renderer.addClass(this.element.nativeElement, c);\n            } else {\n              this.renderer.removeClass(this.element.nativeElement, c);\n            }\n          }); // Emit on isActiveChange after classes are updated\n\n          this.isActiveChange.emit(hasActiveLinks);\n        }\n      });\n    }\n\n    isLinkActive(router) {\n      const options = isActiveMatchOptions(this.routerLinkActiveOptions) ? this.routerLinkActiveOptions : // While the types should disallow `undefined` here, it's possible without strict inputs\n      this.routerLinkActiveOptions.exact || false;\n      return link => link.urlTree ? router.isActive(link.urlTree, options) : false;\n    }\n\n    hasActiveLinks() {\n      const isActiveCheckFn = this.isLinkActive(this.router);\n      return this.link && isActiveCheckFn(this.link) || this.linkWithHref && isActiveCheckFn(this.linkWithHref) || this.links.some(isActiveCheckFn) || this.linksWithHrefs.some(isActiveCheckFn);\n    }\n\n  }\n\n  RouterLinkActive.ɵfac = function RouterLinkActive_Factory(t) {\n    return new (t || RouterLinkActive)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(RouterLink, 8), i0.ɵɵdirectiveInject(RouterLinkWithHref, 8));\n  };\n\n  RouterLinkActive.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLinkActive,\n    selectors: [[\"\", \"routerLinkActive\", \"\"]],\n    contentQueries: function RouterLinkActive_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, RouterLink, 5);\n        i0.ɵɵcontentQuery(dirIndex, RouterLinkWithHref, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.links = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.linksWithHrefs = _t);\n      }\n    },\n    inputs: {\n      routerLinkActiveOptions: \"routerLinkActiveOptions\",\n      routerLinkActive: \"routerLinkActive\"\n    },\n    outputs: {\n      isActiveChange: \"isActiveChange\"\n    },\n    exportAs: [\"routerLinkActive\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return RouterLinkActive;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Use instead of `'paths' in options` to be compatible with property renaming\n */\n\n\nfunction isActiveMatchOptions(options) {\n  return !!options.paths;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @description\n *\n * Provides a preloading strategy.\n *\n * @publicApi\n */\n\n\nclass PreloadingStrategy {}\n/**\n * @description\n *\n * Provides a preloading strategy that preloads all modules as quickly as possible.\n *\n * ```\n * RouterModule.forRoot(ROUTES, {preloadingStrategy: PreloadAllModules})\n * ```\n *\n * @publicApi\n */\n\n\nclass PreloadAllModules {\n  preload(route, fn) {\n    return fn().pipe(catchError(() => of(null)));\n  }\n\n}\n/**\n * @description\n *\n * Provides a preloading strategy that does not preload any modules.\n *\n * This strategy is enabled by default.\n *\n * @publicApi\n */\n\n\nclass NoPreloading {\n  preload(route, fn) {\n    return of(null);\n  }\n\n}\n/**\n * The preloader optimistically loads all router configurations to\n * make navigations into lazily-loaded sections of the application faster.\n *\n * The preloader runs in the background. When the router bootstraps, the preloader\n * starts listening to all navigation events. After every such event, the preloader\n * will check if any configurations can be loaded lazily.\n *\n * If a route is protected by `canLoad` guards, the preloaded will not load it.\n *\n * @publicApi\n */\n\n\nlet RouterPreloader = /*#__PURE__*/(() => {\n  class RouterPreloader {\n    constructor(router, compiler, injector, preloadingStrategy) {\n      this.router = router;\n      this.injector = injector;\n      this.preloadingStrategy = preloadingStrategy;\n\n      const onStartLoad = r => router.triggerEvent(new RouteConfigLoadStart(r));\n\n      const onEndLoad = r => router.triggerEvent(new RouteConfigLoadEnd(r));\n\n      this.loader = new RouterConfigLoader(injector, compiler, onStartLoad, onEndLoad);\n    }\n\n    setUpPreloading() {\n      this.subscription = this.router.events.pipe(filter(e => e instanceof NavigationEnd), concatMap(() => this.preload())).subscribe(() => {});\n    }\n\n    preload() {\n      const ngModule = this.injector.get(NgModuleRef);\n      return this.processRoutes(ngModule, this.router.config);\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n\n    processRoutes(ngModule, routes) {\n      const res = [];\n\n      for (const route of routes) {\n        // we already have the config loaded, just recurse\n        if (route.loadChildren && !route.canLoad && route._loadedConfig) {\n          const childConfig = route._loadedConfig;\n          res.push(this.processRoutes(childConfig.module, childConfig.routes)); // no config loaded, fetch the config\n        } else if (route.loadChildren && !route.canLoad) {\n          res.push(this.preloadConfig(ngModule, route)); // recurse into children\n        } else if (route.children) {\n          res.push(this.processRoutes(ngModule, route.children));\n        }\n      }\n\n      return from(res).pipe(mergeAll(), map(_ => void 0));\n    }\n\n    preloadConfig(ngModule, route) {\n      return this.preloadingStrategy.preload(route, () => {\n        const loaded$ = route._loadedConfig ? of(route._loadedConfig) : this.loader.load(ngModule.injector, route);\n        return loaded$.pipe(mergeMap(config => {\n          route._loadedConfig = config;\n          return this.processRoutes(config.module, config.routes);\n        }));\n      });\n    }\n\n  }\n\n  RouterPreloader.ɵfac = function RouterPreloader_Factory(t) {\n    return new (t || RouterPreloader)(i0.ɵɵinject(Router), i0.ɵɵinject(i0.Compiler), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(PreloadingStrategy));\n  };\n\n  RouterPreloader.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RouterPreloader,\n    factory: RouterPreloader.ɵfac\n  });\n  return RouterPreloader;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet RouterScroller = /*#__PURE__*/(() => {\n  class RouterScroller {\n    constructor(router,\n    /** @docsNotRequired */\n    viewportScroller, options = {}) {\n      this.router = router;\n      this.viewportScroller = viewportScroller;\n      this.options = options;\n      this.lastId = 0;\n      this.lastSource = 'imperative';\n      this.restoredId = 0;\n      this.store = {}; // Default both options to 'disabled'\n\n      options.scrollPositionRestoration = options.scrollPositionRestoration || 'disabled';\n      options.anchorScrolling = options.anchorScrolling || 'disabled';\n    }\n\n    init() {\n      // we want to disable the automatic scrolling because having two places\n      // responsible for scrolling results race conditions, especially given\n      // that browser don't implement this behavior consistently\n      if (this.options.scrollPositionRestoration !== 'disabled') {\n        this.viewportScroller.setHistoryScrollRestoration('manual');\n      }\n\n      this.routerEventsSubscription = this.createScrollEvents();\n      this.scrollEventsSubscription = this.consumeScrollEvents();\n    }\n\n    createScrollEvents() {\n      return this.router.events.subscribe(e => {\n        if (e instanceof NavigationStart) {\n          // store the scroll position of the current stable navigations.\n          this.store[this.lastId] = this.viewportScroller.getScrollPosition();\n          this.lastSource = e.navigationTrigger;\n          this.restoredId = e.restoredState ? e.restoredState.navigationId : 0;\n        } else if (e instanceof NavigationEnd) {\n          this.lastId = e.id;\n          this.scheduleScrollEvent(e, this.router.parseUrl(e.urlAfterRedirects).fragment);\n        }\n      });\n    }\n\n    consumeScrollEvents() {\n      return this.router.events.subscribe(e => {\n        if (!(e instanceof Scroll)) return; // a popstate event. The pop state event will always ignore anchor scrolling.\n\n        if (e.position) {\n          if (this.options.scrollPositionRestoration === 'top') {\n            this.viewportScroller.scrollToPosition([0, 0]);\n          } else if (this.options.scrollPositionRestoration === 'enabled') {\n            this.viewportScroller.scrollToPosition(e.position);\n          } // imperative navigation \"forward\"\n\n        } else {\n          if (e.anchor && this.options.anchorScrolling === 'enabled') {\n            this.viewportScroller.scrollToAnchor(e.anchor);\n          } else if (this.options.scrollPositionRestoration !== 'disabled') {\n            this.viewportScroller.scrollToPosition([0, 0]);\n          }\n        }\n      });\n    }\n\n    scheduleScrollEvent(routerEvent, anchor) {\n      this.router.triggerEvent(new Scroll(routerEvent, this.lastSource === 'popstate' ? this.store[this.restoredId] : null, anchor));\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      if (this.routerEventsSubscription) {\n        this.routerEventsSubscription.unsubscribe();\n      }\n\n      if (this.scrollEventsSubscription) {\n        this.scrollEventsSubscription.unsubscribe();\n      }\n    }\n\n  }\n\n  RouterScroller.ɵfac = function RouterScroller_Factory(t) {\n    i0.ɵɵinvalidFactory();\n  };\n\n  RouterScroller.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RouterScroller,\n    factory: RouterScroller.ɵfac\n  });\n  return RouterScroller;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The directives defined in the `RouterModule`.\n */\n\n\nconst ROUTER_DIRECTIVES = [RouterOutlet, RouterLink, RouterLinkWithHref, RouterLinkActive, ɵEmptyOutletComponent];\n/**\n * A [DI token](guide/glossary/#di-token) for the router service.\n *\n * @publicApi\n */\n\nconst ROUTER_CONFIGURATION = /*#__PURE__*/new InjectionToken('ROUTER_CONFIGURATION');\n/**\n * @docsNotRequired\n */\n\nconst ROUTER_FORROOT_GUARD = /*#__PURE__*/new InjectionToken('ROUTER_FORROOT_GUARD');\nconst ROUTER_PROVIDERS = [Location, {\n  provide: UrlSerializer,\n  useClass: DefaultUrlSerializer\n}, {\n  provide: Router,\n  useFactory: setupRouter,\n  deps: [UrlSerializer, ChildrenOutletContexts, Location, Injector, Compiler, ROUTES, ROUTER_CONFIGURATION, [UrlHandlingStrategy, /*#__PURE__*/new Optional()], [RouteReuseStrategy, /*#__PURE__*/new Optional()]]\n}, ChildrenOutletContexts, {\n  provide: ActivatedRoute,\n  useFactory: rootRoute,\n  deps: [Router]\n}, RouterPreloader, NoPreloading, PreloadAllModules, {\n  provide: ROUTER_CONFIGURATION,\n  useValue: {\n    enableTracing: false\n  }\n}];\n\nfunction routerNgProbeToken() {\n  return new NgProbeToken('Router', Router);\n}\n/**\n * @description\n *\n * Adds directives and providers for in-app navigation among views defined in an application.\n * Use the Angular `Router` service to declaratively specify application states and manage state\n * transitions.\n *\n * You can import this NgModule multiple times, once for each lazy-loaded bundle.\n * However, only one `Router` service can be active.\n * To ensure this, there are two ways to register routes when importing this module:\n *\n * * The `forRoot()` method creates an `NgModule` that contains all the directives, the given\n * routes, and the `Router` service itself.\n * * The `forChild()` method creates an `NgModule` that contains all the directives and the given\n * routes, but does not include the `Router` service.\n *\n * @see [Routing and Navigation guide](guide/router) for an\n * overview of how the `Router` service should be used.\n *\n * @publicApi\n */\n\n\nlet RouterModule = /*#__PURE__*/(() => {\n  class RouterModule {\n    // Note: We are injecting the Router so it gets created eagerly...\n    constructor(guard, router) {}\n    /**\n     * Creates and configures a module with all the router providers and directives.\n     * Optionally sets up an application listener to perform an initial navigation.\n     *\n     * When registering the NgModule at the root, import as follows:\n     *\n     * ```\n     * @NgModule({\n     *   imports: [RouterModule.forRoot(ROUTES)]\n     * })\n     * class MyNgModule {}\n     * ```\n     *\n     * @param routes An array of `Route` objects that define the navigation paths for the application.\n     * @param config An `ExtraOptions` configuration object that controls how navigation is performed.\n     * @return The new `NgModule`.\n     *\n     */\n\n\n    static forRoot(routes, config) {\n      return {\n        ngModule: RouterModule,\n        providers: [ROUTER_PROVIDERS, provideRoutes(routes), {\n          provide: ROUTER_FORROOT_GUARD,\n          useFactory: provideForRootGuard,\n          deps: [[Router, new Optional(), new SkipSelf()]]\n        }, {\n          provide: ROUTER_CONFIGURATION,\n          useValue: config ? config : {}\n        }, {\n          provide: LocationStrategy,\n          useFactory: provideLocationStrategy,\n          deps: [PlatformLocation, [new Inject(APP_BASE_HREF), new Optional()], ROUTER_CONFIGURATION]\n        }, {\n          provide: RouterScroller,\n          useFactory: createRouterScroller,\n          deps: [Router, ViewportScroller, ROUTER_CONFIGURATION]\n        }, {\n          provide: PreloadingStrategy,\n          useExisting: config && config.preloadingStrategy ? config.preloadingStrategy : NoPreloading\n        }, {\n          provide: NgProbeToken,\n          multi: true,\n          useFactory: routerNgProbeToken\n        }, provideRouterInitializer()]\n      };\n    }\n    /**\n     * Creates a module with all the router directives and a provider registering routes,\n     * without creating a new Router service.\n     * When registering for submodules and lazy-loaded submodules, create the NgModule as follows:\n     *\n     * ```\n     * @NgModule({\n     *   imports: [RouterModule.forChild(ROUTES)]\n     * })\n     * class MyNgModule {}\n     * ```\n     *\n     * @param routes An array of `Route` objects that define the navigation paths for the submodule.\n     * @return The new NgModule.\n     *\n     */\n\n\n    static forChild(routes) {\n      return {\n        ngModule: RouterModule,\n        providers: [provideRoutes(routes)]\n      };\n    }\n\n  }\n\n  RouterModule.ɵfac = function RouterModule_Factory(t) {\n    return new (t || RouterModule)(i0.ɵɵinject(ROUTER_FORROOT_GUARD, 8), i0.ɵɵinject(Router, 8));\n  };\n\n  RouterModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: RouterModule\n  });\n  RouterModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return RouterModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction createRouterScroller(router, viewportScroller, config) {\n  if (config.scrollOffset) {\n    viewportScroller.setOffset(config.scrollOffset);\n  }\n\n  return new RouterScroller(router, viewportScroller, config);\n}\n\nfunction provideLocationStrategy(platformLocationStrategy, baseHref, options = {}) {\n  return options.useHash ? new HashLocationStrategy(platformLocationStrategy, baseHref) : new PathLocationStrategy(platformLocationStrategy, baseHref);\n}\n\nfunction provideForRootGuard(router) {\n  if ((typeof ngDevMode === 'undefined' || ngDevMode) && router) {\n    throw new Error(`RouterModule.forRoot() called twice. Lazy loaded modules should use RouterModule.forChild() instead.`);\n  }\n\n  return 'guarded';\n}\n/**\n * Registers a [DI provider](guide/glossary#provider) for a set of routes.\n * @param routes The route configuration to provide.\n *\n * @usageNotes\n *\n * ```\n * @NgModule({\n *   imports: [RouterModule.forChild(ROUTES)],\n *   providers: [provideRoutes(EXTRA_ROUTES)]\n * })\n * class MyNgModule {}\n * ```\n *\n * @publicApi\n */\n\n\nfunction provideRoutes(routes) {\n  return [{\n    provide: ANALYZE_FOR_ENTRY_COMPONENTS,\n    multi: true,\n    useValue: routes\n  }, {\n    provide: ROUTES,\n    multi: true,\n    useValue: routes\n  }];\n}\n\nfunction setupRouter(urlSerializer, contexts, location, injector, compiler, config, opts = {}, urlHandlingStrategy, routeReuseStrategy) {\n  const router = new Router(null, urlSerializer, contexts, location, injector, compiler, flatten(config));\n\n  if (urlHandlingStrategy) {\n    router.urlHandlingStrategy = urlHandlingStrategy;\n  }\n\n  if (routeReuseStrategy) {\n    router.routeReuseStrategy = routeReuseStrategy;\n  }\n\n  assignExtraOptionsToRouter(opts, router);\n\n  if (opts.enableTracing) {\n    router.events.subscribe(e => {\n      var _a, _b; // tslint:disable:no-console\n\n\n      (_a = console.group) === null || _a === void 0 ? void 0 : _a.call(console, `Router Event: ${e.constructor.name}`);\n      console.log(e.toString());\n      console.log(e);\n      (_b = console.groupEnd) === null || _b === void 0 ? void 0 : _b.call(console); // tslint:enable:no-console\n    });\n  }\n\n  return router;\n}\n\nfunction assignExtraOptionsToRouter(opts, router) {\n  if (opts.errorHandler) {\n    router.errorHandler = opts.errorHandler;\n  }\n\n  if (opts.malformedUriErrorHandler) {\n    router.malformedUriErrorHandler = opts.malformedUriErrorHandler;\n  }\n\n  if (opts.onSameUrlNavigation) {\n    router.onSameUrlNavigation = opts.onSameUrlNavigation;\n  }\n\n  if (opts.paramsInheritanceStrategy) {\n    router.paramsInheritanceStrategy = opts.paramsInheritanceStrategy;\n  }\n\n  if (opts.relativeLinkResolution) {\n    router.relativeLinkResolution = opts.relativeLinkResolution;\n  }\n\n  if (opts.urlUpdateStrategy) {\n    router.urlUpdateStrategy = opts.urlUpdateStrategy;\n  }\n\n  if (opts.canceledNavigationResolution) {\n    router.canceledNavigationResolution = opts.canceledNavigationResolution;\n  }\n}\n\nfunction rootRoute(router) {\n  return router.routerState.root;\n}\n/**\n * Router initialization requires two steps:\n *\n * First, we start the navigation in a `APP_INITIALIZER` to block the bootstrap if\n * a resolver or a guard executes asynchronously.\n *\n * Next, we actually run activation in a `BOOTSTRAP_LISTENER`, using the\n * `afterPreactivation` hook provided by the router.\n * The router navigation starts, reaches the point when preactivation is done, and then\n * pauses. It waits for the hook to be resolved. We then resolve it only in a bootstrap listener.\n */\n\n\nlet RouterInitializer = /*#__PURE__*/(() => {\n  class RouterInitializer {\n    constructor(injector) {\n      this.injector = injector;\n      this.initNavigation = false;\n      this.destroyed = false;\n      this.resultOfPreactivationDone = new Subject();\n    }\n\n    appInitializer() {\n      const p = this.injector.get(LOCATION_INITIALIZED, Promise.resolve(null));\n      return p.then(() => {\n        // If the injector was destroyed, the DI lookups below will fail.\n        if (this.destroyed) {\n          return Promise.resolve(true);\n        }\n\n        let resolve = null;\n        const res = new Promise(r => resolve = r);\n        const router = this.injector.get(Router);\n        const opts = this.injector.get(ROUTER_CONFIGURATION);\n\n        if (opts.initialNavigation === 'disabled') {\n          router.setUpLocationChangeListener();\n          resolve(true);\n        } else if ( // TODO: enabled is deprecated as of v11, can be removed in v13\n        opts.initialNavigation === 'enabled' || opts.initialNavigation === 'enabledBlocking') {\n          router.hooks.afterPreactivation = () => {\n            // only the initial navigation should be delayed\n            if (!this.initNavigation) {\n              this.initNavigation = true;\n              resolve(true);\n              return this.resultOfPreactivationDone; // subsequent navigations should not be delayed\n            } else {\n              return of(null);\n            }\n          };\n\n          router.initialNavigation();\n        } else {\n          resolve(true);\n        }\n\n        return res;\n      });\n    }\n\n    bootstrapListener(bootstrappedComponentRef) {\n      const opts = this.injector.get(ROUTER_CONFIGURATION);\n      const preloader = this.injector.get(RouterPreloader);\n      const routerScroller = this.injector.get(RouterScroller);\n      const router = this.injector.get(Router);\n      const ref = this.injector.get(ApplicationRef);\n\n      if (bootstrappedComponentRef !== ref.components[0]) {\n        return;\n      } // Default case\n\n\n      if (opts.initialNavigation === 'enabledNonBlocking' || opts.initialNavigation === undefined) {\n        router.initialNavigation();\n      }\n\n      preloader.setUpPreloading();\n      routerScroller.init();\n      router.resetRootComponentType(ref.componentTypes[0]);\n      this.resultOfPreactivationDone.next(null);\n      this.resultOfPreactivationDone.complete();\n    }\n\n    ngOnDestroy() {\n      this.destroyed = true;\n    }\n\n  }\n\n  RouterInitializer.ɵfac = function RouterInitializer_Factory(t) {\n    return new (t || RouterInitializer)(i0.ɵɵinject(i0.Injector));\n  };\n\n  RouterInitializer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RouterInitializer,\n    factory: RouterInitializer.ɵfac\n  });\n  return RouterInitializer;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction getAppInitializer(r) {\n  return r.appInitializer.bind(r);\n}\n\nfunction getBootstrapListener(r) {\n  return r.bootstrapListener.bind(r);\n}\n/**\n * A [DI token](guide/glossary/#di-token) for the router initializer that\n * is called after the app is bootstrapped.\n *\n * @publicApi\n */\n\n\nconst ROUTER_INITIALIZER = /*#__PURE__*/new InjectionToken('Router Initializer');\n\nfunction provideRouterInitializer() {\n  return [RouterInitializer, {\n    provide: APP_INITIALIZER,\n    multi: true,\n    useFactory: getAppInitializer,\n    deps: [RouterInitializer]\n  }, {\n    provide: ROUTER_INITIALIZER,\n    useFactory: getBootstrapListener,\n    deps: [RouterInitializer]\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useExisting: ROUTER_INITIALIZER\n  }];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nconst VERSION = /*#__PURE__*/new Version('13.1.3');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ActivatedRoute, ActivatedRouteSnapshot, ActivationEnd, ActivationStart, BaseRouteReuseStrategy, ChildActivationEnd, ChildActivationStart, ChildrenOutletContexts, DefaultUrlSerializer, GuardsCheckEnd, GuardsCheckStart, NavigationCancel, NavigationEnd, NavigationError, NavigationStart, NoPreloading, OutletContext, PRIMARY_OUTLET, PreloadAllModules, PreloadingStrategy, ROUTER_CONFIGURATION, ROUTER_INITIALIZER, ROUTES, ResolveEnd, ResolveStart, RouteConfigLoadEnd, RouteConfigLoadStart, RouteReuseStrategy, Router, RouterEvent, RouterLink, RouterLinkActive, RouterLinkWithHref, RouterModule, RouterOutlet, RouterPreloader, RouterState, RouterStateSnapshot, RoutesRecognized, Scroll, UrlHandlingStrategy, UrlSegment, UrlSegmentGroup, UrlSerializer, UrlTree, VERSION, convertToParamMap, provideRoutes, ɵEmptyOutletComponent, ROUTER_PROVIDERS as ɵROUTER_PROVIDERS, assignExtraOptionsToRouter as ɵassignExtraOptionsToRouter, flatten as ɵflatten }; //# sourceMappingURL=router.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}