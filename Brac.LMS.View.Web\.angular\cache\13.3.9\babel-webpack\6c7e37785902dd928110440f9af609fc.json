{"ast": null, "code": "import { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\nexport function bufferToggle(openings, closingSelector) {\n  return operate((source, subscriber) => {\n    const buffers = [];\n    innerFrom(openings).subscribe(new OperatorSubscriber(subscriber, openValue => {\n      const buffer = [];\n      buffers.push(buffer);\n      const closingSubscription = new Subscription();\n\n      const emitBuffer = () => {\n        arrRemove(buffers, buffer);\n        subscriber.next(buffer);\n        closingSubscription.unsubscribe();\n      };\n\n      closingSubscription.add(innerFrom(closingSelector(openValue)).subscribe(new OperatorSubscriber(subscriber, emitBuffer, noop)));\n    }, noop));\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      for (const buffer of buffers) {\n        buffer.push(value);\n      }\n    }, () => {\n      while (buffers.length > 0) {\n        subscriber.next(buffers.shift());\n      }\n\n      subscriber.complete();\n    }));\n  });\n} //# sourceMappingURL=bufferToggle.js.map", "map": null, "metadata": {}, "sourceType": "module"}