<block-ui>
  <!-- Page content-->
  <div class="container-fluid position-relative zindex-5 pb-0 mb-md-1 p-3" style="margin-top: -15px">
    <div class="row">
      <!-- Sidebar-->
      <!--<div class="col-lg-2" style="background-image: linear-gradient(140deg, #F5C253 0%, #F5C253 50%, #F5BD00 75%);" *ngIf="!quizRunning">
        <app-web-side-menu></app-web-side-menu>
      </div>-->
      <!-- Content-->
      <div class="col-lg-10 p-5" *ngIf="pagedetail" [ngClass]="{'col-lg-12': quizRunning}">
        <div class="row h-100 bg-tranparent-black-glass rounded-1 shadow-lg">
          <div class="col-12">
            <div class="pt-2 p-md-3">
              <div class="col-12 d-flex justify-content-between">
                <p class="h3 text-break mb-0">
                  {{ pagedetail.ExamName }} <br />
                  <span class="fw-bold text-uppercase fs-6">Evaluation Test</span>
                </p>
                <a class="btn btn-link text-decoration-none fs-4 fw-bold btn-sm d-flex align-items-center border-start"
                  (click)="backClicked()"><i class="fs-4 ai-arrow-left fs-base me-2"></i>Go Back</a>
              </div>
              <hr />
              <!--start: quiz trace controls-->
              <div class="col-12 mb-3" *ngIf="quizRunning">
                <div class="d-flex justify-content-center">
                  <pagination-controls id="p" previousLabel="Prev" nextLabel="Next" (pageChange)="setPage($event)">
                  </pagination-controls>
                  <ul class="d-none">
                    <li *ngFor="
                              let q of questionList | paginate : {
                                      id:'p',
                                      itemsPerPage: page.size,
                                      currentPage: page.pageNumber,
                                      totalItems: questionList.length
                                    };
                              let i = index
                            ">
                      {{ q.Id }}
                    </li>
                  </ul>
                </div>
              </div>
              <!--end: quiz trace controls-->
              <div class="col-12 mb-3">
                <div class="section" *ngIf="pagedetail">
                  <div class="col-12" *ngIf="!quizRunning">
                    <div class="font-weight-bold">
                      <!-- <div class="sidebar"> -->

                      <div class="widget widget_recent_post">
                        <div class="row">
                          <div class="col-lg-6 col-12">
                            <ul class="list_none blog_meta">
                              <li>
                                <i class="fa fa-arrow-right me-2"></i>
                                Total Marks : {{ pagedetail.Marks }}

                              </li>
                              <li>
                                <i class="fa fa-arrow-right me-2"></i>
                                Question Type :
                                {{ pagedetail.MCQOnly ? "MCQ" : "Mixed" }}
                              </li>
                            </ul>
                          </div>

                          <div class="col-lg-6 col-12">
                            <ul class="list_none blog_meta">
                              <li>
                                <i class="fa fa-arrow-right me-2"></i>
                                Total Duration :
                                {{ getHourMint(pagedetail.DurationMnt) }}
                              </li>
                              <li>
                                <i class="fa fa-arrow-right me-2"></i>
                                Pending Quota :
                                {{ pagedetail.PendingQuota }}
                              </li>
                            </ul>
                          </div>
                          <div class="col-12">
                            <ul class="list_none blog_meta mb-4 mt-2">
                              <li *ngIf="
                                  pagedetail.StartDate && pagedetail.EndDate
                                ">
                                <i class="fa fa-arrow-right me-2"></i>
                                Open in :
                                <b class="text-primary">
                                  {{
                                  pagedetail.StartDate
                                  | amFromUtc
                                  | amLocal
                                  | amDateFormat
                                  : "MMM DD, YYYY hh:mm
                                  A"
                                  }}
                                </b>
                                to
                                <b class="text-primary">
                                  {{
                                  pagedetail.EndDate
                                  | amFromUtc
                                  | amLocal
                                  | amDateFormat
                                  : "MMM DD, YYYY hh:mm
                                  A"
                                  }}
                                </b>
                              </li>
                            </ul>
                          </div>
                          <div class="col-12 mb-3">
                            <button type="button" (click)="openFeedBackModal(templateFeedBackModal)"
                              [disabled]="pagedetail.FeedbackGiven" class="btn btn-mini btn-primary"> <i
                                class="fas fa-tasks"></i> {{pagedetail.FeedbackGiven? 'Feedback Given' : 'Give Your
                              Feedback'}} </button>
                          </div>

                          <div class="col-12 mb-3" *ngIf="pagedetail.ExamInstructions">
                            <accordion [isAnimated]="true">
                              <accordion-group heading="EXAM INSTRUCTIONS (click here to see)"
                                panelClass="custom-accordion">
                                <div [innerHTML]="pagedetail.ExamInstructions"></div>
                              </accordion-group>
                            </accordion>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-12 text-center" *ngIf="!quizRunning">
                    <div class="bg-success rounded-3">
                      <h3 class="p-3 text-white" *ngIf="pagedetail.Attempt>0">
                        You have already apeared in the exam.
                      </h3>
                    </div>
                    <div class="bg-danger rounded-3">
                      <h3 class="p-3 text-white" *ngIf="!pagedetail.Allow">
                        <i class="fa fa-exclamation-triangle"> </i> {{notAllowMessage}}
                      </h3>
                    </div>
                    <button *ngIf="pagedetail.Allow" type="button" class="btn btn-primary"
                      (click)="startQuiz(template)">
                      <i class="fa-solid fa-check"></i> Start Exam

                    </button>
                  </div>

                  <div class="col-12" *ngIf="quizRunning">
                    <div class="card q-panel noselect">
                      <div class="card-header">
                        <div class="row">
                          <div class="col-lg-8 col-md-6 col-12 qs-counter">
                            <i class="fa-solid fa-circle-question"></i>
                            <strong>
                              QUESTION {{ qIndex + 1 }} of
                              {{ questionList.length }}</strong>
                          </div>
                          <div class="col-lg-4 col-md-6 col-12 text-end timer">
                            <i class="fa-solid fa-clock"></i>
                            <strong>
                              Time Left:
                              <b>{{ timer.hour }}h : {{ timer.minute }}m :
                                {{ timer.second }}s</b></strong>
                          </div>
                        </div>
                      </div>
                      <div class="question-body noselect" *ngIf="timer.isRunning">
                        <!------------ MCQ Question ------------>
                        <div class="card-body" [@inOutAnimation] *ngIf="
                            questionList[qIndex].Type === 'MCQ' &&
                            timer.isRunning
                          ">
                          <div class="row">
                            <div class="col-sm-11 col-10">
                              <p class="text-question mb-0">
                                {{ questionList[qIndex].Question }}
                              </p>
                            </div>
                            <div class="col-sm-1 col-2">
                              <p class="text-question mb-0">
                                [{{ questionList[qIndex].Mark }}]
                              </p>
                            </div>
                          </div>
                        </div>
                        <ul class="list-group" [@inOutAnimation] *ngIf="
                            questionList[qIndex].Type === 'MCQ' &&
                            timer.isRunning
                          ">
                          <li class="list-group-item"
                            *ngFor="let option of questionList[qIndex].Options; let oi = index;">
                            <input class="form-check-input" (click)="selectSpecific(questionList[qIndex].Options)"
                              type="radio" [name]="'radioGroup' + qIndex" [(ngModel)]="option.Selected"
                              [id]="'option' + oi + '-' + qIndex" [value]="option.Text" />

                            <label class="form-check-label" [for]="'option' + oi + '-' + qIndex">
                              {{ option.Text }}
                            </label>
                          </li>
                        </ul>
                        <!------------ End MCQ Question ------------>

                        <!------------ True/False Question ------------>
                        <div class="card-body" [@inOutAnimation] *ngIf="
                            questionList[qIndex].Type === 'TFQ' &&
                            timer.isRunning
                          ">
                          <div class="row">
                            <div class="col-sm-11 col-10">
                              <p class="text-question mb-0">
                                {{ questionList[qIndex].Question }}
                              </p>
                            </div>
                            <div class="col-sm-1 col-2">
                              <p class="text-question mb-0">
                                [{{ questionList[qIndex].Mark }}]
                              </p>
                            </div>
                            <div class="col-sm-12 mt-2">
                              <div class="row">
                                <div class="col-12">
                                  <ui-switch uncheckedLabel="Selected False" checkedLabel="Selected True" size="small"
                                    defaultBgColor="#E82D4C" [(ngModel)]="questionList[qIndex].Answer">
                                  </ui-switch>
                                </div>
                                <div class="col-12">
                                  {{questionList[qIndex].Answer==true?'Selected True':'Selected False'}}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!------------ End True/False Question ------------>

                        <!------------ Fill in the gap Question ------------>
                        <div class="card-body" [@inOutAnimation] *ngIf="
                            questionList[qIndex].Type === 'FIGQ' &&
                            timer.isRunning
                          ">
                          <div class="row">
                            <div class="col-sm-11 col-10">
                              <p class="text-question mb-0">
                                {{ questionList[qIndex].Question }}
                              </p>
                            </div>
                            <div class="col-sm-1 col-2">
                              <p class="text-question mb-0">
                                [{{ questionList[qIndex].Mark }}]
                              </p>
                            </div>
                            <div class="col-sm-12 mt-2">
                              <div class="input-group mb-3">
                                <span class="input-group-text">Answer</span>
                                <input type="text" class="form-control" placeholder="Write answer .."
                                  [(ngModel)]="questionList[qIndex].Answer" />
                              </div>
                            </div>
                          </div>
                        </div>
                        <!------------ End Fill in the gap Question ------------>

                        <!------------ Matching Question ------------>
                        <div class="card-body" [@inOutAnimation] *ngIf="
                            questionList[qIndex].Type === 'LRMQ' &&
                            timer.isRunning
                          ">
                          <div class="row">
                            <div class="col-12">
                              <div class="card">
                                <div class="card-header">
                                  <h4 class="text-bold">Matching Questions</h4>
                                </div>
                                <div class="card-body pt-3">
                                  <div class="row">
                                    <div class="col-sm-7 col-12 pb-2">
                                      <div class="row" *ngFor="
                                          let item of questionList[qIndex]
                                            .LeftSides;
                                          let i = index
                                        ">
                                        <div class="col-sm-11 col-xs-11 example-box">
                                          <b>{{ i + 1 }}. {{ item.LeftSide }}</b>
                                        </div>
                                        <div class="col-sm-1 col-xs-1 example-box">
                                          <b>[{{ item.Mark }}]</b>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="col-sm-5 col-12">
                                      <div cdkDropList class="example-list" (cdkDropListDropped)="drop($event)">
                                        <div class="example-box" *ngFor="
                                            let item of questionList[qIndex]
                                              .RightSides;
                                            let i = index
                                          " cdkDrag>
                                          <div class="example-custom-placeholder" *cdkDragPlaceholder></div>
                                          <b>{{ i + 1 }}. {{ item }}</b>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!------------ End Matching Question ------------>

                        <!------------ Written Question ------------>
                        <div class="card-body" [@inOutAnimation] *ngIf="
                            questionList[qIndex].Type === 'WQ' &&
                            timer.isRunning
                          ">
                          <div class="row">
                            <div class="col-sm-11 col-10">
                              <p class="text-question mb-0">
                                {{ questionList[qIndex].Question }}
                              </p>
                            </div>
                            <div class="col-sm-1 col-2">
                              <p class="text-question mb-0">
                                [{{ questionList[qIndex].Mark }}]
                              </p>
                            </div>
                            <div class="col-sm-12 mt-2">
                              <div class="input-group mb-3">
                                <span class="input-group-text">Answer</span>
                                <textarea class="form-control" rows="5"
                                  [(ngModel)]="questionList[qIndex].Answer"></textarea>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!------------ End Written Question ------------>
                      </div>
                      <div class="card-footer">
                        <button type="button" class="btn btn-info btn-box me-1" (click)="prevQuestion()"
                          [disabled]="qIndex === 0">
                          <i class="fa fa-arrow-left"></i> Prev
                        </button>
                        <button type="button" class="btn btn-success" (click)="nextQuestion(template)">
                          {{ btnNextText }}
                          <i class="fa fa-arrow-right"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #templateFeedBackModal>
    <div class="modal-header">
      <h4 class="modal-title float-start">
        Feedbacks on "{{ pagedetail.ExamName }}"
      </h4>
      <button type="button " class="close btn btn-mini btn-outline-danger float-end" aria-label="Close "
        (click)="modalHideFeedBack()">
        <i class="fa fa-close"></i>
      </button>
    </div>
    <div class="modal-body">
      <form class="col-12" autocomplete="off">
        <div class="card card-active mb-3" *ngFor="let item of feedBackQuestionList">
          <div class="card-header py-2 bg-primary text-dark bg-opacity-25">
            <h5 class="card-title mb-0">{{item.Group}} - Feedbacks</h5>
          </div>
          <div class="card-body py-2">
            <div class="row mb-3" *ngFor="let question of item.Questions; let i = index">
              <div class="col-lg-7 col-md-6 col-12">
                <b>{{i + 1}}.</b> {{question.Question}}
              </div>
              <div class="col-lg-5 col-md-4 col-12">
                <div [ngSwitch]="question.QuestionType">
                  <div *ngSwitchCase="'Radio'">
                    <div class="input-group">
                      <div class="form-check form-check-inline" *ngFor="let radio of question.Options; let r = index">
                        <input class="form-check-input" type="radio" id="q-ra-{{question.Id}}-{{r}}" name="radio"
                          [value]="radio" [(ngModel)]="question.Answers" [ngModelOptions]="{standalone: true}">
                        <label class="form-check-label" for="q-ra-{{question.Id}}-{{r}}">{{radio}}</label>
                      </div>
                    </div>
                  </div>

                  <div *ngSwitchCase="'Checkbox'">
                    <div class="form-check mb-1" *ngFor="let ckbox of question.Options; let c = index">
                      <input class="form-check-input" type="checkbox" id="q-ck-{{question.Id}}-{{c}}"
                        [checked]="question.Answers.indexOf(ckbox) !== -1"
                        (change)="onChangeCheckBox($event, question, ckbox)">
                      <label class="form-check-label" for="q-ck-{{question.Id}}-{{c}}">{{ckbox}}</label>
                    </div>
                  </div>

                  <div *ngSwitchCase="'Dropdown'">
                    <ng-select class="rounded-2 w-100" [clearable]="false" [(ngModel)]="question.Answers"
                      [ngModelOptions]="{standalone: true}" [clearOnBackspace]="false" [items]="question.Options"
                      placeholder="Select">
                    </ng-select>
                  </div>

                  <div *ngSwitchCase="'Rating'">
                    <div class="input-group">
                      <rating class="fs-3 text-gold" [(ngModel)]="question.Answers"
                        [ngModelOptions]="{standalone: true}" [max]="5" [readonly]="false">
                      </rating>
                    </div>
                  </div>

                  <div *ngSwitchCase="'Textbox'">
                    <div class="input-group">
                      <textarea rows="3" class="form-control px-2" placeholder="Write anser here"
                        [(ngModel)]="question.Answers" [ngModelOptions]="{standalone: true}"></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button class="btn btn-outline-danger me-2" (click)="modalHideFeedBack()">
        <i class="fa fa-close"></i> Close
      </button>
      <button class="btn btn-primary" (click)="onSubmitFeedback()" *ngIf="feedBackQuestionList.length > 0">
        <i class="fa fa-save"></i> Submit
      </button>
    </div>
  </ng-template>

  <ng-template #template>
    <div class="modal-header d-flex justify-content-center">
      <h4 class="modal-title">Exam Result</h4>
    </div>
    <div class="modal-body">
      <div class="table-responsive" *ngIf="result">
        <table class="table table-sm mb-0 table-hover table-borderless">
          <tbody>
            <tr>
              <th>Score</th>
              <td>{{result.Score}} %</td>
            </tr>
            <tr>
              <th>Grade</th>
              <td>{{result.Grade}}</td>
            </tr>
            <tr>
              <th>Result</th>
              <td><span class="badge"
                  [ngClass]="{'bg-success': result.Result === 'Passed', 'bg-danger': result.Result === 'Failed'}">{{result.Result}}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="modal-footer d-flex justify-content-center py-1">
      <button class="btn btn-primary btn-sm" (click)="modalHide()">
        <i class="fa fa-check"></i> OK
      </button>
    </div>
  </ng-template>
</block-ui>