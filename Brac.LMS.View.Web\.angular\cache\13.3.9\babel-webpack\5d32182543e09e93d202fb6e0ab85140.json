{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { ExternsalCoursePreviewRoutes } from './external-course-preview.routing';\nimport { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let ExternsalCoursePreviewModule = /*#__PURE__*/(() => {\n  class ExternsalCoursePreviewModule {}\n\n  ExternsalCoursePreviewModule.ɵfac = function ExternsalCoursePreviewModule_Factory(t) {\n    return new (t || ExternsalCoursePreviewModule)();\n  };\n\n  ExternsalCoursePreviewModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ExternsalCoursePreviewModule\n  });\n  ExternsalCoursePreviewModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(ExternsalCoursePreviewRoutes), SharedModule, WebLayoutModule, NgxExtendedPdfViewerModule]]\n  });\n  return ExternsalCoursePreviewModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}