{"ast": null, "code": "/**\n * @license Angular v13.1.3\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ApplicationInitStatus, APP_INITIALIZER, Injector, setTestabilityGetter, ɵglobal, Injectable, Inject, ViewEncapsulation, APP_ID, RendererStyleFlags2, ErrorHandler, ɵsetDocument, PLATFORM_ID, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵINJECTOR_SCOPE, NgZone, RendererFactory2, Testability, ApplicationModule, NgModule, Optional, SkipSelf, ɵɵinject, ApplicationRef, ɵConsole, forwardRef, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵgetSanitizationBypassType, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Version } from '@angular/core';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n  constructor() {\n    super(...arguments);\n    this.supportsDOMEvents = true;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n\n/* tslint:disable:requireParameterType no-console */\n\n\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n  static makeCurrent() {\n    ɵsetRootDomAdapter(new BrowserDomAdapter());\n  }\n\n  onAndCancel(el, evt, listener) {\n    el.addEventListener(evt, listener, false); // Needed to follow Dart's subscription semantic, until fix of\n    // https://code.google.com/p/dart/issues/detail?id=17406\n\n    return () => {\n      el.removeEventListener(evt, listener, false);\n    };\n  }\n\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n\n  remove(node) {\n    if (node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n  }\n\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n\n  getDefaultDocument() {\n    return document;\n  }\n\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n\n\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n\n    if (target === 'document') {\n      return doc;\n    }\n\n    if (target === 'body') {\n      return doc.body;\n    }\n\n    return null;\n  }\n\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n\n  resetBaseElement() {\n    baseElement = null;\n  }\n\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n\n  getCookie(name) {\n    return ɵparseCookieValue(document.cookie, name);\n  }\n\n}\n\nlet baseElement = null;\n\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n} // based on urlUtils.js in AngularJS 1\n\n\nlet urlParsingNode;\n\nfunction relativePath(url) {\n  urlParsingNode = urlParsingNode || document.createElement('a');\n  urlParsingNode.setAttribute('href', url);\n  const pathName = urlParsingNode.pathname;\n  return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\n\n\nconst TRANSITION_ID = /*#__PURE__*/new InjectionToken('TRANSITION_ID');\n\nfunction appInitializerFactory(transitionId, document, injector) {\n  return () => {\n    // Wait for all application initializers to be completed before removing the styles set by\n    // the server.\n    injector.get(ApplicationInitStatus).donePromise.then(() => {\n      const dom = ɵgetDOM();\n      const styles = document.querySelectorAll(`style[ng-transition=\"${transitionId}\"]`);\n\n      for (let i = 0; i < styles.length; i++) {\n        dom.remove(styles[i]);\n      }\n    });\n  };\n}\n\nconst SERVER_TRANSITION_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: appInitializerFactory,\n  deps: [TRANSITION_ID, DOCUMENT, Injector],\n  multi: true\n}];\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass BrowserGetTestability {\n  static init() {\n    setTestabilityGetter(new BrowserGetTestability());\n  }\n\n  addToWindow(registry) {\n    ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n\n      if (testability == null) {\n        throw new Error('Could not find testability for element.');\n      }\n\n      return testability;\n    };\n\n    ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n\n    ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n\n    const whenAllStable = (callback\n    /** TODO #9100 */\n    ) => {\n      const testabilities = ɵglobal['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      let didWork = false;\n\n      const decrement = function (didWork_\n      /** TODO #9100 */\n      ) {\n        didWork = didWork || didWork_;\n        count--;\n\n        if (count == 0) {\n          callback(didWork);\n        }\n      };\n\n      testabilities.forEach(function (testability\n      /** TODO #9100 */\n      ) {\n        testability.whenStable(decrement);\n      });\n    };\n\n    if (!ɵglobal['frameworkStabilizers']) {\n      ɵglobal['frameworkStabilizers'] = [];\n    }\n\n    ɵglobal['frameworkStabilizers'].push(whenAllStable);\n  }\n\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n\n    const t = registry.getTestability(elem);\n\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n\n    if (ɵgetDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n\n}\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\n\n\nlet BrowserXhr = /*#__PURE__*/(() => {\n  class BrowserXhr {\n    build() {\n      return new XMLHttpRequest();\n    }\n\n  }\n\n  BrowserXhr.ɵfac = function BrowserXhr_Factory(t) {\n    return new (t || BrowserXhr)();\n  };\n\n  BrowserXhr.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BrowserXhr,\n    factory: BrowserXhr.ɵfac\n  });\n  return BrowserXhr;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\n\n\nconst EVENT_MANAGER_PLUGINS = /*#__PURE__*/new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\n\nlet EventManager = /*#__PURE__*/(() => {\n  class EventManager {\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n      this._zone = _zone;\n      this._eventNameToPlugin = new Map();\n      plugins.forEach(p => p.manager = this);\n      this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n\n\n    addEventListener(element, eventName, handler) {\n      const plugin = this._findPluginFor(eventName);\n\n      return plugin.addEventListener(element, eventName, handler);\n    }\n    /**\n     * Registers a global handler for an event in a target view.\n     *\n     * @param target A target for global event notifications. One of \"window\", \"document\", or \"body\".\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns A callback function that can be used to remove the handler.\n     * @deprecated No longer being used in Ivy code. To be removed in version 14.\n     */\n\n\n    addGlobalEventListener(target, eventName, handler) {\n      const plugin = this._findPluginFor(eventName);\n\n      return plugin.addGlobalEventListener(target, eventName, handler);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n\n\n    getZone() {\n      return this._zone;\n    }\n    /** @internal */\n\n\n    _findPluginFor(eventName) {\n      const plugin = this._eventNameToPlugin.get(eventName);\n\n      if (plugin) {\n        return plugin;\n      }\n\n      const plugins = this._plugins;\n\n      for (let i = 0; i < plugins.length; i++) {\n        const plugin = plugins[i];\n\n        if (plugin.supports(eventName)) {\n          this._eventNameToPlugin.set(eventName, plugin);\n\n          return plugin;\n        }\n      }\n\n      throw new Error(`No event manager plugin found for event ${eventName}`);\n    }\n\n  }\n\n  EventManager.ɵfac = function EventManager_Factory(t) {\n    return new (t || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n  };\n\n  EventManager.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: EventManager,\n    factory: EventManager.ɵfac\n  });\n  return EventManager;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nclass EventManagerPlugin {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n\n  addGlobalEventListener(element, eventName, handler) {\n    const target = ɵgetDOM().getGlobalEventTarget(this._doc, element);\n\n    if (!target) {\n      throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n    }\n\n    return this.addEventListener(target, eventName, handler);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet SharedStylesHost = /*#__PURE__*/(() => {\n  class SharedStylesHost {\n    constructor() {\n      /** @internal */\n      this._stylesSet = new Set();\n    }\n\n    addStyles(styles) {\n      const additions = new Set();\n      styles.forEach(style => {\n        if (!this._stylesSet.has(style)) {\n          this._stylesSet.add(style);\n\n          additions.add(style);\n        }\n      });\n      this.onStylesAdded(additions);\n    }\n\n    onStylesAdded(additions) {}\n\n    getAllStyles() {\n      return Array.from(this._stylesSet);\n    }\n\n  }\n\n  SharedStylesHost.ɵfac = function SharedStylesHost_Factory(t) {\n    return new (t || SharedStylesHost)();\n  };\n\n  SharedStylesHost.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SharedStylesHost,\n    factory: SharedStylesHost.ɵfac\n  });\n  return SharedStylesHost;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet DomSharedStylesHost = /*#__PURE__*/(() => {\n  class DomSharedStylesHost extends SharedStylesHost {\n    constructor(_doc) {\n      super();\n      this._doc = _doc; // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n\n      this._hostNodes = new Map();\n\n      this._hostNodes.set(_doc.head, []);\n    }\n\n    _addStylesToHost(styles, host, styleNodes) {\n      styles.forEach(style => {\n        const styleEl = this._doc.createElement('style');\n\n        styleEl.textContent = style;\n        styleNodes.push(host.appendChild(styleEl));\n      });\n    }\n\n    addHost(hostNode) {\n      const styleNodes = [];\n\n      this._addStylesToHost(this._stylesSet, hostNode, styleNodes);\n\n      this._hostNodes.set(hostNode, styleNodes);\n    }\n\n    removeHost(hostNode) {\n      const styleNodes = this._hostNodes.get(hostNode);\n\n      if (styleNodes) {\n        styleNodes.forEach(removeStyle);\n      }\n\n      this._hostNodes.delete(hostNode);\n    }\n\n    onStylesAdded(additions) {\n      this._hostNodes.forEach((styleNodes, hostNode) => {\n        this._addStylesToHost(additions, hostNode, styleNodes);\n      });\n    }\n\n    ngOnDestroy() {\n      this._hostNodes.forEach(styleNodes => styleNodes.forEach(removeStyle));\n    }\n\n  }\n\n  DomSharedStylesHost.ɵfac = function DomSharedStylesHost_Factory(t) {\n    return new (t || DomSharedStylesHost)(i0.ɵɵinject(DOCUMENT));\n  };\n\n  DomSharedStylesHost.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSharedStylesHost,\n    factory: DomSharedStylesHost.ɵfac\n  });\n  return DomSharedStylesHost;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction removeStyle(styleNode) {\n  ɵgetDOM().remove(styleNode);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n\nfunction flattenStyles(compId, styles, target) {\n  for (let i = 0; i < styles.length; i++) {\n    let style = styles[i];\n\n    if (Array.isArray(style)) {\n      flattenStyles(compId, style, target);\n    } else {\n      style = style.replace(COMPONENT_REGEX, compId);\n      target.push(style);\n    }\n  }\n\n  return target;\n}\n\nfunction decoratePreventDefault(eventHandler) {\n  // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n  // decoratePreventDefault or is a listener added outside the Angular context so it can handle the\n  // two differently. In the first case, the special '__ngUnwrap__' token is passed to the unwrap\n  // the listener (see below).\n  return event => {\n    // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n    // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The debug_node\n    // can inspect the listener toString contents for the existence of this special token. Because\n    // the token is a string literal, it is ensured to not be modified by compiled code.\n    if (event === '__ngUnwrap__') {\n      return eventHandler;\n    }\n\n    const allowDefaultBehavior = eventHandler(event);\n\n    if (allowDefaultBehavior === false) {\n      // TODO(tbosch): move preventDefault into event plugins...\n      event.preventDefault();\n      event.returnValue = false;\n    }\n\n    return undefined;\n  };\n}\n\nlet hasLoggedNativeEncapsulationWarning = false;\nlet DomRendererFactory2 = /*#__PURE__*/(() => {\n  class DomRendererFactory2 {\n    constructor(eventManager, sharedStylesHost, appId) {\n      this.eventManager = eventManager;\n      this.sharedStylesHost = sharedStylesHost;\n      this.appId = appId;\n      this.rendererByCompId = new Map();\n      this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n    }\n\n    createRenderer(element, type) {\n      if (!element || !type) {\n        return this.defaultRenderer;\n      }\n\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          {\n            let renderer = this.rendererByCompId.get(type.id);\n\n            if (!renderer) {\n              renderer = new EmulatedEncapsulationDomRenderer2(this.eventManager, this.sharedStylesHost, type, this.appId);\n              this.rendererByCompId.set(type.id, renderer);\n            }\n\n            renderer.applyToHost(element);\n            return renderer;\n          }\n        // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an enum\n        // value that is not known (but previously was the value for ViewEncapsulation.Native)\n\n        case 1:\n        case ViewEncapsulation.ShadowDom:\n          // TODO(FW-2290): remove the `case 1:` fallback logic and the warning in v12.\n          if ((typeof ngDevMode === 'undefined' || ngDevMode) && // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an\n          // enum value that is not known (but previously was the value for\n          // ViewEncapsulation.Native)\n          !hasLoggedNativeEncapsulationWarning && type.encapsulation === 1) {\n            hasLoggedNativeEncapsulationWarning = true;\n            console.warn('ViewEncapsulation.Native is no longer supported. Falling back to ViewEncapsulation.ShadowDom. The fallback will be removed in v12.');\n          }\n\n          return new ShadowDomRenderer(this.eventManager, this.sharedStylesHost, element, type);\n\n        default:\n          {\n            if (!this.rendererByCompId.has(type.id)) {\n              const styles = flattenStyles(type.id, type.styles, []);\n              this.sharedStylesHost.addStyles(styles);\n              this.rendererByCompId.set(type.id, this.defaultRenderer);\n            }\n\n            return this.defaultRenderer;\n          }\n      }\n    }\n\n    begin() {}\n\n    end() {}\n\n  }\n\n  DomRendererFactory2.ɵfac = function DomRendererFactory2_Factory(t) {\n    return new (t || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(DomSharedStylesHost), i0.ɵɵinject(APP_ID));\n  };\n\n  DomRendererFactory2.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomRendererFactory2,\n    factory: DomRendererFactory2.ɵfac\n  });\n  return DomRendererFactory2;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nclass DefaultDomRenderer2 {\n  constructor(eventManager) {\n    this.eventManager = eventManager;\n    this.data = Object.create(null);\n    this.destroyNode = null;\n  }\n\n  destroy() {}\n\n  createElement(name, namespace) {\n    if (namespace) {\n      // In cases where Ivy (not ViewEngine) is giving us the actual namespace, the look up by key\n      // will result in undefined, so we just return the namespace here.\n      return document.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n\n    return document.createElement(name);\n  }\n\n  createComment(value) {\n    return document.createComment(value);\n  }\n\n  createText(value) {\n    return document.createTextNode(value);\n  }\n\n  appendChild(parent, newChild) {\n    parent.appendChild(newChild);\n  }\n\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      parent.insertBefore(newChild, refChild);\n    }\n  }\n\n  removeChild(parent, oldChild) {\n    if (parent) {\n      parent.removeChild(oldChild);\n    }\n  }\n\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) : selectorOrNode;\n\n    if (!el) {\n      throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n\n    return el;\n  }\n\n  parentNode(node) {\n    return node.parentNode;\n  }\n\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name; // TODO(FW-811): Ivy may cause issues here because it's passing around\n      // full URIs for namespaces, therefore this lookup will fail.\n\n      const namespaceUri = NAMESPACE_URIS[namespace];\n\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      // TODO(FW-811): Ivy may cause issues here because it's passing around\n      // full URIs for namespaces, therefore this lookup will fail.\n      const namespaceUri = NAMESPACE_URIS[namespace];\n\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        // TODO(FW-811): Since ivy is passing around full URIs for namespaces\n        // this could result in properties like `http://www.w3.org/2000/svg:cx=\"123\"`,\n        // which is wrong.\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      el.style.removeProperty(style);\n    } else {\n      // IE requires '' instead of null\n      // see https://github.com/angular/angular/issues/7916\n      el.style[style] = '';\n    }\n  }\n\n  setProperty(el, name, value) {\n    NG_DEV_MODE && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n\n  listen(target, event, callback) {\n    NG_DEV_MODE && checkNoSyntheticProp(event, 'listener');\n\n    if (typeof target === 'string') {\n      return this.eventManager.addGlobalEventListener(target, event, decoratePreventDefault(callback));\n    }\n\n    return this.eventManager.addEventListener(target, event, decoratePreventDefault(callback));\n  }\n\n}\n\nconst AT_CHARCODE = /*#__PURE__*/(() => '@'.charCodeAt(0))();\n\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new Error(`Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n  }\n}\n\nclass EmulatedEncapsulationDomRenderer2 extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, component, appId) {\n    super(eventManager);\n    this.component = component;\n    const styles = flattenStyles(appId + '-' + component.id, component.styles, []);\n    sharedStylesHost.addStyles(styles);\n    this.contentAttr = shimContentAttribute(appId + '-' + component.id);\n    this.hostAttr = shimHostAttribute(appId + '-' + component.id);\n  }\n\n  applyToHost(element) {\n    super.setAttribute(element, this.hostAttr, '');\n  }\n\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n\n}\n\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, hostEl, component) {\n    super(eventManager);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = flattenStyles(component.id, component.styles, []);\n\n    for (let i = 0; i < styles.length; i++) {\n      const styleEl = document.createElement('style');\n      styleEl.textContent = styles[i];\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n\n  removeChild(parent, oldChild) {\n    return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n  }\n\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet DomEventsPlugin = /*#__PURE__*/(() => {\n  class DomEventsPlugin extends EventManagerPlugin {\n    constructor(doc) {\n      super(doc);\n    } // This plugin should come last in the list of plugins, because it accepts all\n    // events.\n\n\n    supports(eventName) {\n      return true;\n    }\n\n    addEventListener(element, eventName, handler) {\n      element.addEventListener(eventName, handler, false);\n      return () => this.removeEventListener(element, eventName, handler);\n    }\n\n    removeEventListener(target, eventName, callback) {\n      return target.removeEventListener(eventName, callback);\n    }\n\n  }\n\n  DomEventsPlugin.ɵfac = function DomEventsPlugin_Factory(t) {\n    return new (t || DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n  };\n\n  DomEventsPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomEventsPlugin,\n    factory: DomEventsPlugin.ɵfac\n  });\n  return DomEventsPlugin;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Defines supported modifiers for key events.\n */\n\n\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\nconst DOM_KEY_LOCATION_NUMPAD = 3; // Map to convert some key or keyIdentifier values to what will be returned by getEventKey\n\nconst _keyMap = {\n  // The following values are here for cross-browser compatibility and to match the W3C standard\n  // cf https://www.w3.org/TR/DOM-Level-3-Events-key/\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n}; // There is a bug in Chrome for numeric keypad keys:\n// https://code.google.com/p/chromium/issues/detail?id=155654\n// 1, 2, 3 ... are reported as A, B, C ...\n\nconst _chromeNumKeyPadMap = {\n  'A': '1',\n  'B': '2',\n  'C': '3',\n  'D': '4',\n  'E': '5',\n  'F': '6',\n  'G': '7',\n  'H': '8',\n  'I': '9',\n  'J': '*',\n  'K': '+',\n  'M': '-',\n  'N': '.',\n  'O': '/',\n  '\\x60': '0',\n  '\\x90': 'NumLock'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\n\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\n\nlet KeyEventsPlugin = /*#__PURE__*/(() => {\n  class KeyEventsPlugin extends EventManagerPlugin {\n    /**\n     * Initializes an instance of the browser plug-in.\n     * @param doc The document in which key events will be detected.\n     */\n    constructor(doc) {\n      super(doc);\n    }\n    /**\n     * Reports whether a named key event is supported.\n     * @param eventName The event name to query.\n     * @return True if the named key event is supported.\n     */\n\n\n    supports(eventName) {\n      return KeyEventsPlugin.parseEventName(eventName) != null;\n    }\n    /**\n     * Registers a handler for a specific element and key event.\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the key event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns The key event that was registered.\n     */\n\n\n    addEventListener(element, eventName, handler) {\n      const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n      const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n      return this.manager.getZone().runOutsideAngular(() => {\n        return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n      });\n    }\n\n    static parseEventName(eventName) {\n      const parts = eventName.toLowerCase().split('.');\n      const domEventName = parts.shift();\n\n      if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n        return null;\n      }\n\n      const key = KeyEventsPlugin._normalizeKey(parts.pop());\n\n      let fullKey = '';\n      MODIFIER_KEYS.forEach(modifierName => {\n        const index = parts.indexOf(modifierName);\n\n        if (index > -1) {\n          parts.splice(index, 1);\n          fullKey += modifierName + '.';\n        }\n      });\n      fullKey += key;\n\n      if (parts.length != 0 || key.length === 0) {\n        // returning null instead of throwing to let another plugin process the event\n        return null;\n      } // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n      //       The code must remain in the `result['domEventName']` form.\n      // return {domEventName, fullKey};\n\n\n      const result = {};\n      result['domEventName'] = domEventName;\n      result['fullKey'] = fullKey;\n      return result;\n    }\n\n    static getEventFullKey(event) {\n      let fullKey = '';\n      let key = getEventKey(event);\n      key = key.toLowerCase();\n\n      if (key === ' ') {\n        key = 'space'; // for readability\n      } else if (key === '.') {\n        key = 'dot'; // because '.' is used as a separator in event names\n      }\n\n      MODIFIER_KEYS.forEach(modifierName => {\n        if (modifierName != key) {\n          const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n\n          if (modifierGetter(event)) {\n            fullKey += modifierName + '.';\n          }\n        }\n      });\n      fullKey += key;\n      return fullKey;\n    }\n    /**\n     * Configures a handler callback for a key event.\n     * @param fullKey The event name that combines all simultaneous keystrokes.\n     * @param handler The function that responds to the key event.\n     * @param zone The zone in which the event occurred.\n     * @returns A callback function.\n     */\n\n\n    static eventCallback(fullKey, handler, zone) {\n      return (event\n      /** TODO #9100 */\n      ) => {\n        if (KeyEventsPlugin.getEventFullKey(event) === fullKey) {\n          zone.runGuarded(() => handler(event));\n        }\n      };\n    }\n    /** @internal */\n\n\n    static _normalizeKey(keyName) {\n      // TODO: switch to a Map if the mapping grows too much\n      switch (keyName) {\n        case 'esc':\n          return 'escape';\n\n        default:\n          return keyName;\n      }\n    }\n\n  }\n\n  KeyEventsPlugin.ɵfac = function KeyEventsPlugin_Factory(t) {\n    return new (t || KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n  };\n\n  KeyEventsPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: KeyEventsPlugin,\n    factory: KeyEventsPlugin.ɵfac\n  });\n  return KeyEventsPlugin;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction getEventKey(event) {\n  let key = event.key;\n\n  if (key == null) {\n    key = event.keyIdentifier; // keyIdentifier is defined in the old draft of DOM Level 3 Events implemented by Chrome and\n    // Safari cf\n    // https://www.w3.org/TR/2007/WD-DOM-Level-3-Events-20071221/events.html#Events-KeyboardEvents-Interfaces\n\n    if (key == null) {\n      return 'Unidentified';\n    }\n\n    if (key.startsWith('U+')) {\n      key = String.fromCharCode(parseInt(key.substring(2), 16));\n\n      if (event.location === DOM_KEY_LOCATION_NUMPAD && _chromeNumKeyPadMap.hasOwnProperty(key)) {\n        // There is a bug in Chrome for numeric keypad keys:\n        // https://code.google.com/p/chromium/issues/detail?id=155654\n        // 1, 2, 3 ... are reported as A, B, C ...\n        key = _chromeNumKeyPadMap[key];\n      }\n    }\n  }\n\n  return _keyMap[key] || key;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n  BrowserGetTestability.init();\n}\n\nfunction errorHandler() {\n  return new ErrorHandler();\n}\n\nfunction _document() {\n  // Tell ivy about the global document\n  ɵsetDocument(document);\n  return document;\n}\n\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document,\n  deps: []\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\n\nconst platformBrowser = /*#__PURE__*/createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: ɵINJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler,\n  deps: []\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT, NgZone, PLATFORM_ID]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, {\n  provide: DomRendererFactory2,\n  useClass: DomRendererFactory2,\n  deps: [EventManager, DomSharedStylesHost, APP_ID]\n}, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: SharedStylesHost,\n  useExisting: DomSharedStylesHost\n}, {\n  provide: DomSharedStylesHost,\n  useClass: DomSharedStylesHost,\n  deps: [DOCUMENT]\n}, {\n  provide: Testability,\n  useClass: Testability,\n  deps: [NgZone]\n}, {\n  provide: EventManager,\n  useClass: EventManager,\n  deps: [EVENT_MANAGER_PLUGINS, NgZone]\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr,\n  deps: []\n}];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\n\nlet BrowserModule = /*#__PURE__*/(() => {\n  class BrowserModule {\n    constructor(parentModule) {\n      if (parentModule) {\n        throw new Error(`BrowserModule has already been loaded. If you need access to common directives such as NgIf and NgFor from a lazy loaded module, import CommonModule instead.`);\n      }\n    }\n    /**\n     * Configures a browser-based app to transition from a server-rendered app, if\n     * one is present on the page.\n     *\n     * @param params An object containing an identifier for the app to transition.\n     * The ID must match between the client and server versions of the app.\n     * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n     */\n\n\n    static withServerTransition(params) {\n      return {\n        ngModule: BrowserModule,\n        providers: [{\n          provide: APP_ID,\n          useValue: params.appId\n        }, {\n          provide: TRANSITION_ID,\n          useExisting: APP_ID\n        }, SERVER_TRANSITION_PROVIDERS]\n      };\n    }\n\n  }\n\n  BrowserModule.ɵfac = function BrowserModule_Factory(t) {\n    return new (t || BrowserModule)(i0.ɵɵinject(BrowserModule, 12));\n  };\n\n  BrowserModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BrowserModule\n  });\n  BrowserModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: BROWSER_MODULE_PROVIDERS,\n    imports: [CommonModule, ApplicationModule]\n  });\n  return BrowserModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\n\n\nfunction createMeta() {\n  return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\n\n\nlet Meta = /*#__PURE__*/(() => {\n  class Meta {\n    constructor(_doc) {\n      this._doc = _doc;\n      this._dom = ɵgetDOM();\n    }\n    /**\n     * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * If an existing element is found, it is returned and is not modified in any way.\n     * @param tag The definition of a `<meta>` element to match or create.\n     * @param forceCreation True to create a new element without checking whether one already exists.\n     * @returns The existing element with the same attributes and values if found,\n     * the new element if no match is found, or `null` if the tag parameter is not defined.\n     */\n\n\n    addTag(tag, forceCreation = false) {\n      if (!tag) return null;\n      return this._getOrCreateElement(tag, forceCreation);\n    }\n    /**\n     * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * @param tags An array of tag definitions to match or create.\n     * @param forceCreation True to create new elements without checking whether they already exist.\n     * @returns The matching elements if found, or the new elements.\n     */\n\n\n    addTags(tags, forceCreation = false) {\n      if (!tags) return [];\n      return tags.reduce((result, tag) => {\n        if (tag) {\n          result.push(this._getOrCreateElement(tag, forceCreation));\n        }\n\n        return result;\n      }, []);\n    }\n    /**\n     * Retrieves a `<meta>` tag element in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching element, if any.\n     */\n\n\n    getTag(attrSelector) {\n      if (!attrSelector) return null;\n      return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n    }\n    /**\n     * Retrieves a set of `<meta>` tag elements in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching elements, if any.\n     */\n\n\n    getTags(attrSelector) {\n      if (!attrSelector) return [];\n\n      const list\n      /*NodeList*/\n      = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n\n      return list ? [].slice.call(list) : [];\n    }\n    /**\n     * Modifies an existing `<meta>` tag element in the current HTML document.\n     * @param tag The tag description with which to replace the existing tag content.\n     * @param selector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n     * replacement tag.\n     * @return The modified element.\n     */\n\n\n    updateTag(tag, selector) {\n      if (!tag) return null;\n      selector = selector || this._parseSelector(tag);\n      const meta = this.getTag(selector);\n\n      if (meta) {\n        return this._setMetaElementAttributes(tag, meta);\n      }\n\n      return this._getOrCreateElement(tag, true);\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param attrSelector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     */\n\n\n    removeTag(attrSelector) {\n      this.removeTagElement(this.getTag(attrSelector));\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param meta The tag definition to match against to identify an existing tag.\n     */\n\n\n    removeTagElement(meta) {\n      if (meta) {\n        this._dom.remove(meta);\n      }\n    }\n\n    _getOrCreateElement(meta, forceCreation = false) {\n      if (!forceCreation) {\n        const selector = this._parseSelector(meta); // It's allowed to have multiple elements with the same name so it's not enough to\n        // just check that element with the same name already present on the page. We also need to\n        // check if element has tag attributes\n\n\n        const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n        if (elem !== undefined) return elem;\n      }\n\n      const element = this._dom.createElement('meta');\n\n      this._setMetaElementAttributes(meta, element);\n\n      const head = this._doc.getElementsByTagName('head')[0];\n\n      head.appendChild(element);\n      return element;\n    }\n\n    _setMetaElementAttributes(tag, el) {\n      Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n      return el;\n    }\n\n    _parseSelector(tag) {\n      const attr = tag.name ? 'name' : 'property';\n      return `${attr}=\"${tag[attr]}\"`;\n    }\n\n    _containsAttributes(tag, elem) {\n      return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n    }\n\n    _getMetaKeyMap(prop) {\n      return META_KEYS_MAP[prop] || prop;\n    }\n\n  }\n\n  Meta.ɵfac = function Meta_Factory(t) {\n    return new (t || Meta)(i0.ɵɵinject(DOCUMENT));\n  };\n\n  Meta.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Meta,\n    factory: function Meta_Factory(t) {\n      let r = null;\n\n      if (t) {\n        r = new t();\n      } else {\n        r = createMeta();\n      }\n\n      return r;\n    },\n    providedIn: 'root'\n  });\n  return Meta;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\n\n\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Factory to create Title service.\n */\n\nfunction createTitle() {\n  return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\n\n\nlet Title = /*#__PURE__*/(() => {\n  class Title {\n    constructor(_doc) {\n      this._doc = _doc;\n    }\n    /**\n     * Get the title of the current HTML document.\n     */\n\n\n    getTitle() {\n      return this._doc.title;\n    }\n    /**\n     * Set the title of the current HTML document.\n     * @param newTitle\n     */\n\n\n    setTitle(newTitle) {\n      this._doc.title = newTitle || '';\n    }\n\n  }\n\n  Title.ɵfac = function Title_Factory(t) {\n    return new (t || Title)(i0.ɵɵinject(DOCUMENT));\n  };\n\n  Title.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Title,\n    factory: function Title_Factory(t) {\n      let r = null;\n\n      if (t) {\n        r = new t();\n      } else {\n        r = createTitle();\n      }\n\n      return r;\n    },\n    providedIn: 'root'\n  });\n  return Title;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst CAMEL_CASE_REGEXP = /([A-Z])/g;\nconst DASH_CASE_REGEXP = /-([a-z])/g;\n\nfunction camelCaseToDashCase(input) {\n  return input.replace(CAMEL_CASE_REGEXP, (...m) => '-' + m[1].toLowerCase());\n}\n\nfunction dashCaseToCamelCase(input) {\n  return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\n\n\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n    ng[name] = value;\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst win = typeof window !== 'undefined' && window || {};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass ChangeDetectionPerfRecord {\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\n\n\nclass AngularProfiler {\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  } // tslint:disable:no-console\n\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n\n\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection'; // Profiler is not available in Android browsers without dev tools opened\n\n    const isProfilerAvailable = win.console.profile != null;\n\n    if (record && isProfilerAvailable) {\n      win.console.profile(profileName);\n    }\n\n    const start = performanceNow();\n    let numTicks = 0;\n\n    while (numTicks < 5 || performanceNow() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n\n    const end = performanceNow();\n\n    if (record && isProfilerAvailable) {\n      win.console.profileEnd(profileName);\n    }\n\n    const msPerTick = (end - start) / numTicks;\n    win.console.log(`ran ${numTicks} change detection cycles`);\n    win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n\n}\n\nfunction performanceNow() {\n  return win.performance && win.performance.now ? win.performance.now() : new Date().getTime();\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\n\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\n\n\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction escapeHtml(text) {\n  const escapedText = {\n    '&': '&a;',\n    '\"': '&q;',\n    '\\'': '&s;',\n    '<': '&l;',\n    '>': '&g;'\n  };\n  return text.replace(/[&\"'<>]/g, s => escapedText[s]);\n}\n\nfunction unescapeHtml(text) {\n  const unescapedText = {\n    '&a;': '&',\n    '&q;': '\"',\n    '&s;': '\\'',\n    '&l;': '<',\n    '&g;': '>'\n  };\n  return text.replace(/&[^;]+;/g, s => unescapedText[s]);\n}\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n */\n\n\nfunction makeStateKey(key) {\n  return key;\n}\n/**\n * A key value store that is transferred from the application on the server side to the application\n * on the client side.\n *\n * `TransferState` will be available as an injectable token. To use it import\n * `ServerTransferStateModule` on the server and `BrowserTransferStateModule` on the client.\n *\n * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only\n * boolean, number, string, null and non-class objects will be serialized and deserialized in a\n * non-lossy manner.\n *\n * @publicApi\n */\n\n\nlet TransferState = /*#__PURE__*/(() => {\n  class TransferState {\n    constructor() {\n      this.store = {};\n      this.onSerializeCallbacks = {};\n    }\n    /** @internal */\n\n\n    static init(initState) {\n      const transferState = new TransferState();\n      transferState.store = initState;\n      return transferState;\n    }\n    /**\n     * Get the value corresponding to a key. Return `defaultValue` if key is not found.\n     */\n\n\n    get(key, defaultValue) {\n      return this.store[key] !== undefined ? this.store[key] : defaultValue;\n    }\n    /**\n     * Set the value corresponding to a key.\n     */\n\n\n    set(key, value) {\n      this.store[key] = value;\n    }\n    /**\n     * Remove a key from the store.\n     */\n\n\n    remove(key) {\n      delete this.store[key];\n    }\n    /**\n     * Test whether a key exists in the store.\n     */\n\n\n    hasKey(key) {\n      return this.store.hasOwnProperty(key);\n    }\n    /**\n     * Register a callback to provide the value for a key when `toJson` is called.\n     */\n\n\n    onSerialize(key, callback) {\n      this.onSerializeCallbacks[key] = callback;\n    }\n    /**\n     * Serialize the current state of the store to JSON.\n     */\n\n\n    toJson() {\n      // Call the onSerialize callbacks and put those values into the store.\n      for (const key in this.onSerializeCallbacks) {\n        if (this.onSerializeCallbacks.hasOwnProperty(key)) {\n          try {\n            this.store[key] = this.onSerializeCallbacks[key]();\n          } catch (e) {\n            console.warn('Exception in onSerialize callback: ', e);\n          }\n        }\n      }\n\n      return JSON.stringify(this.store);\n    }\n\n  }\n\n  TransferState.ɵfac = function TransferState_Factory(t) {\n    return new (t || TransferState)();\n  };\n\n  TransferState.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TransferState,\n    factory: TransferState.ɵfac\n  });\n  return TransferState;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction initTransferState(doc, appId) {\n  // Locate the script tag with the JSON data transferred from the server.\n  // The id of the script tag is set to the Angular appId + 'state'.\n  const script = doc.getElementById(appId + '-state');\n  let initialState = {};\n\n  if (script && script.textContent) {\n    try {\n      // Avoid using any here as it triggers lint errors in google3 (any is not allowed).\n      initialState = JSON.parse(unescapeHtml(script.textContent));\n    } catch (e) {\n      console.warn('Exception while restoring TransferState for app ' + appId, e);\n    }\n  }\n\n  return TransferState.init(initialState);\n}\n/**\n * NgModule to install on the client side while using the `TransferState` to transfer state from\n * server to client.\n *\n * @publicApi\n */\n\n\nlet BrowserTransferStateModule = /*#__PURE__*/(() => {\n  class BrowserTransferStateModule {}\n\n  BrowserTransferStateModule.ɵfac = function BrowserTransferStateModule_Factory(t) {\n    return new (t || BrowserTransferStateModule)();\n  };\n\n  BrowserTransferStateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BrowserTransferStateModule\n  });\n  BrowserTransferStateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: TransferState,\n      useFactory: initTransferState,\n      deps: [DOCUMENT, APP_ID]\n    }]\n  });\n  return BrowserTransferStateModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\n\n\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n\n\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n\n\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n\n}\n\nfunction elementMatches(n, selector) {\n  if (ɵgetDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n\n  return false;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Supported HammerJS recognizer event names.\n */\n\n\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see `HammerGestureConfig`\n *\n * @ngModule HammerModule\n * @publicApi\n */\n\nconst HAMMER_GESTURE_CONFIG = /*#__PURE__*/new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\n\nconst HAMMER_LOADER = /*#__PURE__*/new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\n\nlet HammerGestureConfig = /*#__PURE__*/(() => {\n  class HammerGestureConfig {\n    constructor() {\n      /**\n       * A set of supported event names for gestures to be used in Angular.\n       * Angular supports all built-in recognizers, as listed in\n       * [HammerJS documentation](https://hammerjs.github.io/).\n       */\n      this.events = [];\n      /**\n       * Maps gesture event names to a set of configuration options\n       * that specify overrides to the default values for specific properties.\n       *\n       * The key is a supported event name to be configured,\n       * and the options object contains a set of properties, with override values\n       * to be applied to the named recognizer event.\n       * For example, to disable recognition of the rotate event, specify\n       *  `{\"rotate\": {\"enable\": false}}`.\n       *\n       * Properties that are not present take the HammerJS default values.\n       * For information about which properties are supported for which events,\n       * and their allowed and default values, see\n       * [HammerJS documentation](https://hammerjs.github.io/).\n       *\n       */\n\n      this.overrides = {};\n    }\n    /**\n     * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n     * and attaches it to a given HTML element.\n     * @param element The element that will recognize gestures.\n     * @returns A HammerJS event-manager object.\n     */\n\n\n    buildHammer(element) {\n      const mc = new Hammer(element, this.options);\n      mc.get('pinch').set({\n        enable: true\n      });\n      mc.get('rotate').set({\n        enable: true\n      });\n\n      for (const eventName in this.overrides) {\n        mc.get(eventName).set(this.overrides[eventName]);\n      }\n\n      return mc;\n    }\n\n  }\n\n  HammerGestureConfig.ɵfac = function HammerGestureConfig_Factory(t) {\n    return new (t || HammerGestureConfig)();\n  };\n\n  HammerGestureConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HammerGestureConfig,\n    factory: HammerGestureConfig.ɵfac\n  });\n  return HammerGestureConfig;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\n\n\nlet HammerGesturesPlugin = /*#__PURE__*/(() => {\n  class HammerGesturesPlugin extends EventManagerPlugin {\n    constructor(doc, _config, console, loader) {\n      super(doc);\n      this._config = _config;\n      this.console = console;\n      this.loader = loader;\n      this._loaderPromise = null;\n    }\n\n    supports(eventName) {\n      if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n        return false;\n      }\n\n      if (!window.Hammer && !this.loader) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n        }\n\n        return false;\n      }\n\n      return true;\n    }\n\n    addEventListener(element, eventName, handler) {\n      const zone = this.manager.getZone();\n      eventName = eventName.toLowerCase(); // If Hammer is not present but a loader is specified, we defer adding the event listener\n      // until Hammer is loaded.\n\n      if (!window.Hammer && this.loader) {\n        this._loaderPromise = this._loaderPromise || this.loader(); // This `addEventListener` method returns a function to remove the added listener.\n        // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n        // than remove anything.\n\n        let cancelRegistration = false;\n\n        let deregister = () => {\n          cancelRegistration = true;\n        };\n\n        this._loaderPromise.then(() => {\n          // If Hammer isn't actually loaded when the custom loader resolves, give up.\n          if (!window.Hammer) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n              this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n            }\n\n            deregister = () => {};\n\n            return;\n          }\n\n          if (!cancelRegistration) {\n            // Now that Hammer is loaded and the listener is being loaded for real,\n            // the deregistration function changes from canceling registration to removal.\n            deregister = this.addEventListener(element, eventName, handler);\n          }\n        }).catch(() => {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n          }\n\n          deregister = () => {};\n        }); // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n        // can change the behavior of `deregister` once the listener is added. Using a closure in\n        // this way allows us to avoid any additional data structures to track listener removal.\n\n\n        return () => {\n          deregister();\n        };\n      }\n\n      return zone.runOutsideAngular(() => {\n        // Creating the manager bind events, must be done outside of angular\n        const mc = this._config.buildHammer(element);\n\n        const callback = function (eventObj) {\n          zone.runGuarded(function () {\n            handler(eventObj);\n          });\n        };\n\n        mc.on(eventName, callback);\n        return () => {\n          mc.off(eventName, callback); // destroy mc to prevent memory leak\n\n          if (typeof mc.destroy === 'function') {\n            mc.destroy();\n          }\n        };\n      });\n    }\n\n    isCustomEvent(eventName) {\n      return this._config.events.indexOf(eventName) > -1;\n    }\n\n  }\n\n  HammerGesturesPlugin.ɵfac = function HammerGesturesPlugin_Factory(t) {\n    return new (t || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.ɵConsole), i0.ɵɵinject(HAMMER_LOADER, 8));\n  };\n\n  HammerGesturesPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HammerGesturesPlugin,\n    factory: HammerGesturesPlugin.ɵfac\n  });\n  return HammerGesturesPlugin;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\n\n\nlet HammerModule = /*#__PURE__*/(() => {\n  class HammerModule {}\n\n  HammerModule.ɵfac = function HammerModule_Factory(t) {\n    return new (t || HammerModule)();\n  };\n\n  HammerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HammerModule\n  });\n  HammerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: EVENT_MANAGER_PLUGINS,\n      useClass: HammerGesturesPlugin,\n      multi: true,\n      deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n    }, {\n      provide: HAMMER_GESTURE_CONFIG,\n      useClass: HammerGestureConfig,\n      deps: []\n    }]\n  });\n  return HammerModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\n\n\nlet DomSanitizer = /*#__PURE__*/(() => {\n  class DomSanitizer {}\n\n  DomSanitizer.ɵfac = function DomSanitizer_Factory(t) {\n    return new (t || DomSanitizer)();\n  };\n\n  DomSanitizer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSanitizer,\n    factory: function DomSanitizer_Factory(t) {\n      let r = null;\n\n      if (t) {\n        r = new (t || DomSanitizer)();\n      } else {\n        r = i0.ɵɵinject(DomSanitizerImpl);\n      }\n\n      return r;\n    },\n    providedIn: 'root'\n  });\n  return DomSanitizer;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction domSanitizerImplFactory(injector) {\n  return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\n\nlet DomSanitizerImpl = /*#__PURE__*/(() => {\n  class DomSanitizerImpl extends DomSanitizer {\n    constructor(_doc) {\n      super();\n      this._doc = _doc;\n    }\n\n    sanitize(ctx, value) {\n      if (value == null) return null;\n\n      switch (ctx) {\n        case SecurityContext.NONE:\n          return value;\n\n        case SecurityContext.HTML:\n          if (ɵallowSanitizationBypassAndThrow(value, \"HTML\"\n          /* Html */\n          )) {\n            return ɵunwrapSafeValue(value);\n          }\n\n          return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n\n        case SecurityContext.STYLE:\n          if (ɵallowSanitizationBypassAndThrow(value, \"Style\"\n          /* Style */\n          )) {\n            return ɵunwrapSafeValue(value);\n          }\n\n          return value;\n\n        case SecurityContext.SCRIPT:\n          if (ɵallowSanitizationBypassAndThrow(value, \"Script\"\n          /* Script */\n          )) {\n            return ɵunwrapSafeValue(value);\n          }\n\n          throw new Error('unsafe value used in a script context');\n\n        case SecurityContext.URL:\n          const type = ɵgetSanitizationBypassType(value);\n\n          if (ɵallowSanitizationBypassAndThrow(value, \"URL\"\n          /* Url */\n          )) {\n            return ɵunwrapSafeValue(value);\n          }\n\n          return ɵ_sanitizeUrl(String(value));\n\n        case SecurityContext.RESOURCE_URL:\n          if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\"\n          /* ResourceUrl */\n          )) {\n            return ɵunwrapSafeValue(value);\n          }\n\n          throw new Error('unsafe value used in a resource URL context (see https://g.co/ng/security#xss)');\n\n        default:\n          throw new Error(`Unexpected SecurityContext ${ctx} (see https://g.co/ng/security#xss)`);\n      }\n    }\n\n    bypassSecurityTrustHtml(value) {\n      return ɵbypassSanitizationTrustHtml(value);\n    }\n\n    bypassSecurityTrustStyle(value) {\n      return ɵbypassSanitizationTrustStyle(value);\n    }\n\n    bypassSecurityTrustScript(value) {\n      return ɵbypassSanitizationTrustScript(value);\n    }\n\n    bypassSecurityTrustUrl(value) {\n      return ɵbypassSanitizationTrustUrl(value);\n    }\n\n    bypassSecurityTrustResourceUrl(value) {\n      return ɵbypassSanitizationTrustResourceUrl(value);\n    }\n\n  }\n\n  DomSanitizerImpl.ɵfac = function DomSanitizerImpl_Factory(t) {\n    return new (t || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n  };\n\n  DomSanitizerImpl.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSanitizerImpl,\n    factory: function DomSanitizerImpl_Factory(t) {\n      let r = null;\n\n      if (t) {\n        r = new t();\n      } else {\n        r = domSanitizerImplFactory(i0.ɵɵinject(Injector));\n      }\n\n      return r;\n    },\n    providedIn: 'root'\n  });\n  return DomSanitizerImpl;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nconst VERSION = /*#__PURE__*/new Version('13.1.3');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, BrowserTransferStateModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, Title, TransferState, VERSION, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, DomSharedStylesHost as ɵDomSharedStylesHost, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, NAMESPACE_URIS as ɵNAMESPACE_URIS, SharedStylesHost as ɵSharedStylesHost, TRANSITION_ID as ɵTRANSITION_ID, escapeHtml as ɵescapeHtml, flattenStyles as ɵflattenStyles, initDomAdapter as ɵinitDomAdapter, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute }; //# sourceMappingURL=platform-browser.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}