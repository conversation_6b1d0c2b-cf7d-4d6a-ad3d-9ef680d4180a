{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Applies CSS prefixes to appropriate style keys.\n *\n * Note: `-ms-`, `-moz` and `-webkit-box` are no longer supported. e.g.\n *    {\n *      display: -webkit-flex;     NEW - Safari 6.1+. iOS 7.1+, BB10\n *      display: flex;             NEW, Spec - Firefox, Chrome, Opera\n *      // display: -webkit-box;   OLD - iOS 6-, Safari 3.1-6, BB7\n *      // display: -ms-flexbox;   TWEENER - IE 10\n *      // display: -moz-flexbox;  OLD - Firefox\n *    }\n */\nfunction applyCssPrefixes(target) {\n  for (let key in target) {\n    let value = target[key] || '';\n\n    switch (key) {\n      case 'display':\n        if (value === 'flex') {\n          target['display'] = ['-webkit-flex', 'flex'];\n        } else if (value === 'inline-flex') {\n          target['display'] = ['-webkit-inline-flex', 'inline-flex'];\n        } else {\n          target['display'] = value;\n        }\n\n        break;\n\n      case 'align-items':\n      case 'align-self':\n      case 'align-content':\n      case 'flex':\n      case 'flex-basis':\n      case 'flex-flow':\n      case 'flex-grow':\n      case 'flex-shrink':\n      case 'flex-wrap':\n      case 'justify-content':\n        target['-webkit-' + key] = value;\n        break;\n\n      case 'flex-direction':\n        value = value || 'row';\n        target['-webkit-flex-direction'] = value;\n        target['flex-direction'] = value;\n        break;\n\n      case 'order':\n        target['order'] = target['-webkit-' + key] = isNaN(+value) ? '0' : value;\n        break;\n    }\n  }\n\n  return target;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst INLINE = 'inline';\nconst LAYOUT_VALUES = ['row', 'column', 'row-reverse', 'column-reverse'];\n/**\n * Validate the direction|'direction wrap' value and then update the host's inline flexbox styles\n */\n\nfunction buildLayoutCSS(value) {\n  let [direction, wrap, isInline] = validateValue(value);\n  return buildCSS(direction, wrap, isInline);\n}\n/**\n  * Validate the value to be one of the acceptable value options\n  * Use default fallback of 'row'\n  */\n\n\nfunction validateValue(value) {\n  var _a;\n\n  value = (_a = value === null || value === void 0 ? void 0 : value.toLowerCase()) !== null && _a !== void 0 ? _a : '';\n  let [direction, wrap, inline] = value.split(' '); // First value must be the `flex-direction`\n\n  if (!LAYOUT_VALUES.find(x => x === direction)) {\n    direction = LAYOUT_VALUES[0];\n  }\n\n  if (wrap === INLINE) {\n    wrap = inline !== INLINE ? inline : '';\n    inline = INLINE;\n  }\n\n  return [direction, validateWrapValue(wrap), !!inline];\n}\n/**\n * Determine if the validated, flex-direction value specifies\n * a horizontal/row flow.\n */\n\n\nfunction isFlowHorizontal(value) {\n  let [flow] = validateValue(value);\n  return flow.indexOf('row') > -1;\n}\n/**\n * Convert layout-wrap='<value>' to expected flex-wrap style\n */\n\n\nfunction validateWrapValue(value) {\n  if (!!value) {\n    switch (value.toLowerCase()) {\n      case 'reverse':\n      case 'wrap-reverse':\n      case 'reverse-wrap':\n        value = 'wrap-reverse';\n        break;\n\n      case 'no':\n      case 'none':\n      case 'nowrap':\n        value = 'nowrap';\n        break;\n      // All other values fallback to 'wrap'\n\n      default:\n        value = 'wrap';\n        break;\n    }\n  }\n\n  return value;\n}\n/**\n * Build the CSS that should be assigned to the element instance\n * BUG:\n *   1) min-height on a column flex container won’t apply to its flex item children in IE 10-11.\n *      Use height instead if possible; height : <xxx>vh;\n *\n *  This way any padding or border specified on the child elements are\n *  laid out and drawn inside that element's specified width and height.\n */\n\n\nfunction buildCSS(direction, wrap = null, inline = false) {\n  return {\n    display: inline ? 'inline-flex' : 'flex',\n    'box-sizing': 'border-box',\n    'flex-direction': direction,\n    'flex-wrap': wrap || null\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Extends an object with the *enumerable* and *own* properties of one or more source objects,\n * similar to Object.assign.\n *\n * @param dest The object which will have properties copied to it.\n * @param sources The source objects from which properties will be copied.\n */\n\n\nfunction extendObject(dest, ...sources) {\n  if (dest == null) {\n    throw TypeError('Cannot convert undefined or null to object');\n  }\n\n  for (let source of sources) {\n    if (source != null) {\n      for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n          dest[key] = source[key];\n        }\n      }\n    }\n  }\n\n  return dest;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { INLINE, LAYOUT_VALUES, applyCssPrefixes, buildLayoutCSS, extendObject, isFlowHorizontal, validateValue, validateWrapValue }; //# sourceMappingURL=angular-flex-layout-_private-utils.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}