{"ast": null, "code": "import { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function distinct(keySelector, flushes) {\n  return operate((source, subscriber) => {\n    const distinctKeys = new Set();\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      const key = keySelector ? keySelector(value) : value;\n\n      if (!distinctKeys.has(key)) {\n        distinctKeys.add(key);\n        subscriber.next(value);\n      }\n    }));\n    flushes === null || flushes === void 0 ? void 0 : flushes.subscribe(new OperatorSubscriber(subscriber, () => distinctKeys.clear(), noop));\n  });\n} //# sourceMappingURL=distinct.js.map", "map": null, "metadata": {}, "sourceType": "module"}