{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { innerFrom } from './innerFrom';\nimport { popResultSelector } from '../util/args';\nimport { OperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { createObject } from '../util/createObject';\nexport function forkJoin(...args) {\n  const resultSelector = popResultSelector(args);\n  const {\n    args: sources,\n    keys\n  } = argsArgArrayOrObject(args);\n  const result = new Observable(subscriber => {\n    const {\n      length\n    } = sources;\n\n    if (!length) {\n      subscriber.complete();\n      return;\n    }\n\n    const values = new Array(length);\n    let remainingCompletions = length;\n    let remainingEmissions = length;\n\n    for (let sourceIndex = 0; sourceIndex < length; sourceIndex++) {\n      let hasValue = false;\n      innerFrom(sources[sourceIndex]).subscribe(new OperatorSubscriber(subscriber, value => {\n        if (!hasValue) {\n          hasValue = true;\n          remainingEmissions--;\n        }\n\n        values[sourceIndex] = value;\n      }, () => remainingCompletions--, undefined, () => {\n        if (!remainingCompletions || !hasValue) {\n          if (!remainingEmissions) {\n            subscriber.next(keys ? createObject(keys, values) : values);\n          }\n\n          subscriber.complete();\n        }\n      }));\n    }\n  });\n  return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n} //# sourceMappingURL=forkJoin.js.map", "map": null, "metadata": {}, "sourceType": "module"}