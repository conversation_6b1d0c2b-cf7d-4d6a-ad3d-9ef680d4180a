{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Frisian [fy]\n//! author : <PERSON> : https://github.com/robin0van0der0v\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var monthsShortWithDots = 'jan._feb._mrt._apr._mai_jun._jul._aug._sep._okt._nov._des.'.split('_'),\n      monthsShortWithoutDots = 'jan_feb_mrt_apr_mai_jun_jul_aug_sep_okt_nov_des'.split('_');\n  var fy = moment.defineLocale('fy', {\n    months: 'janne<PERSON>s_febrew<PERSON>_maart_april_maaie_juny_july_augustus_septimber_oktober_novimber_desimber'.split('_'),\n    monthsShort: function (m, format) {\n      if (!m) {\n        return monthsShortWithDots;\n      } else if (/-MMM-/.test(format)) {\n        return monthsShortWithoutDots[m.month()];\n      } else {\n        return monthsShortWithDots[m.month()];\n      }\n    },\n    monthsParseExact: true,\n    weekdays: 'snein_moandei_tiisdei_woansdei_tongersdei_freed_sneon'.split('_'),\n    weekdaysShort: 'si._mo._ti._wo._to._fr._so.'.split('_'),\n    weekdaysMin: 'Si_Mo_Ti_Wo_To_Fr_So'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD-MM-YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[hjoed om] LT',\n      nextDay: '[moarn om] LT',\n      nextWeek: 'dddd [om] LT',\n      lastDay: '[juster om] LT',\n      lastWeek: '[ôfrûne] dddd [om] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'oer %s',\n      past: '%s lyn',\n      s: 'in pear sekonden',\n      ss: '%d sekonden',\n      m: 'ien minút',\n      mm: '%d minuten',\n      h: 'ien oere',\n      hh: '%d oeren',\n      d: 'ien dei',\n      dd: '%d dagen',\n      M: 'ien moanne',\n      MM: '%d moannen',\n      y: 'ien jier',\n      yy: '%d jierren'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(ste|de)/,\n    ordinal: function (number) {\n      return number + (number === 1 || number === 8 || number >= 20 ? 'ste' : 'de');\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return fy;\n});", "map": null, "metadata": {}, "sourceType": "script"}