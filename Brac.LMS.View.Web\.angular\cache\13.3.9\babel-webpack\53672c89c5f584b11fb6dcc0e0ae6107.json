{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Lao [lo]\n//! author : <PERSON> : https://github.com/ryanhart2\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var lo = moment.defineLocale('lo', {\n    months: 'ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ'.split('_'),\n    monthsShort: 'ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ'.split('_'),\n    weekdays: 'ອາທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ'.split('_'),\n    weekdaysShort: 'ທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ'.split('_'),\n    weekdaysMin: 'ທ_ຈ_ອຄ_ພ_ພຫ_ສກ_ສ'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'ວັນdddd D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /ຕອນເຊົ້າ|ຕອນແລງ/,\n    isPM: function (input) {\n      return input === 'ຕອນແລງ';\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'ຕອນເຊົ້າ';\n      } else {\n        return 'ຕອນແລງ';\n      }\n    },\n    calendar: {\n      sameDay: '[ມື້ນີ້ເວລາ] LT',\n      nextDay: '[ມື້ອື່ນເວລາ] LT',\n      nextWeek: '[ວັນ]dddd[ໜ້າເວລາ] LT',\n      lastDay: '[ມື້ວານນີ້ເວລາ] LT',\n      lastWeek: '[ວັນ]dddd[ແລ້ວນີ້ເວລາ] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'ອີກ %s',\n      past: '%sຜ່ານມາ',\n      s: 'ບໍ່ເທົ່າໃດວິນາທີ',\n      ss: '%d ວິນາທີ',\n      m: '1 ນາທີ',\n      mm: '%d ນາທີ',\n      h: '1 ຊົ່ວໂມງ',\n      hh: '%d ຊົ່ວໂມງ',\n      d: '1 ມື້',\n      dd: '%d ມື້',\n      M: '1 ເດືອນ',\n      MM: '%d ເດືອນ',\n      y: '1 ປີ',\n      yy: '%d ປີ'\n    },\n    dayOfMonthOrdinalParse: /(ທີ່)\\d{1,2}/,\n    ordinal: function (number) {\n      return 'ທີ່' + number;\n    }\n  });\n  return lo;\n});", "map": null, "metadata": {}, "sourceType": "script"}