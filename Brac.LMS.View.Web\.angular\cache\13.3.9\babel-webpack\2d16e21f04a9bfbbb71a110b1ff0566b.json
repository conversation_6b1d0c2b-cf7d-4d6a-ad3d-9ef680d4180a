{"ast": null, "code": "import * as i3 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Inject, Optional, ViewChild, Input, Output, Directive, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { mixinTabIndex, mixinColor, mixinDisableRipple, mixinDisabled, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport * as i1 from '@angular/cdk/a11y';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token to be used to override the default options for `mat-slide-toggle`. */\n\nconst _c0 = [\"thumbContainer\"];\nconst _c1 = [\"toggleBar\"];\nconst _c2 = [\"input\"];\n\nconst _c3 = function (a0) {\n  return {\n    enterDuration: a0\n  };\n};\n\nconst _c4 = [\"*\"];\nconst MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-slide-toggle-default-options', {\n  providedIn: 'root',\n  factory: () => ({\n    disableToggleValue: false\n  })\n}); // Increasing integer for generating unique ids for slide-toggle components.\n\nlet nextUniqueId = 0;\n/** @docs-private */\n\nconst MAT_SLIDE_TOGGLE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatSlideToggle),\n  multi: true\n};\n/** Change event object emitted by a MatSlideToggle. */\n\nclass MatSlideToggleChange {\n  constructor(\n  /** The source MatSlideToggle of the event. */\n  source,\n  /** The new `checked` value of the MatSlideToggle. */\n  checked) {\n    this.source = source;\n    this.checked = checked;\n  }\n\n} // Boilerplate for applying mixins to MatSlideToggle.\n\n/** @docs-private */\n\n\nconst _MatSlideToggleBase = /*#__PURE__*/mixinTabIndex( /*#__PURE__*/mixinColor( /*#__PURE__*/mixinDisableRipple( /*#__PURE__*/mixinDisabled(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n\n}))));\n/** Represents a slidable \"switch\" toggle that can be moved between on and off. */\n\n\nlet MatSlideToggle = /*#__PURE__*/(() => {\n  class MatSlideToggle extends _MatSlideToggleBase {\n    constructor(elementRef, _focusMonitor, _changeDetectorRef, tabIndex, defaults, animationMode) {\n      super(elementRef);\n      this._focusMonitor = _focusMonitor;\n      this._changeDetectorRef = _changeDetectorRef;\n      this.defaults = defaults;\n\n      this._onChange = _ => {};\n\n      this._onTouched = () => {};\n\n      this._uniqueId = `mat-slide-toggle-${++nextUniqueId}`;\n      this._required = false;\n      this._checked = false;\n      /** Name value will be applied to the input element if present. */\n\n      this.name = null;\n      /** A unique id for the slide-toggle input. If none is supplied, it will be auto-generated. */\n\n      this.id = this._uniqueId;\n      /** Whether the label should appear after or before the slide-toggle. Defaults to 'after'. */\n\n      this.labelPosition = 'after';\n      /** Used to set the aria-label attribute on the underlying input element. */\n\n      this.ariaLabel = null;\n      /** Used to set the aria-labelledby attribute on the underlying input element. */\n\n      this.ariaLabelledby = null;\n      /** An event will be dispatched each time the slide-toggle changes its value. */\n\n      this.change = new EventEmitter();\n      /**\n       * An event will be dispatched each time the slide-toggle input is toggled.\n       * This event is always emitted when the user toggles the slide toggle, but this does not mean\n       * the slide toggle's value has changed.\n       */\n\n      this.toggleChange = new EventEmitter();\n      this.tabIndex = parseInt(tabIndex) || 0;\n      this.color = this.defaultColor = defaults.color || 'accent';\n      this._noopAnimations = animationMode === 'NoopAnimations';\n    }\n    /** Whether the slide-toggle is required. */\n\n\n    get required() {\n      return this._required;\n    }\n\n    set required(value) {\n      this._required = coerceBooleanProperty(value);\n    }\n    /** Whether the slide-toggle element is checked or not. */\n\n\n    get checked() {\n      return this._checked;\n    }\n\n    set checked(value) {\n      this._checked = coerceBooleanProperty(value);\n\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Returns the unique id for the visual hidden input. */\n\n\n    get inputId() {\n      return `${this.id || this._uniqueId}-input`;\n    }\n\n    ngAfterContentInit() {\n      this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n        if (!focusOrigin) {\n          // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n          // Angular does not expect events to be raised during change detection, so any state\n          // change (such as a form control's 'ng-touched') will cause a changed-after-checked\n          // error. See https://github.com/angular/angular/issues/17793. To work around this,\n          // we defer telling the form control it has been touched until the next tick.\n          Promise.resolve().then(() => this._onTouched());\n        }\n      });\n    }\n\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    /** Method being called whenever the underlying input emits a change event. */\n\n\n    _onChangeEvent(event) {\n      // We always have to stop propagation on the change event.\n      // Otherwise the change event, from the input element, will bubble up and\n      // emit its event object to the component's `change` output.\n      event.stopPropagation();\n      this.toggleChange.emit(); // When the slide toggle's config disables toggle change event by setting\n      // `disableToggleValue: true`, the slide toggle's value does not change, and the\n      // checked state of the underlying input needs to be changed back.\n\n      if (this.defaults.disableToggleValue) {\n        this._inputElement.nativeElement.checked = this.checked;\n        return;\n      } // Sync the value from the underlying input element with the component instance.\n\n\n      this.checked = this._inputElement.nativeElement.checked; // Emit our custom change event only if the underlying input emitted one. This ensures that\n      // there is no change event, when the checked state changes programmatically.\n\n      this._emitChangeEvent();\n    }\n    /** Method being called whenever the slide-toggle has been clicked. */\n\n\n    _onInputClick(event) {\n      // We have to stop propagation for click events on the visual hidden input element.\n      // By default, when a user clicks on a label element, a generated click event will be\n      // dispatched on the associated input element. Since we are using a label element as our\n      // root container, the click event on the `slide-toggle` will be executed twice.\n      // The real click event will bubble up, and the generated click event also tries to bubble up.\n      // This will lead to multiple click events.\n      // Preventing bubbling for the second event will solve that issue.\n      event.stopPropagation();\n    }\n    /** Implemented as part of ControlValueAccessor. */\n\n\n    writeValue(value) {\n      this.checked = !!value;\n    }\n    /** Implemented as part of ControlValueAccessor. */\n\n\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    /** Implemented as part of ControlValueAccessor. */\n\n\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    /** Implemented as a part of ControlValueAccessor. */\n\n\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Focuses the slide-toggle. */\n\n\n    focus(options, origin) {\n      if (origin) {\n        this._focusMonitor.focusVia(this._inputElement, origin, options);\n      } else {\n        this._inputElement.nativeElement.focus(options);\n      }\n    }\n    /** Toggles the checked state of the slide-toggle. */\n\n\n    toggle() {\n      this.checked = !this.checked;\n\n      this._onChange(this.checked);\n    }\n    /**\n     * Emits a change event on the `change` output. Also notifies the FormControl about the change.\n     */\n\n\n    _emitChangeEvent() {\n      this._onChange(this.checked);\n\n      this.change.emit(new MatSlideToggleChange(this, this.checked));\n    }\n    /** Method being called whenever the label text changes. */\n\n\n    _onLabelTextChange() {\n      // Since the event of the `cdkObserveContent` directive runs outside of the zone, the\n      // slide-toggle component will be only marked for check, but no actual change detection runs\n      // automatically. Instead of going back into the zone in order to trigger a change detection\n      // which causes *all* components to be checked (if explicitly marked or not using OnPush),\n      // we only trigger an explicit change detection for the slide-toggle view and its children.\n      this._changeDetectorRef.detectChanges();\n    }\n\n  }\n\n  MatSlideToggle.ɵfac = function MatSlideToggle_Factory(t) {\n    return new (t || MatSlideToggle)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  MatSlideToggle.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSlideToggle,\n    selectors: [[\"mat-slide-toggle\"]],\n    viewQuery: function MatSlideToggle_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._thumbEl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._thumbBarEl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-slide-toggle\"],\n    hostVars: 12,\n    hostBindings: function MatSlideToggle_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null);\n        i0.ɵɵclassProp(\"mat-checked\", ctx.checked)(\"mat-disabled\", ctx.disabled)(\"mat-slide-toggle-label-before\", ctx.labelPosition == \"before\")(\"_mat-animation-noopable\", ctx._noopAnimations);\n      }\n    },\n    inputs: {\n      disabled: \"disabled\",\n      disableRipple: \"disableRipple\",\n      color: \"color\",\n      tabIndex: \"tabIndex\",\n      name: \"name\",\n      id: \"id\",\n      labelPosition: \"labelPosition\",\n      ariaLabel: [\"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n      ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"],\n      required: \"required\",\n      checked: \"checked\"\n    },\n    outputs: {\n      change: \"change\",\n      toggleChange: \"toggleChange\"\n    },\n    exportAs: [\"matSlideToggle\"],\n    features: [i0.ɵɵProvidersFeature([MAT_SLIDE_TOGGLE_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c4,\n    decls: 16,\n    vars: 20,\n    consts: [[1, \"mat-slide-toggle-label\"], [\"label\", \"\"], [1, \"mat-slide-toggle-bar\"], [\"toggleBar\", \"\"], [\"type\", \"checkbox\", \"role\", \"switch\", 1, \"mat-slide-toggle-input\", \"cdk-visually-hidden\", 3, \"id\", \"required\", \"tabIndex\", \"checked\", \"disabled\", \"change\", \"click\"], [\"input\", \"\"], [1, \"mat-slide-toggle-thumb-container\"], [\"thumbContainer\", \"\"], [1, \"mat-slide-toggle-thumb\"], [\"mat-ripple\", \"\", 1, \"mat-slide-toggle-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\"], [1, \"mat-ripple-element\", \"mat-slide-toggle-persistent-ripple\"], [1, \"mat-slide-toggle-content\", 3, \"cdkObserveContent\"], [\"labelContent\", \"\"], [2, \"display\", \"none\"]],\n    template: function MatSlideToggle_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"label\", 0, 1);\n        i0.ɵɵelementStart(2, \"div\", 2, 3);\n        i0.ɵɵelementStart(4, \"input\", 4, 5);\n        i0.ɵɵlistener(\"change\", function MatSlideToggle_Template_input_change_4_listener($event) {\n          return ctx._onChangeEvent($event);\n        })(\"click\", function MatSlideToggle_Template_input_click_4_listener($event) {\n          return ctx._onInputClick($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 6, 7);\n        i0.ɵɵelement(8, \"div\", 8);\n        i0.ɵɵelementStart(9, \"div\", 9);\n        i0.ɵɵelement(10, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"span\", 11, 12);\n        i0.ɵɵlistener(\"cdkObserveContent\", function MatSlideToggle_Template_span_cdkObserveContent_11_listener() {\n          return ctx._onLabelTextChange();\n        });\n        i0.ɵɵelementStart(13, \"span\", 13);\n        i0.ɵɵtext(14, \"\\xA0\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(1);\n\n        const _r4 = i0.ɵɵreference(12);\n\n        i0.ɵɵattribute(\"for\", ctx.inputId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"mat-slide-toggle-bar-no-side-margin\", !_r4.textContent || !_r4.textContent.trim());\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"id\", ctx.inputId)(\"required\", ctx.required)(\"tabIndex\", ctx.tabIndex)(\"checked\", ctx.checked)(\"disabled\", ctx.disabled);\n        i0.ɵɵattribute(\"name\", ctx.name)(\"aria-checked\", ctx.checked.toString())(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"matRippleTrigger\", _r0)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleCentered\", true)(\"matRippleRadius\", 20)(\"matRippleAnimation\", i0.ɵɵpureFunction1(18, _c3, ctx._noopAnimations ? 0 : 150));\n      }\n    },\n    directives: [i2.MatRipple, i3.CdkObserveContent],\n    styles: [\".mat-slide-toggle{display:inline-block;height:24px;max-width:100%;line-height:24px;white-space:nowrap;outline:none;-webkit-tap-highlight-color:transparent}.mat-slide-toggle.mat-checked .mat-slide-toggle-thumb-container{transform:translate3d(16px, 0, 0)}[dir=rtl] .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb-container{transform:translate3d(-16px, 0, 0)}.mat-slide-toggle.mat-disabled{opacity:.38}.mat-slide-toggle.mat-disabled .mat-slide-toggle-label,.mat-slide-toggle.mat-disabled .mat-slide-toggle-thumb-container{cursor:default}.mat-slide-toggle-label{-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;flex:1;flex-direction:row;align-items:center;height:inherit;cursor:pointer}.mat-slide-toggle-content{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-slide-toggle-label-before .mat-slide-toggle-label{order:1}.mat-slide-toggle-label-before .mat-slide-toggle-bar{order:2}[dir=rtl] .mat-slide-toggle-label-before .mat-slide-toggle-bar,.mat-slide-toggle-bar{margin-right:8px;margin-left:0}[dir=rtl] .mat-slide-toggle-bar,.mat-slide-toggle-label-before .mat-slide-toggle-bar{margin-left:8px;margin-right:0}.mat-slide-toggle-bar-no-side-margin{margin-left:0;margin-right:0}.mat-slide-toggle-thumb-container{position:absolute;z-index:1;width:20px;height:20px;top:-3px;left:0;transform:translate3d(0, 0, 0);transition:all 80ms linear;transition-property:transform}._mat-animation-noopable .mat-slide-toggle-thumb-container{transition:none}[dir=rtl] .mat-slide-toggle-thumb-container{left:auto;right:0}.mat-slide-toggle-thumb{height:20px;width:20px;border-radius:50%}.mat-slide-toggle-bar{position:relative;width:36px;height:14px;flex-shrink:0;border-radius:8px}.mat-slide-toggle-input{bottom:0;left:10px}[dir=rtl] .mat-slide-toggle-input{left:auto;right:10px}.mat-slide-toggle-bar,.mat-slide-toggle-thumb{transition:all 80ms linear;transition-property:background-color;transition-delay:50ms}._mat-animation-noopable .mat-slide-toggle-bar,._mat-animation-noopable .mat-slide-toggle-thumb{transition:none}.mat-slide-toggle .mat-slide-toggle-ripple{position:absolute;top:calc(50% - 20px);left:calc(50% - 20px);height:40px;width:40px;z-index:1;pointer-events:none}.mat-slide-toggle .mat-slide-toggle-ripple .mat-ripple-element:not(.mat-slide-toggle-persistent-ripple){opacity:.12}.mat-slide-toggle-persistent-ripple{width:100%;height:100%;transform:none}.mat-slide-toggle-bar:hover .mat-slide-toggle-persistent-ripple{opacity:.04}.mat-slide-toggle:not(.mat-disabled).cdk-keyboard-focused .mat-slide-toggle-persistent-ripple{opacity:.12}.mat-slide-toggle-persistent-ripple,.mat-slide-toggle.mat-disabled .mat-slide-toggle-bar:hover .mat-slide-toggle-persistent-ripple{opacity:0}@media(hover: none){.mat-slide-toggle-bar:hover .mat-slide-toggle-persistent-ripple{display:none}}.cdk-high-contrast-active .mat-slide-toggle-thumb,.cdk-high-contrast-active .mat-slide-toggle-bar{border:1px solid}.cdk-high-contrast-active .mat-slide-toggle.cdk-keyboard-focused .mat-slide-toggle-bar{outline:2px dotted;outline-offset:5px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return MatSlideToggle;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: /*#__PURE__*/forwardRef(() => MatSlideToggleRequiredValidator),\n  multi: true\n};\n/**\n * Validator for Material slide-toggle components with the required attribute in a\n * template-driven form. The default validator for required form controls asserts\n * that the control value is not undefined but that is not appropriate for a slide-toggle\n * where the value is always defined.\n *\n * Required slide-toggle form controls are valid when checked.\n */\n\nlet MatSlideToggleRequiredValidator = /*#__PURE__*/(() => {\n  class MatSlideToggleRequiredValidator extends CheckboxRequiredValidator {}\n\n  MatSlideToggleRequiredValidator.ɵfac = /* @__PURE__ */function () {\n    let ɵMatSlideToggleRequiredValidator_BaseFactory;\n    return function MatSlideToggleRequiredValidator_Factory(t) {\n      return (ɵMatSlideToggleRequiredValidator_BaseFactory || (ɵMatSlideToggleRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MatSlideToggleRequiredValidator)))(t || MatSlideToggleRequiredValidator);\n    };\n  }();\n\n  MatSlideToggleRequiredValidator.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSlideToggleRequiredValidator,\n    selectors: [[\"mat-slide-toggle\", \"required\", \"\", \"formControlName\", \"\"], [\"mat-slide-toggle\", \"required\", \"\", \"formControl\", \"\"], [\"mat-slide-toggle\", \"required\", \"\", \"ngModel\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return MatSlideToggleRequiredValidator;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** This module is used by both original and MDC-based slide-toggle implementations. */\n\n\nlet _MatSlideToggleRequiredValidatorModule = /*#__PURE__*/(() => {\n  class _MatSlideToggleRequiredValidatorModule {}\n\n  _MatSlideToggleRequiredValidatorModule.ɵfac = function _MatSlideToggleRequiredValidatorModule_Factory(t) {\n    return new (t || _MatSlideToggleRequiredValidatorModule)();\n  };\n\n  _MatSlideToggleRequiredValidatorModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: _MatSlideToggleRequiredValidatorModule\n  });\n  _MatSlideToggleRequiredValidatorModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return _MatSlideToggleRequiredValidatorModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet MatSlideToggleModule = /*#__PURE__*/(() => {\n  class MatSlideToggleModule {}\n\n  MatSlideToggleModule.ɵfac = function MatSlideToggleModule_Factory(t) {\n    return new (t || MatSlideToggleModule)();\n  };\n\n  MatSlideToggleModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatSlideToggleModule\n  });\n  MatSlideToggleModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[_MatSlideToggleRequiredValidatorModule, MatRippleModule, MatCommonModule, ObserversModule], _MatSlideToggleRequiredValidatorModule, MatCommonModule]\n  });\n  return MatSlideToggleModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS, MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR, MAT_SLIDE_TOGGLE_VALUE_ACCESSOR, MatSlideToggle, MatSlideToggleChange, MatSlideToggleModule, MatSlideToggleRequiredValidator, _MatSlideToggleRequiredValidatorModule }; //# sourceMappingURL=slide-toggle.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}