{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { environment } from '../../environments/environment';\nimport { NavigationEnd } from '@angular/router';\nimport { BlockUI } from 'ng-block-ui';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport $ from \"jquery\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"../_helpers/confirm-dialog/confirm.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"ngx-bootstrap/modal\";\nimport * as i7 from \"ng-block-ui\";\n\nfunction ExternsalCoursePreviewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵelementStart(1, \"div\", 2);\n    i0.ɵɵelementStart(2, \"div\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4);\n    i0.ɵɵelementStart(4, \"h1\", 5);\n    i0.ɵɵtext(5, \"Course Overview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"a\", 6);\n    i0.ɵɵlistener(\"click\", function ExternsalCoursePreviewComponent_div_1_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return ctx_r1.backClicked();\n    });\n    i0.ɵɵelement(7, \"i\", 7);\n    i0.ɵɵtext(8, \"Go Back \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"hr\", 8);\n    i0.ɵɵelementStart(10, \"div\", 9);\n    i0.ɵɵelementStart(11, \"div\", 10);\n    i0.ɵɵelementStart(12, \"div\", 9);\n    i0.ɵɵelementStart(13, \"div\", 11);\n    i0.ɵɵelementStart(14, \"div\", 12);\n    i0.ɵɵelementStart(15, \"h1\", 13);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 9);\n    i0.ɵɵelementStart(18, \"div\", 14);\n    i0.ɵɵelementStart(19, \"p\", 15);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 16);\n    i0.ɵɵelementStart(22, \"a\", 17);\n    i0.ɵɵtext(23, \"Go to the course\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 18);\n    i0.ɵɵelementStart(25, \"div\", 19);\n    i0.ɵɵelement(26, \"img\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(ctx_r0.courseData.Title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.courseData.Description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"href\", ctx_r0.courseData.ExternalUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate2(\"src\", \"\", ctx_r0.mediaBaseUrl, \"\", ctx_r0.courseData.ImagePath, \"\", i0.ɵɵsanitizeUrl);\n  }\n}\n\nexport class ExternsalCoursePreviewComponent {\n  constructor(router, _service, toastr, confirmService, route, _location, modalService) {\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.confirmService = confirmService;\n    this.route = route;\n    this._location = _location;\n    this.modalService = modalService;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.timestamp = new Date().getTime();\n    this.courseData = null;\n    this.courseId = this.route.snapshot.paramMap.get(\"courseId\");\n    this.router.events.subscribe(evt => {\n      if (!(evt instanceof NavigationEnd)) {\n        return;\n      }\n\n      window.scrollTo(0, 0);\n    });\n  }\n\n  ngOnInit() {\n    this.getExternsalCourse();\n    setTimeout(() => {\n      this.loadJquery();\n    }, 500);\n  }\n\n  loadJquery() {\n    (function ($) {})($);\n  }\n\n  backClicked() {\n    this._location.back();\n  }\n\n  getExternsalCourse() {\n    this.blockUI.start('loading...');\n\n    this._service.get('external-course/get-course-preview/' + this.courseId).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.courseData = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n}\n\nExternsalCoursePreviewComponent.ɵfac = function ExternsalCoursePreviewComponent_Factory(t) {\n  return new (t || ExternsalCoursePreviewComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i4.ConfirmService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Location), i0.ɵɵdirectiveInject(i6.BsModalService));\n};\n\nExternsalCoursePreviewComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ExternsalCoursePreviewComponent,\n  selectors: [[\"app-external-course-preview\"]],\n  decls: 2,\n  vars: 1,\n  consts: [[\"class\", \"container-fluid position-relative zindex-5 pb-0 mb-md-1 p-3 margin-top-neg-15\", 4, \"ngIf\"], [1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"btn\", \"btn-link\", \"fs-4\", \"fw-bold\", \"btn-sm\", \"mb-2\", 3, \"click\"], [1, \"fs-4\", \"ai-arrow-left\", \"fs-base\", \"me-2\"], [1, \"mt-1\", \"mb-4\"], [1, \"row\"], [1, \"col-md-7\"], [1, \"col-md-10\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-2\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-3\"], [1, \"col-12\", \"py-3\"], [1, \"text-justify\"], [1, \"text-center\", \"mt-3\"], [\"rel\", \"noopener\", 1, \"btn\", \"btn-primary\", 3, \"href\"], [1, \"col-md-5\"], [1, \"card-img-top\", \"card-img-bottom\"], [3, \"src\"]],\n  template: function ExternsalCoursePreviewComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵtemplate(1, ExternsalCoursePreviewComponent_div_1_Template, 27, 5, \"div\", 0);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.courseData);\n    }\n  },\n  directives: [i7.BlockUIComponent, i5.NgIf],\n  styles: [\"\"],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], ExternsalCoursePreviewComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}