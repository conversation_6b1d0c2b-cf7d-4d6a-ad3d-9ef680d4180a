{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Portuguese (Brazil) [pt-br]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/caio-ribeiro-pereira\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var ptBr = moment.defineLocale('pt-br', {\n    months: 'janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro'.split('_'),\n    monthsShort: 'jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez'.split('_'),\n    weekdays: 'domingo_segunda-feira_terça-feira_quarta-feira_quinta-feira_sexta-feira_sábado'.split('_'),\n    weekdaysShort: 'dom_seg_ter_qua_qui_sex_sáb'.split('_'),\n    weekdaysMin: 'do_2ª_3ª_4ª_5ª_6ª_sá'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D [de] MMMM [de] YYYY',\n      LLL: 'D [de] MMMM [de] YYYY [às] HH:mm',\n      LLLL: 'dddd, D [de] MMMM [de] YYYY [às] HH:mm'\n    },\n    calendar: {\n      sameDay: '[Hoje às] LT',\n      nextDay: '[Amanhã às] LT',\n      nextWeek: 'dddd [às] LT',\n      lastDay: '[Ontem às] LT',\n      lastWeek: function () {\n        return this.day() === 0 || this.day() === 6 ? '[Último] dddd [às] LT' // Saturday + Sunday\n        : '[Última] dddd [às] LT'; // Monday - Friday\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'em %s',\n      past: 'há %s',\n      s: 'poucos segundos',\n      ss: '%d segundos',\n      m: 'um minuto',\n      mm: '%d minutos',\n      h: 'uma hora',\n      hh: '%d horas',\n      d: 'um dia',\n      dd: '%d dias',\n      M: 'um mês',\n      MM: '%d meses',\n      y: 'um ano',\n      yy: '%d anos'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}º/,\n    ordinal: '%dº',\n    invalidDate: 'Data inválida'\n  });\n  return ptBr;\n});", "map": null, "metadata": {}, "sourceType": "script"}