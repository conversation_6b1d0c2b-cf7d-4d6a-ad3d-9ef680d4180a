{"ast": null, "code": "/**\n * @license Angular v13.1.3\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, forwardRef, Optional, Inject, ɵisPromise, ɵisObservable, Self, ɵRuntimeError, EventEmitter, Input, Host, SkipSelf, Output, NgModule, Injectable, Version } from '@angular/core';\nimport { ɵgetDOM } from '@angular/common';\nimport { from, forkJoin } from 'rxjs';\nimport { map } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Base class for all ControlValueAccessor classes defined in Forms package.\n * Contains common logic and utility functions.\n *\n * Note: this is an *internal-only* class and should not be extended or used directly in\n * applications code.\n */\n\nlet BaseControlValueAccessor = /*#__PURE__*/(() => {\n  class BaseControlValueAccessor {\n    constructor(_renderer, _elementRef) {\n      this._renderer = _renderer;\n      this._elementRef = _elementRef;\n      /**\n       * The registered callback function called when a change or input event occurs on the input\n       * element.\n       * @nodoc\n       */\n\n      this.onChange = _ => {};\n      /**\n       * The registered callback function called when a blur event occurs on the input element.\n       * @nodoc\n       */\n\n\n      this.onTouched = () => {};\n    }\n    /**\n     * Helper method that sets a property on a target element using the current Renderer\n     * implementation.\n     * @nodoc\n     */\n\n\n    setProperty(key, value) {\n      this._renderer.setProperty(this._elementRef.nativeElement, key, value);\n    }\n    /**\n     * Registers a function called when the control is touched.\n     * @nodoc\n     */\n\n\n    registerOnTouched(fn) {\n      this.onTouched = fn;\n    }\n    /**\n     * Registers a function called when the control value changes.\n     * @nodoc\n     */\n\n\n    registerOnChange(fn) {\n      this.onChange = fn;\n    }\n    /**\n     * Sets the \"disabled\" property on the range input element.\n     * @nodoc\n     */\n\n\n    setDisabledState(isDisabled) {\n      this.setProperty('disabled', isDisabled);\n    }\n\n  }\n\n  BaseControlValueAccessor.ɵfac = function BaseControlValueAccessor_Factory(t) {\n    return new (t || BaseControlValueAccessor)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n\n  BaseControlValueAccessor.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BaseControlValueAccessor\n  });\n  return BaseControlValueAccessor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Base class for all built-in ControlValueAccessor classes (except DefaultValueAccessor, which is\n * used in case no other CVAs can be found). We use this class to distinguish between default CVA,\n * built-in CVAs and custom CVAs, so that Forms logic can recognize built-in CVAs and treat custom\n * ones with higher priority (when both built-in and custom CVAs are present).\n *\n * Note: this is an *internal-only* class and should not be extended or used directly in\n * applications code.\n */\n\n\nlet BuiltInControlValueAccessor = /*#__PURE__*/(() => {\n  class BuiltInControlValueAccessor extends BaseControlValueAccessor {}\n\n  BuiltInControlValueAccessor.ɵfac = /* @__PURE__ */function () {\n    let ɵBuiltInControlValueAccessor_BaseFactory;\n    return function BuiltInControlValueAccessor_Factory(t) {\n      return (ɵBuiltInControlValueAccessor_BaseFactory || (ɵBuiltInControlValueAccessor_BaseFactory = i0.ɵɵgetInheritedFactory(BuiltInControlValueAccessor)))(t || BuiltInControlValueAccessor);\n    };\n  }();\n\n  BuiltInControlValueAccessor.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BuiltInControlValueAccessor,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return BuiltInControlValueAccessor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Used to provide a `ControlValueAccessor` for form controls.\n *\n * See `DefaultValueAccessor` for how to implement one.\n *\n * @publicApi\n */\n\n\nconst NG_VALUE_ACCESSOR = /*#__PURE__*/new InjectionToken('NgValueAccessor');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst CHECKBOX_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => CheckboxControlValueAccessor),\n  multi: true\n};\n/**\n * @description\n * A `ControlValueAccessor` for writing a value and listening to changes on a checkbox input\n * element.\n *\n * @usageNotes\n *\n * ### Using a checkbox with a reactive form.\n *\n * The following example shows how to use a checkbox with a reactive form.\n *\n * ```ts\n * const rememberLoginControl = new FormControl();\n * ```\n *\n * ```\n * <input type=\"checkbox\" [formControl]=\"rememberLoginControl\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\nlet CheckboxControlValueAccessor = /*#__PURE__*/(() => {\n  class CheckboxControlValueAccessor extends BuiltInControlValueAccessor {\n    /**\n     * Sets the \"checked\" property on the input element.\n     * @nodoc\n     */\n    writeValue(value) {\n      this.setProperty('checked', value);\n    }\n\n  }\n\n  CheckboxControlValueAccessor.ɵfac = /* @__PURE__ */function () {\n    let ɵCheckboxControlValueAccessor_BaseFactory;\n    return function CheckboxControlValueAccessor_Factory(t) {\n      return (ɵCheckboxControlValueAccessor_BaseFactory || (ɵCheckboxControlValueAccessor_BaseFactory = i0.ɵɵgetInheritedFactory(CheckboxControlValueAccessor)))(t || CheckboxControlValueAccessor);\n    };\n  }();\n\n  CheckboxControlValueAccessor.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CheckboxControlValueAccessor,\n    selectors: [[\"input\", \"type\", \"checkbox\", \"formControlName\", \"\"], [\"input\", \"type\", \"checkbox\", \"formControl\", \"\"], [\"input\", \"type\", \"checkbox\", \"ngModel\", \"\"]],\n    hostBindings: function CheckboxControlValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"change\", function CheckboxControlValueAccessor_change_HostBindingHandler($event) {\n          return ctx.onChange($event.target.checked);\n        })(\"blur\", function CheckboxControlValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        });\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([CHECKBOX_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return CheckboxControlValueAccessor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => DefaultValueAccessor),\n  multi: true\n};\n/**\n * We must check whether the agent is Android because composition events\n * behave differently between iOS and Android.\n */\n\nfunction _isAndroid() {\n  const userAgent = ɵgetDOM() ? ɵgetDOM().getUserAgent() : '';\n  return /android (\\d+)/.test(userAgent.toLowerCase());\n}\n/**\n * @description\n * Provide this token to control if form directives buffer IME input until\n * the \"compositionend\" event occurs.\n * @publicApi\n */\n\n\nconst COMPOSITION_BUFFER_MODE = /*#__PURE__*/new InjectionToken('CompositionEventMode');\n/**\n * The default `ControlValueAccessor` for writing a value and listening to changes on input\n * elements. The accessor is used by the `FormControlDirective`, `FormControlName`, and\n * `NgModel` directives.\n *\n * {@searchKeywords ngDefaultControl}\n *\n * @usageNotes\n *\n * ### Using the default value accessor\n *\n * The following example shows how to use an input element that activates the default value accessor\n * (in this case, a text field).\n *\n * ```ts\n * const firstNameControl = new FormControl();\n * ```\n *\n * ```\n * <input type=\"text\" [formControl]=\"firstNameControl\">\n * ```\n *\n * This value accessor is used by default for `<input type=\"text\">` and `<textarea>` elements, but\n * you could also use it for custom components that have similar behavior and do not require special\n * processing. In order to attach the default value accessor to a custom element, add the\n * `ngDefaultControl` attribute as shown below.\n *\n * ```\n * <custom-input-component ngDefaultControl [(ngModel)]=\"value\"></custom-input-component>\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\nlet DefaultValueAccessor = /*#__PURE__*/(() => {\n  class DefaultValueAccessor extends BaseControlValueAccessor {\n    constructor(renderer, elementRef, _compositionMode) {\n      super(renderer, elementRef);\n      this._compositionMode = _compositionMode;\n      /** Whether the user is creating a composition string (IME events). */\n\n      this._composing = false;\n\n      if (this._compositionMode == null) {\n        this._compositionMode = !_isAndroid();\n      }\n    }\n    /**\n     * Sets the \"value\" property on the input element.\n     * @nodoc\n     */\n\n\n    writeValue(value) {\n      const normalizedValue = value == null ? '' : value;\n      this.setProperty('value', normalizedValue);\n    }\n    /** @internal */\n\n\n    _handleInput(value) {\n      if (!this._compositionMode || this._compositionMode && !this._composing) {\n        this.onChange(value);\n      }\n    }\n    /** @internal */\n\n\n    _compositionStart() {\n      this._composing = true;\n    }\n    /** @internal */\n\n\n    _compositionEnd(value) {\n      this._composing = false;\n      this._compositionMode && this.onChange(value);\n    }\n\n  }\n\n  DefaultValueAccessor.ɵfac = function DefaultValueAccessor_Factory(t) {\n    return new (t || DefaultValueAccessor)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(COMPOSITION_BUFFER_MODE, 8));\n  };\n\n  DefaultValueAccessor.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultValueAccessor,\n    selectors: [[\"input\", \"formControlName\", \"\", 3, \"type\", \"checkbox\"], [\"textarea\", \"formControlName\", \"\"], [\"input\", \"formControl\", \"\", 3, \"type\", \"checkbox\"], [\"textarea\", \"formControl\", \"\"], [\"input\", \"ngModel\", \"\", 3, \"type\", \"checkbox\"], [\"textarea\", \"ngModel\", \"\"], [\"\", \"ngDefaultControl\", \"\"]],\n    hostBindings: function DefaultValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function DefaultValueAccessor_input_HostBindingHandler($event) {\n          return ctx._handleInput($event.target.value);\n        })(\"blur\", function DefaultValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        })(\"compositionstart\", function DefaultValueAccessor_compositionstart_HostBindingHandler() {\n          return ctx._compositionStart();\n        })(\"compositionend\", function DefaultValueAccessor_compositionend_HostBindingHandler($event) {\n          return ctx._compositionEnd($event.target.value);\n        });\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([DEFAULT_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultValueAccessor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction isEmptyInputValue(value) {\n  // we don't check for string here so it also works with arrays\n  return value == null || value.length === 0;\n}\n\nfunction hasValidLength(value) {\n  // non-strict comparison is intentional, to check for both `null` and `undefined` values\n  return value != null && typeof value.length === 'number';\n}\n/**\n * @description\n * An `InjectionToken` for registering additional synchronous validators used with\n * `AbstractControl`s.\n *\n * @see `NG_ASYNC_VALIDATORS`\n *\n * @usageNotes\n *\n * ### Providing a custom validator\n *\n * The following example registers a custom validator directive. Adding the validator to the\n * existing collection of validators requires the `multi: true` option.\n *\n * ```typescript\n * @Directive({\n *   selector: '[customValidator]',\n *   providers: [{provide: NG_VALIDATORS, useExisting: CustomValidatorDirective, multi: true}]\n * })\n * class CustomValidatorDirective implements Validator {\n *   validate(control: AbstractControl): ValidationErrors | null {\n *     return { 'custom': true };\n *   }\n * }\n * ```\n *\n * @publicApi\n */\n\n\nconst NG_VALIDATORS = /*#__PURE__*/new InjectionToken('NgValidators');\n/**\n * @description\n * An `InjectionToken` for registering additional asynchronous validators used with\n * `AbstractControl`s.\n *\n * @see `NG_VALIDATORS`\n *\n * @usageNotes\n *\n * ### Provide a custom async validator directive\n *\n * The following example implements the `AsyncValidator` interface to create an\n * async validator directive with a custom error key.\n *\n * ```typescript\n * @Directive({\n *   selector: '[customAsyncValidator]',\n *   providers: [{provide: NG_ASYNC_VALIDATORS, useExisting: CustomAsyncValidatorDirective, multi:\n * true}]\n * })\n * class CustomAsyncValidatorDirective implements AsyncValidator {\n *   validate(control: AbstractControl): Promise<ValidationErrors|null> {\n *     return Promise.resolve({'custom': true});\n *   }\n * }\n * ```\n *\n * @publicApi\n */\n\nconst NG_ASYNC_VALIDATORS = /*#__PURE__*/new InjectionToken('NgAsyncValidators');\n/**\n * A regular expression that matches valid e-mail addresses.\n *\n * At a high level, this regexp matches e-mail addresses of the format `local-part@tld`, where:\n * - `local-part` consists of one or more of the allowed characters (alphanumeric and some\n *   punctuation symbols).\n * - `local-part` cannot begin or end with a period (`.`).\n * - `local-part` cannot be longer than 64 characters.\n * - `tld` consists of one or more `labels` separated by periods (`.`). For example `localhost` or\n *   `foo.com`.\n * - A `label` consists of one or more of the allowed characters (alphanumeric, dashes (`-`) and\n *   periods (`.`)).\n * - A `label` cannot begin or end with a dash (`-`) or a period (`.`).\n * - A `label` cannot be longer than 63 characters.\n * - The whole address cannot be longer than 254 characters.\n *\n * ## Implementation background\n *\n * This regexp was ported over from AngularJS (see there for git history):\n * https://github.com/angular/angular.js/blob/c133ef836/src/ng/directive/input.js#L27\n * It is based on the\n * [WHATWG version](https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address) with\n * some enhancements to incorporate more RFC rules (such as rules related to domain names and the\n * lengths of different parts of the address). The main differences from the WHATWG version are:\n *   - Disallow `local-part` to begin or end with a period (`.`).\n *   - Disallow `local-part` length to exceed 64 characters.\n *   - Disallow total address length to exceed 254 characters.\n *\n * See [this commit](https://github.com/angular/angular.js/commit/f3f5cf72e) for more details.\n */\n\nconst EMAIL_REGEXP = /^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n/**\n * @description\n * Provides a set of built-in validators that can be used by form controls.\n *\n * A validator is a function that processes a `FormControl` or collection of\n * controls and returns an error map or null. A null map means that validation has passed.\n *\n * @see [Form Validation](/guide/form-validation)\n *\n * @publicApi\n */\n\nclass Validators {\n  /**\n   * @description\n   * Validator that requires the control's value to be greater than or equal to the provided number.\n   *\n   * @usageNotes\n   *\n   * ### Validate against a minimum of 3\n   *\n   * ```typescript\n   * const control = new FormControl(2, Validators.min(3));\n   *\n   * console.log(control.errors); // {min: {min: 3, actual: 2}}\n   * ```\n   *\n   * @returns A validator function that returns an error map with the\n   * `min` property if the validation check fails, otherwise `null`.\n   *\n   * @see `updateValueAndValidity()`\n   *\n   */\n  static min(min) {\n    return minValidator(min);\n  }\n  /**\n   * @description\n   * Validator that requires the control's value to be less than or equal to the provided number.\n   *\n   * @usageNotes\n   *\n   * ### Validate against a maximum of 15\n   *\n   * ```typescript\n   * const control = new FormControl(16, Validators.max(15));\n   *\n   * console.log(control.errors); // {max: {max: 15, actual: 16}}\n   * ```\n   *\n   * @returns A validator function that returns an error map with the\n   * `max` property if the validation check fails, otherwise `null`.\n   *\n   * @see `updateValueAndValidity()`\n   *\n   */\n\n\n  static max(max) {\n    return maxValidator(max);\n  }\n  /**\n   * @description\n   * Validator that requires the control have a non-empty value.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field is non-empty\n   *\n   * ```typescript\n   * const control = new FormControl('', Validators.required);\n   *\n   * console.log(control.errors); // {required: true}\n   * ```\n   *\n   * @returns An error map with the `required` property\n   * if the validation check fails, otherwise `null`.\n   *\n   * @see `updateValueAndValidity()`\n   *\n   */\n\n\n  static required(control) {\n    return requiredValidator(control);\n  }\n  /**\n   * @description\n   * Validator that requires the control's value be true. This validator is commonly\n   * used for required checkboxes.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field value is true\n   *\n   * ```typescript\n   * const control = new FormControl('', Validators.requiredTrue);\n   *\n   * console.log(control.errors); // {required: true}\n   * ```\n   *\n   * @returns An error map that contains the `required` property\n   * set to `true` if the validation check fails, otherwise `null`.\n   *\n   * @see `updateValueAndValidity()`\n   *\n   */\n\n\n  static requiredTrue(control) {\n    return requiredTrueValidator(control);\n  }\n  /**\n   * @description\n   * Validator that requires the control's value pass an email validation test.\n   *\n   * Tests the value using a [regular\n   * expression](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions)\n   * pattern suitable for common usecases. The pattern is based on the definition of a valid email\n   * address in the [WHATWG HTML\n   * specification](https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address) with\n   * some enhancements to incorporate more RFC rules (such as rules related to domain names and the\n   * lengths of different parts of the address).\n   *\n   * The differences from the WHATWG version include:\n   * - Disallow `local-part` (the part before the `@` symbol) to begin or end with a period (`.`).\n   * - Disallow `local-part` to be longer than 64 characters.\n   * - Disallow the whole address to be longer than 254 characters.\n   *\n   * If this pattern does not satisfy your business needs, you can use `Validators.pattern()` to\n   * validate the value against a different pattern.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field matches a valid email pattern\n   *\n   * ```typescript\n   * const control = new FormControl('bad@', Validators.email);\n   *\n   * console.log(control.errors); // {email: true}\n   * ```\n   *\n   * @returns An error map with the `email` property\n   * if the validation check fails, otherwise `null`.\n   *\n   * @see `updateValueAndValidity()`\n   *\n   */\n\n\n  static email(control) {\n    return emailValidator(control);\n  }\n  /**\n   * @description\n   * Validator that requires the length of the control's value to be greater than or equal\n   * to the provided minimum length. This validator is also provided by default if you use the\n   * the HTML5 `minlength` attribute. Note that the `minLength` validator is intended to be used\n   * only for types that have a numeric `length` property, such as strings or arrays. The\n   * `minLength` validator logic is also not invoked for values when their `length` property is 0\n   * (for example in case of an empty string or an empty array), to support optional controls. You\n   * can use the standard `required` validator if empty values should not be considered valid.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field has a minimum of 3 characters\n   *\n   * ```typescript\n   * const control = new FormControl('ng', Validators.minLength(3));\n   *\n   * console.log(control.errors); // {minlength: {requiredLength: 3, actualLength: 2}}\n   * ```\n   *\n   * ```html\n   * <input minlength=\"5\">\n   * ```\n   *\n   * @returns A validator function that returns an error map with the\n   * `minlength` property if the validation check fails, otherwise `null`.\n   *\n   * @see `updateValueAndValidity()`\n   *\n   */\n\n\n  static minLength(minLength) {\n    return minLengthValidator(minLength);\n  }\n  /**\n   * @description\n   * Validator that requires the length of the control's value to be less than or equal\n   * to the provided maximum length. This validator is also provided by default if you use the\n   * the HTML5 `maxlength` attribute. Note that the `maxLength` validator is intended to be used\n   * only for types that have a numeric `length` property, such as strings or arrays.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field has maximum of 5 characters\n   *\n   * ```typescript\n   * const control = new FormControl('Angular', Validators.maxLength(5));\n   *\n   * console.log(control.errors); // {maxlength: {requiredLength: 5, actualLength: 7}}\n   * ```\n   *\n   * ```html\n   * <input maxlength=\"5\">\n   * ```\n   *\n   * @returns A validator function that returns an error map with the\n   * `maxlength` property if the validation check fails, otherwise `null`.\n   *\n   * @see `updateValueAndValidity()`\n   *\n   */\n\n\n  static maxLength(maxLength) {\n    return maxLengthValidator(maxLength);\n  }\n  /**\n   * @description\n   * Validator that requires the control's value to match a regex pattern. This validator is also\n   * provided by default if you use the HTML5 `pattern` attribute.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field only contains letters or spaces\n   *\n   * ```typescript\n   * const control = new FormControl('1', Validators.pattern('[a-zA-Z ]*'));\n   *\n   * console.log(control.errors); // {pattern: {requiredPattern: '^[a-zA-Z ]*$', actualValue: '1'}}\n   * ```\n   *\n   * ```html\n   * <input pattern=\"[a-zA-Z ]*\">\n   * ```\n   *\n   * ### Pattern matching with the global or sticky flag\n   *\n   * `RegExp` objects created with the `g` or `y` flags that are passed into `Validators.pattern`\n   * can produce different results on the same input when validations are run consecutively. This is\n   * due to how the behavior of `RegExp.prototype.test` is\n   * specified in [ECMA-262](https://tc39.es/ecma262/#sec-regexpbuiltinexec)\n   * (`RegExp` preserves the index of the last match when the global or sticky flag is used).\n   * Due to this behavior, it is recommended that when using\n   * `Validators.pattern` you **do not** pass in a `RegExp` object with either the global or sticky\n   * flag enabled.\n   *\n   * ```typescript\n   * // Not recommended (since the `g` flag is used)\n   * const controlOne = new FormControl('1', Validators.pattern(/foo/g));\n   *\n   * // Good\n   * const controlTwo = new FormControl('1', Validators.pattern(/foo/));\n   * ```\n   *\n   * @param pattern A regular expression to be used as is to test the values, or a string.\n   * If a string is passed, the `^` character is prepended and the `$` character is\n   * appended to the provided string (if not already present), and the resulting regular\n   * expression is used to test the values.\n   *\n   * @returns A validator function that returns an error map with the\n   * `pattern` property if the validation check fails, otherwise `null`.\n   *\n   * @see `updateValueAndValidity()`\n   *\n   */\n\n\n  static pattern(pattern) {\n    return patternValidator(pattern);\n  }\n  /**\n   * @description\n   * Validator that performs no operation.\n   *\n   * @see `updateValueAndValidity()`\n   *\n   */\n\n\n  static nullValidator(control) {\n    return nullValidator(control);\n  }\n\n  static compose(validators) {\n    return compose(validators);\n  }\n  /**\n   * @description\n   * Compose multiple async validators into a single function that returns the union\n   * of the individual error objects for the provided control.\n   *\n   * @returns A validator function that returns an error map with the\n   * merged error objects of the async validators if the validation check fails, otherwise `null`.\n   *\n   * @see `updateValueAndValidity()`\n   *\n   */\n\n\n  static composeAsync(validators) {\n    return composeAsync(validators);\n  }\n\n}\n/**\n * Validator that requires the control's value to be greater than or equal to the provided number.\n * See `Validators.min` for additional information.\n */\n\n\nfunction minValidator(min) {\n  return control => {\n    if (isEmptyInputValue(control.value) || isEmptyInputValue(min)) {\n      return null; // don't validate empty values to allow optional controls\n    }\n\n    const value = parseFloat(control.value); // Controls with NaN values after parsing should be treated as not having a\n    // minimum, per the HTML forms spec: https://www.w3.org/TR/html5/forms.html#attr-input-min\n\n    return !isNaN(value) && value < min ? {\n      'min': {\n        'min': min,\n        'actual': control.value\n      }\n    } : null;\n  };\n}\n/**\n * Validator that requires the control's value to be less than or equal to the provided number.\n * See `Validators.max` for additional information.\n */\n\n\nfunction maxValidator(max) {\n  return control => {\n    if (isEmptyInputValue(control.value) || isEmptyInputValue(max)) {\n      return null; // don't validate empty values to allow optional controls\n    }\n\n    const value = parseFloat(control.value); // Controls with NaN values after parsing should be treated as not having a\n    // maximum, per the HTML forms spec: https://www.w3.org/TR/html5/forms.html#attr-input-max\n\n    return !isNaN(value) && value > max ? {\n      'max': {\n        'max': max,\n        'actual': control.value\n      }\n    } : null;\n  };\n}\n/**\n * Validator that requires the control have a non-empty value.\n * See `Validators.required` for additional information.\n */\n\n\nfunction requiredValidator(control) {\n  return isEmptyInputValue(control.value) ? {\n    'required': true\n  } : null;\n}\n/**\n * Validator that requires the control's value be true. This validator is commonly\n * used for required checkboxes.\n * See `Validators.requiredTrue` for additional information.\n */\n\n\nfunction requiredTrueValidator(control) {\n  return control.value === true ? null : {\n    'required': true\n  };\n}\n/**\n * Validator that requires the control's value pass an email validation test.\n * See `Validators.email` for additional information.\n */\n\n\nfunction emailValidator(control) {\n  if (isEmptyInputValue(control.value)) {\n    return null; // don't validate empty values to allow optional controls\n  }\n\n  return EMAIL_REGEXP.test(control.value) ? null : {\n    'email': true\n  };\n}\n/**\n * Validator that requires the length of the control's value to be greater than or equal\n * to the provided minimum length. See `Validators.minLength` for additional information.\n */\n\n\nfunction minLengthValidator(minLength) {\n  return control => {\n    if (isEmptyInputValue(control.value) || !hasValidLength(control.value)) {\n      // don't validate empty values to allow optional controls\n      // don't validate values without `length` property\n      return null;\n    }\n\n    return control.value.length < minLength ? {\n      'minlength': {\n        'requiredLength': minLength,\n        'actualLength': control.value.length\n      }\n    } : null;\n  };\n}\n/**\n * Validator that requires the length of the control's value to be less than or equal\n * to the provided maximum length. See `Validators.maxLength` for additional information.\n */\n\n\nfunction maxLengthValidator(maxLength) {\n  return control => {\n    return hasValidLength(control.value) && control.value.length > maxLength ? {\n      'maxlength': {\n        'requiredLength': maxLength,\n        'actualLength': control.value.length\n      }\n    } : null;\n  };\n}\n/**\n * Validator that requires the control's value to match a regex pattern.\n * See `Validators.pattern` for additional information.\n */\n\n\nfunction patternValidator(pattern) {\n  if (!pattern) return nullValidator;\n  let regex;\n  let regexStr;\n\n  if (typeof pattern === 'string') {\n    regexStr = '';\n    if (pattern.charAt(0) !== '^') regexStr += '^';\n    regexStr += pattern;\n    if (pattern.charAt(pattern.length - 1) !== '$') regexStr += '$';\n    regex = new RegExp(regexStr);\n  } else {\n    regexStr = pattern.toString();\n    regex = pattern;\n  }\n\n  return control => {\n    if (isEmptyInputValue(control.value)) {\n      return null; // don't validate empty values to allow optional controls\n    }\n\n    const value = control.value;\n    return regex.test(value) ? null : {\n      'pattern': {\n        'requiredPattern': regexStr,\n        'actualValue': value\n      }\n    };\n  };\n}\n/**\n * Function that has `ValidatorFn` shape, but performs no operation.\n */\n\n\nfunction nullValidator(control) {\n  return null;\n}\n\nfunction isPresent(o) {\n  return o != null;\n}\n\nfunction toObservable(r) {\n  const obs = ɵisPromise(r) ? from(r) : r;\n\n  if (!ɵisObservable(obs) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n    throw new Error(`Expected validator to return Promise or Observable.`);\n  }\n\n  return obs;\n}\n\nfunction mergeErrors(arrayOfErrors) {\n  let res = {}; // Not using Array.reduce here due to a Chrome 80 bug\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=1049982\n\n  arrayOfErrors.forEach(errors => {\n    res = errors != null ? Object.assign(Object.assign({}, res), errors) : res;\n  });\n  return Object.keys(res).length === 0 ? null : res;\n}\n\nfunction executeValidators(control, validators) {\n  return validators.map(validator => validator(control));\n}\n\nfunction isValidatorFn(validator) {\n  return !validator.validate;\n}\n/**\n * Given the list of validators that may contain both functions as well as classes, return the list\n * of validator functions (convert validator classes into validator functions). This is needed to\n * have consistent structure in validators list before composing them.\n *\n * @param validators The set of validators that may contain validators both in plain function form\n *     as well as represented as a validator class.\n */\n\n\nfunction normalizeValidators(validators) {\n  return validators.map(validator => {\n    return isValidatorFn(validator) ? validator : c => validator.validate(c);\n  });\n}\n/**\n * Merges synchronous validators into a single validator function.\n * See `Validators.compose` for additional information.\n */\n\n\nfunction compose(validators) {\n  if (!validators) return null;\n  const presentValidators = validators.filter(isPresent);\n  if (presentValidators.length == 0) return null;\n  return function (control) {\n    return mergeErrors(executeValidators(control, presentValidators));\n  };\n}\n/**\n * Accepts a list of validators of different possible shapes (`Validator` and `ValidatorFn`),\n * normalizes the list (converts everything to `ValidatorFn`) and merges them into a single\n * validator function.\n */\n\n\nfunction composeValidators(validators) {\n  return validators != null ? compose(normalizeValidators(validators)) : null;\n}\n/**\n * Merges asynchronous validators into a single validator function.\n * See `Validators.composeAsync` for additional information.\n */\n\n\nfunction composeAsync(validators) {\n  if (!validators) return null;\n  const presentValidators = validators.filter(isPresent);\n  if (presentValidators.length == 0) return null;\n  return function (control) {\n    const observables = executeValidators(control, presentValidators).map(toObservable);\n    return forkJoin(observables).pipe(map(mergeErrors));\n  };\n}\n/**\n * Accepts a list of async validators of different possible shapes (`AsyncValidator` and\n * `AsyncValidatorFn`), normalizes the list (converts everything to `AsyncValidatorFn`) and merges\n * them into a single validator function.\n */\n\n\nfunction composeAsyncValidators(validators) {\n  return validators != null ? composeAsync(normalizeValidators(validators)) : null;\n}\n/**\n * Merges raw control validators with a given directive validator and returns the combined list of\n * validators as an array.\n */\n\n\nfunction mergeValidators(controlValidators, dirValidator) {\n  if (controlValidators === null) return [dirValidator];\n  return Array.isArray(controlValidators) ? [...controlValidators, dirValidator] : [controlValidators, dirValidator];\n}\n/**\n * Retrieves the list of raw synchronous validators attached to a given control.\n */\n\n\nfunction getControlValidators(control) {\n  return control._rawValidators;\n}\n/**\n * Retrieves the list of raw asynchronous validators attached to a given control.\n */\n\n\nfunction getControlAsyncValidators(control) {\n  return control._rawAsyncValidators;\n}\n/**\n * Accepts a singleton validator, an array, or null, and returns an array type with the provided\n * validators.\n *\n * @param validators A validator, validators, or null.\n * @returns A validators array.\n */\n\n\nfunction makeValidatorsArray(validators) {\n  if (!validators) return [];\n  return Array.isArray(validators) ? validators : [validators];\n}\n/**\n * Determines whether a validator or validators array has a given validator.\n *\n * @param validators The validator or validators to compare against.\n * @param validator The validator to check.\n * @returns Whether the validator is present.\n */\n\n\nfunction hasValidator(validators, validator) {\n  return Array.isArray(validators) ? validators.includes(validator) : validators === validator;\n}\n/**\n * Combines two arrays of validators into one. If duplicates are provided, only one will be added.\n *\n * @param validators The new validators.\n * @param currentValidators The base array of currrent validators.\n * @returns An array of validators.\n */\n\n\nfunction addValidators(validators, currentValidators) {\n  const current = makeValidatorsArray(currentValidators);\n  const validatorsToAdd = makeValidatorsArray(validators);\n  validatorsToAdd.forEach(v => {\n    // Note: if there are duplicate entries in the new validators array,\n    // only the first one would be added to the current list of validarors.\n    // Duplicate ones would be ignored since `hasValidator` would detect\n    // the presence of a validator function and we update the current list in place.\n    if (!hasValidator(current, v)) {\n      current.push(v);\n    }\n  });\n  return current;\n}\n\nfunction removeValidators(validators, currentValidators) {\n  return makeValidatorsArray(currentValidators).filter(v => !hasValidator(validators, v));\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @description\n * Base class for control directives.\n *\n * This class is only used internally in the `ReactiveFormsModule` and the `FormsModule`.\n *\n * @publicApi\n */\n\n\nclass AbstractControlDirective {\n  constructor() {\n    /**\n     * Set of synchronous validators as they were provided while calling `setValidators` function.\n     * @internal\n     */\n    this._rawValidators = [];\n    /**\n     * Set of asynchronous validators as they were provided while calling `setAsyncValidators`\n     * function.\n     * @internal\n     */\n\n    this._rawAsyncValidators = [];\n    /*\n     * The set of callbacks to be invoked when directive instance is being destroyed.\n     */\n\n    this._onDestroyCallbacks = [];\n  }\n  /**\n   * @description\n   * Reports the value of the control if it is present, otherwise null.\n   */\n\n\n  get value() {\n    return this.control ? this.control.value : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is valid. A control is considered valid if no\n   * validation errors exist with the current value.\n   * If the control is not present, null is returned.\n   */\n\n\n  get valid() {\n    return this.control ? this.control.valid : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is invalid, meaning that an error exists in the input value.\n   * If the control is not present, null is returned.\n   */\n\n\n  get invalid() {\n    return this.control ? this.control.invalid : null;\n  }\n  /**\n   * @description\n   * Reports whether a control is pending, meaning that that async validation is occurring and\n   * errors are not yet available for the input value. If the control is not present, null is\n   * returned.\n   */\n\n\n  get pending() {\n    return this.control ? this.control.pending : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is disabled, meaning that the control is disabled\n   * in the UI and is exempt from validation checks and excluded from aggregate\n   * values of ancestor controls. If the control is not present, null is returned.\n   */\n\n\n  get disabled() {\n    return this.control ? this.control.disabled : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is enabled, meaning that the control is included in ancestor\n   * calculations of validity or value. If the control is not present, null is returned.\n   */\n\n\n  get enabled() {\n    return this.control ? this.control.enabled : null;\n  }\n  /**\n   * @description\n   * Reports the control's validation errors. If the control is not present, null is returned.\n   */\n\n\n  get errors() {\n    return this.control ? this.control.errors : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is pristine, meaning that the user has not yet changed\n   * the value in the UI. If the control is not present, null is returned.\n   */\n\n\n  get pristine() {\n    return this.control ? this.control.pristine : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is dirty, meaning that the user has changed\n   * the value in the UI. If the control is not present, null is returned.\n   */\n\n\n  get dirty() {\n    return this.control ? this.control.dirty : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is touched, meaning that the user has triggered\n   * a `blur` event on it. If the control is not present, null is returned.\n   */\n\n\n  get touched() {\n    return this.control ? this.control.touched : null;\n  }\n  /**\n   * @description\n   * Reports the validation status of the control. Possible values include:\n   * 'VALID', 'INVALID', 'DISABLED', and 'PENDING'.\n   * If the control is not present, null is returned.\n   */\n\n\n  get status() {\n    return this.control ? this.control.status : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is untouched, meaning that the user has not yet triggered\n   * a `blur` event on it. If the control is not present, null is returned.\n   */\n\n\n  get untouched() {\n    return this.control ? this.control.untouched : null;\n  }\n  /**\n   * @description\n   * Returns a multicasting observable that emits a validation status whenever it is\n   * calculated for the control. If the control is not present, null is returned.\n   */\n\n\n  get statusChanges() {\n    return this.control ? this.control.statusChanges : null;\n  }\n  /**\n   * @description\n   * Returns a multicasting observable of value changes for the control that emits every time the\n   * value of the control changes in the UI or programmatically.\n   * If the control is not present, null is returned.\n   */\n\n\n  get valueChanges() {\n    return this.control ? this.control.valueChanges : null;\n  }\n  /**\n   * @description\n   * Returns an array that represents the path from the top-level form to this control.\n   * Each index is the string name of the control on that level.\n   */\n\n\n  get path() {\n    return null;\n  }\n  /**\n   * Sets synchronous validators for this directive.\n   * @internal\n   */\n\n\n  _setValidators(validators) {\n    this._rawValidators = validators || [];\n    this._composedValidatorFn = composeValidators(this._rawValidators);\n  }\n  /**\n   * Sets asynchronous validators for this directive.\n   * @internal\n   */\n\n\n  _setAsyncValidators(validators) {\n    this._rawAsyncValidators = validators || [];\n    this._composedAsyncValidatorFn = composeAsyncValidators(this._rawAsyncValidators);\n  }\n  /**\n   * @description\n   * Synchronous validator function composed of all the synchronous validators registered with this\n   * directive.\n   */\n\n\n  get validator() {\n    return this._composedValidatorFn || null;\n  }\n  /**\n   * @description\n   * Asynchronous validator function composed of all the asynchronous validators registered with\n   * this directive.\n   */\n\n\n  get asyncValidator() {\n    return this._composedAsyncValidatorFn || null;\n  }\n  /**\n   * Internal function to register callbacks that should be invoked\n   * when directive instance is being destroyed.\n   * @internal\n   */\n\n\n  _registerOnDestroy(fn) {\n    this._onDestroyCallbacks.push(fn);\n  }\n  /**\n   * Internal function to invoke all registered \"on destroy\" callbacks.\n   * Note: calling this function also clears the list of callbacks.\n   * @internal\n   */\n\n\n  _invokeOnDestroyCallbacks() {\n    this._onDestroyCallbacks.forEach(fn => fn());\n\n    this._onDestroyCallbacks = [];\n  }\n  /**\n   * @description\n   * Resets the control with the provided value if the control is present.\n   */\n\n\n  reset(value = undefined) {\n    if (this.control) this.control.reset(value);\n  }\n  /**\n   * @description\n   * Reports whether the control with the given path has the error specified.\n   *\n   * @param errorCode The code of the error to check\n   * @param path A list of control names that designates how to move from the current control\n   * to the control that should be queried for errors.\n   *\n   * @usageNotes\n   * For example, for the following `FormGroup`:\n   *\n   * ```\n   * form = new FormGroup({\n   *   address: new FormGroup({ street: new FormControl() })\n   * });\n   * ```\n   *\n   * The path to the 'street' control from the root form would be 'address' -> 'street'.\n   *\n   * It can be provided to this method in one of two formats:\n   *\n   * 1. An array of string control names, e.g. `['address', 'street']`\n   * 1. A period-delimited list of control names in one string, e.g. `'address.street'`\n   *\n   * If no path is given, this method checks for the error on the current control.\n   *\n   * @returns whether the given error is present in the control at the given path.\n   *\n   * If the control is not present, false is returned.\n   */\n\n\n  hasError(errorCode, path) {\n    return this.control ? this.control.hasError(errorCode, path) : false;\n  }\n  /**\n   * @description\n   * Reports error data for the control with the given path.\n   *\n   * @param errorCode The code of the error to check\n   * @param path A list of control names that designates how to move from the current control\n   * to the control that should be queried for errors.\n   *\n   * @usageNotes\n   * For example, for the following `FormGroup`:\n   *\n   * ```\n   * form = new FormGroup({\n   *   address: new FormGroup({ street: new FormControl() })\n   * });\n   * ```\n   *\n   * The path to the 'street' control from the root form would be 'address' -> 'street'.\n   *\n   * It can be provided to this method in one of two formats:\n   *\n   * 1. An array of string control names, e.g. `['address', 'street']`\n   * 1. A period-delimited list of control names in one string, e.g. `'address.street'`\n   *\n   * @returns error data for that particular error. If the control or error is not present,\n   * null is returned.\n   */\n\n\n  getError(errorCode, path) {\n    return this.control ? this.control.getError(errorCode, path) : null;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @description\n * A base class that all `FormControl`-based directives extend. It binds a `FormControl`\n * object to a DOM element.\n *\n * @publicApi\n */\n\n\nclass NgControl extends AbstractControlDirective {\n  constructor() {\n    super(...arguments);\n    /**\n     * @description\n     * The parent form for the control.\n     *\n     * @internal\n     */\n\n    this._parent = null;\n    /**\n     * @description\n     * The name for the control\n     */\n\n    this.name = null;\n    /**\n     * @description\n     * The value accessor for the control\n     */\n\n    this.valueAccessor = null;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @description\n * A base class for directives that contain multiple registered instances of `NgControl`.\n * Only used by the forms module.\n *\n * @publicApi\n */\n\n\nclass ControlContainer extends AbstractControlDirective {\n  /**\n   * @description\n   * The top-level form directive for the control.\n   */\n  get formDirective() {\n    return null;\n  }\n  /**\n   * @description\n   * The path to this group.\n   */\n\n\n  get path() {\n    return null;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass AbstractControlStatus {\n  constructor(cd) {\n    this._cd = cd;\n  }\n\n  is(status) {\n    var _a, _b, _c; // Currently with ViewEngine (in AOT mode) it's not possible to use private methods in host\n    // bindings.\n    // TODO: once ViewEngine is removed, this function should be refactored:\n    //  - make the `is` method `protected`, so it's not accessible publicly\n    //  - move the `submitted` status logic to the `NgControlStatusGroup` class\n    //    and make it `private` or `protected` too.\n\n\n    if (status === 'submitted') {\n      // We check for the `submitted` field from `NgForm` and `FormGroupDirective` classes, but\n      // we avoid instanceof checks to prevent non-tree-shakable references to those types.\n      return !!((_a = this._cd) === null || _a === void 0 ? void 0 : _a.submitted);\n    }\n\n    return !!((_c = (_b = this._cd) === null || _b === void 0 ? void 0 : _b.control) === null || _c === void 0 ? void 0 : _c[status]);\n  }\n\n}\n\nconst ngControlStatusHost = {\n  '[class.ng-untouched]': 'is(\"untouched\")',\n  '[class.ng-touched]': 'is(\"touched\")',\n  '[class.ng-pristine]': 'is(\"pristine\")',\n  '[class.ng-dirty]': 'is(\"dirty\")',\n  '[class.ng-valid]': 'is(\"valid\")',\n  '[class.ng-invalid]': 'is(\"invalid\")',\n  '[class.ng-pending]': 'is(\"pending\")'\n};\nconst ngGroupStatusHost = {\n  '[class.ng-untouched]': 'is(\"untouched\")',\n  '[class.ng-touched]': 'is(\"touched\")',\n  '[class.ng-pristine]': 'is(\"pristine\")',\n  '[class.ng-dirty]': 'is(\"dirty\")',\n  '[class.ng-valid]': 'is(\"valid\")',\n  '[class.ng-invalid]': 'is(\"invalid\")',\n  '[class.ng-pending]': 'is(\"pending\")',\n  '[class.ng-submitted]': 'is(\"submitted\")'\n};\n/**\n * @description\n * Directive automatically applied to Angular form controls that sets CSS classes\n * based on control status.\n *\n * @usageNotes\n *\n * ### CSS classes applied\n *\n * The following classes are applied as the properties become true:\n *\n * * ng-valid\n * * ng-invalid\n * * ng-pending\n * * ng-pristine\n * * ng-dirty\n * * ng-untouched\n * * ng-touched\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\nlet NgControlStatus = /*#__PURE__*/(() => {\n  class NgControlStatus extends AbstractControlStatus {\n    constructor(cd) {\n      super(cd);\n    }\n\n  }\n\n  NgControlStatus.ɵfac = function NgControlStatus_Factory(t) {\n    return new (t || NgControlStatus)(i0.ɵɵdirectiveInject(NgControl, 2));\n  };\n\n  NgControlStatus.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgControlStatus,\n    selectors: [[\"\", \"formControlName\", \"\"], [\"\", \"ngModel\", \"\"], [\"\", \"formControl\", \"\"]],\n    hostVars: 14,\n    hostBindings: function NgControlStatus_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ng-untouched\", ctx.is(\"untouched\"))(\"ng-touched\", ctx.is(\"touched\"))(\"ng-pristine\", ctx.is(\"pristine\"))(\"ng-dirty\", ctx.is(\"dirty\"))(\"ng-valid\", ctx.is(\"valid\"))(\"ng-invalid\", ctx.is(\"invalid\"))(\"ng-pending\", ctx.is(\"pending\"));\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return NgControlStatus;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n * Directive automatically applied to Angular form groups that sets CSS classes\n * based on control status (valid/invalid/dirty/etc). On groups, this includes the additional\n * class ng-submitted.\n *\n * @see `NgControlStatus`\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\n\nlet NgControlStatusGroup = /*#__PURE__*/(() => {\n  class NgControlStatusGroup extends AbstractControlStatus {\n    constructor(cd) {\n      super(cd);\n    }\n\n  }\n\n  NgControlStatusGroup.ɵfac = function NgControlStatusGroup_Factory(t) {\n    return new (t || NgControlStatusGroup)(i0.ɵɵdirectiveInject(ControlContainer, 10));\n  };\n\n  NgControlStatusGroup.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgControlStatusGroup,\n    selectors: [[\"\", \"formGroupName\", \"\"], [\"\", \"formArrayName\", \"\"], [\"\", \"ngModelGroup\", \"\"], [\"\", \"formGroup\", \"\"], [\"form\", 3, \"ngNoForm\", \"\"], [\"\", \"ngForm\", \"\"]],\n    hostVars: 16,\n    hostBindings: function NgControlStatusGroup_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ng-untouched\", ctx.is(\"untouched\"))(\"ng-touched\", ctx.is(\"touched\"))(\"ng-pristine\", ctx.is(\"pristine\"))(\"ng-dirty\", ctx.is(\"dirty\"))(\"ng-valid\", ctx.is(\"valid\"))(\"ng-invalid\", ctx.is(\"invalid\"))(\"ng-pending\", ctx.is(\"pending\"))(\"ng-submitted\", ctx.is(\"submitted\"));\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return NgControlStatusGroup;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst formControlNameExample = `\n  <div [formGroup]=\"myGroup\">\n    <input formControlName=\"firstName\">\n  </div>\n\n  In your class:\n\n  this.myGroup = new FormGroup({\n      firstName: new FormControl()\n  });`;\nconst formGroupNameExample = `\n  <div [formGroup]=\"myGroup\">\n      <div formGroupName=\"person\">\n        <input formControlName=\"firstName\">\n      </div>\n  </div>\n\n  In your class:\n\n  this.myGroup = new FormGroup({\n      person: new FormGroup({ firstName: new FormControl() })\n  });`;\nconst formArrayNameExample = `\n  <div [formGroup]=\"myGroup\">\n    <div formArrayName=\"cities\">\n      <div *ngFor=\"let city of cityArray.controls; index as i\">\n        <input [formControlName]=\"i\">\n      </div>\n    </div>\n  </div>\n\n  In your class:\n\n  this.cityArray = new FormArray([new FormControl('SF')]);\n  this.myGroup = new FormGroup({\n    cities: this.cityArray\n  });`;\nconst ngModelGroupExample = `\n  <form>\n      <div ngModelGroup=\"person\">\n        <input [(ngModel)]=\"person.name\" name=\"firstName\">\n      </div>\n  </form>`;\nconst ngModelWithFormGroupExample = `\n  <div [formGroup]=\"myGroup\">\n      <input formControlName=\"firstName\">\n      <input [(ngModel)]=\"showMoreControls\" [ngModelOptions]=\"{standalone: true}\">\n  </div>\n`;\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nfunction controlParentException() {\n  return new Error(`formControlName must be used with a parent formGroup directive.  You'll want to add a formGroup\n      directive and pass it an existing FormGroup instance (you can create one in your class).\n\n    Example:\n\n    ${formControlNameExample}`);\n}\n\nfunction ngModelGroupException() {\n  return new Error(`formControlName cannot be used with an ngModelGroup parent. It is only compatible with parents\n      that also have a \"form\" prefix: formGroupName, formArrayName, or formGroup.\n\n      Option 1:  Update the parent to be formGroupName (reactive form strategy)\n\n      ${formGroupNameExample}\n\n      Option 2: Use ngModel instead of formControlName (template-driven strategy)\n\n      ${ngModelGroupExample}`);\n}\n\nfunction missingFormException() {\n  return new Error(`formGroup expects a FormGroup instance. Please pass one in.\n\n      Example:\n\n      ${formControlNameExample}`);\n}\n\nfunction groupParentException() {\n  return new Error(`formGroupName must be used with a parent formGroup directive.  You'll want to add a formGroup\n    directive and pass it an existing FormGroup instance (you can create one in your class).\n\n    Example:\n\n    ${formGroupNameExample}`);\n}\n\nfunction arrayParentException() {\n  return new Error(`formArrayName must be used with a parent formGroup directive.  You'll want to add a formGroup\n      directive and pass it an existing FormGroup instance (you can create one in your class).\n\n      Example:\n\n      ${formArrayNameExample}`);\n}\n\nconst disabledAttrWarning = `\n  It looks like you're using the disabled attribute with a reactive form directive. If you set disabled to true\n  when you set up this control in your component class, the disabled attribute will actually be set in the DOM for\n  you. We recommend using this approach to avoid 'changed after checked' errors.\n\n  Example:\n  form = new FormGroup({\n    first: new FormControl({value: 'Nancy', disabled: true}, Validators.required),\n    last: new FormControl('Drew', Validators.required)\n  });\n`;\n\nfunction ngModelWarning(directiveName) {\n  return `\n  It looks like you're using ngModel on the same form field as ${directiveName}.\n  Support for using the ngModel input property and ngModelChange event with\n  reactive form directives has been deprecated in Angular v6 and will be removed\n  in a future version of Angular.\n\n  For more information on this, see our API docs here:\n  https://angular.io/api/forms/${directiveName === 'formControl' ? 'FormControlDirective' : 'FormControlName'}#use-with-ngmodel\n  `;\n}\n\nfunction describeKey(isFormGroup, key) {\n  return isFormGroup ? `with name: '${key}'` : `at index: ${key}`;\n}\n\nfunction noControlsError(isFormGroup) {\n  return `\n    There are no form controls registered with this ${isFormGroup ? 'group' : 'array'} yet. If you're using ngModel,\n    you may want to check next tick (e.g. use setTimeout).\n  `;\n}\n\nfunction missingControlError(isFormGroup, key) {\n  return `Cannot find form control ${describeKey(isFormGroup, key)}`;\n}\n\nfunction missingControlValueError(isFormGroup, key) {\n  return `Must supply a value for form control ${describeKey(isFormGroup, key)}`;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction controlPath(name, parent) {\n  return [...parent.path, name];\n}\n/**\n * Links a Form control and a Form directive by setting up callbacks (such as `onChange`) on both\n * instances. This function is typically invoked when form directive is being initialized.\n *\n * @param control Form control instance that should be linked.\n * @param dir Directive that should be linked with a given control.\n */\n\n\nfunction setUpControl(control, dir) {\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    if (!control) _throwError(dir, 'Cannot find control with');\n    if (!dir.valueAccessor) _throwError(dir, 'No value accessor for form control with');\n  }\n\n  setUpValidators(control, dir);\n  dir.valueAccessor.writeValue(control.value);\n  setUpViewChangePipeline(control, dir);\n  setUpModelChangePipeline(control, dir);\n  setUpBlurPipeline(control, dir);\n  setUpDisabledChangeHandler(control, dir);\n}\n/**\n * Reverts configuration performed by the `setUpControl` control function.\n * Effectively disconnects form control with a given form directive.\n * This function is typically invoked when corresponding form directive is being destroyed.\n *\n * @param control Form control which should be cleaned up.\n * @param dir Directive that should be disconnected from a given control.\n * @param validateControlPresenceOnChange Flag that indicates whether onChange handler should\n *     contain asserts to verify that it's not called once directive is destroyed. We need this flag\n *     to avoid potentially breaking changes caused by better control cleanup introduced in #39235.\n */\n\n\nfunction cleanUpControl(control, dir, validateControlPresenceOnChange = true) {\n  const noop = () => {\n    if (validateControlPresenceOnChange && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      _noControlError(dir);\n    }\n  }; // The `valueAccessor` field is typically defined on FromControl and FormControlName directive\n  // instances and there is a logic in `selectValueAccessor` function that throws if it's not the\n  // case. We still check the presence of `valueAccessor` before invoking its methods to make sure\n  // that cleanup works correctly if app code or tests are setup to ignore the error thrown from\n  // `selectValueAccessor`. See https://github.com/angular/angular/issues/40521.\n\n\n  if (dir.valueAccessor) {\n    dir.valueAccessor.registerOnChange(noop);\n    dir.valueAccessor.registerOnTouched(noop);\n  }\n\n  cleanUpValidators(control, dir);\n\n  if (control) {\n    dir._invokeOnDestroyCallbacks();\n\n    control._registerOnCollectionChange(() => {});\n  }\n}\n\nfunction registerOnValidatorChange(validators, onChange) {\n  validators.forEach(validator => {\n    if (validator.registerOnValidatorChange) validator.registerOnValidatorChange(onChange);\n  });\n}\n/**\n * Sets up disabled change handler function on a given form control if ControlValueAccessor\n * associated with a given directive instance supports the `setDisabledState` call.\n *\n * @param control Form control where disabled change handler should be setup.\n * @param dir Corresponding directive instance associated with this control.\n */\n\n\nfunction setUpDisabledChangeHandler(control, dir) {\n  if (dir.valueAccessor.setDisabledState) {\n    const onDisabledChange = isDisabled => {\n      dir.valueAccessor.setDisabledState(isDisabled);\n    };\n\n    control.registerOnDisabledChange(onDisabledChange); // Register a callback function to cleanup disabled change handler\n    // from a control instance when a directive is destroyed.\n\n    dir._registerOnDestroy(() => {\n      control._unregisterOnDisabledChange(onDisabledChange);\n    });\n  }\n}\n/**\n * Sets up sync and async directive validators on provided form control.\n * This function merges validators from the directive into the validators of the control.\n *\n * @param control Form control where directive validators should be setup.\n * @param dir Directive instance that contains validators to be setup.\n */\n\n\nfunction setUpValidators(control, dir) {\n  const validators = getControlValidators(control);\n\n  if (dir.validator !== null) {\n    control.setValidators(mergeValidators(validators, dir.validator));\n  } else if (typeof validators === 'function') {\n    // If sync validators are represented by a single validator function, we force the\n    // `Validators.compose` call to happen by executing the `setValidators` function with\n    // an array that contains that function. We need this to avoid possible discrepancies in\n    // validators behavior, so sync validators are always processed by the `Validators.compose`.\n    // Note: we should consider moving this logic inside the `setValidators` function itself, so we\n    // have consistent behavior on AbstractControl API level. The same applies to the async\n    // validators logic below.\n    control.setValidators([validators]);\n  }\n\n  const asyncValidators = getControlAsyncValidators(control);\n\n  if (dir.asyncValidator !== null) {\n    control.setAsyncValidators(mergeValidators(asyncValidators, dir.asyncValidator));\n  } else if (typeof asyncValidators === 'function') {\n    control.setAsyncValidators([asyncValidators]);\n  } // Re-run validation when validator binding changes, e.g. minlength=3 -> minlength=4\n\n\n  const onValidatorChange = () => control.updateValueAndValidity();\n\n  registerOnValidatorChange(dir._rawValidators, onValidatorChange);\n  registerOnValidatorChange(dir._rawAsyncValidators, onValidatorChange);\n}\n/**\n * Cleans up sync and async directive validators on provided form control.\n * This function reverts the setup performed by the `setUpValidators` function, i.e.\n * removes directive-specific validators from a given control instance.\n *\n * @param control Form control from where directive validators should be removed.\n * @param dir Directive instance that contains validators to be removed.\n * @returns true if a control was updated as a result of this action.\n */\n\n\nfunction cleanUpValidators(control, dir) {\n  let isControlUpdated = false;\n\n  if (control !== null) {\n    if (dir.validator !== null) {\n      const validators = getControlValidators(control);\n\n      if (Array.isArray(validators) && validators.length > 0) {\n        // Filter out directive validator function.\n        const updatedValidators = validators.filter(validator => validator !== dir.validator);\n\n        if (updatedValidators.length !== validators.length) {\n          isControlUpdated = true;\n          control.setValidators(updatedValidators);\n        }\n      }\n    }\n\n    if (dir.asyncValidator !== null) {\n      const asyncValidators = getControlAsyncValidators(control);\n\n      if (Array.isArray(asyncValidators) && asyncValidators.length > 0) {\n        // Filter out directive async validator function.\n        const updatedAsyncValidators = asyncValidators.filter(asyncValidator => asyncValidator !== dir.asyncValidator);\n\n        if (updatedAsyncValidators.length !== asyncValidators.length) {\n          isControlUpdated = true;\n          control.setAsyncValidators(updatedAsyncValidators);\n        }\n      }\n    }\n  } // Clear onValidatorChange callbacks by providing a noop function.\n\n\n  const noop = () => {};\n\n  registerOnValidatorChange(dir._rawValidators, noop);\n  registerOnValidatorChange(dir._rawAsyncValidators, noop);\n  return isControlUpdated;\n}\n\nfunction setUpViewChangePipeline(control, dir) {\n  dir.valueAccessor.registerOnChange(newValue => {\n    control._pendingValue = newValue;\n    control._pendingChange = true;\n    control._pendingDirty = true;\n    if (control.updateOn === 'change') updateControl(control, dir);\n  });\n}\n\nfunction setUpBlurPipeline(control, dir) {\n  dir.valueAccessor.registerOnTouched(() => {\n    control._pendingTouched = true;\n    if (control.updateOn === 'blur' && control._pendingChange) updateControl(control, dir);\n    if (control.updateOn !== 'submit') control.markAsTouched();\n  });\n}\n\nfunction updateControl(control, dir) {\n  if (control._pendingDirty) control.markAsDirty();\n  control.setValue(control._pendingValue, {\n    emitModelToViewChange: false\n  });\n  dir.viewToModelUpdate(control._pendingValue);\n  control._pendingChange = false;\n}\n\nfunction setUpModelChangePipeline(control, dir) {\n  const onChange = (newValue, emitModelEvent) => {\n    // control -> view\n    dir.valueAccessor.writeValue(newValue); // control -> ngModel\n\n    if (emitModelEvent) dir.viewToModelUpdate(newValue);\n  };\n\n  control.registerOnChange(onChange); // Register a callback function to cleanup onChange handler\n  // from a control instance when a directive is destroyed.\n\n  dir._registerOnDestroy(() => {\n    control._unregisterOnChange(onChange);\n  });\n}\n/**\n * Links a FormGroup or FormArray instance and corresponding Form directive by setting up validators\n * present in the view.\n *\n * @param control FormGroup or FormArray instance that should be linked.\n * @param dir Directive that provides view validators.\n */\n\n\nfunction setUpFormContainer(control, dir) {\n  if (control == null && (typeof ngDevMode === 'undefined' || ngDevMode)) _throwError(dir, 'Cannot find control with');\n  setUpValidators(control, dir);\n}\n/**\n * Reverts the setup performed by the `setUpFormContainer` function.\n *\n * @param control FormGroup or FormArray instance that should be cleaned up.\n * @param dir Directive that provided view validators.\n * @returns true if a control was updated as a result of this action.\n */\n\n\nfunction cleanUpFormContainer(control, dir) {\n  return cleanUpValidators(control, dir);\n}\n\nfunction _noControlError(dir) {\n  return _throwError(dir, 'There is no FormControl instance attached to form control element with');\n}\n\nfunction _throwError(dir, message) {\n  let messageEnd;\n\n  if (dir.path.length > 1) {\n    messageEnd = `path: '${dir.path.join(' -> ')}'`;\n  } else if (dir.path[0]) {\n    messageEnd = `name: '${dir.path}'`;\n  } else {\n    messageEnd = 'unspecified name attribute';\n  }\n\n  throw new Error(`${message} ${messageEnd}`);\n}\n\nfunction isPropertyUpdated(changes, viewModel) {\n  if (!changes.hasOwnProperty('model')) return false;\n  const change = changes['model'];\n  if (change.isFirstChange()) return true;\n  return !Object.is(viewModel, change.currentValue);\n}\n\nfunction isBuiltInAccessor(valueAccessor) {\n  // Check if a given value accessor is an instance of a class that directly extends\n  // `BuiltInControlValueAccessor` one.\n  return Object.getPrototypeOf(valueAccessor.constructor) === BuiltInControlValueAccessor;\n}\n\nfunction syncPendingControls(form, directives) {\n  form._syncPendingControls();\n\n  directives.forEach(dir => {\n    const control = dir.control;\n\n    if (control.updateOn === 'submit' && control._pendingChange) {\n      dir.viewToModelUpdate(control._pendingValue);\n      control._pendingChange = false;\n    }\n  });\n} // TODO: vsavkin remove it once https://github.com/angular/angular/issues/3011 is implemented\n\n\nfunction selectValueAccessor(dir, valueAccessors) {\n  if (!valueAccessors) return null;\n  if (!Array.isArray(valueAccessors) && (typeof ngDevMode === 'undefined' || ngDevMode)) _throwError(dir, 'Value accessor was not provided as an array for form control with');\n  let defaultAccessor = undefined;\n  let builtinAccessor = undefined;\n  let customAccessor = undefined;\n  valueAccessors.forEach(v => {\n    if (v.constructor === DefaultValueAccessor) {\n      defaultAccessor = v;\n    } else if (isBuiltInAccessor(v)) {\n      if (builtinAccessor && (typeof ngDevMode === 'undefined' || ngDevMode)) _throwError(dir, 'More than one built-in value accessor matches form control with');\n      builtinAccessor = v;\n    } else {\n      if (customAccessor && (typeof ngDevMode === 'undefined' || ngDevMode)) _throwError(dir, 'More than one custom value accessor matches form control with');\n      customAccessor = v;\n    }\n  });\n  if (customAccessor) return customAccessor;\n  if (builtinAccessor) return builtinAccessor;\n  if (defaultAccessor) return defaultAccessor;\n\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    _throwError(dir, 'No valid value accessor for form control with');\n  }\n\n  return null;\n}\n\nfunction removeListItem(list, el) {\n  const index = list.indexOf(el);\n  if (index > -1) list.splice(index, 1);\n} // TODO(kara): remove after deprecation period\n\n\nfunction _ngModelWarning(name, type, instance, warningConfig) {\n  if (warningConfig === 'never') return;\n\n  if ((warningConfig === null || warningConfig === 'once') && !type._ngModelWarningSentOnce || warningConfig === 'always' && !instance._ngModelWarningSent) {\n    console.warn(ngModelWarning(name));\n    type._ngModelWarningSentOnce = true;\n    instance._ngModelWarningSent = true;\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\n/**\n * Reports that a FormControl is valid, meaning that no errors exist in the input value.\n *\n * @see `status`\n */\n\nconst VALID = 'VALID';\n/**\n * Reports that a FormControl is invalid, meaning that an error exists in the input value.\n *\n * @see `status`\n */\n\nconst INVALID = 'INVALID';\n/**\n * Reports that a FormControl is pending, meaning that that async validation is occurring and\n * errors are not yet available for the input value.\n *\n * @see `markAsPending`\n * @see `status`\n */\n\nconst PENDING = 'PENDING';\n/**\n * Reports that a FormControl is disabled, meaning that the control is exempt from ancestor\n * calculations of validity or value.\n *\n * @see `markAsDisabled`\n * @see `status`\n */\n\nconst DISABLED = 'DISABLED';\n\nfunction _find(control, path, delimiter) {\n  if (path == null) return null;\n\n  if (!Array.isArray(path)) {\n    path = path.split(delimiter);\n  }\n\n  if (Array.isArray(path) && path.length === 0) return null; // Not using Array.reduce here due to a Chrome 80 bug\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=1049982\n\n  let controlToFind = control;\n  path.forEach(name => {\n    if (isFormGroup(controlToFind)) {\n      controlToFind = controlToFind.controls.hasOwnProperty(name) ? controlToFind.controls[name] : null;\n    } else if (isFormArray(controlToFind)) {\n      controlToFind = controlToFind.at(name) || null;\n    } else {\n      controlToFind = null;\n    }\n  });\n  return controlToFind;\n}\n/**\n * Gets validators from either an options object or given validators.\n */\n\n\nfunction pickValidators(validatorOrOpts) {\n  return (isOptionsObj(validatorOrOpts) ? validatorOrOpts.validators : validatorOrOpts) || null;\n}\n/**\n * Creates validator function by combining provided validators.\n */\n\n\nfunction coerceToValidator(validator) {\n  return Array.isArray(validator) ? composeValidators(validator) : validator || null;\n}\n/**\n * Gets async validators from either an options object or given validators.\n */\n\n\nfunction pickAsyncValidators(asyncValidator, validatorOrOpts) {\n  return (isOptionsObj(validatorOrOpts) ? validatorOrOpts.asyncValidators : asyncValidator) || null;\n}\n/**\n * Creates async validator function by combining provided async validators.\n */\n\n\nfunction coerceToAsyncValidator(asyncValidator) {\n  return Array.isArray(asyncValidator) ? composeAsyncValidators(asyncValidator) : asyncValidator || null;\n}\n\nfunction isOptionsObj(validatorOrOpts) {\n  return validatorOrOpts != null && !Array.isArray(validatorOrOpts) && typeof validatorOrOpts === 'object';\n}\n\nconst isFormControl = control => control instanceof FormControl;\n\nconst isFormGroup = control => control instanceof FormGroup;\n\nconst isFormArray = control => control instanceof FormArray;\n\nfunction getRawValue(control) {\n  return isFormControl(control) ? control.value : control.getRawValue();\n}\n\nfunction assertControlPresent(parent, key) {\n  const isGroup = isFormGroup(parent);\n  const controls = parent.controls;\n  const collection = isGroup ? Object.keys(controls) : controls;\n\n  if (!collection.length) {\n    throw new ɵRuntimeError(1000\n    /* NO_CONTROLS */\n    , NG_DEV_MODE ? noControlsError(isGroup) : '');\n  }\n\n  if (!controls[key]) {\n    throw new ɵRuntimeError(1001\n    /* MISSING_CONTROL */\n    , NG_DEV_MODE ? missingControlError(isGroup, key) : '');\n  }\n}\n\nfunction assertAllValuesPresent(control, value) {\n  const isGroup = isFormGroup(control);\n\n  control._forEachChild((_, key) => {\n    if (value[key] === undefined) {\n      throw new ɵRuntimeError(1002\n      /* MISSING_CONTROL_VALUE */\n      , NG_DEV_MODE ? missingControlValueError(isGroup, key) : '');\n    }\n  });\n}\n/**\n * This is the base class for `FormControl`, `FormGroup`, and `FormArray`.\n *\n * It provides some of the shared behavior that all controls and groups of controls have, like\n * running validators, calculating status, and resetting state. It also defines the properties\n * that are shared between all sub-classes, like `value`, `valid`, and `dirty`. It shouldn't be\n * instantiated directly.\n *\n * @see [Forms Guide](/guide/forms)\n * @see [Reactive Forms Guide](/guide/reactive-forms)\n * @see [Dynamic Forms Guide](/guide/dynamic-form)\n *\n * @publicApi\n */\n\n\nclass AbstractControl {\n  /**\n   * Initialize the AbstractControl instance.\n   *\n   * @param validators The function or array of functions that is used to determine the validity of\n   *     this control synchronously.\n   * @param asyncValidators The function or array of functions that is used to determine validity of\n   *     this control asynchronously.\n   */\n  constructor(validators, asyncValidators) {\n    /** @internal */\n    this._pendingDirty = false;\n    /**\n     * Indicates that a control has its own pending asynchronous validation in progress.\n     *\n     * @internal\n     */\n\n    this._hasOwnPendingAsyncValidator = false;\n    /** @internal */\n\n    this._pendingTouched = false;\n    /** @internal */\n\n    this._onCollectionChange = () => {};\n\n    this._parent = null;\n    /**\n     * A control is `pristine` if the user has not yet changed\n     * the value in the UI.\n     *\n     * @returns True if the user has not yet changed the value in the UI; compare `dirty`.\n     * Programmatic changes to a control's value do not mark it dirty.\n     */\n\n    this.pristine = true;\n    /**\n     * True if the control is marked as `touched`.\n     *\n     * A control is marked `touched` once the user has triggered\n     * a `blur` event on it.\n     */\n\n    this.touched = false;\n    /** @internal */\n\n    this._onDisabledChange = [];\n    this._rawValidators = validators;\n    this._rawAsyncValidators = asyncValidators;\n    this._composedValidatorFn = coerceToValidator(this._rawValidators);\n    this._composedAsyncValidatorFn = coerceToAsyncValidator(this._rawAsyncValidators);\n  }\n  /**\n   * Returns the function that is used to determine the validity of this control synchronously.\n   * If multiple validators have been added, this will be a single composed function.\n   * See `Validators.compose()` for additional information.\n   */\n\n\n  get validator() {\n    return this._composedValidatorFn;\n  }\n\n  set validator(validatorFn) {\n    this._rawValidators = this._composedValidatorFn = validatorFn;\n  }\n  /**\n   * Returns the function that is used to determine the validity of this control asynchronously.\n   * If multiple validators have been added, this will be a single composed function.\n   * See `Validators.compose()` for additional information.\n   */\n\n\n  get asyncValidator() {\n    return this._composedAsyncValidatorFn;\n  }\n\n  set asyncValidator(asyncValidatorFn) {\n    this._rawAsyncValidators = this._composedAsyncValidatorFn = asyncValidatorFn;\n  }\n  /**\n   * The parent control.\n   */\n\n\n  get parent() {\n    return this._parent;\n  }\n  /**\n   * A control is `valid` when its `status` is `VALID`.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @returns True if the control has passed all of its validation tests,\n   * false otherwise.\n   */\n\n\n  get valid() {\n    return this.status === VALID;\n  }\n  /**\n   * A control is `invalid` when its `status` is `INVALID`.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @returns True if this control has failed one or more of its validation checks,\n   * false otherwise.\n   */\n\n\n  get invalid() {\n    return this.status === INVALID;\n  }\n  /**\n   * A control is `pending` when its `status` is `PENDING`.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @returns True if this control is in the process of conducting a validation check,\n   * false otherwise.\n   */\n\n\n  get pending() {\n    return this.status == PENDING;\n  }\n  /**\n   * A control is `disabled` when its `status` is `DISABLED`.\n   *\n   * Disabled controls are exempt from validation checks and\n   * are not included in the aggregate value of their ancestor\n   * controls.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @returns True if the control is disabled, false otherwise.\n   */\n\n\n  get disabled() {\n    return this.status === DISABLED;\n  }\n  /**\n   * A control is `enabled` as long as its `status` is not `DISABLED`.\n   *\n   * @returns True if the control has any status other than 'DISABLED',\n   * false if the status is 'DISABLED'.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   */\n\n\n  get enabled() {\n    return this.status !== DISABLED;\n  }\n  /**\n   * A control is `dirty` if the user has changed the value\n   * in the UI.\n   *\n   * @returns True if the user has changed the value of this control in the UI; compare `pristine`.\n   * Programmatic changes to a control's value do not mark it dirty.\n   */\n\n\n  get dirty() {\n    return !this.pristine;\n  }\n  /**\n   * True if the control has not been marked as touched\n   *\n   * A control is `untouched` if the user has not yet triggered\n   * a `blur` event on it.\n   */\n\n\n  get untouched() {\n    return !this.touched;\n  }\n  /**\n   * Reports the update strategy of the `AbstractControl` (meaning\n   * the event on which the control updates itself).\n   * Possible values: `'change'` | `'blur'` | `'submit'`\n   * Default value: `'change'`\n   */\n\n\n  get updateOn() {\n    return this._updateOn ? this._updateOn : this.parent ? this.parent.updateOn : 'change';\n  }\n  /**\n   * Sets the synchronous validators that are active on this control.  Calling\n   * this overwrites any existing synchronous validators.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * If you want to add a new validator without affecting existing ones, consider\n   * using `addValidators()` method instead.\n   */\n\n\n  setValidators(validators) {\n    this._rawValidators = validators;\n    this._composedValidatorFn = coerceToValidator(validators);\n  }\n  /**\n   * Sets the asynchronous validators that are active on this control. Calling this\n   * overwrites any existing asynchronous validators.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * If you want to add a new validator without affecting existing ones, consider\n   * using `addAsyncValidators()` method instead.\n   */\n\n\n  setAsyncValidators(validators) {\n    this._rawAsyncValidators = validators;\n    this._composedAsyncValidatorFn = coerceToAsyncValidator(validators);\n  }\n  /**\n   * Add a synchronous validator or validators to this control, without affecting other validators.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * Adding a validator that already exists will have no effect. If duplicate validator functions\n   * are present in the `validators` array, only the first instance would be added to a form\n   * control.\n   *\n   * @param validators The new validator function or functions to add to this control.\n   */\n\n\n  addValidators(validators) {\n    this.setValidators(addValidators(validators, this._rawValidators));\n  }\n  /**\n   * Add an asynchronous validator or validators to this control, without affecting other\n   * validators.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * Adding a validator that already exists will have no effect.\n   *\n   * @param validators The new asynchronous validator function or functions to add to this control.\n   */\n\n\n  addAsyncValidators(validators) {\n    this.setAsyncValidators(addValidators(validators, this._rawAsyncValidators));\n  }\n  /**\n   * Remove a synchronous validator from this control, without affecting other validators.\n   * Validators are compared by function reference; you must pass a reference to the exact same\n   * validator function as the one that was originally set. If a provided validator is not found,\n   * it is ignored.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * @param validators The validator or validators to remove.\n   */\n\n\n  removeValidators(validators) {\n    this.setValidators(removeValidators(validators, this._rawValidators));\n  }\n  /**\n   * Remove an asynchronous validator from this control, without affecting other validators.\n   * Validators are compared by function reference; you must pass a reference to the exact same\n   * validator function as the one that was originally set. If a provided validator is not found, it\n   * is ignored.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * @param validators The asynchronous validator or validators to remove.\n   */\n\n\n  removeAsyncValidators(validators) {\n    this.setAsyncValidators(removeValidators(validators, this._rawAsyncValidators));\n  }\n  /**\n   * Check whether a synchronous validator function is present on this control. The provided\n   * validator must be a reference to the exact same function that was provided.\n   *\n   * @param validator The validator to check for presence. Compared by function reference.\n   * @returns Whether the provided validator was found on this control.\n   */\n\n\n  hasValidator(validator) {\n    return hasValidator(this._rawValidators, validator);\n  }\n  /**\n   * Check whether an asynchronous validator function is present on this control. The provided\n   * validator must be a reference to the exact same function that was provided.\n   *\n   * @param validator The asynchronous validator to check for presence. Compared by function\n   *     reference.\n   * @returns Whether the provided asynchronous validator was found on this control.\n   */\n\n\n  hasAsyncValidator(validator) {\n    return hasValidator(this._rawAsyncValidators, validator);\n  }\n  /**\n   * Empties out the synchronous validator list.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   */\n\n\n  clearValidators() {\n    this.validator = null;\n  }\n  /**\n   * Empties out the async validator list.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   */\n\n\n  clearAsyncValidators() {\n    this.asyncValidator = null;\n  }\n  /**\n   * Marks the control as `touched`. A control is touched by focus and\n   * blur events that do not change the value.\n   *\n   * @see `markAsUntouched()`\n   * @see `markAsDirty()`\n   * @see `markAsPristine()`\n   *\n   * @param opts Configuration options that determine how the control propagates changes\n   * and emits events after marking is applied.\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   */\n\n\n  markAsTouched(opts = {}) {\n    this.touched = true;\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsTouched(opts);\n    }\n  }\n  /**\n   * Marks the control and all its descendant controls as `touched`.\n   * @see `markAsTouched()`\n   */\n\n\n  markAllAsTouched() {\n    this.markAsTouched({\n      onlySelf: true\n    });\n\n    this._forEachChild(control => control.markAllAsTouched());\n  }\n  /**\n   * Marks the control as `untouched`.\n   *\n   * If the control has any children, also marks all children as `untouched`\n   * and recalculates the `touched` status of all parent controls.\n   *\n   * @see `markAsTouched()`\n   * @see `markAsDirty()`\n   * @see `markAsPristine()`\n   *\n   * @param opts Configuration options that determine how the control propagates changes\n   * and emits events after the marking is applied.\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   */\n\n\n  markAsUntouched(opts = {}) {\n    this.touched = false;\n    this._pendingTouched = false;\n\n    this._forEachChild(control => {\n      control.markAsUntouched({\n        onlySelf: true\n      });\n    });\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updateTouched(opts);\n    }\n  }\n  /**\n   * Marks the control as `dirty`. A control becomes dirty when\n   * the control's value is changed through the UI; compare `markAsTouched`.\n   *\n   * @see `markAsTouched()`\n   * @see `markAsUntouched()`\n   * @see `markAsPristine()`\n   *\n   * @param opts Configuration options that determine how the control propagates changes\n   * and emits events after marking is applied.\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   */\n\n\n  markAsDirty(opts = {}) {\n    this.pristine = false;\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsDirty(opts);\n    }\n  }\n  /**\n   * Marks the control as `pristine`.\n   *\n   * If the control has any children, marks all children as `pristine`,\n   * and recalculates the `pristine` status of all parent\n   * controls.\n   *\n   * @see `markAsTouched()`\n   * @see `markAsUntouched()`\n   * @see `markAsDirty()`\n   *\n   * @param opts Configuration options that determine how the control emits events after\n   * marking is applied.\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   */\n\n\n  markAsPristine(opts = {}) {\n    this.pristine = true;\n    this._pendingDirty = false;\n\n    this._forEachChild(control => {\n      control.markAsPristine({\n        onlySelf: true\n      });\n    });\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updatePristine(opts);\n    }\n  }\n  /**\n   * Marks the control as `pending`.\n   *\n   * A control is pending while the control performs async validation.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @param opts Configuration options that determine how the control propagates changes and\n   * emits events after marking is applied.\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   * * `emitEvent`: When true or not supplied (the default), the `statusChanges`\n   * observable emits an event with the latest status the control is marked pending.\n   * When false, no events are emitted.\n   *\n   */\n\n\n  markAsPending(opts = {}) {\n    this.status = PENDING;\n\n    if (opts.emitEvent !== false) {\n      this.statusChanges.emit(this.status);\n    }\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsPending(opts);\n    }\n  }\n  /**\n   * Disables the control. This means the control is exempt from validation checks and\n   * excluded from the aggregate value of any parent. Its status is `DISABLED`.\n   *\n   * If the control has children, all children are also disabled.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @param opts Configuration options that determine how the control propagates\n   * changes and emits events after the control is disabled.\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is disabled.\n   * When false, no events are emitted.\n   */\n\n\n  disable(opts = {}) {\n    // If parent has been marked artificially dirty we don't want to re-calculate the\n    // parent's dirtiness based on the children.\n    const skipPristineCheck = this._parentMarkedDirty(opts.onlySelf);\n\n    this.status = DISABLED;\n    this.errors = null;\n\n    this._forEachChild(control => {\n      control.disable(Object.assign(Object.assign({}, opts), {\n        onlySelf: true\n      }));\n    });\n\n    this._updateValue();\n\n    if (opts.emitEvent !== false) {\n      this.valueChanges.emit(this.value);\n      this.statusChanges.emit(this.status);\n    }\n\n    this._updateAncestors(Object.assign(Object.assign({}, opts), {\n      skipPristineCheck\n    }));\n\n    this._onDisabledChange.forEach(changeFn => changeFn(true));\n  }\n  /**\n   * Enables the control. This means the control is included in validation checks and\n   * the aggregate value of its parent. Its status recalculates based on its value and\n   * its validators.\n   *\n   * By default, if the control has children, all children are enabled.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @param opts Configure options that control how the control propagates changes and\n   * emits events when marked as untouched\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is enabled.\n   * When false, no events are emitted.\n   */\n\n\n  enable(opts = {}) {\n    // If parent has been marked artificially dirty we don't want to re-calculate the\n    // parent's dirtiness based on the children.\n    const skipPristineCheck = this._parentMarkedDirty(opts.onlySelf);\n\n    this.status = VALID;\n\n    this._forEachChild(control => {\n      control.enable(Object.assign(Object.assign({}, opts), {\n        onlySelf: true\n      }));\n    });\n\n    this.updateValueAndValidity({\n      onlySelf: true,\n      emitEvent: opts.emitEvent\n    });\n\n    this._updateAncestors(Object.assign(Object.assign({}, opts), {\n      skipPristineCheck\n    }));\n\n    this._onDisabledChange.forEach(changeFn => changeFn(false));\n  }\n\n  _updateAncestors(opts) {\n    if (this._parent && !opts.onlySelf) {\n      this._parent.updateValueAndValidity(opts);\n\n      if (!opts.skipPristineCheck) {\n        this._parent._updatePristine();\n      }\n\n      this._parent._updateTouched();\n    }\n  }\n  /**\n   * @param parent Sets the parent of the control\n   */\n\n\n  setParent(parent) {\n    this._parent = parent;\n  }\n  /**\n   * Recalculates the value and validation status of the control.\n   *\n   * By default, it also updates the value and validity of its ancestors.\n   *\n   * @param opts Configuration options determine how the control propagates changes and emits events\n   * after updates and validity checks are applied.\n   * * `onlySelf`: When true, only update this control. When false or not supplied,\n   * update all direct ancestors. Default is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is updated.\n   * When false, no events are emitted.\n   */\n\n\n  updateValueAndValidity(opts = {}) {\n    this._setInitialStatus();\n\n    this._updateValue();\n\n    if (this.enabled) {\n      this._cancelExistingSubscription();\n\n      this.errors = this._runValidator();\n      this.status = this._calculateStatus();\n\n      if (this.status === VALID || this.status === PENDING) {\n        this._runAsyncValidator(opts.emitEvent);\n      }\n    }\n\n    if (opts.emitEvent !== false) {\n      this.valueChanges.emit(this.value);\n      this.statusChanges.emit(this.status);\n    }\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.updateValueAndValidity(opts);\n    }\n  }\n  /** @internal */\n\n\n  _updateTreeValidity(opts = {\n    emitEvent: true\n  }) {\n    this._forEachChild(ctrl => ctrl._updateTreeValidity(opts));\n\n    this.updateValueAndValidity({\n      onlySelf: true,\n      emitEvent: opts.emitEvent\n    });\n  }\n\n  _setInitialStatus() {\n    this.status = this._allControlsDisabled() ? DISABLED : VALID;\n  }\n\n  _runValidator() {\n    return this.validator ? this.validator(this) : null;\n  }\n\n  _runAsyncValidator(emitEvent) {\n    if (this.asyncValidator) {\n      this.status = PENDING;\n      this._hasOwnPendingAsyncValidator = true;\n      const obs = toObservable(this.asyncValidator(this));\n      this._asyncValidationSubscription = obs.subscribe(errors => {\n        this._hasOwnPendingAsyncValidator = false; // This will trigger the recalculation of the validation status, which depends on\n        // the state of the asynchronous validation (whether it is in progress or not). So, it is\n        // necessary that we have updated the `_hasOwnPendingAsyncValidator` boolean flag first.\n\n        this.setErrors(errors, {\n          emitEvent\n        });\n      });\n    }\n  }\n\n  _cancelExistingSubscription() {\n    if (this._asyncValidationSubscription) {\n      this._asyncValidationSubscription.unsubscribe();\n\n      this._hasOwnPendingAsyncValidator = false;\n    }\n  }\n  /**\n   * Sets errors on a form control when running validations manually, rather than automatically.\n   *\n   * Calling `setErrors` also updates the validity of the parent control.\n   *\n   * @usageNotes\n   *\n   * ### Manually set the errors for a control\n   *\n   * ```\n   * const login = new FormControl('someLogin');\n   * login.setErrors({\n   *   notUnique: true\n   * });\n   *\n   * expect(login.valid).toEqual(false);\n   * expect(login.errors).toEqual({ notUnique: true });\n   *\n   * login.setValue('someOtherLogin');\n   *\n   * expect(login.valid).toEqual(true);\n   * ```\n   */\n\n\n  setErrors(errors, opts = {}) {\n    this.errors = errors;\n\n    this._updateControlsErrors(opts.emitEvent !== false);\n  }\n  /**\n   * Retrieves a child control given the control's name or path.\n   *\n   * @param path A dot-delimited string or array of string/number values that define the path to the\n   * control.\n   *\n   * @usageNotes\n   * ### Retrieve a nested control\n   *\n   * For example, to get a `name` control nested within a `person` sub-group:\n   *\n   * * `this.form.get('person.name');`\n   *\n   * -OR-\n   *\n   * * `this.form.get(['person', 'name']);`\n   *\n   * ### Retrieve a control in a FormArray\n   *\n   * When accessing an element inside a FormArray, you can use an element index.\n   * For example, to get a `price` control from the first element in an `items` array you can use:\n   *\n   * * `this.form.get('items.0.price');`\n   *\n   * -OR-\n   *\n   * * `this.form.get(['items', 0, 'price']);`\n   */\n\n\n  get(path) {\n    return _find(this, path, '.');\n  }\n  /**\n   * @description\n   * Reports error data for the control with the given path.\n   *\n   * @param errorCode The code of the error to check\n   * @param path A list of control names that designates how to move from the current control\n   * to the control that should be queried for errors.\n   *\n   * @usageNotes\n   * For example, for the following `FormGroup`:\n   *\n   * ```\n   * form = new FormGroup({\n   *   address: new FormGroup({ street: new FormControl() })\n   * });\n   * ```\n   *\n   * The path to the 'street' control from the root form would be 'address' -> 'street'.\n   *\n   * It can be provided to this method in one of two formats:\n   *\n   * 1. An array of string control names, e.g. `['address', 'street']`\n   * 1. A period-delimited list of control names in one string, e.g. `'address.street'`\n   *\n   * @returns error data for that particular error. If the control or error is not present,\n   * null is returned.\n   */\n\n\n  getError(errorCode, path) {\n    const control = path ? this.get(path) : this;\n    return control && control.errors ? control.errors[errorCode] : null;\n  }\n  /**\n   * @description\n   * Reports whether the control with the given path has the error specified.\n   *\n   * @param errorCode The code of the error to check\n   * @param path A list of control names that designates how to move from the current control\n   * to the control that should be queried for errors.\n   *\n   * @usageNotes\n   * For example, for the following `FormGroup`:\n   *\n   * ```\n   * form = new FormGroup({\n   *   address: new FormGroup({ street: new FormControl() })\n   * });\n   * ```\n   *\n   * The path to the 'street' control from the root form would be 'address' -> 'street'.\n   *\n   * It can be provided to this method in one of two formats:\n   *\n   * 1. An array of string control names, e.g. `['address', 'street']`\n   * 1. A period-delimited list of control names in one string, e.g. `'address.street'`\n   *\n   * If no path is given, this method checks for the error on the current control.\n   *\n   * @returns whether the given error is present in the control at the given path.\n   *\n   * If the control is not present, false is returned.\n   */\n\n\n  hasError(errorCode, path) {\n    return !!this.getError(errorCode, path);\n  }\n  /**\n   * Retrieves the top-level ancestor of this control.\n   */\n\n\n  get root() {\n    let x = this;\n\n    while (x._parent) {\n      x = x._parent;\n    }\n\n    return x;\n  }\n  /** @internal */\n\n\n  _updateControlsErrors(emitEvent) {\n    this.status = this._calculateStatus();\n\n    if (emitEvent) {\n      this.statusChanges.emit(this.status);\n    }\n\n    if (this._parent) {\n      this._parent._updateControlsErrors(emitEvent);\n    }\n  }\n  /** @internal */\n\n\n  _initObservables() {\n    this.valueChanges = new EventEmitter();\n    this.statusChanges = new EventEmitter();\n  }\n\n  _calculateStatus() {\n    if (this._allControlsDisabled()) return DISABLED;\n    if (this.errors) return INVALID;\n    if (this._hasOwnPendingAsyncValidator || this._anyControlsHaveStatus(PENDING)) return PENDING;\n    if (this._anyControlsHaveStatus(INVALID)) return INVALID;\n    return VALID;\n  }\n  /** @internal */\n\n\n  _anyControlsHaveStatus(status) {\n    return this._anyControls(control => control.status === status);\n  }\n  /** @internal */\n\n\n  _anyControlsDirty() {\n    return this._anyControls(control => control.dirty);\n  }\n  /** @internal */\n\n\n  _anyControlsTouched() {\n    return this._anyControls(control => control.touched);\n  }\n  /** @internal */\n\n\n  _updatePristine(opts = {}) {\n    this.pristine = !this._anyControlsDirty();\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updatePristine(opts);\n    }\n  }\n  /** @internal */\n\n\n  _updateTouched(opts = {}) {\n    this.touched = this._anyControlsTouched();\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updateTouched(opts);\n    }\n  }\n  /** @internal */\n\n\n  _isBoxedValue(formState) {\n    return typeof formState === 'object' && formState !== null && Object.keys(formState).length === 2 && 'value' in formState && 'disabled' in formState;\n  }\n  /** @internal */\n\n\n  _registerOnCollectionChange(fn) {\n    this._onCollectionChange = fn;\n  }\n  /** @internal */\n\n\n  _setUpdateStrategy(opts) {\n    if (isOptionsObj(opts) && opts.updateOn != null) {\n      this._updateOn = opts.updateOn;\n    }\n  }\n  /**\n   * Check to see if parent has been marked artificially dirty.\n   *\n   * @internal\n   */\n\n\n  _parentMarkedDirty(onlySelf) {\n    const parentDirty = this._parent && this._parent.dirty;\n    return !onlySelf && !!parentDirty && !this._parent._anyControlsDirty();\n  }\n\n}\n/**\n * Tracks the value and validation status of an individual form control.\n *\n * This is one of the three fundamental building blocks of Angular forms, along with\n * `FormGroup` and `FormArray`. It extends the `AbstractControl` class that\n * implements most of the base functionality for accessing the value, validation status,\n * user interactions and events. See [usage examples below](#usage-notes).\n *\n * @see `AbstractControl`\n * @see [Reactive Forms Guide](guide/reactive-forms)\n * @see [Usage Notes](#usage-notes)\n *\n * @usageNotes\n *\n * ### Initializing Form Controls\n *\n * Instantiate a `FormControl`, with an initial value.\n *\n * ```ts\n * const control = new FormControl('some value');\n * console.log(control.value);     // 'some value'\n *```\n *\n * The following example initializes the control with a form state object. The `value`\n * and `disabled` keys are required in this case.\n *\n * ```ts\n * const control = new FormControl({ value: 'n/a', disabled: true });\n * console.log(control.value);     // 'n/a'\n * console.log(control.status);    // 'DISABLED'\n * ```\n *\n * The following example initializes the control with a synchronous validator.\n *\n * ```ts\n * const control = new FormControl('', Validators.required);\n * console.log(control.value);      // ''\n * console.log(control.status);     // 'INVALID'\n * ```\n *\n * The following example initializes the control using an options object.\n *\n * ```ts\n * const control = new FormControl('', {\n *    validators: Validators.required,\n *    asyncValidators: myAsyncValidator\n * });\n * ```\n *\n * ### Configure the control to update on a blur event\n *\n * Set the `updateOn` option to `'blur'` to update on the blur `event`.\n *\n * ```ts\n * const control = new FormControl('', { updateOn: 'blur' });\n * ```\n *\n * ### Configure the control to update on a submit event\n *\n * Set the `updateOn` option to `'submit'` to update on a submit `event`.\n *\n * ```ts\n * const control = new FormControl('', { updateOn: 'submit' });\n * ```\n *\n * ### Reset the control back to an initial value\n *\n * You reset to a specific form state by passing through a standalone\n * value or a form state object that contains both a value and a disabled state\n * (these are the only two properties that cannot be calculated).\n *\n * ```ts\n * const control = new FormControl('Nancy');\n *\n * console.log(control.value); // 'Nancy'\n *\n * control.reset('Drew');\n *\n * console.log(control.value); // 'Drew'\n * ```\n *\n * ### Reset the control back to an initial value and disabled\n *\n * ```\n * const control = new FormControl('Nancy');\n *\n * console.log(control.value); // 'Nancy'\n * console.log(control.status); // 'VALID'\n *\n * control.reset({ value: 'Drew', disabled: true });\n *\n * console.log(control.value); // 'Drew'\n * console.log(control.status); // 'DISABLED'\n * ```\n *\n * @publicApi\n */\n\n\nclass FormControl extends AbstractControl {\n  /**\n   * Creates a new `FormControl` instance.\n   *\n   * @param formState Initializes the control with an initial value,\n   * or an object that defines the initial value and disabled state.\n   *\n   * @param validatorOrOpts A synchronous validator function, or an array of\n   * such functions, or an `AbstractControlOptions` object that contains validation functions\n   * and a validation trigger.\n   *\n   * @param asyncValidator A single async validator or array of async validator functions\n   *\n   */\n  constructor(formState = null, validatorOrOpts, asyncValidator) {\n    super(pickValidators(validatorOrOpts), pickAsyncValidators(asyncValidator, validatorOrOpts));\n    /** @internal */\n\n    this._onChange = [];\n    /** @internal */\n\n    this._pendingChange = false;\n\n    this._applyFormState(formState);\n\n    this._setUpdateStrategy(validatorOrOpts);\n\n    this._initObservables();\n\n    this.updateValueAndValidity({\n      onlySelf: true,\n      // If `asyncValidator` is present, it will trigger control status change from `PENDING` to\n      // `VALID` or `INVALID`.\n      // The status should be broadcasted via the `statusChanges` observable, so we set `emitEvent`\n      // to `true` to allow that during the control creation process.\n      emitEvent: !!this.asyncValidator\n    });\n  }\n  /**\n   * Sets a new value for the form control.\n   *\n   * @param value The new value for the control.\n   * @param options Configuration options that determine how the control propagates changes\n   * and emits events when the value changes.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default is\n   * false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control value is updated.\n   * When false, no events are emitted.\n   * * `emitModelToViewChange`: When true or not supplied  (the default), each change triggers an\n   * `onChange` event to\n   * update the view.\n   * * `emitViewToModelChange`: When true or not supplied (the default), each change triggers an\n   * `ngModelChange`\n   * event to update the model.\n   *\n   */\n\n\n  setValue(value, options = {}) {\n    this.value = this._pendingValue = value;\n\n    if (this._onChange.length && options.emitModelToViewChange !== false) {\n      this._onChange.forEach(changeFn => changeFn(this.value, options.emitViewToModelChange !== false));\n    }\n\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * Patches the value of a control.\n   *\n   * This function is functionally the same as {@link FormControl#setValue setValue} at this level.\n   * It exists for symmetry with {@link FormGroup#patchValue patchValue} on `FormGroups` and\n   * `FormArrays`, where it does behave differently.\n   *\n   * @see `setValue` for options\n   */\n\n\n  patchValue(value, options = {}) {\n    this.setValue(value, options);\n  }\n  /**\n   * Resets the form control, marking it `pristine` and `untouched`, and setting\n   * the value to null.\n   *\n   * @param formState Resets the control with an initial value,\n   * or an object that defines the initial value and disabled state.\n   *\n   * @param options Configuration options that determine how the control propagates changes\n   * and emits events after the value changes.\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default is\n   * false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is reset.\n   * When false, no events are emitted.\n   *\n   */\n\n\n  reset(formState = null, options = {}) {\n    this._applyFormState(formState);\n\n    this.markAsPristine(options);\n    this.markAsUntouched(options);\n    this.setValue(this.value, options);\n    this._pendingChange = false;\n  }\n  /**\n   * @internal\n   */\n\n\n  _updateValue() {}\n  /**\n   * @internal\n   */\n\n\n  _anyControls(condition) {\n    return false;\n  }\n  /**\n   * @internal\n   */\n\n\n  _allControlsDisabled() {\n    return this.disabled;\n  }\n  /**\n   * Register a listener for change events.\n   *\n   * @param fn The method that is called when the value changes\n   */\n\n\n  registerOnChange(fn) {\n    this._onChange.push(fn);\n  }\n  /**\n   * Internal function to unregister a change events listener.\n   * @internal\n   */\n\n\n  _unregisterOnChange(fn) {\n    removeListItem(this._onChange, fn);\n  }\n  /**\n   * Register a listener for disabled events.\n   *\n   * @param fn The method that is called when the disabled status changes.\n   */\n\n\n  registerOnDisabledChange(fn) {\n    this._onDisabledChange.push(fn);\n  }\n  /**\n   * Internal function to unregister a disabled event listener.\n   * @internal\n   */\n\n\n  _unregisterOnDisabledChange(fn) {\n    removeListItem(this._onDisabledChange, fn);\n  }\n  /**\n   * @internal\n   */\n\n\n  _forEachChild(cb) {}\n  /** @internal */\n\n\n  _syncPendingControls() {\n    if (this.updateOn === 'submit') {\n      if (this._pendingDirty) this.markAsDirty();\n      if (this._pendingTouched) this.markAsTouched();\n\n      if (this._pendingChange) {\n        this.setValue(this._pendingValue, {\n          onlySelf: true,\n          emitModelToViewChange: false\n        });\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  _applyFormState(formState) {\n    if (this._isBoxedValue(formState)) {\n      this.value = this._pendingValue = formState.value;\n      formState.disabled ? this.disable({\n        onlySelf: true,\n        emitEvent: false\n      }) : this.enable({\n        onlySelf: true,\n        emitEvent: false\n      });\n    } else {\n      this.value = this._pendingValue = formState;\n    }\n  }\n\n}\n/**\n * Tracks the value and validity state of a group of `FormControl` instances.\n *\n * A `FormGroup` aggregates the values of each child `FormControl` into one object,\n * with each control name as the key.  It calculates its status by reducing the status values\n * of its children. For example, if one of the controls in a group is invalid, the entire\n * group becomes invalid.\n *\n * `FormGroup` is one of the three fundamental building blocks used to define forms in Angular,\n * along with `FormControl` and `FormArray`.\n *\n * When instantiating a `FormGroup`, pass in a collection of child controls as the first\n * argument. The key for each child registers the name for the control.\n *\n * @usageNotes\n *\n * ### Create a form group with 2 controls\n *\n * ```\n * const form = new FormGroup({\n *   first: new FormControl('Nancy', Validators.minLength(2)),\n *   last: new FormControl('Drew'),\n * });\n *\n * console.log(form.value);   // {first: 'Nancy', last; 'Drew'}\n * console.log(form.status);  // 'VALID'\n * ```\n *\n * ### Create a form group with a group-level validator\n *\n * You include group-level validators as the second arg, or group-level async\n * validators as the third arg. These come in handy when you want to perform validation\n * that considers the value of more than one child control.\n *\n * ```\n * const form = new FormGroup({\n *   password: new FormControl('', Validators.minLength(2)),\n *   passwordConfirm: new FormControl('', Validators.minLength(2)),\n * }, passwordMatchValidator);\n *\n *\n * function passwordMatchValidator(g: FormGroup) {\n *    return g.get('password').value === g.get('passwordConfirm').value\n *       ? null : {'mismatch': true};\n * }\n * ```\n *\n * Like `FormControl` instances, you choose to pass in\n * validators and async validators as part of an options object.\n *\n * ```\n * const form = new FormGroup({\n *   password: new FormControl('')\n *   passwordConfirm: new FormControl('')\n * }, { validators: passwordMatchValidator, asyncValidators: otherValidator });\n * ```\n *\n * ### Set the updateOn property for all controls in a form group\n *\n * The options object is used to set a default value for each child\n * control's `updateOn` property. If you set `updateOn` to `'blur'` at the\n * group level, all child controls default to 'blur', unless the child\n * has explicitly specified a different `updateOn` value.\n *\n * ```ts\n * const c = new FormGroup({\n *   one: new FormControl()\n * }, { updateOn: 'blur' });\n * ```\n *\n * @publicApi\n */\n\n\nclass FormGroup extends AbstractControl {\n  /**\n   * Creates a new `FormGroup` instance.\n   *\n   * @param controls A collection of child controls. The key for each child is the name\n   * under which it is registered.\n   *\n   * @param validatorOrOpts A synchronous validator function, or an array of\n   * such functions, or an `AbstractControlOptions` object that contains validation functions\n   * and a validation trigger.\n   *\n   * @param asyncValidator A single async validator or array of async validator functions\n   *\n   */\n  constructor(controls, validatorOrOpts, asyncValidator) {\n    super(pickValidators(validatorOrOpts), pickAsyncValidators(asyncValidator, validatorOrOpts));\n    this.controls = controls;\n\n    this._initObservables();\n\n    this._setUpdateStrategy(validatorOrOpts);\n\n    this._setUpControls();\n\n    this.updateValueAndValidity({\n      onlySelf: true,\n      // If `asyncValidator` is present, it will trigger control status change from `PENDING` to\n      // `VALID` or `INVALID`. The status should be broadcasted via the `statusChanges` observable,\n      // so we set `emitEvent` to `true` to allow that during the control creation process.\n      emitEvent: !!this.asyncValidator\n    });\n  }\n  /**\n   * Registers a control with the group's list of controls.\n   *\n   * This method does not update the value or validity of the control.\n   * Use {@link FormGroup#addControl addControl} instead.\n   *\n   * @param name The control name to register in the collection\n   * @param control Provides the control for the given name\n   */\n\n\n  registerControl(name, control) {\n    if (this.controls[name]) return this.controls[name];\n    this.controls[name] = control;\n    control.setParent(this);\n\n    control._registerOnCollectionChange(this._onCollectionChange);\n\n    return control;\n  }\n  /**\n   * Add a control to this group.\n   *\n   * If a control with a given name already exists, it would *not* be replaced with a new one.\n   * If you want to replace an existing control, use the {@link FormGroup#setControl setControl}\n   * method instead. This method also updates the value and validity of the control.\n   *\n   * @param name The control name to add to the collection\n   * @param control Provides the control for the given name\n   * @param options Specifies whether this FormGroup instance should emit events after a new\n   *     control is added.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * added. When false, no events are emitted.\n   */\n\n\n  addControl(name, control, options = {}) {\n    this.registerControl(name, control);\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n\n    this._onCollectionChange();\n  }\n  /**\n   * Remove a control from this group.\n   *\n   * This method also updates the value and validity of the control.\n   *\n   * @param name The control name to remove from the collection\n   * @param options Specifies whether this FormGroup instance should emit events after a\n   *     control is removed.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * removed. When false, no events are emitted.\n   */\n\n\n  removeControl(name, options = {}) {\n    if (this.controls[name]) this.controls[name]._registerOnCollectionChange(() => {});\n    delete this.controls[name];\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n\n    this._onCollectionChange();\n  }\n  /**\n   * Replace an existing control.\n   *\n   * If a control with a given name does not exist in this `FormGroup`, it will be added.\n   *\n   * @param name The control name to replace in the collection\n   * @param control Provides the control for the given name\n   * @param options Specifies whether this FormGroup instance should emit events after an\n   *     existing control is replaced.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * replaced with a new one. When false, no events are emitted.\n   */\n\n\n  setControl(name, control, options = {}) {\n    if (this.controls[name]) this.controls[name]._registerOnCollectionChange(() => {});\n    delete this.controls[name];\n    if (control) this.registerControl(name, control);\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n\n    this._onCollectionChange();\n  }\n  /**\n   * Check whether there is an enabled control with the given name in the group.\n   *\n   * Reports false for disabled controls. If you'd like to check for existence in the group\n   * only, use {@link AbstractControl#get get} instead.\n   *\n   * @param controlName The control name to check for existence in the collection\n   *\n   * @returns false for disabled controls, true otherwise.\n   */\n\n\n  contains(controlName) {\n    return this.controls.hasOwnProperty(controlName) && this.controls[controlName].enabled;\n  }\n  /**\n   * Sets the value of the `FormGroup`. It accepts an object that matches\n   * the structure of the group, with control names as keys.\n   *\n   * @usageNotes\n   * ### Set the complete value for the form group\n   *\n   * ```\n   * const form = new FormGroup({\n   *   first: new FormControl(),\n   *   last: new FormControl()\n   * });\n   *\n   * console.log(form.value);   // {first: null, last: null}\n   *\n   * form.setValue({first: 'Nancy', last: 'Drew'});\n   * console.log(form.value);   // {first: 'Nancy', last: 'Drew'}\n   * ```\n   *\n   * @throws When strict checks fail, such as setting the value of a control\n   * that doesn't exist or if you exclude a value of a control that does exist.\n   *\n   * @param value The new value for the control that matches the structure of the group.\n   * @param options Configuration options that determine how the control propagates changes\n   * and emits events after the value changes.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default is\n   * false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control value is updated.\n   * When false, no events are emitted.\n   */\n\n\n  setValue(value, options = {}) {\n    assertAllValuesPresent(this, value);\n    Object.keys(value).forEach(name => {\n      assertControlPresent(this, name);\n      this.controls[name].setValue(value[name], {\n        onlySelf: true,\n        emitEvent: options.emitEvent\n      });\n    });\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * Patches the value of the `FormGroup`. It accepts an object with control\n   * names as keys, and does its best to match the values to the correct controls\n   * in the group.\n   *\n   * It accepts both super-sets and sub-sets of the group without throwing an error.\n   *\n   * @usageNotes\n   * ### Patch the value for a form group\n   *\n   * ```\n   * const form = new FormGroup({\n   *    first: new FormControl(),\n   *    last: new FormControl()\n   * });\n   * console.log(form.value);   // {first: null, last: null}\n   *\n   * form.patchValue({first: 'Nancy'});\n   * console.log(form.value);   // {first: 'Nancy', last: null}\n   * ```\n   *\n   * @param value The object that matches the structure of the group.\n   * @param options Configuration options that determine how the control propagates changes and\n   * emits events after the value is patched.\n   * * `onlySelf`: When true, each change only affects this control and not its parent. Default is\n   * true.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control value\n   * is updated. When false, no events are emitted. The configuration options are passed to\n   * the {@link AbstractControl#updateValueAndValidity updateValueAndValidity} method.\n   */\n\n\n  patchValue(value, options = {}) {\n    // Even though the `value` argument type doesn't allow `null` and `undefined` values, the\n    // `patchValue` can be called recursively and inner data structures might have these values, so\n    // we just ignore such cases when a field containing FormGroup instance receives `null` or\n    // `undefined` as a value.\n    if (value == null\n    /* both `null` and `undefined` */\n    ) return;\n    Object.keys(value).forEach(name => {\n      if (this.controls[name]) {\n        this.controls[name].patchValue(value[name], {\n          onlySelf: true,\n          emitEvent: options.emitEvent\n        });\n      }\n    });\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * Resets the `FormGroup`, marks all descendants `pristine` and `untouched` and sets\n   * the value of all descendants to null.\n   *\n   * You reset to a specific form state by passing in a map of states\n   * that matches the structure of your form, with control names as keys. The state\n   * is a standalone value or a form state object with both a value and a disabled\n   * status.\n   *\n   * @param value Resets the control with an initial value,\n   * or an object that defines the initial value and disabled state.\n   *\n   * @param options Configuration options that determine how the control propagates changes\n   * and emits events when the group is reset.\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default is\n   * false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is reset.\n   * When false, no events are emitted.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   *\n   * @usageNotes\n   *\n   * ### Reset the form group values\n   *\n   * ```ts\n   * const form = new FormGroup({\n   *   first: new FormControl('first name'),\n   *   last: new FormControl('last name')\n   * });\n   *\n   * console.log(form.value);  // {first: 'first name', last: 'last name'}\n   *\n   * form.reset({ first: 'name', last: 'last name' });\n   *\n   * console.log(form.value);  // {first: 'name', last: 'last name'}\n   * ```\n   *\n   * ### Reset the form group values and disabled status\n   *\n   * ```\n   * const form = new FormGroup({\n   *   first: new FormControl('first name'),\n   *   last: new FormControl('last name')\n   * });\n   *\n   * form.reset({\n   *   first: {value: 'name', disabled: true},\n   *   last: 'last'\n   * });\n   *\n   * console.log(form.value);  // {last: 'last'}\n   * console.log(form.get('first').status);  // 'DISABLED'\n   * ```\n   */\n\n\n  reset(value = {}, options = {}) {\n    this._forEachChild((control, name) => {\n      control.reset(value[name], {\n        onlySelf: true,\n        emitEvent: options.emitEvent\n      });\n    });\n\n    this._updatePristine(options);\n\n    this._updateTouched(options);\n\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * The aggregate value of the `FormGroup`, including any disabled controls.\n   *\n   * Retrieves all values regardless of disabled status.\n   * The `value` property is the best way to get the value of the group, because\n   * it excludes disabled controls in the `FormGroup`.\n   */\n\n\n  getRawValue() {\n    return this._reduceChildren({}, (acc, control, name) => {\n      acc[name] = getRawValue(control);\n      return acc;\n    });\n  }\n  /** @internal */\n\n\n  _syncPendingControls() {\n    let subtreeUpdated = this._reduceChildren(false, (updated, child) => {\n      return child._syncPendingControls() ? true : updated;\n    });\n\n    if (subtreeUpdated) this.updateValueAndValidity({\n      onlySelf: true\n    });\n    return subtreeUpdated;\n  }\n  /** @internal */\n\n\n  _forEachChild(cb) {\n    Object.keys(this.controls).forEach(key => {\n      // The list of controls can change (for ex. controls might be removed) while the loop\n      // is running (as a result of invoking Forms API in `valueChanges` subscription), so we\n      // have to null check before invoking the callback.\n      const control = this.controls[key];\n      control && cb(control, key);\n    });\n  }\n  /** @internal */\n\n\n  _setUpControls() {\n    this._forEachChild(control => {\n      control.setParent(this);\n\n      control._registerOnCollectionChange(this._onCollectionChange);\n    });\n  }\n  /** @internal */\n\n\n  _updateValue() {\n    this.value = this._reduceValue();\n  }\n  /** @internal */\n\n\n  _anyControls(condition) {\n    for (const controlName of Object.keys(this.controls)) {\n      const control = this.controls[controlName];\n\n      if (this.contains(controlName) && condition(control)) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n  /** @internal */\n\n\n  _reduceValue() {\n    return this._reduceChildren({}, (acc, control, name) => {\n      if (control.enabled || this.disabled) {\n        acc[name] = control.value;\n      }\n\n      return acc;\n    });\n  }\n  /** @internal */\n\n\n  _reduceChildren(initValue, fn) {\n    let res = initValue;\n\n    this._forEachChild((control, name) => {\n      res = fn(res, control, name);\n    });\n\n    return res;\n  }\n  /** @internal */\n\n\n  _allControlsDisabled() {\n    for (const controlName of Object.keys(this.controls)) {\n      if (this.controls[controlName].enabled) {\n        return false;\n      }\n    }\n\n    return Object.keys(this.controls).length > 0 || this.disabled;\n  }\n\n}\n/**\n * Tracks the value and validity state of an array of `FormControl`,\n * `FormGroup` or `FormArray` instances.\n *\n * A `FormArray` aggregates the values of each child `FormControl` into an array.\n * It calculates its status by reducing the status values of its children. For example, if one of\n * the controls in a `FormArray` is invalid, the entire array becomes invalid.\n *\n * `FormArray` is one of the three fundamental building blocks used to define forms in Angular,\n * along with `FormControl` and `FormGroup`.\n *\n * @usageNotes\n *\n * ### Create an array of form controls\n *\n * ```\n * const arr = new FormArray([\n *   new FormControl('Nancy', Validators.minLength(2)),\n *   new FormControl('Drew'),\n * ]);\n *\n * console.log(arr.value);   // ['Nancy', 'Drew']\n * console.log(arr.status);  // 'VALID'\n * ```\n *\n * ### Create a form array with array-level validators\n *\n * You include array-level validators and async validators. These come in handy\n * when you want to perform validation that considers the value of more than one child\n * control.\n *\n * The two types of validators are passed in separately as the second and third arg\n * respectively, or together as part of an options object.\n *\n * ```\n * const arr = new FormArray([\n *   new FormControl('Nancy'),\n *   new FormControl('Drew')\n * ], {validators: myValidator, asyncValidators: myAsyncValidator});\n * ```\n *\n * ### Set the updateOn property for all controls in a form array\n *\n * The options object is used to set a default value for each child\n * control's `updateOn` property. If you set `updateOn` to `'blur'` at the\n * array level, all child controls default to 'blur', unless the child\n * has explicitly specified a different `updateOn` value.\n *\n * ```ts\n * const arr = new FormArray([\n *    new FormControl()\n * ], {updateOn: 'blur'});\n * ```\n *\n * ### Adding or removing controls from a form array\n *\n * To change the controls in the array, use the `push`, `insert`, `removeAt` or `clear` methods\n * in `FormArray` itself. These methods ensure the controls are properly tracked in the\n * form's hierarchy. Do not modify the array of `AbstractControl`s used to instantiate\n * the `FormArray` directly, as that result in strange and unexpected behavior such\n * as broken change detection.\n *\n * @publicApi\n */\n\n\nclass FormArray extends AbstractControl {\n  /**\n   * Creates a new `FormArray` instance.\n   *\n   * @param controls An array of child controls. Each child control is given an index\n   * where it is registered.\n   *\n   * @param validatorOrOpts A synchronous validator function, or an array of\n   * such functions, or an `AbstractControlOptions` object that contains validation functions\n   * and a validation trigger.\n   *\n   * @param asyncValidator A single async validator or array of async validator functions\n   *\n   */\n  constructor(controls, validatorOrOpts, asyncValidator) {\n    super(pickValidators(validatorOrOpts), pickAsyncValidators(asyncValidator, validatorOrOpts));\n    this.controls = controls;\n\n    this._initObservables();\n\n    this._setUpdateStrategy(validatorOrOpts);\n\n    this._setUpControls();\n\n    this.updateValueAndValidity({\n      onlySelf: true,\n      // If `asyncValidator` is present, it will trigger control status change from `PENDING` to\n      // `VALID` or `INVALID`.\n      // The status should be broadcasted via the `statusChanges` observable, so we set `emitEvent`\n      // to `true` to allow that during the control creation process.\n      emitEvent: !!this.asyncValidator\n    });\n  }\n  /**\n   * Get the `AbstractControl` at the given `index` in the array.\n   *\n   * @param index Index in the array to retrieve the control\n   */\n\n\n  at(index) {\n    return this.controls[index];\n  }\n  /**\n   * Insert a new `AbstractControl` at the end of the array.\n   *\n   * @param control Form control to be inserted\n   * @param options Specifies whether this FormArray instance should emit events after a new\n   *     control is added.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * inserted. When false, no events are emitted.\n   */\n\n\n  push(control, options = {}) {\n    this.controls.push(control);\n\n    this._registerControl(control);\n\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n\n    this._onCollectionChange();\n  }\n  /**\n   * Insert a new `AbstractControl` at the given `index` in the array.\n   *\n   * @param index Index in the array to insert the control\n   * @param control Form control to be inserted\n   * @param options Specifies whether this FormArray instance should emit events after a new\n   *     control is inserted.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * inserted. When false, no events are emitted.\n   */\n\n\n  insert(index, control, options = {}) {\n    this.controls.splice(index, 0, control);\n\n    this._registerControl(control);\n\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n  }\n  /**\n   * Remove the control at the given `index` in the array.\n   *\n   * @param index Index in the array to remove the control\n   * @param options Specifies whether this FormArray instance should emit events after a\n   *     control is removed.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * removed. When false, no events are emitted.\n   */\n\n\n  removeAt(index, options = {}) {\n    if (this.controls[index]) this.controls[index]._registerOnCollectionChange(() => {});\n    this.controls.splice(index, 1);\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n  }\n  /**\n   * Replace an existing control.\n   *\n   * @param index Index in the array to replace the control\n   * @param control The `AbstractControl` control to replace the existing control\n   * @param options Specifies whether this FormArray instance should emit events after an\n   *     existing control is replaced with a new one.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * replaced with a new one. When false, no events are emitted.\n   */\n\n\n  setControl(index, control, options = {}) {\n    if (this.controls[index]) this.controls[index]._registerOnCollectionChange(() => {});\n    this.controls.splice(index, 1);\n\n    if (control) {\n      this.controls.splice(index, 0, control);\n\n      this._registerControl(control);\n    }\n\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n\n    this._onCollectionChange();\n  }\n  /**\n   * Length of the control array.\n   */\n\n\n  get length() {\n    return this.controls.length;\n  }\n  /**\n   * Sets the value of the `FormArray`. It accepts an array that matches\n   * the structure of the control.\n   *\n   * This method performs strict checks, and throws an error if you try\n   * to set the value of a control that doesn't exist or if you exclude the\n   * value of a control.\n   *\n   * @usageNotes\n   * ### Set the values for the controls in the form array\n   *\n   * ```\n   * const arr = new FormArray([\n   *   new FormControl(),\n   *   new FormControl()\n   * ]);\n   * console.log(arr.value);   // [null, null]\n   *\n   * arr.setValue(['Nancy', 'Drew']);\n   * console.log(arr.value);   // ['Nancy', 'Drew']\n   * ```\n   *\n   * @param value Array of values for the controls\n   * @param options Configure options that determine how the control propagates changes and\n   * emits events after the value changes\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default\n   * is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control value is updated.\n   * When false, no events are emitted.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   */\n\n\n  setValue(value, options = {}) {\n    assertAllValuesPresent(this, value);\n    value.forEach((newValue, index) => {\n      assertControlPresent(this, index);\n      this.at(index).setValue(newValue, {\n        onlySelf: true,\n        emitEvent: options.emitEvent\n      });\n    });\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * Patches the value of the `FormArray`. It accepts an array that matches the\n   * structure of the control, and does its best to match the values to the correct\n   * controls in the group.\n   *\n   * It accepts both super-sets and sub-sets of the array without throwing an error.\n   *\n   * @usageNotes\n   * ### Patch the values for controls in a form array\n   *\n   * ```\n   * const arr = new FormArray([\n   *    new FormControl(),\n   *    new FormControl()\n   * ]);\n   * console.log(arr.value);   // [null, null]\n   *\n   * arr.patchValue(['Nancy']);\n   * console.log(arr.value);   // ['Nancy', null]\n   * ```\n   *\n   * @param value Array of latest values for the controls\n   * @param options Configure options that determine how the control propagates changes and\n   * emits events after the value changes\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default\n   * is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control value\n   * is updated. When false, no events are emitted. The configuration options are passed to\n   * the {@link AbstractControl#updateValueAndValidity updateValueAndValidity} method.\n   */\n\n\n  patchValue(value, options = {}) {\n    // Even though the `value` argument type doesn't allow `null` and `undefined` values, the\n    // `patchValue` can be called recursively and inner data structures might have these values, so\n    // we just ignore such cases when a field containing FormArray instance receives `null` or\n    // `undefined` as a value.\n    if (value == null\n    /* both `null` and `undefined` */\n    ) return;\n    value.forEach((newValue, index) => {\n      if (this.at(index)) {\n        this.at(index).patchValue(newValue, {\n          onlySelf: true,\n          emitEvent: options.emitEvent\n        });\n      }\n    });\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * Resets the `FormArray` and all descendants are marked `pristine` and `untouched`, and the\n   * value of all descendants to null or null maps.\n   *\n   * You reset to a specific form state by passing in an array of states\n   * that matches the structure of the control. The state is a standalone value\n   * or a form state object with both a value and a disabled status.\n   *\n   * @usageNotes\n   * ### Reset the values in a form array\n   *\n   * ```ts\n   * const arr = new FormArray([\n   *    new FormControl(),\n   *    new FormControl()\n   * ]);\n   * arr.reset(['name', 'last name']);\n   *\n   * console.log(arr.value);  // ['name', 'last name']\n   * ```\n   *\n   * ### Reset the values in a form array and the disabled status for the first control\n   *\n   * ```\n   * arr.reset([\n   *   {value: 'name', disabled: true},\n   *   'last'\n   * ]);\n   *\n   * console.log(arr.value);  // ['last']\n   * console.log(arr.at(0).status);  // 'DISABLED'\n   * ```\n   *\n   * @param value Array of values for the controls\n   * @param options Configure options that determine how the control propagates changes and\n   * emits events after the value changes\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default\n   * is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is reset.\n   * When false, no events are emitted.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   */\n\n\n  reset(value = [], options = {}) {\n    this._forEachChild((control, index) => {\n      control.reset(value[index], {\n        onlySelf: true,\n        emitEvent: options.emitEvent\n      });\n    });\n\n    this._updatePristine(options);\n\n    this._updateTouched(options);\n\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * The aggregate value of the array, including any disabled controls.\n   *\n   * Reports all values regardless of disabled status.\n   * For enabled controls only, the `value` property is the best way to get the value of the array.\n   */\n\n\n  getRawValue() {\n    return this.controls.map(control => getRawValue(control));\n  }\n  /**\n   * Remove all controls in the `FormArray`.\n   *\n   * @param options Specifies whether this FormArray instance should emit events after all\n   *     controls are removed.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when all controls\n   * in this FormArray instance are removed. When false, no events are emitted.\n   *\n   * @usageNotes\n   * ### Remove all elements from a FormArray\n   *\n   * ```ts\n   * const arr = new FormArray([\n   *    new FormControl(),\n   *    new FormControl()\n   * ]);\n   * console.log(arr.length);  // 2\n   *\n   * arr.clear();\n   * console.log(arr.length);  // 0\n   * ```\n   *\n   * It's a simpler and more efficient alternative to removing all elements one by one:\n   *\n   * ```ts\n   * const arr = new FormArray([\n   *    new FormControl(),\n   *    new FormControl()\n   * ]);\n   *\n   * while (arr.length) {\n   *    arr.removeAt(0);\n   * }\n   * ```\n   */\n\n\n  clear(options = {}) {\n    if (this.controls.length < 1) return;\n\n    this._forEachChild(control => control._registerOnCollectionChange(() => {}));\n\n    this.controls.splice(0);\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n  }\n  /** @internal */\n\n\n  _syncPendingControls() {\n    let subtreeUpdated = this.controls.reduce((updated, child) => {\n      return child._syncPendingControls() ? true : updated;\n    }, false);\n    if (subtreeUpdated) this.updateValueAndValidity({\n      onlySelf: true\n    });\n    return subtreeUpdated;\n  }\n  /** @internal */\n\n\n  _forEachChild(cb) {\n    this.controls.forEach((control, index) => {\n      cb(control, index);\n    });\n  }\n  /** @internal */\n\n\n  _updateValue() {\n    this.value = this.controls.filter(control => control.enabled || this.disabled).map(control => control.value);\n  }\n  /** @internal */\n\n\n  _anyControls(condition) {\n    return this.controls.some(control => control.enabled && condition(control));\n  }\n  /** @internal */\n\n\n  _setUpControls() {\n    this._forEachChild(control => this._registerControl(control));\n  }\n  /** @internal */\n\n\n  _allControlsDisabled() {\n    for (const control of this.controls) {\n      if (control.enabled) return false;\n    }\n\n    return this.controls.length > 0 || this.disabled;\n  }\n\n  _registerControl(control) {\n    control.setParent(this);\n\n    control._registerOnCollectionChange(this._onCollectionChange);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst formDirectiveProvider$1 = {\n  provide: ControlContainer,\n  useExisting: /*#__PURE__*/forwardRef(() => NgForm)\n};\n\nconst resolvedPromise$1 = /*#__PURE__*/(() => Promise.resolve(null))();\n/**\n * @description\n * Creates a top-level `FormGroup` instance and binds it to a form\n * to track aggregate form value and validation status.\n *\n * As soon as you import the `FormsModule`, this directive becomes active by default on\n * all `<form>` tags.  You don't need to add a special selector.\n *\n * You optionally export the directive into a local template variable using `ngForm` as the key\n * (ex: `#myForm=\"ngForm\"`). This is optional, but useful.  Many properties from the underlying\n * `FormGroup` instance are duplicated on the directive itself, so a reference to it\n * gives you access to the aggregate value and validity status of the form, as well as\n * user interaction properties like `dirty` and `touched`.\n *\n * To register child controls with the form, use `NgModel` with a `name`\n * attribute. You may use `NgModelGroup` to create sub-groups within the form.\n *\n * If necessary, listen to the directive's `ngSubmit` event to be notified when the user has\n * triggered a form submission. The `ngSubmit` event emits the original form\n * submission event.\n *\n * In template driven forms, all `<form>` tags are automatically tagged as `NgForm`.\n * To import the `FormsModule` but skip its usage in some forms,\n * for example, to use native HTML5 validation, add the `ngNoForm` and the `<form>`\n * tags won't create an `NgForm` directive. In reactive forms, using `ngNoForm` is\n * unnecessary because the `<form>` tags are inert. In that case, you would\n * refrain from using the `formGroup` directive.\n *\n * @usageNotes\n *\n * ### Listening for form submission\n *\n * The following example shows how to capture the form values from the \"ngSubmit\" event.\n *\n * {@example forms/ts/simpleForm/simple_form_example.ts region='Component'}\n *\n * ### Setting the update options\n *\n * The following example shows you how to change the \"updateOn\" option from its default using\n * ngFormOptions.\n *\n * ```html\n * <form [ngFormOptions]=\"{updateOn: 'blur'}\">\n *    <input name=\"one\" ngModel>  <!-- this ngModel will update on blur -->\n * </form>\n * ```\n *\n * ### Native DOM validation UI\n *\n * In order to prevent the native DOM form validation UI from interfering with Angular's form\n * validation, Angular automatically adds the `novalidate` attribute on any `<form>` whenever\n * `FormModule` or `ReactiveFormModule` are imported into the application.\n * If you want to explicitly enable native DOM validation UI with Angular forms, you can add the\n * `ngNativeValidate` attribute to the `<form>` element:\n *\n * ```html\n * <form ngNativeValidate>\n *   ...\n * </form>\n * ```\n *\n * @ngModule FormsModule\n * @publicApi\n */\n\n\nlet NgForm = /*#__PURE__*/(() => {\n  class NgForm extends ControlContainer {\n    constructor(validators, asyncValidators) {\n      super();\n      /**\n       * @description\n       * Returns whether the form submission has been triggered.\n       */\n\n      this.submitted = false;\n      this._directives = [];\n      /**\n       * @description\n       * Event emitter for the \"ngSubmit\" event\n       */\n\n      this.ngSubmit = new EventEmitter();\n      this.form = new FormGroup({}, composeValidators(validators), composeAsyncValidators(asyncValidators));\n    }\n    /** @nodoc */\n\n\n    ngAfterViewInit() {\n      this._setUpdateStrategy();\n    }\n    /**\n     * @description\n     * The directive instance.\n     */\n\n\n    get formDirective() {\n      return this;\n    }\n    /**\n     * @description\n     * The internal `FormGroup` instance.\n     */\n\n\n    get control() {\n      return this.form;\n    }\n    /**\n     * @description\n     * Returns an array representing the path to this group. Because this directive\n     * always lives at the top level of a form, it is always an empty array.\n     */\n\n\n    get path() {\n      return [];\n    }\n    /**\n     * @description\n     * Returns a map of the controls in this group.\n     */\n\n\n    get controls() {\n      return this.form.controls;\n    }\n    /**\n     * @description\n     * Method that sets up the control directive in this group, re-calculates its value\n     * and validity, and adds the instance to the internal list of directives.\n     *\n     * @param dir The `NgModel` directive instance.\n     */\n\n\n    addControl(dir) {\n      resolvedPromise$1.then(() => {\n        const container = this._findContainer(dir.path);\n\n        dir.control = container.registerControl(dir.name, dir.control);\n        setUpControl(dir.control, dir);\n        dir.control.updateValueAndValidity({\n          emitEvent: false\n        });\n\n        this._directives.push(dir);\n      });\n    }\n    /**\n     * @description\n     * Retrieves the `FormControl` instance from the provided `NgModel` directive.\n     *\n     * @param dir The `NgModel` directive instance.\n     */\n\n\n    getControl(dir) {\n      return this.form.get(dir.path);\n    }\n    /**\n     * @description\n     * Removes the `NgModel` instance from the internal list of directives\n     *\n     * @param dir The `NgModel` directive instance.\n     */\n\n\n    removeControl(dir) {\n      resolvedPromise$1.then(() => {\n        const container = this._findContainer(dir.path);\n\n        if (container) {\n          container.removeControl(dir.name);\n        }\n\n        removeListItem(this._directives, dir);\n      });\n    }\n    /**\n     * @description\n     * Adds a new `NgModelGroup` directive instance to the form.\n     *\n     * @param dir The `NgModelGroup` directive instance.\n     */\n\n\n    addFormGroup(dir) {\n      resolvedPromise$1.then(() => {\n        const container = this._findContainer(dir.path);\n\n        const group = new FormGroup({});\n        setUpFormContainer(group, dir);\n        container.registerControl(dir.name, group);\n        group.updateValueAndValidity({\n          emitEvent: false\n        });\n      });\n    }\n    /**\n     * @description\n     * Removes the `NgModelGroup` directive instance from the form.\n     *\n     * @param dir The `NgModelGroup` directive instance.\n     */\n\n\n    removeFormGroup(dir) {\n      resolvedPromise$1.then(() => {\n        const container = this._findContainer(dir.path);\n\n        if (container) {\n          container.removeControl(dir.name);\n        }\n      });\n    }\n    /**\n     * @description\n     * Retrieves the `FormGroup` for a provided `NgModelGroup` directive instance\n     *\n     * @param dir The `NgModelGroup` directive instance.\n     */\n\n\n    getFormGroup(dir) {\n      return this.form.get(dir.path);\n    }\n    /**\n     * Sets the new value for the provided `NgControl` directive.\n     *\n     * @param dir The `NgControl` directive instance.\n     * @param value The new value for the directive's control.\n     */\n\n\n    updateModel(dir, value) {\n      resolvedPromise$1.then(() => {\n        const ctrl = this.form.get(dir.path);\n        ctrl.setValue(value);\n      });\n    }\n    /**\n     * @description\n     * Sets the value for this `FormGroup`.\n     *\n     * @param value The new value\n     */\n\n\n    setValue(value) {\n      this.control.setValue(value);\n    }\n    /**\n     * @description\n     * Method called when the \"submit\" event is triggered on the form.\n     * Triggers the `ngSubmit` emitter to emit the \"submit\" event as its payload.\n     *\n     * @param $event The \"submit\" event object\n     */\n\n\n    onSubmit($event) {\n      this.submitted = true;\n      syncPendingControls(this.form, this._directives);\n      this.ngSubmit.emit($event);\n      return false;\n    }\n    /**\n     * @description\n     * Method called when the \"reset\" event is triggered on the form.\n     */\n\n\n    onReset() {\n      this.resetForm();\n    }\n    /**\n     * @description\n     * Resets the form to an initial value and resets its submitted status.\n     *\n     * @param value The new value for the form.\n     */\n\n\n    resetForm(value = undefined) {\n      this.form.reset(value);\n      this.submitted = false;\n    }\n\n    _setUpdateStrategy() {\n      if (this.options && this.options.updateOn != null) {\n        this.form._updateOn = this.options.updateOn;\n      }\n    }\n    /** @internal */\n\n\n    _findContainer(path) {\n      path.pop();\n      return path.length ? this.form.get(path) : this.form;\n    }\n\n  }\n\n  NgForm.ɵfac = function NgForm_Factory(t) {\n    return new (t || NgForm)(i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10));\n  };\n\n  NgForm.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgForm,\n    selectors: [[\"form\", 3, \"ngNoForm\", \"\", 3, \"formGroup\", \"\"], [\"ng-form\"], [\"\", \"ngForm\", \"\"]],\n    hostBindings: function NgForm_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"submit\", function NgForm_submit_HostBindingHandler($event) {\n          return ctx.onSubmit($event);\n        })(\"reset\", function NgForm_reset_HostBindingHandler() {\n          return ctx.onReset();\n        });\n      }\n    },\n    inputs: {\n      options: [\"ngFormOptions\", \"options\"]\n    },\n    outputs: {\n      ngSubmit: \"ngSubmit\"\n    },\n    exportAs: [\"ngForm\"],\n    features: [i0.ɵɵProvidersFeature([formDirectiveProvider$1]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return NgForm;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @description\n * A base class for code shared between the `NgModelGroup` and `FormGroupName` directives.\n *\n * @publicApi\n */\n\n\nlet AbstractFormGroupDirective = /*#__PURE__*/(() => {\n  class AbstractFormGroupDirective extends ControlContainer {\n    /** @nodoc */\n    ngOnInit() {\n      this._checkParentType(); // Register the group with its parent group.\n\n\n      this.formDirective.addFormGroup(this);\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      if (this.formDirective) {\n        // Remove the group from its parent group.\n        this.formDirective.removeFormGroup(this);\n      }\n    }\n    /**\n     * @description\n     * The `FormGroup` bound to this directive.\n     */\n\n\n    get control() {\n      return this.formDirective.getFormGroup(this);\n    }\n    /**\n     * @description\n     * The path to this group from the top-level directive.\n     */\n\n\n    get path() {\n      return controlPath(this.name == null ? this.name : this.name.toString(), this._parent);\n    }\n    /**\n     * @description\n     * The top-level directive for this group if present, otherwise null.\n     */\n\n\n    get formDirective() {\n      return this._parent ? this._parent.formDirective : null;\n    }\n    /** @internal */\n\n\n    _checkParentType() {}\n\n  }\n\n  AbstractFormGroupDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵAbstractFormGroupDirective_BaseFactory;\n    return function AbstractFormGroupDirective_Factory(t) {\n      return (ɵAbstractFormGroupDirective_BaseFactory || (ɵAbstractFormGroupDirective_BaseFactory = i0.ɵɵgetInheritedFactory(AbstractFormGroupDirective)))(t || AbstractFormGroupDirective);\n    };\n  }();\n\n  AbstractFormGroupDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: AbstractFormGroupDirective,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return AbstractFormGroupDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction modelParentException() {\n  return new Error(`\n    ngModel cannot be used to register form controls with a parent formGroup directive.  Try using\n    formGroup's partner directive \"formControlName\" instead.  Example:\n\n    ${formControlNameExample}\n\n    Or, if you'd like to avoid registering this form control, indicate that it's standalone in ngModelOptions:\n\n    Example:\n\n    ${ngModelWithFormGroupExample}`);\n}\n\nfunction formGroupNameException() {\n  return new Error(`\n    ngModel cannot be used to register form controls with a parent formGroupName or formArrayName directive.\n\n    Option 1: Use formControlName instead of ngModel (reactive strategy):\n\n    ${formGroupNameExample}\n\n    Option 2:  Update ngModel's parent be ngModelGroup (template-driven strategy):\n\n    ${ngModelGroupExample}`);\n}\n\nfunction missingNameException() {\n  return new Error(`If ngModel is used within a form tag, either the name attribute must be set or the form\n    control must be defined as 'standalone' in ngModelOptions.\n\n    Example 1: <input [(ngModel)]=\"person.firstName\" name=\"first\">\n    Example 2: <input [(ngModel)]=\"person.firstName\" [ngModelOptions]=\"{standalone: true}\">`);\n}\n\nfunction modelGroupParentException() {\n  return new Error(`\n    ngModelGroup cannot be used with a parent formGroup directive.\n\n    Option 1: Use formGroupName instead of ngModelGroup (reactive strategy):\n\n    ${formGroupNameExample}\n\n    Option 2:  Use a regular form tag instead of the formGroup directive (template-driven strategy):\n\n    ${ngModelGroupExample}`);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst modelGroupProvider = {\n  provide: ControlContainer,\n  useExisting: /*#__PURE__*/forwardRef(() => NgModelGroup)\n};\n/**\n * @description\n * Creates and binds a `FormGroup` instance to a DOM element.\n *\n * This directive can only be used as a child of `NgForm` (within `<form>` tags).\n *\n * Use this directive to validate a sub-group of your form separately from the\n * rest of your form, or if some values in your domain model make more sense\n * to consume together in a nested object.\n *\n * Provide a name for the sub-group and it will become the key\n * for the sub-group in the form's full value. If you need direct access, export the directive into\n * a local template variable using `ngModelGroup` (ex: `#myGroup=\"ngModelGroup\"`).\n *\n * @usageNotes\n *\n * ### Consuming controls in a grouping\n *\n * The following example shows you how to combine controls together in a sub-group\n * of the form.\n *\n * {@example forms/ts/ngModelGroup/ng_model_group_example.ts region='Component'}\n *\n * @ngModule FormsModule\n * @publicApi\n */\n\nlet NgModelGroup = /*#__PURE__*/(() => {\n  class NgModelGroup extends AbstractFormGroupDirective {\n    constructor(parent, validators, asyncValidators) {\n      super();\n      this._parent = parent;\n\n      this._setValidators(validators);\n\n      this._setAsyncValidators(asyncValidators);\n    }\n    /** @internal */\n\n\n    _checkParentType() {\n      if (!(this._parent instanceof NgModelGroup) && !(this._parent instanceof NgForm) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw modelGroupParentException();\n      }\n    }\n\n  }\n\n  NgModelGroup.ɵfac = function NgModelGroup_Factory(t) {\n    return new (t || NgModelGroup)(i0.ɵɵdirectiveInject(ControlContainer, 5), i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10));\n  };\n\n  NgModelGroup.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgModelGroup,\n    selectors: [[\"\", \"ngModelGroup\", \"\"]],\n    inputs: {\n      name: [\"ngModelGroup\", \"name\"]\n    },\n    exportAs: [\"ngModelGroup\"],\n    features: [i0.ɵɵProvidersFeature([modelGroupProvider]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return NgModelGroup;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst formControlBinding$1 = {\n  provide: NgControl,\n  useExisting: /*#__PURE__*/forwardRef(() => NgModel)\n};\n/**\n * `ngModel` forces an additional change detection run when its inputs change:\n * E.g.:\n * ```\n * <div>{{myModel.valid}}</div>\n * <input [(ngModel)]=\"myValue\" #myModel=\"ngModel\">\n * ```\n * I.e. `ngModel` can export itself on the element and then be used in the template.\n * Normally, this would result in expressions before the `input` that use the exported directive\n * to have an old value as they have been\n * dirty checked before. As this is a very common case for `ngModel`, we added this second change\n * detection run.\n *\n * Notes:\n * - this is just one extra run no matter how many `ngModel`s have been changed.\n * - this is a general problem when using `exportAs` for directives!\n */\n\nconst resolvedPromise = /*#__PURE__*/(() => Promise.resolve(null))();\n/**\n * @description\n * Creates a `FormControl` instance from a domain model and binds it\n * to a form control element.\n *\n * The `FormControl` instance tracks the value, user interaction, and\n * validation status of the control and keeps the view synced with the model. If used\n * within a parent form, the directive also registers itself with the form as a child\n * control.\n *\n * This directive is used by itself or as part of a larger form. Use the\n * `ngModel` selector to activate it.\n *\n * It accepts a domain model as an optional `Input`. If you have a one-way binding\n * to `ngModel` with `[]` syntax, changing the domain model's value in the component\n * class sets the value in the view. If you have a two-way binding with `[()]` syntax\n * (also known as 'banana-in-a-box syntax'), the value in the UI always syncs back to\n * the domain model in your class.\n *\n * To inspect the properties of the associated `FormControl` (like the validity state),\n * export the directive into a local template variable using `ngModel` as the key (ex:\n * `#myVar=\"ngModel\"`). You can then access the control using the directive's `control` property.\n * However, the most commonly used properties (like `valid` and `dirty`) also exist on the control\n * for direct access. See a full list of properties directly available in\n * `AbstractControlDirective`.\n *\n * @see `RadioControlValueAccessor`\n * @see `SelectControlValueAccessor`\n *\n * @usageNotes\n *\n * ### Using ngModel on a standalone control\n *\n * The following examples show a simple standalone control using `ngModel`:\n *\n * {@example forms/ts/simpleNgModel/simple_ng_model_example.ts region='Component'}\n *\n * When using the `ngModel` within `<form>` tags, you'll also need to supply a `name` attribute\n * so that the control can be registered with the parent form under that name.\n *\n * In the context of a parent form, it's often unnecessary to include one-way or two-way binding,\n * as the parent form syncs the value for you. You access its properties by exporting it into a\n * local template variable using `ngForm` such as (`#f=\"ngForm\"`). Use the variable where\n * needed on form submission.\n *\n * If you do need to populate initial values into your form, using a one-way binding for\n * `ngModel` tends to be sufficient as long as you use the exported form's value rather\n * than the domain model's value on submit.\n *\n * ### Using ngModel within a form\n *\n * The following example shows controls using `ngModel` within a form:\n *\n * {@example forms/ts/simpleForm/simple_form_example.ts region='Component'}\n *\n * ### Using a standalone ngModel within a group\n *\n * The following example shows you how to use a standalone ngModel control\n * within a form. This controls the display of the form, but doesn't contain form data.\n *\n * ```html\n * <form>\n *   <input name=\"login\" ngModel placeholder=\"Login\">\n *   <input type=\"checkbox\" ngModel [ngModelOptions]=\"{standalone: true}\"> Show more options?\n * </form>\n * <!-- form value: {login: ''} -->\n * ```\n *\n * ### Setting the ngModel `name` attribute through options\n *\n * The following example shows you an alternate way to set the name attribute. Here,\n * an attribute identified as name is used within a custom form control component. To still be able\n * to specify the NgModel's name, you must specify it using the `ngModelOptions` input instead.\n *\n * ```html\n * <form>\n *   <my-custom-form-control name=\"Nancy\" ngModel [ngModelOptions]=\"{name: 'user'}\">\n *   </my-custom-form-control>\n * </form>\n * <!-- form value: {user: ''} -->\n * ```\n *\n * @ngModule FormsModule\n * @publicApi\n */\n\n\nlet NgModel = /*#__PURE__*/(() => {\n  class NgModel extends NgControl {\n    constructor(parent, validators, asyncValidators, valueAccessors) {\n      super();\n      this.control = new FormControl();\n      /** @internal */\n\n      this._registered = false;\n      /**\n       * @description\n       * Event emitter for producing the `ngModelChange` event after\n       * the view model updates.\n       */\n\n      this.update = new EventEmitter();\n      this._parent = parent;\n\n      this._setValidators(validators);\n\n      this._setAsyncValidators(asyncValidators);\n\n      this.valueAccessor = selectValueAccessor(this, valueAccessors);\n    }\n    /** @nodoc */\n\n\n    ngOnChanges(changes) {\n      this._checkForErrors();\n\n      if (!this._registered) this._setUpControl();\n\n      if ('isDisabled' in changes) {\n        this._updateDisabled(changes);\n      }\n\n      if (isPropertyUpdated(changes, this.viewModel)) {\n        this._updateValue(this.model);\n\n        this.viewModel = this.model;\n      }\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      this.formDirective && this.formDirective.removeControl(this);\n    }\n    /**\n     * @description\n     * Returns an array that represents the path from the top-level form to this control.\n     * Each index is the string name of the control on that level.\n     */\n\n\n    get path() {\n      return this._parent ? controlPath(this.name, this._parent) : [this.name];\n    }\n    /**\n     * @description\n     * The top-level directive for this control if present, otherwise null.\n     */\n\n\n    get formDirective() {\n      return this._parent ? this._parent.formDirective : null;\n    }\n    /**\n     * @description\n     * Sets the new value for the view model and emits an `ngModelChange` event.\n     *\n     * @param newValue The new value emitted by `ngModelChange`.\n     */\n\n\n    viewToModelUpdate(newValue) {\n      this.viewModel = newValue;\n      this.update.emit(newValue);\n    }\n\n    _setUpControl() {\n      this._setUpdateStrategy();\n\n      this._isStandalone() ? this._setUpStandalone() : this.formDirective.addControl(this);\n      this._registered = true;\n    }\n\n    _setUpdateStrategy() {\n      if (this.options && this.options.updateOn != null) {\n        this.control._updateOn = this.options.updateOn;\n      }\n    }\n\n    _isStandalone() {\n      return !this._parent || !!(this.options && this.options.standalone);\n    }\n\n    _setUpStandalone() {\n      setUpControl(this.control, this);\n      this.control.updateValueAndValidity({\n        emitEvent: false\n      });\n    }\n\n    _checkForErrors() {\n      if (!this._isStandalone()) {\n        this._checkParentType();\n      }\n\n      this._checkName();\n    }\n\n    _checkParentType() {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (!(this._parent instanceof NgModelGroup) && this._parent instanceof AbstractFormGroupDirective) {\n          throw formGroupNameException();\n        } else if (!(this._parent instanceof NgModelGroup) && !(this._parent instanceof NgForm)) {\n          throw modelParentException();\n        }\n      }\n    }\n\n    _checkName() {\n      if (this.options && this.options.name) this.name = this.options.name;\n\n      if (!this._isStandalone() && !this.name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw missingNameException();\n      }\n    }\n\n    _updateValue(value) {\n      resolvedPromise.then(() => {\n        this.control.setValue(value, {\n          emitViewToModelChange: false\n        });\n      });\n    }\n\n    _updateDisabled(changes) {\n      const disabledValue = changes['isDisabled'].currentValue;\n      const isDisabled = disabledValue === '' || disabledValue && disabledValue !== 'false';\n      resolvedPromise.then(() => {\n        if (isDisabled && !this.control.disabled) {\n          this.control.disable();\n        } else if (!isDisabled && this.control.disabled) {\n          this.control.enable();\n        }\n      });\n    }\n\n  }\n\n  NgModel.ɵfac = function NgModel_Factory(t) {\n    return new (t || NgModel)(i0.ɵɵdirectiveInject(ControlContainer, 9), i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_VALUE_ACCESSOR, 10));\n  };\n\n  NgModel.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgModel,\n    selectors: [[\"\", \"ngModel\", \"\", 3, \"formControlName\", \"\", 3, \"formControl\", \"\"]],\n    inputs: {\n      name: \"name\",\n      isDisabled: [\"disabled\", \"isDisabled\"],\n      model: [\"ngModel\", \"model\"],\n      options: [\"ngModelOptions\", \"options\"]\n    },\n    outputs: {\n      update: \"ngModelChange\"\n    },\n    exportAs: [\"ngModel\"],\n    features: [i0.ɵɵProvidersFeature([formControlBinding$1]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n  return NgModel;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @description\n *\n * Adds `novalidate` attribute to all forms by default.\n *\n * `novalidate` is used to disable browser's native form validation.\n *\n * If you want to use native validation with Angular forms, just add `ngNativeValidate` attribute:\n *\n * ```\n * <form ngNativeValidate></form>\n * ```\n *\n * @publicApi\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n */\n\n\nlet ɵNgNoValidate = /*#__PURE__*/(() => {\n  class ɵNgNoValidate {}\n\n  ɵNgNoValidate.ɵfac = function ɵNgNoValidate_Factory(t) {\n    return new (t || ɵNgNoValidate)();\n  };\n\n  ɵNgNoValidate.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ɵNgNoValidate,\n    selectors: [[\"form\", 3, \"ngNoForm\", \"\", 3, \"ngNativeValidate\", \"\"]],\n    hostAttrs: [\"novalidate\", \"\"]\n  });\n  return ɵNgNoValidate;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst NUMBER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => NumberValueAccessor),\n  multi: true\n};\n/**\n * @description\n * The `ControlValueAccessor` for writing a number value and listening to number input changes.\n * The value accessor is used by the `FormControlDirective`, `FormControlName`, and `NgModel`\n * directives.\n *\n * @usageNotes\n *\n * ### Using a number input with a reactive form.\n *\n * The following example shows how to use a number input with a reactive form.\n *\n * ```ts\n * const totalCountControl = new FormControl();\n * ```\n *\n * ```\n * <input type=\"number\" [formControl]=\"totalCountControl\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\nlet NumberValueAccessor = /*#__PURE__*/(() => {\n  class NumberValueAccessor extends BuiltInControlValueAccessor {\n    /**\n     * Sets the \"value\" property on the input element.\n     * @nodoc\n     */\n    writeValue(value) {\n      // The value needs to be normalized for IE9, otherwise it is set to 'null' when null\n      const normalizedValue = value == null ? '' : value;\n      this.setProperty('value', normalizedValue);\n    }\n    /**\n     * Registers a function called when the control value changes.\n     * @nodoc\n     */\n\n\n    registerOnChange(fn) {\n      this.onChange = value => {\n        fn(value == '' ? null : parseFloat(value));\n      };\n    }\n\n  }\n\n  NumberValueAccessor.ɵfac = /* @__PURE__ */function () {\n    let ɵNumberValueAccessor_BaseFactory;\n    return function NumberValueAccessor_Factory(t) {\n      return (ɵNumberValueAccessor_BaseFactory || (ɵNumberValueAccessor_BaseFactory = i0.ɵɵgetInheritedFactory(NumberValueAccessor)))(t || NumberValueAccessor);\n    };\n  }();\n\n  NumberValueAccessor.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NumberValueAccessor,\n    selectors: [[\"input\", \"type\", \"number\", \"formControlName\", \"\"], [\"input\", \"type\", \"number\", \"formControl\", \"\"], [\"input\", \"type\", \"number\", \"ngModel\", \"\"]],\n    hostBindings: function NumberValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function NumberValueAccessor_input_HostBindingHandler($event) {\n          return ctx.onChange($event.target.value);\n        })(\"blur\", function NumberValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        });\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([NUMBER_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return NumberValueAccessor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst RADIO_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => RadioControlValueAccessor),\n  multi: true\n};\n\nfunction throwNameError() {\n  throw new Error(`\n      If you define both a name and a formControlName attribute on your radio button, their values\n      must match. Ex: <input type=\"radio\" formControlName=\"food\" name=\"food\">\n    `);\n}\n/**\n * Internal-only NgModule that works as a host for the `RadioControlRegistry` tree-shakable\n * provider. Note: the `InternalFormsSharedModule` can not be used here directly, since it's\n * declared *after* the `RadioControlRegistry` class and the `providedIn` doesn't support\n * `forwardRef` logic.\n */\n\n\nlet RadioControlRegistryModule = /*#__PURE__*/(() => {\n  class RadioControlRegistryModule {}\n\n  RadioControlRegistryModule.ɵfac = function RadioControlRegistryModule_Factory(t) {\n    return new (t || RadioControlRegistryModule)();\n  };\n\n  RadioControlRegistryModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: RadioControlRegistryModule\n  });\n  RadioControlRegistryModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return RadioControlRegistryModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n * Class used by Angular to track radio buttons. For internal use only.\n */\n\n\nlet RadioControlRegistry = /*#__PURE__*/(() => {\n  class RadioControlRegistry {\n    constructor() {\n      this._accessors = [];\n    }\n    /**\n     * @description\n     * Adds a control to the internal registry. For internal use only.\n     */\n\n\n    add(control, accessor) {\n      this._accessors.push([control, accessor]);\n    }\n    /**\n     * @description\n     * Removes a control from the internal registry. For internal use only.\n     */\n\n\n    remove(accessor) {\n      for (let i = this._accessors.length - 1; i >= 0; --i) {\n        if (this._accessors[i][1] === accessor) {\n          this._accessors.splice(i, 1);\n\n          return;\n        }\n      }\n    }\n    /**\n     * @description\n     * Selects a radio button. For internal use only.\n     */\n\n\n    select(accessor) {\n      this._accessors.forEach(c => {\n        if (this._isSameGroup(c, accessor) && c[1] !== accessor) {\n          c[1].fireUncheck(accessor.value);\n        }\n      });\n    }\n\n    _isSameGroup(controlPair, accessor) {\n      if (!controlPair[0].control) return false;\n      return controlPair[0]._parent === accessor._control._parent && controlPair[1].name === accessor.name;\n    }\n\n  }\n\n  RadioControlRegistry.ɵfac = function RadioControlRegistry_Factory(t) {\n    return new (t || RadioControlRegistry)();\n  };\n\n  RadioControlRegistry.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RadioControlRegistry,\n    factory: RadioControlRegistry.ɵfac,\n    providedIn: RadioControlRegistryModule\n  });\n  return RadioControlRegistry;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n * The `ControlValueAccessor` for writing radio control values and listening to radio control\n * changes. The value accessor is used by the `FormControlDirective`, `FormControlName`, and\n * `NgModel` directives.\n *\n * @usageNotes\n *\n * ### Using radio buttons with reactive form directives\n *\n * The follow example shows how to use radio buttons in a reactive form. When using radio buttons in\n * a reactive form, radio buttons in the same group should have the same `formControlName`.\n * Providing a `name` attribute is optional.\n *\n * {@example forms/ts/reactiveRadioButtons/reactive_radio_button_example.ts region='Reactive'}\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\n\nlet RadioControlValueAccessor = /*#__PURE__*/(() => {\n  class RadioControlValueAccessor extends BuiltInControlValueAccessor {\n    constructor(renderer, elementRef, _registry, _injector) {\n      super(renderer, elementRef);\n      this._registry = _registry;\n      this._injector = _injector;\n      /**\n       * The registered callback function called when a change event occurs on the input element.\n       * Note: we declare `onChange` here (also used as host listener) as a function with no arguments\n       * to override the `onChange` function (which expects 1 argument) in the parent\n       * `BaseControlValueAccessor` class.\n       * @nodoc\n       */\n\n      this.onChange = () => {};\n    }\n    /** @nodoc */\n\n\n    ngOnInit() {\n      this._control = this._injector.get(NgControl);\n\n      this._checkName();\n\n      this._registry.add(this._control, this);\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      this._registry.remove(this);\n    }\n    /**\n     * Sets the \"checked\" property value on the radio input element.\n     * @nodoc\n     */\n\n\n    writeValue(value) {\n      this._state = value === this.value;\n      this.setProperty('checked', this._state);\n    }\n    /**\n     * Registers a function called when the control value changes.\n     * @nodoc\n     */\n\n\n    registerOnChange(fn) {\n      this._fn = fn;\n\n      this.onChange = () => {\n        fn(this.value);\n\n        this._registry.select(this);\n      };\n    }\n    /**\n     * Sets the \"value\" on the radio input element and unchecks it.\n     *\n     * @param value\n     */\n\n\n    fireUncheck(value) {\n      this.writeValue(value);\n    }\n\n    _checkName() {\n      if (this.name && this.formControlName && this.name !== this.formControlName && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwNameError();\n      }\n\n      if (!this.name && this.formControlName) this.name = this.formControlName;\n    }\n\n  }\n\n  RadioControlValueAccessor.ɵfac = function RadioControlValueAccessor_Factory(t) {\n    return new (t || RadioControlValueAccessor)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(RadioControlRegistry), i0.ɵɵdirectiveInject(i0.Injector));\n  };\n\n  RadioControlValueAccessor.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RadioControlValueAccessor,\n    selectors: [[\"input\", \"type\", \"radio\", \"formControlName\", \"\"], [\"input\", \"type\", \"radio\", \"formControl\", \"\"], [\"input\", \"type\", \"radio\", \"ngModel\", \"\"]],\n    hostBindings: function RadioControlValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"change\", function RadioControlValueAccessor_change_HostBindingHandler() {\n          return ctx.onChange();\n        })(\"blur\", function RadioControlValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        });\n      }\n    },\n    inputs: {\n      name: \"name\",\n      formControlName: \"formControlName\",\n      value: \"value\"\n    },\n    features: [i0.ɵɵProvidersFeature([RADIO_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return RadioControlValueAccessor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst RANGE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => RangeValueAccessor),\n  multi: true\n};\n/**\n * @description\n * The `ControlValueAccessor` for writing a range value and listening to range input changes.\n * The value accessor is used by the `FormControlDirective`, `FormControlName`, and  `NgModel`\n * directives.\n *\n * @usageNotes\n *\n * ### Using a range input with a reactive form\n *\n * The following example shows how to use a range input with a reactive form.\n *\n * ```ts\n * const ageControl = new FormControl();\n * ```\n *\n * ```\n * <input type=\"range\" [formControl]=\"ageControl\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\nlet RangeValueAccessor = /*#__PURE__*/(() => {\n  class RangeValueAccessor extends BuiltInControlValueAccessor {\n    /**\n     * Sets the \"value\" property on the input element.\n     * @nodoc\n     */\n    writeValue(value) {\n      this.setProperty('value', parseFloat(value));\n    }\n    /**\n     * Registers a function called when the control value changes.\n     * @nodoc\n     */\n\n\n    registerOnChange(fn) {\n      this.onChange = value => {\n        fn(value == '' ? null : parseFloat(value));\n      };\n    }\n\n  }\n\n  RangeValueAccessor.ɵfac = /* @__PURE__ */function () {\n    let ɵRangeValueAccessor_BaseFactory;\n    return function RangeValueAccessor_Factory(t) {\n      return (ɵRangeValueAccessor_BaseFactory || (ɵRangeValueAccessor_BaseFactory = i0.ɵɵgetInheritedFactory(RangeValueAccessor)))(t || RangeValueAccessor);\n    };\n  }();\n\n  RangeValueAccessor.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RangeValueAccessor,\n    selectors: [[\"input\", \"type\", \"range\", \"formControlName\", \"\"], [\"input\", \"type\", \"range\", \"formControl\", \"\"], [\"input\", \"type\", \"range\", \"ngModel\", \"\"]],\n    hostBindings: function RangeValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"change\", function RangeValueAccessor_change_HostBindingHandler($event) {\n          return ctx.onChange($event.target.value);\n        })(\"input\", function RangeValueAccessor_input_HostBindingHandler($event) {\n          return ctx.onChange($event.target.value);\n        })(\"blur\", function RangeValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        });\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([RANGE_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return RangeValueAccessor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Token to provide to turn off the ngModel warning on formControl and formControlName.\n */\n\n\nconst NG_MODEL_WITH_FORM_CONTROL_WARNING = /*#__PURE__*/new InjectionToken('NgModelWithFormControlWarning');\nconst formControlBinding = {\n  provide: NgControl,\n  useExisting: /*#__PURE__*/forwardRef(() => FormControlDirective)\n};\n/**\n * @description\n * Synchronizes a standalone `FormControl` instance to a form control element.\n *\n * Note that support for using the `ngModel` input property and `ngModelChange` event with reactive\n * form directives was deprecated in Angular v6 and is scheduled for removal in\n * a future version of Angular.\n * For details, see [Deprecated features](guide/deprecations#ngmodel-with-reactive-forms).\n *\n * @see [Reactive Forms Guide](guide/reactive-forms)\n * @see `FormControl`\n * @see `AbstractControl`\n *\n * @usageNotes\n *\n * The following example shows how to register a standalone control and set its value.\n *\n * {@example forms/ts/simpleFormControl/simple_form_control_example.ts region='Component'}\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\n\nlet FormControlDirective = /*#__PURE__*/(() => {\n  class FormControlDirective extends NgControl {\n    constructor(validators, asyncValidators, valueAccessors, _ngModelWarningConfig) {\n      super();\n      this._ngModelWarningConfig = _ngModelWarningConfig;\n      /** @deprecated as of v6 */\n\n      this.update = new EventEmitter();\n      /**\n       * @description\n       * Instance property used to track whether an ngModel warning has been sent out for this\n       * particular `FormControlDirective` instance. Used to support warning config of \"always\".\n       *\n       * @internal\n       */\n\n      this._ngModelWarningSent = false;\n\n      this._setValidators(validators);\n\n      this._setAsyncValidators(asyncValidators);\n\n      this.valueAccessor = selectValueAccessor(this, valueAccessors);\n    }\n    /**\n     * @description\n     * Triggers a warning in dev mode that this input should not be used with reactive forms.\n     */\n\n\n    set isDisabled(isDisabled) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        console.warn(disabledAttrWarning);\n      }\n    }\n    /** @nodoc */\n\n\n    ngOnChanges(changes) {\n      if (this._isControlChanged(changes)) {\n        const previousForm = changes['form'].previousValue;\n\n        if (previousForm) {\n          cleanUpControl(previousForm, this,\n          /* validateControlPresenceOnChange */\n          false);\n        }\n\n        setUpControl(this.form, this);\n\n        if (this.control.disabled && this.valueAccessor.setDisabledState) {\n          this.valueAccessor.setDisabledState(true);\n        }\n\n        this.form.updateValueAndValidity({\n          emitEvent: false\n        });\n      }\n\n      if (isPropertyUpdated(changes, this.viewModel)) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          _ngModelWarning('formControl', FormControlDirective, this, this._ngModelWarningConfig);\n        }\n\n        this.form.setValue(this.model);\n        this.viewModel = this.model;\n      }\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      if (this.form) {\n        cleanUpControl(this.form, this,\n        /* validateControlPresenceOnChange */\n        false);\n      }\n    }\n    /**\n     * @description\n     * Returns an array that represents the path from the top-level form to this control.\n     * Each index is the string name of the control on that level.\n     */\n\n\n    get path() {\n      return [];\n    }\n    /**\n     * @description\n     * The `FormControl` bound to this directive.\n     */\n\n\n    get control() {\n      return this.form;\n    }\n    /**\n     * @description\n     * Sets the new value for the view model and emits an `ngModelChange` event.\n     *\n     * @param newValue The new value for the view model.\n     */\n\n\n    viewToModelUpdate(newValue) {\n      this.viewModel = newValue;\n      this.update.emit(newValue);\n    }\n\n    _isControlChanged(changes) {\n      return changes.hasOwnProperty('form');\n    }\n\n  }\n\n  /**\n   * @description\n   * Static property used to track whether any ngModel warnings have been sent across\n   * all instances of FormControlDirective. Used to support warning config of \"once\".\n   *\n   * @internal\n   */\n  FormControlDirective._ngModelWarningSentOnce = false;\n\n  FormControlDirective.ɵfac = function FormControlDirective_Factory(t) {\n    return new (t || FormControlDirective)(i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_VALUE_ACCESSOR, 10), i0.ɵɵdirectiveInject(NG_MODEL_WITH_FORM_CONTROL_WARNING, 8));\n  };\n\n  FormControlDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FormControlDirective,\n    selectors: [[\"\", \"formControl\", \"\"]],\n    inputs: {\n      form: [\"formControl\", \"form\"],\n      isDisabled: [\"disabled\", \"isDisabled\"],\n      model: [\"ngModel\", \"model\"]\n    },\n    outputs: {\n      update: \"ngModelChange\"\n    },\n    exportAs: [\"ngForm\"],\n    features: [i0.ɵɵProvidersFeature([formControlBinding]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n  return FormControlDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst formDirectiveProvider = {\n  provide: ControlContainer,\n  useExisting: /*#__PURE__*/forwardRef(() => FormGroupDirective)\n};\n/**\n * @description\n *\n * Binds an existing `FormGroup` to a DOM element.\n *\n * This directive accepts an existing `FormGroup` instance. It will then use this\n * `FormGroup` instance to match any child `FormControl`, `FormGroup`,\n * and `FormArray` instances to child `FormControlName`, `FormGroupName`,\n * and `FormArrayName` directives.\n *\n * @see [Reactive Forms Guide](guide/reactive-forms)\n * @see `AbstractControl`\n *\n * @usageNotes\n * ### Register Form Group\n *\n * The following example registers a `FormGroup` with first name and last name controls,\n * and listens for the *ngSubmit* event when the button is clicked.\n *\n * {@example forms/ts/simpleFormGroup/simple_form_group_example.ts region='Component'}\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\n\nlet FormGroupDirective = /*#__PURE__*/(() => {\n  class FormGroupDirective extends ControlContainer {\n    constructor(validators, asyncValidators) {\n      super();\n      this.validators = validators;\n      this.asyncValidators = asyncValidators;\n      /**\n       * @description\n       * Reports whether the form submission has been triggered.\n       */\n\n      this.submitted = false;\n      /**\n       * Callback that should be invoked when controls in FormGroup or FormArray collection change\n       * (added or removed). This callback triggers corresponding DOM updates.\n       */\n\n      this._onCollectionChange = () => this._updateDomValue();\n      /**\n       * @description\n       * Tracks the list of added `FormControlName` instances\n       */\n\n\n      this.directives = [];\n      /**\n       * @description\n       * Tracks the `FormGroup` bound to this directive.\n       */\n\n      this.form = null;\n      /**\n       * @description\n       * Emits an event when the form submission has been triggered.\n       */\n\n      this.ngSubmit = new EventEmitter();\n\n      this._setValidators(validators);\n\n      this._setAsyncValidators(asyncValidators);\n    }\n    /** @nodoc */\n\n\n    ngOnChanges(changes) {\n      this._checkFormPresent();\n\n      if (changes.hasOwnProperty('form')) {\n        this._updateValidators();\n\n        this._updateDomValue();\n\n        this._updateRegistrations();\n\n        this._oldForm = this.form;\n      }\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      if (this.form) {\n        cleanUpValidators(this.form, this); // Currently the `onCollectionChange` callback is rewritten each time the\n        // `_registerOnCollectionChange` function is invoked. The implication is that cleanup should\n        // happen *only* when the `onCollectionChange` callback was set by this directive instance.\n        // Otherwise it might cause overriding a callback of some other directive instances. We should\n        // consider updating this logic later to make it similar to how `onChange` callbacks are\n        // handled, see https://github.com/angular/angular/issues/39732 for additional info.\n\n        if (this.form._onCollectionChange === this._onCollectionChange) {\n          this.form._registerOnCollectionChange(() => {});\n        }\n      }\n    }\n    /**\n     * @description\n     * Returns this directive's instance.\n     */\n\n\n    get formDirective() {\n      return this;\n    }\n    /**\n     * @description\n     * Returns the `FormGroup` bound to this directive.\n     */\n\n\n    get control() {\n      return this.form;\n    }\n    /**\n     * @description\n     * Returns an array representing the path to this group. Because this directive\n     * always lives at the top level of a form, it always an empty array.\n     */\n\n\n    get path() {\n      return [];\n    }\n    /**\n     * @description\n     * Method that sets up the control directive in this group, re-calculates its value\n     * and validity, and adds the instance to the internal list of directives.\n     *\n     * @param dir The `FormControlName` directive instance.\n     */\n\n\n    addControl(dir) {\n      const ctrl = this.form.get(dir.path);\n      setUpControl(ctrl, dir);\n      ctrl.updateValueAndValidity({\n        emitEvent: false\n      });\n      this.directives.push(dir);\n      return ctrl;\n    }\n    /**\n     * @description\n     * Retrieves the `FormControl` instance from the provided `FormControlName` directive\n     *\n     * @param dir The `FormControlName` directive instance.\n     */\n\n\n    getControl(dir) {\n      return this.form.get(dir.path);\n    }\n    /**\n     * @description\n     * Removes the `FormControlName` instance from the internal list of directives\n     *\n     * @param dir The `FormControlName` directive instance.\n     */\n\n\n    removeControl(dir) {\n      cleanUpControl(dir.control || null, dir,\n      /* validateControlPresenceOnChange */\n      false);\n      removeListItem(this.directives, dir);\n    }\n    /**\n     * Adds a new `FormGroupName` directive instance to the form.\n     *\n     * @param dir The `FormGroupName` directive instance.\n     */\n\n\n    addFormGroup(dir) {\n      this._setUpFormContainer(dir);\n    }\n    /**\n     * Performs the necessary cleanup when a `FormGroupName` directive instance is removed from the\n     * view.\n     *\n     * @param dir The `FormGroupName` directive instance.\n     */\n\n\n    removeFormGroup(dir) {\n      this._cleanUpFormContainer(dir);\n    }\n    /**\n     * @description\n     * Retrieves the `FormGroup` for a provided `FormGroupName` directive instance\n     *\n     * @param dir The `FormGroupName` directive instance.\n     */\n\n\n    getFormGroup(dir) {\n      return this.form.get(dir.path);\n    }\n    /**\n     * Performs the necessary setup when a `FormArrayName` directive instance is added to the view.\n     *\n     * @param dir The `FormArrayName` directive instance.\n     */\n\n\n    addFormArray(dir) {\n      this._setUpFormContainer(dir);\n    }\n    /**\n     * Performs the necessary cleanup when a `FormArrayName` directive instance is removed from the\n     * view.\n     *\n     * @param dir The `FormArrayName` directive instance.\n     */\n\n\n    removeFormArray(dir) {\n      this._cleanUpFormContainer(dir);\n    }\n    /**\n     * @description\n     * Retrieves the `FormArray` for a provided `FormArrayName` directive instance.\n     *\n     * @param dir The `FormArrayName` directive instance.\n     */\n\n\n    getFormArray(dir) {\n      return this.form.get(dir.path);\n    }\n    /**\n     * Sets the new value for the provided `FormControlName` directive.\n     *\n     * @param dir The `FormControlName` directive instance.\n     * @param value The new value for the directive's control.\n     */\n\n\n    updateModel(dir, value) {\n      const ctrl = this.form.get(dir.path);\n      ctrl.setValue(value);\n    }\n    /**\n     * @description\n     * Method called with the \"submit\" event is triggered on the form.\n     * Triggers the `ngSubmit` emitter to emit the \"submit\" event as its payload.\n     *\n     * @param $event The \"submit\" event object\n     */\n\n\n    onSubmit($event) {\n      this.submitted = true;\n      syncPendingControls(this.form, this.directives);\n      this.ngSubmit.emit($event);\n      return false;\n    }\n    /**\n     * @description\n     * Method called when the \"reset\" event is triggered on the form.\n     */\n\n\n    onReset() {\n      this.resetForm();\n    }\n    /**\n     * @description\n     * Resets the form to an initial value and resets its submitted status.\n     *\n     * @param value The new value for the form.\n     */\n\n\n    resetForm(value = undefined) {\n      this.form.reset(value);\n      this.submitted = false;\n    }\n    /** @internal */\n\n\n    _updateDomValue() {\n      this.directives.forEach(dir => {\n        const oldCtrl = dir.control;\n        const newCtrl = this.form.get(dir.path);\n\n        if (oldCtrl !== newCtrl) {\n          // Note: the value of the `dir.control` may not be defined, for example when it's a first\n          // `FormControl` that is added to a `FormGroup` instance (via `addControl` call).\n          cleanUpControl(oldCtrl || null, dir); // Check whether new control at the same location inside the corresponding `FormGroup` is an\n          // instance of `FormControl` and perform control setup only if that's the case.\n          // Note: we don't need to clear the list of directives (`this.directives`) here, it would be\n          // taken care of in the `removeControl` method invoked when corresponding `formControlName`\n          // directive instance is being removed (invoked from `FormControlName.ngOnDestroy`).\n\n          if (isFormControl(newCtrl)) {\n            setUpControl(newCtrl, dir);\n            dir.control = newCtrl;\n          }\n        }\n      });\n\n      this.form._updateTreeValidity({\n        emitEvent: false\n      });\n    }\n\n    _setUpFormContainer(dir) {\n      const ctrl = this.form.get(dir.path);\n      setUpFormContainer(ctrl, dir); // NOTE: this operation looks unnecessary in case no new validators were added in\n      // `setUpFormContainer` call. Consider updating this code to match the logic in\n      // `_cleanUpFormContainer` function.\n\n      ctrl.updateValueAndValidity({\n        emitEvent: false\n      });\n    }\n\n    _cleanUpFormContainer(dir) {\n      if (this.form) {\n        const ctrl = this.form.get(dir.path);\n\n        if (ctrl) {\n          const isControlUpdated = cleanUpFormContainer(ctrl, dir);\n\n          if (isControlUpdated) {\n            // Run validity check only in case a control was updated (i.e. view validators were\n            // removed) as removing view validators might cause validity to change.\n            ctrl.updateValueAndValidity({\n              emitEvent: false\n            });\n          }\n        }\n      }\n    }\n\n    _updateRegistrations() {\n      this.form._registerOnCollectionChange(this._onCollectionChange);\n\n      if (this._oldForm) {\n        this._oldForm._registerOnCollectionChange(() => {});\n      }\n    }\n\n    _updateValidators() {\n      setUpValidators(this.form, this);\n\n      if (this._oldForm) {\n        cleanUpValidators(this._oldForm, this);\n      }\n    }\n\n    _checkFormPresent() {\n      if (!this.form && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw missingFormException();\n      }\n    }\n\n  }\n\n  FormGroupDirective.ɵfac = function FormGroupDirective_Factory(t) {\n    return new (t || FormGroupDirective)(i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10));\n  };\n\n  FormGroupDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FormGroupDirective,\n    selectors: [[\"\", \"formGroup\", \"\"]],\n    hostBindings: function FormGroupDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"submit\", function FormGroupDirective_submit_HostBindingHandler($event) {\n          return ctx.onSubmit($event);\n        })(\"reset\", function FormGroupDirective_reset_HostBindingHandler() {\n          return ctx.onReset();\n        });\n      }\n    },\n    inputs: {\n      form: [\"formGroup\", \"form\"]\n    },\n    outputs: {\n      ngSubmit: \"ngSubmit\"\n    },\n    exportAs: [\"ngForm\"],\n    features: [i0.ɵɵProvidersFeature([formDirectiveProvider]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n  return FormGroupDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst formGroupNameProvider = {\n  provide: ControlContainer,\n  useExisting: /*#__PURE__*/forwardRef(() => FormGroupName)\n};\n/**\n * @description\n *\n * Syncs a nested `FormGroup` to a DOM element.\n *\n * This directive can only be used with a parent `FormGroupDirective`.\n *\n * It accepts the string name of the nested `FormGroup` to link, and\n * looks for a `FormGroup` registered with that name in the parent\n * `FormGroup` instance you passed into `FormGroupDirective`.\n *\n * Use nested form groups to validate a sub-group of a\n * form separately from the rest or to group the values of certain\n * controls into their own nested object.\n *\n * @see [Reactive Forms Guide](guide/reactive-forms)\n *\n * @usageNotes\n *\n * ### Access the group by name\n *\n * The following example uses the {@link AbstractControl#get get} method to access the\n * associated `FormGroup`\n *\n * ```ts\n *   this.form.get('name');\n * ```\n *\n * ### Access individual controls in the group\n *\n * The following example uses the {@link AbstractControl#get get} method to access\n * individual controls within the group using dot syntax.\n *\n * ```ts\n *   this.form.get('name.first');\n * ```\n *\n * ### Register a nested `FormGroup`.\n *\n * The following example registers a nested *name* `FormGroup` within an existing `FormGroup`,\n * and provides methods to retrieve the nested `FormGroup` and individual controls.\n *\n * {@example forms/ts/nestedFormGroup/nested_form_group_example.ts region='Component'}\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\n\nlet FormGroupName = /*#__PURE__*/(() => {\n  class FormGroupName extends AbstractFormGroupDirective {\n    constructor(parent, validators, asyncValidators) {\n      super();\n      this._parent = parent;\n\n      this._setValidators(validators);\n\n      this._setAsyncValidators(asyncValidators);\n    }\n    /** @internal */\n\n\n    _checkParentType() {\n      if (_hasInvalidParent(this._parent) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw groupParentException();\n      }\n    }\n\n  }\n\n  FormGroupName.ɵfac = function FormGroupName_Factory(t) {\n    return new (t || FormGroupName)(i0.ɵɵdirectiveInject(ControlContainer, 13), i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10));\n  };\n\n  FormGroupName.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FormGroupName,\n    selectors: [[\"\", \"formGroupName\", \"\"]],\n    inputs: {\n      name: [\"formGroupName\", \"name\"]\n    },\n    features: [i0.ɵɵProvidersFeature([formGroupNameProvider]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return FormGroupName;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst formArrayNameProvider = {\n  provide: ControlContainer,\n  useExisting: /*#__PURE__*/forwardRef(() => FormArrayName)\n};\n/**\n * @description\n *\n * Syncs a nested `FormArray` to a DOM element.\n *\n * This directive is designed to be used with a parent `FormGroupDirective` (selector:\n * `[formGroup]`).\n *\n * It accepts the string name of the nested `FormArray` you want to link, and\n * will look for a `FormArray` registered with that name in the parent\n * `FormGroup` instance you passed into `FormGroupDirective`.\n *\n * @see [Reactive Forms Guide](guide/reactive-forms)\n * @see `AbstractControl`\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example forms/ts/nestedFormArray/nested_form_array_example.ts region='Component'}\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\n\nlet FormArrayName = /*#__PURE__*/(() => {\n  class FormArrayName extends ControlContainer {\n    constructor(parent, validators, asyncValidators) {\n      super();\n      this._parent = parent;\n\n      this._setValidators(validators);\n\n      this._setAsyncValidators(asyncValidators);\n    }\n    /**\n     * A lifecycle method called when the directive's inputs are initialized. For internal use only.\n     * @throws If the directive does not have a valid parent.\n     * @nodoc\n     */\n\n\n    ngOnInit() {\n      this._checkParentType();\n\n      this.formDirective.addFormArray(this);\n    }\n    /**\n     * A lifecycle method called before the directive's instance is destroyed. For internal use only.\n     * @nodoc\n     */\n\n\n    ngOnDestroy() {\n      if (this.formDirective) {\n        this.formDirective.removeFormArray(this);\n      }\n    }\n    /**\n     * @description\n     * The `FormArray` bound to this directive.\n     */\n\n\n    get control() {\n      return this.formDirective.getFormArray(this);\n    }\n    /**\n     * @description\n     * The top-level directive for this group if present, otherwise null.\n     */\n\n\n    get formDirective() {\n      return this._parent ? this._parent.formDirective : null;\n    }\n    /**\n     * @description\n     * Returns an array that represents the path from the top-level form to this control.\n     * Each index is the string name of the control on that level.\n     */\n\n\n    get path() {\n      return controlPath(this.name == null ? this.name : this.name.toString(), this._parent);\n    }\n\n    _checkParentType() {\n      if (_hasInvalidParent(this._parent) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw arrayParentException();\n      }\n    }\n\n  }\n\n  FormArrayName.ɵfac = function FormArrayName_Factory(t) {\n    return new (t || FormArrayName)(i0.ɵɵdirectiveInject(ControlContainer, 13), i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10));\n  };\n\n  FormArrayName.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FormArrayName,\n    selectors: [[\"\", \"formArrayName\", \"\"]],\n    inputs: {\n      name: [\"formArrayName\", \"name\"]\n    },\n    features: [i0.ɵɵProvidersFeature([formArrayNameProvider]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return FormArrayName;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction _hasInvalidParent(parent) {\n  return !(parent instanceof FormGroupName) && !(parent instanceof FormGroupDirective) && !(parent instanceof FormArrayName);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst controlNameBinding = {\n  provide: NgControl,\n  useExisting: /*#__PURE__*/forwardRef(() => FormControlName)\n};\n/**\n * @description\n * Syncs a `FormControl` in an existing `FormGroup` to a form control\n * element by name.\n *\n * @see [Reactive Forms Guide](guide/reactive-forms)\n * @see `FormControl`\n * @see `AbstractControl`\n *\n * @usageNotes\n *\n * ### Register `FormControl` within a group\n *\n * The following example shows how to register multiple form controls within a form group\n * and set their value.\n *\n * {@example forms/ts/simpleFormGroup/simple_form_group_example.ts region='Component'}\n *\n * To see `formControlName` examples with different form control types, see:\n *\n * * Radio buttons: `RadioControlValueAccessor`\n * * Selects: `SelectControlValueAccessor`\n *\n * ### Use with ngModel is deprecated\n *\n * Support for using the `ngModel` input property and `ngModelChange` event with reactive\n * form directives has been deprecated in Angular v6 and is scheduled for removal in\n * a future version of Angular.\n *\n * For details, see [Deprecated features](guide/deprecations#ngmodel-with-reactive-forms).\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\n\nlet FormControlName = /*#__PURE__*/(() => {\n  class FormControlName extends NgControl {\n    constructor(parent, validators, asyncValidators, valueAccessors, _ngModelWarningConfig) {\n      super();\n      this._ngModelWarningConfig = _ngModelWarningConfig;\n      this._added = false;\n      /** @deprecated as of v6 */\n\n      this.update = new EventEmitter();\n      /**\n       * @description\n       * Instance property used to track whether an ngModel warning has been sent out for this\n       * particular FormControlName instance. Used to support warning config of \"always\".\n       *\n       * @internal\n       */\n\n      this._ngModelWarningSent = false;\n      this._parent = parent;\n\n      this._setValidators(validators);\n\n      this._setAsyncValidators(asyncValidators);\n\n      this.valueAccessor = selectValueAccessor(this, valueAccessors);\n    }\n    /**\n     * @description\n     * Triggers a warning in dev mode that this input should not be used with reactive forms.\n     */\n\n\n    set isDisabled(isDisabled) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        console.warn(disabledAttrWarning);\n      }\n    }\n    /** @nodoc */\n\n\n    ngOnChanges(changes) {\n      if (!this._added) this._setUpControl();\n\n      if (isPropertyUpdated(changes, this.viewModel)) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          _ngModelWarning('formControlName', FormControlName, this, this._ngModelWarningConfig);\n        }\n\n        this.viewModel = this.model;\n        this.formDirective.updateModel(this, this.model);\n      }\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      if (this.formDirective) {\n        this.formDirective.removeControl(this);\n      }\n    }\n    /**\n     * @description\n     * Sets the new value for the view model and emits an `ngModelChange` event.\n     *\n     * @param newValue The new value for the view model.\n     */\n\n\n    viewToModelUpdate(newValue) {\n      this.viewModel = newValue;\n      this.update.emit(newValue);\n    }\n    /**\n     * @description\n     * Returns an array that represents the path from the top-level form to this control.\n     * Each index is the string name of the control on that level.\n     */\n\n\n    get path() {\n      return controlPath(this.name == null ? this.name : this.name.toString(), this._parent);\n    }\n    /**\n     * @description\n     * The top-level directive for this group if present, otherwise null.\n     */\n\n\n    get formDirective() {\n      return this._parent ? this._parent.formDirective : null;\n    }\n\n    _checkParentType() {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (!(this._parent instanceof FormGroupName) && this._parent instanceof AbstractFormGroupDirective) {\n          throw ngModelGroupException();\n        } else if (!(this._parent instanceof FormGroupName) && !(this._parent instanceof FormGroupDirective) && !(this._parent instanceof FormArrayName)) {\n          throw controlParentException();\n        }\n      }\n    }\n\n    _setUpControl() {\n      this._checkParentType();\n\n      this.control = this.formDirective.addControl(this);\n\n      if (this.control.disabled && this.valueAccessor.setDisabledState) {\n        this.valueAccessor.setDisabledState(true);\n      }\n\n      this._added = true;\n    }\n\n  }\n\n  /**\n   * @description\n   * Static property used to track whether any ngModel warnings have been sent across\n   * all instances of FormControlName. Used to support warning config of \"once\".\n   *\n   * @internal\n   */\n  FormControlName._ngModelWarningSentOnce = false;\n\n  FormControlName.ɵfac = function FormControlName_Factory(t) {\n    return new (t || FormControlName)(i0.ɵɵdirectiveInject(ControlContainer, 13), i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_VALUE_ACCESSOR, 10), i0.ɵɵdirectiveInject(NG_MODEL_WITH_FORM_CONTROL_WARNING, 8));\n  };\n\n  FormControlName.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FormControlName,\n    selectors: [[\"\", \"formControlName\", \"\"]],\n    inputs: {\n      name: [\"formControlName\", \"name\"],\n      isDisabled: [\"disabled\", \"isDisabled\"],\n      model: [\"ngModel\", \"model\"]\n    },\n    outputs: {\n      update: \"ngModelChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([controlNameBinding]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n  return FormControlName;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst SELECT_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => SelectControlValueAccessor),\n  multi: true\n};\n\nfunction _buildValueString$1(id, value) {\n  if (id == null) return `${value}`;\n  if (value && typeof value === 'object') value = 'Object';\n  return `${id}: ${value}`.slice(0, 50);\n}\n\nfunction _extractId$1(valueString) {\n  return valueString.split(':')[0];\n}\n/**\n * @description\n * The `ControlValueAccessor` for writing select control values and listening to select control\n * changes. The value accessor is used by the `FormControlDirective`, `FormControlName`, and\n * `NgModel` directives.\n *\n * @usageNotes\n *\n * ### Using select controls in a reactive form\n *\n * The following examples show how to use a select control in a reactive form.\n *\n * {@example forms/ts/reactiveSelectControl/reactive_select_control_example.ts region='Component'}\n *\n * ### Using select controls in a template-driven form\n *\n * To use a select in a template-driven form, simply add an `ngModel` and a `name`\n * attribute to the main `<select>` tag.\n *\n * {@example forms/ts/selectControl/select_control_example.ts region='Component'}\n *\n * ### Customizing option selection\n *\n * Angular uses object identity to select option. It's possible for the identities of items\n * to change while the data does not. This can happen, for example, if the items are produced\n * from an RPC to the server, and that RPC is re-run. Even if the data hasn't changed, the\n * second response will produce objects with different identities.\n *\n * To customize the default option comparison algorithm, `<select>` supports `compareWith` input.\n * `compareWith` takes a **function** which has two arguments: `option1` and `option2`.\n * If `compareWith` is given, Angular selects option by the return value of the function.\n *\n * ```ts\n * const selectedCountriesControl = new FormControl();\n * ```\n *\n * ```\n * <select [compareWith]=\"compareFn\"  [formControl]=\"selectedCountriesControl\">\n *     <option *ngFor=\"let country of countries\" [ngValue]=\"country\">\n *         {{country.name}}\n *     </option>\n * </select>\n *\n * compareFn(c1: Country, c2: Country): boolean {\n *     return c1 && c2 ? c1.id === c2.id : c1 === c2;\n * }\n * ```\n *\n * **Note:** We listen to the 'change' event because 'input' events aren't fired\n * for selects in IE, see:\n * https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/input_event#browser_compatibility\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\n\nlet SelectControlValueAccessor = /*#__PURE__*/(() => {\n  class SelectControlValueAccessor extends BuiltInControlValueAccessor {\n    constructor() {\n      super(...arguments);\n      /** @internal */\n\n      this._optionMap = new Map();\n      /** @internal */\n\n      this._idCounter = 0;\n      this._compareWith = Object.is;\n    }\n    /**\n     * @description\n     * Tracks the option comparison algorithm for tracking identities when\n     * checking for changes.\n     */\n\n\n    set compareWith(fn) {\n      if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error(`compareWith must be a function, but received ${JSON.stringify(fn)}`);\n      }\n\n      this._compareWith = fn;\n    }\n    /**\n     * Sets the \"value\" property on the input element. The \"selectedIndex\"\n     * property is also set if an ID is provided on the option element.\n     * @nodoc\n     */\n\n\n    writeValue(value) {\n      this.value = value;\n\n      const id = this._getOptionId(value);\n\n      if (id == null) {\n        this.setProperty('selectedIndex', -1);\n      }\n\n      const valueString = _buildValueString$1(id, value);\n\n      this.setProperty('value', valueString);\n    }\n    /**\n     * Registers a function called when the control value changes.\n     * @nodoc\n     */\n\n\n    registerOnChange(fn) {\n      this.onChange = valueString => {\n        this.value = this._getOptionValue(valueString);\n        fn(this.value);\n      };\n    }\n    /** @internal */\n\n\n    _registerOption() {\n      return (this._idCounter++).toString();\n    }\n    /** @internal */\n\n\n    _getOptionId(value) {\n      for (const id of Array.from(this._optionMap.keys())) {\n        if (this._compareWith(this._optionMap.get(id), value)) return id;\n      }\n\n      return null;\n    }\n    /** @internal */\n\n\n    _getOptionValue(valueString) {\n      const id = _extractId$1(valueString);\n\n      return this._optionMap.has(id) ? this._optionMap.get(id) : valueString;\n    }\n\n  }\n\n  SelectControlValueAccessor.ɵfac = /* @__PURE__ */function () {\n    let ɵSelectControlValueAccessor_BaseFactory;\n    return function SelectControlValueAccessor_Factory(t) {\n      return (ɵSelectControlValueAccessor_BaseFactory || (ɵSelectControlValueAccessor_BaseFactory = i0.ɵɵgetInheritedFactory(SelectControlValueAccessor)))(t || SelectControlValueAccessor);\n    };\n  }();\n\n  SelectControlValueAccessor.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: SelectControlValueAccessor,\n    selectors: [[\"select\", \"formControlName\", \"\", 3, \"multiple\", \"\"], [\"select\", \"formControl\", \"\", 3, \"multiple\", \"\"], [\"select\", \"ngModel\", \"\", 3, \"multiple\", \"\"]],\n    hostBindings: function SelectControlValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"change\", function SelectControlValueAccessor_change_HostBindingHandler($event) {\n          return ctx.onChange($event.target.value);\n        })(\"blur\", function SelectControlValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        });\n      }\n    },\n    inputs: {\n      compareWith: \"compareWith\"\n    },\n    features: [i0.ɵɵProvidersFeature([SELECT_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return SelectControlValueAccessor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n * Marks `<option>` as dynamic, so Angular can be notified when options change.\n *\n * @see `SelectControlValueAccessor`\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\n\nlet NgSelectOption = /*#__PURE__*/(() => {\n  class NgSelectOption {\n    constructor(_element, _renderer, _select) {\n      this._element = _element;\n      this._renderer = _renderer;\n      this._select = _select;\n      if (this._select) this.id = this._select._registerOption();\n    }\n    /**\n     * @description\n     * Tracks the value bound to the option element. Unlike the value binding,\n     * ngValue supports binding to objects.\n     */\n\n\n    set ngValue(value) {\n      if (this._select == null) return;\n\n      this._select._optionMap.set(this.id, value);\n\n      this._setElementValue(_buildValueString$1(this.id, value));\n\n      this._select.writeValue(this._select.value);\n    }\n    /**\n     * @description\n     * Tracks simple string values bound to the option element.\n     * For objects, use the `ngValue` input binding.\n     */\n\n\n    set value(value) {\n      this._setElementValue(value);\n\n      if (this._select) this._select.writeValue(this._select.value);\n    }\n    /** @internal */\n\n\n    _setElementValue(value) {\n      this._renderer.setProperty(this._element.nativeElement, 'value', value);\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      if (this._select) {\n        this._select._optionMap.delete(this.id);\n\n        this._select.writeValue(this._select.value);\n      }\n    }\n\n  }\n\n  NgSelectOption.ɵfac = function NgSelectOption_Factory(t) {\n    return new (t || NgSelectOption)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(SelectControlValueAccessor, 9));\n  };\n\n  NgSelectOption.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgSelectOption,\n    selectors: [[\"option\"]],\n    inputs: {\n      ngValue: \"ngValue\",\n      value: \"value\"\n    }\n  });\n  return NgSelectOption;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst SELECT_MULTIPLE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => SelectMultipleControlValueAccessor),\n  multi: true\n};\n\nfunction _buildValueString(id, value) {\n  if (id == null) return `${value}`;\n  if (typeof value === 'string') value = `'${value}'`;\n  if (value && typeof value === 'object') value = 'Object';\n  return `${id}: ${value}`.slice(0, 50);\n}\n\nfunction _extractId(valueString) {\n  return valueString.split(':')[0];\n}\n/** Mock interface for HTMLCollection */\n\n\nclass HTMLCollection {}\n/**\n * @description\n * The `ControlValueAccessor` for writing multi-select control values and listening to multi-select\n * control changes. The value accessor is used by the `FormControlDirective`, `FormControlName`, and\n * `NgModel` directives.\n *\n * @see `SelectControlValueAccessor`\n *\n * @usageNotes\n *\n * ### Using a multi-select control\n *\n * The follow example shows you how to use a multi-select control with a reactive form.\n *\n * ```ts\n * const countryControl = new FormControl();\n * ```\n *\n * ```\n * <select multiple name=\"countries\" [formControl]=\"countryControl\">\n *   <option *ngFor=\"let country of countries\" [ngValue]=\"country\">\n *     {{ country.name }}\n *   </option>\n * </select>\n * ```\n *\n * ### Customizing option selection\n *\n * To customize the default option comparison algorithm, `<select>` supports `compareWith` input.\n * See the `SelectControlValueAccessor` for usage.\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\n\nlet SelectMultipleControlValueAccessor = /*#__PURE__*/(() => {\n  class SelectMultipleControlValueAccessor extends BuiltInControlValueAccessor {\n    constructor() {\n      super(...arguments);\n      /** @internal */\n\n      this._optionMap = new Map();\n      /** @internal */\n\n      this._idCounter = 0;\n      this._compareWith = Object.is;\n    }\n    /**\n     * @description\n     * Tracks the option comparison algorithm for tracking identities when\n     * checking for changes.\n     */\n\n\n    set compareWith(fn) {\n      if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error(`compareWith must be a function, but received ${JSON.stringify(fn)}`);\n      }\n\n      this._compareWith = fn;\n    }\n    /**\n     * Sets the \"value\" property on one or of more of the select's options.\n     * @nodoc\n     */\n\n\n    writeValue(value) {\n      this.value = value;\n      let optionSelectedStateSetter;\n\n      if (Array.isArray(value)) {\n        // convert values to ids\n        const ids = value.map(v => this._getOptionId(v));\n\n        optionSelectedStateSetter = (opt, o) => {\n          opt._setSelected(ids.indexOf(o.toString()) > -1);\n        };\n      } else {\n        optionSelectedStateSetter = (opt, o) => {\n          opt._setSelected(false);\n        };\n      }\n\n      this._optionMap.forEach(optionSelectedStateSetter);\n    }\n    /**\n     * Registers a function called when the control value changes\n     * and writes an array of the selected options.\n     * @nodoc\n     */\n\n\n    registerOnChange(fn) {\n      this.onChange = element => {\n        const selected = [];\n        const selectedOptions = element.selectedOptions;\n\n        if (selectedOptions !== undefined) {\n          const options = selectedOptions;\n\n          for (let i = 0; i < options.length; i++) {\n            const opt = options[i];\n\n            const val = this._getOptionValue(opt.value);\n\n            selected.push(val);\n          }\n        } // Degrade to use `options` when `selectedOptions` property is not available.\n        // Note: the `selectedOptions` is available in all supported browsers, but the Domino lib\n        // doesn't have it currently, see https://github.com/fgnass/domino/issues/177.\n        else {\n          const options = element.options;\n\n          for (let i = 0; i < options.length; i++) {\n            const opt = options[i];\n\n            if (opt.selected) {\n              const val = this._getOptionValue(opt.value);\n\n              selected.push(val);\n            }\n          }\n        }\n\n        this.value = selected;\n        fn(selected);\n      };\n    }\n    /** @internal */\n\n\n    _registerOption(value) {\n      const id = (this._idCounter++).toString();\n\n      this._optionMap.set(id, value);\n\n      return id;\n    }\n    /** @internal */\n\n\n    _getOptionId(value) {\n      for (const id of Array.from(this._optionMap.keys())) {\n        if (this._compareWith(this._optionMap.get(id)._value, value)) return id;\n      }\n\n      return null;\n    }\n    /** @internal */\n\n\n    _getOptionValue(valueString) {\n      const id = _extractId(valueString);\n\n      return this._optionMap.has(id) ? this._optionMap.get(id)._value : valueString;\n    }\n\n  }\n\n  SelectMultipleControlValueAccessor.ɵfac = /* @__PURE__ */function () {\n    let ɵSelectMultipleControlValueAccessor_BaseFactory;\n    return function SelectMultipleControlValueAccessor_Factory(t) {\n      return (ɵSelectMultipleControlValueAccessor_BaseFactory || (ɵSelectMultipleControlValueAccessor_BaseFactory = i0.ɵɵgetInheritedFactory(SelectMultipleControlValueAccessor)))(t || SelectMultipleControlValueAccessor);\n    };\n  }();\n\n  SelectMultipleControlValueAccessor.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: SelectMultipleControlValueAccessor,\n    selectors: [[\"select\", \"multiple\", \"\", \"formControlName\", \"\"], [\"select\", \"multiple\", \"\", \"formControl\", \"\"], [\"select\", \"multiple\", \"\", \"ngModel\", \"\"]],\n    hostBindings: function SelectMultipleControlValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"change\", function SelectMultipleControlValueAccessor_change_HostBindingHandler($event) {\n          return ctx.onChange($event.target);\n        })(\"blur\", function SelectMultipleControlValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        });\n      }\n    },\n    inputs: {\n      compareWith: \"compareWith\"\n    },\n    features: [i0.ɵɵProvidersFeature([SELECT_MULTIPLE_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return SelectMultipleControlValueAccessor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n * Marks `<option>` as dynamic, so Angular can be notified when options change.\n *\n * @see `SelectMultipleControlValueAccessor`\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\n\nlet ɵNgSelectMultipleOption = /*#__PURE__*/(() => {\n  class ɵNgSelectMultipleOption {\n    constructor(_element, _renderer, _select) {\n      this._element = _element;\n      this._renderer = _renderer;\n      this._select = _select;\n\n      if (this._select) {\n        this.id = this._select._registerOption(this);\n      }\n    }\n    /**\n     * @description\n     * Tracks the value bound to the option element. Unlike the value binding,\n     * ngValue supports binding to objects.\n     */\n\n\n    set ngValue(value) {\n      if (this._select == null) return;\n      this._value = value;\n\n      this._setElementValue(_buildValueString(this.id, value));\n\n      this._select.writeValue(this._select.value);\n    }\n    /**\n     * @description\n     * Tracks simple string values bound to the option element.\n     * For objects, use the `ngValue` input binding.\n     */\n\n\n    set value(value) {\n      if (this._select) {\n        this._value = value;\n\n        this._setElementValue(_buildValueString(this.id, value));\n\n        this._select.writeValue(this._select.value);\n      } else {\n        this._setElementValue(value);\n      }\n    }\n    /** @internal */\n\n\n    _setElementValue(value) {\n      this._renderer.setProperty(this._element.nativeElement, 'value', value);\n    }\n    /** @internal */\n\n\n    _setSelected(selected) {\n      this._renderer.setProperty(this._element.nativeElement, 'selected', selected);\n    }\n    /** @nodoc */\n\n\n    ngOnDestroy() {\n      if (this._select) {\n        this._select._optionMap.delete(this.id);\n\n        this._select.writeValue(this._select.value);\n      }\n    }\n\n  }\n\n  ɵNgSelectMultipleOption.ɵfac = function ɵNgSelectMultipleOption_Factory(t) {\n    return new (t || ɵNgSelectMultipleOption)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(SelectMultipleControlValueAccessor, 9));\n  };\n\n  ɵNgSelectMultipleOption.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ɵNgSelectMultipleOption,\n    selectors: [[\"option\"]],\n    inputs: {\n      ngValue: \"ngValue\",\n      value: \"value\"\n    }\n  });\n  return ɵNgSelectMultipleOption;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Method that updates string to integer if not already a number\n *\n * @param value The value to convert to integer\n * @returns value of parameter in number or integer.\n */\n\n\nfunction toInteger(value) {\n  return typeof value === 'number' ? value : parseInt(value, 10);\n}\n/**\n * Method that ensures that provided value is a float (and converts it to float if needed).\n *\n * @param value The value to convert to float\n * @returns value of parameter in number or float.\n */\n\n\nfunction toFloat(value) {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n/**\n * A base class for Validator-based Directives. The class contains common logic shared across such\n * Directives.\n *\n * For internal use only, this class is not intended for use outside of the Forms package.\n */\n\n\nlet AbstractValidatorDirective = /*#__PURE__*/(() => {\n  class AbstractValidatorDirective {\n    constructor() {\n      this._validator = nullValidator;\n    }\n    /** @nodoc */\n\n\n    ngOnChanges(changes) {\n      if (this.inputName in changes) {\n        const input = this.normalizeInput(changes[this.inputName].currentValue);\n        this._enabled = this.enabled(input);\n        this._validator = this._enabled ? this.createValidator(input) : nullValidator;\n\n        if (this._onChange) {\n          this._onChange();\n        }\n      }\n    }\n    /** @nodoc */\n\n\n    validate(control) {\n      return this._validator(control);\n    }\n    /** @nodoc */\n\n\n    registerOnValidatorChange(fn) {\n      this._onChange = fn;\n    }\n    /**\n     * @description\n     * Determines whether this validator should be active or not based on an input.\n     * Base class implementation checks whether an input is defined (if the value is different from\n     * `null` and `undefined`). Validator classes that extend this base class can override this\n     * function with the logic specific to a particular validator directive.\n     */\n\n\n    enabled(input) {\n      return input != null\n      /* both `null` and `undefined` */\n      ;\n    }\n\n  }\n\n  AbstractValidatorDirective.ɵfac = function AbstractValidatorDirective_Factory(t) {\n    return new (t || AbstractValidatorDirective)();\n  };\n\n  AbstractValidatorDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: AbstractValidatorDirective,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return AbstractValidatorDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n * Provider which adds `MaxValidator` to the `NG_VALIDATORS` multi-provider list.\n */\n\n\nconst MAX_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: /*#__PURE__*/forwardRef(() => MaxValidator),\n  multi: true\n};\n/**\n * A directive which installs the {@link MaxValidator} for any `formControlName`,\n * `formControl`, or control with `ngModel` that also has a `max` attribute.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a max validator\n *\n * The following example shows how to add a max validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input type=\"number\" ngModel max=\"4\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\nlet MaxValidator = /*#__PURE__*/(() => {\n  class MaxValidator extends AbstractValidatorDirective {\n    constructor() {\n      super(...arguments);\n      /** @internal */\n\n      this.inputName = 'max';\n      /** @internal */\n\n      this.normalizeInput = input => toFloat(input);\n      /** @internal */\n\n\n      this.createValidator = max => maxValidator(max);\n    }\n\n  }\n\n  MaxValidator.ɵfac = /* @__PURE__ */function () {\n    let ɵMaxValidator_BaseFactory;\n    return function MaxValidator_Factory(t) {\n      return (ɵMaxValidator_BaseFactory || (ɵMaxValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MaxValidator)))(t || MaxValidator);\n    };\n  }();\n\n  MaxValidator.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MaxValidator,\n    selectors: [[\"input\", \"type\", \"number\", \"max\", \"\", \"formControlName\", \"\"], [\"input\", \"type\", \"number\", \"max\", \"\", \"formControl\", \"\"], [\"input\", \"type\", \"number\", \"max\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function MaxValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"max\", ctx._enabled ? ctx.max : null);\n      }\n    },\n    inputs: {\n      max: \"max\"\n    },\n    features: [i0.ɵɵProvidersFeature([MAX_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return MaxValidator;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n * Provider which adds `MinValidator` to the `NG_VALIDATORS` multi-provider list.\n */\n\n\nconst MIN_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: /*#__PURE__*/forwardRef(() => MinValidator),\n  multi: true\n};\n/**\n * A directive which installs the {@link MinValidator} for any `formControlName`,\n * `formControl`, or control with `ngModel` that also has a `min` attribute.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a min validator\n *\n * The following example shows how to add a min validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input type=\"number\" ngModel min=\"4\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\nlet MinValidator = /*#__PURE__*/(() => {\n  class MinValidator extends AbstractValidatorDirective {\n    constructor() {\n      super(...arguments);\n      /** @internal */\n\n      this.inputName = 'min';\n      /** @internal */\n\n      this.normalizeInput = input => toFloat(input);\n      /** @internal */\n\n\n      this.createValidator = min => minValidator(min);\n    }\n\n  }\n\n  MinValidator.ɵfac = /* @__PURE__ */function () {\n    let ɵMinValidator_BaseFactory;\n    return function MinValidator_Factory(t) {\n      return (ɵMinValidator_BaseFactory || (ɵMinValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MinValidator)))(t || MinValidator);\n    };\n  }();\n\n  MinValidator.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MinValidator,\n    selectors: [[\"input\", \"type\", \"number\", \"min\", \"\", \"formControlName\", \"\"], [\"input\", \"type\", \"number\", \"min\", \"\", \"formControl\", \"\"], [\"input\", \"type\", \"number\", \"min\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function MinValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"min\", ctx._enabled ? ctx.min : null);\n      }\n    },\n    inputs: {\n      min: \"min\"\n    },\n    features: [i0.ɵɵProvidersFeature([MIN_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return MinValidator;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n * Provider which adds `RequiredValidator` to the `NG_VALIDATORS` multi-provider list.\n */\n\n\nconst REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: /*#__PURE__*/forwardRef(() => RequiredValidator),\n  multi: true\n};\n/**\n * @description\n * Provider which adds `CheckboxRequiredValidator` to the `NG_VALIDATORS` multi-provider list.\n */\n\nconst CHECKBOX_REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: /*#__PURE__*/forwardRef(() => CheckboxRequiredValidator),\n  multi: true\n};\n/**\n * @description\n * A directive that adds the `required` validator to any controls marked with the\n * `required` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a required validator using template-driven forms\n *\n * ```\n * <input name=\"fullName\" ngModel required>\n * ```\n *\n * @ngModule FormsModule\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\n\nlet RequiredValidator = /*#__PURE__*/(() => {\n  class RequiredValidator {\n    constructor() {\n      this._required = false;\n    }\n    /**\n     * @description\n     * Tracks changes to the required attribute bound to this directive.\n     */\n\n\n    get required() {\n      return this._required;\n    }\n\n    set required(value) {\n      this._required = value != null && value !== false && `${value}` !== 'false';\n      if (this._onChange) this._onChange();\n    }\n    /**\n     * Method that validates whether the control is empty.\n     * Returns the validation result if enabled, otherwise null.\n     * @nodoc\n     */\n\n\n    validate(control) {\n      return this.required ? requiredValidator(control) : null;\n    }\n    /**\n     * Registers a callback function to call when the validator inputs change.\n     * @nodoc\n     */\n\n\n    registerOnValidatorChange(fn) {\n      this._onChange = fn;\n    }\n\n  }\n\n  RequiredValidator.ɵfac = function RequiredValidator_Factory(t) {\n    return new (t || RequiredValidator)();\n  };\n\n  RequiredValidator.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RequiredValidator,\n    selectors: [[\"\", \"required\", \"\", \"formControlName\", \"\", 3, \"type\", \"checkbox\"], [\"\", \"required\", \"\", \"formControl\", \"\", 3, \"type\", \"checkbox\"], [\"\", \"required\", \"\", \"ngModel\", \"\", 3, \"type\", \"checkbox\"]],\n    hostVars: 1,\n    hostBindings: function RequiredValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"required\", ctx.required ? \"\" : null);\n      }\n    },\n    inputs: {\n      required: \"required\"\n    },\n    features: [i0.ɵɵProvidersFeature([REQUIRED_VALIDATOR])]\n  });\n  return RequiredValidator;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * A Directive that adds the `required` validator to checkbox controls marked with the\n * `required` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a required checkbox validator using template-driven forms\n *\n * The following example shows how to add a checkbox required validator to an input attached to an\n * ngModel binding.\n *\n * ```\n * <input type=\"checkbox\" name=\"active\" ngModel required>\n * ```\n *\n * @publicApi\n * @ngModule FormsModule\n * @ngModule ReactiveFormsModule\n */\n\n\nlet CheckboxRequiredValidator = /*#__PURE__*/(() => {\n  class CheckboxRequiredValidator extends RequiredValidator {\n    /**\n     * Method that validates whether or not the checkbox has been checked.\n     * Returns the validation result if enabled, otherwise null.\n     * @nodoc\n     */\n    validate(control) {\n      return this.required ? requiredTrueValidator(control) : null;\n    }\n\n  }\n\n  CheckboxRequiredValidator.ɵfac = /* @__PURE__ */function () {\n    let ɵCheckboxRequiredValidator_BaseFactory;\n    return function CheckboxRequiredValidator_Factory(t) {\n      return (ɵCheckboxRequiredValidator_BaseFactory || (ɵCheckboxRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(CheckboxRequiredValidator)))(t || CheckboxRequiredValidator);\n    };\n  }();\n\n  CheckboxRequiredValidator.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CheckboxRequiredValidator,\n    selectors: [[\"input\", \"type\", \"checkbox\", \"required\", \"\", \"formControlName\", \"\"], [\"input\", \"type\", \"checkbox\", \"required\", \"\", \"formControl\", \"\"], [\"input\", \"type\", \"checkbox\", \"required\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function CheckboxRequiredValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"required\", ctx.required ? \"\" : null);\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([CHECKBOX_REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return CheckboxRequiredValidator;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n * Provider which adds `EmailValidator` to the `NG_VALIDATORS` multi-provider list.\n */\n\n\nconst EMAIL_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: /*#__PURE__*/forwardRef(() => EmailValidator),\n  multi: true\n};\n/**\n * A directive that adds the `email` validator to controls marked with the\n * `email` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding an email validator\n *\n * The following example shows how to add an email validator to an input attached to an ngModel\n * binding.\n *\n * ```\n * <input type=\"email\" name=\"email\" ngModel email>\n * <input type=\"email\" name=\"email\" ngModel email=\"true\">\n * <input type=\"email\" name=\"email\" ngModel [email]=\"true\">\n * ```\n *\n * @publicApi\n * @ngModule FormsModule\n * @ngModule ReactiveFormsModule\n */\n\nlet EmailValidator = /*#__PURE__*/(() => {\n  class EmailValidator extends AbstractValidatorDirective {\n    constructor() {\n      super(...arguments);\n      /** @internal */\n\n      this.inputName = 'email';\n      /** @internal */\n\n      this.normalizeInput = input => // Avoid TSLint requirement to omit semicolon, see\n      // https://github.com/palantir/tslint/issues/1476\n      // tslint:disable-next-line:semicolon\n      input === '' || input === true || input === 'true';\n      /** @internal */\n\n\n      this.createValidator = input => emailValidator;\n    }\n    /** @nodoc */\n\n\n    enabled(input) {\n      return input;\n    }\n\n  }\n\n  EmailValidator.ɵfac = /* @__PURE__ */function () {\n    let ɵEmailValidator_BaseFactory;\n    return function EmailValidator_Factory(t) {\n      return (ɵEmailValidator_BaseFactory || (ɵEmailValidator_BaseFactory = i0.ɵɵgetInheritedFactory(EmailValidator)))(t || EmailValidator);\n    };\n  }();\n\n  EmailValidator.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: EmailValidator,\n    selectors: [[\"\", \"email\", \"\", \"formControlName\", \"\"], [\"\", \"email\", \"\", \"formControl\", \"\"], [\"\", \"email\", \"\", \"ngModel\", \"\"]],\n    inputs: {\n      email: \"email\"\n    },\n    features: [i0.ɵɵProvidersFeature([EMAIL_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return EmailValidator;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n * Provider which adds `MinLengthValidator` to the `NG_VALIDATORS` multi-provider list.\n */\n\n\nconst MIN_LENGTH_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: /*#__PURE__*/forwardRef(() => MinLengthValidator),\n  multi: true\n};\n/**\n * A directive that adds minimum length validation to controls marked with the\n * `minlength` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a minimum length validator\n *\n * The following example shows how to add a minimum length validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input name=\"firstName\" ngModel minlength=\"4\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\nlet MinLengthValidator = /*#__PURE__*/(() => {\n  class MinLengthValidator extends AbstractValidatorDirective {\n    constructor() {\n      super(...arguments);\n      /** @internal */\n\n      this.inputName = 'minlength';\n      /** @internal */\n\n      this.normalizeInput = input => toInteger(input);\n      /** @internal */\n\n\n      this.createValidator = minlength => minLengthValidator(minlength);\n    }\n\n  }\n\n  MinLengthValidator.ɵfac = /* @__PURE__ */function () {\n    let ɵMinLengthValidator_BaseFactory;\n    return function MinLengthValidator_Factory(t) {\n      return (ɵMinLengthValidator_BaseFactory || (ɵMinLengthValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MinLengthValidator)))(t || MinLengthValidator);\n    };\n  }();\n\n  MinLengthValidator.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MinLengthValidator,\n    selectors: [[\"\", \"minlength\", \"\", \"formControlName\", \"\"], [\"\", \"minlength\", \"\", \"formControl\", \"\"], [\"\", \"minlength\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function MinLengthValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"minlength\", ctx._enabled ? ctx.minlength : null);\n      }\n    },\n    inputs: {\n      minlength: \"minlength\"\n    },\n    features: [i0.ɵɵProvidersFeature([MIN_LENGTH_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return MinLengthValidator;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n * Provider which adds `MaxLengthValidator` to the `NG_VALIDATORS` multi-provider list.\n */\n\n\nconst MAX_LENGTH_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: /*#__PURE__*/forwardRef(() => MaxLengthValidator),\n  multi: true\n};\n/**\n * A directive that adds max length validation to controls marked with the\n * `maxlength` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a maximum length validator\n *\n * The following example shows how to add a maximum length validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input name=\"firstName\" ngModel maxlength=\"25\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\nlet MaxLengthValidator = /*#__PURE__*/(() => {\n  class MaxLengthValidator extends AbstractValidatorDirective {\n    constructor() {\n      super(...arguments);\n      /** @internal */\n\n      this.inputName = 'maxlength';\n      /** @internal */\n\n      this.normalizeInput = input => toInteger(input);\n      /** @internal */\n\n\n      this.createValidator = maxlength => maxLengthValidator(maxlength);\n    }\n\n  }\n\n  MaxLengthValidator.ɵfac = /* @__PURE__ */function () {\n    let ɵMaxLengthValidator_BaseFactory;\n    return function MaxLengthValidator_Factory(t) {\n      return (ɵMaxLengthValidator_BaseFactory || (ɵMaxLengthValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MaxLengthValidator)))(t || MaxLengthValidator);\n    };\n  }();\n\n  MaxLengthValidator.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MaxLengthValidator,\n    selectors: [[\"\", \"maxlength\", \"\", \"formControlName\", \"\"], [\"\", \"maxlength\", \"\", \"formControl\", \"\"], [\"\", \"maxlength\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function MaxLengthValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"maxlength\", ctx._enabled ? ctx.maxlength : null);\n      }\n    },\n    inputs: {\n      maxlength: \"maxlength\"\n    },\n    features: [i0.ɵɵProvidersFeature([MAX_LENGTH_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return MaxLengthValidator;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n * Provider which adds `PatternValidator` to the `NG_VALIDATORS` multi-provider list.\n */\n\n\nconst PATTERN_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: /*#__PURE__*/forwardRef(() => PatternValidator),\n  multi: true\n};\n/**\n * @description\n * A directive that adds regex pattern validation to controls marked with the\n * `pattern` attribute. The regex must match the entire control value.\n * The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a pattern validator\n *\n * The following example shows how to add a pattern validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input name=\"firstName\" ngModel pattern=\"[a-zA-Z ]*\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\n\nlet PatternValidator = /*#__PURE__*/(() => {\n  class PatternValidator {\n    constructor() {\n      this._validator = nullValidator;\n    }\n    /** @nodoc */\n\n\n    ngOnChanges(changes) {\n      if ('pattern' in changes) {\n        this._createValidator();\n\n        if (this._onChange) this._onChange();\n      }\n    }\n    /**\n     * Method that validates whether the value matches the pattern requirement.\n     * @nodoc\n     */\n\n\n    validate(control) {\n      return this._validator(control);\n    }\n    /**\n     * Registers a callback function to call when the validator inputs change.\n     * @nodoc\n     */\n\n\n    registerOnValidatorChange(fn) {\n      this._onChange = fn;\n    }\n\n    _createValidator() {\n      this._validator = patternValidator(this.pattern);\n    }\n\n  }\n\n  PatternValidator.ɵfac = function PatternValidator_Factory(t) {\n    return new (t || PatternValidator)();\n  };\n\n  PatternValidator.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: PatternValidator,\n    selectors: [[\"\", \"pattern\", \"\", \"formControlName\", \"\"], [\"\", \"pattern\", \"\", \"formControl\", \"\"], [\"\", \"pattern\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function PatternValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"pattern\", ctx.pattern ? ctx.pattern : null);\n      }\n    },\n    inputs: {\n      pattern: \"pattern\"\n    },\n    features: [i0.ɵɵProvidersFeature([PATTERN_VALIDATOR]), i0.ɵɵNgOnChangesFeature]\n  });\n  return PatternValidator;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst SHARED_FORM_DIRECTIVES = [ɵNgNoValidate, NgSelectOption, ɵNgSelectMultipleOption, DefaultValueAccessor, NumberValueAccessor, RangeValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, SelectMultipleControlValueAccessor, RadioControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, MinLengthValidator, MaxLengthValidator, PatternValidator, CheckboxRequiredValidator, EmailValidator, MinValidator, MaxValidator];\nconst TEMPLATE_DRIVEN_DIRECTIVES = [NgModel, NgModelGroup, NgForm];\nconst REACTIVE_DRIVEN_DIRECTIVES = [FormControlDirective, FormGroupDirective, FormControlName, FormGroupName, FormArrayName];\n/**\n * Internal module used for sharing directives between FormsModule and ReactiveFormsModule\n */\n\nlet ɵInternalFormsSharedModule = /*#__PURE__*/(() => {\n  class ɵInternalFormsSharedModule {}\n\n  ɵInternalFormsSharedModule.ɵfac = function ɵInternalFormsSharedModule_Factory(t) {\n    return new (t || ɵInternalFormsSharedModule)();\n  };\n\n  ɵInternalFormsSharedModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ɵInternalFormsSharedModule\n  });\n  ɵInternalFormsSharedModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[RadioControlRegistryModule]]\n  });\n  return ɵInternalFormsSharedModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Exports the required providers and directives for template-driven forms,\n * making them available for import by NgModules that import this module.\n *\n * Providers associated with this module:\n * * `RadioControlRegistry`\n *\n * @see [Forms Overview](/guide/forms-overview)\n * @see [Template-driven Forms Guide](/guide/forms)\n *\n * @publicApi\n */\n\n\nlet FormsModule = /*#__PURE__*/(() => {\n  class FormsModule {}\n\n  FormsModule.ɵfac = function FormsModule_Factory(t) {\n    return new (t || FormsModule)();\n  };\n\n  FormsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FormsModule\n  });\n  FormsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ɵInternalFormsSharedModule]\n  });\n  return FormsModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Exports the required infrastructure and directives for reactive forms,\n * making them available for import by NgModules that import this module.\n *\n * Providers associated with this module:\n * * `FormBuilder`\n * * `RadioControlRegistry`\n *\n * @see [Forms Overview](guide/forms-overview)\n * @see [Reactive Forms Guide](guide/reactive-forms)\n *\n * @publicApi\n */\n\n\nlet ReactiveFormsModule = /*#__PURE__*/(() => {\n  class ReactiveFormsModule {\n    /**\n     * @description\n     * Provides options for configuring the reactive forms module.\n     *\n     * @param opts An object of configuration options\n     * * `warnOnNgModelWithFormControl` Configures when to emit a warning when an `ngModel`\n     * binding is used with reactive form directives.\n     */\n    static withConfig(opts) {\n      return {\n        ngModule: ReactiveFormsModule,\n        providers: [{\n          provide: NG_MODEL_WITH_FORM_CONTROL_WARNING,\n          useValue: opts.warnOnNgModelWithFormControl\n        }]\n      };\n    }\n\n  }\n\n  ReactiveFormsModule.ɵfac = function ReactiveFormsModule_Factory(t) {\n    return new (t || ReactiveFormsModule)();\n  };\n\n  ReactiveFormsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ReactiveFormsModule\n  });\n  ReactiveFormsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ɵInternalFormsSharedModule]\n  });\n  return ReactiveFormsModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction isAbstractControlOptions(options) {\n  return options.asyncValidators !== undefined || options.validators !== undefined || options.updateOn !== undefined;\n}\n/**\n * @description\n * Creates an `AbstractControl` from a user-specified configuration.\n *\n * The `FormBuilder` provides syntactic sugar that shortens creating instances of a `FormControl`,\n * `FormGroup`, or `FormArray`. It reduces the amount of boilerplate needed to build complex\n * forms.\n *\n * @see [Reactive Forms Guide](/guide/reactive-forms)\n *\n * @publicApi\n */\n\n\nlet FormBuilder = /*#__PURE__*/(() => {\n  class FormBuilder {\n    group(controlsConfig, options = null) {\n      const controls = this._reduceControls(controlsConfig);\n\n      let validators = null;\n      let asyncValidators = null;\n      let updateOn = undefined;\n\n      if (options != null) {\n        if (isAbstractControlOptions(options)) {\n          // `options` are `AbstractControlOptions`\n          validators = options.validators != null ? options.validators : null;\n          asyncValidators = options.asyncValidators != null ? options.asyncValidators : null;\n          updateOn = options.updateOn != null ? options.updateOn : undefined;\n        } else {\n          // `options` are legacy form group options\n          validators = options['validator'] != null ? options['validator'] : null;\n          asyncValidators = options['asyncValidator'] != null ? options['asyncValidator'] : null;\n        }\n      }\n\n      return new FormGroup(controls, {\n        asyncValidators,\n        updateOn,\n        validators\n      });\n    }\n    /**\n     * @description\n     * Construct a new `FormControl` with the given state, validators and options.\n     *\n     * @param formState Initializes the control with an initial state value, or\n     * with an object that contains both a value and a disabled status.\n     *\n     * @param validatorOrOpts A synchronous validator function, or an array of\n     * such functions, or an `AbstractControlOptions` object that contains\n     * validation functions and a validation trigger.\n     *\n     * @param asyncValidator A single async validator or array of async validator\n     * functions.\n     *\n     * @usageNotes\n     *\n     * ### Initialize a control as disabled\n     *\n     * The following example returns a control with an initial value in a disabled state.\n     *\n     * <code-example path=\"forms/ts/formBuilder/form_builder_example.ts\" region=\"disabled-control\">\n     * </code-example>\n     */\n\n\n    control(formState, validatorOrOpts, asyncValidator) {\n      return new FormControl(formState, validatorOrOpts, asyncValidator);\n    }\n    /**\n     * Constructs a new `FormArray` from the given array of configurations,\n     * validators and options.\n     *\n     * @param controlsConfig An array of child controls or control configs. Each\n     * child control is given an index when it is registered.\n     *\n     * @param validatorOrOpts A synchronous validator function, or an array of\n     * such functions, or an `AbstractControlOptions` object that contains\n     * validation functions and a validation trigger.\n     *\n     * @param asyncValidator A single async validator or array of async validator\n     * functions.\n     */\n\n\n    array(controlsConfig, validatorOrOpts, asyncValidator) {\n      const controls = controlsConfig.map(c => this._createControl(c));\n      return new FormArray(controls, validatorOrOpts, asyncValidator);\n    }\n    /** @internal */\n\n\n    _reduceControls(controlsConfig) {\n      const controls = {};\n      Object.keys(controlsConfig).forEach(controlName => {\n        controls[controlName] = this._createControl(controlsConfig[controlName]);\n      });\n      return controls;\n    }\n    /** @internal */\n\n\n    _createControl(controlConfig) {\n      if (isFormControl(controlConfig) || isFormGroup(controlConfig) || isFormArray(controlConfig)) {\n        return controlConfig;\n      } else if (Array.isArray(controlConfig)) {\n        const value = controlConfig[0];\n        const validator = controlConfig.length > 1 ? controlConfig[1] : null;\n        const asyncValidator = controlConfig.length > 2 ? controlConfig[2] : null;\n        return this.control(value, validator, asyncValidator);\n      } else {\n        return this.control(controlConfig);\n      }\n    }\n\n  }\n\n  FormBuilder.ɵfac = function FormBuilder_Factory(t) {\n    return new (t || FormBuilder)();\n  };\n\n  FormBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FormBuilder,\n    factory: FormBuilder.ɵfac,\n    providedIn: ReactiveFormsModule\n  });\n  return FormBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nconst VERSION = /*#__PURE__*/new Version('13.1.3');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AbstractControl, AbstractControlDirective, AbstractFormGroupDirective, COMPOSITION_BUFFER_MODE, CheckboxControlValueAccessor, CheckboxRequiredValidator, ControlContainer, DefaultValueAccessor, EmailValidator, FormArray, FormArrayName, FormBuilder, FormControl, FormControlDirective, FormControlName, FormGroup, FormGroupDirective, FormGroupName, FormsModule, MaxLengthValidator, MaxValidator, MinLengthValidator, MinValidator, NG_ASYNC_VALIDATORS, NG_VALIDATORS, NG_VALUE_ACCESSOR, NgControl, NgControlStatus, NgControlStatusGroup, NgForm, NgModel, NgModelGroup, NgSelectOption, NumberValueAccessor, PatternValidator, RadioControlValueAccessor, RangeValueAccessor, ReactiveFormsModule, RequiredValidator, SelectControlValueAccessor, SelectMultipleControlValueAccessor, VERSION, Validators, ɵInternalFormsSharedModule, ɵNgNoValidate, ɵNgSelectMultipleOption }; //# sourceMappingURL=forms.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}