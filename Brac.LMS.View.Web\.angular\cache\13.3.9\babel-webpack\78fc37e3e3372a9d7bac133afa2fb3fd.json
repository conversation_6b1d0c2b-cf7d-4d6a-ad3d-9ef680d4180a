{"ast": null, "code": "import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function bufferWhen(closingSelector) {\n  return operate((source, subscriber) => {\n    let buffer = null;\n    let closingSubscriber = null;\n\n    const openBuffer = () => {\n      closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n      const b = buffer;\n      buffer = [];\n      b && subscriber.next(b);\n      innerFrom(closingSelector()).subscribe(closingSubscriber = new OperatorSubscriber(subscriber, openBuffer, noop));\n    };\n\n    openBuffer();\n    source.subscribe(new OperatorSubscriber(subscriber, value => buffer === null || buffer === void 0 ? void 0 : buffer.push(value), () => {\n      buffer && subscriber.next(buffer);\n      subscriber.complete();\n    }, undefined, () => buffer = closingSubscriber = null));\n  });\n} //# sourceMappingURL=bufferWhen.js.map", "map": null, "metadata": {}, "sourceType": "module"}