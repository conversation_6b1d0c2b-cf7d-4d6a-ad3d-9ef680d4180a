{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Directive, Inject, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i1 from '@angular/flex-layout/core';\nimport { StyleBuilder, BaseDirective2, LAYOUT_CONFIG, ɵmultiply, validateBasis, CoreModule } from '@angular/flex-layout/core';\nimport { buildLayoutCSS, LAYOUT_VALUES, isFlowHorizontal, extendObject } from '@angular/flex-layout/_private-utils';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nlet LayoutStyleBuilder = /*#__PURE__*/(() => {\n  class LayoutStyleBuilder extends StyleBuilder {\n    buildStyles(input, {\n      display\n    }) {\n      const css = buildLayoutCSS(input);\n      return Object.assign(Object.assign({}, css), {\n        display: display === 'none' ? display : css.display\n      });\n    }\n\n  }\n\n  LayoutStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵLayoutStyleBuilder_BaseFactory;\n    return function LayoutStyleBuilder_Factory(t) {\n      return (ɵLayoutStyleBuilder_BaseFactory || (ɵLayoutStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(LayoutStyleBuilder)))(t || LayoutStyleBuilder);\n    };\n  }();\n\n  LayoutStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LayoutStyleBuilder,\n    factory: LayoutStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return LayoutStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst inputs$6 = ['fxLayout', 'fxLayout.xs', 'fxLayout.sm', 'fxLayout.md', 'fxLayout.lg', 'fxLayout.xl', 'fxLayout.lt-sm', 'fxLayout.lt-md', 'fxLayout.lt-lg', 'fxLayout.lt-xl', 'fxLayout.gt-xs', 'fxLayout.gt-sm', 'fxLayout.gt-md', 'fxLayout.gt-lg'];\nconst selector$6 = `\n  [fxLayout], [fxLayout.xs], [fxLayout.sm], [fxLayout.md],\n  [fxLayout.lg], [fxLayout.xl], [fxLayout.lt-sm], [fxLayout.lt-md],\n  [fxLayout.lt-lg], [fxLayout.lt-xl], [fxLayout.gt-xs], [fxLayout.gt-sm],\n  [fxLayout.gt-md], [fxLayout.gt-lg]\n`;\n/**\n * 'layout' flexbox styling directive\n * Defines the positioning flow direction for the child elements: row or column\n * Optional values: column or row (default)\n * @see https://css-tricks.com/almanac/properties/f/flex-direction/\n *\n */\n\nlet LayoutDirective = /*#__PURE__*/(() => {\n  class LayoutDirective extends BaseDirective2 {\n    constructor(elRef, styleUtils, styleBuilder, marshal, _config) {\n      super(elRef, styleBuilder, styleUtils, marshal);\n      this._config = _config;\n      this.DIRECTIVE_KEY = 'layout';\n      this.init();\n    }\n\n    updateWithValue(input) {\n      var _a;\n\n      const detectLayoutDisplay = this._config.detectLayoutDisplay;\n      const display = detectLayoutDisplay ? this.styler.lookupStyle(this.nativeElement, 'display') : '';\n      this.styleCache = (_a = cacheMap.get(display)) !== null && _a !== void 0 ? _a : new Map();\n      cacheMap.set(display, this.styleCache);\n\n      if (this.currentValue !== input) {\n        this.addStyles(input, {\n          display\n        });\n        this.currentValue = input;\n      }\n    }\n\n  }\n\n  LayoutDirective.ɵfac = function LayoutDirective_Factory(t) {\n    return new (t || LayoutDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(LayoutStyleBuilder), i0.ɵɵdirectiveInject(i1.MediaMarshaller), i0.ɵɵdirectiveInject(LAYOUT_CONFIG));\n  };\n\n  LayoutDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: LayoutDirective,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return LayoutDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet DefaultLayoutDirective = /*#__PURE__*/(() => {\n  class DefaultLayoutDirective extends LayoutDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$6;\n    }\n\n  }\n\n  DefaultLayoutDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultLayoutDirective_BaseFactory;\n    return function DefaultLayoutDirective_Factory(t) {\n      return (ɵDefaultLayoutDirective_BaseFactory || (ɵDefaultLayoutDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultLayoutDirective)))(t || DefaultLayoutDirective);\n    };\n  }();\n\n  DefaultLayoutDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultLayoutDirective,\n    selectors: [[\"\", \"fxLayout\", \"\"], [\"\", \"fxLayout.xs\", \"\"], [\"\", \"fxLayout.sm\", \"\"], [\"\", \"fxLayout.md\", \"\"], [\"\", \"fxLayout.lg\", \"\"], [\"\", \"fxLayout.xl\", \"\"], [\"\", \"fxLayout.lt-sm\", \"\"], [\"\", \"fxLayout.lt-md\", \"\"], [\"\", \"fxLayout.lt-lg\", \"\"], [\"\", \"fxLayout.lt-xl\", \"\"], [\"\", \"fxLayout.gt-xs\", \"\"], [\"\", \"fxLayout.gt-sm\", \"\"], [\"\", \"fxLayout.gt-md\", \"\"], [\"\", \"fxLayout.gt-lg\", \"\"]],\n    inputs: {\n      fxLayout: \"fxLayout\",\n      \"fxLayout.xs\": \"fxLayout.xs\",\n      \"fxLayout.sm\": \"fxLayout.sm\",\n      \"fxLayout.md\": \"fxLayout.md\",\n      \"fxLayout.lg\": \"fxLayout.lg\",\n      \"fxLayout.xl\": \"fxLayout.xl\",\n      \"fxLayout.lt-sm\": \"fxLayout.lt-sm\",\n      \"fxLayout.lt-md\": \"fxLayout.lt-md\",\n      \"fxLayout.lt-lg\": \"fxLayout.lt-lg\",\n      \"fxLayout.lt-xl\": \"fxLayout.lt-xl\",\n      \"fxLayout.gt-xs\": \"fxLayout.gt-xs\",\n      \"fxLayout.gt-sm\": \"fxLayout.gt-sm\",\n      \"fxLayout.gt-md\": \"fxLayout.gt-md\",\n      \"fxLayout.gt-lg\": \"fxLayout.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultLayoutDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst cacheMap = /*#__PURE__*/new Map();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst CLEAR_MARGIN_CSS = {\n  'margin-left': null,\n  'margin-right': null,\n  'margin-top': null,\n  'margin-bottom': null\n};\nlet LayoutGapStyleBuilder = /*#__PURE__*/(() => {\n  class LayoutGapStyleBuilder extends StyleBuilder {\n    constructor(_styler, _config) {\n      super();\n      this._styler = _styler;\n      this._config = _config;\n    }\n\n    buildStyles(gapValue, parent) {\n      if (gapValue.endsWith(GRID_SPECIFIER)) {\n        gapValue = gapValue.slice(0, gapValue.indexOf(GRID_SPECIFIER));\n        gapValue = ɵmultiply(gapValue, this._config.multiplier); // Add the margin to the host element\n\n        return buildGridMargin(gapValue, parent.directionality);\n      } else {\n        return {};\n      }\n    }\n\n    sideEffect(gapValue, _styles, parent) {\n      const items = parent.items;\n\n      if (gapValue.endsWith(GRID_SPECIFIER)) {\n        gapValue = gapValue.slice(0, gapValue.indexOf(GRID_SPECIFIER));\n        gapValue = ɵmultiply(gapValue, this._config.multiplier); // For each `element` children, set the padding\n\n        const paddingStyles = buildGridPadding(gapValue, parent.directionality);\n\n        this._styler.applyStyleToElements(paddingStyles, parent.items);\n      } else {\n        gapValue = ɵmultiply(gapValue, this._config.multiplier);\n        gapValue = this.addFallbackUnit(gapValue);\n        const lastItem = items.pop(); // For each `element` children EXCEPT the last,\n        // set the margin right/bottom styles...\n\n        const gapCss = buildGapCSS(gapValue, parent);\n\n        this._styler.applyStyleToElements(gapCss, items); // Clear all gaps for all visible elements\n\n\n        this._styler.applyStyleToElements(CLEAR_MARGIN_CSS, [lastItem]);\n      }\n    }\n\n    addFallbackUnit(value) {\n      return !isNaN(+value) ? `${value}${this._config.defaultUnit}` : value;\n    }\n\n  }\n\n  LayoutGapStyleBuilder.ɵfac = function LayoutGapStyleBuilder_Factory(t) {\n    return new (t || LayoutGapStyleBuilder)(i0.ɵɵinject(i1.StyleUtils), i0.ɵɵinject(LAYOUT_CONFIG));\n  };\n\n  LayoutGapStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LayoutGapStyleBuilder,\n    factory: LayoutGapStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return LayoutGapStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst inputs$5 = ['fxLayoutGap', 'fxLayoutGap.xs', 'fxLayoutGap.sm', 'fxLayoutGap.md', 'fxLayoutGap.lg', 'fxLayoutGap.xl', 'fxLayoutGap.lt-sm', 'fxLayoutGap.lt-md', 'fxLayoutGap.lt-lg', 'fxLayoutGap.lt-xl', 'fxLayoutGap.gt-xs', 'fxLayoutGap.gt-sm', 'fxLayoutGap.gt-md', 'fxLayoutGap.gt-lg'];\nconst selector$5 = `\n  [fxLayoutGap], [fxLayoutGap.xs], [fxLayoutGap.sm], [fxLayoutGap.md],\n  [fxLayoutGap.lg], [fxLayoutGap.xl], [fxLayoutGap.lt-sm], [fxLayoutGap.lt-md],\n  [fxLayoutGap.lt-lg], [fxLayoutGap.lt-xl], [fxLayoutGap.gt-xs], [fxLayoutGap.gt-sm],\n  [fxLayoutGap.gt-md], [fxLayoutGap.gt-lg]\n`;\n/**\n * 'layout-padding' styling directive\n *  Defines padding of child elements in a layout container\n */\n\nlet LayoutGapDirective = /*#__PURE__*/(() => {\n  class LayoutGapDirective extends BaseDirective2 {\n    constructor(elRef, zone, directionality, styleUtils, styleBuilder, marshal) {\n      super(elRef, styleBuilder, styleUtils, marshal);\n      this.zone = zone;\n      this.directionality = directionality;\n      this.styleUtils = styleUtils;\n      this.layout = 'row'; // default flex-direction\n\n      this.DIRECTIVE_KEY = 'layout-gap';\n      this.observerSubject = new Subject();\n      const extraTriggers = [this.directionality.change, this.observerSubject.asObservable()];\n      this.init(extraTriggers);\n      this.marshal.trackValue(this.nativeElement, 'layout').pipe(takeUntil(this.destroySubject)).subscribe(this.onLayoutChange.bind(this));\n    }\n    /** Special accessor to query for all child 'element' nodes regardless of type, class, etc */\n\n\n    get childrenNodes() {\n      const obj = this.nativeElement.children;\n      const buffer = []; // iterate backwards ensuring that length is an UInt32\n\n      for (let i = obj.length; i--;) {\n        buffer[i] = obj[i];\n      }\n\n      return buffer;\n    } // *********************************************\n    // Lifecycle Methods\n    // *********************************************\n\n\n    ngAfterContentInit() {\n      this.buildChildObservable();\n      this.triggerUpdate();\n    }\n\n    ngOnDestroy() {\n      super.ngOnDestroy();\n\n      if (this.observer) {\n        this.observer.disconnect();\n      }\n    } // *********************************************\n    // Protected methods\n    // *********************************************\n\n    /**\n     * Cache the parent container 'flex-direction' and update the 'margin' styles\n     */\n\n\n    onLayoutChange(matcher) {\n      const layout = matcher.value; // Make sure to filter out 'wrap' option\n\n      const direction = layout.split(' ');\n      this.layout = direction[0];\n\n      if (!LAYOUT_VALUES.find(x => x === this.layout)) {\n        this.layout = 'row';\n      }\n\n      this.triggerUpdate();\n    }\n    /**\n     *\n     */\n\n\n    updateWithValue(value) {\n      // Gather all non-hidden Element nodes\n      const items = this.childrenNodes.filter(el => el.nodeType === 1 && this.willDisplay(el)).sort((a, b) => {\n        const orderA = +this.styler.lookupStyle(a, 'order');\n        const orderB = +this.styler.lookupStyle(b, 'order');\n\n        if (isNaN(orderA) || isNaN(orderB) || orderA === orderB) {\n          return 0;\n        } else {\n          return orderA > orderB ? 1 : -1;\n        }\n      });\n\n      if (items.length > 0) {\n        const directionality = this.directionality.value;\n        const layout = this.layout;\n\n        if (layout === 'row' && directionality === 'rtl') {\n          this.styleCache = layoutGapCacheRowRtl;\n        } else if (layout === 'row' && directionality !== 'rtl') {\n          this.styleCache = layoutGapCacheRowLtr;\n        } else if (layout === 'column' && directionality === 'rtl') {\n          this.styleCache = layoutGapCacheColumnRtl;\n        } else if (layout === 'column' && directionality !== 'rtl') {\n          this.styleCache = layoutGapCacheColumnLtr;\n        }\n\n        this.addStyles(value, {\n          directionality,\n          items,\n          layout\n        });\n      }\n    }\n    /** We need to override clearStyles because in most cases mru isn't populated */\n\n\n    clearStyles() {\n      const gridMode = Object.keys(this.mru).length > 0;\n      const childrenStyle = gridMode ? 'padding' : getMarginType(this.directionality.value, this.layout); // If there are styles on the parent remove them\n\n      if (gridMode) {\n        super.clearStyles();\n      } // Then remove the children styles too\n\n\n      this.styleUtils.applyStyleToElements({\n        [childrenStyle]: ''\n      }, this.childrenNodes);\n    }\n    /** Determine if an element will show or hide based on current activation */\n\n\n    willDisplay(source) {\n      const value = this.marshal.getValue(source, 'show-hide');\n      return value === true || value === undefined && this.styleUtils.lookupStyle(source, 'display') !== 'none';\n    }\n\n    buildChildObservable() {\n      this.zone.runOutsideAngular(() => {\n        if (typeof MutationObserver !== 'undefined') {\n          this.observer = new MutationObserver(mutations => {\n            const validatedChanges = it => {\n              return it.addedNodes && it.addedNodes.length > 0 || it.removedNodes && it.removedNodes.length > 0;\n            }; // update gap styles only for child 'added' or 'removed' events\n\n\n            if (mutations.some(validatedChanges)) {\n              this.observerSubject.next();\n            }\n          });\n          this.observer.observe(this.nativeElement, {\n            childList: true\n          });\n        }\n      });\n    }\n\n  }\n\n  LayoutGapDirective.ɵfac = function LayoutGapDirective_Factory(t) {\n    return new (t || LayoutGapDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(LayoutGapStyleBuilder), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  LayoutGapDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: LayoutGapDirective,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return LayoutGapDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet DefaultLayoutGapDirective = /*#__PURE__*/(() => {\n  class DefaultLayoutGapDirective extends LayoutGapDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$5;\n    }\n\n  }\n\n  DefaultLayoutGapDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultLayoutGapDirective_BaseFactory;\n    return function DefaultLayoutGapDirective_Factory(t) {\n      return (ɵDefaultLayoutGapDirective_BaseFactory || (ɵDefaultLayoutGapDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultLayoutGapDirective)))(t || DefaultLayoutGapDirective);\n    };\n  }();\n\n  DefaultLayoutGapDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultLayoutGapDirective,\n    selectors: [[\"\", \"fxLayoutGap\", \"\"], [\"\", \"fxLayoutGap.xs\", \"\"], [\"\", \"fxLayoutGap.sm\", \"\"], [\"\", \"fxLayoutGap.md\", \"\"], [\"\", \"fxLayoutGap.lg\", \"\"], [\"\", \"fxLayoutGap.xl\", \"\"], [\"\", \"fxLayoutGap.lt-sm\", \"\"], [\"\", \"fxLayoutGap.lt-md\", \"\"], [\"\", \"fxLayoutGap.lt-lg\", \"\"], [\"\", \"fxLayoutGap.lt-xl\", \"\"], [\"\", \"fxLayoutGap.gt-xs\", \"\"], [\"\", \"fxLayoutGap.gt-sm\", \"\"], [\"\", \"fxLayoutGap.gt-md\", \"\"], [\"\", \"fxLayoutGap.gt-lg\", \"\"]],\n    inputs: {\n      fxLayoutGap: \"fxLayoutGap\",\n      \"fxLayoutGap.xs\": \"fxLayoutGap.xs\",\n      \"fxLayoutGap.sm\": \"fxLayoutGap.sm\",\n      \"fxLayoutGap.md\": \"fxLayoutGap.md\",\n      \"fxLayoutGap.lg\": \"fxLayoutGap.lg\",\n      \"fxLayoutGap.xl\": \"fxLayoutGap.xl\",\n      \"fxLayoutGap.lt-sm\": \"fxLayoutGap.lt-sm\",\n      \"fxLayoutGap.lt-md\": \"fxLayoutGap.lt-md\",\n      \"fxLayoutGap.lt-lg\": \"fxLayoutGap.lt-lg\",\n      \"fxLayoutGap.lt-xl\": \"fxLayoutGap.lt-xl\",\n      \"fxLayoutGap.gt-xs\": \"fxLayoutGap.gt-xs\",\n      \"fxLayoutGap.gt-sm\": \"fxLayoutGap.gt-sm\",\n      \"fxLayoutGap.gt-md\": \"fxLayoutGap.gt-md\",\n      \"fxLayoutGap.gt-lg\": \"fxLayoutGap.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultLayoutGapDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst layoutGapCacheRowRtl = /*#__PURE__*/new Map();\nconst layoutGapCacheColumnRtl = /*#__PURE__*/new Map();\nconst layoutGapCacheRowLtr = /*#__PURE__*/new Map();\nconst layoutGapCacheColumnLtr = /*#__PURE__*/new Map();\nconst GRID_SPECIFIER = ' grid';\n\nfunction buildGridPadding(value, directionality) {\n  const [between, below] = value.split(' ');\n  const bottom = below !== null && below !== void 0 ? below : between;\n  let paddingRight = '0px',\n      paddingBottom = bottom,\n      paddingLeft = '0px';\n\n  if (directionality === 'rtl') {\n    paddingLeft = between;\n  } else {\n    paddingRight = between;\n  }\n\n  return {\n    'padding': `0px ${paddingRight} ${paddingBottom} ${paddingLeft}`\n  };\n}\n\nfunction buildGridMargin(value, directionality) {\n  const [between, below] = value.split(' ');\n  const bottom = below !== null && below !== void 0 ? below : between;\n\n  const minus = str => `-${str}`;\n\n  let marginRight = '0px',\n      marginBottom = minus(bottom),\n      marginLeft = '0px';\n\n  if (directionality === 'rtl') {\n    marginLeft = minus(between);\n  } else {\n    marginRight = minus(between);\n  }\n\n  return {\n    'margin': `0px ${marginRight} ${marginBottom} ${marginLeft}`\n  };\n}\n\nfunction getMarginType(directionality, layout) {\n  switch (layout) {\n    case 'column':\n      return 'margin-bottom';\n\n    case 'column-reverse':\n      return 'margin-top';\n\n    case 'row':\n      return directionality === 'rtl' ? 'margin-left' : 'margin-right';\n\n    case 'row-reverse':\n      return directionality === 'rtl' ? 'margin-right' : 'margin-left';\n\n    default:\n      return directionality === 'rtl' ? 'margin-left' : 'margin-right';\n  }\n}\n\nfunction buildGapCSS(gapValue, parent) {\n  const key = getMarginType(parent.directionality, parent.layout);\n  const margins = Object.assign({}, CLEAR_MARGIN_CSS);\n  margins[key] = gapValue;\n  return margins;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet FlexStyleBuilder = /*#__PURE__*/(() => {\n  class FlexStyleBuilder extends StyleBuilder {\n    constructor(layoutConfig) {\n      super();\n      this.layoutConfig = layoutConfig;\n    }\n\n    buildStyles(input, parent) {\n      let [grow, shrink, ...basisParts] = input.split(' ');\n      let basis = basisParts.join(' '); // The flex-direction of this element's flex container. Defaults to 'row'.\n\n      const direction = parent.direction.indexOf('column') > -1 ? 'column' : 'row';\n      const max = isFlowHorizontal(direction) ? 'max-width' : 'max-height';\n      const min = isFlowHorizontal(direction) ? 'min-width' : 'min-height';\n      const hasCalc = String(basis).indexOf('calc') > -1;\n      const usingCalc = hasCalc || basis === 'auto';\n      const isPercent = String(basis).indexOf('%') > -1 && !hasCalc;\n      const hasUnits = String(basis).indexOf('px') > -1 || String(basis).indexOf('rem') > -1 || String(basis).indexOf('em') > -1 || String(basis).indexOf('vw') > -1 || String(basis).indexOf('vh') > -1;\n      let isValue = hasCalc || hasUnits;\n      grow = grow == '0' ? 0 : grow;\n      shrink = shrink == '0' ? 0 : shrink; // make box inflexible when shrink and grow are both zero\n      // should not set a min when the grow is zero\n      // should not set a max when the shrink is zero\n\n      const isFixed = !grow && !shrink;\n      let css = {}; // flex-basis allows you to specify the initial/starting main-axis size of the element,\n      // before anything else is computed. It can either be a percentage or an absolute value.\n      // It is, however, not the breaking point for flex-grow/shrink properties\n      //\n      // flex-grow can be seen as this:\n      //   0: Do not stretch. Either size to element's content width, or obey 'flex-basis'.\n      //   1: (Default value). Stretch; will be the same size to all other flex items on\n      //       the same row since they have a default value of 1.\n      //   ≥2 (integer n): Stretch. Will be n times the size of other elements\n      //      with 'flex-grow: 1' on the same row.\n      // Use `null` to clear existing styles.\n\n      const clearStyles = {\n        'max-width': null,\n        'max-height': null,\n        'min-width': null,\n        'min-height': null\n      };\n\n      switch (basis || '') {\n        case '':\n          const useColumnBasisZero = this.layoutConfig.useColumnBasisZero !== false;\n          basis = direction === 'row' ? '0%' : useColumnBasisZero ? '0.000000001px' : 'auto';\n          break;\n\n        case 'initial': // default\n\n        case 'nogrow':\n          grow = 0;\n          basis = 'auto';\n          break;\n\n        case 'grow':\n          basis = '100%';\n          break;\n\n        case 'noshrink':\n          shrink = 0;\n          basis = 'auto';\n          break;\n\n        case 'auto':\n          break;\n\n        case 'none':\n          grow = 0;\n          shrink = 0;\n          basis = 'auto';\n          break;\n\n        default:\n          // Defaults to percentage sizing unless `px` is explicitly set\n          if (!isValue && !isPercent && !isNaN(basis)) {\n            basis = basis + '%';\n          } // Fix for issue 280\n\n\n          if (basis === '0%') {\n            isValue = true;\n          }\n\n          if (basis === '0px') {\n            basis = '0%';\n          } // fix issue #5345\n\n\n          if (hasCalc) {\n            css = extendObject(clearStyles, {\n              'flex-grow': grow,\n              'flex-shrink': shrink,\n              'flex-basis': isValue ? basis : '100%'\n            });\n          } else {\n            css = extendObject(clearStyles, {\n              'flex': `${grow} ${shrink} ${isValue ? basis : '100%'}`\n            });\n          }\n\n          break;\n      }\n\n      if (!(css['flex'] || css['flex-grow'])) {\n        if (hasCalc) {\n          css = extendObject(clearStyles, {\n            'flex-grow': grow,\n            'flex-shrink': shrink,\n            'flex-basis': basis\n          });\n        } else {\n          css = extendObject(clearStyles, {\n            'flex': `${grow} ${shrink} ${basis}`\n          });\n        }\n      } // Fix for issues 277, 534, and 728\n\n\n      if (basis !== '0%' && basis !== '0px' && basis !== '0.000000001px' && basis !== 'auto') {\n        css[min] = isFixed || isValue && grow ? basis : null;\n        css[max] = isFixed || !usingCalc && shrink ? basis : null;\n      } // Fix for issue 528\n\n\n      if (!css[min] && !css[max]) {\n        if (hasCalc) {\n          css = extendObject(clearStyles, {\n            'flex-grow': grow,\n            'flex-shrink': shrink,\n            'flex-basis': basis\n          });\n        } else {\n          css = extendObject(clearStyles, {\n            'flex': `${grow} ${shrink} ${basis}`\n          });\n        }\n      } else {\n        // Fix for issue 660\n        if (parent.hasWrap) {\n          css[hasCalc ? 'flex-basis' : 'flex'] = css[max] ? hasCalc ? css[max] : `${grow} ${shrink} ${css[max]}` : hasCalc ? css[min] : `${grow} ${shrink} ${css[min]}`;\n        }\n      }\n\n      return extendObject(css, {\n        'box-sizing': 'border-box'\n      });\n    }\n\n  }\n\n  FlexStyleBuilder.ɵfac = function FlexStyleBuilder_Factory(t) {\n    return new (t || FlexStyleBuilder)(i0.ɵɵinject(LAYOUT_CONFIG));\n  };\n\n  FlexStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FlexStyleBuilder,\n    factory: FlexStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return FlexStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst inputs$4 = ['fxFlex', 'fxFlex.xs', 'fxFlex.sm', 'fxFlex.md', 'fxFlex.lg', 'fxFlex.xl', 'fxFlex.lt-sm', 'fxFlex.lt-md', 'fxFlex.lt-lg', 'fxFlex.lt-xl', 'fxFlex.gt-xs', 'fxFlex.gt-sm', 'fxFlex.gt-md', 'fxFlex.gt-lg'];\nconst selector$4 = `\n  [fxFlex], [fxFlex.xs], [fxFlex.sm], [fxFlex.md],\n  [fxFlex.lg], [fxFlex.xl], [fxFlex.lt-sm], [fxFlex.lt-md],\n  [fxFlex.lt-lg], [fxFlex.lt-xl], [fxFlex.gt-xs], [fxFlex.gt-sm],\n  [fxFlex.gt-md], [fxFlex.gt-lg]\n`;\n/**\n * Directive to control the size of a flex item using flex-basis, flex-grow, and flex-shrink.\n * Corresponds to the css `flex` shorthand property.\n *\n * @see https://css-tricks.com/snippets/css/a-guide-to-flexbox/\n */\n\nlet FlexDirective = /*#__PURE__*/(() => {\n  class FlexDirective extends BaseDirective2 {\n    constructor(elRef, styleUtils, layoutConfig, styleBuilder, marshal) {\n      super(elRef, styleBuilder, styleUtils, marshal);\n      this.layoutConfig = layoutConfig;\n      this.marshal = marshal;\n      this.DIRECTIVE_KEY = 'flex';\n      this.direction = undefined;\n      this.wrap = undefined;\n      this.flexGrow = '1';\n      this.flexShrink = '1';\n      this.init();\n    }\n\n    get shrink() {\n      return this.flexShrink;\n    }\n\n    set shrink(value) {\n      this.flexShrink = value || '1';\n      this.triggerReflow();\n    }\n\n    get grow() {\n      return this.flexGrow;\n    }\n\n    set grow(value) {\n      this.flexGrow = value || '1';\n      this.triggerReflow();\n    }\n\n    ngOnInit() {\n      if (this.parentElement) {\n        this.marshal.trackValue(this.parentElement, 'layout').pipe(takeUntil(this.destroySubject)).subscribe(this.onLayoutChange.bind(this));\n        this.marshal.trackValue(this.nativeElement, 'layout-align').pipe(takeUntil(this.destroySubject)).subscribe(this.triggerReflow.bind(this));\n      }\n    }\n    /**\n     * Caches the parent container's 'flex-direction' and updates the element's style.\n     * Used as a handler for layout change events from the parent flex container.\n     */\n\n\n    onLayoutChange(matcher) {\n      const layout = matcher.value;\n      const layoutParts = layout.split(' ');\n      this.direction = layoutParts[0];\n      this.wrap = layoutParts[1] !== undefined && layoutParts[1] === 'wrap';\n      this.triggerUpdate();\n    }\n    /** Input to this is exclusively the basis input value */\n\n\n    updateWithValue(value) {\n      const addFlexToParent = this.layoutConfig.addFlexToParent !== false;\n\n      if (this.direction === undefined) {\n        this.direction = this.getFlexFlowDirection(this.parentElement, addFlexToParent);\n      }\n\n      if (this.wrap === undefined) {\n        this.wrap = this.hasWrap(this.parentElement);\n      }\n\n      const direction = this.direction;\n      const isHorizontal = direction.startsWith('row');\n      const hasWrap = this.wrap;\n\n      if (isHorizontal && hasWrap) {\n        this.styleCache = flexRowWrapCache;\n      } else if (isHorizontal && !hasWrap) {\n        this.styleCache = flexRowCache;\n      } else if (!isHorizontal && hasWrap) {\n        this.styleCache = flexColumnWrapCache;\n      } else if (!isHorizontal && !hasWrap) {\n        this.styleCache = flexColumnCache;\n      }\n\n      const basis = String(value).replace(';', '');\n      const parts = validateBasis(basis, this.flexGrow, this.flexShrink);\n      this.addStyles(parts.join(' '), {\n        direction,\n        hasWrap\n      });\n    }\n    /** Trigger a style reflow, usually based on a shrink/grow input event */\n\n\n    triggerReflow() {\n      const activatedValue = this.activatedValue;\n\n      if (activatedValue !== undefined) {\n        const parts = validateBasis(activatedValue + '', this.flexGrow, this.flexShrink);\n        this.marshal.updateElement(this.nativeElement, this.DIRECTIVE_KEY, parts.join(' '));\n      }\n    }\n\n  }\n\n  FlexDirective.ɵfac = function FlexDirective_Factory(t) {\n    return new (t || FlexDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(LAYOUT_CONFIG), i0.ɵɵdirectiveInject(FlexStyleBuilder), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  FlexDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FlexDirective,\n    inputs: {\n      shrink: [\"fxShrink\", \"shrink\"],\n      grow: [\"fxGrow\", \"grow\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return FlexDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet DefaultFlexDirective = /*#__PURE__*/(() => {\n  class DefaultFlexDirective extends FlexDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$4;\n    }\n\n  }\n\n  DefaultFlexDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultFlexDirective_BaseFactory;\n    return function DefaultFlexDirective_Factory(t) {\n      return (ɵDefaultFlexDirective_BaseFactory || (ɵDefaultFlexDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultFlexDirective)))(t || DefaultFlexDirective);\n    };\n  }();\n\n  DefaultFlexDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultFlexDirective,\n    selectors: [[\"\", \"fxFlex\", \"\"], [\"\", \"fxFlex.xs\", \"\"], [\"\", \"fxFlex.sm\", \"\"], [\"\", \"fxFlex.md\", \"\"], [\"\", \"fxFlex.lg\", \"\"], [\"\", \"fxFlex.xl\", \"\"], [\"\", \"fxFlex.lt-sm\", \"\"], [\"\", \"fxFlex.lt-md\", \"\"], [\"\", \"fxFlex.lt-lg\", \"\"], [\"\", \"fxFlex.lt-xl\", \"\"], [\"\", \"fxFlex.gt-xs\", \"\"], [\"\", \"fxFlex.gt-sm\", \"\"], [\"\", \"fxFlex.gt-md\", \"\"], [\"\", \"fxFlex.gt-lg\", \"\"]],\n    inputs: {\n      fxFlex: \"fxFlex\",\n      \"fxFlex.xs\": \"fxFlex.xs\",\n      \"fxFlex.sm\": \"fxFlex.sm\",\n      \"fxFlex.md\": \"fxFlex.md\",\n      \"fxFlex.lg\": \"fxFlex.lg\",\n      \"fxFlex.xl\": \"fxFlex.xl\",\n      \"fxFlex.lt-sm\": \"fxFlex.lt-sm\",\n      \"fxFlex.lt-md\": \"fxFlex.lt-md\",\n      \"fxFlex.lt-lg\": \"fxFlex.lt-lg\",\n      \"fxFlex.lt-xl\": \"fxFlex.lt-xl\",\n      \"fxFlex.gt-xs\": \"fxFlex.gt-xs\",\n      \"fxFlex.gt-sm\": \"fxFlex.gt-sm\",\n      \"fxFlex.gt-md\": \"fxFlex.gt-md\",\n      \"fxFlex.gt-lg\": \"fxFlex.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultFlexDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst flexRowCache = /*#__PURE__*/new Map();\nconst flexColumnCache = /*#__PURE__*/new Map();\nconst flexRowWrapCache = /*#__PURE__*/new Map();\nconst flexColumnWrapCache = /*#__PURE__*/new Map();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nlet FlexOrderStyleBuilder = /*#__PURE__*/(() => {\n  class FlexOrderStyleBuilder extends StyleBuilder {\n    buildStyles(value) {\n      return {\n        order: value && parseInt(value, 10) || ''\n      };\n    }\n\n  }\n\n  FlexOrderStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵFlexOrderStyleBuilder_BaseFactory;\n    return function FlexOrderStyleBuilder_Factory(t) {\n      return (ɵFlexOrderStyleBuilder_BaseFactory || (ɵFlexOrderStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(FlexOrderStyleBuilder)))(t || FlexOrderStyleBuilder);\n    };\n  }();\n\n  FlexOrderStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FlexOrderStyleBuilder,\n    factory: FlexOrderStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return FlexOrderStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst inputs$3 = ['fxFlexOrder', 'fxFlexOrder.xs', 'fxFlexOrder.sm', 'fxFlexOrder.md', 'fxFlexOrder.lg', 'fxFlexOrder.xl', 'fxFlexOrder.lt-sm', 'fxFlexOrder.lt-md', 'fxFlexOrder.lt-lg', 'fxFlexOrder.lt-xl', 'fxFlexOrder.gt-xs', 'fxFlexOrder.gt-sm', 'fxFlexOrder.gt-md', 'fxFlexOrder.gt-lg'];\nconst selector$3 = `\n  [fxFlexOrder], [fxFlexOrder.xs], [fxFlexOrder.sm], [fxFlexOrder.md],\n  [fxFlexOrder.lg], [fxFlexOrder.xl], [fxFlexOrder.lt-sm], [fxFlexOrder.lt-md],\n  [fxFlexOrder.lt-lg], [fxFlexOrder.lt-xl], [fxFlexOrder.gt-xs], [fxFlexOrder.gt-sm],\n  [fxFlexOrder.gt-md], [fxFlexOrder.gt-lg]\n`;\n/**\n * 'flex-order' flexbox styling directive\n * Configures the positional ordering of the element in a sorted layout container\n * @see https://css-tricks.com/almanac/properties/o/order/\n */\n\nlet FlexOrderDirective = /*#__PURE__*/(() => {\n  class FlexOrderDirective extends BaseDirective2 {\n    constructor(elRef, styleUtils, styleBuilder, marshal) {\n      super(elRef, styleBuilder, styleUtils, marshal);\n      this.DIRECTIVE_KEY = 'flex-order';\n      this.styleCache = flexOrderCache;\n      this.init();\n    }\n\n  }\n\n  FlexOrderDirective.ɵfac = function FlexOrderDirective_Factory(t) {\n    return new (t || FlexOrderDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(FlexOrderStyleBuilder), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  FlexOrderDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FlexOrderDirective,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return FlexOrderDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst flexOrderCache = /*#__PURE__*/new Map();\nlet DefaultFlexOrderDirective = /*#__PURE__*/(() => {\n  class DefaultFlexOrderDirective extends FlexOrderDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$3;\n    }\n\n  }\n\n  DefaultFlexOrderDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultFlexOrderDirective_BaseFactory;\n    return function DefaultFlexOrderDirective_Factory(t) {\n      return (ɵDefaultFlexOrderDirective_BaseFactory || (ɵDefaultFlexOrderDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultFlexOrderDirective)))(t || DefaultFlexOrderDirective);\n    };\n  }();\n\n  DefaultFlexOrderDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultFlexOrderDirective,\n    selectors: [[\"\", \"fxFlexOrder\", \"\"], [\"\", \"fxFlexOrder.xs\", \"\"], [\"\", \"fxFlexOrder.sm\", \"\"], [\"\", \"fxFlexOrder.md\", \"\"], [\"\", \"fxFlexOrder.lg\", \"\"], [\"\", \"fxFlexOrder.xl\", \"\"], [\"\", \"fxFlexOrder.lt-sm\", \"\"], [\"\", \"fxFlexOrder.lt-md\", \"\"], [\"\", \"fxFlexOrder.lt-lg\", \"\"], [\"\", \"fxFlexOrder.lt-xl\", \"\"], [\"\", \"fxFlexOrder.gt-xs\", \"\"], [\"\", \"fxFlexOrder.gt-sm\", \"\"], [\"\", \"fxFlexOrder.gt-md\", \"\"], [\"\", \"fxFlexOrder.gt-lg\", \"\"]],\n    inputs: {\n      fxFlexOrder: \"fxFlexOrder\",\n      \"fxFlexOrder.xs\": \"fxFlexOrder.xs\",\n      \"fxFlexOrder.sm\": \"fxFlexOrder.sm\",\n      \"fxFlexOrder.md\": \"fxFlexOrder.md\",\n      \"fxFlexOrder.lg\": \"fxFlexOrder.lg\",\n      \"fxFlexOrder.xl\": \"fxFlexOrder.xl\",\n      \"fxFlexOrder.lt-sm\": \"fxFlexOrder.lt-sm\",\n      \"fxFlexOrder.lt-md\": \"fxFlexOrder.lt-md\",\n      \"fxFlexOrder.lt-lg\": \"fxFlexOrder.lt-lg\",\n      \"fxFlexOrder.lt-xl\": \"fxFlexOrder.lt-xl\",\n      \"fxFlexOrder.gt-xs\": \"fxFlexOrder.gt-xs\",\n      \"fxFlexOrder.gt-sm\": \"fxFlexOrder.gt-sm\",\n      \"fxFlexOrder.gt-md\": \"fxFlexOrder.gt-md\",\n      \"fxFlexOrder.gt-lg\": \"fxFlexOrder.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultFlexOrderDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet FlexOffsetStyleBuilder = /*#__PURE__*/(() => {\n  class FlexOffsetStyleBuilder extends StyleBuilder {\n    constructor(_config) {\n      super();\n      this._config = _config;\n    }\n\n    buildStyles(offset, parent) {\n      offset || (offset = '0');\n      offset = ɵmultiply(offset, this._config.multiplier);\n      const isPercent = String(offset).indexOf('%') > -1;\n      const isPx = String(offset).indexOf('px') > -1;\n\n      if (!isPx && !isPercent && !isNaN(+offset)) {\n        offset = `${offset}%`;\n      }\n\n      const horizontalLayoutKey = parent.isRtl ? 'margin-right' : 'margin-left';\n      const styles = isFlowHorizontal(parent.layout) ? {\n        [horizontalLayoutKey]: offset\n      } : {\n        'margin-top': offset\n      };\n      return styles;\n    }\n\n  }\n\n  FlexOffsetStyleBuilder.ɵfac = function FlexOffsetStyleBuilder_Factory(t) {\n    return new (t || FlexOffsetStyleBuilder)(i0.ɵɵinject(LAYOUT_CONFIG));\n  };\n\n  FlexOffsetStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FlexOffsetStyleBuilder,\n    factory: FlexOffsetStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return FlexOffsetStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst inputs$2 = ['fxFlexOffset', 'fxFlexOffset.xs', 'fxFlexOffset.sm', 'fxFlexOffset.md', 'fxFlexOffset.lg', 'fxFlexOffset.xl', 'fxFlexOffset.lt-sm', 'fxFlexOffset.lt-md', 'fxFlexOffset.lt-lg', 'fxFlexOffset.lt-xl', 'fxFlexOffset.gt-xs', 'fxFlexOffset.gt-sm', 'fxFlexOffset.gt-md', 'fxFlexOffset.gt-lg'];\nconst selector$2 = `\n  [fxFlexOffset], [fxFlexOffset.xs], [fxFlexOffset.sm], [fxFlexOffset.md],\n  [fxFlexOffset.lg], [fxFlexOffset.xl], [fxFlexOffset.lt-sm], [fxFlexOffset.lt-md],\n  [fxFlexOffset.lt-lg], [fxFlexOffset.lt-xl], [fxFlexOffset.gt-xs], [fxFlexOffset.gt-sm],\n  [fxFlexOffset.gt-md], [fxFlexOffset.gt-lg]\n`;\n/**\n * 'flex-offset' flexbox styling directive\n * Configures the 'margin-left' of the element in a layout container\n */\n\nlet FlexOffsetDirective = /*#__PURE__*/(() => {\n  class FlexOffsetDirective extends BaseDirective2 {\n    constructor(elRef, directionality, styleBuilder, marshal, styler) {\n      super(elRef, styleBuilder, styler, marshal);\n      this.directionality = directionality;\n      this.DIRECTIVE_KEY = 'flex-offset';\n      this.init([this.directionality.change]); // Parent DOM `layout-gap` with affect the nested child with `flex-offset`\n\n      if (this.parentElement) {\n        this.marshal.trackValue(this.parentElement, 'layout-gap').pipe(takeUntil(this.destroySubject)).subscribe(this.triggerUpdate.bind(this));\n      }\n    } // *********************************************\n    // Protected methods\n    // *********************************************\n\n    /**\n     * Using the current fxFlexOffset value, update the inline CSS\n     * NOTE: this will assign `margin-left` if the parent flex-direction == 'row',\n     *       otherwise `margin-top` is used for the offset.\n     */\n\n\n    updateWithValue(value = '') {\n      // The flex-direction of this element's flex container. Defaults to 'row'.\n      const layout = this.getFlexFlowDirection(this.parentElement, true);\n      const isRtl = this.directionality.value === 'rtl';\n\n      if (layout === 'row' && isRtl) {\n        this.styleCache = flexOffsetCacheRowRtl;\n      } else if (layout === 'row' && !isRtl) {\n        this.styleCache = flexOffsetCacheRowLtr;\n      } else if (layout === 'column' && isRtl) {\n        this.styleCache = flexOffsetCacheColumnRtl;\n      } else if (layout === 'column' && !isRtl) {\n        this.styleCache = flexOffsetCacheColumnLtr;\n      }\n\n      this.addStyles(value + '', {\n        layout,\n        isRtl\n      });\n    }\n\n  }\n\n  FlexOffsetDirective.ɵfac = function FlexOffsetDirective_Factory(t) {\n    return new (t || FlexOffsetDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Directionality), i0.ɵɵdirectiveInject(FlexOffsetStyleBuilder), i0.ɵɵdirectiveInject(i1.MediaMarshaller), i0.ɵɵdirectiveInject(i1.StyleUtils));\n  };\n\n  FlexOffsetDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FlexOffsetDirective,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return FlexOffsetDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet DefaultFlexOffsetDirective = /*#__PURE__*/(() => {\n  class DefaultFlexOffsetDirective extends FlexOffsetDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$2;\n    }\n\n  }\n\n  DefaultFlexOffsetDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultFlexOffsetDirective_BaseFactory;\n    return function DefaultFlexOffsetDirective_Factory(t) {\n      return (ɵDefaultFlexOffsetDirective_BaseFactory || (ɵDefaultFlexOffsetDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultFlexOffsetDirective)))(t || DefaultFlexOffsetDirective);\n    };\n  }();\n\n  DefaultFlexOffsetDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultFlexOffsetDirective,\n    selectors: [[\"\", \"fxFlexOffset\", \"\"], [\"\", \"fxFlexOffset.xs\", \"\"], [\"\", \"fxFlexOffset.sm\", \"\"], [\"\", \"fxFlexOffset.md\", \"\"], [\"\", \"fxFlexOffset.lg\", \"\"], [\"\", \"fxFlexOffset.xl\", \"\"], [\"\", \"fxFlexOffset.lt-sm\", \"\"], [\"\", \"fxFlexOffset.lt-md\", \"\"], [\"\", \"fxFlexOffset.lt-lg\", \"\"], [\"\", \"fxFlexOffset.lt-xl\", \"\"], [\"\", \"fxFlexOffset.gt-xs\", \"\"], [\"\", \"fxFlexOffset.gt-sm\", \"\"], [\"\", \"fxFlexOffset.gt-md\", \"\"], [\"\", \"fxFlexOffset.gt-lg\", \"\"]],\n    inputs: {\n      fxFlexOffset: \"fxFlexOffset\",\n      \"fxFlexOffset.xs\": \"fxFlexOffset.xs\",\n      \"fxFlexOffset.sm\": \"fxFlexOffset.sm\",\n      \"fxFlexOffset.md\": \"fxFlexOffset.md\",\n      \"fxFlexOffset.lg\": \"fxFlexOffset.lg\",\n      \"fxFlexOffset.xl\": \"fxFlexOffset.xl\",\n      \"fxFlexOffset.lt-sm\": \"fxFlexOffset.lt-sm\",\n      \"fxFlexOffset.lt-md\": \"fxFlexOffset.lt-md\",\n      \"fxFlexOffset.lt-lg\": \"fxFlexOffset.lt-lg\",\n      \"fxFlexOffset.lt-xl\": \"fxFlexOffset.lt-xl\",\n      \"fxFlexOffset.gt-xs\": \"fxFlexOffset.gt-xs\",\n      \"fxFlexOffset.gt-sm\": \"fxFlexOffset.gt-sm\",\n      \"fxFlexOffset.gt-md\": \"fxFlexOffset.gt-md\",\n      \"fxFlexOffset.gt-lg\": \"fxFlexOffset.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultFlexOffsetDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst flexOffsetCacheRowRtl = /*#__PURE__*/new Map();\nconst flexOffsetCacheColumnRtl = /*#__PURE__*/new Map();\nconst flexOffsetCacheRowLtr = /*#__PURE__*/new Map();\nconst flexOffsetCacheColumnLtr = /*#__PURE__*/new Map();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nlet FlexAlignStyleBuilder = /*#__PURE__*/(() => {\n  class FlexAlignStyleBuilder extends StyleBuilder {\n    buildStyles(input) {\n      input = input || 'stretch';\n      const styles = {}; // Cross-axis\n\n      switch (input) {\n        case 'start':\n          styles['align-self'] = 'flex-start';\n          break;\n\n        case 'end':\n          styles['align-self'] = 'flex-end';\n          break;\n\n        default:\n          styles['align-self'] = input;\n          break;\n      }\n\n      return styles;\n    }\n\n  }\n\n  FlexAlignStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵFlexAlignStyleBuilder_BaseFactory;\n    return function FlexAlignStyleBuilder_Factory(t) {\n      return (ɵFlexAlignStyleBuilder_BaseFactory || (ɵFlexAlignStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(FlexAlignStyleBuilder)))(t || FlexAlignStyleBuilder);\n    };\n  }();\n\n  FlexAlignStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FlexAlignStyleBuilder,\n    factory: FlexAlignStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return FlexAlignStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst inputs$1 = ['fxFlexAlign', 'fxFlexAlign.xs', 'fxFlexAlign.sm', 'fxFlexAlign.md', 'fxFlexAlign.lg', 'fxFlexAlign.xl', 'fxFlexAlign.lt-sm', 'fxFlexAlign.lt-md', 'fxFlexAlign.lt-lg', 'fxFlexAlign.lt-xl', 'fxFlexAlign.gt-xs', 'fxFlexAlign.gt-sm', 'fxFlexAlign.gt-md', 'fxFlexAlign.gt-lg'];\nconst selector$1 = `\n  [fxFlexAlign], [fxFlexAlign.xs], [fxFlexAlign.sm], [fxFlexAlign.md],\n  [fxFlexAlign.lg], [fxFlexAlign.xl], [fxFlexAlign.lt-sm], [fxFlexAlign.lt-md],\n  [fxFlexAlign.lt-lg], [fxFlexAlign.lt-xl], [fxFlexAlign.gt-xs], [fxFlexAlign.gt-sm],\n  [fxFlexAlign.gt-md], [fxFlexAlign.gt-lg]\n`;\n/**\n * 'flex-align' flexbox styling directive\n * Allows element-specific overrides for cross-axis alignments in a layout container\n * @see https://css-tricks.com/almanac/properties/a/align-self/\n */\n\nlet FlexAlignDirective = /*#__PURE__*/(() => {\n  class FlexAlignDirective extends BaseDirective2 {\n    constructor(elRef, styleUtils, styleBuilder, marshal) {\n      super(elRef, styleBuilder, styleUtils, marshal);\n      this.DIRECTIVE_KEY = 'flex-align';\n      this.styleCache = flexAlignCache;\n      this.init();\n    }\n\n  }\n\n  FlexAlignDirective.ɵfac = function FlexAlignDirective_Factory(t) {\n    return new (t || FlexAlignDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(FlexAlignStyleBuilder), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  FlexAlignDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FlexAlignDirective,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return FlexAlignDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst flexAlignCache = /*#__PURE__*/new Map();\nlet DefaultFlexAlignDirective = /*#__PURE__*/(() => {\n  class DefaultFlexAlignDirective extends FlexAlignDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$1;\n    }\n\n  }\n\n  DefaultFlexAlignDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultFlexAlignDirective_BaseFactory;\n    return function DefaultFlexAlignDirective_Factory(t) {\n      return (ɵDefaultFlexAlignDirective_BaseFactory || (ɵDefaultFlexAlignDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultFlexAlignDirective)))(t || DefaultFlexAlignDirective);\n    };\n  }();\n\n  DefaultFlexAlignDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultFlexAlignDirective,\n    selectors: [[\"\", \"fxFlexAlign\", \"\"], [\"\", \"fxFlexAlign.xs\", \"\"], [\"\", \"fxFlexAlign.sm\", \"\"], [\"\", \"fxFlexAlign.md\", \"\"], [\"\", \"fxFlexAlign.lg\", \"\"], [\"\", \"fxFlexAlign.xl\", \"\"], [\"\", \"fxFlexAlign.lt-sm\", \"\"], [\"\", \"fxFlexAlign.lt-md\", \"\"], [\"\", \"fxFlexAlign.lt-lg\", \"\"], [\"\", \"fxFlexAlign.lt-xl\", \"\"], [\"\", \"fxFlexAlign.gt-xs\", \"\"], [\"\", \"fxFlexAlign.gt-sm\", \"\"], [\"\", \"fxFlexAlign.gt-md\", \"\"], [\"\", \"fxFlexAlign.gt-lg\", \"\"]],\n    inputs: {\n      fxFlexAlign: \"fxFlexAlign\",\n      \"fxFlexAlign.xs\": \"fxFlexAlign.xs\",\n      \"fxFlexAlign.sm\": \"fxFlexAlign.sm\",\n      \"fxFlexAlign.md\": \"fxFlexAlign.md\",\n      \"fxFlexAlign.lg\": \"fxFlexAlign.lg\",\n      \"fxFlexAlign.xl\": \"fxFlexAlign.xl\",\n      \"fxFlexAlign.lt-sm\": \"fxFlexAlign.lt-sm\",\n      \"fxFlexAlign.lt-md\": \"fxFlexAlign.lt-md\",\n      \"fxFlexAlign.lt-lg\": \"fxFlexAlign.lt-lg\",\n      \"fxFlexAlign.lt-xl\": \"fxFlexAlign.lt-xl\",\n      \"fxFlexAlign.gt-xs\": \"fxFlexAlign.gt-xs\",\n      \"fxFlexAlign.gt-sm\": \"fxFlexAlign.gt-sm\",\n      \"fxFlexAlign.gt-md\": \"fxFlexAlign.gt-md\",\n      \"fxFlexAlign.gt-lg\": \"fxFlexAlign.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultFlexAlignDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst FLEX_FILL_CSS = {\n  'margin': 0,\n  'width': '100%',\n  'height': '100%',\n  'min-width': '100%',\n  'min-height': '100%'\n};\nlet FlexFillStyleBuilder = /*#__PURE__*/(() => {\n  class FlexFillStyleBuilder extends StyleBuilder {\n    buildStyles(_input) {\n      return FLEX_FILL_CSS;\n    }\n\n  }\n\n  FlexFillStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵFlexFillStyleBuilder_BaseFactory;\n    return function FlexFillStyleBuilder_Factory(t) {\n      return (ɵFlexFillStyleBuilder_BaseFactory || (ɵFlexFillStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(FlexFillStyleBuilder)))(t || FlexFillStyleBuilder);\n    };\n  }();\n\n  FlexFillStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FlexFillStyleBuilder,\n    factory: FlexFillStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return FlexFillStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * 'fxFill' flexbox styling directive\n *  Maximizes width and height of element in a layout container\n *\n *  NOTE: fxFill is NOT responsive API!!\n */\n\n\nlet FlexFillDirective = /*#__PURE__*/(() => {\n  class FlexFillDirective extends BaseDirective2 {\n    constructor(elRef, styleUtils, styleBuilder, marshal) {\n      super(elRef, styleBuilder, styleUtils, marshal);\n      this.styleCache = flexFillCache;\n      this.addStyles('');\n    }\n\n  }\n\n  FlexFillDirective.ɵfac = function FlexFillDirective_Factory(t) {\n    return new (t || FlexFillDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(FlexFillStyleBuilder), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  FlexFillDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FlexFillDirective,\n    selectors: [[\"\", \"fxFill\", \"\"], [\"\", \"fxFlexFill\", \"\"]],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return FlexFillDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst flexFillCache = /*#__PURE__*/new Map();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nlet LayoutAlignStyleBuilder = /*#__PURE__*/(() => {\n  class LayoutAlignStyleBuilder extends StyleBuilder {\n    buildStyles(align, parent) {\n      const css = {},\n            [mainAxis, crossAxis] = align.split(' '); // Main axis\n\n      switch (mainAxis) {\n        case 'center':\n          css['justify-content'] = 'center';\n          break;\n\n        case 'space-around':\n          css['justify-content'] = 'space-around';\n          break;\n\n        case 'space-between':\n          css['justify-content'] = 'space-between';\n          break;\n\n        case 'space-evenly':\n          css['justify-content'] = 'space-evenly';\n          break;\n\n        case 'end':\n        case 'flex-end':\n          css['justify-content'] = 'flex-end';\n          break;\n\n        case 'start':\n        case 'flex-start':\n        default:\n          css['justify-content'] = 'flex-start'; // default main axis\n\n          break;\n      } // Cross-axis\n\n\n      switch (crossAxis) {\n        case 'start':\n        case 'flex-start':\n          css['align-items'] = css['align-content'] = 'flex-start';\n          break;\n\n        case 'center':\n          css['align-items'] = css['align-content'] = 'center';\n          break;\n\n        case 'end':\n        case 'flex-end':\n          css['align-items'] = css['align-content'] = 'flex-end';\n          break;\n\n        case 'space-between':\n          css['align-content'] = 'space-between';\n          css['align-items'] = 'stretch';\n          break;\n\n        case 'space-around':\n          css['align-content'] = 'space-around';\n          css['align-items'] = 'stretch';\n          break;\n\n        case 'baseline':\n          css['align-content'] = 'stretch';\n          css['align-items'] = 'baseline';\n          break;\n\n        case 'stretch':\n        default:\n          // 'stretch'\n          css['align-items'] = css['align-content'] = 'stretch'; // default cross axis\n\n          break;\n      }\n\n      return extendObject(css, {\n        'display': parent.inline ? 'inline-flex' : 'flex',\n        'flex-direction': parent.layout,\n        'box-sizing': 'border-box',\n        'max-width': crossAxis === 'stretch' ? !isFlowHorizontal(parent.layout) ? '100%' : null : null,\n        'max-height': crossAxis === 'stretch' ? isFlowHorizontal(parent.layout) ? '100%' : null : null\n      });\n    }\n\n  }\n\n  LayoutAlignStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵLayoutAlignStyleBuilder_BaseFactory;\n    return function LayoutAlignStyleBuilder_Factory(t) {\n      return (ɵLayoutAlignStyleBuilder_BaseFactory || (ɵLayoutAlignStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(LayoutAlignStyleBuilder)))(t || LayoutAlignStyleBuilder);\n    };\n  }();\n\n  LayoutAlignStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LayoutAlignStyleBuilder,\n    factory: LayoutAlignStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return LayoutAlignStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst inputs = ['fxLayoutAlign', 'fxLayoutAlign.xs', 'fxLayoutAlign.sm', 'fxLayoutAlign.md', 'fxLayoutAlign.lg', 'fxLayoutAlign.xl', 'fxLayoutAlign.lt-sm', 'fxLayoutAlign.lt-md', 'fxLayoutAlign.lt-lg', 'fxLayoutAlign.lt-xl', 'fxLayoutAlign.gt-xs', 'fxLayoutAlign.gt-sm', 'fxLayoutAlign.gt-md', 'fxLayoutAlign.gt-lg'];\nconst selector = `\n  [fxLayoutAlign], [fxLayoutAlign.xs], [fxLayoutAlign.sm], [fxLayoutAlign.md],\n  [fxLayoutAlign.lg], [fxLayoutAlign.xl], [fxLayoutAlign.lt-sm], [fxLayoutAlign.lt-md],\n  [fxLayoutAlign.lt-lg], [fxLayoutAlign.lt-xl], [fxLayoutAlign.gt-xs], [fxLayoutAlign.gt-sm],\n  [fxLayoutAlign.gt-md], [fxLayoutAlign.gt-lg]\n`;\n/**\n * 'layout-align' flexbox styling directive\n *  Defines positioning of child elements along main and cross axis in a layout container\n *  Optional values: {main-axis} values or {main-axis cross-axis} value pairs\n *\n *  @see https://css-tricks.com/almanac/properties/j/justify-content/\n *  @see https://css-tricks.com/almanac/properties/a/align-items/\n *  @see https://css-tricks.com/almanac/properties/a/align-content/\n */\n\nlet LayoutAlignDirective = /*#__PURE__*/(() => {\n  class LayoutAlignDirective extends BaseDirective2 {\n    constructor(elRef, styleUtils, styleBuilder, marshal) {\n      super(elRef, styleBuilder, styleUtils, marshal);\n      this.DIRECTIVE_KEY = 'layout-align';\n      this.layout = 'row'; // default flex-direction\n\n      this.inline = false; // default inline value\n\n      this.init();\n      this.marshal.trackValue(this.nativeElement, 'layout').pipe(takeUntil(this.destroySubject)).subscribe(this.onLayoutChange.bind(this));\n    } // *********************************************\n    // Protected methods\n    // *********************************************\n\n    /**\n     *\n     */\n\n\n    updateWithValue(value) {\n      const layout = this.layout || 'row';\n      const inline = this.inline;\n\n      if (layout === 'row' && inline) {\n        this.styleCache = layoutAlignHorizontalInlineCache;\n      } else if (layout === 'row' && !inline) {\n        this.styleCache = layoutAlignHorizontalCache;\n      } else if (layout === 'row-reverse' && inline) {\n        this.styleCache = layoutAlignHorizontalRevInlineCache;\n      } else if (layout === 'row-reverse' && !inline) {\n        this.styleCache = layoutAlignHorizontalRevCache;\n      } else if (layout === 'column' && inline) {\n        this.styleCache = layoutAlignVerticalInlineCache;\n      } else if (layout === 'column' && !inline) {\n        this.styleCache = layoutAlignVerticalCache;\n      } else if (layout === 'column-reverse' && inline) {\n        this.styleCache = layoutAlignVerticalRevInlineCache;\n      } else if (layout === 'column-reverse' && !inline) {\n        this.styleCache = layoutAlignVerticalRevCache;\n      }\n\n      this.addStyles(value, {\n        layout,\n        inline\n      });\n    }\n    /**\n     * Cache the parent container 'flex-direction' and update the 'flex' styles\n     */\n\n\n    onLayoutChange(matcher) {\n      const layoutKeys = matcher.value.split(' ');\n      this.layout = layoutKeys[0];\n      this.inline = matcher.value.includes('inline');\n\n      if (!LAYOUT_VALUES.find(x => x === this.layout)) {\n        this.layout = 'row';\n      }\n\n      this.triggerUpdate();\n    }\n\n  }\n\n  LayoutAlignDirective.ɵfac = function LayoutAlignDirective_Factory(t) {\n    return new (t || LayoutAlignDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(LayoutAlignStyleBuilder), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  LayoutAlignDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: LayoutAlignDirective,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return LayoutAlignDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet DefaultLayoutAlignDirective = /*#__PURE__*/(() => {\n  class DefaultLayoutAlignDirective extends LayoutAlignDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs;\n    }\n\n  }\n\n  DefaultLayoutAlignDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultLayoutAlignDirective_BaseFactory;\n    return function DefaultLayoutAlignDirective_Factory(t) {\n      return (ɵDefaultLayoutAlignDirective_BaseFactory || (ɵDefaultLayoutAlignDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultLayoutAlignDirective)))(t || DefaultLayoutAlignDirective);\n    };\n  }();\n\n  DefaultLayoutAlignDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultLayoutAlignDirective,\n    selectors: [[\"\", \"fxLayoutAlign\", \"\"], [\"\", \"fxLayoutAlign.xs\", \"\"], [\"\", \"fxLayoutAlign.sm\", \"\"], [\"\", \"fxLayoutAlign.md\", \"\"], [\"\", \"fxLayoutAlign.lg\", \"\"], [\"\", \"fxLayoutAlign.xl\", \"\"], [\"\", \"fxLayoutAlign.lt-sm\", \"\"], [\"\", \"fxLayoutAlign.lt-md\", \"\"], [\"\", \"fxLayoutAlign.lt-lg\", \"\"], [\"\", \"fxLayoutAlign.lt-xl\", \"\"], [\"\", \"fxLayoutAlign.gt-xs\", \"\"], [\"\", \"fxLayoutAlign.gt-sm\", \"\"], [\"\", \"fxLayoutAlign.gt-md\", \"\"], [\"\", \"fxLayoutAlign.gt-lg\", \"\"]],\n    inputs: {\n      fxLayoutAlign: \"fxLayoutAlign\",\n      \"fxLayoutAlign.xs\": \"fxLayoutAlign.xs\",\n      \"fxLayoutAlign.sm\": \"fxLayoutAlign.sm\",\n      \"fxLayoutAlign.md\": \"fxLayoutAlign.md\",\n      \"fxLayoutAlign.lg\": \"fxLayoutAlign.lg\",\n      \"fxLayoutAlign.xl\": \"fxLayoutAlign.xl\",\n      \"fxLayoutAlign.lt-sm\": \"fxLayoutAlign.lt-sm\",\n      \"fxLayoutAlign.lt-md\": \"fxLayoutAlign.lt-md\",\n      \"fxLayoutAlign.lt-lg\": \"fxLayoutAlign.lt-lg\",\n      \"fxLayoutAlign.lt-xl\": \"fxLayoutAlign.lt-xl\",\n      \"fxLayoutAlign.gt-xs\": \"fxLayoutAlign.gt-xs\",\n      \"fxLayoutAlign.gt-sm\": \"fxLayoutAlign.gt-sm\",\n      \"fxLayoutAlign.gt-md\": \"fxLayoutAlign.gt-md\",\n      \"fxLayoutAlign.gt-lg\": \"fxLayoutAlign.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultLayoutAlignDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst layoutAlignHorizontalCache = /*#__PURE__*/new Map();\nconst layoutAlignVerticalCache = /*#__PURE__*/new Map();\nconst layoutAlignHorizontalRevCache = /*#__PURE__*/new Map();\nconst layoutAlignVerticalRevCache = /*#__PURE__*/new Map();\nconst layoutAlignHorizontalInlineCache = /*#__PURE__*/new Map();\nconst layoutAlignVerticalInlineCache = /*#__PURE__*/new Map();\nconst layoutAlignHorizontalRevInlineCache = /*#__PURE__*/new Map();\nconst layoutAlignVerticalRevInlineCache = /*#__PURE__*/new Map();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst ALL_DIRECTIVES = [DefaultLayoutDirective, DefaultLayoutGapDirective, DefaultLayoutAlignDirective, DefaultFlexOrderDirective, DefaultFlexOffsetDirective, FlexFillDirective, DefaultFlexAlignDirective, DefaultFlexDirective];\n/**\n * *****************************************************************\n * Define module for the Flex API\n * *****************************************************************\n */\n\nlet FlexModule = /*#__PURE__*/(() => {\n  class FlexModule {}\n\n  FlexModule.ɵfac = function FlexModule_Factory(t) {\n    return new (t || FlexModule)();\n  };\n\n  FlexModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FlexModule\n  });\n  FlexModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CoreModule, BidiModule]]\n  });\n  return FlexModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { DefaultFlexAlignDirective, DefaultFlexDirective, DefaultFlexOffsetDirective, DefaultFlexOrderDirective, DefaultLayoutAlignDirective, DefaultLayoutDirective, DefaultLayoutGapDirective, FlexAlignDirective, FlexAlignStyleBuilder, FlexDirective, FlexFillDirective, FlexFillStyleBuilder, FlexModule, FlexOffsetDirective, FlexOffsetStyleBuilder, FlexOrderDirective, FlexOrderStyleBuilder, FlexStyleBuilder, LayoutAlignDirective, LayoutAlignStyleBuilder, LayoutDirective, LayoutGapDirective, LayoutGapStyleBuilder, LayoutStyleBuilder }; //# sourceMappingURL=angular-flex-layout-flex.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}