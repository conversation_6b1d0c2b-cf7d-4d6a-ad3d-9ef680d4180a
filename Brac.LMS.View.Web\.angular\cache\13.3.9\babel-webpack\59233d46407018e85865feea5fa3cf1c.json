{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { AvailableCoursesRoutes } from './available-courses.routing';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let AvailableCoursesModule = /*#__PURE__*/(() => {\n  class AvailableCoursesModule {}\n\n  AvailableCoursesModule.ɵfac = function AvailableCoursesModule_Factory(t) {\n    return new (t || AvailableCoursesModule)();\n  };\n\n  AvailableCoursesModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AvailableCoursesModule\n  });\n  AvailableCoursesModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(AvailableCoursesRoutes), SharedModule, MatExpansionModule]]\n  });\n  return AvailableCoursesModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}