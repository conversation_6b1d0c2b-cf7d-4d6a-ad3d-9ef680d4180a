{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, PLATFORM_ID, Directive, Inject, Input, Optional, Self, SecurityContext, NgModule } from '@angular/core';\nimport * as i1 from '@angular/flex-layout/core';\nimport { StyleBuilder, BaseDirective2, SERVER_TOKEN, LAYOUT_CONFIG, CoreModule } from '@angular/flex-layout/core';\nimport * as i2 from '@angular/common';\nimport { isPlatformServer, NgClass, NgStyle } from '@angular/common';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i2$1 from '@angular/platform-browser';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nlet ImgSrcStyleBuilder = /*#__PURE__*/(() => {\n  class ImgSrcStyleBuilder extends StyleBuilder {\n    buildStyles(url) {\n      return {\n        'content': url ? `url(${url})` : ''\n      };\n    }\n\n  }\n\n  ImgSrcStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵImgSrcStyleBuilder_BaseFactory;\n    return function ImgSrcStyleBuilder_Factory(t) {\n      return (ɵImgSrcStyleBuilder_BaseFactory || (ɵImgSrcStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(ImgSrcStyleBuilder)))(t || ImgSrcStyleBuilder);\n    };\n  }();\n\n  ImgSrcStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ImgSrcStyleBuilder,\n    factory: ImgSrcStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return ImgSrcStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet ImgSrcDirective = /*#__PURE__*/(() => {\n  class ImgSrcDirective extends BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal, platformId, serverModuleLoaded) {\n      super(elementRef, styleBuilder, styler, marshal);\n      this.platformId = platformId;\n      this.serverModuleLoaded = serverModuleLoaded;\n      this.DIRECTIVE_KEY = 'img-src';\n      this.defaultSrc = '';\n      this.styleCache = imgSrcCache;\n      this.init();\n      this.setValue(this.nativeElement.getAttribute('src') || '', '');\n\n      if (isPlatformServer(this.platformId) && this.serverModuleLoaded) {\n        this.nativeElement.setAttribute('src', '');\n      }\n    }\n\n    set src(val) {\n      this.defaultSrc = val;\n      this.setValue(this.defaultSrc, '');\n    }\n    /**\n     * Use the [responsively] activated input value to update\n     * the host img src attribute or assign a default `img.src=''`\n     * if the src has not been defined.\n     *\n     * Do nothing to standard `<img src=\"\">` usages, only when responsive\n     * keys are present do we actually call `setAttribute()`\n     */\n\n\n    updateWithValue(value) {\n      const url = value || this.defaultSrc;\n\n      if (isPlatformServer(this.platformId) && this.serverModuleLoaded) {\n        this.addStyles(url);\n      } else {\n        this.nativeElement.setAttribute('src', url);\n      }\n    }\n\n  }\n\n  ImgSrcDirective.ɵfac = function ImgSrcDirective_Factory(t) {\n    return new (t || ImgSrcDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ImgSrcStyleBuilder), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(SERVER_TOKEN));\n  };\n\n  ImgSrcDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ImgSrcDirective,\n    inputs: {\n      src: \"src\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return ImgSrcDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst imgSrcCache = /*#__PURE__*/new Map();\nconst inputs$3 = ['src.xs', 'src.sm', 'src.md', 'src.lg', 'src.xl', 'src.lt-sm', 'src.lt-md', 'src.lt-lg', 'src.lt-xl', 'src.gt-xs', 'src.gt-sm', 'src.gt-md', 'src.gt-lg'];\nconst selector$3 = `\n  img[src.xs],    img[src.sm],    img[src.md],    img[src.lg],   img[src.xl],\n  img[src.lt-sm], img[src.lt-md], img[src.lt-lg], img[src.lt-xl],\n  img[src.gt-xs], img[src.gt-sm], img[src.gt-md], img[src.gt-lg]\n`;\n/**\n * This directive provides a responsive API for the HTML <img> 'src' attribute\n * and will update the img.src property upon each responsive activation.\n *\n * e.g.\n *      <img src=\"defaultScene.jpg\" src.xs=\"mobileScene.jpg\"></img>\n *\n * @see https://css-tricks.com/responsive-images-youre-just-changing-resolutions-use-src/\n */\n\nlet DefaultImgSrcDirective = /*#__PURE__*/(() => {\n  class DefaultImgSrcDirective extends ImgSrcDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$3;\n    }\n\n  }\n\n  DefaultImgSrcDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultImgSrcDirective_BaseFactory;\n    return function DefaultImgSrcDirective_Factory(t) {\n      return (ɵDefaultImgSrcDirective_BaseFactory || (ɵDefaultImgSrcDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultImgSrcDirective)))(t || DefaultImgSrcDirective);\n    };\n  }();\n\n  DefaultImgSrcDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultImgSrcDirective,\n    selectors: [[\"img\", \"src.xs\", \"\"], [\"img\", \"src.sm\", \"\"], [\"img\", \"src.md\", \"\"], [\"img\", \"src.lg\", \"\"], [\"img\", \"src.xl\", \"\"], [\"img\", \"src.lt-sm\", \"\"], [\"img\", \"src.lt-md\", \"\"], [\"img\", \"src.lt-lg\", \"\"], [\"img\", \"src.lt-xl\", \"\"], [\"img\", \"src.gt-xs\", \"\"], [\"img\", \"src.gt-sm\", \"\"], [\"img\", \"src.gt-md\", \"\"], [\"img\", \"src.gt-lg\", \"\"]],\n    inputs: {\n      \"src.xs\": \"src.xs\",\n      \"src.sm\": \"src.sm\",\n      \"src.md\": \"src.md\",\n      \"src.lg\": \"src.lg\",\n      \"src.xl\": \"src.xl\",\n      \"src.lt-sm\": \"src.lt-sm\",\n      \"src.lt-md\": \"src.lt-md\",\n      \"src.lt-lg\": \"src.lt-lg\",\n      \"src.lt-xl\": \"src.lt-xl\",\n      \"src.gt-xs\": \"src.gt-xs\",\n      \"src.gt-sm\": \"src.gt-sm\",\n      \"src.gt-md\": \"src.gt-md\",\n      \"src.gt-lg\": \"src.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultImgSrcDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet ClassDirective = /*#__PURE__*/(() => {\n  class ClassDirective extends BaseDirective2 {\n    constructor(elementRef, styler, marshal, iterableDiffers, keyValueDiffers, renderer2, ngClassInstance) {\n      super(elementRef, null, styler, marshal);\n      this.ngClassInstance = ngClassInstance;\n      this.DIRECTIVE_KEY = 'ngClass';\n\n      if (!this.ngClassInstance) {\n        // Create an instance NgClass Directive instance only if `ngClass=\"\"` has NOT been defined on\n        // the same host element; since the responsive variations may be defined...\n        this.ngClassInstance = new NgClass(iterableDiffers, keyValueDiffers, elementRef, renderer2);\n      }\n\n      this.init();\n      this.setValue('', '');\n    }\n    /**\n     * Capture class assignments so we cache the default classes\n     * which are merged with activated styles and used as fallbacks.\n     */\n\n\n    set klass(val) {\n      this.ngClassInstance.klass = val;\n      this.setValue(val, '');\n    }\n\n    updateWithValue(value) {\n      this.ngClassInstance.ngClass = value;\n      this.ngClassInstance.ngDoCheck();\n    } // ******************************************************************\n    // Lifecycle Hooks\n    // ******************************************************************\n\n    /**\n     * For ChangeDetectionStrategy.onPush and ngOnChanges() updates\n     */\n\n\n    ngDoCheck() {\n      this.ngClassInstance.ngDoCheck();\n    }\n\n  }\n\n  ClassDirective.ɵfac = function ClassDirective_Factory(t) {\n    return new (t || ClassDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i0.KeyValueDiffers), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.NgClass, 10));\n  };\n\n  ClassDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ClassDirective,\n    inputs: {\n      klass: [\"class\", \"klass\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return ClassDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst inputs$2 = ['ngClass', 'ngClass.xs', 'ngClass.sm', 'ngClass.md', 'ngClass.lg', 'ngClass.xl', 'ngClass.lt-sm', 'ngClass.lt-md', 'ngClass.lt-lg', 'ngClass.lt-xl', 'ngClass.gt-xs', 'ngClass.gt-sm', 'ngClass.gt-md', 'ngClass.gt-lg'];\nconst selector$2 = `\n  [ngClass], [ngClass.xs], [ngClass.sm], [ngClass.md], [ngClass.lg], [ngClass.xl],\n  [ngClass.lt-sm], [ngClass.lt-md], [ngClass.lt-lg], [ngClass.lt-xl],\n  [ngClass.gt-xs], [ngClass.gt-sm], [ngClass.gt-md], [ngClass.gt-lg]\n`;\n/**\n * Directive to add responsive support for ngClass.\n * This maintains the core functionality of 'ngClass' and adds responsive API\n * Note: this class is a no-op when rendered on the server\n */\n\nlet DefaultClassDirective = /*#__PURE__*/(() => {\n  class DefaultClassDirective extends ClassDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$2;\n    }\n\n  }\n\n  DefaultClassDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultClassDirective_BaseFactory;\n    return function DefaultClassDirective_Factory(t) {\n      return (ɵDefaultClassDirective_BaseFactory || (ɵDefaultClassDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultClassDirective)))(t || DefaultClassDirective);\n    };\n  }();\n\n  DefaultClassDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultClassDirective,\n    selectors: [[\"\", \"ngClass\", \"\"], [\"\", \"ngClass.xs\", \"\"], [\"\", \"ngClass.sm\", \"\"], [\"\", \"ngClass.md\", \"\"], [\"\", \"ngClass.lg\", \"\"], [\"\", \"ngClass.xl\", \"\"], [\"\", \"ngClass.lt-sm\", \"\"], [\"\", \"ngClass.lt-md\", \"\"], [\"\", \"ngClass.lt-lg\", \"\"], [\"\", \"ngClass.lt-xl\", \"\"], [\"\", \"ngClass.gt-xs\", \"\"], [\"\", \"ngClass.gt-sm\", \"\"], [\"\", \"ngClass.gt-md\", \"\"], [\"\", \"ngClass.gt-lg\", \"\"]],\n    inputs: {\n      ngClass: \"ngClass\",\n      \"ngClass.xs\": \"ngClass.xs\",\n      \"ngClass.sm\": \"ngClass.sm\",\n      \"ngClass.md\": \"ngClass.md\",\n      \"ngClass.lg\": \"ngClass.lg\",\n      \"ngClass.xl\": \"ngClass.xl\",\n      \"ngClass.lt-sm\": \"ngClass.lt-sm\",\n      \"ngClass.lt-md\": \"ngClass.lt-md\",\n      \"ngClass.lt-lg\": \"ngClass.lt-lg\",\n      \"ngClass.lt-xl\": \"ngClass.lt-xl\",\n      \"ngClass.gt-xs\": \"ngClass.gt-xs\",\n      \"ngClass.gt-sm\": \"ngClass.gt-sm\",\n      \"ngClass.gt-md\": \"ngClass.gt-md\",\n      \"ngClass.gt-lg\": \"ngClass.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultClassDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet ShowHideStyleBuilder = /*#__PURE__*/(() => {\n  class ShowHideStyleBuilder extends StyleBuilder {\n    buildStyles(show, parent) {\n      const shouldShow = show === 'true';\n      return {\n        'display': shouldShow ? parent.display || (parent.isServer ? 'initial' : '') : 'none'\n      };\n    }\n\n  }\n\n  ShowHideStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵShowHideStyleBuilder_BaseFactory;\n    return function ShowHideStyleBuilder_Factory(t) {\n      return (ɵShowHideStyleBuilder_BaseFactory || (ɵShowHideStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(ShowHideStyleBuilder)))(t || ShowHideStyleBuilder);\n    };\n  }();\n\n  ShowHideStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ShowHideStyleBuilder,\n    factory: ShowHideStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return ShowHideStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet ShowHideDirective = /*#__PURE__*/(() => {\n  class ShowHideDirective extends BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal, layoutConfig, platformId, serverModuleLoaded) {\n      super(elementRef, styleBuilder, styler, marshal);\n      this.layoutConfig = layoutConfig;\n      this.platformId = platformId;\n      this.serverModuleLoaded = serverModuleLoaded;\n      this.DIRECTIVE_KEY = 'show-hide';\n      /** Original DOM Element CSS display style */\n\n      this.display = '';\n      this.hasLayout = false;\n      this.hasFlexChild = false;\n    } // *********************************************\n    // Lifecycle Methods\n    // *********************************************\n\n\n    ngAfterViewInit() {\n      this.trackExtraTriggers();\n      const children = Array.from(this.nativeElement.children);\n\n      for (let i = 0; i < children.length; i++) {\n        if (this.marshal.hasValue(children[i], 'flex')) {\n          this.hasFlexChild = true;\n          break;\n        }\n      }\n\n      if (DISPLAY_MAP.has(this.nativeElement)) {\n        this.display = DISPLAY_MAP.get(this.nativeElement);\n      } else {\n        this.display = this.getDisplayStyle();\n        DISPLAY_MAP.set(this.nativeElement, this.display);\n      }\n\n      this.init(); // set the default to show unless explicitly overridden\n\n      const defaultValue = this.marshal.getValue(this.nativeElement, this.DIRECTIVE_KEY, '');\n\n      if (defaultValue === undefined || defaultValue === '') {\n        this.setValue(true, '');\n      } else {\n        this.triggerUpdate();\n      }\n    }\n    /**\n     * On changes to any @Input properties...\n     * Default to use the non-responsive Input value ('fxShow')\n     * Then conditionally override with the mq-activated Input's current value\n     */\n\n\n    ngOnChanges(changes) {\n      Object.keys(changes).forEach(key => {\n        if (this.inputs.indexOf(key) !== -1) {\n          const inputKey = key.split('.');\n          const bp = inputKey.slice(1).join('.');\n          const inputValue = changes[key].currentValue;\n          let shouldShow = inputValue !== '' ? inputValue !== 0 ? coerceBooleanProperty(inputValue) : false : true;\n\n          if (inputKey[0] === 'fxHide') {\n            shouldShow = !shouldShow;\n          }\n\n          this.setValue(shouldShow, bp);\n        }\n      });\n    } // *********************************************\n    // Protected methods\n    // *********************************************\n\n    /**\n     *  Watch for these extra triggers to update fxShow, fxHide stylings\n     */\n\n\n    trackExtraTriggers() {\n      this.hasLayout = this.marshal.hasValue(this.nativeElement, 'layout');\n      ['layout', 'layout-align'].forEach(key => {\n        this.marshal.trackValue(this.nativeElement, key).pipe(takeUntil(this.destroySubject)).subscribe(this.triggerUpdate.bind(this));\n      });\n    }\n    /**\n     * Override accessor to the current HTMLElement's `display` style\n     * Note: Show/Hide will not change the display to 'flex' but will set it to 'block'\n     * unless it was already explicitly specified inline or in a CSS stylesheet.\n     */\n\n\n    getDisplayStyle() {\n      return this.hasLayout || this.hasFlexChild && this.layoutConfig.addFlexToParent ? 'flex' : this.styler.lookupStyle(this.nativeElement, 'display', true);\n    }\n    /** Validate the visibility value and then update the host's inline display style */\n\n\n    updateWithValue(value = true) {\n      if (value === '') {\n        return;\n      }\n\n      const isServer = isPlatformServer(this.platformId);\n      this.addStyles(value ? 'true' : 'false', {\n        display: this.display,\n        isServer\n      });\n\n      if (isServer && this.serverModuleLoaded) {\n        this.nativeElement.style.setProperty('display', '');\n      }\n\n      this.marshal.triggerUpdate(this.parentElement, 'layout-gap');\n    }\n\n  }\n\n  ShowHideDirective.ɵfac = function ShowHideDirective_Factory(t) {\n    return new (t || ShowHideDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ShowHideStyleBuilder), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller), i0.ɵɵdirectiveInject(LAYOUT_CONFIG), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(SERVER_TOKEN));\n  };\n\n  ShowHideDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ShowHideDirective,\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n  return ShowHideDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst DISPLAY_MAP = /*#__PURE__*/new WeakMap();\nconst inputs$1 = ['fxShow', 'fxShow.print', 'fxShow.xs', 'fxShow.sm', 'fxShow.md', 'fxShow.lg', 'fxShow.xl', 'fxShow.lt-sm', 'fxShow.lt-md', 'fxShow.lt-lg', 'fxShow.lt-xl', 'fxShow.gt-xs', 'fxShow.gt-sm', 'fxShow.gt-md', 'fxShow.gt-lg', 'fxHide', 'fxHide.print', 'fxHide.xs', 'fxHide.sm', 'fxHide.md', 'fxHide.lg', 'fxHide.xl', 'fxHide.lt-sm', 'fxHide.lt-md', 'fxHide.lt-lg', 'fxHide.lt-xl', 'fxHide.gt-xs', 'fxHide.gt-sm', 'fxHide.gt-md', 'fxHide.gt-lg'];\nconst selector$1 = `\n  [fxShow], [fxShow.print],\n  [fxShow.xs], [fxShow.sm], [fxShow.md], [fxShow.lg], [fxShow.xl],\n  [fxShow.lt-sm], [fxShow.lt-md], [fxShow.lt-lg], [fxShow.lt-xl],\n  [fxShow.gt-xs], [fxShow.gt-sm], [fxShow.gt-md], [fxShow.gt-lg],\n  [fxHide], [fxHide.print],\n  [fxHide.xs], [fxHide.sm], [fxHide.md], [fxHide.lg], [fxHide.xl],\n  [fxHide.lt-sm], [fxHide.lt-md], [fxHide.lt-lg], [fxHide.lt-xl],\n  [fxHide.gt-xs], [fxHide.gt-sm], [fxHide.gt-md], [fxHide.gt-lg]\n`;\n/**\n * 'show' Layout API directive\n */\n\nlet DefaultShowHideDirective = /*#__PURE__*/(() => {\n  class DefaultShowHideDirective extends ShowHideDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$1;\n    }\n\n  }\n\n  DefaultShowHideDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultShowHideDirective_BaseFactory;\n    return function DefaultShowHideDirective_Factory(t) {\n      return (ɵDefaultShowHideDirective_BaseFactory || (ɵDefaultShowHideDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultShowHideDirective)))(t || DefaultShowHideDirective);\n    };\n  }();\n\n  DefaultShowHideDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultShowHideDirective,\n    selectors: [[\"\", \"fxShow\", \"\"], [\"\", \"fxShow.print\", \"\"], [\"\", \"fxShow.xs\", \"\"], [\"\", \"fxShow.sm\", \"\"], [\"\", \"fxShow.md\", \"\"], [\"\", \"fxShow.lg\", \"\"], [\"\", \"fxShow.xl\", \"\"], [\"\", \"fxShow.lt-sm\", \"\"], [\"\", \"fxShow.lt-md\", \"\"], [\"\", \"fxShow.lt-lg\", \"\"], [\"\", \"fxShow.lt-xl\", \"\"], [\"\", \"fxShow.gt-xs\", \"\"], [\"\", \"fxShow.gt-sm\", \"\"], [\"\", \"fxShow.gt-md\", \"\"], [\"\", \"fxShow.gt-lg\", \"\"], [\"\", \"fxHide\", \"\"], [\"\", \"fxHide.print\", \"\"], [\"\", \"fxHide.xs\", \"\"], [\"\", \"fxHide.sm\", \"\"], [\"\", \"fxHide.md\", \"\"], [\"\", \"fxHide.lg\", \"\"], [\"\", \"fxHide.xl\", \"\"], [\"\", \"fxHide.lt-sm\", \"\"], [\"\", \"fxHide.lt-md\", \"\"], [\"\", \"fxHide.lt-lg\", \"\"], [\"\", \"fxHide.lt-xl\", \"\"], [\"\", \"fxHide.gt-xs\", \"\"], [\"\", \"fxHide.gt-sm\", \"\"], [\"\", \"fxHide.gt-md\", \"\"], [\"\", \"fxHide.gt-lg\", \"\"]],\n    inputs: {\n      fxShow: \"fxShow\",\n      \"fxShow.print\": \"fxShow.print\",\n      \"fxShow.xs\": \"fxShow.xs\",\n      \"fxShow.sm\": \"fxShow.sm\",\n      \"fxShow.md\": \"fxShow.md\",\n      \"fxShow.lg\": \"fxShow.lg\",\n      \"fxShow.xl\": \"fxShow.xl\",\n      \"fxShow.lt-sm\": \"fxShow.lt-sm\",\n      \"fxShow.lt-md\": \"fxShow.lt-md\",\n      \"fxShow.lt-lg\": \"fxShow.lt-lg\",\n      \"fxShow.lt-xl\": \"fxShow.lt-xl\",\n      \"fxShow.gt-xs\": \"fxShow.gt-xs\",\n      \"fxShow.gt-sm\": \"fxShow.gt-sm\",\n      \"fxShow.gt-md\": \"fxShow.gt-md\",\n      \"fxShow.gt-lg\": \"fxShow.gt-lg\",\n      fxHide: \"fxHide\",\n      \"fxHide.print\": \"fxHide.print\",\n      \"fxHide.xs\": \"fxHide.xs\",\n      \"fxHide.sm\": \"fxHide.sm\",\n      \"fxHide.md\": \"fxHide.md\",\n      \"fxHide.lg\": \"fxHide.lg\",\n      \"fxHide.xl\": \"fxHide.xl\",\n      \"fxHide.lt-sm\": \"fxHide.lt-sm\",\n      \"fxHide.lt-md\": \"fxHide.lt-md\",\n      \"fxHide.lt-lg\": \"fxHide.lt-lg\",\n      \"fxHide.lt-xl\": \"fxHide.lt-xl\",\n      \"fxHide.gt-xs\": \"fxHide.gt-xs\",\n      \"fxHide.gt-sm\": \"fxHide.gt-sm\",\n      \"fxHide.gt-md\": \"fxHide.gt-md\",\n      \"fxHide.gt-lg\": \"fxHide.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultShowHideDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** NgStyle allowed inputs */\n\n\nclass NgStyleKeyValue {\n  constructor(key, value, noQuotes = true) {\n    this.key = key;\n    this.value = value;\n    this.key = noQuotes ? key.replace(/['\"]/g, '').trim() : key.trim();\n    this.value = noQuotes ? value.replace(/['\"]/g, '').trim() : value.trim();\n    this.value = this.value.replace(/;/, '');\n  }\n\n}\n\nfunction getType(target) {\n  let what = typeof target;\n\n  if (what === 'object') {\n    return target.constructor === Array ? 'array' : target.constructor === Set ? 'set' : 'object';\n  }\n\n  return what;\n}\n/**\n * Split string of key:value pairs into Array of k-v pairs\n * e.g.  'key:value; key:value; key:value;' -> ['key:value',...]\n */\n\n\nfunction buildRawList(source, delimiter = ';') {\n  return String(source).trim().split(delimiter).map(val => val.trim()).filter(val => val !== '');\n}\n/** Convert array of key:value strings to a iterable map object */\n\n\nfunction buildMapFromList$1(styles, sanitize) {\n  const sanitizeValue = it => {\n    if (sanitize) {\n      it.value = sanitize(it.value);\n    }\n\n    return it;\n  };\n\n  return styles.map(stringToKeyValue).filter(entry => !!entry).map(sanitizeValue).reduce(keyValuesToMap, {});\n}\n/** Convert Set<string> or raw Object to an iterable NgStyleMap */\n\n\nfunction buildMapFromSet(source, sanitize) {\n  let list = [];\n\n  if (getType(source) === 'set') {\n    source.forEach(entry => list.push(entry));\n  } else {\n    Object.keys(source).forEach(key => {\n      list.push(`${key}:${source[key]}`);\n    });\n  }\n\n  return buildMapFromList$1(list, sanitize);\n}\n/** Convert 'key:value' -> [key, value] */\n\n\nfunction stringToKeyValue(it) {\n  const [key, ...vals] = it.split(':');\n  return new NgStyleKeyValue(key, vals.join(':'));\n}\n/** Convert [ [key,value] ] -> { key : value } */\n\n\nfunction keyValuesToMap(map, entry) {\n  if (!!entry.key) {\n    map[entry.key] = entry.value;\n  }\n\n  return map;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet StyleDirective = /*#__PURE__*/(() => {\n  class StyleDirective extends BaseDirective2 {\n    constructor(elementRef, styler, marshal, sanitizer, differs, renderer2, ngStyleInstance, serverLoaded, platformId) {\n      var _a;\n\n      super(elementRef, null, styler, marshal);\n      this.sanitizer = sanitizer;\n      this.ngStyleInstance = ngStyleInstance;\n      this.DIRECTIVE_KEY = 'ngStyle';\n\n      if (!this.ngStyleInstance) {\n        // Create an instance NgStyle Directive instance only if `ngStyle=\"\"` has NOT been\n        // defined on the same host element; since the responsive variations may be defined...\n        this.ngStyleInstance = new NgStyle(elementRef, differs, renderer2);\n      }\n\n      this.init();\n      const styles = (_a = this.nativeElement.getAttribute('style')) !== null && _a !== void 0 ? _a : '';\n      this.fallbackStyles = this.buildStyleMap(styles);\n      this.isServer = serverLoaded && isPlatformServer(platformId);\n    }\n    /** Add generated styles */\n\n\n    updateWithValue(value) {\n      const styles = this.buildStyleMap(value);\n      this.ngStyleInstance.ngStyle = Object.assign(Object.assign({}, this.fallbackStyles), styles);\n\n      if (this.isServer) {\n        this.applyStyleToElement(styles);\n      }\n\n      this.ngStyleInstance.ngDoCheck();\n    }\n    /** Remove generated styles */\n\n\n    clearStyles() {\n      this.ngStyleInstance.ngStyle = this.fallbackStyles;\n      this.ngStyleInstance.ngDoCheck();\n    }\n    /**\n     * Convert raw strings to ngStyleMap; which is required by ngStyle\n     * NOTE: Raw string key-value pairs MUST be delimited by `;`\n     *       Comma-delimiters are not supported due to complexities of\n     *       possible style values such as `rgba(x,x,x,x)` and others\n     */\n\n\n    buildStyleMap(styles) {\n      // Always safe-guard (aka sanitize) style property values\n      const sanitizer = val => {\n        var _a;\n\n        return (_a = this.sanitizer.sanitize(SecurityContext.STYLE, val)) !== null && _a !== void 0 ? _a : '';\n      };\n\n      if (styles) {\n        switch (getType(styles)) {\n          case 'string':\n            return buildMapFromList(buildRawList(styles), sanitizer);\n\n          case 'array':\n            return buildMapFromList(styles, sanitizer);\n\n          case 'set':\n            return buildMapFromSet(styles, sanitizer);\n\n          default:\n            return buildMapFromSet(styles, sanitizer);\n        }\n      }\n\n      return {};\n    } // ******************************************************************\n    // Lifecycle Hooks\n    // ******************************************************************\n\n    /** For ChangeDetectionStrategy.onPush and ngOnChanges() updates */\n\n\n    ngDoCheck() {\n      this.ngStyleInstance.ngDoCheck();\n    }\n\n  }\n\n  StyleDirective.ɵfac = function StyleDirective_Factory(t) {\n    return new (t || StyleDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller), i0.ɵɵdirectiveInject(i2$1.DomSanitizer), i0.ɵɵdirectiveInject(i0.KeyValueDiffers), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.NgStyle, 10), i0.ɵɵdirectiveInject(SERVER_TOKEN), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n\n  StyleDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: StyleDirective,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return StyleDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst inputs = ['ngStyle', 'ngStyle.xs', 'ngStyle.sm', 'ngStyle.md', 'ngStyle.lg', 'ngStyle.xl', 'ngStyle.lt-sm', 'ngStyle.lt-md', 'ngStyle.lt-lg', 'ngStyle.lt-xl', 'ngStyle.gt-xs', 'ngStyle.gt-sm', 'ngStyle.gt-md', 'ngStyle.gt-lg'];\nconst selector = `\n  [ngStyle],\n  [ngStyle.xs], [ngStyle.sm], [ngStyle.md], [ngStyle.lg], [ngStyle.xl],\n  [ngStyle.lt-sm], [ngStyle.lt-md], [ngStyle.lt-lg], [ngStyle.lt-xl],\n  [ngStyle.gt-xs], [ngStyle.gt-sm], [ngStyle.gt-md], [ngStyle.gt-lg]\n`;\n/**\n * Directive to add responsive support for ngStyle.\n *\n */\n\nlet DefaultStyleDirective = /*#__PURE__*/(() => {\n  class DefaultStyleDirective extends StyleDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs;\n    }\n\n  }\n\n  DefaultStyleDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultStyleDirective_BaseFactory;\n    return function DefaultStyleDirective_Factory(t) {\n      return (ɵDefaultStyleDirective_BaseFactory || (ɵDefaultStyleDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultStyleDirective)))(t || DefaultStyleDirective);\n    };\n  }();\n\n  DefaultStyleDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultStyleDirective,\n    selectors: [[\"\", \"ngStyle\", \"\"], [\"\", \"ngStyle.xs\", \"\"], [\"\", \"ngStyle.sm\", \"\"], [\"\", \"ngStyle.md\", \"\"], [\"\", \"ngStyle.lg\", \"\"], [\"\", \"ngStyle.xl\", \"\"], [\"\", \"ngStyle.lt-sm\", \"\"], [\"\", \"ngStyle.lt-md\", \"\"], [\"\", \"ngStyle.lt-lg\", \"\"], [\"\", \"ngStyle.lt-xl\", \"\"], [\"\", \"ngStyle.gt-xs\", \"\"], [\"\", \"ngStyle.gt-sm\", \"\"], [\"\", \"ngStyle.gt-md\", \"\"], [\"\", \"ngStyle.gt-lg\", \"\"]],\n    inputs: {\n      ngStyle: \"ngStyle\",\n      \"ngStyle.xs\": \"ngStyle.xs\",\n      \"ngStyle.sm\": \"ngStyle.sm\",\n      \"ngStyle.md\": \"ngStyle.md\",\n      \"ngStyle.lg\": \"ngStyle.lg\",\n      \"ngStyle.xl\": \"ngStyle.xl\",\n      \"ngStyle.lt-sm\": \"ngStyle.lt-sm\",\n      \"ngStyle.lt-md\": \"ngStyle.lt-md\",\n      \"ngStyle.lt-lg\": \"ngStyle.lt-lg\",\n      \"ngStyle.lt-xl\": \"ngStyle.lt-xl\",\n      \"ngStyle.gt-xs\": \"ngStyle.gt-xs\",\n      \"ngStyle.gt-sm\": \"ngStyle.gt-sm\",\n      \"ngStyle.gt-md\": \"ngStyle.gt-md\",\n      \"ngStyle.gt-lg\": \"ngStyle.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultStyleDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Build a styles map from a list of styles, while sanitizing bad values first */\n\n\nfunction buildMapFromList(styles, sanitize) {\n  const sanitizeValue = it => {\n    if (sanitize) {\n      it.value = sanitize(it.value);\n    }\n\n    return it;\n  };\n\n  return styles.map(stringToKeyValue).filter(entry => !!entry).map(sanitizeValue).reduce(keyValuesToMap, {});\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst ALL_DIRECTIVES = [DefaultShowHideDirective, DefaultClassDirective, DefaultStyleDirective, DefaultImgSrcDirective];\n/**\n * *****************************************************************\n * Define module for the Extended API\n * *****************************************************************\n */\n\nlet ExtendedModule = /*#__PURE__*/(() => {\n  class ExtendedModule {}\n\n  ExtendedModule.ɵfac = function ExtendedModule_Factory(t) {\n    return new (t || ExtendedModule)();\n  };\n\n  ExtendedModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ExtendedModule\n  });\n  ExtendedModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CoreModule]]\n  });\n  return ExtendedModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ClassDirective, DefaultClassDirective, DefaultImgSrcDirective, DefaultShowHideDirective, DefaultStyleDirective, ExtendedModule, ImgSrcDirective, ImgSrcStyleBuilder, ShowHideDirective, ShowHideStyleBuilder, StyleDirective }; //# sourceMappingURL=angular-flex-layout-extended.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}