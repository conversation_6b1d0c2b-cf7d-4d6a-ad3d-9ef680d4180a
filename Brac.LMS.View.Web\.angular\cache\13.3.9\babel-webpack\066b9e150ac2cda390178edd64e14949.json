{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Component, ChangeDetectionStrategy, Input, Directive, Output, NgModule } from '@angular/core';\nimport { getBsVer, parseTriggers } from 'ngx-bootstrap/utils';\nimport * as i3 from 'ngx-bootstrap/positioning';\nimport { PlacementForBs5, checkMargins, PositioningService } from 'ngx-bootstrap/positioning';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { timer } from 'rxjs';\nimport * as i2$1 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\n/**\n * Configuration service for the Popover directive.\n * You can inject this service, typically in your root component, and customize\n * the values of its properties in order to provide default values for all the\n * popovers used in the application.\n */\n\nfunction PopoverContainerComponent_h3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.title);\n  }\n}\n\nconst _c0 = [\"*\"];\nlet PopoverConfig = /*#__PURE__*/(() => {\n  class PopoverConfig {\n    constructor() {\n      /** sets disable adaptive position */\n      this.adaptivePosition = true;\n      /**\n       * Placement of a popover. Accepts: \"top\", \"bottom\", \"left\", \"right\", \"auto\"\n       */\n\n      this.placement = 'top';\n      /**\n       * Specifies events that should trigger. Supports a space separated list of\n       * event names.\n       */\n\n      this.triggers = 'click';\n      this.outsideClick = false;\n      /** delay before showing the tooltip */\n\n      this.delay = 0;\n    }\n\n  }\n\n  PopoverConfig.ɵfac = function PopoverConfig_Factory(t) {\n    return new (t || PopoverConfig)();\n  };\n\n  PopoverConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PopoverConfig,\n    factory: PopoverConfig.ɵfac,\n    providedIn: 'root'\n  });\n  return PopoverConfig;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet PopoverContainerComponent = /*#__PURE__*/(() => {\n  class PopoverContainerComponent {\n    constructor(config) {\n      this._placement = 'top';\n      Object.assign(this, config);\n    }\n\n    set placement(value) {\n      if (!this._bsVersions.isBs5) {\n        this._placement = value;\n      } else {\n        this._placement = PlacementForBs5[value];\n      }\n    }\n\n    get _bsVersions() {\n      return getBsVer();\n    }\n\n    checkMarginNecessity() {\n      return checkMargins(this._placement);\n    }\n\n  }\n\n  PopoverContainerComponent.ɵfac = function PopoverContainerComponent_Factory(t) {\n    return new (t || PopoverContainerComponent)(i0.ɵɵdirectiveInject(PopoverConfig));\n  };\n\n  PopoverContainerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: PopoverContainerComponent,\n    selectors: [[\"popover-container\"]],\n    hostAttrs: [\"role\", \"tooltip\", 2, \"display\", \"block\"],\n    hostVars: 7,\n    hostBindings: function PopoverContainerComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx.popoverId);\n        i0.ɵɵclassMap(\"popover in popover-\" + ctx._placement + \" \" + \"bs-popover-\" + ctx._placement + \" \" + ctx._placement + \" \" + ctx.containerClass + \" \" + ctx.checkMarginNecessity());\n        i0.ɵɵclassProp(\"show\", !ctx._bsVersions.isBs3)(\"bs3\", ctx._bsVersions.isBs3);\n      }\n    },\n    inputs: {\n      placement: \"placement\",\n      title: \"title\"\n    },\n    ngContentSelectors: _c0,\n    decls: 4,\n    vars: 1,\n    consts: [[1, \"popover-arrow\", \"arrow\"], [\"class\", \"popover-title popover-header\", 4, \"ngIf\"], [1, \"popover-content\", \"popover-body\"], [1, \"popover-title\", \"popover-header\"]],\n    template: function PopoverContainerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelement(0, \"div\", 0);\n        i0.ɵɵtemplate(1, PopoverContainerComponent_h3_1_Template, 2, 1, \"h3\", 1);\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵprojection(3);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.title);\n      }\n    },\n    directives: [i2.NgIf],\n    styles: [\".bs3.popover-top[_nghost-%COMP%]{margin-bottom:10px}.bs3.popover.top[_nghost-%COMP%] > .arrow[_ngcontent-%COMP%]{margin-left:-2px}.bs3.popover.top[_nghost-%COMP%]{margin-bottom:10px}.popover.bottom[_nghost-%COMP%] > .arrow[_ngcontent-%COMP%]{margin-left:-4px}.bs3.bs-popover-left[_nghost-%COMP%]{margin-right:.5rem}.bs3.bs-popover-right[_nghost-%COMP%]   .arrow[_ngcontent-%COMP%], .bs3.bs-popover-left[_nghost-%COMP%]   .arrow[_ngcontent-%COMP%]{margin:.3rem 0}\"],\n    changeDetection: 0\n  });\n  return PopoverContainerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet id = 0;\n/**\n * A lightweight, extensible directive for fancy popover creation.\n */\n\nlet PopoverDirective = /*#__PURE__*/(() => {\n  class PopoverDirective {\n    constructor(_config, _elementRef, _renderer, _viewContainerRef, cis, _positionService) {\n      this._elementRef = _elementRef;\n      this._renderer = _renderer;\n      this._positionService = _positionService;\n      /** unique id popover - use for aria-describedby */\n\n      this.popoverId = id++;\n      /** sets disable adaptive position */\n\n      this.adaptivePosition = true;\n      /**\n       * Placement of a popover. Accepts: \"top\", \"bottom\", \"left\", \"right\"\n       */\n\n      this.placement = 'top';\n      /**\n       * Close popover on outside click\n       */\n\n      this.outsideClick = false;\n      /**\n       * Specifies events that should trigger. Supports a space separated list of\n       * event names.\n       */\n\n      this.triggers = 'click';\n      /**\n       * Css class for popover container\n       */\n\n      this.containerClass = '';\n      /**\n       * Delay before showing the tooltip\n       */\n\n      this.delay = 0;\n      this._isInited = false;\n      this._popover = cis.createLoader(_elementRef, _viewContainerRef, _renderer).provide({\n        provide: PopoverConfig,\n        useValue: _config\n      });\n      Object.assign(this, _config);\n      this.onShown = this._popover.onShown;\n      this.onHidden = this._popover.onHidden; // fix: no focus on button on Mac OS #1795\n\n      if (typeof window !== 'undefined') {\n        _elementRef.nativeElement.addEventListener('click', function () {\n          try {\n            _elementRef.nativeElement.focus();\n          } catch (err) {\n            return;\n          }\n        });\n      }\n    }\n    /**\n     * Returns whether or not the popover is currently being shown\n     */\n\n\n    get isOpen() {\n      return this._popover.isShown;\n    }\n\n    set isOpen(value) {\n      if (value) {\n        this.show();\n      } else {\n        this.hide();\n      }\n    }\n    /**\n     * Set attribute aria-describedBy for element directive and\n     * set id for the popover\n     */\n\n\n    setAriaDescribedBy() {\n      this._ariaDescribedby = this.isOpen ? `ngx-popover-${this.popoverId}` : void 0;\n\n      if (this._ariaDescribedby) {\n        if (this._popover.instance) {\n          this._popover.instance.popoverId = this._ariaDescribedby;\n        }\n\n        this._renderer.setAttribute(this._elementRef.nativeElement, 'aria-describedby', this._ariaDescribedby);\n      } else {\n        this._renderer.removeAttribute(this._elementRef.nativeElement, 'aria-describedby');\n      }\n    }\n    /**\n     * Opens an element’s popover. This is considered a “manual” triggering of\n     * the popover.\n     */\n\n\n    show() {\n      if (this._popover.isShown || !this.popover || this._delayTimeoutId) {\n        return;\n      }\n\n      this._positionService.setOptions({\n        modifiers: {\n          flip: {\n            enabled: this.adaptivePosition\n          },\n          preventOverflow: {\n            enabled: this.adaptivePosition\n          }\n        }\n      });\n\n      const showPopover = () => {\n        if (this._delayTimeoutId) {\n          this._delayTimeoutId = undefined;\n        }\n\n        this._popover.attach(PopoverContainerComponent).to(this.container).position({\n          attachment: this.placement\n        }).show({\n          content: this.popover,\n          context: this.popoverContext,\n          placement: this.placement,\n          title: this.popoverTitle,\n          containerClass: this.containerClass\n        });\n\n        if (!this.adaptivePosition && this._popover._componentRef) {\n          this._positionService.calcPosition();\n\n          this._positionService.deletePositionElement(this._popover._componentRef.location);\n        }\n\n        this.isOpen = true;\n        this.setAriaDescribedBy();\n      };\n\n      const cancelDelayedTooltipShowing = () => {\n        if (this._popoverCancelShowFn) {\n          this._popoverCancelShowFn();\n        }\n      };\n\n      if (this.delay) {\n        const _timer = timer(this.delay).subscribe(() => {\n          showPopover();\n          cancelDelayedTooltipShowing();\n        });\n\n        if (this.triggers) {\n          parseTriggers(this.triggers).forEach(trigger => {\n            if (!trigger.close) {\n              return;\n            }\n\n            this._popoverCancelShowFn = this._renderer.listen(this._elementRef.nativeElement, trigger.close, () => {\n              _timer.unsubscribe();\n\n              cancelDelayedTooltipShowing();\n            });\n          });\n        }\n      } else {\n        showPopover();\n      }\n    }\n    /**\n     * Closes an element’s popover. This is considered a “manual” triggering of\n     * the popover.\n     */\n\n\n    hide() {\n      if (this._delayTimeoutId) {\n        clearTimeout(this._delayTimeoutId);\n        this._delayTimeoutId = undefined;\n      }\n\n      if (this.isOpen) {\n        this._popover.hide();\n\n        this.setAriaDescribedBy();\n        this.isOpen = false;\n      }\n    }\n    /**\n     * Toggles an element’s popover. This is considered a “manual” triggering of\n     * the popover.\n     */\n\n\n    toggle() {\n      if (this.isOpen) {\n        return this.hide();\n      }\n\n      this.show();\n    }\n\n    ngOnInit() {\n      // fix: seems there are an issue with `routerLinkActive`\n      // which result in duplicated call ngOnInit without call to ngOnDestroy\n      // read more: https://github.com/valor-software/ngx-bootstrap/issues/1885\n      if (this._isInited) {\n        return;\n      }\n\n      this._isInited = true;\n\n      this._popover.listen({\n        triggers: this.triggers,\n        outsideClick: this.outsideClick,\n        show: () => this.show(),\n        hide: () => this.hide()\n      });\n    }\n\n    ngOnDestroy() {\n      this._popover.dispose();\n    }\n\n  }\n\n  PopoverDirective.ɵfac = function PopoverDirective_Factory(t) {\n    return new (t || PopoverDirective)(i0.ɵɵdirectiveInject(PopoverConfig), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2$1.ComponentLoaderFactory), i0.ɵɵdirectiveInject(i3.PositioningService));\n  };\n\n  PopoverDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: PopoverDirective,\n    selectors: [[\"\", \"popover\", \"\"]],\n    inputs: {\n      adaptivePosition: \"adaptivePosition\",\n      popover: \"popover\",\n      popoverContext: \"popoverContext\",\n      popoverTitle: \"popoverTitle\",\n      placement: \"placement\",\n      outsideClick: \"outsideClick\",\n      triggers: \"triggers\",\n      container: \"container\",\n      containerClass: \"containerClass\",\n      isOpen: \"isOpen\",\n      delay: \"delay\"\n    },\n    outputs: {\n      onShown: \"onShown\",\n      onHidden: \"onHidden\"\n    },\n    exportAs: [\"bs-popover\"]\n  });\n  return PopoverDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet PopoverModule = /*#__PURE__*/(() => {\n  class PopoverModule {\n    static forRoot() {\n      return {\n        ngModule: PopoverModule,\n        providers: [ComponentLoaderFactory, PositioningService]\n      };\n    }\n\n  }\n\n  PopoverModule.ɵfac = function PopoverModule_Factory(t) {\n    return new (t || PopoverModule)();\n  };\n\n  PopoverModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PopoverModule\n  });\n  PopoverModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule]]\n  });\n  return PopoverModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { PopoverConfig, PopoverContainerComponent, PopoverDirective, PopoverModule }; //# sourceMappingURL=ngx-bootstrap-popover.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}