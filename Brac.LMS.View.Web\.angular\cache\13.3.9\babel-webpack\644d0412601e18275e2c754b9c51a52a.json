{"ast": null, "code": "import { Observable } from '../../Observable';\nimport { Subscription } from '../../Subscription';\nimport { performanceTimestampProvider } from '../../scheduler/performanceTimestampProvider';\nimport { animationFrameProvider } from '../../scheduler/animationFrameProvider';\nexport function animationFrames(timestampProvider) {\n  return timestampProvider ? animationFramesFactory(timestampProvider) : DEFAULT_ANIMATION_FRAMES;\n}\n\nfunction animationFramesFactory(timestampProvider) {\n  const {\n    schedule\n  } = animationFrameProvider;\n  return new Observable(subscriber => {\n    const subscription = new Subscription();\n    const provider = timestampProvider || performanceTimestampProvider;\n    const start = provider.now();\n\n    const run = timestamp => {\n      const now = provider.now();\n      subscriber.next({\n        timestamp: timestampProvider ? now : timestamp,\n        elapsed: now - start\n      });\n\n      if (!subscriber.closed) {\n        subscription.add(schedule(run));\n      }\n    };\n\n    subscription.add(schedule(run));\n    return subscription;\n  });\n}\n\nconst DEFAULT_ANIMATION_FRAMES = animationFramesFactory(); //# sourceMappingURL=animationFrames.js.map", "map": null, "metadata": {}, "sourceType": "module"}