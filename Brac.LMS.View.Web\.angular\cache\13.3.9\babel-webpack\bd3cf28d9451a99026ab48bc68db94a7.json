{"ast": null, "code": "//! moment.js locale configuration\n//! locale : German [de]\n//! author : lluchs : https://github.com/lluchs\n//! author: <PERSON><PERSON><PERSON>: https://github.com/Oire\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mik01aj\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  function processRelativeTime(number, withoutSuffix, key, isFuture) {\n    var format = {\n      m: ['eine Minute', 'einer Minute'],\n      h: ['eine Stunde', 'einer Stunde'],\n      d: ['ein Tag', 'einem Tag'],\n      dd: [number + ' Tage', number + ' Tagen'],\n      w: ['eine Woche', 'einer Woche'],\n      M: ['ein <PERSON><PERSON>', 'einem Monat'],\n      MM: [number + ' <PERSON><PERSON>', number + ' <PERSON><PERSON>'],\n      y: ['ein Jahr', 'einem Jahr'],\n      yy: [number + ' Jahre', number + ' Jahren']\n    };\n    return withoutSuffix ? format[key][0] : format[key][1];\n  }\n\n  var de = moment.defineLocale('de', {\n    months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split('_'),\n    monthsShort: 'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split('_'),\n    weekdaysShort: 'So._Mo._Di._Mi._Do._Fr._Sa.'.split('_'),\n    weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY HH:mm',\n      LLLL: 'dddd, D. MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[heute um] LT [Uhr]',\n      sameElse: 'L',\n      nextDay: '[morgen um] LT [Uhr]',\n      nextWeek: 'dddd [um] LT [Uhr]',\n      lastDay: '[gestern um] LT [Uhr]',\n      lastWeek: '[letzten] dddd [um] LT [Uhr]'\n    },\n    relativeTime: {\n      future: 'in %s',\n      past: 'vor %s',\n      s: 'ein paar Sekunden',\n      ss: '%d Sekunden',\n      m: processRelativeTime,\n      mm: '%d Minuten',\n      h: processRelativeTime,\n      hh: '%d Stunden',\n      d: processRelativeTime,\n      dd: processRelativeTime,\n      w: processRelativeTime,\n      ww: '%d Wochen',\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return de;\n});", "map": null, "metadata": {}, "sourceType": "script"}