{"ast": null, "code": "// pagination.component.ts\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./pagination.service\";\nimport * as i2 from \"@angular/common\";\n\nfunction PaginationComponent_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 8);\n    i0.ɵɵelementStart(1, \"a\", 9);\n    i0.ɵɵlistener(\"click\", function PaginationComponent_li_2_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return ctx_r7.setPage(ctx_r7.getPrevious10(ctx_r7.activePage));\n    });\n    i0.ɵɵelement(2, \"i\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction PaginationComponent_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 11);\n    i0.ɵɵelementStart(1, \"a\", 9);\n    i0.ɵɵlistener(\"click\", function PaginationComponent_li_3_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return ctx_r9.setPage(ctx_r9.activePage - 1);\n    });\n    i0.ɵɵelement(2, \"i\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction PaginationComponent_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 13);\n    i0.ɵɵelementStart(1, \"a\", 14);\n    i0.ɵɵtext(2, \"...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction PaginationComponent_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 15);\n    i0.ɵɵelementStart(1, \"a\", 9);\n    i0.ɵɵlistener(\"click\", function PaginationComponent_li_5_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const i_r12 = restoredCtx.index;\n      const ctx_r13 = i0.ɵɵnextContext();\n      return ctx_r13.setPage(i_r12);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r12 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r3.list[i_r12].ClassName);\n    i0.ɵɵclassProp(\"active\", i_r12 === ctx_r3.activePage);\n    i0.ɵɵproperty(\"title\", ctx_r3.list[i_r12].ClassName !== \"\" ? ctx_r3.list[i_r12].ClassName : ctx_r3.title)(\"hidden\", i_r12 < ctx_r3.startPage || i_r12 >= ctx_r3.activePage + 1 && i_r12 >= ctx_r3.paginationControlToShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r12 + 1);\n  }\n}\n\nfunction PaginationComponent_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 13);\n    i0.ɵɵelementStart(1, \"a\", 14);\n    i0.ɵɵtext(2, \"...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction PaginationComponent_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 16);\n    i0.ɵɵelementStart(1, \"a\", 9);\n    i0.ɵɵlistener(\"click\", function PaginationComponent_li_7_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return ctx_r15.setPage(ctx_r15.activePage + 1);\n    });\n    i0.ɵɵelement(2, \"i\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction PaginationComponent_li_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 18);\n    i0.ɵɵelementStart(1, \"a\", 9);\n    i0.ɵɵlistener(\"click\", function PaginationComponent_li_8_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return ctx_r17.setPage(ctx_r17.getNext10(ctx_r17.activePage + 1));\n    });\n    i0.ɵɵelement(2, \"i\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport let PaginationComponent = /*#__PURE__*/(() => {\n  class PaginationComponent {\n    constructor(pageService) {\n      this.pageService = pageService;\n      this.itemsPerPage = 1;\n      this.paginationControlToShow = 5;\n      this.pageChange = new EventEmitter();\n      this.change = new EventEmitter();\n      this.mqAnswered = false;\n      this.pages = [];\n      this.startPage = 0;\n      this.previousPage = 0;\n      this.activePage = 0;\n      this.title = 'New';\n      this.skippedlist = [];\n      this.visitedlist = [];\n    }\n\n    ngOnChanges(changes) {\n      this.currentPageSub = this.pageService.currentPage.subscribe(page => {\n        this.setPage(page - 1);\n      });\n      let currentPageData = changes['currentPage'];\n\n      if (currentPageData) {\n        this.previousPage = currentPageData.previousValue != undefined && currentPageData.previousValue - 1;\n        this.activePage = currentPageData.currentValue - 1;\n\n        if (this.previousPage) {\n          if (!this.visitedlist.some(x => x.index == this.previousPage && x.visited)) {\n            this.visitedlist.push({\n              index: this.previousPage,\n              visited: true\n            });\n          }\n        }\n\n        this.setStyleDefinition(this.currentPage);\n      }\n    }\n\n    ngOnInit() {\n      this.pages = Array(Math.ceil(this.totalItems / this.itemsPerPage)).fill(0).map((x, i) => i);\n\n      if (this.list == undefined) {\n        this.list = Array(this.definitions.length).fill({\n          Answered: false,\n          Skipped: false,\n          ClassName: ''\n        });\n      }\n\n      if (!this.visitedlist.some(x => x.index == 0 && x.visited)) {\n        this.visitedlist.push({\n          index: 0,\n          visited: true\n        });\n      }\n\n      this.setPage(0);\n    }\n\n    ngOnDestroy() {\n      this.currentPageSub.unsubscribe();\n    }\n\n    setPage(page) {\n      this.getStyleDefinition();\n\n      if (page >= 0 && page < this.pages.length) {\n        this.startPage = Math.max(0, Math.min(this.activePage < this.paginationControlToShow ? 0 : 1 + this.activePage - this.paginationControlToShow)); // Math.max(0, Math.min(this.pages.length - 10, page - 4));\n\n        this.pageChange.emit(page + 1);\n      }\n    }\n\n    getPrevious10(pageNo) {\n      return Math.max(this.pages.length - 10, pageNo - 10);\n    }\n\n    getNext10(pageNo) {\n      return Math.min(this.pages.length - 10, pageNo + 10), Math.min(this.pages.length - 1, pageNo + 9);\n    }\n\n    setStyleDefinition(pageNo) {\n      this.getStyleDefinition();\n      let args = this.list != undefined && this.previousPage != undefined && this.list[this.previousPage].Answered;\n\n      if (!args && this.visitedlist.some(x => x.index == this.previousPage)) {\n        this.doSkipp(this.previousPage, true);\n      }\n    }\n\n    getStyleDefinition() {\n      this.definitions.forEach((element, index) => {\n        if ((element.Type === 'MCQ' || element.Type === undefined) && this.visitedlist.some(x => x.index == index)) {\n          this.doAnswer(index, element.Options.some(x => x.Selected));\n        } else if (element.Type === 'TFQ' && this.visitedlist.some(x => x.index == index)) {\n          this.doAnswer(index, element.Answer !== null && element.Answer !== undefined);\n        } else if (element.Type === 'FIGQ' && this.visitedlist.some(x => x.index == index)) {\n          this.doAnswer(index, element.Answer ? element.Answer.trim() !== \"\" : false);\n        } else if (element.Type === 'LRMQ' && this.visitedlist.some(x => x.index == index)) {\n          this.doAnswer(index, this.mqAnswered);\n        } else if (element.Type === 'WQ' && this.visitedlist.some(x => x.index == index)) {\n          this.doAnswer(index, element.Answer ? element.Answer.trim() !== \"\" : false);\n        }\n      });\n      this.generateSkippedList();\n    }\n\n    doSkipp(i, args) {\n      if (!this.skippedlist.some(x => x.index == i && x.skipped)) {\n        this.skippedlist.push({\n          index: i,\n          skipped: args\n        });\n      }\n\n      this.generateSkippedList();\n    }\n\n    generateSkippedList() {\n      this.skippedlist.forEach((vv, ii) => {\n        if (this.list != undefined && this.list[vv.index] != undefined && !this.list[vv.index].Answered) {\n          this.list[vv.index] = {\n            Answered: false,\n            Skipped: vv.skipped,\n            ClassName: vv.skipped ? 'Skipped' : ''\n          };\n        }\n      });\n    }\n\n    doAnswer(i, args) {\n      this.list[i] = {\n        Answered: args,\n        Skipped: false,\n        ClassName: args ? 'Answered' : ''\n      };\n    }\n\n  }\n\n  PaginationComponent.ɵfac = function PaginationComponent_Factory(t) {\n    return new (t || PaginationComponent)(i0.ɵɵdirectiveInject(i1.PaginationService));\n  };\n\n  PaginationComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PaginationComponent,\n    selectors: [[\"app-pagination\"]],\n    inputs: {\n      totalItems: \"totalItems\",\n      itemsPerPage: \"itemsPerPage\",\n      paginationControlToShow: \"paginationControlToShow\",\n      definitions: \"definitions\",\n      currentPage: \"currentPage\",\n      mqAnswered: \"mqAnswered\"\n    },\n    outputs: {\n      pageChange: \"pageChange\",\n      change: \"change\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 9,\n    vars: 7,\n    consts: [[1, \"d-flex\", \"justify-content-center\"], [1, \"pagination\"], [\"title\", \"Previous 10 page\", \"class\", \"page-item\", 4, \"ngIf\"], [\"title\", \"Previous page\", \"class\", \"page-item\", 4, \"ngIf\"], [\"class\", \"page-item\", 4, \"ngIf\"], [\"class\", \"page-item \", 3, \"class\", \"title\", \"active\", \"hidden\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"Next page\", \"class\", \"page-item\", 4, \"ngIf\"], [\"title\", \"Next 10 page\", \"class\", \"page-item\", 4, \"ngIf\"], [\"title\", \"Previous 10 page\", 1, \"page-item\"], [1, \"page-link\", 3, \"click\"], [1, \"fa-solid\", \"fa-angles-left\"], [\"title\", \"Previous page\", 1, \"page-item\"], [1, \"fa-solid\", \"fa-chevron-left\"], [1, \"page-item\"], [1, \"page-link\"], [1, \"page-item\", 3, \"title\", \"hidden\"], [\"title\", \"Next page\", 1, \"page-item\"], [1, \"fa-solid\", \"fa-chevron-right\"], [\"title\", \"Next 10 page\", 1, \"page-item\"], [1, \"fa-solid\", \"fa-angles-right\"]],\n    template: function PaginationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"ul\", 1);\n        i0.ɵɵtemplate(2, PaginationComponent_li_2_Template, 3, 0, \"li\", 2);\n        i0.ɵɵtemplate(3, PaginationComponent_li_3_Template, 3, 0, \"li\", 3);\n        i0.ɵɵtemplate(4, PaginationComponent_li_4_Template, 3, 0, \"li\", 4);\n        i0.ɵɵtemplate(5, PaginationComponent_li_5_Template, 3, 7, \"li\", 5);\n        i0.ɵɵtemplate(6, PaginationComponent_li_6_Template, 3, 0, \"li\", 4);\n        i0.ɵɵtemplate(7, PaginationComponent_li_7_Template, 3, 0, \"li\", 6);\n        i0.ɵɵtemplate(8, PaginationComponent_li_8_Template, 3, 0, \"li\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.activePage > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.activePage > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.activePage > 0 && ctx.pages.length >= ctx.paginationControlToShow);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.pages);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.activePage + 1 < ctx.pages.length && ctx.pages.length >= ctx.paginationControlToShow);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.activePage + 1 < ctx.pages.length);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.activePage + 1 < ctx.pages.length);\n      }\n    },\n    directives: [i2.NgIf, i2.NgForOf],\n    styles: [\".Skipped[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{z-index:3;color:#000;background-color:#c2b307;border-color:#fff}.Answered[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{z-index:3;color:#fff!important;background-color:#0c027a;border-color:#fff}.page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{z-index:3;color:#fff;background-color:#16c995;border-color:#fff}\"],\n    changeDetection: 0\n  });\n  return PaginationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}