{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { AllCoursesRoutes } from './all-courses.routing';\nimport { SharedModule } from '../shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let AllCoursesModule = /*#__PURE__*/(() => {\n  class AllCoursesModule {}\n\n  AllCoursesModule.ɵfac = function AllCoursesModule_Factory(t) {\n    return new (t || AllCoursesModule)();\n  };\n\n  AllCoursesModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AllCoursesModule\n  });\n  AllCoursesModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(AllCoursesRoutes), SharedModule]]\n  });\n  return AllCoursesModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}