{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { ForumPostRoutes } from './forum-post.routing';\nimport { QuillModule } from 'ngx-quill';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-quill\";\nexport let ForumPostModule = /*#__PURE__*/(() => {\n  class ForumPostModule {}\n\n  ForumPostModule.ɵfac = function ForumPostModule_Factory(t) {\n    return new (t || ForumPostModule)();\n  };\n\n  ForumPostModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ForumPostModule\n  });\n  ForumPostModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(ForumPostRoutes), SharedModule, WebLayoutModule, QuillModule.forRoot()]]\n  });\n  return ForumPostModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}