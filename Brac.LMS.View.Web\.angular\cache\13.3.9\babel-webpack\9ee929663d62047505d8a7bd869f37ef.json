{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nexport let SafePipe = /*#__PURE__*/(() => {\n  class SafePipe {\n    constructor(sanitizer) {\n      this.sanitizer = sanitizer;\n    }\n\n    transform(value, type) {\n      switch (type) {\n        case 'html':\n          return this.sanitizer.bypassSecurityTrustHtml(value);\n\n        case 'style':\n          return this.sanitizer.bypassSecurityTrustStyle(value);\n\n        case 'script':\n          return this.sanitizer.bypassSecurityTrustScript(value);\n\n        case 'url':\n          return this.sanitizer.bypassSecurityTrustUrl(value);\n\n        case 'resourceUrl':\n          return this.sanitizer.bypassSecurityTrustResourceUrl(value);\n\n        default:\n          throw new Error(`Invalid safe type specified: ${type}`);\n      }\n    }\n\n  }\n\n  SafePipe.ɵfac = function SafePipe_Factory(t) {\n    return new (t || SafePipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n  };\n\n  SafePipe.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n    name: \"safe\",\n    type: SafePipe,\n    pure: true\n  });\n  return SafePipe;\n})();", "map": null, "metadata": {}, "sourceType": "module"}