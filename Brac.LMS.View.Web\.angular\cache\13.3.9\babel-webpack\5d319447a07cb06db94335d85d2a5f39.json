{"ast": null, "code": "import { Subject } from 'rxjs';\nexport class Timer {\n  constructor() {\n    this.timeLeft = 0;\n    this.hour = 0;\n    this.minute = 0;\n    this.second = 0;\n    this.status = new Subject(); // consider putting the actual type of the data you will receive\n\n    this.isRunning = false;\n  }\n\n  hourString() {\n    return this.zeroPad(this.hour, 2);\n  }\n\n  minuteString() {\n    return this.zeroPad(this.minute, 2);\n  }\n\n  secondString() {\n    return this.zeroPad(this.second, 2);\n  }\n\n  update() {\n    this.hour = Math.floor(this.timeLeft / 3600);\n    this.minute = Math.floor(this.timeLeft / 60);\n    this.second = this.timeLeft % 60;\n  }\n\n  start(seconds) {\n    this.timeLeft = seconds;\n    this.isRunning = true;\n    this.status.next('running');\n    this.interval = setInterval(() => {\n      if (this.timeLeft > 0) {\n        this.timeLeft--;\n      } else {\n        this.timeLeft = 60;\n      }\n\n      this.update();\n\n      if (this.timeLeft === 0) {\n        this.end();\n      }\n    }, 1000);\n    return this.status.asObservable();\n  }\n\n  stop() {\n    if (this.interval) clearInterval(this.interval);\n    this.hour = 0;\n    this.minute = 0;\n    this.second = 0;\n    this.timeLeft = 0;\n    this.isRunning = false;\n    this.status.next('stopped');\n  }\n\n  end() {\n    if (this.interval) clearInterval(this.interval);\n    this.hour = 0;\n    this.minute = 0;\n    this.second = 0;\n    this.timeLeft = 0;\n    this.isRunning = false;\n    this.status.next('ended');\n  }\n\n  pause() {\n    if (this.interval) clearInterval(this.interval);\n    this.isRunning = false;\n    this.status.next('paused');\n  }\n\n  resume() {\n    if (this.interval) clearInterval(this.interval);\n    if (this.timeLeft === 0) return;\n    this.isRunning = true;\n    this.status.next('running');\n    this.interval = setInterval(() => {\n      if (this.timeLeft > 0) {\n        this.timeLeft--;\n      } else {\n        this.timeLeft = 60;\n      }\n\n      this.update();\n\n      if (this.timeLeft === 0) {\n        this.end();\n      }\n    }, 1000);\n  }\n\n  reset(seconds) {\n    if (this.interval) clearInterval(this.interval);\n    return this.start(seconds);\n  }\n\n  zeroPad(num, places) {\n    var zero = places - num.toString().length + 1;\n    return Array(+(zero > 0 && zero)).join(\"0\") + num;\n  }\n\n}", "map": null, "metadata": {}, "sourceType": "module"}