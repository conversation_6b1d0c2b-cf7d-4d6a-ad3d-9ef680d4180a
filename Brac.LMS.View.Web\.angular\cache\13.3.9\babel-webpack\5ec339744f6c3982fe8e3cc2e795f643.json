{"ast": null, "code": "import { ElementRef } from '@angular/core';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\n\nfunction coerceBooleanProperty(value) {\n  return value != null && `${value}` !== 'false';\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n  return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\n\n\nfunction _isNumberValue(value) {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Coerces a value to a CSS pixel value. */\n\n\nfunction coerceCssPixelValue(value) {\n  if (value == null) {\n    return '';\n  }\n\n  return typeof value === 'string' ? value : `${value}px`;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\n\n\nfunction coerceElement(elementOrRef) {\n  return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\n\n\nfunction coerceStringArray(value, separator = /\\s+/) {\n  const result = [];\n\n  if (value != null) {\n    const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n\n    for (const sourceValue of sourceValues) {\n      const trimmedString = `${sourceValue}`.trim();\n\n      if (trimmedString) {\n        result.push(trimmedString);\n      }\n    }\n  }\n\n  return result;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nexport { _isNumberValue, coerceArray, coerceBooleanProperty, coerceCssPixelValue, coerceElement, coerceNumberProperty, coerceStringArray }; //# sourceMappingURL=coercion.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}