{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nimport { share } from './share';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n  var _a, _b;\n\n  let bufferSize;\n  let refCount = false;\n\n  if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n    bufferSize = (_a = configOrBufferSize.bufferSize) !== null && _a !== void 0 ? _a : Infinity;\n    windowTime = (_b = configOrBufferSize.windowTime) !== null && _b !== void 0 ? _b : Infinity;\n    refCount = !!configOrBufferSize.refCount;\n    scheduler = configOrBufferSize.scheduler;\n  } else {\n    bufferSize = configOrBufferSize !== null && configOrBufferSize !== void 0 ? configOrBufferSize : Infinity;\n  }\n\n  return share({\n    connector: () => new ReplaySubject(bufferSize, windowTime, scheduler),\n    resetOnError: true,\n    resetOnComplete: false,\n    resetOnRefCountZero: refCount\n  });\n} //# sourceMappingURL=shareReplay.js.map", "map": null, "metadata": {}, "sourceType": "module"}