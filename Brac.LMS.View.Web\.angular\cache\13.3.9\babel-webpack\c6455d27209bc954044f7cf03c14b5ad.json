{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Inject, InjectionToken, Directive, Input, EventEmitter, Optional, SkipSelf, Output, Self, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { _getEventTarget, normalizePassiveListenerOptions, _getShadowRoot } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceElement, coerceArray, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { Subject, Subscription, interval, animationFrameScheduler, Observable, merge } from 'rxjs';\nimport { takeUntil, startWith, map, take, tap, switchMap } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/bidi';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\n\nfunction extendStyles(dest, source, importantProperties) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      const value = source[key];\n\n      if (value) {\n        dest.setProperty(key, value, (importantProperties === null || importantProperties === void 0 ? void 0 : importantProperties.has(key)) ? 'important' : '');\n      } else {\n        dest.removeProperty(key);\n      }\n    }\n  }\n\n  return dest;\n}\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\n\n\nfunction toggleNativeDragInteractions(element, enable) {\n  const userSelect = enable ? '' : 'none';\n  extendStyles(element.style, {\n    'touch-action': enable ? '' : 'none',\n    '-webkit-user-drag': enable ? '' : 'none',\n    '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n    'user-select': userSelect,\n    '-ms-user-select': userSelect,\n    '-webkit-user-select': userSelect,\n    '-moz-user-select': userSelect\n  });\n}\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\n\n\nfunction toggleVisibility(element, enable, importantProperties) {\n  extendStyles(element.style, {\n    position: enable ? '' : 'fixed',\n    top: enable ? '' : '0',\n    opacity: enable ? '' : '0',\n    left: enable ? '' : '-999em'\n  }, importantProperties);\n}\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\n\n\nfunction combineTransforms(transform, initialTransform) {\n  return initialTransform && initialTransform != 'none' ? transform + ' ' + initialTransform : transform;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Parses a CSS time value to milliseconds. */\n\n\nfunction parseCssTimeUnitsToMs(value) {\n  // Some browsers will return it in seconds, whereas others will return milliseconds.\n  const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n  return parseFloat(value) * multiplier;\n}\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\n\n\nfunction getTransformTransitionDurationInMs(element) {\n  const computedStyle = getComputedStyle(element);\n  const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n  const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all'); // If there's no transition for `all` or `transform`, we shouldn't do anything.\n\n  if (!property) {\n    return 0;\n  } // Get the index of the property that we're interested in and match\n  // it up to the same index in `transition-delay` and `transition-duration`.\n\n\n  const propertyIndex = transitionedProperties.indexOf(property);\n  const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n  const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n  return parseCssTimeUnitsToMs(rawDurations[propertyIndex]) + parseCssTimeUnitsToMs(rawDelays[propertyIndex]);\n}\n/** Parses out multiple values from a computed style into an array. */\n\n\nfunction parseCssPropertyValue(computedStyle, name) {\n  const value = computedStyle.getPropertyValue(name);\n  return value.split(',').map(part => part.trim());\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Gets a mutable version of an element's bounding `ClientRect`. */\n\n\nfunction getMutableClientRect(element) {\n  const clientRect = element.getBoundingClientRect(); // We need to clone the `clientRect` here, because all the values on it are readonly\n  // and we need to be able to update them. Also we can't use a spread here, because\n  // the values on a `ClientRect` aren't own properties. See:\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n\n  return {\n    top: clientRect.top,\n    right: clientRect.right,\n    bottom: clientRect.bottom,\n    left: clientRect.left,\n    width: clientRect.width,\n    height: clientRect.height,\n    x: clientRect.x,\n    y: clientRect.y\n  };\n}\n/**\n * Checks whether some coordinates are within a `ClientRect`.\n * @param clientRect ClientRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\n\n\nfunction isInsideClientRect(clientRect, x, y) {\n  const {\n    top,\n    bottom,\n    left,\n    right\n  } = clientRect;\n  return y >= top && y <= bottom && x >= left && x <= right;\n}\n/**\n * Updates the top/left positions of a `ClientRect`, as well as their bottom/right counterparts.\n * @param clientRect `ClientRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\n\n\nfunction adjustClientRect(clientRect, top, left) {\n  clientRect.top += top;\n  clientRect.bottom = clientRect.top + clientRect.height;\n  clientRect.left += left;\n  clientRect.right = clientRect.left + clientRect.width;\n}\n/**\n * Checks whether the pointer coordinates are close to a ClientRect.\n * @param rect ClientRect to check against.\n * @param threshold Threshold around the ClientRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\n\n\nfunction isPointerNearClientRect(rect, threshold, pointerX, pointerY) {\n  const {\n    top,\n    right,\n    bottom,\n    left,\n    width,\n    height\n  } = rect;\n  const xThreshold = width * threshold;\n  const yThreshold = height * threshold;\n  return pointerY > top - yThreshold && pointerY < bottom + yThreshold && pointerX > left - xThreshold && pointerX < right + xThreshold;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\n\n\nclass ParentPositionTracker {\n  constructor(_document, _viewportRuler) {\n    this._document = _document;\n    this._viewportRuler = _viewportRuler;\n    /** Cached positions of the scrollable parent elements. */\n\n    this.positions = new Map();\n  }\n  /** Clears the cached positions. */\n\n\n  clear() {\n    this.positions.clear();\n  }\n  /** Caches the positions. Should be called at the beginning of a drag sequence. */\n\n\n  cache(elements) {\n    this.clear();\n    this.positions.set(this._document, {\n      scrollPosition: this._viewportRuler.getViewportScrollPosition()\n    });\n    elements.forEach(element => {\n      this.positions.set(element, {\n        scrollPosition: {\n          top: element.scrollTop,\n          left: element.scrollLeft\n        },\n        clientRect: getMutableClientRect(element)\n      });\n    });\n  }\n  /** Handles scrolling while a drag is taking place. */\n\n\n  handleScroll(event) {\n    const target = _getEventTarget(event);\n\n    const cachedPosition = this.positions.get(target);\n\n    if (!cachedPosition) {\n      return null;\n    }\n\n    const scrollPosition = cachedPosition.scrollPosition;\n    let newTop;\n    let newLeft;\n\n    if (target === this._document) {\n      const viewportScrollPosition = this._viewportRuler.getViewportScrollPosition();\n\n      newTop = viewportScrollPosition.top;\n      newLeft = viewportScrollPosition.left;\n    } else {\n      newTop = target.scrollTop;\n      newLeft = target.scrollLeft;\n    }\n\n    const topDifference = scrollPosition.top - newTop;\n    const leftDifference = scrollPosition.left - newLeft; // Go through and update the cached positions of the scroll\n    // parents that are inside the element that was scrolled.\n\n    this.positions.forEach((position, node) => {\n      if (position.clientRect && target !== node && target.contains(node)) {\n        adjustClientRect(position.clientRect, topDifference, leftDifference);\n      }\n    });\n    scrollPosition.top = newTop;\n    scrollPosition.left = newLeft;\n    return {\n      top: topDifference,\n      left: leftDifference\n    };\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Creates a deep clone of an element. */\n\n\nfunction deepCloneNode(node) {\n  const clone = node.cloneNode(true);\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase(); // Remove the `id` to avoid having multiple elements with the same id on the page.\n\n  clone.removeAttribute('id');\n\n  for (let i = 0; i < descendantsWithId.length; i++) {\n    descendantsWithId[i].removeAttribute('id');\n  }\n\n  if (nodeName === 'canvas') {\n    transferCanvasData(node, clone);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node, clone);\n  }\n\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\n\n\nfunction transferData(selector, node, clone, callback) {\n  const descendantElements = node.querySelectorAll(selector);\n\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll(selector);\n\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n} // Counter for unique cloned radio button names.\n\n\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\n\nfunction transferInputData(source, clone) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  } // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n\n\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n/** Transfers the data of one canvas element to another. */\n\n\nfunction transferCanvasData(source, clone) {\n  const context = clone.getContext('2d');\n\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch (_a) {}\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Options that can be used to bind a passive event listener. */\n\n\nconst passiveEventListenerOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: true\n});\n/** Options that can be used to bind an active event listener. */\n\nconst activeEventListenerOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: false\n});\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\n\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n/** Inline styles to be set as `!important` while dragging. */\n\nconst dragImportantProperties = /*#__PURE__*/new Set([// Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n'position']);\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\n\nclass DragRef {\n  constructor(element, _config, _document, _ngZone, _viewportRuler, _dragDropRegistry) {\n    this._config = _config;\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n    /**\n     * CSS `transform` applied to the element when it isn't being dragged. We need a\n     * passive transform in order for the dragged element to retain its new position\n     * after the user has stopped dragging and because we need to know the relative\n     * position in case they start dragging again. This corresponds to `element.style.transform`.\n     */\n\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n    /** CSS `transform` that is applied to the element while it's being dragged. */\n\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * Whether the dragging sequence has been started. Doesn't\n     * necessarily mean that the element has been moved.\n     */\n\n    this._hasStartedDragging = false;\n    /** Emits when the item is being moved. */\n\n    this._moveEvents = new Subject();\n    /** Subscription to pointer movement events. */\n\n    this._pointerMoveSubscription = Subscription.EMPTY;\n    /** Subscription to the event that is dispatched when the user lifts their pointer. */\n\n    this._pointerUpSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being scrolled. */\n\n    this._scrollSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being resized. */\n\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Cached reference to the boundary element. */\n\n    this._boundaryElement = null;\n    /** Whether the native dragging interactions have been enabled on the root element. */\n\n    this._nativeInteractionsEnabled = true;\n    /** Elements that can be used to drag the draggable item. */\n\n    this._handles = [];\n    /** Registered handles that are currently disabled. */\n\n    this._disabledHandles = new Set();\n    /** Layout direction of the item. */\n\n    this._direction = 'ltr';\n    /**\n     * Amount of milliseconds to wait after the user has put their\n     * pointer down before starting to drag the element.\n     */\n\n    this.dragStartDelay = 0;\n    this._disabled = false;\n    /** Emits as the drag sequence is being prepared. */\n\n    this.beforeStarted = new Subject();\n    /** Emits when the user starts dragging the item. */\n\n    this.started = new Subject();\n    /** Emits when the user has released a drag item, before any animations have started. */\n\n    this.released = new Subject();\n    /** Emits when the user stops dragging an item in the container. */\n\n    this.ended = new Subject();\n    /** Emits when the user has moved the item into a new container. */\n\n    this.entered = new Subject();\n    /** Emits when the user removes the item its container by dragging it into another container. */\n\n    this.exited = new Subject();\n    /** Emits when the user drops the item inside a container. */\n\n    this.dropped = new Subject();\n    /**\n     * Emits as the user is dragging the item. Use with caution,\n     * because this event will fire for every pixel that the user has dragged.\n     */\n\n    this.moved = this._moveEvents;\n    /** Handler for the `mousedown`/`touchstart` events. */\n\n    this._pointerDown = event => {\n      this.beforeStarted.next(); // Delegate the event based on whether it started from a handle or the element itself.\n\n      if (this._handles.length) {\n        const targetHandle = this._handles.find(handle => {\n          return event.target && (event.target === handle || handle.contains(event.target));\n        });\n\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          this._initializeDragSequence(targetHandle, event);\n        }\n      } else if (!this.disabled) {\n        this._initializeDragSequence(this._rootElement, event);\n      }\n    };\n    /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n\n\n    this._pointerMove = event => {\n      const pointerPosition = this._getPointerPositionOnPage(event);\n\n      if (!this._hasStartedDragging) {\n        const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n        const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n        const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold; // Only start dragging after the user has moved more than the minimum distance in either\n        // direction. Note that this is preferrable over doing something like `skip(minimumDistance)`\n        // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n        // per pixel of movement (e.g. if the user moves their pointer quickly).\n\n        if (isOverThreshold) {\n          const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n\n          const container = this._dropContainer;\n\n          if (!isDelayElapsed) {\n            this._endDragSequence(event);\n\n            return;\n          } // Prevent other drag sequences from starting while something in the container is still\n          // being dragged. This can happen while we're waiting for the drop animation to finish\n          // and can cause errors, because some elements might still be moving around.\n\n\n          if (!container || !container.isDragging() && !container.isReceiving()) {\n            // Prevent the default action as soon as the dragging sequence is considered as\n            // \"started\" since waiting for the next event can allow the device to begin scrolling.\n            event.preventDefault();\n            this._hasStartedDragging = true;\n\n            this._ngZone.run(() => this._startDragSequence(event));\n          }\n        }\n\n        return;\n      } // We only need the preview dimensions if we have a boundary element.\n\n\n      if (this._boundaryElement) {\n        // Cache the preview element rect if we haven't cached it already or if\n        // we cached it too early before the element dimensions were computed.\n        if (!this._previewRect || !this._previewRect.width && !this._previewRect.height) {\n          this._previewRect = (this._preview || this._rootElement).getBoundingClientRect();\n        }\n      } // We prevent the default action down here so that we know that dragging has started. This is\n      // important for touch devices where doing this too early can unnecessarily block scrolling,\n      // if there's a dragging delay.\n\n\n      event.preventDefault();\n\n      const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n\n      this._hasMoved = true;\n      this._lastKnownPointerPosition = pointerPosition;\n\n      this._updatePointerDirectionDelta(constrainedPointerPosition);\n\n      if (this._dropContainer) {\n        this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n      } else {\n        const activeTransform = this._activeTransform;\n        activeTransform.x = constrainedPointerPosition.x - this._pickupPositionOnPage.x + this._passiveTransform.x;\n        activeTransform.y = constrainedPointerPosition.y - this._pickupPositionOnPage.y + this._passiveTransform.y;\n\n        this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n      } // Since this event gets fired for every pixel while dragging, we only\n      // want to fire it if the consumer opted into it. Also we have to\n      // re-enter the zone because we run all of the events on the outside.\n\n\n      if (this._moveEvents.observers.length) {\n        this._ngZone.run(() => {\n          this._moveEvents.next({\n            source: this,\n            pointerPosition: constrainedPointerPosition,\n            event,\n            distance: this._getDragDistance(constrainedPointerPosition),\n            delta: this._pointerDirectionDelta\n          });\n        });\n      }\n    };\n    /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n\n\n    this._pointerUp = event => {\n      this._endDragSequence(event);\n    };\n\n    this.withRootElement(element).withParent(_config.parentDragRef || null);\n    this._parentPositions = new ParentPositionTracker(_document, _viewportRuler);\n\n    _dragDropRegistry.registerDragItem(this);\n  }\n  /** Whether starting to drag this element is disabled. */\n\n\n  get disabled() {\n    return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n  }\n\n  set disabled(value) {\n    const newValue = coerceBooleanProperty(value);\n\n    if (newValue !== this._disabled) {\n      this._disabled = newValue;\n\n      this._toggleNativeDragInteractions();\n\n      this._handles.forEach(handle => toggleNativeDragInteractions(handle, newValue));\n    }\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n\n\n  getPlaceholderElement() {\n    return this._placeholder;\n  }\n  /** Returns the root draggable element. */\n\n\n  getRootElement() {\n    return this._rootElement;\n  }\n  /**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */\n\n\n  getVisibleElement() {\n    return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n  }\n  /** Registers the handles that can be used to drag the element. */\n\n\n  withHandles(handles) {\n    this._handles = handles.map(handle => coerceElement(handle));\n\n    this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n\n    this._toggleNativeDragInteractions(); // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n    // the set, rather than iterate over it and filter out the destroyed handles, because while\n    // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n    // use an array internally which may throw an error.\n\n\n    const disabledHandles = new Set();\n\n    this._disabledHandles.forEach(handle => {\n      if (this._handles.indexOf(handle) > -1) {\n        disabledHandles.add(handle);\n      }\n    });\n\n    this._disabledHandles = disabledHandles;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */\n\n\n  withPreviewTemplate(template) {\n    this._previewTemplate = template;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */\n\n\n  withPlaceholderTemplate(template) {\n    this._placeholderTemplate = template;\n    return this;\n  }\n  /**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */\n\n\n  withRootElement(rootElement) {\n    const element = coerceElement(rootElement);\n\n    if (element !== this._rootElement) {\n      if (this._rootElement) {\n        this._removeRootElementListeners(this._rootElement);\n      }\n\n      this._ngZone.runOutsideAngular(() => {\n        element.addEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n        element.addEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n      });\n\n      this._initialTransform = undefined;\n      this._rootElement = element;\n    }\n\n    if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n      this._ownerSVGElement = this._rootElement.ownerSVGElement;\n    }\n\n    return this;\n  }\n  /**\n   * Element to which the draggable's position will be constrained.\n   */\n\n\n  withBoundaryElement(boundaryElement) {\n    this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n\n    this._resizeSubscription.unsubscribe();\n\n    if (boundaryElement) {\n      this._resizeSubscription = this._viewportRuler.change(10).subscribe(() => this._containInsideBoundaryOnResize());\n    }\n\n    return this;\n  }\n  /** Sets the parent ref that the ref is nested in.  */\n\n\n  withParent(parent) {\n    this._parentDragRef = parent;\n    return this;\n  }\n  /** Removes the dragging functionality from the DOM element. */\n\n\n  dispose() {\n    var _a, _b;\n\n    this._removeRootElementListeners(this._rootElement); // Do this check before removing from the registry since it'll\n    // stop being considered as dragged once it is removed.\n\n\n    if (this.isDragging()) {\n      // Since we move out the element to the end of the body while it's being\n      // dragged, we have to make sure that it's removed if it gets destroyed.\n      (_a = this._rootElement) === null || _a === void 0 ? void 0 : _a.remove();\n    }\n\n    (_b = this._anchor) === null || _b === void 0 ? void 0 : _b.remove();\n\n    this._destroyPreview();\n\n    this._destroyPlaceholder();\n\n    this._dragDropRegistry.removeDragItem(this);\n\n    this._removeSubscriptions();\n\n    this.beforeStarted.complete();\n    this.started.complete();\n    this.released.complete();\n    this.ended.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n\n    this._moveEvents.complete();\n\n    this._handles = [];\n\n    this._disabledHandles.clear();\n\n    this._dropContainer = undefined;\n\n    this._resizeSubscription.unsubscribe();\n\n    this._parentPositions.clear();\n\n    this._boundaryElement = this._rootElement = this._ownerSVGElement = this._placeholderTemplate = this._previewTemplate = this._anchor = this._parentDragRef = null;\n  }\n  /** Checks whether the element is currently being dragged. */\n\n\n  isDragging() {\n    return this._hasStartedDragging && this._dragDropRegistry.isDragging(this);\n  }\n  /** Resets a standalone drag item to its initial position. */\n\n\n  reset() {\n    this._rootElement.style.transform = this._initialTransform || '';\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n  }\n  /**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */\n\n\n  disableHandle(handle) {\n    if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n      this._disabledHandles.add(handle);\n\n      toggleNativeDragInteractions(handle, true);\n    }\n  }\n  /**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */\n\n\n  enableHandle(handle) {\n    if (this._disabledHandles.has(handle)) {\n      this._disabledHandles.delete(handle);\n\n      toggleNativeDragInteractions(handle, this.disabled);\n    }\n  }\n  /** Sets the layout direction of the draggable item. */\n\n\n  withDirection(direction) {\n    this._direction = direction;\n    return this;\n  }\n  /** Sets the container that the item is part of. */\n\n\n  _withDropContainer(container) {\n    this._dropContainer = container;\n  }\n  /**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */\n\n\n  getFreeDragPosition() {\n    const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n    return {\n      x: position.x,\n      y: position.y\n    };\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n\n\n  setFreeDragPosition(value) {\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform.x = value.x;\n    this._passiveTransform.y = value.y;\n\n    if (!this._dropContainer) {\n      this._applyRootElementTransform(value.x, value.y);\n    }\n\n    return this;\n  }\n  /**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */\n\n\n  withPreviewContainer(value) {\n    this._previewContainer = value;\n    return this;\n  }\n  /** Updates the item's sort order based on the last-known pointer position. */\n\n\n  _sortFromLastPointerPosition() {\n    const position = this._lastKnownPointerPosition;\n\n    if (position && this._dropContainer) {\n      this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n    }\n  }\n  /** Unsubscribes from the global subscriptions. */\n\n\n  _removeSubscriptions() {\n    this._pointerMoveSubscription.unsubscribe();\n\n    this._pointerUpSubscription.unsubscribe();\n\n    this._scrollSubscription.unsubscribe();\n  }\n  /** Destroys the preview element and its ViewRef. */\n\n\n  _destroyPreview() {\n    var _a, _b;\n\n    (_a = this._preview) === null || _a === void 0 ? void 0 : _a.remove();\n    (_b = this._previewRef) === null || _b === void 0 ? void 0 : _b.destroy();\n    this._preview = this._previewRef = null;\n  }\n  /** Destroys the placeholder element and its ViewRef. */\n\n\n  _destroyPlaceholder() {\n    var _a, _b;\n\n    (_a = this._placeholder) === null || _a === void 0 ? void 0 : _a.remove();\n    (_b = this._placeholderRef) === null || _b === void 0 ? void 0 : _b.destroy();\n    this._placeholder = this._placeholderRef = null;\n  }\n  /**\n   * Clears subscriptions and stops the dragging sequence.\n   * @param event Browser event object that ended the sequence.\n   */\n\n\n  _endDragSequence(event) {\n    // Note that here we use `isDragging` from the service, rather than from `this`.\n    // The difference is that the one from the service reflects whether a dragging sequence\n    // has been initiated, whereas the one on `this` includes whether the user has passed\n    // the minimum dragging threshold.\n    if (!this._dragDropRegistry.isDragging(this)) {\n      return;\n    }\n\n    this._removeSubscriptions();\n\n    this._dragDropRegistry.stopDragging(this);\n\n    this._toggleNativeDragInteractions();\n\n    if (this._handles) {\n      this._rootElement.style.webkitTapHighlightColor = this._rootElementTapHighlight;\n    }\n\n    if (!this._hasStartedDragging) {\n      return;\n    }\n\n    this.released.next({\n      source: this\n    });\n\n    if (this._dropContainer) {\n      // Stop scrolling immediately, instead of waiting for the animation to finish.\n      this._dropContainer._stopScrolling();\n\n      this._animatePreviewToPlaceholder().then(() => {\n        this._cleanupDragArtifacts(event);\n\n        this._cleanupCachedDimensions();\n\n        this._dragDropRegistry.stopDragging(this);\n      });\n    } else {\n      // Convert the active transform into a passive one. This means that next time\n      // the user starts dragging the item, its position will be calculated relatively\n      // to the new passive transform.\n      this._passiveTransform.x = this._activeTransform.x;\n\n      const pointerPosition = this._getPointerPositionOnPage(event);\n\n      this._passiveTransform.y = this._activeTransform.y;\n\n      this._ngZone.run(() => {\n        this.ended.next({\n          source: this,\n          distance: this._getDragDistance(pointerPosition),\n          dropPoint: pointerPosition\n        });\n      });\n\n      this._cleanupCachedDimensions();\n\n      this._dragDropRegistry.stopDragging(this);\n    }\n  }\n  /** Starts the dragging sequence. */\n\n\n  _startDragSequence(event) {\n    if (isTouchEvent(event)) {\n      this._lastTouchEventTime = Date.now();\n    }\n\n    this._toggleNativeDragInteractions();\n\n    const dropContainer = this._dropContainer;\n\n    if (dropContainer) {\n      const element = this._rootElement;\n      const parent = element.parentNode;\n\n      const placeholder = this._placeholder = this._createPlaceholderElement();\n\n      const anchor = this._anchor = this._anchor || this._document.createComment(''); // Needs to happen before the root element is moved.\n\n\n      const shadowRoot = this._getShadowRoot(); // Insert an anchor node so that we can restore the element's position in the DOM.\n\n\n      parent.insertBefore(anchor, element); // There's no risk of transforms stacking when inside a drop container so\n      // we can keep the initial transform up to date any time dragging starts.\n\n      this._initialTransform = element.style.transform || ''; // Create the preview after the initial transform has\n      // been cached, because it can be affected by the transform.\n\n      this._preview = this._createPreviewElement(); // We move the element out at the end of the body and we make it hidden, because keeping it in\n      // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n      // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n\n      toggleVisibility(element, false, dragImportantProperties);\n\n      this._document.body.appendChild(parent.replaceChild(placeholder, element));\n\n      this._getPreviewInsertionPoint(parent, shadowRoot).appendChild(this._preview);\n\n      this.started.next({\n        source: this\n      }); // Emit before notifying the container.\n\n      dropContainer.start();\n      this._initialContainer = dropContainer;\n      this._initialIndex = dropContainer.getItemIndex(this);\n    } else {\n      this.started.next({\n        source: this\n      });\n      this._initialContainer = this._initialIndex = undefined;\n    } // Important to run after we've called `start` on the parent container\n    // so that it has had time to resolve its scrollable parents.\n\n\n    this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n  }\n  /**\n   * Sets up the different variables and subscriptions\n   * that will be necessary for the dragging sequence.\n   * @param referenceElement Element that started the drag sequence.\n   * @param event Browser event object that started the sequence.\n   */\n\n\n  _initializeDragSequence(referenceElement, event) {\n    // Stop propagation if the item is inside another\n    // draggable so we don't start multiple drag sequences.\n    if (this._parentDragRef) {\n      event.stopPropagation();\n    }\n\n    const isDragging = this.isDragging();\n    const isTouchSequence = isTouchEvent(event);\n    const isAuxiliaryMouseButton = !isTouchSequence && event.button !== 0;\n    const rootElement = this._rootElement;\n\n    const target = _getEventTarget(event);\n\n    const isSyntheticEvent = !isTouchSequence && this._lastTouchEventTime && this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n    const isFakeEvent = isTouchSequence ? isFakeTouchstartFromScreenReader(event) : isFakeMousedownFromScreenReader(event); // If the event started from an element with the native HTML drag&drop, it'll interfere\n    // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n    // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n    // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n    // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n    // events from firing on touch devices.\n\n    if (target && target.draggable && event.type === 'mousedown') {\n      event.preventDefault();\n    } // Abort if the user is already dragging or is using a mouse button other than the primary one.\n\n\n    if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n      return;\n    } // If we've got handles, we need to disable the tap highlight on the entire root element,\n    // otherwise iOS will still add it, even though all the drag interactions on the handle\n    // are disabled.\n\n\n    if (this._handles.length) {\n      const rootStyles = rootElement.style;\n      this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n      rootStyles.webkitTapHighlightColor = 'transparent';\n    }\n\n    this._hasStartedDragging = this._hasMoved = false; // Avoid multiple subscriptions and memory leaks when multi touch\n    // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n\n    this._removeSubscriptions();\n\n    this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n    this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n    this._scrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n\n    if (this._boundaryElement) {\n      this._boundaryRect = getMutableClientRect(this._boundaryElement);\n    } // If we have a custom preview we can't know ahead of time how large it'll be so we position\n    // it next to the cursor. The exception is when the consumer has opted into making the preview\n    // the same size as the root element, in which case we do know the size.\n\n\n    const previewTemplate = this._previewTemplate;\n    this._pickupPositionInElement = previewTemplate && previewTemplate.template && !previewTemplate.matchSize ? {\n      x: 0,\n      y: 0\n    } : this._getPointerPositionInElement(referenceElement, event);\n\n    const pointerPosition = this._pickupPositionOnPage = this._lastKnownPointerPosition = this._getPointerPositionOnPage(event);\n\n    this._pointerDirectionDelta = {\n      x: 0,\n      y: 0\n    };\n    this._pointerPositionAtLastDirectionChange = {\n      x: pointerPosition.x,\n      y: pointerPosition.y\n    };\n    this._dragStartTime = Date.now();\n\n    this._dragDropRegistry.startDragging(this, event);\n  }\n  /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n\n\n  _cleanupDragArtifacts(event) {\n    // Restore the element's visibility and insert it at its old position in the DOM.\n    // It's important that we maintain the position, because moving the element around in the DOM\n    // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n    // while moving the existing elements in all other cases.\n    toggleVisibility(this._rootElement, true, dragImportantProperties);\n\n    this._anchor.parentNode.replaceChild(this._rootElement, this._anchor);\n\n    this._destroyPreview();\n\n    this._destroyPlaceholder();\n\n    this._boundaryRect = this._previewRect = this._initialTransform = undefined; // Re-enter the NgZone since we bound `document` events on the outside.\n\n    this._ngZone.run(() => {\n      const container = this._dropContainer;\n      const currentIndex = container.getItemIndex(this);\n\n      const pointerPosition = this._getPointerPositionOnPage(event);\n\n      const distance = this._getDragDistance(pointerPosition);\n\n      const isPointerOverContainer = container._isOverContainer(pointerPosition.x, pointerPosition.y);\n\n      this.ended.next({\n        source: this,\n        distance,\n        dropPoint: pointerPosition\n      });\n      this.dropped.next({\n        item: this,\n        currentIndex,\n        previousIndex: this._initialIndex,\n        container: container,\n        previousContainer: this._initialContainer,\n        isPointerOverContainer,\n        distance,\n        dropPoint: pointerPosition\n      });\n      container.drop(this, currentIndex, this._initialIndex, this._initialContainer, isPointerOverContainer, distance, pointerPosition);\n      this._dropContainer = this._initialContainer;\n    });\n  }\n  /**\n   * Updates the item's position in its drop container, or moves it\n   * into a new one, depending on its current drag position.\n   */\n\n\n  _updateActiveDropContainer({\n    x,\n    y\n  }, {\n    x: rawX,\n    y: rawY\n  }) {\n    // Drop container that draggable has been moved into.\n    let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y); // If we couldn't find a new container to move the item into, and the item has left its\n    // initial container, check whether the it's over the initial container. This handles the\n    // case where two containers are connected one way and the user tries to undo dragging an\n    // item into a new container.\n\n\n    if (!newContainer && this._dropContainer !== this._initialContainer && this._initialContainer._isOverContainer(x, y)) {\n      newContainer = this._initialContainer;\n    }\n\n    if (newContainer && newContainer !== this._dropContainer) {\n      this._ngZone.run(() => {\n        // Notify the old container that the item has left.\n        this.exited.next({\n          item: this,\n          container: this._dropContainer\n        });\n\n        this._dropContainer.exit(this); // Notify the new container that the item has entered.\n\n\n        this._dropContainer = newContainer;\n\n        this._dropContainer.enter(this, x, y, newContainer === this._initialContainer && // If we're re-entering the initial container and sorting is disabled,\n        // put item the into its starting index to begin with.\n        newContainer.sortingDisabled ? this._initialIndex : undefined);\n\n        this.entered.next({\n          item: this,\n          container: newContainer,\n          currentIndex: newContainer.getItemIndex(this)\n        });\n      });\n    } // Dragging may have been interrupted as a result of the events above.\n\n\n    if (this.isDragging()) {\n      this._dropContainer._startScrollingIfNecessary(rawX, rawY);\n\n      this._dropContainer._sortItem(this, x, y, this._pointerDirectionDelta);\n\n      this._applyPreviewTransform(x - this._pickupPositionInElement.x, y - this._pickupPositionInElement.y);\n    }\n  }\n  /**\n   * Creates the element that will be rendered next to the user's pointer\n   * and will be used as a preview of the element that is being dragged.\n   */\n\n\n  _createPreviewElement() {\n    const previewConfig = this._previewTemplate;\n    const previewClass = this.previewClass;\n    const previewTemplate = previewConfig ? previewConfig.template : null;\n    let preview;\n\n    if (previewTemplate && previewConfig) {\n      // Measure the element before we've inserted the preview\n      // since the insertion could throw off the measurement.\n      const rootRect = previewConfig.matchSize ? this._rootElement.getBoundingClientRect() : null;\n      const viewRef = previewConfig.viewContainer.createEmbeddedView(previewTemplate, previewConfig.context);\n      viewRef.detectChanges();\n      preview = getRootNode(viewRef, this._document);\n      this._previewRef = viewRef;\n\n      if (previewConfig.matchSize) {\n        matchElementSize(preview, rootRect);\n      } else {\n        preview.style.transform = getTransform(this._pickupPositionOnPage.x, this._pickupPositionOnPage.y);\n      }\n    } else {\n      const element = this._rootElement;\n      preview = deepCloneNode(element);\n      matchElementSize(preview, element.getBoundingClientRect());\n\n      if (this._initialTransform) {\n        preview.style.transform = this._initialTransform;\n      }\n    }\n\n    extendStyles(preview.style, {\n      // It's important that we disable the pointer events on the preview, because\n      // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n      'pointer-events': 'none',\n      // We have to reset the margin, because it can throw off positioning relative to the viewport.\n      'margin': '0',\n      'position': 'fixed',\n      'top': '0',\n      'left': '0',\n      'z-index': `${this._config.zIndex || 1000}`\n    }, dragImportantProperties);\n    toggleNativeDragInteractions(preview, false);\n    preview.classList.add('cdk-drag-preview');\n    preview.setAttribute('dir', this._direction);\n\n    if (previewClass) {\n      if (Array.isArray(previewClass)) {\n        previewClass.forEach(className => preview.classList.add(className));\n      } else {\n        preview.classList.add(previewClass);\n      }\n    }\n\n    return preview;\n  }\n  /**\n   * Animates the preview element from its current position to the location of the drop placeholder.\n   * @returns Promise that resolves when the animation completes.\n   */\n\n\n  _animatePreviewToPlaceholder() {\n    // If the user hasn't moved yet, the transitionend event won't fire.\n    if (!this._hasMoved) {\n      return Promise.resolve();\n    }\n\n    const placeholderRect = this._placeholder.getBoundingClientRect(); // Apply the class that adds a transition to the preview.\n\n\n    this._preview.classList.add('cdk-drag-animating'); // Move the preview to the placeholder position.\n\n\n    this._applyPreviewTransform(placeholderRect.left, placeholderRect.top); // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n    // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n    // apply its style, we take advantage of the available info to figure out whether we need to\n    // bind the event in the first place.\n\n\n    const duration = getTransformTransitionDurationInMs(this._preview);\n\n    if (duration === 0) {\n      return Promise.resolve();\n    }\n\n    return this._ngZone.runOutsideAngular(() => {\n      return new Promise(resolve => {\n        const handler = event => {\n          var _a;\n\n          if (!event || _getEventTarget(event) === this._preview && event.propertyName === 'transform') {\n            (_a = this._preview) === null || _a === void 0 ? void 0 : _a.removeEventListener('transitionend', handler);\n            resolve();\n            clearTimeout(timeout);\n          }\n        }; // If a transition is short enough, the browser might not fire the `transitionend` event.\n        // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n        // fire if the transition hasn't completed when it was supposed to.\n\n\n        const timeout = setTimeout(handler, duration * 1.5);\n\n        this._preview.addEventListener('transitionend', handler);\n      });\n    });\n  }\n  /** Creates an element that will be shown instead of the current element while dragging. */\n\n\n  _createPlaceholderElement() {\n    const placeholderConfig = this._placeholderTemplate;\n    const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n    let placeholder;\n\n    if (placeholderTemplate) {\n      this._placeholderRef = placeholderConfig.viewContainer.createEmbeddedView(placeholderTemplate, placeholderConfig.context);\n\n      this._placeholderRef.detectChanges();\n\n      placeholder = getRootNode(this._placeholderRef, this._document);\n    } else {\n      placeholder = deepCloneNode(this._rootElement);\n    }\n\n    placeholder.classList.add('cdk-drag-placeholder');\n    return placeholder;\n  }\n  /**\n   * Figures out the coordinates at which an element was picked up.\n   * @param referenceElement Element that initiated the dragging.\n   * @param event Event that initiated the dragging.\n   */\n\n\n  _getPointerPositionInElement(referenceElement, event) {\n    const elementRect = this._rootElement.getBoundingClientRect();\n\n    const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n    const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n    const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n\n    const scrollPosition = this._getViewportScrollPosition();\n\n    const x = point.pageX - referenceRect.left - scrollPosition.left;\n    const y = point.pageY - referenceRect.top - scrollPosition.top;\n    return {\n      x: referenceRect.left - elementRect.left + x,\n      y: referenceRect.top - elementRect.top + y\n    };\n  }\n  /** Determines the point of the page that was touched by the user. */\n\n\n  _getPointerPositionOnPage(event) {\n    const scrollPosition = this._getViewportScrollPosition();\n\n    const point = isTouchEvent(event) ? // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n    // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n    // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n    // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n    // throwing an error. The value returned here will be incorrect, but since this only\n    // breaks inside a developer tool and the value is only used for secondary information,\n    // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n    event.touches[0] || event.changedTouches[0] || {\n      pageX: 0,\n      pageY: 0\n    } : event;\n    const x = point.pageX - scrollPosition.left;\n    const y = point.pageY - scrollPosition.top; // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n    // coordinate system\n\n    if (this._ownerSVGElement) {\n      const svgMatrix = this._ownerSVGElement.getScreenCTM();\n\n      if (svgMatrix) {\n        const svgPoint = this._ownerSVGElement.createSVGPoint();\n\n        svgPoint.x = x;\n        svgPoint.y = y;\n        return svgPoint.matrixTransform(svgMatrix.inverse());\n      }\n    }\n\n    return {\n      x,\n      y\n    };\n  }\n  /** Gets the pointer position on the page, accounting for any position constraints. */\n\n\n  _getConstrainedPointerPosition(point) {\n    const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n    let {\n      x,\n      y\n    } = this.constrainPosition ? this.constrainPosition(point, this) : point;\n\n    if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n      y = this._pickupPositionOnPage.y;\n    } else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n      x = this._pickupPositionOnPage.x;\n    }\n\n    if (this._boundaryRect) {\n      const {\n        x: pickupX,\n        y: pickupY\n      } = this._pickupPositionInElement;\n      const boundaryRect = this._boundaryRect;\n      const previewRect = this._previewRect;\n      const minY = boundaryRect.top + pickupY;\n      const maxY = boundaryRect.bottom - (previewRect.height - pickupY);\n      const minX = boundaryRect.left + pickupX;\n      const maxX = boundaryRect.right - (previewRect.width - pickupX);\n      x = clamp$1(x, minX, maxX);\n      y = clamp$1(y, minY, maxY);\n    }\n\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the current drag delta, based on the user's current pointer position on the page. */\n\n\n  _updatePointerDirectionDelta(pointerPositionOnPage) {\n    const {\n      x,\n      y\n    } = pointerPositionOnPage;\n    const delta = this._pointerDirectionDelta;\n    const positionSinceLastChange = this._pointerPositionAtLastDirectionChange; // Amount of pixels the user has dragged since the last time the direction changed.\n\n    const changeX = Math.abs(x - positionSinceLastChange.x);\n    const changeY = Math.abs(y - positionSinceLastChange.y); // Because we handle pointer events on a per-pixel basis, we don't want the delta\n    // to change for every pixel, otherwise anything that depends on it can look erratic.\n    // To make the delta more consistent, we track how much the user has moved since the last\n    // delta change and we only update it after it has reached a certain threshold.\n\n    if (changeX > this._config.pointerDirectionChangeThreshold) {\n      delta.x = x > positionSinceLastChange.x ? 1 : -1;\n      positionSinceLastChange.x = x;\n    }\n\n    if (changeY > this._config.pointerDirectionChangeThreshold) {\n      delta.y = y > positionSinceLastChange.y ? 1 : -1;\n      positionSinceLastChange.y = y;\n    }\n\n    return delta;\n  }\n  /** Toggles the native drag interactions, based on how many handles are registered. */\n\n\n  _toggleNativeDragInteractions() {\n    if (!this._rootElement || !this._handles) {\n      return;\n    }\n\n    const shouldEnable = this._handles.length > 0 || !this.isDragging();\n\n    if (shouldEnable !== this._nativeInteractionsEnabled) {\n      this._nativeInteractionsEnabled = shouldEnable;\n      toggleNativeDragInteractions(this._rootElement, shouldEnable);\n    }\n  }\n  /** Removes the manually-added event listeners from the root element. */\n\n\n  _removeRootElementListeners(element) {\n    element.removeEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n    element.removeEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n  }\n  /**\n   * Applies a `transform` to the root element, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n\n\n  _applyRootElementTransform(x, y) {\n    const transform = getTransform(x, y);\n    const styles = this._rootElement.style; // Cache the previous transform amount only after the first drag sequence, because\n    // we don't want our own transforms to stack on top of each other.\n    // Should be excluded none because none + translate3d(x, y, x) is invalid css\n\n    if (this._initialTransform == null) {\n      this._initialTransform = styles.transform && styles.transform != 'none' ? styles.transform : '';\n    } // Preserve the previous `transform` value, if there was one. Note that we apply our own\n    // transform before the user's, because things like rotation can affect which direction\n    // the element will be translated towards.\n\n\n    styles.transform = combineTransforms(transform, this._initialTransform);\n  }\n  /**\n   * Applies a `transform` to the preview, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n\n\n  _applyPreviewTransform(x, y) {\n    var _a; // Only apply the initial transform if the preview is a clone of the original element, otherwise\n    // it could be completely different and the transform might not make sense anymore.\n\n\n    const initialTransform = ((_a = this._previewTemplate) === null || _a === void 0 ? void 0 : _a.template) ? undefined : this._initialTransform;\n    const transform = getTransform(x, y);\n    this._preview.style.transform = combineTransforms(transform, initialTransform);\n  }\n  /**\n   * Gets the distance that the user has dragged during the current drag sequence.\n   * @param currentPosition Current position of the user's pointer.\n   */\n\n\n  _getDragDistance(currentPosition) {\n    const pickupPosition = this._pickupPositionOnPage;\n\n    if (pickupPosition) {\n      return {\n        x: currentPosition.x - pickupPosition.x,\n        y: currentPosition.y - pickupPosition.y\n      };\n    }\n\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n\n\n  _cleanupCachedDimensions() {\n    this._boundaryRect = this._previewRect = undefined;\n\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the element is still inside its boundary after the viewport has been resized.\n   * If not, the position is adjusted so that the element fits again.\n   */\n\n\n  _containInsideBoundaryOnResize() {\n    let {\n      x,\n      y\n    } = this._passiveTransform;\n\n    if (x === 0 && y === 0 || this.isDragging() || !this._boundaryElement) {\n      return;\n    }\n\n    const boundaryRect = this._boundaryElement.getBoundingClientRect();\n\n    const elementRect = this._rootElement.getBoundingClientRect(); // It's possible that the element got hidden away after dragging (e.g. by switching to a\n    // different tab). Don't do anything in this case so we don't clear the user's position.\n\n\n    if (boundaryRect.width === 0 && boundaryRect.height === 0 || elementRect.width === 0 && elementRect.height === 0) {\n      return;\n    }\n\n    const leftOverflow = boundaryRect.left - elementRect.left;\n    const rightOverflow = elementRect.right - boundaryRect.right;\n    const topOverflow = boundaryRect.top - elementRect.top;\n    const bottomOverflow = elementRect.bottom - boundaryRect.bottom; // If the element has become wider than the boundary, we can't\n    // do much to make it fit so we just anchor it to the left.\n\n    if (boundaryRect.width > elementRect.width) {\n      if (leftOverflow > 0) {\n        x += leftOverflow;\n      }\n\n      if (rightOverflow > 0) {\n        x -= rightOverflow;\n      }\n    } else {\n      x = 0;\n    } // If the element has become taller than the boundary, we can't\n    // do much to make it fit so we just anchor it to the top.\n\n\n    if (boundaryRect.height > elementRect.height) {\n      if (topOverflow > 0) {\n        y += topOverflow;\n      }\n\n      if (bottomOverflow > 0) {\n        y -= bottomOverflow;\n      }\n    } else {\n      y = 0;\n    }\n\n    if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n      this.setFreeDragPosition({\n        y,\n        x\n      });\n    }\n  }\n  /** Gets the drag start delay, based on the event type. */\n\n\n  _getDragStartDelay(event) {\n    const value = this.dragStartDelay;\n\n    if (typeof value === 'number') {\n      return value;\n    } else if (isTouchEvent(event)) {\n      return value.touch;\n    }\n\n    return value ? value.mouse : 0;\n  }\n  /** Updates the internal state of the draggable element when scrolling has occurred. */\n\n\n  _updateOnScroll(event) {\n    const scrollDifference = this._parentPositions.handleScroll(event);\n\n    if (scrollDifference) {\n      const target = _getEventTarget(event); // ClientRect dimensions are based on the scroll position of the page and its parent\n      // node so we have to update the cached boundary ClientRect if the user has scrolled.\n\n\n      if (this._boundaryRect && target !== this._boundaryElement && target.contains(this._boundaryElement)) {\n        adjustClientRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n      }\n\n      this._pickupPositionOnPage.x += scrollDifference.left;\n      this._pickupPositionOnPage.y += scrollDifference.top; // If we're in free drag mode, we have to update the active transform, because\n      // it isn't relative to the viewport like the preview inside a drop list.\n\n      if (!this._dropContainer) {\n        this._activeTransform.x -= scrollDifference.left;\n        this._activeTransform.y -= scrollDifference.top;\n\n        this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n      }\n    }\n  }\n  /** Gets the scroll position of the viewport. */\n\n\n  _getViewportScrollPosition() {\n    const cachedPosition = this._parentPositions.positions.get(this._document);\n\n    return cachedPosition ? cachedPosition.scrollPosition : this._viewportRuler.getViewportScrollPosition();\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n\n\n  _getShadowRoot() {\n    if (this._cachedShadowRoot === undefined) {\n      this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n    }\n\n    return this._cachedShadowRoot;\n  }\n  /** Gets the element into which the drag preview should be inserted. */\n\n\n  _getPreviewInsertionPoint(initialParent, shadowRoot) {\n    const previewContainer = this._previewContainer || 'global';\n\n    if (previewContainer === 'parent') {\n      return initialParent;\n    }\n\n    if (previewContainer === 'global') {\n      const documentRef = this._document; // We can't use the body if the user is in fullscreen mode,\n      // because the preview will render under the fullscreen element.\n      // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n\n      return shadowRoot || documentRef.fullscreenElement || documentRef.webkitFullscreenElement || documentRef.mozFullScreenElement || documentRef.msFullscreenElement || documentRef.body;\n    }\n\n    return coerceElement(previewContainer);\n  }\n\n}\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\n\n\nfunction getTransform(x, y) {\n  // Round the transforms since some browsers will\n  // blur the elements for sub-pixel transforms.\n  return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n/** Clamps a value between a minimum and a maximum. */\n\n\nfunction clamp$1(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/** Determines whether an event is a touch event. */\n\n\nfunction isTouchEvent(event) {\n  // This function is called for every pixel that the user has dragged so we need it to be\n  // as fast as possible. Since we only bind mouse events and touch events, we can assume\n  // that if the event's name starts with `t`, it's a touch event.\n  return event.type[0] === 't';\n}\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\n\n\nfunction getRootNode(viewRef, _document) {\n  const rootNodes = viewRef.rootNodes;\n\n  if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n    return rootNodes[0];\n  }\n\n  const wrapper = _document.createElement('div');\n\n  rootNodes.forEach(node => wrapper.appendChild(node));\n  return wrapper;\n}\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\n\n\nfunction matchElementSize(target, sourceRect) {\n  target.style.width = `${sourceRect.width}px`;\n  target.style.height = `${sourceRect.height}px`;\n  target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\n\n\nfunction moveItemInArray(array, fromIndex, toIndex) {\n  const from = clamp(fromIndex, array.length - 1);\n  const to = clamp(toIndex, array.length - 1);\n\n  if (from === to) {\n    return;\n  }\n\n  const target = array[from];\n  const delta = to < from ? -1 : 1;\n\n  for (let i = from; i !== to; i += delta) {\n    array[i] = array[i + delta];\n  }\n\n  array[to] = target;\n}\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\n\n\nfunction transferArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const from = clamp(currentIndex, currentArray.length - 1);\n  const to = clamp(targetIndex, targetArray.length);\n\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n  }\n}\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\n\n\nfunction copyArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const to = clamp(targetIndex, targetArray.length);\n\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray[currentIndex]);\n  }\n}\n/** Clamps a number between zero and a maximum. */\n\n\nfunction clamp(value, max) {\n  return Math.max(0, Math.min(max, value));\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\n\n\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\n\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\n\nclass DropListRef {\n  constructor(element, _dragDropRegistry, _document, _ngZone, _viewportRuler) {\n    this._dragDropRegistry = _dragDropRegistry;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    /** Whether starting a dragging sequence from this container is disabled. */\n\n    this.disabled = false;\n    /** Whether sorting items within the list is disabled. */\n\n    this.sortingDisabled = false;\n    /**\n     * Whether auto-scrolling the view when the user\n     * moves their pointer close to the edges is disabled.\n     */\n\n    this.autoScrollDisabled = false;\n    /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n\n    this.autoScrollStep = 2;\n    /**\n     * Function that is used to determine whether an item\n     * is allowed to be moved into a drop container.\n     */\n\n    this.enterPredicate = () => true;\n    /** Functions that is used to determine whether an item can be sorted into a particular index. */\n\n\n    this.sortPredicate = () => true;\n    /** Emits right before dragging has started. */\n\n\n    this.beforeStarted = new Subject();\n    /**\n     * Emits when the user has moved a new drag item into this container.\n     */\n\n    this.entered = new Subject();\n    /**\n     * Emits when the user removes an item from the container\n     * by dragging it into another container.\n     */\n\n    this.exited = new Subject();\n    /** Emits when the user drops an item inside the container. */\n\n    this.dropped = new Subject();\n    /** Emits as the user is swapping items while actively dragging. */\n\n    this.sorted = new Subject();\n    /** Whether an item in the list is being dragged. */\n\n    this._isDragging = false;\n    /** Cache of the dimensions of all the items inside the container. */\n\n    this._itemPositions = [];\n    /**\n     * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n     * the pointer was moving in when the swap occured and whether the user's pointer continued to\n     * overlap with the swapped item after the swapping occurred.\n     */\n\n    this._previousSwap = {\n      drag: null,\n      delta: 0,\n      overlaps: false\n    };\n    /** Draggable items in the container. */\n\n    this._draggables = [];\n    /** Drop lists that are connected to the current one. */\n\n    this._siblings = [];\n    /** Direction in which the list is oriented. */\n\n    this._orientation = 'vertical';\n    /** Connected siblings that currently have a dragged item. */\n\n    this._activeSiblings = new Set();\n    /** Layout direction of the drop list. */\n\n    this._direction = 'ltr';\n    /** Subscription to the window being scrolled. */\n\n    this._viewportScrollSubscription = Subscription.EMPTY;\n    /** Vertical direction in which the list is currently scrolling. */\n\n    this._verticalScrollDirection = 0\n    /* NONE */\n    ;\n    /** Horizontal direction in which the list is currently scrolling. */\n\n    this._horizontalScrollDirection = 0\n    /* NONE */\n    ;\n    /** Used to signal to the current auto-scroll sequence when to stop. */\n\n    this._stopScrollTimers = new Subject();\n    /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n\n    this._cachedShadowRoot = null;\n    /** Starts the interval that'll auto-scroll the element. */\n\n    this._startScrollInterval = () => {\n      this._stopScrolling();\n\n      interval(0, animationFrameScheduler).pipe(takeUntil(this._stopScrollTimers)).subscribe(() => {\n        const node = this._scrollNode;\n        const scrollStep = this.autoScrollStep;\n\n        if (this._verticalScrollDirection === 1\n        /* UP */\n        ) {\n          node.scrollBy(0, -scrollStep);\n        } else if (this._verticalScrollDirection === 2\n        /* DOWN */\n        ) {\n          node.scrollBy(0, scrollStep);\n        }\n\n        if (this._horizontalScrollDirection === 1\n        /* LEFT */\n        ) {\n          node.scrollBy(-scrollStep, 0);\n        } else if (this._horizontalScrollDirection === 2\n        /* RIGHT */\n        ) {\n          node.scrollBy(scrollStep, 0);\n        }\n      });\n    };\n\n    this.element = coerceElement(element);\n    this._document = _document;\n    this.withScrollableParents([this.element]);\n\n    _dragDropRegistry.registerDropContainer(this);\n\n    this._parentPositions = new ParentPositionTracker(_document, _viewportRuler);\n  }\n  /** Removes the drop list functionality from the DOM element. */\n\n\n  dispose() {\n    this._stopScrolling();\n\n    this._stopScrollTimers.complete();\n\n    this._viewportScrollSubscription.unsubscribe();\n\n    this.beforeStarted.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this.sorted.complete();\n\n    this._activeSiblings.clear();\n\n    this._scrollNode = null;\n\n    this._parentPositions.clear();\n\n    this._dragDropRegistry.removeDropContainer(this);\n  }\n  /** Whether an item from this list is currently being dragged. */\n\n\n  isDragging() {\n    return this._isDragging;\n  }\n  /** Starts dragging an item. */\n\n\n  start() {\n    this._draggingStarted();\n\n    this._notifyReceivingSiblings();\n  }\n  /**\n   * Emits an event to indicate that the user moved an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n\n\n  enter(item, pointerX, pointerY, index) {\n    this._draggingStarted(); // If sorting is disabled, we want the item to return to its starting\n    // position if the user is returning it to its initial container.\n\n\n    let newIndex;\n\n    if (index == null) {\n      newIndex = this.sortingDisabled ? this._draggables.indexOf(item) : -1;\n\n      if (newIndex === -1) {\n        // We use the coordinates of where the item entered the drop\n        // zone to figure out at which index it should be inserted.\n        newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY);\n      }\n    } else {\n      newIndex = index;\n    }\n\n    const activeDraggables = this._activeDraggables;\n    const currentIndex = activeDraggables.indexOf(item);\n    const placeholder = item.getPlaceholderElement();\n    let newPositionReference = activeDraggables[newIndex]; // If the item at the new position is the same as the item that is being dragged,\n    // it means that we're trying to restore the item to its initial position. In this\n    // case we should use the next item from the list as the reference.\n\n    if (newPositionReference === item) {\n      newPositionReference = activeDraggables[newIndex + 1];\n    } // If we didn't find a new position reference, it means that either the item didn't start off\n    // in this container, or that the item requested to be inserted at the end of the list.\n\n\n    if (!newPositionReference && (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) && this._shouldEnterAsFirstChild(pointerX, pointerY)) {\n      newPositionReference = activeDraggables[0];\n    } // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n    // into another container and back again), we have to ensure that it isn't duplicated.\n\n\n    if (currentIndex > -1) {\n      activeDraggables.splice(currentIndex, 1);\n    } // Don't use items that are being dragged as a reference, because\n    // their element has been moved down to the bottom of the body.\n\n\n    if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n      const element = newPositionReference.getRootElement();\n      element.parentElement.insertBefore(placeholder, element);\n      activeDraggables.splice(newIndex, 0, item);\n    } else {\n      coerceElement(this.element).appendChild(placeholder);\n      activeDraggables.push(item);\n    } // The transform needs to be cleared so it doesn't throw off the measurements.\n\n\n    placeholder.style.transform = ''; // Note that the positions were already cached when we called `start` above,\n    // but we need to refresh them since the amount of items has changed and also parent rects.\n\n    this._cacheItemPositions();\n\n    this._cacheParentPositions(); // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n\n\n    this._notifyReceivingSiblings();\n\n    this.entered.next({\n      item,\n      container: this,\n      currentIndex: this.getItemIndex(item)\n    });\n  }\n  /**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */\n\n\n  exit(item) {\n    this._reset();\n\n    this.exited.next({\n      item,\n      container: this\n    });\n  }\n  /**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   */\n\n\n  drop(item, currentIndex, previousIndex, previousContainer, isPointerOverContainer, distance, dropPoint) {\n    this._reset();\n\n    this.dropped.next({\n      item,\n      currentIndex,\n      previousIndex,\n      container: this,\n      previousContainer,\n      isPointerOverContainer,\n      distance,\n      dropPoint\n    });\n  }\n  /**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */\n\n\n  withItems(items) {\n    const previousItems = this._draggables;\n    this._draggables = items;\n    items.forEach(item => item._withDropContainer(this));\n\n    if (this.isDragging()) {\n      const draggedItems = previousItems.filter(item => item.isDragging()); // If all of the items being dragged were removed\n      // from the list, abort the current drag sequence.\n\n      if (draggedItems.every(item => items.indexOf(item) === -1)) {\n        this._reset();\n      } else {\n        this._cacheItems();\n      }\n    }\n\n    return this;\n  }\n  /** Sets the layout direction of the drop list. */\n\n\n  withDirection(direction) {\n    this._direction = direction;\n    return this;\n  }\n  /**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */\n\n\n  connectedTo(connectedTo) {\n    this._siblings = connectedTo.slice();\n    return this;\n  }\n  /**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */\n\n\n  withOrientation(orientation) {\n    this._orientation = orientation;\n    return this;\n  }\n  /**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */\n\n\n  withScrollableParents(elements) {\n    const element = coerceElement(this.element); // We always allow the current element to be scrollable\n    // so we need to ensure that it's in the array.\n\n    this._scrollableElements = elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n    return this;\n  }\n  /** Gets the scrollable parents that are registered with this drop container. */\n\n\n  getScrollableParents() {\n    return this._scrollableElements;\n  }\n  /**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */\n\n\n  getItemIndex(item) {\n    if (!this._isDragging) {\n      return this._draggables.indexOf(item);\n    } // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n    // The rest of the logic still stands no matter what orientation we're in, however\n    // we need to invert the array when determining the index.\n\n\n    const items = this._orientation === 'horizontal' && this._direction === 'rtl' ? this._itemPositions.slice().reverse() : this._itemPositions;\n    return items.findIndex(currentItem => currentItem.drag === item);\n  }\n  /**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */\n\n\n  isReceiving() {\n    return this._activeSiblings.size > 0;\n  }\n  /**\n   * Sorts an item inside the container based on its position.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n\n\n  _sortItem(item, pointerX, pointerY, pointerDelta) {\n    // Don't sort the item if sorting is disabled or it's out of range.\n    if (this.sortingDisabled || !this._clientRect || !isPointerNearClientRect(this._clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n      return;\n    }\n\n    const siblings = this._itemPositions;\n\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n\n    if (newIndex === -1 && siblings.length > 0) {\n      return;\n    }\n\n    const isHorizontal = this._orientation === 'horizontal';\n    const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n    const siblingAtNewPosition = siblings[newIndex];\n    const currentPosition = siblings[currentIndex].clientRect;\n    const newPosition = siblingAtNewPosition.clientRect;\n    const delta = currentIndex > newIndex ? 1 : -1; // How many pixels the item's placeholder should be offset.\n\n    const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta); // How many pixels all the other items should be offset.\n\n\n    const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta); // Save the previous order of the items before moving the item to its new index.\n    // We use this to check whether an item has been moved as a result of the sorting.\n\n\n    const oldOrder = siblings.slice(); // Shuffle the array in place.\n\n    moveItemInArray(siblings, currentIndex, newIndex);\n    this.sorted.next({\n      previousIndex: currentIndex,\n      currentIndex: newIndex,\n      container: this,\n      item\n    });\n    siblings.forEach((sibling, index) => {\n      // Don't do anything if the position hasn't changed.\n      if (oldOrder[index] === sibling) {\n        return;\n      }\n\n      const isDraggedItem = sibling.drag === item;\n      const offset = isDraggedItem ? itemOffset : siblingOffset;\n      const elementToOffset = isDraggedItem ? item.getPlaceholderElement() : sibling.drag.getRootElement(); // Update the offset to reflect the new position.\n\n      sibling.offset += offset; // Since we're moving the items with a `transform`, we need to adjust their cached\n      // client rects to reflect their new position, as well as swap their positions in the cache.\n      // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n      // elements may be mid-animation which will give us a wrong result.\n\n      if (isHorizontal) {\n        // Round the transforms since some browsers will\n        // blur the elements, for sub-pixel transforms.\n        elementToOffset.style.transform = combineTransforms(`translate3d(${Math.round(sibling.offset)}px, 0, 0)`, sibling.initialTransform);\n        adjustClientRect(sibling.clientRect, 0, offset);\n      } else {\n        elementToOffset.style.transform = combineTransforms(`translate3d(0, ${Math.round(sibling.offset)}px, 0)`, sibling.initialTransform);\n        adjustClientRect(sibling.clientRect, offset, 0);\n      }\n    }); // Note that it's important that we do this after the client rects have been adjusted.\n\n    this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n    this._previousSwap.drag = siblingAtNewPosition.drag;\n    this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n  }\n  /**\n   * Checks whether the user's pointer is close to the edges of either the\n   * viewport or the drop list and starts the auto-scroll sequence.\n   * @param pointerX User's pointer position along the x axis.\n   * @param pointerY User's pointer position along the y axis.\n   */\n\n\n  _startScrollingIfNecessary(pointerX, pointerY) {\n    if (this.autoScrollDisabled) {\n      return;\n    }\n\n    let scrollNode;\n    let verticalScrollDirection = 0\n    /* NONE */\n    ;\n    let horizontalScrollDirection = 0\n    /* NONE */\n    ; // Check whether we should start scrolling any of the parent containers.\n\n    this._parentPositions.positions.forEach((position, element) => {\n      // We have special handling for the `document` below. Also this would be\n      // nicer with a  for...of loop, but it requires changing a compiler flag.\n      if (element === this._document || !position.clientRect || scrollNode) {\n        return;\n      }\n\n      if (isPointerNearClientRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n        [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(element, position.clientRect, pointerX, pointerY);\n\n        if (verticalScrollDirection || horizontalScrollDirection) {\n          scrollNode = element;\n        }\n      }\n    }); // Otherwise check if we can start scrolling the viewport.\n\n\n    if (!verticalScrollDirection && !horizontalScrollDirection) {\n      const {\n        width,\n        height\n      } = this._viewportRuler.getViewportSize();\n\n      const clientRect = {\n        width,\n        height,\n        top: 0,\n        right: width,\n        bottom: height,\n        left: 0\n      };\n      verticalScrollDirection = getVerticalScrollDirection(clientRect, pointerY);\n      horizontalScrollDirection = getHorizontalScrollDirection(clientRect, pointerX);\n      scrollNode = window;\n    }\n\n    if (scrollNode && (verticalScrollDirection !== this._verticalScrollDirection || horizontalScrollDirection !== this._horizontalScrollDirection || scrollNode !== this._scrollNode)) {\n      this._verticalScrollDirection = verticalScrollDirection;\n      this._horizontalScrollDirection = horizontalScrollDirection;\n      this._scrollNode = scrollNode;\n\n      if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n        this._ngZone.runOutsideAngular(this._startScrollInterval);\n      } else {\n        this._stopScrolling();\n      }\n    }\n  }\n  /** Stops any currently-running auto-scroll sequences. */\n\n\n  _stopScrolling() {\n    this._stopScrollTimers.next();\n  }\n  /** Starts the dragging sequence within the list. */\n\n\n  _draggingStarted() {\n    const styles = coerceElement(this.element).style;\n    this.beforeStarted.next();\n    this._isDragging = true; // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n    // scrolling. The browser seems to round the value based on the snapping points which means\n    // that we can't increment/decrement the scroll position.\n\n    this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n    styles.scrollSnapType = styles.msScrollSnapType = 'none';\n\n    this._cacheItems();\n\n    this._viewportScrollSubscription.unsubscribe();\n\n    this._listenToScrollEvents();\n  }\n  /** Caches the positions of the configured scrollable parents. */\n\n\n  _cacheParentPositions() {\n    const element = coerceElement(this.element);\n\n    this._parentPositions.cache(this._scrollableElements); // The list element is always in the `scrollableElements`\n    // so we can take advantage of the cached `ClientRect`.\n\n\n    this._clientRect = this._parentPositions.positions.get(element).clientRect;\n  }\n  /** Refreshes the position cache of the items and sibling containers. */\n\n\n  _cacheItemPositions() {\n    const isHorizontal = this._orientation === 'horizontal';\n    this._itemPositions = this._activeDraggables.map(drag => {\n      const elementToMeasure = drag.getVisibleElement();\n      return {\n        drag,\n        offset: 0,\n        initialTransform: elementToMeasure.style.transform || '',\n        clientRect: getMutableClientRect(elementToMeasure)\n      };\n    }).sort((a, b) => {\n      return isHorizontal ? a.clientRect.left - b.clientRect.left : a.clientRect.top - b.clientRect.top;\n    });\n  }\n  /** Resets the container to its initial state. */\n\n\n  _reset() {\n    this._isDragging = false;\n    const styles = coerceElement(this.element).style;\n    styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap; // TODO(crisbeto): may have to wait for the animations to finish.\n\n    this._activeDraggables.forEach(item => {\n      var _a;\n\n      const rootElement = item.getRootElement();\n\n      if (rootElement) {\n        const initialTransform = (_a = this._itemPositions.find(current => current.drag === item)) === null || _a === void 0 ? void 0 : _a.initialTransform;\n        rootElement.style.transform = initialTransform || '';\n      }\n    });\n\n    this._siblings.forEach(sibling => sibling._stopReceiving(this));\n\n    this._activeDraggables = [];\n    this._itemPositions = [];\n    this._previousSwap.drag = null;\n    this._previousSwap.delta = 0;\n    this._previousSwap.overlaps = false;\n\n    this._stopScrolling();\n\n    this._viewportScrollSubscription.unsubscribe();\n\n    this._parentPositions.clear();\n  }\n  /**\n   * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n   * @param currentIndex Index of the item currently being dragged.\n   * @param siblings All of the items in the list.\n   * @param delta Direction in which the user is moving.\n   */\n\n\n  _getSiblingOffsetPx(currentIndex, siblings, delta) {\n    const isHorizontal = this._orientation === 'horizontal';\n    const currentPosition = siblings[currentIndex].clientRect;\n    const immediateSibling = siblings[currentIndex + delta * -1];\n    let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n\n    if (immediateSibling) {\n      const start = isHorizontal ? 'left' : 'top';\n      const end = isHorizontal ? 'right' : 'bottom'; // Get the spacing between the start of the current item and the end of the one immediately\n      // after it in the direction in which the user is dragging, or vice versa. We add it to the\n      // offset in order to push the element to where it will be when it's inline and is influenced\n      // by the `margin` of its siblings.\n\n      if (delta === -1) {\n        siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n      } else {\n        siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n      }\n    }\n\n    return siblingOffset;\n  }\n  /**\n   * Gets the offset in pixels by which the item that is being dragged should be moved.\n   * @param currentPosition Current position of the item.\n   * @param newPosition Position of the item where the current item should be moved.\n   * @param delta Direction in which the user is moving.\n   */\n\n\n  _getItemOffsetPx(currentPosition, newPosition, delta) {\n    const isHorizontal = this._orientation === 'horizontal';\n    let itemOffset = isHorizontal ? newPosition.left - currentPosition.left : newPosition.top - currentPosition.top; // Account for differences in the item width/height.\n\n    if (delta === -1) {\n      itemOffset += isHorizontal ? newPosition.width - currentPosition.width : newPosition.height - currentPosition.height;\n    }\n\n    return itemOffset;\n  }\n  /**\n   * Checks if pointer is entering in the first position\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n\n\n  _shouldEnterAsFirstChild(pointerX, pointerY) {\n    if (!this._activeDraggables.length) {\n      return false;\n    }\n\n    const itemPositions = this._itemPositions;\n    const isHorizontal = this._orientation === 'horizontal'; // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n    // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n\n    const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n\n    if (reversed) {\n      const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n      return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n    } else {\n      const firstItemRect = itemPositions[0].clientRect;\n      return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n    }\n  }\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n\n\n  _getItemIndexFromPointerPosition(item, pointerX, pointerY, delta) {\n    const isHorizontal = this._orientation === 'horizontal';\n\n    const index = this._itemPositions.findIndex(({\n      drag,\n      clientRect\n    }) => {\n      // Skip the item itself.\n      if (drag === item) {\n        return false;\n      }\n\n      if (delta) {\n        const direction = isHorizontal ? delta.x : delta.y; // If the user is still hovering over the same item as last time, their cursor hasn't left\n        // the item after we made the swap, and they didn't change the direction in which they're\n        // dragging, we don't consider it a direction swap.\n\n        if (drag === this._previousSwap.drag && this._previousSwap.overlaps && direction === this._previousSwap.delta) {\n          return false;\n        }\n      }\n\n      return isHorizontal ? // Round these down since most browsers report client rects with\n      // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n      pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right) : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n    });\n\n    return index === -1 || !this.sortPredicate(index, item, this) ? -1 : index;\n  }\n  /** Caches the current items in the list and their positions. */\n\n\n  _cacheItems() {\n    this._activeDraggables = this._draggables.slice();\n\n    this._cacheItemPositions();\n\n    this._cacheParentPositions();\n  }\n  /**\n   * Checks whether the user's pointer is positioned over the container.\n   * @param x Pointer position along the X axis.\n   * @param y Pointer position along the Y axis.\n   */\n\n\n  _isOverContainer(x, y) {\n    return this._clientRect != null && isInsideClientRect(this._clientRect, x, y);\n  }\n  /**\n   * Figures out whether an item should be moved into a sibling\n   * drop container, based on its current position.\n   * @param item Drag item that is being moved.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n\n\n  _getSiblingContainerFromPosition(item, x, y) {\n    return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n  }\n  /**\n   * Checks whether the drop list can receive the passed-in item.\n   * @param item Item that is being dragged into the list.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n\n\n  _canReceive(item, x, y) {\n    if (!this._clientRect || !isInsideClientRect(this._clientRect, x, y) || !this.enterPredicate(item, this)) {\n      return false;\n    }\n\n    const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y); // If there's no element at the pointer position, then\n    // the client rect is probably scrolled out of the view.\n\n\n    if (!elementFromPoint) {\n      return false;\n    }\n\n    const nativeElement = coerceElement(this.element); // The `ClientRect`, that we're using to find the container over which the user is\n    // hovering, doesn't give us any information on whether the element has been scrolled\n    // out of the view or whether it's overlapping with other containers. This means that\n    // we could end up transferring the item into a container that's invisible or is positioned\n    // below another one. We use the result from `elementFromPoint` to get the top-most element\n    // at the pointer position and to find whether it's one of the intersecting drop containers.\n\n    return elementFromPoint === nativeElement || nativeElement.contains(elementFromPoint);\n  }\n  /**\n   * Called by one of the connected drop lists when a dragging sequence has started.\n   * @param sibling Sibling in which dragging has started.\n   */\n\n\n  _startReceiving(sibling, items) {\n    const activeSiblings = this._activeSiblings;\n\n    if (!activeSiblings.has(sibling) && items.every(item => {\n      // Note that we have to add an exception to the `enterPredicate` for items that started off\n      // in this drop list. The drag ref has logic that allows an item to return to its initial\n      // container, if it has left the initial container and none of the connected containers\n      // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n      return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n    })) {\n      activeSiblings.add(sibling);\n\n      this._cacheParentPositions();\n\n      this._listenToScrollEvents();\n    }\n  }\n  /**\n   * Called by a connected drop list when dragging has stopped.\n   * @param sibling Sibling whose dragging has stopped.\n   */\n\n\n  _stopReceiving(sibling) {\n    this._activeSiblings.delete(sibling);\n\n    this._viewportScrollSubscription.unsubscribe();\n  }\n  /**\n   * Starts listening to scroll events on the viewport.\n   * Used for updating the internal state of the list.\n   */\n\n\n  _listenToScrollEvents() {\n    this._viewportScrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(event => {\n      if (this.isDragging()) {\n        const scrollDifference = this._parentPositions.handleScroll(event);\n\n        if (scrollDifference) {\n          // Since we know the amount that the user has scrolled we can shift all of the\n          // client rectangles ourselves. This is cheaper than re-measuring everything and\n          // we can avoid inconsistent behavior where we might be measuring the element before\n          // its position has changed.\n          this._itemPositions.forEach(({\n            clientRect\n          }) => {\n            adjustClientRect(clientRect, scrollDifference.top, scrollDifference.left);\n          }); // We need two loops for this, because we want all of the cached\n          // positions to be up-to-date before we re-sort the item.\n\n\n          this._itemPositions.forEach(({\n            drag\n          }) => {\n            if (this._dragDropRegistry.isDragging(drag)) {\n              // We need to re-sort the item manually, because the pointer move\n              // events won't be dispatched while the user is scrolling.\n              drag._sortFromLastPointerPosition();\n            }\n          });\n        }\n      } else if (this.isReceiving()) {\n        this._cacheParentPositions();\n      }\n    });\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n\n\n  _getShadowRoot() {\n    if (!this._cachedShadowRoot) {\n      const shadowRoot = _getShadowRoot(coerceElement(this.element));\n\n      this._cachedShadowRoot = shadowRoot || this._document;\n    }\n\n    return this._cachedShadowRoot;\n  }\n  /** Notifies any siblings that may potentially receive the item. */\n\n\n  _notifyReceivingSiblings() {\n    const draggedItems = this._activeDraggables.filter(item => item.isDragging());\n\n    this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n  }\n\n}\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\n\n\nfunction getVerticalScrollDirection(clientRect, pointerY) {\n  const {\n    top,\n    bottom,\n    height\n  } = clientRect;\n  const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n\n  if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n    return 1\n    /* UP */\n    ;\n  } else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n    return 2\n    /* DOWN */\n    ;\n  }\n\n  return 0\n  /* NONE */\n  ;\n}\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\n\n\nfunction getHorizontalScrollDirection(clientRect, pointerX) {\n  const {\n    left,\n    right,\n    width\n  } = clientRect;\n  const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n\n  if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n    return 1\n    /* LEFT */\n    ;\n  } else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n    return 2\n    /* RIGHT */\n    ;\n  }\n\n  return 0\n  /* NONE */\n  ;\n}\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\n\n\nfunction getElementScrollDirections(element, clientRect, pointerX, pointerY) {\n  const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n  const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n  let verticalScrollDirection = 0\n  /* NONE */\n  ;\n  let horizontalScrollDirection = 0\n  /* NONE */\n  ; // Note that we here we do some extra checks for whether the element is actually scrollable in\n  // a certain direction and we only assign the scroll direction if it is. We do this so that we\n  // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n  // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n\n  if (computedVertical) {\n    const scrollTop = element.scrollTop;\n\n    if (computedVertical === 1\n    /* UP */\n    ) {\n      if (scrollTop > 0) {\n        verticalScrollDirection = 1\n        /* UP */\n        ;\n      }\n    } else if (element.scrollHeight - scrollTop > element.clientHeight) {\n      verticalScrollDirection = 2\n      /* DOWN */\n      ;\n    }\n  }\n\n  if (computedHorizontal) {\n    const scrollLeft = element.scrollLeft;\n\n    if (computedHorizontal === 1\n    /* LEFT */\n    ) {\n      if (scrollLeft > 0) {\n        horizontalScrollDirection = 1\n        /* LEFT */\n        ;\n      }\n    } else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n      horizontalScrollDirection = 2\n      /* RIGHT */\n      ;\n    }\n  }\n\n  return [verticalScrollDirection, horizontalScrollDirection];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Event options that can be used to bind an active, capturing event. */\n\n\nconst activeCapturingEventOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: false,\n  capture: true\n});\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\n// Note: this class is generic, rather than referencing CdkDrag and CdkDropList directly, in order\n// to avoid circular imports. If we were to reference them here, importing the registry into the\n// classes that are registering themselves will introduce a circular import.\n\nlet DragDropRegistry = /*#__PURE__*/(() => {\n  class DragDropRegistry {\n    constructor(_ngZone, _document) {\n      this._ngZone = _ngZone;\n      /** Registered drop container instances. */\n\n      this._dropInstances = new Set();\n      /** Registered drag item instances. */\n\n      this._dragInstances = new Set();\n      /** Drag item instances that are currently being dragged. */\n\n      this._activeDragInstances = [];\n      /** Keeps track of the event listeners that we've bound to the `document`. */\n\n      this._globalListeners = new Map();\n      /**\n       * Predicate function to check if an item is being dragged.  Moved out into a property,\n       * because it'll be called a lot and we don't want to create a new function every time.\n       */\n\n      this._draggingPredicate = item => item.isDragging();\n      /**\n       * Emits the `touchmove` or `mousemove` events that are dispatched\n       * while the user is dragging a drag item instance.\n       */\n\n\n      this.pointerMove = new Subject();\n      /**\n       * Emits the `touchend` or `mouseup` events that are dispatched\n       * while the user is dragging a drag item instance.\n       */\n\n      this.pointerUp = new Subject();\n      /**\n       * Emits when the viewport has been scrolled while the user is dragging an item.\n       * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n       * @breaking-change 13.0.0\n       */\n\n      this.scroll = new Subject();\n      /**\n       * Event listener that will prevent the default browser action while the user is dragging.\n       * @param event Event whose default action should be prevented.\n       */\n\n      this._preventDefaultWhileDragging = event => {\n        if (this._activeDragInstances.length > 0) {\n          event.preventDefault();\n        }\n      };\n      /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n\n\n      this._persistentTouchmoveListener = event => {\n        if (this._activeDragInstances.length > 0) {\n          // Note that we only want to prevent the default action after dragging has actually started.\n          // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n          // but it could be pushed back if the user has set up a drag delay or threshold.\n          if (this._activeDragInstances.some(this._draggingPredicate)) {\n            event.preventDefault();\n          }\n\n          this.pointerMove.next(event);\n        }\n      };\n\n      this._document = _document;\n    }\n    /** Adds a drop container to the registry. */\n\n\n    registerDropContainer(drop) {\n      if (!this._dropInstances.has(drop)) {\n        this._dropInstances.add(drop);\n      }\n    }\n    /** Adds a drag item instance to the registry. */\n\n\n    registerDragItem(drag) {\n      this._dragInstances.add(drag); // The `touchmove` event gets bound once, ahead of time, because WebKit\n      // won't preventDefault on a dynamically-added `touchmove` listener.\n      // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n\n\n      if (this._dragInstances.size === 1) {\n        this._ngZone.runOutsideAngular(() => {\n          // The event handler has to be explicitly active,\n          // because newer browsers make it passive by default.\n          this._document.addEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n        });\n      }\n    }\n    /** Removes a drop container from the registry. */\n\n\n    removeDropContainer(drop) {\n      this._dropInstances.delete(drop);\n    }\n    /** Removes a drag item instance from the registry. */\n\n\n    removeDragItem(drag) {\n      this._dragInstances.delete(drag);\n\n      this.stopDragging(drag);\n\n      if (this._dragInstances.size === 0) {\n        this._document.removeEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n      }\n    }\n    /**\n     * Starts the dragging sequence for a drag instance.\n     * @param drag Drag instance which is being dragged.\n     * @param event Event that initiated the dragging.\n     */\n\n\n    startDragging(drag, event) {\n      // Do not process the same drag twice to avoid memory leaks and redundant listeners\n      if (this._activeDragInstances.indexOf(drag) > -1) {\n        return;\n      }\n\n      this._activeDragInstances.push(drag);\n\n      if (this._activeDragInstances.length === 1) {\n        const isTouchEvent = event.type.startsWith('touch'); // We explicitly bind __active__ listeners here, because newer browsers will default to\n        // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n        // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n\n        this._globalListeners.set(isTouchEvent ? 'touchend' : 'mouseup', {\n          handler: e => this.pointerUp.next(e),\n          options: true\n        }).set('scroll', {\n          handler: e => this.scroll.next(e),\n          // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n          // the document. See https://github.com/angular/components/issues/17144.\n          options: true\n        }) // Preventing the default action on `mousemove` isn't enough to disable text selection\n        // on Safari so we need to prevent the selection event as well. Alternatively this can\n        // be done by setting `user-select: none` on the `body`, however it has causes a style\n        // recalculation which can be expensive on pages with a lot of elements.\n        .set('selectstart', {\n          handler: this._preventDefaultWhileDragging,\n          options: activeCapturingEventOptions\n        }); // We don't have to bind a move event for touch drag sequences, because\n        // we already have a persistent global one bound from `registerDragItem`.\n\n\n        if (!isTouchEvent) {\n          this._globalListeners.set('mousemove', {\n            handler: e => this.pointerMove.next(e),\n            options: activeCapturingEventOptions\n          });\n        }\n\n        this._ngZone.runOutsideAngular(() => {\n          this._globalListeners.forEach((config, name) => {\n            this._document.addEventListener(name, config.handler, config.options);\n          });\n        });\n      }\n    }\n    /** Stops dragging a drag item instance. */\n\n\n    stopDragging(drag) {\n      const index = this._activeDragInstances.indexOf(drag);\n\n      if (index > -1) {\n        this._activeDragInstances.splice(index, 1);\n\n        if (this._activeDragInstances.length === 0) {\n          this._clearGlobalListeners();\n        }\n      }\n    }\n    /** Gets whether a drag item instance is currently being dragged. */\n\n\n    isDragging(drag) {\n      return this._activeDragInstances.indexOf(drag) > -1;\n    }\n    /**\n     * Gets a stream that will emit when any element on the page is scrolled while an item is being\n     * dragged.\n     * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n     *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n     *   be used to include an additional top-level listener at the shadow root level.\n     */\n\n\n    scrolled(shadowRoot) {\n      const streams = [this.scroll];\n\n      if (shadowRoot && shadowRoot !== this._document) {\n        // Note that this is basically the same as `fromEvent` from rjxs, but we do it ourselves,\n        // because we want to guarantee that the event is bound outside of the `NgZone`. With\n        // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n        streams.push(new Observable(observer => {\n          return this._ngZone.runOutsideAngular(() => {\n            const eventOptions = true;\n\n            const callback = event => {\n              if (this._activeDragInstances.length) {\n                observer.next(event);\n              }\n            };\n\n            shadowRoot.addEventListener('scroll', callback, eventOptions);\n            return () => {\n              shadowRoot.removeEventListener('scroll', callback, eventOptions);\n            };\n          });\n        }));\n      }\n\n      return merge(...streams);\n    }\n\n    ngOnDestroy() {\n      this._dragInstances.forEach(instance => this.removeDragItem(instance));\n\n      this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n\n      this._clearGlobalListeners();\n\n      this.pointerMove.complete();\n      this.pointerUp.complete();\n    }\n    /** Clears out the global event listeners from the `document`. */\n\n\n    _clearGlobalListeners() {\n      this._globalListeners.forEach((config, name) => {\n        this._document.removeEventListener(name, config.handler, config.options);\n      });\n\n      this._globalListeners.clear();\n    }\n\n  }\n\n  DragDropRegistry.ɵfac = function DragDropRegistry_Factory(t) {\n    return new (t || DragDropRegistry)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n  };\n\n  DragDropRegistry.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DragDropRegistry,\n    factory: DragDropRegistry.ɵfac,\n    providedIn: 'root'\n  });\n  return DragDropRegistry;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Default configuration to be used when creating a `DragRef`. */\n\n\nconst DEFAULT_CONFIG = {\n  dragStartThreshold: 5,\n  pointerDirectionChangeThreshold: 5\n};\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\n\nlet DragDrop = /*#__PURE__*/(() => {\n  class DragDrop {\n    constructor(_document, _ngZone, _viewportRuler, _dragDropRegistry) {\n      this._document = _document;\n      this._ngZone = _ngZone;\n      this._viewportRuler = _viewportRuler;\n      this._dragDropRegistry = _dragDropRegistry;\n    }\n    /**\n     * Turns an element into a draggable item.\n     * @param element Element to which to attach the dragging functionality.\n     * @param config Object used to configure the dragging behavior.\n     */\n\n\n    createDrag(element, config = DEFAULT_CONFIG) {\n      return new DragRef(element, config, this._document, this._ngZone, this._viewportRuler, this._dragDropRegistry);\n    }\n    /**\n     * Turns an element into a drop list.\n     * @param element Element to which to attach the drop list functionality.\n     */\n\n\n    createDropList(element) {\n      return new DropListRef(element, this._dragDropRegistry, this._document, this._ngZone, this._viewportRuler);\n    }\n\n  }\n\n  DragDrop.ɵfac = function DragDrop_Factory(t) {\n    return new (t || DragDrop)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DragDropRegistry));\n  };\n\n  DragDrop.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DragDrop,\n    factory: DragDrop.ɵfac,\n    providedIn: 'root'\n  });\n  return DragDrop;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\n\n\nconst CDK_DRAG_PARENT = /*#__PURE__*/new InjectionToken('CDK_DRAG_PARENT');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst CDK_DROP_LIST_GROUP = /*#__PURE__*/new InjectionToken('CdkDropListGroup');\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\n\nlet CdkDropListGroup = /*#__PURE__*/(() => {\n  class CdkDropListGroup {\n    constructor() {\n      /** Drop lists registered inside the group. */\n      this._items = new Set();\n      this._disabled = false;\n    }\n    /** Whether starting a dragging sequence from inside this group is disabled. */\n\n\n    get disabled() {\n      return this._disabled;\n    }\n\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n\n    ngOnDestroy() {\n      this._items.clear();\n    }\n\n  }\n\n  CdkDropListGroup.ɵfac = function CdkDropListGroup_Factory(t) {\n    return new (t || CdkDropListGroup)();\n  };\n\n  CdkDropListGroup.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkDropListGroup,\n    selectors: [[\"\", \"cdkDropListGroup\", \"\"]],\n    inputs: {\n      disabled: [\"cdkDropListGroupDisabled\", \"disabled\"]\n    },\n    exportAs: [\"cdkDropListGroup\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_DROP_LIST_GROUP,\n      useExisting: CdkDropListGroup\n    }])]\n  });\n  return CdkDropListGroup;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\n\n\nconst CDK_DRAG_CONFIG = /*#__PURE__*/new InjectionToken('CDK_DRAG_CONFIG');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\n\nfunction assertElementNode(node, name) {\n  if (node.nodeType !== 1) {\n    throw Error(`${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`);\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Counter used to generate unique ids for drop zones. */\n\n\nlet _uniqueIdCounter = 0;\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst CDK_DROP_LIST = /*#__PURE__*/new InjectionToken('CdkDropList');\n/** Container that wraps a set of draggable items. */\n\nlet CdkDropList = /*#__PURE__*/(() => {\n  class CdkDropList {\n    constructor(\n    /** Element that the drop list is attached to. */\n    element, dragDrop, _changeDetectorRef, _scrollDispatcher, _dir, _group, config) {\n      this.element = element;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._scrollDispatcher = _scrollDispatcher;\n      this._dir = _dir;\n      this._group = _group;\n      /** Emits when the list has been destroyed. */\n\n      this._destroyed = new Subject();\n      /**\n       * Other draggable containers that this container is connected to and into which the\n       * container's items can be transferred. Can either be references to other drop containers,\n       * or their unique IDs.\n       */\n\n      this.connectedTo = [];\n      /**\n       * Unique ID for the drop zone. Can be used as a reference\n       * in the `connectedTo` of another `CdkDropList`.\n       */\n\n      this.id = `cdk-drop-list-${_uniqueIdCounter++}`;\n      /**\n       * Function that is used to determine whether an item\n       * is allowed to be moved into a drop container.\n       */\n\n      this.enterPredicate = () => true;\n      /** Functions that is used to determine whether an item can be sorted into a particular index. */\n\n\n      this.sortPredicate = () => true;\n      /** Emits when the user drops an item inside the container. */\n\n\n      this.dropped = new EventEmitter();\n      /**\n       * Emits when the user has moved a new drag item into this container.\n       */\n\n      this.entered = new EventEmitter();\n      /**\n       * Emits when the user removes an item from the container\n       * by dragging it into another container.\n       */\n\n      this.exited = new EventEmitter();\n      /** Emits as the user is swapping items while actively dragging. */\n\n      this.sorted = new EventEmitter();\n      /**\n       * Keeps track of the items that are registered with this container. Historically we used to\n       * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n       * well which means that we can't handle cases like dragging the headers of a `mat-table`\n       * correctly. What we do instead is to have the items register themselves with the container\n       * and then we sort them based on their position in the DOM.\n       */\n\n      this._unsortedItems = new Set();\n\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        assertElementNode(element.nativeElement, 'cdkDropList');\n      }\n\n      this._dropListRef = dragDrop.createDropList(element);\n      this._dropListRef.data = this;\n\n      if (config) {\n        this._assignDefaults(config);\n      }\n\n      this._dropListRef.enterPredicate = (drag, drop) => {\n        return this.enterPredicate(drag.data, drop.data);\n      };\n\n      this._dropListRef.sortPredicate = (index, drag, drop) => {\n        return this.sortPredicate(index, drag.data, drop.data);\n      };\n\n      this._setupInputSyncSubscription(this._dropListRef);\n\n      this._handleEvents(this._dropListRef);\n\n      CdkDropList._dropLists.push(this);\n\n      if (_group) {\n        _group._items.add(this);\n      }\n    }\n    /** Whether starting a dragging sequence from this container is disabled. */\n\n\n    get disabled() {\n      return this._disabled || !!this._group && this._group.disabled;\n    }\n\n    set disabled(value) {\n      // Usually we sync the directive and ref state right before dragging starts, in order to have\n      // a single point of failure and to avoid having to use setters for everything. `disabled` is\n      // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n      // the user in a disabled state, so we also need to sync it as it's being set.\n      this._dropListRef.disabled = this._disabled = coerceBooleanProperty(value);\n    }\n    /** Registers an items with the drop list. */\n\n\n    addItem(item) {\n      this._unsortedItems.add(item);\n\n      if (this._dropListRef.isDragging()) {\n        this._syncItemsWithRef();\n      }\n    }\n    /** Removes an item from the drop list. */\n\n\n    removeItem(item) {\n      this._unsortedItems.delete(item);\n\n      if (this._dropListRef.isDragging()) {\n        this._syncItemsWithRef();\n      }\n    }\n    /** Gets the registered items in the list, sorted by their position in the DOM. */\n\n\n    getSortedItems() {\n      return Array.from(this._unsortedItems).sort((a, b) => {\n        const documentPosition = a._dragRef.getVisibleElement().compareDocumentPosition(b._dragRef.getVisibleElement()); // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n        // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        // tslint:disable-next-line:no-bitwise\n\n\n        return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n      });\n    }\n\n    ngOnDestroy() {\n      const index = CdkDropList._dropLists.indexOf(this);\n\n      if (index > -1) {\n        CdkDropList._dropLists.splice(index, 1);\n      }\n\n      if (this._group) {\n        this._group._items.delete(this);\n      }\n\n      this._unsortedItems.clear();\n\n      this._dropListRef.dispose();\n\n      this._destroyed.next();\n\n      this._destroyed.complete();\n    }\n    /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n\n\n    _setupInputSyncSubscription(ref) {\n      if (this._dir) {\n        this._dir.change.pipe(startWith(this._dir.value), takeUntil(this._destroyed)).subscribe(value => ref.withDirection(value));\n      }\n\n      ref.beforeStarted.subscribe(() => {\n        const siblings = coerceArray(this.connectedTo).map(drop => {\n          if (typeof drop === 'string') {\n            const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n\n            if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n              console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n            }\n\n            return correspondingDropList;\n          }\n\n          return drop;\n        });\n\n        if (this._group) {\n          this._group._items.forEach(drop => {\n            if (siblings.indexOf(drop) === -1) {\n              siblings.push(drop);\n            }\n          });\n        } // Note that we resolve the scrollable parents here so that we delay the resolution\n        // as long as possible, ensuring that the element is in its final place in the DOM.\n\n\n        if (!this._scrollableParentsResolved) {\n          const scrollableParents = this._scrollDispatcher.getAncestorScrollContainers(this.element).map(scrollable => scrollable.getElementRef().nativeElement);\n\n          this._dropListRef.withScrollableParents(scrollableParents); // Only do this once since it involves traversing the DOM and the parents\n          // shouldn't be able to change without the drop list being destroyed.\n\n\n          this._scrollableParentsResolved = true;\n        }\n\n        ref.disabled = this.disabled;\n        ref.lockAxis = this.lockAxis;\n        ref.sortingDisabled = coerceBooleanProperty(this.sortingDisabled);\n        ref.autoScrollDisabled = coerceBooleanProperty(this.autoScrollDisabled);\n        ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n        ref.connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef)).withOrientation(this.orientation);\n      });\n    }\n    /** Handles events from the underlying DropListRef. */\n\n\n    _handleEvents(ref) {\n      ref.beforeStarted.subscribe(() => {\n        this._syncItemsWithRef();\n\n        this._changeDetectorRef.markForCheck();\n      });\n      ref.entered.subscribe(event => {\n        this.entered.emit({\n          container: this,\n          item: event.item.data,\n          currentIndex: event.currentIndex\n        });\n      });\n      ref.exited.subscribe(event => {\n        this.exited.emit({\n          container: this,\n          item: event.item.data\n        });\n\n        this._changeDetectorRef.markForCheck();\n      });\n      ref.sorted.subscribe(event => {\n        this.sorted.emit({\n          previousIndex: event.previousIndex,\n          currentIndex: event.currentIndex,\n          container: this,\n          item: event.item.data\n        });\n      });\n      ref.dropped.subscribe(event => {\n        this.dropped.emit({\n          previousIndex: event.previousIndex,\n          currentIndex: event.currentIndex,\n          previousContainer: event.previousContainer.data,\n          container: event.container.data,\n          item: event.item.data,\n          isPointerOverContainer: event.isPointerOverContainer,\n          distance: event.distance,\n          dropPoint: event.dropPoint\n        }); // Mark for check since all of these events run outside of change\n        // detection and we're not guaranteed for something else to have triggered it.\n\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n    /** Assigns the default input values based on a provided config object. */\n\n\n    _assignDefaults(config) {\n      const {\n        lockAxis,\n        draggingDisabled,\n        sortingDisabled,\n        listAutoScrollDisabled,\n        listOrientation\n      } = config;\n      this.disabled = draggingDisabled == null ? false : draggingDisabled;\n      this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n      this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n      this.orientation = listOrientation || 'vertical';\n\n      if (lockAxis) {\n        this.lockAxis = lockAxis;\n      }\n    }\n    /** Syncs up the registered drag items with underlying drop list ref. */\n\n\n    _syncItemsWithRef() {\n      this._dropListRef.withItems(this.getSortedItems().map(item => item._dragRef));\n    }\n\n  }\n\n  /** Keeps track of the drop lists that are currently on the page. */\n  CdkDropList._dropLists = [];\n\n  CdkDropList.ɵfac = function CdkDropList_Factory(t) {\n    return new (t || CdkDropList)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(CDK_DROP_LIST_GROUP, 12), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8));\n  };\n\n  CdkDropList.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkDropList,\n    selectors: [[\"\", \"cdkDropList\", \"\"], [\"cdk-drop-list\"]],\n    hostAttrs: [1, \"cdk-drop-list\"],\n    hostVars: 7,\n    hostBindings: function CdkDropList_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx.id);\n        i0.ɵɵclassProp(\"cdk-drop-list-disabled\", ctx.disabled)(\"cdk-drop-list-dragging\", ctx._dropListRef.isDragging())(\"cdk-drop-list-receiving\", ctx._dropListRef.isReceiving());\n      }\n    },\n    inputs: {\n      connectedTo: [\"cdkDropListConnectedTo\", \"connectedTo\"],\n      data: [\"cdkDropListData\", \"data\"],\n      orientation: [\"cdkDropListOrientation\", \"orientation\"],\n      id: \"id\",\n      lockAxis: [\"cdkDropListLockAxis\", \"lockAxis\"],\n      disabled: [\"cdkDropListDisabled\", \"disabled\"],\n      sortingDisabled: [\"cdkDropListSortingDisabled\", \"sortingDisabled\"],\n      enterPredicate: [\"cdkDropListEnterPredicate\", \"enterPredicate\"],\n      sortPredicate: [\"cdkDropListSortPredicate\", \"sortPredicate\"],\n      autoScrollDisabled: [\"cdkDropListAutoScrollDisabled\", \"autoScrollDisabled\"],\n      autoScrollStep: [\"cdkDropListAutoScrollStep\", \"autoScrollStep\"]\n    },\n    outputs: {\n      dropped: \"cdkDropListDropped\",\n      entered: \"cdkDropListEntered\",\n      exited: \"cdkDropListExited\",\n      sorted: \"cdkDropListSorted\"\n    },\n    exportAs: [\"cdkDropList\"],\n    features: [i0.ɵɵProvidersFeature([// Prevent child drop lists from picking up the same group as their parent.\n    {\n      provide: CDK_DROP_LIST_GROUP,\n      useValue: undefined\n    }, {\n      provide: CDK_DROP_LIST,\n      useExisting: CdkDropList\n    }])]\n  });\n  return CdkDropList;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst CDK_DRAG_HANDLE = /*#__PURE__*/new InjectionToken('CdkDragHandle');\n/** Handle that can be used to drag a CdkDrag instance. */\n\nlet CdkDragHandle = /*#__PURE__*/(() => {\n  class CdkDragHandle {\n    constructor(element, parentDrag) {\n      this.element = element;\n      /** Emits when the state of the handle has changed. */\n\n      this._stateChanges = new Subject();\n      this._disabled = false;\n\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        assertElementNode(element.nativeElement, 'cdkDragHandle');\n      }\n\n      this._parentDrag = parentDrag;\n    }\n    /** Whether starting to drag through this handle is disabled. */\n\n\n    get disabled() {\n      return this._disabled;\n    }\n\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n\n      this._stateChanges.next(this);\n    }\n\n    ngOnDestroy() {\n      this._stateChanges.complete();\n    }\n\n  }\n\n  CdkDragHandle.ɵfac = function CdkDragHandle_Factory(t) {\n    return new (t || CdkDragHandle)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n  };\n\n  CdkDragHandle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkDragHandle,\n    selectors: [[\"\", \"cdkDragHandle\", \"\"]],\n    hostAttrs: [1, \"cdk-drag-handle\"],\n    inputs: {\n      disabled: [\"cdkDragHandleDisabled\", \"disabled\"]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_DRAG_HANDLE,\n      useExisting: CdkDragHandle\n    }])]\n  });\n  return CdkDragHandle;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst CDK_DRAG_PLACEHOLDER = /*#__PURE__*/new InjectionToken('CdkDragPlaceholder');\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\n\nlet CdkDragPlaceholder = /*#__PURE__*/(() => {\n  class CdkDragPlaceholder {\n    constructor(templateRef) {\n      this.templateRef = templateRef;\n    }\n\n  }\n\n  CdkDragPlaceholder.ɵfac = function CdkDragPlaceholder_Factory(t) {\n    return new (t || CdkDragPlaceholder)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n  };\n\n  CdkDragPlaceholder.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkDragPlaceholder,\n    selectors: [[\"ng-template\", \"cdkDragPlaceholder\", \"\"]],\n    inputs: {\n      data: \"data\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_DRAG_PLACEHOLDER,\n      useExisting: CdkDragPlaceholder\n    }])]\n  });\n  return CdkDragPlaceholder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst CDK_DRAG_PREVIEW = /*#__PURE__*/new InjectionToken('CdkDragPreview');\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\n\nlet CdkDragPreview = /*#__PURE__*/(() => {\n  class CdkDragPreview {\n    constructor(templateRef) {\n      this.templateRef = templateRef;\n      this._matchSize = false;\n    }\n    /** Whether the preview should preserve the same size as the item that is being dragged. */\n\n\n    get matchSize() {\n      return this._matchSize;\n    }\n\n    set matchSize(value) {\n      this._matchSize = coerceBooleanProperty(value);\n    }\n\n  }\n\n  CdkDragPreview.ɵfac = function CdkDragPreview_Factory(t) {\n    return new (t || CdkDragPreview)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n  };\n\n  CdkDragPreview.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkDragPreview,\n    selectors: [[\"ng-template\", \"cdkDragPreview\", \"\"]],\n    inputs: {\n      data: \"data\",\n      matchSize: \"matchSize\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_DRAG_PREVIEW,\n      useExisting: CdkDragPreview\n    }])]\n  });\n  return CdkDragPreview;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst DRAG_HOST_CLASS = 'cdk-drag';\n/** Element that can be moved inside a CdkDropList container. */\n\nlet CdkDrag = /*#__PURE__*/(() => {\n  class CdkDrag {\n    constructor(\n    /** Element that the draggable is attached to. */\n    element,\n    /** Droppable container that the draggable is a part of. */\n    dropContainer,\n    /**\n     * @deprecated `_document` parameter no longer being used and will be removed.\n     * @breaking-change 12.0.0\n     */\n    _document, _ngZone, _viewContainerRef, config, _dir, dragDrop, _changeDetectorRef, _selfHandle, _parentDrag) {\n      this.element = element;\n      this.dropContainer = dropContainer;\n      this._ngZone = _ngZone;\n      this._viewContainerRef = _viewContainerRef;\n      this._dir = _dir;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._selfHandle = _selfHandle;\n      this._parentDrag = _parentDrag;\n      this._destroyed = new Subject();\n      /** Emits when the user starts dragging the item. */\n\n      this.started = new EventEmitter();\n      /** Emits when the user has released a drag item, before any animations have started. */\n\n      this.released = new EventEmitter();\n      /** Emits when the user stops dragging an item in the container. */\n\n      this.ended = new EventEmitter();\n      /** Emits when the user has moved the item into a new container. */\n\n      this.entered = new EventEmitter();\n      /** Emits when the user removes the item its container by dragging it into another container. */\n\n      this.exited = new EventEmitter();\n      /** Emits when the user drops the item inside a container. */\n\n      this.dropped = new EventEmitter();\n      /**\n       * Emits as the user is dragging the item. Use with caution,\n       * because this event will fire for every pixel that the user has dragged.\n       */\n\n      this.moved = new Observable(observer => {\n        const subscription = this._dragRef.moved.pipe(map(movedEvent => ({\n          source: this,\n          pointerPosition: movedEvent.pointerPosition,\n          event: movedEvent.event,\n          delta: movedEvent.delta,\n          distance: movedEvent.distance\n        }))).subscribe(observer);\n\n        return () => {\n          subscription.unsubscribe();\n        };\n      });\n      this._dragRef = dragDrop.createDrag(element, {\n        dragStartThreshold: config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n        pointerDirectionChangeThreshold: config && config.pointerDirectionChangeThreshold != null ? config.pointerDirectionChangeThreshold : 5,\n        zIndex: config === null || config === void 0 ? void 0 : config.zIndex\n      });\n      this._dragRef.data = this; // We have to keep track of the drag instances in order to be able to match an element to\n      // a drag instance. We can't go through the global registry of `DragRef`, because the root\n      // element could be different.\n\n      CdkDrag._dragInstances.push(this);\n\n      if (config) {\n        this._assignDefaults(config);\n      } // Note that usually the container is assigned when the drop list is picks up the item, but in\n      // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n      // where there are no items on the first change detection pass, but the items get picked up as\n      // soon as the user triggers another pass by dragging. This is a problem, because the item would\n      // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n      // is too late since the two modes save different kinds of information. We work around it by\n      // assigning the drop container both from here and the list.\n\n\n      if (dropContainer) {\n        this._dragRef._withDropContainer(dropContainer._dropListRef);\n\n        dropContainer.addItem(this);\n      }\n\n      this._syncInputs(this._dragRef);\n\n      this._handleEvents(this._dragRef);\n    }\n    /** Whether starting to drag this element is disabled. */\n\n\n    get disabled() {\n      return this._disabled || this.dropContainer && this.dropContainer.disabled;\n    }\n\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n      this._dragRef.disabled = this._disabled;\n    }\n    /**\n     * Returns the element that is being used as a placeholder\n     * while the current element is being dragged.\n     */\n\n\n    getPlaceholderElement() {\n      return this._dragRef.getPlaceholderElement();\n    }\n    /** Returns the root draggable element. */\n\n\n    getRootElement() {\n      return this._dragRef.getRootElement();\n    }\n    /** Resets a standalone drag item to its initial position. */\n\n\n    reset() {\n      this._dragRef.reset();\n    }\n    /**\n     * Gets the pixel coordinates of the draggable outside of a drop container.\n     */\n\n\n    getFreeDragPosition() {\n      return this._dragRef.getFreeDragPosition();\n    }\n\n    ngAfterViewInit() {\n      // Normally this isn't in the zone, but it can cause major performance regressions for apps\n      // using `zone-patch-rxjs` because it'll trigger a change detection when it unsubscribes.\n      this._ngZone.runOutsideAngular(() => {\n        // We need to wait for the zone to stabilize, in order for the reference\n        // element to be in the proper place in the DOM. This is mostly relevant\n        // for draggable elements inside portals since they get stamped out in\n        // their original DOM position and then they get transferred to the portal.\n        this._ngZone.onStable.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n          this._updateRootElement();\n\n          this._setupHandlesListener();\n\n          if (this.freeDragPosition) {\n            this._dragRef.setFreeDragPosition(this.freeDragPosition);\n          }\n        });\n      });\n    }\n\n    ngOnChanges(changes) {\n      const rootSelectorChange = changes['rootElementSelector'];\n      const positionChange = changes['freeDragPosition']; // We don't have to react to the first change since it's being\n      // handled in `ngAfterViewInit` where it needs to be deferred.\n\n      if (rootSelectorChange && !rootSelectorChange.firstChange) {\n        this._updateRootElement();\n      } // Skip the first change since it's being handled in `ngAfterViewInit`.\n\n\n      if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n        this._dragRef.setFreeDragPosition(this.freeDragPosition);\n      }\n    }\n\n    ngOnDestroy() {\n      if (this.dropContainer) {\n        this.dropContainer.removeItem(this);\n      }\n\n      const index = CdkDrag._dragInstances.indexOf(this);\n\n      if (index > -1) {\n        CdkDrag._dragInstances.splice(index, 1);\n      } // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n\n\n      this._ngZone.runOutsideAngular(() => {\n        this._destroyed.next();\n\n        this._destroyed.complete();\n\n        this._dragRef.dispose();\n      });\n    }\n    /** Syncs the root element with the `DragRef`. */\n\n\n    _updateRootElement() {\n      var _a;\n\n      const element = this.element.nativeElement;\n      let rootElement = element;\n\n      if (this.rootElementSelector) {\n        rootElement = element.closest !== undefined ? element.closest(this.rootElementSelector) : // Comment tag doesn't have closest method, so use parent's one.\n        (_a = element.parentElement) === null || _a === void 0 ? void 0 : _a.closest(this.rootElementSelector);\n      }\n\n      if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        assertElementNode(rootElement, 'cdkDrag');\n      }\n\n      this._dragRef.withRootElement(rootElement || element);\n    }\n    /** Gets the boundary element, based on the `boundaryElement` value. */\n\n\n    _getBoundaryElement() {\n      const boundary = this.boundaryElement;\n\n      if (!boundary) {\n        return null;\n      }\n\n      if (typeof boundary === 'string') {\n        return this.element.nativeElement.closest(boundary);\n      }\n\n      return coerceElement(boundary);\n    }\n    /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n\n\n    _syncInputs(ref) {\n      ref.beforeStarted.subscribe(() => {\n        if (!ref.isDragging()) {\n          const dir = this._dir;\n          const dragStartDelay = this.dragStartDelay;\n          const placeholder = this._placeholderTemplate ? {\n            template: this._placeholderTemplate.templateRef,\n            context: this._placeholderTemplate.data,\n            viewContainer: this._viewContainerRef\n          } : null;\n          const preview = this._previewTemplate ? {\n            template: this._previewTemplate.templateRef,\n            context: this._previewTemplate.data,\n            matchSize: this._previewTemplate.matchSize,\n            viewContainer: this._viewContainerRef\n          } : null;\n          ref.disabled = this.disabled;\n          ref.lockAxis = this.lockAxis;\n          ref.dragStartDelay = typeof dragStartDelay === 'object' && dragStartDelay ? dragStartDelay : coerceNumberProperty(dragStartDelay);\n          ref.constrainPosition = this.constrainPosition;\n          ref.previewClass = this.previewClass;\n          ref.withBoundaryElement(this._getBoundaryElement()).withPlaceholderTemplate(placeholder).withPreviewTemplate(preview).withPreviewContainer(this.previewContainer || 'global');\n\n          if (dir) {\n            ref.withDirection(dir.value);\n          }\n        }\n      }); // This only needs to be resolved once.\n\n      ref.beforeStarted.pipe(take(1)).subscribe(() => {\n        var _a; // If we managed to resolve a parent through DI, use it.\n\n\n        if (this._parentDrag) {\n          ref.withParent(this._parentDrag._dragRef);\n          return;\n        } // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n        // the item was projected into another item by something like `ngTemplateOutlet`.\n\n\n        let parent = this.element.nativeElement.parentElement;\n\n        while (parent) {\n          if (parent.classList.contains(DRAG_HOST_CLASS)) {\n            ref.withParent(((_a = CdkDrag._dragInstances.find(drag => {\n              return drag.element.nativeElement === parent;\n            })) === null || _a === void 0 ? void 0 : _a._dragRef) || null);\n            break;\n          }\n\n          parent = parent.parentElement;\n        }\n      });\n    }\n    /** Handles the events from the underlying `DragRef`. */\n\n\n    _handleEvents(ref) {\n      ref.started.subscribe(() => {\n        this.started.emit({\n          source: this\n        }); // Since all of these events run outside of change detection,\n        // we need to ensure that everything is marked correctly.\n\n        this._changeDetectorRef.markForCheck();\n      });\n      ref.released.subscribe(() => {\n        this.released.emit({\n          source: this\n        });\n      });\n      ref.ended.subscribe(event => {\n        this.ended.emit({\n          source: this,\n          distance: event.distance,\n          dropPoint: event.dropPoint\n        }); // Since all of these events run outside of change detection,\n        // we need to ensure that everything is marked correctly.\n\n        this._changeDetectorRef.markForCheck();\n      });\n      ref.entered.subscribe(event => {\n        this.entered.emit({\n          container: event.container.data,\n          item: this,\n          currentIndex: event.currentIndex\n        });\n      });\n      ref.exited.subscribe(event => {\n        this.exited.emit({\n          container: event.container.data,\n          item: this\n        });\n      });\n      ref.dropped.subscribe(event => {\n        this.dropped.emit({\n          previousIndex: event.previousIndex,\n          currentIndex: event.currentIndex,\n          previousContainer: event.previousContainer.data,\n          container: event.container.data,\n          isPointerOverContainer: event.isPointerOverContainer,\n          item: this,\n          distance: event.distance,\n          dropPoint: event.dropPoint\n        });\n      });\n    }\n    /** Assigns the default input values based on a provided config object. */\n\n\n    _assignDefaults(config) {\n      const {\n        lockAxis,\n        dragStartDelay,\n        constrainPosition,\n        previewClass,\n        boundaryElement,\n        draggingDisabled,\n        rootElementSelector,\n        previewContainer\n      } = config;\n      this.disabled = draggingDisabled == null ? false : draggingDisabled;\n      this.dragStartDelay = dragStartDelay || 0;\n\n      if (lockAxis) {\n        this.lockAxis = lockAxis;\n      }\n\n      if (constrainPosition) {\n        this.constrainPosition = constrainPosition;\n      }\n\n      if (previewClass) {\n        this.previewClass = previewClass;\n      }\n\n      if (boundaryElement) {\n        this.boundaryElement = boundaryElement;\n      }\n\n      if (rootElementSelector) {\n        this.rootElementSelector = rootElementSelector;\n      }\n\n      if (previewContainer) {\n        this.previewContainer = previewContainer;\n      }\n    }\n    /** Sets up the listener that syncs the handles with the drag ref. */\n\n\n    _setupHandlesListener() {\n      // Listen for any newly-added handles.\n      this._handles.changes.pipe(startWith(this._handles), // Sync the new handles with the DragRef.\n      tap(handles => {\n        const childHandleElements = handles.filter(handle => handle._parentDrag === this).map(handle => handle.element); // Usually handles are only allowed to be a descendant of the drag element, but if\n        // the consumer defined a different drag root, we should allow the drag element\n        // itself to be a handle too.\n\n        if (this._selfHandle && this.rootElementSelector) {\n          childHandleElements.push(this.element);\n        }\n\n        this._dragRef.withHandles(childHandleElements);\n      }), // Listen if the state of any of the handles changes.\n      switchMap(handles => {\n        return merge(...handles.map(item => {\n          return item._stateChanges.pipe(startWith(item));\n        }));\n      }), takeUntil(this._destroyed)).subscribe(handleInstance => {\n        // Enabled/disable the handle that changed in the DragRef.\n        const dragRef = this._dragRef;\n        const handle = handleInstance.element.nativeElement;\n        handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n      });\n    }\n\n  }\n\n  CdkDrag._dragInstances = [];\n\n  CdkDrag.ɵfac = function CdkDrag_Factory(t) {\n    return new (t || CdkDrag)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DROP_LIST, 12), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(CDK_DRAG_HANDLE, 10), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n  };\n\n  CdkDrag.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkDrag,\n    selectors: [[\"\", \"cdkDrag\", \"\"]],\n    contentQueries: function CdkDrag_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, CDK_DRAG_PREVIEW, 5);\n        i0.ɵɵcontentQuery(dirIndex, CDK_DRAG_PLACEHOLDER, 5);\n        i0.ɵɵcontentQuery(dirIndex, CDK_DRAG_HANDLE, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previewTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._placeholderTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._handles = _t);\n      }\n    },\n    hostAttrs: [1, \"cdk-drag\"],\n    hostVars: 4,\n    hostBindings: function CdkDrag_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"cdk-drag-disabled\", ctx.disabled)(\"cdk-drag-dragging\", ctx._dragRef.isDragging());\n      }\n    },\n    inputs: {\n      data: [\"cdkDragData\", \"data\"],\n      lockAxis: [\"cdkDragLockAxis\", \"lockAxis\"],\n      rootElementSelector: [\"cdkDragRootElement\", \"rootElementSelector\"],\n      boundaryElement: [\"cdkDragBoundary\", \"boundaryElement\"],\n      dragStartDelay: [\"cdkDragStartDelay\", \"dragStartDelay\"],\n      freeDragPosition: [\"cdkDragFreeDragPosition\", \"freeDragPosition\"],\n      disabled: [\"cdkDragDisabled\", \"disabled\"],\n      constrainPosition: [\"cdkDragConstrainPosition\", \"constrainPosition\"],\n      previewClass: [\"cdkDragPreviewClass\", \"previewClass\"],\n      previewContainer: [\"cdkDragPreviewContainer\", \"previewContainer\"]\n    },\n    outputs: {\n      started: \"cdkDragStarted\",\n      released: \"cdkDragReleased\",\n      ended: \"cdkDragEnded\",\n      entered: \"cdkDragEntered\",\n      exited: \"cdkDragExited\",\n      dropped: \"cdkDragDropped\",\n      moved: \"cdkDragMoved\"\n    },\n    exportAs: [\"cdkDrag\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_DRAG_PARENT,\n      useExisting: CdkDrag\n    }]), i0.ɵɵNgOnChangesFeature]\n  });\n  return CdkDrag;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet DragDropModule = /*#__PURE__*/(() => {\n  class DragDropModule {}\n\n  DragDropModule.ɵfac = function DragDropModule_Factory(t) {\n    return new (t || DragDropModule)();\n  };\n\n  DragDropModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: DragDropModule\n  });\n  DragDropModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [DragDrop],\n    imports: [CdkScrollableModule]\n  });\n  return DragDropModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CDK_DRAG_CONFIG, CDK_DRAG_HANDLE, CDK_DRAG_PARENT, CDK_DRAG_PLACEHOLDER, CDK_DRAG_PREVIEW, CDK_DROP_LIST, CDK_DROP_LIST_GROUP, CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDragPreview, CdkDropList, CdkDropListGroup, DragDrop, DragDropModule, DragDropRegistry, DragRef, DropListRef, copyArrayItem, moveItemInArray, transferArrayItem }; //# sourceMappingURL=drag-drop.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}