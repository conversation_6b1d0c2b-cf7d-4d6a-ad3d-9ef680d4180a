<block-ui>
  <!-- Page content-->
  <div class="container-fluid position-relative zindex-5 pb-0 mb-md-1 p-3" style="margin-top: -15px">
    <div class="row">
      <!-- Sidebar-->
      <!--<div class="col-lg-2" style="background-image: linear-gradient(140deg, #F5C253 0%, #F5C253 50%, #F5BD00 75%)" *ngIf="!quizRunning">
        <app-web-side-menu></app-web-side-menu>
      </div>-->
      <!-- Content-->
      <div class="col-lg-10 p-5" *ngIf="pagedetail" [ngClass]="{'col-lg-12': quizRunning}">
        <div class="row h-100 bg-tranparent-black-glass rounded-1 shadow-lg">
          <div class="col-12">
            <div class="pt-2 p-md-3">
              <div class="col-12 d-flex justify-content-between">
                <p class="h3 text-break mb-0">
                  {{ pagedetail.CourseTitle }} <br />
                  <span class="fw-bold text-uppercase fs-6">Certification Test</span>
                </p>
                <a class="btn btn-link text-decoration-none fs-4 fw-bold btn-sm d-flex align-items-center border-start"
                  (click)="examStarted ? backClicked(false) : backClicked(true)"><i
                    class="fs-4 ai-arrow-left fs-base me-2"></i>Go Back</a>
              </div>
              <hr />
              <!--start: quiz trace controls-->
              <div class="col-12 mb-3" *ngIf="quizRunning">
                <div class="d-flex justify-content-center">
                  <pagination-controls id="p" previousLabel="Prev" nextLabel="Next" (pageChange)="setPage($event)">
                  </pagination-controls>
                  <ul class="d-none">
                    <li *ngFor="
                        let q of questionList | paginate : {
                                id:'p',
                                itemsPerPage: page.size,
                                currentPage: page.pageNumber,
                                totalItems: questionList.length
                              };
                        let i = index
                      ">
                      {{ q.Id }}
                    </li>
                  </ul>
                </div>
              </div>
              <!--end: quiz trace controls-->
              <div class="col-12 mb-3">
                <div class="section" *ngIf="pagedetail">
                  <div class="col-12" *ngIf="!quizRunning">
                    <!-- <div class="sidebar"> -->
                    <div class="widget widget_recent_post">
                      <div class="row">
                        <div class="col-lg-6 col-12">
                          <ul class="list_none blog_meta">
                            <li>
                              <i class="fa fa-arrow-right me-2"></i>
                              Total Marks : {{ pagedetail.Marks }}
                            </li>
                            <li>
                              <i class="fa fa-arrow-right me-2"></i>
                              Question Type :
                              {{ pagedetail.MCQOnly ? "MCQ" : "Mixed" }}
                            </li>
                          </ul>
                        </div>

                        <div class="col-lg-6 col-12">
                          <ul class="list_none blog_meta">
                            <li>
                              <i class="fa fa-arrow-right me-2"></i>
                              Total Duration :
                              {{ getHourMint(pagedetail.DurationMnt) }}
                            </li>
                            <li>
                              <i class="fa fa-arrow-right me-2"></i>
                              Pending Quota :
                              {{ pagedetail.PendingQuota }}
                            </li>
                          </ul>
                        </div>

                        <div class="col-12" *ngIf="pagedetail.StartDate && pagedetail.EndDate">
                          <ul class="list_none blog_meta mb-4 mt-2">
                            <li>
                              <i class="fa fa-arrow-right me-2"></i>
                              Open in :
                              <b class="text-primary">
                                {{
                                pagedetail.StartDate
                                | amFromUtc
                                | amLocal
                                | amDateFormat
                                : "MMM DD, YYYY hh:mm
                                A"
                                }}
                              </b>
                              to
                              <b class="text-primary">
                                {{
                                pagedetail.EndDate
                                | amFromUtc
                                | amLocal
                                | amDateFormat
                                : "MMM DD, YYYY hh:mm
                                A"
                                }}
                              </b>
                            </li>
                          </ul>
                        </div>

                        <div class="col-12 mb-3" *ngIf="pagedetail.ExamInstructions">
                          <accordion [isAnimated]="true">
                            <accordion-group heading="EXAM INSTRUCTIONS (click here to see)"
                              panelClass="custom-accordion">
                              <div [innerHTML]="pagedetail.ExamInstructions"></div>
                            </accordion-group>
                          </accordion>
                        </div>
                      </div>
                    </div>
                    <!-- </div> -->
                  </div>

                  <div class="col-12 text-center" *ngIf="!quizRunning">
                    <div class="bg-success rounded-3" *ngIf="pagedetail.CertificateAchieved">
                      <h3 class="p-3 text-white">
                        <i class="fa fa-award"> </i> You have already obtained a certificate for this test
                        <button type="button" class="btn btn-link btn-sm text-decoration-none text-white"
                          (click)="downloadCertificate(pagedetail.CourseId)">
                          <i class="fa-solid fa-download"></i> Download Certificate
                        </button>
                      </h3>
                    </div>
                    <div class="bg-danger rounded-3" *ngIf="!pagedetail.Allow">
                      <h3 class="p-3 text-white">
                        <i class="fa fa-exclamation-triangle"> </i> {{notAllowMessage}}
                      </h3>
                    </div>

                    <button *ngIf="pagedetail.Allow" type="button" class="btn btn-primary"
                      (click)="startQuiz(template)">
                      <i class="fa-solid fa-check"></i> {{pagedetail.CertificateAchieved?'Improvement':'Start Exam'}}
                    </button>
                  </div>

                  <div class="col-12" *ngIf="quizRunning">
                    <div class="card q-panel noselect">
                      <div class="card-header">
                        <div class="row">
                          <div class="col-lg-8 col-md-6 col-12 qs-counter">
                            <i class="fa-solid fa-circle-question"></i>
                            <strong>
                              QUESTION {{ qIndex + 1 }} of
                              {{ questionList.length }}</strong>
                          </div>
                          <div class="col-lg-4 col-md-6 col-12 text-end timer">
                            <i class="fa-solid fa-clock"></i>
                            <strong>
                              Time Left:
                              <b>{{ timer.hour }}h : {{ timer.minute }}m :
                                {{ timer.second }}s</b></strong>
                          </div>
                        </div>
                      </div>
                      <div class="question-body noselect" *ngIf="timer.isRunning">
                        <!------------ MCQ Question ------------>
                        <div class="card-body" [@inOutAnimation] *ngIf="
                            questionList[qIndex].Type === 'MCQ' &&
                            timer.isRunning
                          ">
                          <div class="row">
                            <div class="col-sm-11 col-10">
                              <p class="text-question mb-0">
                                {{ questionList[qIndex].Question }}
                              </p>
                            </div>
                            <div class="col-sm-1 col-2">
                              <p class="text-question mb-0">
                                [{{ questionList[qIndex].Mark }}]
                              </p>
                            </div>
                          </div>
                        </div>
                        <ul class="list-group" [@inOutAnimation] *ngIf="
                            questionList[qIndex].Type === 'MCQ' &&
                            timer.isRunning
                          ">
                          <li class="list-group-item"
                            *ngFor="let option of questionList[qIndex].Options; let oi = index;">
                            <input class="form-check-input" (click)="selectSpecific(questionList[qIndex].Options)"
                              type="radio" [name]="'radioGroup' + qIndex" [(ngModel)]="option.Selected"
                              [id]="'option' + oi + '-' + qIndex" [value]="option.Text" />

                            <label class="form-check-label" [for]="'option' + oi + '-' + qIndex">
                              {{ option.Text }}
                            </label>
                          </li>
                        </ul>
                        <!------------ End MCQ Question ------------>

                        <!------------ True/False Question ------------>
                        <div class="card-body" [@inOutAnimation] *ngIf="
                            questionList[qIndex].Type === 'TFQ' &&
                            timer.isRunning
                          ">
                          <div class="row">
                            <div class="col-sm-11 col-10">
                              <p class="text-question mb-0">
                                {{ questionList[qIndex].Question }}
                              </p>
                            </div>
                            <div class="col-sm-1 col-2">
                              <p class="text-question mb-0">
                                [{{ questionList[qIndex].Mark }}]
                              </p>
                            </div>
                            <div class="col-sm-12 mt-2">
                              <div class="row">
                                <div class="col-12">
                                  <ui-switch labelOff="False" labelOn="True" defaultBgColor="#E82D4C"
                                    [(ngModel)]="questionList[qIndex].Answer">
                                  </ui-switch>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!------------ End True/False Question ------------>

                        <!------------ Fill in the gap Question ------------>
                        <div class="card-body" [@inOutAnimation] *ngIf="
                            questionList[qIndex].Type === 'FIGQ' &&
                            timer.isRunning
                          ">
                          <div class="row">
                            <div class="col-sm-11 col-10">
                              <p class="text-question mb-0">
                                {{ questionList[qIndex].Question }}
                              </p>
                            </div>
                            <div class="col-sm-1 col-2">
                              <p class="text-question mb-0">
                                [{{ questionList[qIndex].Mark }}]
                              </p>
                            </div>
                            <div class="col-sm-12 mt-2">
                              <div class="input-group mb-3">
                                <span class="input-group-text">Answer</span>
                                <input type="text" class="form-control" placeholder="Write answer .."
                                  [(ngModel)]="questionList[qIndex].Answer" />
                              </div>
                            </div>
                          </div>
                        </div>
                        <!------------ End Fill in the gap Question ------------>

                        <!------------ Matching Question ------------>
                        <div class="card-body" [@inOutAnimation] *ngIf="
                            questionList[qIndex].Type === 'LRMQ' &&
                            timer.isRunning
                          ">
                          <div class="row">
                            <div class="col-12">
                              <div class="card">
                                <div class="card-header">
                                  <h4 class="text-bold">Matching Questions</h4>
                                </div>
                                <div class="card-body pt-3">
                                  <div class="row">
                                    <div class="col-sm-7 col-12 pb-2">
                                      <div class="row" *ngFor="
                                          let item of questionList[qIndex]
                                            .LeftSides;
                                          let i = index
                                        ">
                                        <div class="col-sm-11 col-xs-11 example-box">
                                          <b>{{ i + 1 }}. {{ item.LeftSide }}</b>
                                        </div>
                                        <div class="col-sm-1 col-xs-1 example-box">
                                          <b>[{{ item.Mark }}]</b>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="col-sm-5 col-12">
                                      <div cdkDropList class="example-list" (cdkDropListDropped)="drop($event)">
                                        <div class="example-box" *ngFor="
                                            let item of questionList[qIndex]
                                              .RightSides;
                                            let i = index
                                          " cdkDrag>
                                          <div class="example-custom-placeholder" *cdkDragPlaceholder></div>
                                          <b>{{ i + 1 }}. {{ item }}</b>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!------------ End Matching Question ------------>

                        <!------------ Written Question ------------>
                        <div class="card-body" [@inOutAnimation] *ngIf="
                            questionList[qIndex].Type === 'WQ' &&
                            timer.isRunning
                          ">
                          <div class="row">
                            <div class="col-sm-11 col-10">
                              <p class="text-question mb-0">
                                {{ questionList[qIndex].Question }}
                              </p>
                            </div>
                            <div class="col-sm-1 col-2">
                              <p class="text-question mb-0">
                                [{{ questionList[qIndex].Mark }}]
                              </p>
                            </div>
                            <div class="col-sm-12 mt-2">
                              <div class="input-group mb-3">
                                <span class="input-group-text">Answer</span>
                                <textarea class="form-control" rows="5"
                                  [(ngModel)]="questionList[qIndex].Answer"></textarea>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!------------ End Written Question ------------>
                      </div>
                      <div class="card-footer">
                        <button type="button" class="btn btn-info btn-box me-1" (click)="prevQuestion()"
                          [disabled]="qIndex === 0">
                          <i class="fa fa-arrow-left"></i> Prev
                        </button>
                        <button type="button" class="btn btn-success" (click)="nextQuestion(template)">
                          {{ btnNextText }}
                          <i class="fa fa-arrow-right"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #template>
    <div style="background-image: linear-gradient(#E9EFF5, #BBE1F7, #6EABF7); border-radius: 15px;">
      <button type="button" class="btn btn-sm close pull-right" aria-label="Close" (click)="modalHide()">
        <span aria-hidden="true">&times;</span>
      </button>
      <div class="modal-header d-flex justify-content-center">
        <h4 class="modal-title {{ result.Result === 'Passed'?'text-primary':'text-danger'}}" style="font-style:italic">
          {{result.Result === 'Passed'?'Congratulations!':'Sorry!'}}</h4>
      </div>
      <div class="modal-body">
        <div class="text-center" *ngIf="result">
          <img src="./assets/img/CertificateCongratulations.gif" alt="" *ngIf="result.Result === 'Passed'">
          <h5 style="font-style: italic;" class="{{result.Result === 'Passed'?'text-overImage-centered':''}}">

            <p> Your achieved mark is <span> {{result.CorrectAnswer}}/{{result.TotalQuestion}}</span></p>
            <p> Your score is <span class="{{result.Result === 'Passed'?'text-primary':'text-danger'}}">
                {{result.Score}} %</span></p>
            <p>{{result.Result === 'Passed'?'You have complted the test successfully':'You could not achieve the pass
              mark'}}</p>
          </h5>
        </div>
      </div>
      <div class="modal-footer d-flex justify-content-center py-1">
        {{result.Result === 'Passed'?'Please download your certificate from Certificate Section':''}}
        <button *ngIf="result.Result !== 'Passed'" type="button" class="btn btn-primary btn-sm"
          (click)="tryAgain()">Please try again</button>
        <button *ngIf="result.Result === 'Passed'" type="button" class="btn btn-primary btn-sm"
          (click)="downloadCertificate(result.CourseExamData.CourseId)">
          <i class="fa-solid fa-download"></i> Download
        </button>
      </div>
    </div>
  </ng-template>
</block-ui>