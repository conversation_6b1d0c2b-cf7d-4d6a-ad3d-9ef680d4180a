{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Tagalog (Philippines) [tl-ph]\n//! author : <PERSON> : https://github.com/hagmandan\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var tlPh = moment.defineLocale('tl-ph', {\n    months: 'Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre'.split('_'),\n    monthsShort: 'Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis'.split('_'),\n    weekdays: 'Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyer<PERSON>_Sabado'.split('_'),\n    weekdaysShort: 'Lin_Lun_Mar_Miy_Huw_Biy_Sab'.split('_'),\n    weekdaysMin: 'Li_Lu_Ma_Mi_Hu_Bi_Sab'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'MM/D/YYYY',\n      LL: 'MMMM D, YYYY',\n      LLL: 'MMMM D, YYYY HH:mm',\n      LLLL: 'dddd, MMMM DD, YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: 'LT [ngayong araw]',\n      nextDay: '[Bukas ng] LT',\n      nextWeek: 'LT [sa susunod na] dddd',\n      lastDay: 'LT [kahapon]',\n      lastWeek: 'LT [noong nakaraang] dddd',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'sa loob ng %s',\n      past: '%s ang nakalipas',\n      s: 'ilang segundo',\n      ss: '%d segundo',\n      m: 'isang minuto',\n      mm: '%d minuto',\n      h: 'isang oras',\n      hh: '%d oras',\n      d: 'isang araw',\n      dd: '%d araw',\n      M: 'isang buwan',\n      MM: '%d buwan',\n      y: 'isang taon',\n      yy: '%d taon'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}/,\n    ordinal: function (number) {\n      return number;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return tlPh;\n});", "map": null, "metadata": {}, "sourceType": "script"}