{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Input, EventEmitter, Optional, Inject, SkipSelf, Output, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Subject, Subscription } from 'rxjs';\nimport * as i1 from '@angular/cdk/collections';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Used to generate unique ID for each accordion. */\n\nlet nextId$1 = 0;\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst CDK_ACCORDION = /*#__PURE__*/new InjectionToken('CdkAccordion');\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\n\nlet CdkAccordion = /*#__PURE__*/(() => {\n  class CdkAccordion {\n    constructor() {\n      /** Emits when the state of the accordion changes */\n      this._stateChanges = new Subject();\n      /** Stream that emits true/false when openAll/closeAll is triggered. */\n\n      this._openCloseAllActions = new Subject();\n      /** A readonly id value to use for unique selection coordination. */\n\n      this.id = `cdk-accordion-${nextId$1++}`;\n      this._multi = false;\n    }\n    /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n\n\n    get multi() {\n      return this._multi;\n    }\n\n    set multi(multi) {\n      this._multi = coerceBooleanProperty(multi);\n    }\n    /** Opens all enabled accordion items in an accordion where multi is enabled. */\n\n\n    openAll() {\n      if (this._multi) {\n        this._openCloseAllActions.next(true);\n      }\n    }\n    /** Closes all enabled accordion items in an accordion where multi is enabled. */\n\n\n    closeAll() {\n      this._openCloseAllActions.next(false);\n    }\n\n    ngOnChanges(changes) {\n      this._stateChanges.next(changes);\n    }\n\n    ngOnDestroy() {\n      this._stateChanges.complete();\n\n      this._openCloseAllActions.complete();\n    }\n\n  }\n\n  CdkAccordion.ɵfac = function CdkAccordion_Factory(t) {\n    return new (t || CdkAccordion)();\n  };\n\n  CdkAccordion.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAccordion,\n    selectors: [[\"cdk-accordion\"], [\"\", \"cdkAccordion\", \"\"]],\n    inputs: {\n      multi: \"multi\"\n    },\n    exportAs: [\"cdkAccordion\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_ACCORDION,\n      useExisting: CdkAccordion\n    }]), i0.ɵɵNgOnChangesFeature]\n  });\n  return CdkAccordion;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Used to generate unique ID for each accordion item. */\n\n\nlet nextId = 0;\n/**\n * An basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\n\nlet CdkAccordionItem = /*#__PURE__*/(() => {\n  class CdkAccordionItem {\n    constructor(accordion, _changeDetectorRef, _expansionDispatcher) {\n      this.accordion = accordion;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._expansionDispatcher = _expansionDispatcher;\n      /** Subscription to openAll/closeAll events. */\n\n      this._openCloseAllSubscription = Subscription.EMPTY;\n      /** Event emitted every time the AccordionItem is closed. */\n\n      this.closed = new EventEmitter();\n      /** Event emitted every time the AccordionItem is opened. */\n\n      this.opened = new EventEmitter();\n      /** Event emitted when the AccordionItem is destroyed. */\n\n      this.destroyed = new EventEmitter();\n      /**\n       * Emits whenever the expanded state of the accordion changes.\n       * Primarily used to facilitate two-way binding.\n       * @docs-private\n       */\n\n      this.expandedChange = new EventEmitter();\n      /** The unique AccordionItem id. */\n\n      this.id = `cdk-accordion-child-${nextId++}`;\n      this._expanded = false;\n      this._disabled = false;\n      /** Unregister function for _expansionDispatcher. */\n\n      this._removeUniqueSelectionListener = () => {};\n\n      this._removeUniqueSelectionListener = _expansionDispatcher.listen((id, accordionId) => {\n        if (this.accordion && !this.accordion.multi && this.accordion.id === accordionId && this.id !== id) {\n          this.expanded = false;\n        }\n      }); // When an accordion item is hosted in an accordion, subscribe to open/close events.\n\n      if (this.accordion) {\n        this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n      }\n    }\n    /** Whether the AccordionItem is expanded. */\n\n\n    get expanded() {\n      return this._expanded;\n    }\n\n    set expanded(expanded) {\n      expanded = coerceBooleanProperty(expanded); // Only emit events and update the internal value if the value changes.\n\n      if (this._expanded !== expanded) {\n        this._expanded = expanded;\n        this.expandedChange.emit(expanded);\n\n        if (expanded) {\n          this.opened.emit();\n          /**\n           * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n           * the name value is the id of the accordion.\n           */\n\n          const accordionId = this.accordion ? this.accordion.id : this.id;\n\n          this._expansionDispatcher.notify(this.id, accordionId);\n        } else {\n          this.closed.emit();\n        } // Ensures that the animation will run when the value is set outside of an `@Input`.\n        // This includes cases like the open, close and toggle methods.\n\n\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /** Whether the AccordionItem is disabled. */\n\n\n    get disabled() {\n      return this._disabled;\n    }\n\n    set disabled(disabled) {\n      this._disabled = coerceBooleanProperty(disabled);\n    }\n    /** Emits an event for the accordion item being destroyed. */\n\n\n    ngOnDestroy() {\n      this.opened.complete();\n      this.closed.complete();\n      this.destroyed.emit();\n      this.destroyed.complete();\n\n      this._removeUniqueSelectionListener();\n\n      this._openCloseAllSubscription.unsubscribe();\n    }\n    /** Toggles the expanded state of the accordion item. */\n\n\n    toggle() {\n      if (!this.disabled) {\n        this.expanded = !this.expanded;\n      }\n    }\n    /** Sets the expanded state of the accordion item to false. */\n\n\n    close() {\n      if (!this.disabled) {\n        this.expanded = false;\n      }\n    }\n    /** Sets the expanded state of the accordion item to true. */\n\n\n    open() {\n      if (!this.disabled) {\n        this.expanded = true;\n      }\n    }\n\n    _subscribeToOpenCloseAllActions() {\n      return this.accordion._openCloseAllActions.subscribe(expanded => {\n        // Only change expanded state if item is enabled\n        if (!this.disabled) {\n          this.expanded = expanded;\n        }\n      });\n    }\n\n  }\n\n  CdkAccordionItem.ɵfac = function CdkAccordionItem_Factory(t) {\n    return new (t || CdkAccordionItem)(i0.ɵɵdirectiveInject(CDK_ACCORDION, 12), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.UniqueSelectionDispatcher));\n  };\n\n  CdkAccordionItem.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAccordionItem,\n    selectors: [[\"cdk-accordion-item\"], [\"\", \"cdkAccordionItem\", \"\"]],\n    inputs: {\n      expanded: \"expanded\",\n      disabled: \"disabled\"\n    },\n    outputs: {\n      closed: \"closed\",\n      opened: \"opened\",\n      destroyed: \"destroyed\",\n      expandedChange: \"expandedChange\"\n    },\n    exportAs: [\"cdkAccordionItem\"],\n    features: [i0.ɵɵProvidersFeature([// Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n    // registering to the same accordion.\n    {\n      provide: CDK_ACCORDION,\n      useValue: undefined\n    }])]\n  });\n  return CdkAccordionItem;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet CdkAccordionModule = /*#__PURE__*/(() => {\n  class CdkAccordionModule {}\n\n  CdkAccordionModule.ɵfac = function CdkAccordionModule_Factory(t) {\n    return new (t || CdkAccordionModule)();\n  };\n\n  CdkAccordionModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CdkAccordionModule\n  });\n  CdkAccordionModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return CdkAccordionModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CdkAccordion, CdkAccordionItem, CdkAccordionModule }; //# sourceMappingURL=accordion.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}