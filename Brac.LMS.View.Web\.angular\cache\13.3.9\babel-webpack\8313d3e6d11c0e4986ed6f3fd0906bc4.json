{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { CourseCertificateTestRoutes } from './course-certificate-test.routing';\nimport { AccordionModule } from 'ngx-bootstrap/accordion';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { UiSwitchModule } from 'ngx-toggle-switch';\nimport { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-bootstrap/accordion\";\nimport * as i3 from \"ngx-smart-modal\";\nexport let CourseCertificateTestModule = /*#__PURE__*/(() => {\n  class CourseCertificateTestModule {}\n\n  CourseCertificateTestModule.ɵfac = function CourseCertificateTestModule_Factory(t) {\n    return new (t || CourseCertificateTestModule)();\n  };\n\n  CourseCertificateTestModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CourseCertificateTestModule\n  });\n  CourseCertificateTestModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [NgxSmartModalService],\n    imports: [[CommonModule, RouterModule.forChild(CourseCertificateTestRoutes), SharedModule, WebLayoutModule, AccordionModule.forRoot(), DragDropModule, UiSwitchModule, NgxSmartModalModule.forRoot()]]\n  });\n  return CourseCertificateTestModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}