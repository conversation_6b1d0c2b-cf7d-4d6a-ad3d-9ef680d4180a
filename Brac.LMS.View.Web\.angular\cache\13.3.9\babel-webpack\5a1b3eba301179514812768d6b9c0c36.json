{"ast": null, "code": "import { getBsVer } from 'ngx-bootstrap/utils';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_ID, Injectable, Inject, ElementRef } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { Subject, merge, fromEvent, of, animationFrameScheduler } from 'rxjs';\nvar MapPlacementInToRL = /*#__PURE__*/(() => {\n  (function (MapPlacementInToRL) {\n    MapPlacementInToRL[\"top\"] = \"top\";\n    MapPlacementInToRL[\"bottom\"] = \"bottom\";\n    MapPlacementInToRL[\"left\"] = \"left\";\n    MapPlacementInToRL[\"right\"] = \"right\";\n    MapPlacementInToRL[\"auto\"] = \"auto\";\n    MapPlacementInToRL[\"end\"] = \"right\";\n    MapPlacementInToRL[\"start\"] = \"left\";\n    MapPlacementInToRL[\"top left\"] = \"top left\";\n    MapPlacementInToRL[\"top right\"] = \"top right\";\n    MapPlacementInToRL[\"right top\"] = \"right top\";\n    MapPlacementInToRL[\"right bottom\"] = \"right bottom\";\n    MapPlacementInToRL[\"bottom right\"] = \"bottom right\";\n    MapPlacementInToRL[\"bottom left\"] = \"bottom left\";\n    MapPlacementInToRL[\"left bottom\"] = \"left bottom\";\n    MapPlacementInToRL[\"left top\"] = \"left top\";\n    MapPlacementInToRL[\"top start\"] = \"top left\";\n    MapPlacementInToRL[\"top end\"] = \"top right\";\n    MapPlacementInToRL[\"end top\"] = \"right top\";\n    MapPlacementInToRL[\"end bottom\"] = \"right bottom\";\n    MapPlacementInToRL[\"bottom end\"] = \"bottom right\";\n    MapPlacementInToRL[\"bottom start\"] = \"bottom left\";\n    MapPlacementInToRL[\"start bottom\"] = \"start bottom\";\n    MapPlacementInToRL[\"start top\"] = \"left top\";\n  })(MapPlacementInToRL || (MapPlacementInToRL = {}));\n\n  return MapPlacementInToRL;\n})();\nvar PlacementForBs5 = /*#__PURE__*/(() => {\n  (function (PlacementForBs5) {\n    PlacementForBs5[\"top\"] = \"top\";\n    PlacementForBs5[\"bottom\"] = \"bottom\";\n    PlacementForBs5[\"left\"] = \"start\";\n    PlacementForBs5[\"right\"] = \"end\";\n    PlacementForBs5[\"auto\"] = \"auto\";\n    PlacementForBs5[\"end\"] = \"end\";\n    PlacementForBs5[\"start\"] = \"start\";\n    PlacementForBs5[\"top left\"] = \"top start\";\n    PlacementForBs5[\"top right\"] = \"top end\";\n    PlacementForBs5[\"right top\"] = \"end top\";\n    PlacementForBs5[\"right bottom\"] = \"end bottom\";\n    PlacementForBs5[\"bottom right\"] = \"bottom end\";\n    PlacementForBs5[\"bottom left\"] = \"bottom start\";\n    PlacementForBs5[\"left bottom\"] = \"start bottom\";\n    PlacementForBs5[\"left top\"] = \"start top\";\n    PlacementForBs5[\"top start\"] = \"top start\";\n    PlacementForBs5[\"top end\"] = \"top end\";\n    PlacementForBs5[\"end top\"] = \"end top\";\n    PlacementForBs5[\"end bottom\"] = \"end bottom\";\n    PlacementForBs5[\"bottom end\"] = \"bottom end\";\n    PlacementForBs5[\"bottom start\"] = \"bottom start\";\n    PlacementForBs5[\"start bottom\"] = \"start bottom\";\n    PlacementForBs5[\"start top\"] = \"start top\";\n  })(PlacementForBs5 || (PlacementForBs5 = {}));\n\n  return PlacementForBs5;\n})();\n\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  } // NOTE: 1 DOM access here\n\n\n  const window = element.ownerDocument.defaultView;\n  const css = window === null || window === void 0 ? void 0 : window.getComputedStyle(element, null); // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n\n  return property ? css && css[property] : css;\n}\n/**\n * Returns the offset parent of the given element\n */\n\n\nfunction getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  const noOffsetParent = null; // NOTE: 1 DOM access here\n\n  let offsetParent = element === null || element === void 0 ? void 0 : element.offsetParent; // Skip hidden elements which don't have an offsetParent\n\n  let sibling = void 0;\n\n  while (offsetParent === noOffsetParent && element.nextElementSibling && sibling !== element.nextElementSibling) {\n    // todo: valorkin fix\n    sibling = element.nextElementSibling;\n    offsetParent = sibling.offsetParent;\n  }\n\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return sibling ? sibling.ownerDocument.documentElement : document.documentElement;\n  } // .offsetParent will return the closest TH, TD or TABLE in case\n\n\n  if (offsetParent && ['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n} // todo: valorkin fix\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\nfunction isOffsetContainer(element) {\n  const {\n    nodeName\n  } = element;\n\n  if (nodeName === 'BODY') {\n    return false;\n  }\n\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n */\n\n\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n/**\n * Finds the offset parent common to the two provided nodes\n */\n\n\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  } // Here we make sure to give as \"start\" the element that comes first in the DOM\n\n\n  const order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1; // Get common ancestor container\n\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0); // todo: valorkin fix\n\n  const commonAncestorContainer = range.commonAncestorContainer; // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  } // one of the nodes is inside shadowDOM, find which one\n\n\n  const element1root = getRoot(element1);\n\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n/**\n * Finds the first parent of an element that has a transformed property defined\n */\n\n\nfunction getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element || !element.parentElement) {\n    return document.documentElement;\n  }\n\n  let el = element.parentElement;\n\n  while ((el === null || el === void 0 ? void 0 : el.parentElement) && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n\n  return el || document.documentElement;\n}\n/**\n * Helper to detect borders of a given element\n */\n\n\nfunction getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n  return parseFloat(styles[`border${sideA}Width`]) + parseFloat(styles[`border${sideB}Width`]);\n}\n\nfunction getSize(axis, body, html, computedStyle) {\n  const _body = body;\n  const _html = html;\n  const _computedStyle = computedStyle;\n  return Math.max(_body[`offset${axis}`], _body[`scroll${axis}`], _html[`client${axis}`], _html[`offset${axis}`], _html[`scroll${axis}`], 0);\n}\n\nfunction getWindowSizes(document) {\n  const body = document.body;\n  const html = document.documentElement;\n  const computedStyle = void 0;\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\n\nfunction getClientRect(offsets) {\n  return Object.assign(Object.assign({}, offsets), {\n    right: (offsets.left || 0) + offsets.width,\n    bottom: (offsets.top || 0) + offsets.height\n  });\n}\n/**\n * Tells if a given input is a number\n */\n\n\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(Number(n));\n} // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\nfunction isNumber(value) {\n  return typeof value === 'number' || Object.prototype.toString.call(value) === '[object Number]';\n}\n/**\n * Get bounding client rect of given element\n */\n\n\nfunction getBoundingClientRect(element) {\n  const rect = element.getBoundingClientRect(); // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  // try {\n  //   if (isIE(10)) {\n  //     const scrollTop = getScroll(element, 'top');\n  //     const scrollLeft = getScroll(element, 'left');\n  //     if (rect && isNumber(rect.top) && isNumber(rect.left) && isNumber(rect.bottom) && isNumber(rect.right)) {\n  //       rect.top += scrollTop;\n  //       rect.left += scrollLeft;\n  //       rect.bottom += scrollTop;\n  //       rect.right += scrollLeft;\n  //     }\n  //   }\n  // } catch (e) {\n  //   return rect;\n  // }\n\n  if (!(rect && isNumber(rect.top) && isNumber(rect.left) && isNumber(rect.bottom) && isNumber(rect.right))) {\n    return rect;\n  }\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  }; // subtract scrollbar size from sizes\n\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : undefined;\n  const width = (sizes === null || sizes === void 0 ? void 0 : sizes.width) || element.clientWidth || isNumber(rect.right) && isNumber(result.left) && rect.right - result.left || 0;\n  const height = (sizes === null || sizes === void 0 ? void 0 : sizes.height) || element.clientHeight || isNumber(rect.bottom) && isNumber(result.top) && rect.bottom - result.top || 0;\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height; // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n/**\n * Returns the parentNode or the host of the element\n */\n// todo: valorkin fix\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n\n  return element.parentNode || element.host;\n}\n/**\n * Returns the scrolling parent of the given element\n */\n// todo: valorkin fix\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n\n    case '#document':\n      return element.body;\n\n    default:\n  } // Firefox want us to check `-x` and `-y` variations as well\n\n\n  const {\n    overflow,\n    overflowX,\n    overflowY\n  } = getStyleComputedProperty(element);\n\n  if (/(auto|scroll|overlay)/.test(String(overflow) + String(overflowY) + String(overflowX))) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n\nfunction getOffsetRectRelativeToArbitraryNode(children, parent, fixedPosition = false) {\n  var _a, _b, _c, _d, _e, _f;\n\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = parseFloat(styles.borderTopWidth);\n  const borderLeftWidth = parseFloat(styles.borderLeftWidth); // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n\n  if (fixedPosition && isHTML) {\n    parentRect.top = Math.max((_a = parentRect.top) !== null && _a !== void 0 ? _a : 0, 0);\n    parentRect.left = Math.max((_b = parentRect.left) !== null && _b !== void 0 ? _b : 0, 0);\n  }\n\n  const offsets = getClientRect({\n    top: ((_c = childrenRect.top) !== null && _c !== void 0 ? _c : 0) - ((_d = parentRect.top) !== null && _d !== void 0 ? _d : 0) - borderTopWidth,\n    left: ((_e = childrenRect.left) !== null && _e !== void 0 ? _e : 0) - ((_f = parentRect.left) !== null && _f !== void 0 ? _f : 0) - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0; // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n\n  if (isHTML) {\n    const marginTop = parseFloat(styles.marginTop);\n    const marginLeft = parseFloat(styles.marginLeft);\n\n    if (isNumber(offsets.top)) {\n      offsets.top -= borderTopWidth - marginTop;\n    }\n\n    if (isNumber(offsets.bottom)) {\n      offsets.bottom -= borderTopWidth - marginTop;\n    }\n\n    if (isNumber(offsets.left)) {\n      offsets.left -= borderLeftWidth - marginLeft;\n    }\n\n    if (isNumber(offsets.right)) {\n      offsets.right -= borderLeftWidth - marginLeft;\n    } // Attach marginTop and marginLeft because in some circumstances we may need them\n\n\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  return offsets;\n}\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n */\n\n\nfunction getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element, excludeScroll = false) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n  const scrollTop = !excludeScroll ? getScroll(html) : 0;\n  const scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n  const offset = {\n    top: scrollTop - Number(relativeOffset === null || relativeOffset === void 0 ? void 0 : relativeOffset.top) + Number(relativeOffset === null || relativeOffset === void 0 ? void 0 : relativeOffset.marginTop),\n    left: scrollLeft - Number(relativeOffset === null || relativeOffset === void 0 ? void 0 : relativeOffset.left) + Number(relativeOffset === null || relativeOffset === void 0 ? void 0 : relativeOffset.marginLeft),\n    width,\n    height\n  };\n  return getClientRect(offset);\n}\n/**\n * Check if the given element is fixed or is inside a fixed parent\n */\n\n\nfunction isFixed(element) {\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n\n  return isFixed(getParentNode(element));\n}\n\nfunction getBoundaries(target, host, padding = 0, boundariesElement, fixedPosition = false) {\n  // NOTE: 1 DOM access here\n  let boundaries = {\n    top: 0,\n    left: 0\n  };\n  const offsetParent = fixedPosition ? getFixedPositionOffsetParent(target) : findCommonOffsetParent(target, host); // Handle viewport case\n\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(host));\n\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = target.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = target.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition); // In case of HTML, we need a different computation\n\n    if (offsets && boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const {\n        height,\n        width\n      } = getWindowSizes(target.ownerDocument);\n\n      if (isNumber(boundaries.top) && isNumber(offsets.top) && isNumber(offsets.marginTop)) {\n        boundaries.top += offsets.top - offsets.marginTop;\n      }\n\n      if (isNumber(boundaries.top)) {\n        boundaries.bottom = Number(height) + Number(offsets.top);\n      }\n\n      if (isNumber(boundaries.left) && isNumber(offsets.left) && isNumber(offsets.marginLeft)) {\n        boundaries.left += offsets.left - offsets.marginLeft;\n      }\n\n      if (isNumber(boundaries.top)) {\n        boundaries.right = Number(width) + Number(offsets.left);\n      }\n    } else if (offsets) {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  } // Add paddings\n\n\n  if (isNumber(boundaries.left)) {\n    boundaries.left += padding;\n  }\n\n  if (isNumber(boundaries.top)) {\n    boundaries.top += padding;\n  }\n\n  if (isNumber(boundaries.right)) {\n    boundaries.right -= padding;\n  }\n\n  if (isNumber(boundaries.bottom)) {\n    boundaries.bottom -= padding;\n  }\n\n  return boundaries;\n}\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n */\n\n\nfunction getArea({\n  width,\n  height\n}) {\n  return width * height;\n}\n\nfunction computeAutoPlacement(placement, refRect, target, host, allowedPositions = ['top', 'bottom', 'right', 'left'], boundariesElement = 'viewport', padding = 0) {\n  var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(target, host, padding, boundariesElement);\n  const rects = {\n    top: {\n      width: (_a = boundaries === null || boundaries === void 0 ? void 0 : boundaries.width) !== null && _a !== void 0 ? _a : 0,\n      height: ((_b = refRect === null || refRect === void 0 ? void 0 : refRect.top) !== null && _b !== void 0 ? _b : 0) - ((_c = boundaries === null || boundaries === void 0 ? void 0 : boundaries.top) !== null && _c !== void 0 ? _c : 0)\n    },\n    right: {\n      width: ((_d = boundaries === null || boundaries === void 0 ? void 0 : boundaries.right) !== null && _d !== void 0 ? _d : 0) - ((_e = refRect === null || refRect === void 0 ? void 0 : refRect.right) !== null && _e !== void 0 ? _e : 0),\n      height: (_f = boundaries === null || boundaries === void 0 ? void 0 : boundaries.height) !== null && _f !== void 0 ? _f : 0\n    },\n    bottom: {\n      width: (_g = boundaries === null || boundaries === void 0 ? void 0 : boundaries.width) !== null && _g !== void 0 ? _g : 0,\n      height: ((_h = boundaries === null || boundaries === void 0 ? void 0 : boundaries.bottom) !== null && _h !== void 0 ? _h : 0) - ((_j = refRect === null || refRect === void 0 ? void 0 : refRect.bottom) !== null && _j !== void 0 ? _j : 0)\n    },\n    left: {\n      width: ((_k = refRect.left) !== null && _k !== void 0 ? _k : 0) - ((_l = boundaries === null || boundaries === void 0 ? void 0 : boundaries.left) !== null && _l !== void 0 ? _l : 0),\n      height: (_m = boundaries === null || boundaries === void 0 ? void 0 : boundaries.height) !== null && _m !== void 0 ? _m : 0\n    }\n  };\n  const sortedAreas = Object.keys(rects).map(key => Object.assign(Object.assign({\n    position: key\n  }, rects[key]), {\n    area: getArea(rects[key])\n  })).sort((a, b) => b.area - a.area);\n  let filteredAreas = sortedAreas.filter(({\n    width,\n    height\n  }) => {\n    return width >= target.clientWidth && height >= target.clientHeight;\n  });\n  filteredAreas = filteredAreas.filter(({\n    position\n  }) => {\n    return allowedPositions.some(allowedPosition => {\n      return allowedPosition === position;\n    });\n  });\n  const computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].position : sortedAreas[0].position;\n  const variation = placement.split(' ')[1]; // for tooltip on auto position\n\n  target.className = target.className.replace(/bs-tooltip-auto/g, `bs-tooltip-${getBsVer().isBs5 ? PlacementForBs5[computedPlacement] : computedPlacement}`);\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n\nfunction getOffsets(data) {\n  var _a, _b, _c, _d;\n\n  return {\n    width: data.offsets.target.width,\n    height: data.offsets.target.height,\n    left: Math.floor((_a = data.offsets.target.left) !== null && _a !== void 0 ? _a : 0),\n    top: Math.round((_b = data.offsets.target.top) !== null && _b !== void 0 ? _b : 0),\n    bottom: Math.round((_c = data.offsets.target.bottom) !== null && _c !== void 0 ? _c : 0),\n    right: Math.floor((_d = data.offsets.target.right) !== null && _d !== void 0 ? _d : 0)\n  };\n}\n/**\n * Get the opposite placement of the given one\n */\n\n\nfunction getOppositePlacement(placement) {\n  const hash = {\n    left: 'right',\n    right: 'left',\n    bottom: 'top',\n    top: 'bottom'\n  };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n/**\n * Get the opposite placement variation of the given one\n */\n\n\nfunction getOppositeVariation(variation) {\n  if (variation === 'right') {\n    return 'left';\n  } else if (variation === 'left') {\n    return 'right';\n  }\n\n  return variation;\n}\n\nconst parse = (value, def = 0) => value ? parseFloat(value) : def;\n\nfunction getOuterSizes(element) {\n  const window = element.ownerDocument.defaultView;\n  const styles = window === null || window === void 0 ? void 0 : window.getComputedStyle(element);\n  const x = parse(styles === null || styles === void 0 ? void 0 : styles.marginTop) + parse(styles === null || styles === void 0 ? void 0 : styles.marginBottom);\n  const y = parse(styles === null || styles === void 0 ? void 0 : styles.marginLeft) + parse(styles === null || styles === void 0 ? void 0 : styles.marginRight);\n  return {\n    width: Number(element.offsetWidth) + y,\n    height: Number(element.offsetHeight) + x\n  };\n}\n/**\n * Get offsets to the reference element\n */\n\n\nfunction getReferenceOffsets(target, host, fixedPosition) {\n  const commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(target) : findCommonOffsetParent(target, host);\n  return getOffsetRectRelativeToArbitraryNode(host, commonOffsetParent, fixedPosition);\n}\n/**\n * Get offsets to the target\n */\n\n\nfunction getTargetOffsets(target, hostOffsets, position) {\n  var _a, _b, _c;\n\n  const placement = position.split(' ')[0]; // Get target node sizes\n\n  const targetRect = getOuterSizes(target); // Add position, width and height to our offsets object\n\n  const targetOffsets = {\n    width: targetRect.width,\n    height: targetRect.height\n  }; // depending by the target placement we have to compute its offsets slightly differently\n\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n  targetOffsets[mainSide] = ((_a = hostOffsets[mainSide]) !== null && _a !== void 0 ? _a : 0) + hostOffsets[measurement] / 2 - targetRect[measurement] / 2;\n  targetOffsets[secondarySide] = placement === secondarySide ? ((_b = hostOffsets[secondarySide]) !== null && _b !== void 0 ? _b : 0) - targetRect[secondaryMeasurement] : (_c = hostOffsets[getOppositePlacement(secondarySide)]) !== null && _c !== void 0 ? _c : 0;\n  return targetOffsets;\n}\n\nfunction isModifierEnabled(options, modifierName) {\n  var _a;\n\n  return !!((_a = options.modifiers[modifierName]) === null || _a === void 0 ? void 0 : _a.enabled);\n}\n\nconst availablePositions = {\n  top: ['top', 'top start', 'top end'],\n  bottom: ['bottom', 'bottom start', 'bottom end'],\n  start: ['start', 'start top', 'start bottom'],\n  end: ['end', 'end top', 'end bottom']\n};\n\nfunction checkPopoverMargin(placement, checkPosition) {\n  if (!getBsVer().isBs5) {\n    return false;\n  }\n\n  return availablePositions[checkPosition].includes(placement);\n}\n\nfunction checkMargins(placement) {\n  if (!getBsVer().isBs5) {\n    return '';\n  }\n\n  if (checkPopoverMargin(placement, 'end')) {\n    return 'ms-2';\n  }\n\n  if (checkPopoverMargin(placement, 'start')) {\n    return 'me-2';\n  }\n\n  if (checkPopoverMargin(placement, 'top')) {\n    return 'mb-2';\n  }\n\n  if (checkPopoverMargin(placement, 'bottom')) {\n    return 'mt-2';\n  }\n\n  return '';\n}\n\nfunction updateContainerClass(data, renderer) {\n  const target = data.instance.target;\n  let containerClass = target.className;\n  const dataPlacement = getBsVer().isBs5 ? PlacementForBs5[data.placement] : data.placement;\n\n  if (data.placementAuto) {\n    containerClass = containerClass.replace(/bs-popover-auto/g, `bs-popover-${dataPlacement}`);\n    containerClass = containerClass.replace(/ms-2|me-2|mb-2|mt-2/g, '');\n    containerClass = containerClass.replace(/bs-tooltip-auto/g, `bs-tooltip-${dataPlacement}`);\n    containerClass = containerClass.replace(/\\sauto/g, ` ${dataPlacement}`);\n\n    if (containerClass.indexOf('popover') !== -1) {\n      containerClass = containerClass + ' ' + checkMargins(dataPlacement);\n    }\n\n    if (containerClass.indexOf('popover') !== -1 && containerClass.indexOf('popover-auto') === -1) {\n      containerClass += ' popover-auto';\n    }\n\n    if (containerClass.indexOf('tooltip') !== -1 && containerClass.indexOf('tooltip-auto') === -1) {\n      containerClass += ' tooltip-auto';\n    }\n  }\n\n  containerClass = containerClass.replace(/left|right|top|bottom|end|start/g, `${dataPlacement.split(' ')[0]}`);\n\n  if (renderer) {\n    renderer.setAttribute(target, 'class', containerClass);\n    return;\n  }\n\n  target.className = containerClass;\n}\n\nfunction setStyles(element, styles, renderer) {\n  if (!element || !styles) {\n    return;\n  }\n\n  Object.keys(styles).forEach(prop => {\n    let unit = ''; // add unit if the value is numeric and is one of the following\n\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n\n    if (renderer) {\n      renderer.setStyle(element, prop, `${String(styles[prop])}${unit}`);\n      return;\n    } // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\n    element.style[prop] = String(styles[prop]) + unit;\n  });\n}\n\nfunction arrow(data) {\n  var _a, _b, _c, _d, _e, _f;\n\n  let targetOffsets = data.offsets.target; // if arrowElement is a string, suppose it's a CSS selector\n\n  const arrowElement = data.instance.target.querySelector('.arrow'); // if arrowElement is not found, don't run the modifier\n\n  if (!arrowElement) {\n    return data;\n  }\n\n  const isVertical = ['left', 'right'].indexOf(data.placement.split(' ')[0]) !== -1;\n  const len = isVertical ? 'height' : 'width';\n  const sideCapitalized = isVertical ? 'Top' : 'Left';\n  const side = sideCapitalized.toLowerCase();\n  const altSide = isVertical ? 'left' : 'top';\n  const opSide = isVertical ? 'bottom' : 'right';\n  const arrowElementSize = getOuterSizes(arrowElement)[len];\n  const placementVariation = data.placement.split(' ')[1]; // top/left side\n\n  if (((_a = data.offsets.host[opSide]) !== null && _a !== void 0 ? _a : 0) - arrowElementSize < ((_b = targetOffsets[side]) !== null && _b !== void 0 ? _b : 0)) {\n    targetOffsets[side] -= ((_c = targetOffsets[side]) !== null && _c !== void 0 ? _c : 0) - (((_d = data.offsets.host[opSide]) !== null && _d !== void 0 ? _d : 0) - arrowElementSize);\n  } // bottom/right side\n\n\n  if (Number(data.offsets.host[side]) + Number(arrowElementSize) > ((_e = targetOffsets[opSide]) !== null && _e !== void 0 ? _e : 0)) {\n    targetOffsets[side] += Number(data.offsets.host[side]) + Number(arrowElementSize) - Number(targetOffsets[opSide]);\n  }\n\n  targetOffsets = getClientRect(targetOffsets); // Compute the sideValue using the updated target offsets\n  // take target margin in account because we don't have this info available\n\n  const css = getStyleComputedProperty(data.instance.target);\n  const targetMarginSide = parseFloat(css[`margin${sideCapitalized}`]) || 0;\n  const targetBorderSide = parseFloat(css[`border${sideCapitalized}Width`]) || 0; // compute center of the target\n\n  let center;\n\n  if (!placementVariation) {\n    center = Number(data.offsets.host[side]) + Number(data.offsets.host[len] / 2 - arrowElementSize / 2);\n  } else {\n    const targetBorderRadius = parseFloat(css[\"borderRadius\"]) || 0;\n    const targetSideArrowOffset = Number(targetMarginSide + targetBorderSide + targetBorderRadius);\n    center = side === placementVariation ? Number(data.offsets.host[side]) + targetSideArrowOffset : Number(data.offsets.host[side]) + Number(data.offsets.host[len] - targetSideArrowOffset);\n  }\n\n  let sideValue = center - ((_f = targetOffsets[side]) !== null && _f !== void 0 ? _f : 0) - targetMarginSide - targetBorderSide; // prevent arrowElement from being placed not contiguously to its target\n\n  sideValue = Math.max(Math.min(targetOffsets[len] - (arrowElementSize + 5), sideValue), 0);\n  data.offsets.arrow = {\n    [side]: Math.round(sideValue),\n    [altSide]: '' // make sure to unset any eventual altSide value from the DOM node\n\n  };\n  data.instance.arrow = arrowElement;\n  return data;\n}\n\nfunction flip(data) {\n  data.offsets.target = getClientRect(data.offsets.target);\n\n  if (!isModifierEnabled(data.options, 'flip')) {\n    data.offsets.target = Object.assign(Object.assign({}, data.offsets.target), getTargetOffsets(data.instance.target, data.offsets.host, data.placement));\n    return data;\n  }\n\n  const boundaries = getBoundaries(data.instance.target, data.instance.host, 0, // padding\n  'viewport', false // positionFixed\n  );\n  let placement = data.placement.split(' ')[0];\n  let variation = data.placement.split(' ')[1] || '';\n  const offsetsHost = data.offsets.host;\n  const target = data.instance.target;\n  const host = data.instance.host;\n  const adaptivePosition = computeAutoPlacement('auto', offsetsHost, target, host, data.options.allowedPositions);\n  const flipOrder = [placement, adaptivePosition];\n  flipOrder.forEach((step, index) => {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r;\n\n    if (placement !== step || flipOrder.length === index + 1) {\n      return;\n    }\n\n    placement = data.placement.split(' ')[0]; // using floor because the host offsets may contain decimals we are not going to consider here\n\n    const overlapsRef = placement === 'left' && Math.floor((_a = data.offsets.target.right) !== null && _a !== void 0 ? _a : 0) > Math.floor((_b = data.offsets.host.left) !== null && _b !== void 0 ? _b : 0) || placement === 'right' && Math.floor((_c = data.offsets.target.left) !== null && _c !== void 0 ? _c : 0) < Math.floor((_d = data.offsets.host.right) !== null && _d !== void 0 ? _d : 0) || placement === 'top' && Math.floor((_e = data.offsets.target.bottom) !== null && _e !== void 0 ? _e : 0) > Math.floor((_f = data.offsets.host.top) !== null && _f !== void 0 ? _f : 0) || placement === 'bottom' && Math.floor((_g = data.offsets.target.top) !== null && _g !== void 0 ? _g : 0) < Math.floor((_h = data.offsets.host.bottom) !== null && _h !== void 0 ? _h : 0);\n    const overflowsLeft = Math.floor((_j = data.offsets.target.left) !== null && _j !== void 0 ? _j : 0) < Math.floor((_k = boundaries.left) !== null && _k !== void 0 ? _k : 0);\n    const overflowsRight = Math.floor((_l = data.offsets.target.right) !== null && _l !== void 0 ? _l : 0) > Math.floor((_m = boundaries.right) !== null && _m !== void 0 ? _m : 0);\n    const overflowsTop = Math.floor((_o = data.offsets.target.top) !== null && _o !== void 0 ? _o : 0) < Math.floor((_p = boundaries.top) !== null && _p !== void 0 ? _p : 0);\n    const overflowsBottom = Math.floor((_q = data.offsets.target.bottom) !== null && _q !== void 0 ? _q : 0) > Math.floor((_r = boundaries.bottom) !== null && _r !== void 0 ? _r : 0);\n    const overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom; // flip the variation if required\n\n    const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n    const flippedVariation = isVertical && variation === 'left' && overflowsLeft || isVertical && variation === 'right' && overflowsRight || !isVertical && variation === 'left' && overflowsTop || !isVertical && variation === 'right' && overflowsBottom;\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? ` ${variation}` : '');\n      data.offsets.target = Object.assign(Object.assign({}, data.offsets.target), getTargetOffsets(data.instance.target, data.offsets.host, data.placement));\n    }\n  });\n  return data;\n}\n\nfunction initData(targetElement, hostElement, position, options) {\n  if (!targetElement || !hostElement) {\n    return;\n  }\n\n  const hostElPosition = getReferenceOffsets(targetElement, hostElement);\n\n  if (!position.match(/^(auto)*\\s*(left|right|top|bottom|start|end)*$/) && !position.match(/^(left|right|top|bottom|start|end)*(?: (left|right|top|bottom|start|end))*$/)) {\n    position = 'auto';\n  }\n\n  const placementAuto = !!position.match(/auto/g); // support old placements 'auto left|right|top|bottom'\n\n  let placement = position.match(/auto\\s(left|right|top|bottom|start|end)/) ? position.split(' ')[1] || 'auto' : position; // Normalize placements that have identical main placement and variation (\"right right\" => \"right\").\n\n  const matches = placement.match(/^(left|right|top|bottom|start|end)* ?(?!\\1)(left|right|top|bottom|start|end)?/);\n\n  if (matches) {\n    placement = matches[1] + (matches[2] ? ` ${matches[2]}` : '');\n  } // \"left right\", \"top bottom\" etc. placements also considered incorrect.\n\n\n  if (['left right', 'right left', 'top bottom', 'bottom top'].indexOf(placement) !== -1) {\n    placement = 'auto';\n  }\n\n  placement = computeAutoPlacement(placement, hostElPosition, targetElement, hostElement, options ? options.allowedPositions : undefined);\n  const targetOffset = getTargetOffsets(targetElement, hostElPosition, placement);\n  return {\n    options: options || {\n      modifiers: {}\n    },\n    instance: {\n      target: targetElement,\n      host: hostElement,\n      arrow: void 0\n    },\n    offsets: {\n      target: targetOffset,\n      host: hostElPosition,\n      arrow: void 0\n    },\n    positionFixed: false,\n    placement,\n    placementAuto\n  };\n}\n\nfunction preventOverflow(data) {\n  var _a;\n\n  if (!isModifierEnabled(data.options, 'preventOverflow')) {\n    return data;\n  } // NOTE: DOM access here\n  // resets the target Offsets's position so that the document size can be calculated excluding\n  // the size of the targetOffsets element itself\n\n\n  const transformProp = 'transform';\n  const targetStyles = data.instance.target.style; // assignment to help minification\n\n  const {\n    top,\n    left,\n    [transformProp]: transform\n  } = targetStyles;\n  targetStyles.top = '';\n  targetStyles.left = '';\n  targetStyles[transformProp] = '';\n  const boundaries = getBoundaries(data.instance.target, data.instance.host, 0, // padding\n  ((_a = data.options.modifiers.preventOverflow) === null || _a === void 0 ? void 0 : _a.boundariesElement) || 'scrollParent', false // positionFixed\n  ); // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n\n  targetStyles.top = top;\n  targetStyles.left = left;\n  targetStyles[transformProp] = transform;\n  const order = ['left', 'right', 'top', 'bottom'];\n  const check = {\n    primary(placement) {\n      var _a, _b, _c, _d;\n\n      let value = data.offsets.target[placement]; // options.escapeWithReference\n\n      if (((_a = data.offsets.target[placement]) !== null && _a !== void 0 ? _a : 0) < ((_b = boundaries[placement]) !== null && _b !== void 0 ? _b : 0)) {\n        value = Math.max((_c = data.offsets.target[placement]) !== null && _c !== void 0 ? _c : 0, (_d = boundaries[placement]) !== null && _d !== void 0 ? _d : 0);\n      }\n\n      return {\n        [placement]: value\n      };\n    },\n\n    secondary(placement) {\n      var _a, _b, _c, _d;\n\n      const isPlacementHorizontal = placement === 'right';\n      const mainSide = isPlacementHorizontal ? 'left' : 'top';\n      const measurement = isPlacementHorizontal ? 'width' : 'height';\n      let value = data.offsets.target[mainSide]; // escapeWithReference\n\n      if (((_a = data.offsets.target[placement]) !== null && _a !== void 0 ? _a : 0) > ((_b = boundaries[placement]) !== null && _b !== void 0 ? _b : 0)) {\n        value = Math.min((_c = data.offsets.target[mainSide]) !== null && _c !== void 0 ? _c : 0, ((_d = boundaries[placement]) !== null && _d !== void 0 ? _d : 0) - data.offsets.target[measurement]);\n      }\n\n      return {\n        [mainSide]: value\n      };\n    }\n\n  };\n  order.forEach(placement => {\n    const side = ['left', 'top', 'start'].indexOf(placement) !== -1 ? check['primary'] : check['secondary'];\n    data.offsets.target = Object.assign(Object.assign({}, data.offsets.target), side(placement));\n  });\n  return data;\n}\n\nfunction shift(data) {\n  var _a;\n\n  const placement = data.placement;\n  const basePlacement = placement.split(' ')[0];\n  const shiftVariation = placement.split(' ')[1];\n\n  if (shiftVariation) {\n    const {\n      host,\n      target\n    } = data.offsets;\n    const isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    const side = isVertical ? 'left' : 'top';\n    const measurement = isVertical ? 'width' : 'height';\n    const shiftOffsets = {\n      start: {\n        [side]: host[side]\n      },\n      end: {\n        [side]: ((_a = host[side]) !== null && _a !== void 0 ? _a : 0) + host[measurement] - target[measurement]\n      }\n    };\n    data.offsets.target = Object.assign(Object.assign({}, target), {\n      [side]: side === shiftVariation ? shiftOffsets.start[side] : shiftOffsets.end[side]\n    });\n  }\n\n  return data;\n}\n\nclass Positioning {\n  position(hostElement, targetElement\n  /*, round = true*/\n  ) {\n    return this.offset(hostElement, targetElement\n    /*, false*/\n    );\n  }\n\n  offset(hostElement, targetElement\n  /*, round = true*/\n  ) {\n    return getReferenceOffsets(targetElement, hostElement);\n  }\n\n  positionElements(hostElement, targetElement, position, appendToBody, options) {\n    const chainOfModifiers = [flip, shift, preventOverflow, arrow];\n    const _position = MapPlacementInToRL[position];\n    const data = initData(targetElement, hostElement, _position, options);\n\n    if (!data) {\n      return;\n    }\n\n    return chainOfModifiers.reduce((modifiedData, modifier) => modifier(modifiedData), data);\n  }\n\n}\n\nconst positionService = new Positioning();\n\nfunction positionElements(hostElement, targetElement, placement, appendToBody, options, renderer) {\n  const data = positionService.positionElements(hostElement, targetElement, placement, appendToBody, options);\n\n  if (!data) {\n    return;\n  }\n\n  const offsets = getOffsets(data);\n  setStyles(targetElement, {\n    'will-change': 'transform',\n    top: '0px',\n    left: '0px',\n    transform: `translate3d(${offsets.left}px, ${offsets.top}px, 0px)`\n  }, renderer);\n\n  if (data.instance.arrow) {\n    setStyles(data.instance.arrow, data.offsets.arrow, renderer);\n  }\n\n  updateContainerClass(data, renderer);\n}\n\nlet PositioningService = /*#__PURE__*/(() => {\n  class PositioningService {\n    constructor(ngZone, rendererFactory, platformId) {\n      this.update$$ = new Subject();\n      this.positionElements = new Map();\n      this.isDisabled = false;\n\n      if (isPlatformBrowser(platformId)) {\n        ngZone.runOutsideAngular(() => {\n          this.triggerEvent$ = merge(fromEvent(window, 'scroll', {\n            passive: true\n          }), fromEvent(window, 'resize', {\n            passive: true\n          }), of(0, animationFrameScheduler), this.update$$);\n          this.triggerEvent$.subscribe(() => {\n            if (this.isDisabled) {\n              return;\n            }\n\n            this.positionElements // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            .forEach(positionElement => {\n              positionElements(_getHtmlElement(positionElement.target), _getHtmlElement(positionElement.element), positionElement.attachment, positionElement.appendToBody, this.options, rendererFactory.createRenderer(null, null));\n            });\n          });\n        });\n      }\n    }\n\n    position(options) {\n      this.addPositionElement(options);\n    }\n\n    get event$() {\n      return this.triggerEvent$;\n    }\n\n    disable() {\n      this.isDisabled = true;\n    }\n\n    enable() {\n      this.isDisabled = false;\n    }\n\n    addPositionElement(options) {\n      this.positionElements.set(_getHtmlElement(options.element), options);\n    }\n\n    calcPosition() {\n      this.update$$.next(null);\n    }\n\n    deletePositionElement(elRef) {\n      this.positionElements.delete(_getHtmlElement(elRef));\n    }\n\n    setOptions(options) {\n      this.options = options;\n    }\n\n  }\n\n  PositioningService.ɵfac = function PositioningService_Factory(t) {\n    return new (t || PositioningService)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(PLATFORM_ID));\n  };\n\n  PositioningService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PositioningService,\n    factory: PositioningService.ɵfac,\n    providedIn: 'root'\n  });\n  return PositioningService;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction _getHtmlElement(element) {\n  // it means that we got a selector\n  if (typeof element === 'string') {\n    return document.querySelector(element);\n  }\n\n  if (element instanceof ElementRef) {\n    return element.nativeElement;\n  }\n\n  return element !== null && element !== void 0 ? element : null;\n}\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { PlacementForBs5, Positioning, PositioningService, checkMargins, positionElements }; //# sourceMappingURL=ngx-bootstrap-positioning.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}