{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { environment } from '../../environments/environment';\nimport { NavigationEnd } from '@angular/router';\nimport { BlockUI } from 'ng-block-ui';\nimport $ from \"jquery\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../_services/common.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"../_helpers/confirm-dialog/confirm.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"ngx-bootstrap/modal\";\nimport * as i8 from \"src/app/_services/authentication.service\";\nimport * as i9 from \"ng-block-ui\";\n\nfunction GhooriLearningComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 9);\n  }\n}\n\nexport class GhooriLearningComponent {\n  constructor(sanitizer, router, _service, toastr, confirmService, route, _location, modalService, authService) {\n    this.sanitizer = sanitizer;\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.confirmService = confirmService;\n    this.route = route;\n    this._location = _location;\n    this.modalService = modalService;\n    this.authService = authService;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.timestamp = new Date().getTime();\n    this.isTrainee = false;\n    this.redirectUrl = null;\n    this.rawHtmlContent = '<div>Your raw HTML content here</div>';\n    this.ghooriUrl = environment.ghooriUrl;\n    this.router.events.subscribe(evt => {\n      if (!(evt instanceof NavigationEnd)) {\n        return;\n      }\n\n      window.scrollTo(0, 0);\n    });\n  }\n\n  ngOnInit() {\n    this.authService.getCurrentUser().subscribe(user => {\n      this.currentUser = user;\n      console.log('this.currentUser', this.currentUser);\n      this.isTrainee = user.Roles.indexOf('Trainee') !== -1 || user.Roles.indexOf('Guest') !== -1;\n    });\n    console.log('this.currentUser', this.currentUser);\n    this.loginToGhoori();\n  }\n\n  onIframeLoad() {\n    // This function will be called when the iframe content finishes loading\n    this.blockUI.stop();\n  }\n\n  loginToGhoori() {\n    let obj = {\n      username: this.currentUser.FullName,\n      pin: this.currentUser.UserName,\n      access_key: \"lDRBqT7NR77yAFScp4m1GOJ4bMmXXkt5\"\n    };\n    this.blockUI.start('You will be redirected to \"Ghoori Learning\". \\nIt will take few seconds. Please wait...');\n\n    this._service.postGhoori('api/brac/user-authentication', obj, this.ghooriUrl).subscribe({\n      next: res => {\n        console.log('res', res);\n\n        if (res.message !== \"Token has been created successfully.\") {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          this.blockUI.stop();\n          return;\n        }\n\n        ;\n        this.blockUI.stop();\n        window.open(res.redirect_url, '_blank'); // this.redirectUrl  = this.sanitizer.bypassSecurityTrustResourceUrl(`${res.redirect_url}`);\n        // this.safeHtmlContent = this.sanitizer.bypassSecurityTrustHtml(`${res.redirect_url}`);\n      } // this.learningHoursContentList = res.Data.Records;\n\n    });\n  }\n\n  loadJquery() {\n    (function ($) {})($);\n  }\n\n  backClicked() {\n    this._location.back();\n  }\n\n}\n\nGhooriLearningComponent.ɵfac = function GhooriLearningComponent_Factory(t) {\n  return new (t || GhooriLearningComponent)(i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.CommonService), i0.ɵɵdirectiveInject(i4.ToastrService), i0.ɵɵdirectiveInject(i5.ConfirmService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i6.Location), i0.ɵɵdirectiveInject(i7.BsModalService), i0.ɵɵdirectiveInject(i8.AuthenticationService));\n};\n\nGhooriLearningComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: GhooriLearningComponent,\n  selectors: [[\"app-ghoori-learning\"]],\n  decls: 12,\n  vars: 1,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"btn\", \"btn-link\", \"fs-4\", \"fw-bold\", \"btn-sm\", \"mb-2\", 3, \"click\"], [1, \"fs-4\", \"ai-arrow-left\", \"fs-base\", \"me-2\"], [1, \"mt-1\", \"mb-4\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"row\"]],\n  template: function GhooriLearningComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h1\", 4);\n      i0.ɵɵtext(6, \"Ghoori Learning\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"a\", 5);\n      i0.ɵɵlistener(\"click\", function GhooriLearningComponent_Template_a_click_7_listener() {\n        return ctx.backClicked();\n      });\n      i0.ɵɵelement(8, \"i\", 6);\n      i0.ɵɵtext(9, \"Go Back \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(10, \"hr\", 7);\n      i0.ɵɵtemplate(11, GhooriLearningComponent_div_11_Template, 1, 0, \"div\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(11);\n      i0.ɵɵproperty(\"ngIf\", ctx.redirectUrl != null);\n    }\n  },\n  directives: [i9.BlockUIComponent, i6.NgIf],\n  styles: [\"\"],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], GhooriLearningComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}