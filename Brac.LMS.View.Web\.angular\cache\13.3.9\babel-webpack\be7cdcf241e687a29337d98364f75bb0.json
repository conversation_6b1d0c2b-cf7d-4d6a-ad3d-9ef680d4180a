{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { BookmarksRoutes } from './bookmarks.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let BookmarksModule = /*#__PURE__*/(() => {\n  class BookmarksModule {}\n\n  BookmarksModule.ɵfac = function BookmarksModule_Factory(t) {\n    return new (t || BookmarksModule)();\n  };\n\n  BookmarksModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BookmarksModule\n  });\n  BookmarksModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(BookmarksRoutes), SharedModule]]\n  });\n  return BookmarksModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}