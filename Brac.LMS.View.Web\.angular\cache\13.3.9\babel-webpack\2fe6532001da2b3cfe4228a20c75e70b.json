{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { TemplateRef } from '@angular/core';\nimport { FormControl, Validators } from '@angular/forms';\nimport { BlockUI } from 'ng-block-ui';\nimport { MustMatch } from 'src/app/_helpers/must-match.validator';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { Timer } from 'src/app/_models/timer';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../_services/authentication.service\";\nimport * as i3 from \"src/app/_services/common.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"ngx-bootstrap/modal\";\nimport * as i7 from \"ngx-smart-modal\";\nimport * as i8 from \"ng-block-ui\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"ngx-extended-pdf-viewer\";\nimport * as i11 from \"ng-otp-input\";\n\nfunction LoginComponent_div_29_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1, \" Username is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, LoginComponent_div_29_span_1_Template, 2, 0, \"span\", 39);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"Username\"].errors[\"required\"]);\n  }\n}\n\nfunction LoginComponent_div_37_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1, \" Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, LoginComponent_div_37_span_1_Template, 2, 0, \"span\", 39);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"Password\"].errors[\"required\"]);\n  }\n}\n\nfunction LoginComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtext(1, \" Caps lock on \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginComponent_div_48_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelementStart(1, \"div\", 47);\n    i0.ɵɵelementStart(2, \"p\", 48);\n    i0.ɵɵtext(3, \" This document can't preview here. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementStart(1, \"div\", 43);\n    i0.ɵɵelementStart(2, \"ngx-extended-pdf-viewer\", 44);\n    i0.ɵɵlistener(\"srcChange\", function LoginComponent_div_48_Template_ngx_extended_pdf_viewer_srcChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return ctx_r10.pdfSrc = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, LoginComponent_div_48_div_3_Template, 4, 0, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r4.pdfSrc)(\"zoom\", \"page-width\")(\"showPrintButton\", true)(\"showRotateButton\", false)(\"showOpenFileButton\", false)(\"showDownloadButton\", true)(\"showBookmarkButton\", true)(\"showSecondaryToolbarButton\", false)(\"useBrowserLocale\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.docObj);\n  }\n}\n\nfunction LoginComponent_ng_template_49_ng_container_5_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtext(1, \"PIN is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginComponent_ng_template_49_ng_container_5_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtemplate(1, LoginComponent_ng_template_49_ng_container_5_div_9_div_1_Template, 2, 0, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.pf[\"pin\"].errors[\"required\"]);\n  }\n}\n\nfunction LoginComponent_ng_template_49_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p\");\n    i0.ɵɵtext(2, \"Lost your password? Please enter your PIN to receive OTP in your Official Number. Use the OTP to reset your password.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 54);\n    i0.ɵɵelementStart(4, \"div\", 55);\n    i0.ɵɵelementStart(5, \"label\", 56);\n    i0.ɵɵtext(6, \"PIN\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 57);\n    i0.ɵɵelement(8, \"input\", 58);\n    i0.ɵɵtemplate(9, LoginComponent_ng_template_49_ng_container_5_div_9_Template, 2, 1, \"div\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 60);\n    i0.ɵɵelementStart(11, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function LoginComponent_ng_template_49_ng_container_5_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return ctx_r17.onSendOtpRequest();\n    });\n    i0.ɵɵtext(12, \"Send OTP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 62);\n    i0.ɵɵelementStart(14, \"a\", 63);\n    i0.ɵɵlistener(\"click\", function LoginComponent_ng_template_49_ng_container_5_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return ctx_r19.modalHideForgotPassword();\n    });\n    i0.ɵɵtext(15, \"Back to Sign in\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r12.pinForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.submitted && ctx_r12.pf[\"pin\"].errors);\n  }\n}\n\nfunction LoginComponent_ng_template_49_ng_container_6_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"You may resend request after \", ctx_r20.timer.minute, \"m \", ctx_r20.timer.second, \"s\");\n  }\n}\n\nconst _c0 = function () {\n  return {\n    length: 6,\n    allowNumbersOnly: true\n  };\n};\n\nfunction LoginComponent_ng_template_49_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 66);\n    i0.ɵɵelementStart(4, \"div\", 67);\n    i0.ɵɵelementStart(5, \"label\", 68);\n    i0.ɵɵtext(6, \"Verify Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"ng-otp-input\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function LoginComponent_ng_template_49_ng_container_6_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return ctx_r21.validateOTP();\n    });\n    i0.ɵɵtext(9, \" Validate \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function LoginComponent_ng_template_49_ng_container_6_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return ctx_r23.resendOTP();\n    });\n    i0.ɵɵtext(11, \" Resend OTP \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, LoginComponent_ng_template_49_ng_container_6_span_12_Template, 2, 2, \"span\", 72);\n    i0.ɵɵelementStart(13, \"a\", 73);\n    i0.ɵɵlistener(\"click\", function LoginComponent_ng_template_49_ng_container_6_Template_a_click_13_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return ctx_r24.modalHideForgotPassword();\n    });\n    i0.ɵɵtext(14, \"Back to Sign in\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" An OTP (One Time Passcode) has been sent to \", ctx_r13.pf[\"phoneNumber\"].value, \". Please enter the OTP in the field below to verify your phone number. \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"formCtrl\", ctx_r13.otp)(\"config\", i0.ɵɵpureFunction0(6, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r13.otp.invalid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r13.resendOtpDisabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.resendOtpDisabled);\n  }\n}\n\nfunction LoginComponent_ng_template_49_ng_container_7_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtext(1, \"New Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginComponent_ng_template_49_ng_container_7_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtext(1, \"New Password must be at least 12 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginComponent_ng_template_49_ng_container_7_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtemplate(1, LoginComponent_ng_template_49_ng_container_7_div_9_div_1_Template, 2, 0, \"div\", 39);\n    i0.ɵɵtemplate(2, LoginComponent_ng_template_49_ng_container_7_div_9_div_2_Template, 2, 0, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.rpf[\"new_password\"].errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.rpf[\"new_password\"].errors[\"minlength\"]);\n  }\n}\n\nfunction LoginComponent_ng_template_49_ng_container_7_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtext(1, \"Confirm Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginComponent_ng_template_49_ng_container_7_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtext(1, \"Passwords must match \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginComponent_ng_template_49_ng_container_7_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtemplate(1, LoginComponent_ng_template_49_ng_container_7_div_15_div_1_Template, 2, 0, \"div\", 39);\n    i0.ɵɵtemplate(2, LoginComponent_ng_template_49_ng_container_7_div_15_div_2_Template, 2, 0, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r26.rpf[\"confirmPassword\"].errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r26.rpf[\"confirmPassword\"].errors[\"mustMatch\"]);\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction LoginComponent_ng_template_49_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h5\");\n    i0.ɵɵtext(2, \"Reset Your Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 54);\n    i0.ɵɵelementStart(4, \"div\", 55);\n    i0.ɵɵelementStart(5, \"label\", 75);\n    i0.ɵɵtext(6, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 76);\n    i0.ɵɵelement(8, \"input\", 77);\n    i0.ɵɵtemplate(9, LoginComponent_ng_template_49_ng_container_7_div_9_Template, 3, 2, \"div\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 55);\n    i0.ɵɵelementStart(11, \"label\", 75);\n    i0.ɵɵtext(12, \"Confirm Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 76);\n    i0.ɵɵelement(14, \"input\", 78);\n    i0.ɵɵtemplate(15, LoginComponent_ng_template_49_ng_container_7_div_15_Template, 3, 2, \"div\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 60);\n    i0.ɵɵelementStart(17, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function LoginComponent_ng_template_49_ng_container_7_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return ctx_r31.onFormSubmit();\n    });\n    i0.ɵɵtext(18, \" Submit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 62);\n    i0.ɵɵelementStart(20, \"a\", 79);\n    i0.ɵɵlistener(\"click\", function LoginComponent_ng_template_49_ng_container_7_Template_a_click_20_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return ctx_r33.modalHideForgotPassword();\n    });\n    i0.ɵɵtext(21, \"Back to Sign in\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r14.resetPasswordForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, ctx_r14.submitted && ctx_r14.rpf[\"new_password\"].errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.submitted && ctx_r14.rpf[\"new_password\"].errors);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c1, ctx_r14.submitted && ctx_r14.rpf[\"confirmPassword\"].errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.submitted && ctx_r14.rpf[\"confirmPassword\"].errors);\n  }\n}\n\nfunction LoginComponent_ng_template_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelementStart(1, \"h4\", 50);\n    i0.ɵɵtext(2, \" Forgot Password \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function LoginComponent_ng_template_49_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return ctx_r34.modalHideForgotPassword();\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 52);\n    i0.ɵɵtemplate(5, LoginComponent_ng_template_49_ng_container_5_Template, 16, 2, \"ng-container\", 53);\n    i0.ɵɵtemplate(6, LoginComponent_ng_template_49_ng_container_6_Template, 15, 7, \"ng-container\", 53);\n    i0.ɵɵtemplate(7, LoginComponent_ng_template_49_ng_container_7_Template, 22, 9, \"ng-container\", 53);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.otpStep == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.otpStep === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.otpStep === 2);\n  }\n}\n\nconst _c2 = function () {\n  return {\n    standalone: true\n  };\n};\n\nexport class LoginComponent {\n  constructor(formBuilder, authService, _service, toastr, router, modalService, route, ngxSmartModalService) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this._service = _service;\n    this.toastr = toastr;\n    this.router = router;\n    this.modalService = modalService;\n    this.route = route;\n    this.ngxSmartModalService = ngxSmartModalService;\n    this.submitted = false;\n    this.show = false;\n    this.returnUrl = '';\n    this.otpStep = 0;\n    this.otp = new FormControl('', [Validators.required, Validators.minLength(6)]);\n    this.resendOtpDisabled = false;\n    this.capsLockIsOn = false;\n    this.timer = new Timer();\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.pdfSrc = null;\n    this.docObj = null;\n    this.authService.isLoggedIn().subscribe(isLoggedIn => {\n      if (isLoggedIn) {\n        this.router.navigate([this.authService.INITIAL_PATH]);\n        return;\n      }\n    });\n  }\n\n  ngOnInit() {\n    this.LoginForm = this.formBuilder.group({\n      Username: ['', [Validators.required]],\n      Password: ['', [Validators.required]]\n    });\n    this.pinForm = this.formBuilder.group({\n      pin: ['', [Validators.required]],\n      phoneNumber: ['']\n    });\n    this.resetPasswordForm = this.formBuilder.group({\n      new_password: ['', [Validators.required, Validators.minLength(12)]],\n      confirmPassword: ['', Validators.required]\n    }, {\n      validator: MustMatch('new_password', 'confirmPassword')\n    });\n    this.getAttachments();\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\n  }\n\n  get f() {\n    return this.LoginForm.controls;\n  }\n\n  get pf() {\n    return this.pinForm.controls;\n  }\n\n  get rpf() {\n    return this.resetPasswordForm.controls;\n  }\n\n  onLoginSubmit() {\n    this.submitted = true;\n\n    if (this.LoginForm.invalid) {\n      return;\n    }\n\n    this.blockUI.start('Processing. Please wait...');\n    this.authService.login({\n      UserName: this.LoginForm.value.Username,\n      Password: this.LoginForm.value.Password\n    }).subscribe({\n      next: data => {\n        this.blockUI.stop();\n\n        if (data) {\n          this.toastr.success('Successfully logged in', 'Success!', {\n            timeOut: 2000\n          });\n          if (this.returnUrl === '/') this.router.navigate(['/dashboard']);else this.router.navigate([this.returnUrl]);\n        }\n      },\n      error: e => {\n        this.blockUI.stop();\n\n        if (e.status === 400) {\n          this.toastr.error('Unauthorized request found', 'Warning!', {\n            timeOut: 3000\n          });\n        } else if (e.status === 401) {\n          this.toastr.error('Invalid Username Or Password', 'Warning!', {\n            timeOut: 3000\n          });\n        }\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  forgotPassword(template) {\n    this.modalRef = this.modalService.show(template, {\n      class: 'gray',\n      backdrop: 'static'\n    });\n  }\n\n  modalHideForgotPassword() {\n    console.log('This', {\n      pinForm: this.pinForm,\n      resetPasswordForm: this.resetPasswordForm,\n      otp: this.otp,\n      otpStep: this.otpStep,\n      modalRef: this.modalRef,\n      submitted: this.submitted\n    });\n    this.pinForm.reset();\n    this.resetPasswordForm.reset(); // this.otp.reset();\n\n    this.otp = new FormControl('', [Validators.required, Validators.minLength(6)]);\n    this.otpStep = 0;\n    this.modalRef.hide();\n    this.submitted = false;\n  }\n\n  onSendOtpRequest() {\n    this.submitted = true;\n    if (this.pinForm.invalid) return;\n    this.blockUI.start('Submitting data. Please wait...');\n\n    this._service.post('account/request-for-otp/' + this.pinForm.value.pin).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        if (res.Data) this.pinForm.controls['phoneNumber'].setValue(res.Data);\n\n        if (res.Data && !res.Message) {\n          this.otpStep = 1;\n          this.submitted = false;\n          this.resendOtpDisabled = true;\n          this.timerSubscription = this.timer.start(2 * 60).subscribe(status => {\n            if (status === 'ended') {\n              this.resendOtpDisabled = false;\n              this.timerSubscription.unsubscribe();\n            }\n          });\n        }\n\n        if (res.Message) {\n          this.resendOtpDisabled = false;\n          if (this.timerSubscription) this.timerSubscription.unsubscribe();\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n        }\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  validateOTP() {\n    this.blockUI.start('Validating your OTP. Please wait...');\n    const obj = {\n      pin: this.pinForm.value.pin,\n      otp: this.otp.value\n    };\n\n    this._service.get('account/validate-otp', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        if (res.Data) {\n          this.otpStep = 2;\n          this.otp = res.Data.Otp;\n          this.submitted = false;\n          this.resendOtpDisabled = true;\n        } else if (res.Message) this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 3000\n        });\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  resendOTP() {\n    this.resendOtpDisabled = true;\n    this.timerSubscription = this.timer.start(2 * 60).subscribe(status => {\n      if (status === 'ended') {\n        this.resendOtpDisabled = false;\n        this.timerSubscription.unsubscribe();\n      }\n    });\n    this.onSendOtpRequest();\n  }\n\n  onFormSubmit() {\n    this.submitted = true;\n    if (this.resetPasswordForm.invalid) return;\n    const obj = {\n      Pin: this.pinForm.value.pin,\n      Password: this.resetPasswordForm.value.new_password // Otp: this.otp\n\n    };\n    this.blockUI.start('Submitting data. Please wait...');\n\n    this._service.post('account/reset-password-by-otp', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, 'Success!');\n        this.modalHideForgotPassword();\n        return;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  openPdf(type) {\n    this.ngxSmartModalService.create('docModal', this.tpl).open();\n\n    this._service.getPDFFile(this.mediaBaseUrl + '/api/configuration/download-document-file?type=' + type).subscribe(res => {\n      this.docObj = res;\n      console.log('res res', res);\n      this.pdfSrc = res;\n    });\n  }\n\n  onKeyDown(event) {\n    const capsLockOn = event.getModifierState('CapsLock');\n    this.capsLockIsOn = capsLockOn;\n  }\n\n  downloadDoc() {\n    var link = document.createElement('a');\n    link.href = this.mediaBaseUrl + this.docObj.FilePath;\n    link.target = '_blank';\n    link.rel = 'noopener';\n    link.download = this.docObj.Title + '.' + this.docObj.FilePath.split('.').pop();\n    link.click();\n    link.remove();\n  }\n\n  getAttachments() {\n    this.blockUI.start('Getting data...');\n\n    this._service.get('configuration/doc-or-info-file-available').subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      console.log('attachment', res.Data);\n      this.attachment = res.Data;\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.warning(err.Message || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  modalClose() {\n    this.pdfSrc = null;\n    this.docObj = null;\n  }\n\n}\n\nLoginComponent.ɵfac = function LoginComponent_Factory(t) {\n  return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthenticationService), i0.ɵɵdirectiveInject(i3.CommonService), i0.ɵɵdirectiveInject(i4.ToastrService), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.BsModalService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i7.NgxSmartModalService));\n};\n\nLoginComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: LoginComponent,\n  selectors: [[\"app-login\"]],\n  viewQuery: function LoginComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tpl = _t.first);\n    }\n  },\n  decls: 51,\n  vars: 9,\n  consts: [[1, \"vh-100\"], [1, \"container\", \"py-5\", \"h-100\"], [1, \"row\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"h-100\"], [1, \"col\", \"col-xl-10\"], [1, \"card\", \"login-style\"], [1, \"row\", \"g-0\"], [1, \"offset-md-8\", \"offset-sm-0\", \"offset-xs-0\", \"col-md-4\"], [1, \"btn\", \"btn-mini\", \"rounded-pill\", \"w-50\", \"text-uppercase\", \"text-white\", 3, \"click\"], [1, \"justify-content-end\", \"d-flex\", \"login-style-1\"], [\"src\", \"/assets/img/logo/ALO-Logo_512x300.svg\", \"alt\", \"LOGO\", 1, \"login-style-4\"], [1, \"col-md-8\", \"col-lg-8\", \"d-none\", \"d-md-block\"], [1, \"login-style-2\"], [\"src\", \"./assets/img/login/loginsideimage.png\", \"id\", \"reflection\", \"alt\", \"login form\", 1, \"img-fluid\", \"login-style-3\"], [1, \"col-md-4\", \"col-lg-4\", \"d-flex\", \"align-items-center\", \"login-style-5\"], [1, \"card-body\", \"text-black\", \"login-style-6\"], [1, \"text-center\", \"mb-3\", \"pb-1\"], [1, \"h4\", \"fw-bold\", \"mb-0r\", \"text-white\"], [\"autocomplete\", \"off\", \"novalidate\", \"\", 1, \"needs-validation\", \"mt-3\", 3, \"formGroup\"], [\"id\", \"example1\"], [1, \"input-group\", \"mb-2\"], [1, \"fa\", \"fa-user\", \"position-absolute\", \"top-50\", \"start-0\", \"translate-middle-y\", \"ms-3\"], [\"formControlName\", \"Username\", \"type\", \"text\", \"placeholder\", \"Username/PIN\", \"autocomplete\", \"username\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", \"rounded\", 3, \"keydown\"], [\"class\", \"error-text text-start my-2 \", 4, \"ngIf\"], [1, \"fa\", \"fa-lock\", \"position-absolute\", \"top-50\", \"start-0\", \"translate-middle-y\", \"ms-3\"], [1, \"password-toggle\", \"w-100\"], [\"formControlName\", \"Password\", \"placeholder\", \"Password\", \"autocomplete\", \"current-password\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", \"rounded\", 3, \"type\", \"keydown\"], [\"aria-label\", \"Show/hide password\", 1, \"password-toggle-btn\"], [\"type\", \"checkbox\", 1, \"password-toggle-check\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"password-toggle-indicator\"], [\"class\", \"error-text text-start my-2\", 4, \"ngIf\"], [\"class\", \"text-danger text-bold\", 4, \"ngIf\"], [\"href\", \"javascript:;\", 1, \"float-start\", \"mb-2\", \"text-white\", \"w-50\", \"login-style-7\", 3, \"click\"], [1, \"login-style-8\", \"btn\", \"btn-info\", \"btn-sm\", \"fa-pull-left\", \"rounded-pill\", \"w-50\", \"text-uppercase\", \"text-white\", 3, \"click\"], [1, \"col-lg-12\"], [\"identifier\", \"docModal\", \"customClass\", \"nsm-dialog-animation-btt modal-lg\", 3, \"onAnyCloseEventFinished\"], [\"docModal\", \"\"], [\"class\", \"p-1\", 4, \"ngIf\"], [\"templateForgotPassword\", \"\"], [1, \"error-text\", \"text-start\", \"my-2\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [1, \"text-danger\", \"text-bold\"], [1, \"p-1\"], [1, \"w-100\", \"login-style-10\"], [3, \"src\", \"zoom\", \"showPrintButton\", \"showRotateButton\", \"showOpenFileButton\", \"showDownloadButton\", \"showBookmarkButton\", \"showSecondaryToolbarButton\", \"useBrowserLocale\", \"srcChange\"], [\"class\", \"card py-3 mb-2 login-style-11\", 4, \"ngIf\"], [1, \"card\", \"py-3\", \"mb-2\", \"login-style-11\"], [1, \"card-body\"], [1, \"font-size-20\"], [1, \"modal-header\"], [1, \"modal-title\", \"float-start\"], [\"type\", \"button \", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\"], [4, \"ngIf\"], [\"autocomplete\", \"off\", 3, \"formGroup\"], [1, \"mb-3\", \"row\"], [\"for\", \"pin\", 1, \"col-sm-1\", \"col-12\", \"col-form-label\"], [1, \"col-sm-11\", \"col-12\"], [\"type\", \"text\", \"formControlName\", \"pin\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"d-grid\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"text-center\", \"mt-3\", \"fw-bold\"], [\"href\", \"javascript:;\", 1, \"login-style-12\", 3, \"click\"], [1, \"invalid-feedback\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\"], [\"autocomplete\", \"off\"], [1, \"mb-3\"], [\"for\", \"pin\", 1, \"col-form-label\"], [3, \"formCtrl\", \"config\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"disabled\", \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"ms-2\", 3, \"disabled\", \"click\"], [\"class\", \"mb-0 text-muted ms-1 login-style-13\", 4, \"ngIf\"], [\"href\", \"javascript:;\", 1, \"fw-bold\", \"float-end\", \"login-style-15\", 3, \"click\"], [1, \"mb-0\", \"text-muted\", \"ms-1\", \"login-style-13\"], [\"for\", \"Password\", 1, \"col-sm-4\", \"col-form-label\", \"font-weight-bold\"], [1, \"col-sm-8\"], [\"type\", \"password\", \"formControlName\", \"new_password\", \"id\", \"NewPassword\", \"placeholder\", \"************\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"password\", \"formControlName\", \"confirmPassword\", \"id\", \"ConfirmPassword\", \"placeholder\", \"************\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"href\", \"javascript:;\", 1, \"login-style-15\", 3, \"click\"]],\n  template: function LoginComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r36 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"section\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵelementStart(6, \"div\", 5);\n      i0.ɵɵelementStart(7, \"div\", 6);\n      i0.ɵɵelementStart(8, \"button\", 7);\n      i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_8_listener() {\n        return ctx.openPdf(\"document\");\n      });\n      i0.ɵɵelementStart(9, \"span\");\n      i0.ɵɵtext(10, \"User Guide\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(11, \"button\", 7);\n      i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_11_listener() {\n        return ctx.openPdf(\"info\");\n      });\n      i0.ɵɵelementStart(12, \"span\");\n      i0.ɵɵtext(13, \"About Us\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"div\", 8);\n      i0.ɵɵelement(15, \"img\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"div\", 10);\n      i0.ɵɵelementStart(17, \"div\", 11);\n      i0.ɵɵelement(18, \"img\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"div\", 13);\n      i0.ɵɵelementStart(20, \"div\", 14);\n      i0.ɵɵelementStart(21, \"div\", 15);\n      i0.ɵɵelementStart(22, \"span\", 16);\n      i0.ɵɵtext(23, \"Learn|Fun|Grow|Change\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"form\", 17);\n      i0.ɵɵelementStart(25, \"div\", 18);\n      i0.ɵɵelementStart(26, \"div\", 19);\n      i0.ɵɵelement(27, \"i\", 20);\n      i0.ɵɵelementStart(28, \"input\", 21);\n      i0.ɵɵlistener(\"keydown\", function LoginComponent_Template_input_keydown_28_listener($event) {\n        return ctx.onKeyDown($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(29, LoginComponent_div_29_Template, 2, 1, \"div\", 22);\n      i0.ɵɵelementStart(30, \"div\", 19);\n      i0.ɵɵelement(31, \"i\", 23);\n      i0.ɵɵelementStart(32, \"div\", 24);\n      i0.ɵɵelementStart(33, \"input\", 25);\n      i0.ɵɵlistener(\"keydown\", function LoginComponent_Template_input_keydown_33_listener($event) {\n        return ctx.onKeyDown($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"label\", 26);\n      i0.ɵɵelementStart(35, \"input\", 27);\n      i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_35_listener($event) {\n        return ctx.show = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(36, \"span\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(37, LoginComponent_div_37_Template, 2, 1, \"div\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(38, LoginComponent_div_38_Template, 2, 0, \"div\", 30);\n      i0.ɵɵelementStart(39, \"a\", 31);\n      i0.ɵɵlistener(\"click\", function LoginComponent_Template_a_click_39_listener() {\n        i0.ɵɵrestoreView(_r36);\n\n        const _r5 = i0.ɵɵreference(50);\n\n        return ctx.forgotPassword(_r5);\n      });\n      i0.ɵɵtext(40, \"Forgot Password?\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(41, \"button\", 32);\n      i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_41_listener() {\n        return ctx.onLoginSubmit();\n      });\n      i0.ɵɵtext(42, \"Login\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"div\", 33);\n      i0.ɵɵelementStart(44, \"div\");\n      i0.ɵɵelementStart(45, \"ngx-smart-modal\", 34, 35);\n      i0.ɵɵlistener(\"onAnyCloseEventFinished\", function LoginComponent_Template_ngx_smart_modal_onAnyCloseEventFinished_45_listener() {\n        return ctx.modalClose();\n      });\n      i0.ɵɵelementStart(47, \"div\");\n      i0.ɵɵtemplate(48, LoginComponent_div_48_Template, 4, 10, \"div\", 36);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(49, LoginComponent_ng_template_49_Template, 8, 3, \"ng-template\", null, 37, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(24);\n      i0.ɵɵproperty(\"formGroup\", ctx.LoginForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"Username\"].errors);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"type\", ctx.show ? \"text\" : \"password\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngModel\", ctx.show)(\"ngModelOptions\", i0.ɵɵpureFunction0(8, _c2));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"Password\"].errors);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.capsLockIsOn);\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngIf\", ctx.docObj);\n    }\n  },\n  directives: [i8.BlockUIComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i1.RequiredValidator, i9.NgIf, i1.CheckboxControlValueAccessor, i1.NgModel, i7.NgxSmartModalComponent, i10.NgxExtendedPdfViewerComponent, i1.NgForm, i11.NgOtpInputComponent, i9.NgClass],\n  styles: [\"#example1[_ngcontent-%COMP%]{border:1px solid;padding:10px;box-shadow:2px 2px #575555;color:#015cca;border-radius:25px}.divider[_ngcontent-%COMP%]:after, .divider[_ngcontent-%COMP%]:before{content:\\\"\\\";flex:1;height:1px;background:#eee}.h-custom[_ngcontent-%COMP%]{height:calc(100% - 73px)}@media (max-width: 450px){.h-custom[_ngcontent-%COMP%]{height:100%}}.reflection[_ngcontent-%COMP%]{width:500px;height:333px;position:relative}.reflection[_ngcontent-%COMP%]:before{content:\\\"\\\";width:inherit;height:42%;position:absolute;bottom:-42%;background:linear-gradient(to bottom,rgba(255,255,255,.3),white);z-index:1}.login-style[_ngcontent-%COMP%]{border-radius:1rem;background-image:linear-gradient(skyblue,skyblue,#45ADEB)}.login-style-1[_ngcontent-%COMP%]{margin-top:30px;padding-top:20px}.login-style-2[_ngcontent-%COMP%]{align-items:center;margin-top:-160px}.login-style-3[_ngcontent-%COMP%]{border-radius:1rem 0 0 1rem}.login-style-4[_ngcontent-%COMP%]{width:16rem;margin-right:60px;height:16rem}.login-style-5[_ngcontent-%COMP%]{margin-top:-90px}.login-style-6[_ngcontent-%COMP%]{padding:0 3rem 3rem}.login-style-7[_ngcontent-%COMP%]{font-size:14px}.login-style-8[_ngcontent-%COMP%]{background-color:#0073b0}.login-style-9[_ngcontent-%COMP%]{letter-spacing:1px}.login-style-10[_ngcontent-%COMP%]{height:605px}.login-style-11[_ngcontent-%COMP%]{height:205px}.login-style-12[_ngcontent-%COMP%]{font-size:14px}.login-style-13[_ngcontent-%COMP%]{font-size:12px}.login-style-14[_ngcontent-%COMP%], .login-style-15[_ngcontent-%COMP%], .login-style-16[_ngcontent-%COMP%]{font-size:14px}\"]\n});\n\n__decorate([BlockUI()], LoginComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}