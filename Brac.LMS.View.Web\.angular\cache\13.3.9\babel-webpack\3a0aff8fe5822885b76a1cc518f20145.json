{"ast": null, "code": "// custom validator to check that two fields match\nexport function MustMatch(controlName, matchingControlName) {\n  return formGroup => {\n    const control = formGroup.controls[controlName];\n    const matchingControl = formGroup.controls[matchingControlName];\n\n    if (matchingControl.errors && !matchingControl.errors['mustMatch']) {\n      // return if another validator has already found an error on the matchingControl\n      return;\n    } // set error on matchingControl if validation fails\n\n\n    if (control.value !== matchingControl.value) {\n      matchingControl.setErrors({\n        mustMatch: true\n      });\n    } else {\n      matchingControl.setErrors(null);\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module"}