{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { PLATFORM_ID, Injectable, Inject, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\n\nlet hasV8BreakIterator; // We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\n\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n} catch (_a) {\n  hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\n\n\nlet Platform = /*#__PURE__*/(() => {\n  class Platform {\n    constructor(_platformId) {\n      this._platformId = _platformId; // We want to use the Angular platform check because if the Document is shimmed\n      // without the navigator, the following checks will fail. This is preferred because\n      // sometimes the Document may be shimmed without the user's knowledge or intention\n\n      /** Whether the Angular application is being rendered in the browser. */\n\n      this.isBrowser = this._platformId ? isPlatformBrowser(this._platformId) : typeof document === 'object' && !!document;\n      /** Whether the current browser is Microsoft Edge. */\n\n      this.EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n      /** Whether the current rendering engine is Microsoft Trident. */\n\n      this.TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent); // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n\n      /** Whether the current rendering engine is Blink. */\n\n      this.BLINK = this.isBrowser && !!(window.chrome || hasV8BreakIterator) && typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT; // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n      // ensure that Webkit runs standalone and is not used as another engine's base.\n\n      /** Whether the current rendering engine is WebKit. */\n\n      this.WEBKIT = this.isBrowser && /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n      /** Whether the current platform is Apple iOS. */\n\n      this.IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window); // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n      // them self as Gecko-like browsers and modify the userAgent's according to that.\n      // Since we only cover one explicit Firefox case, we can simply check for Firefox\n      // instead of having an unstable check for Gecko.\n\n      /** Whether the current browser is Firefox. */\n\n      this.FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n      /** Whether the current platform is Android. */\n      // Trident on mobile adds the android platform to the userAgent to trick detections.\n\n      this.ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT; // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n      // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n      // Safari browser should also use Webkit as its layout engine.\n\n      /** Whether the current browser is Safari. */\n\n      this.SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n    }\n\n  }\n\n  Platform.ɵfac = function Platform_Factory(t) {\n    return new (t || Platform)(i0.ɵɵinject(PLATFORM_ID));\n  };\n\n  Platform.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Platform,\n    factory: Platform.ɵfac,\n    providedIn: 'root'\n  });\n  return Platform;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet PlatformModule = /*#__PURE__*/(() => {\n  class PlatformModule {}\n\n  PlatformModule.ɵfac = function PlatformModule_Factory(t) {\n    return new (t || PlatformModule)();\n  };\n\n  PlatformModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PlatformModule\n  });\n  PlatformModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return PlatformModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Cached result Set of input types support by the current browser. */\n\n\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\n\nconst candidateInputTypes = [// `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n// first changing it to something else:\n// The specified value \"\" does not conform to the required format.\n// The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n'color', 'button', 'checkbox', 'date', 'datetime-local', 'email', 'file', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'text', 'time', 'url', 'week'];\n/** @returns The input types supported by this browser. */\n\nfunction getSupportedInputTypes() {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  } // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n\n\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n    featureTestInput.setAttribute('type', value);\n    return featureTestInput.type === value;\n  }));\n  return supportedInputTypes;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Cached result of whether the user's browser supports passive event listeners. */\n\n\nlet supportsPassiveEvents;\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\n\nfunction supportsPassiveEventListeners() {\n  if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n    try {\n      window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n        get: () => supportsPassiveEvents = true\n      }));\n    } finally {\n      supportsPassiveEvents = supportsPassiveEvents || false;\n    }\n  }\n\n  return supportsPassiveEvents;\n}\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\n\n\nfunction normalizePassiveListenerOptions(options) {\n  return supportsPassiveEventListeners() ? options : !!options.capture;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\n\n\nlet rtlScrollAxisType;\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\n\nlet scrollBehaviorSupported;\n/** Check whether the browser supports scroll behaviors. */\n\nfunction supportsScrollBehavior() {\n  if (scrollBehaviorSupported == null) {\n    // If we're not in the browser, it can't be supported. Also check for `Element`, because\n    // some projects stub out the global `document` during SSR which can throw us off.\n    if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n      scrollBehaviorSupported = false;\n      return scrollBehaviorSupported;\n    } // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n\n\n    if ('scrollBehavior' in document.documentElement.style) {\n      scrollBehaviorSupported = true;\n    } else {\n      // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n      // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n      const scrollToFunction = Element.prototype.scrollTo;\n\n      if (scrollToFunction) {\n        // We can detect if the function has been polyfilled by calling `toString` on it. Native\n        // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n        // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n        // polyfilled functions as supporting scroll behavior.\n        scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n      } else {\n        scrollBehaviorSupported = false;\n      }\n    }\n  }\n\n  return scrollBehaviorSupported;\n}\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\n\n\nfunction getRtlScrollAxisType() {\n  // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n  if (typeof document !== 'object' || !document) {\n    return 0\n    /* NORMAL */\n    ;\n  }\n\n  if (rtlScrollAxisType == null) {\n    // Create a 1px wide scrolling container and a 2px wide content element.\n    const scrollContainer = document.createElement('div');\n    const containerStyle = scrollContainer.style;\n    scrollContainer.dir = 'rtl';\n    containerStyle.width = '1px';\n    containerStyle.overflow = 'auto';\n    containerStyle.visibility = 'hidden';\n    containerStyle.pointerEvents = 'none';\n    containerStyle.position = 'absolute';\n    const content = document.createElement('div');\n    const contentStyle = content.style;\n    contentStyle.width = '2px';\n    contentStyle.height = '1px';\n    scrollContainer.appendChild(content);\n    document.body.appendChild(scrollContainer);\n    rtlScrollAxisType = 0\n    /* NORMAL */\n    ; // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n    // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n    // dealing with one of the other two types of browsers.\n\n    if (scrollContainer.scrollLeft === 0) {\n      // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n      // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n      // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n      // return 0 when we read it again.\n      scrollContainer.scrollLeft = 1;\n      rtlScrollAxisType = scrollContainer.scrollLeft === 0 ? 1\n      /* NEGATED */\n      : 2\n      /* INVERTED */\n      ;\n    }\n\n    scrollContainer.remove();\n  }\n\n  return rtlScrollAxisType;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\n\nfunction _supportsShadowDom() {\n  if (shadowDomIsSupported == null) {\n    const head = typeof document !== 'undefined' ? document.head : null;\n    shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n  }\n\n  return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\n\n\nfunction _getShadowRoot(element) {\n  if (_supportsShadowDom()) {\n    const rootNode = element.getRootNode ? element.getRootNode() : null; // Note that this should be caught by `_supportsShadowDom`, but some\n    // teams have been able to hit this code path on unsupported browsers.\n\n    if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n      return rootNode;\n    }\n  }\n\n  return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\n\n\nfunction _getFocusedElementPierceShadowDom() {\n  let activeElement = typeof document !== 'undefined' && document ? document.activeElement : null;\n\n  while (activeElement && activeElement.shadowRoot) {\n    const newActiveElement = activeElement.shadowRoot.activeElement;\n\n    if (newActiveElement === activeElement) {\n      break;\n    } else {\n      activeElement = newActiveElement;\n    }\n  }\n\n  return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\n\n\nfunction _getEventTarget(event) {\n  // If an event is bound outside the Shadow DOM, the `event.target` will\n  // point to the shadow root so we have to use `composedPath` instead.\n  return event.composedPath ? event.composedPath()[0] : event.target;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Gets whether the code is currently running in a test environment. */\n\n\nfunction _isTestEnvironment() {\n  // We can't use `declare const` because it causes conflicts inside Google with the real typings\n  // for these symbols and we can't read them off the global object, because they don't appear to\n  // be attached there for some runners like Jest.\n  // (see: https://github.com/angular/components/issues/23365#issuecomment-*********)\n  return (// @ts-ignore\n    typeof __karma__ !== 'undefined' && !!__karma__ || // @ts-ignore\n    typeof jasmine !== 'undefined' && !!jasmine || // @ts-ignore\n    typeof jest !== 'undefined' && !!jest || // @ts-ignore\n    typeof Mocha !== 'undefined' && !!Mocha\n  );\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Platform, PlatformModule, _getEventTarget, _getFocusedElementPierceShadowDom, _getShadowRoot, _isTestEnvironment, _supportsShadowDom, getRtlScrollAxisType, getSupportedInputTypes, normalizePassiveListenerOptions, supportsPassiveEventListeners, supportsScrollBehavior }; //# sourceMappingURL=platform.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}