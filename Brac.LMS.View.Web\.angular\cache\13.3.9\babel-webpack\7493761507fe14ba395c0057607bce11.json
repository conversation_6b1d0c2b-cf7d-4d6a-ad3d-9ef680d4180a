{"ast": null, "code": "import { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nexport function bufferCount(bufferSize, startBufferEvery = null) {\n  startBufferEvery = startBufferEvery !== null && startBufferEvery !== void 0 ? startBufferEvery : bufferSize;\n  return operate((source, subscriber) => {\n    let buffers = [];\n    let count = 0;\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      let toEmit = null;\n\n      if (count++ % startBufferEvery === 0) {\n        buffers.push([]);\n      }\n\n      for (const buffer of buffers) {\n        buffer.push(value);\n\n        if (bufferSize <= buffer.length) {\n          toEmit = toEmit !== null && toEmit !== void 0 ? toEmit : [];\n          toEmit.push(buffer);\n        }\n      }\n\n      if (toEmit) {\n        for (const buffer of toEmit) {\n          arrRemove(buffers, buffer);\n          subscriber.next(buffer);\n        }\n      }\n    }, () => {\n      for (const buffer of buffers) {\n        subscriber.next(buffer);\n      }\n\n      subscriber.complete();\n    }, undefined, () => {\n      buffers = null;\n    }));\n  });\n} //# sourceMappingURL=bufferCount.js.map", "map": null, "metadata": {}, "sourceType": "module"}