{"ast": null, "code": "import { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport * as i2 from '@angular/cdk/portal';\nimport { TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, SkipSelf, Inject, Input, Output, ContentChild, ViewChild, Host, Attribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { mixinTabIndex, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { distinctUntilChanged, startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/cdk/collections';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\n\nconst _c0 = [\"body\"];\n\nfunction MatExpansionPanel_ng_template_5_Template(rf, ctx) {}\n\nconst _c1 = [[[\"mat-expansion-panel-header\"]], \"*\", [[\"mat-action-row\"]]];\nconst _c2 = [\"mat-expansion-panel-header\", \"*\", \"mat-action-row\"];\n\nfunction MatExpansionPanelHeader_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 2);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@indicatorRotate\", ctx_r0._getExpandedState());\n  }\n}\n\nconst _c3 = [[[\"mat-panel-title\"]], [[\"mat-panel-description\"]], \"*\"];\nconst _c4 = [\"mat-panel-title\", \"mat-panel-description\", \"*\"];\nconst MAT_ACCORDION = /*#__PURE__*/new InjectionToken('MAT_ACCORDION');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Time and timing curve for expansion panel animations. */\n// Note: Keep this in sync with the Sass variable for the panel header animation.\n\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM.  This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n */\n\nconst matExpansionAnimations = {\n  /** Animation that rotates the indicator arrow. */\n  indicatorRotate: /*#__PURE__*/trigger('indicatorRotate', [/*#__PURE__*/state('collapsed, void', /*#__PURE__*/style({\n    transform: 'rotate(0deg)'\n  })), /*#__PURE__*/state('expanded', /*#__PURE__*/style({\n    transform: 'rotate(180deg)'\n  })), /*#__PURE__*/transition('expanded <=> collapsed, void => collapsed', /*#__PURE__*/animate(EXPANSION_PANEL_ANIMATION_TIMING))]),\n\n  /** Animation that expands and collapses the panel content. */\n  bodyExpansion: /*#__PURE__*/trigger('bodyExpansion', [/*#__PURE__*/state('collapsed, void', /*#__PURE__*/style({\n    height: '0px',\n    visibility: 'hidden'\n  })), /*#__PURE__*/state('expanded', /*#__PURE__*/style({\n    height: '*',\n    visibility: 'visible'\n  })), /*#__PURE__*/transition('expanded <=> collapsed, void => collapsed', /*#__PURE__*/animate(EXPANSION_PANEL_ANIMATION_TIMING))])\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\n\nlet MatExpansionPanelContent = /*#__PURE__*/(() => {\n  class MatExpansionPanelContent {\n    constructor(_template) {\n      this._template = _template;\n    }\n\n  }\n\n  MatExpansionPanelContent.ɵfac = function MatExpansionPanelContent_Factory(t) {\n    return new (t || MatExpansionPanelContent)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n  };\n\n  MatExpansionPanelContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatExpansionPanelContent,\n    selectors: [[\"ng-template\", \"matExpansionPanelContent\", \"\"]]\n  });\n  return MatExpansionPanelContent;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Counter for generating unique element ids. */\n\n\nlet uniqueId = 0;\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\n\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\n\nlet MatExpansionPanel = /*#__PURE__*/(() => {\n  class MatExpansionPanel extends CdkAccordionItem {\n    constructor(accordion, _changeDetectorRef, _uniqueSelectionDispatcher, _viewContainerRef, _document, _animationMode, defaultOptions) {\n      super(accordion, _changeDetectorRef, _uniqueSelectionDispatcher);\n      this._viewContainerRef = _viewContainerRef;\n      this._animationMode = _animationMode;\n      this._hideToggle = false;\n      /** An event emitted after the body's expansion animation happens. */\n\n      this.afterExpand = new EventEmitter();\n      /** An event emitted after the body's collapse animation happens. */\n\n      this.afterCollapse = new EventEmitter();\n      /** Stream that emits for changes in `@Input` properties. */\n\n      this._inputChanges = new Subject();\n      /** ID for the associated header element. Used for a11y labelling. */\n\n      this._headerId = `mat-expansion-panel-header-${uniqueId++}`;\n      /** Stream of body animation done events. */\n\n      this._bodyAnimationDone = new Subject();\n      this.accordion = accordion;\n      this._document = _document; // We need a Subject with distinctUntilChanged, because the `done` event\n      // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n\n      this._bodyAnimationDone.pipe(distinctUntilChanged((x, y) => {\n        return x.fromState === y.fromState && x.toState === y.toState;\n      })).subscribe(event => {\n        if (event.fromState !== 'void') {\n          if (event.toState === 'expanded') {\n            this.afterExpand.emit();\n          } else if (event.toState === 'collapsed') {\n            this.afterCollapse.emit();\n          }\n        }\n      });\n\n      if (defaultOptions) {\n        this.hideToggle = defaultOptions.hideToggle;\n      }\n    }\n    /** Whether the toggle indicator should be hidden. */\n\n\n    get hideToggle() {\n      return this._hideToggle || this.accordion && this.accordion.hideToggle;\n    }\n\n    set hideToggle(value) {\n      this._hideToggle = coerceBooleanProperty(value);\n    }\n    /** The position of the expansion indicator. */\n\n\n    get togglePosition() {\n      return this._togglePosition || this.accordion && this.accordion.togglePosition;\n    }\n\n    set togglePosition(value) {\n      this._togglePosition = value;\n    }\n    /** Determines whether the expansion panel should have spacing between it and its siblings. */\n\n\n    _hasSpacing() {\n      if (this.accordion) {\n        return this.expanded && this.accordion.displayMode === 'default';\n      }\n\n      return false;\n    }\n    /** Gets the expanded state string. */\n\n\n    _getExpandedState() {\n      return this.expanded ? 'expanded' : 'collapsed';\n    }\n    /** Toggles the expanded state of the expansion panel. */\n\n\n    toggle() {\n      this.expanded = !this.expanded;\n    }\n    /** Sets the expanded state of the expansion panel to false. */\n\n\n    close() {\n      this.expanded = false;\n    }\n    /** Sets the expanded state of the expansion panel to true. */\n\n\n    open() {\n      this.expanded = true;\n    }\n\n    ngAfterContentInit() {\n      if (this._lazyContent) {\n        // Render the content as soon as the panel becomes open.\n        this.opened.pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1)).subscribe(() => {\n          this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n        });\n      }\n    }\n\n    ngOnChanges(changes) {\n      this._inputChanges.next(changes);\n    }\n\n    ngOnDestroy() {\n      super.ngOnDestroy();\n\n      this._bodyAnimationDone.complete();\n\n      this._inputChanges.complete();\n    }\n    /** Checks whether the expansion panel's content contains the currently-focused element. */\n\n\n    _containsFocus() {\n      if (this._body) {\n        const focusedElement = this._document.activeElement;\n        const bodyElement = this._body.nativeElement;\n        return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n      }\n\n      return false;\n    }\n\n  }\n\n  MatExpansionPanel.ɵfac = function MatExpansionPanel_Factory(t) {\n    return new (t || MatExpansionPanel)(i0.ɵɵdirectiveInject(MAT_ACCORDION, 12), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.UniqueSelectionDispatcher), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, 8));\n  };\n\n  MatExpansionPanel.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatExpansionPanel,\n    selectors: [[\"mat-expansion-panel\"]],\n    contentQueries: function MatExpansionPanel_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelContent, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lazyContent = _t.first);\n      }\n    },\n    viewQuery: function MatExpansionPanel_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._body = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-expansion-panel\"],\n    hostVars: 6,\n    hostBindings: function MatExpansionPanel_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-expanded\", ctx.expanded)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-expansion-panel-spacing\", ctx._hasSpacing());\n      }\n    },\n    inputs: {\n      disabled: \"disabled\",\n      expanded: \"expanded\",\n      hideToggle: \"hideToggle\",\n      togglePosition: \"togglePosition\"\n    },\n    outputs: {\n      opened: \"opened\",\n      closed: \"closed\",\n      expandedChange: \"expandedChange\",\n      afterExpand: \"afterExpand\",\n      afterCollapse: \"afterCollapse\"\n    },\n    exportAs: [\"matExpansionPanel\"],\n    features: [i0.ɵɵProvidersFeature([// Provide MatAccordion as undefined to prevent nested expansion panels from registering\n    // to the same accordion.\n    {\n      provide: MAT_ACCORDION,\n      useValue: undefined\n    }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c2,\n    decls: 7,\n    vars: 4,\n    consts: [[\"role\", \"region\", 1, \"mat-expansion-panel-content\", 3, \"id\"], [\"body\", \"\"], [1, \"mat-expansion-panel-body\"], [3, \"cdkPortalOutlet\"]],\n    template: function MatExpansionPanel_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵprojection(0);\n        i0.ɵɵelementStart(1, \"div\", 0, 1);\n        i0.ɵɵlistener(\"@bodyExpansion.done\", function MatExpansionPanel_Template_div_animation_bodyExpansion_done_1_listener($event) {\n          return ctx._bodyAnimationDone.next($event);\n        });\n        i0.ɵɵelementStart(3, \"div\", 2);\n        i0.ɵɵprojection(4, 1);\n        i0.ɵɵtemplate(5, MatExpansionPanel_ng_template_5_Template, 0, 0, \"ng-template\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(6, 2);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"@bodyExpansion\", ctx._getExpandedState())(\"id\", ctx.id);\n        i0.ɵɵattribute(\"aria-labelledby\", ctx._headerId);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"cdkPortalOutlet\", ctx._portal);\n      }\n    },\n    directives: [i2.CdkPortalOutlet],\n    styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;border-radius:4px;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:4px;border-top-left-radius:4px}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px}.mat-action-row button.mat-button-base,.mat-action-row button.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row button.mat-button-base,[dir=rtl] .mat-action-row button.mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [matExpansionAnimations.bodyExpansion]\n    },\n    changeDetection: 0\n  });\n  return MatExpansionPanel;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\n\n\nlet MatExpansionPanelActionRow = /*#__PURE__*/(() => {\n  class MatExpansionPanelActionRow {}\n\n  MatExpansionPanelActionRow.ɵfac = function MatExpansionPanelActionRow_Factory(t) {\n    return new (t || MatExpansionPanelActionRow)();\n  };\n\n  MatExpansionPanelActionRow.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatExpansionPanelActionRow,\n    selectors: [[\"mat-action-row\"]],\n    hostAttrs: [1, \"mat-action-row\"]\n  });\n  return MatExpansionPanelActionRow;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})(); // Boilerplate for applying mixins to MatExpansionPanelHeader.\n\n/** @docs-private */\n\n\nclass MatExpansionPanelHeaderBase {}\n\nconst _MatExpansionPanelHeaderMixinBase = /*#__PURE__*/mixinTabIndex(MatExpansionPanelHeaderBase);\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\n\n\nlet MatExpansionPanelHeader = /*#__PURE__*/(() => {\n  class MatExpansionPanelHeader extends _MatExpansionPanelHeaderMixinBase {\n    constructor(panel, _element, _focusMonitor, _changeDetectorRef, defaultOptions, _animationMode, tabIndex) {\n      super();\n      this.panel = panel;\n      this._element = _element;\n      this._focusMonitor = _focusMonitor;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._animationMode = _animationMode;\n      this._parentChangeSubscription = Subscription.EMPTY;\n      const accordionHideToggleChange = panel.accordion ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition']))) : EMPTY;\n      this.tabIndex = parseInt(tabIndex || '') || 0; // Since the toggle state depends on an @Input on the panel, we\n      // need to subscribe and trigger change detection manually.\n\n      this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n        return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n      }))).subscribe(() => this._changeDetectorRef.markForCheck()); // Avoids focus being lost if the panel contained the focused element and was closed.\n\n      panel.closed.pipe(filter(() => panel._containsFocus())).subscribe(() => _focusMonitor.focusVia(_element, 'program'));\n\n      if (defaultOptions) {\n        this.expandedHeight = defaultOptions.expandedHeight;\n        this.collapsedHeight = defaultOptions.collapsedHeight;\n      }\n    }\n    /**\n     * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n     * @docs-private\n     */\n\n\n    get disabled() {\n      return this.panel.disabled;\n    }\n    /** Toggles the expanded state of the panel. */\n\n\n    _toggle() {\n      if (!this.disabled) {\n        this.panel.toggle();\n      }\n    }\n    /** Gets whether the panel is expanded. */\n\n\n    _isExpanded() {\n      return this.panel.expanded;\n    }\n    /** Gets the expanded state string of the panel. */\n\n\n    _getExpandedState() {\n      return this.panel._getExpandedState();\n    }\n    /** Gets the panel id. */\n\n\n    _getPanelId() {\n      return this.panel.id;\n    }\n    /** Gets the toggle position for the header. */\n\n\n    _getTogglePosition() {\n      return this.panel.togglePosition;\n    }\n    /** Gets whether the expand indicator should be shown. */\n\n\n    _showToggle() {\n      return !this.panel.hideToggle && !this.panel.disabled;\n    }\n    /**\n     * Gets the current height of the header. Null if no custom height has been\n     * specified, and if the default height from the stylesheet should be used.\n     */\n\n\n    _getHeaderHeight() {\n      const isExpanded = this._isExpanded();\n\n      if (isExpanded && this.expandedHeight) {\n        return this.expandedHeight;\n      } else if (!isExpanded && this.collapsedHeight) {\n        return this.collapsedHeight;\n      }\n\n      return null;\n    }\n    /** Handle keydown event calling to toggle() if appropriate. */\n\n\n    _keydown(event) {\n      switch (event.keyCode) {\n        // Toggle for space and enter keys.\n        case SPACE:\n        case ENTER:\n          if (!hasModifierKey(event)) {\n            event.preventDefault();\n\n            this._toggle();\n          }\n\n          break;\n\n        default:\n          if (this.panel.accordion) {\n            this.panel.accordion._handleHeaderKeydown(event);\n          }\n\n          return;\n      }\n    }\n    /**\n     * Focuses the panel header. Implemented as a part of `FocusableOption`.\n     * @param origin Origin of the action that triggered the focus.\n     * @docs-private\n     */\n\n\n    focus(origin, options) {\n      if (origin) {\n        this._focusMonitor.focusVia(this._element, origin, options);\n      } else {\n        this._element.nativeElement.focus(options);\n      }\n    }\n\n    ngAfterViewInit() {\n      this._focusMonitor.monitor(this._element).subscribe(origin => {\n        if (origin && this.panel.accordion) {\n          this.panel.accordion._handleHeaderFocus(this);\n        }\n      });\n    }\n\n    ngOnDestroy() {\n      this._parentChangeSubscription.unsubscribe();\n\n      this._focusMonitor.stopMonitoring(this._element);\n    }\n\n  }\n\n  MatExpansionPanelHeader.ɵfac = function MatExpansionPanelHeader_Factory(t) {\n    return new (t || MatExpansionPanelHeader)(i0.ɵɵdirectiveInject(MatExpansionPanel, 1), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2$1.FocusMonitor), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵinjectAttribute('tabindex'));\n  };\n\n  MatExpansionPanelHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatExpansionPanelHeader,\n    selectors: [[\"mat-expansion-panel-header\"]],\n    hostAttrs: [\"role\", \"button\", 1, \"mat-expansion-panel-header\", \"mat-focus-indicator\"],\n    hostVars: 15,\n    hostBindings: function MatExpansionPanelHeader_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatExpansionPanelHeader_click_HostBindingHandler() {\n          return ctx._toggle();\n        })(\"keydown\", function MatExpansionPanelHeader_keydown_HostBindingHandler($event) {\n          return ctx._keydown($event);\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx.panel._headerId)(\"tabindex\", ctx.tabIndex)(\"aria-controls\", ctx._getPanelId())(\"aria-expanded\", ctx._isExpanded())(\"aria-disabled\", ctx.panel.disabled);\n        i0.ɵɵstyleProp(\"height\", ctx._getHeaderHeight());\n        i0.ɵɵclassProp(\"mat-expanded\", ctx._isExpanded())(\"mat-expansion-toggle-indicator-after\", ctx._getTogglePosition() === \"after\")(\"mat-expansion-toggle-indicator-before\", ctx._getTogglePosition() === \"before\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n      }\n    },\n    inputs: {\n      tabIndex: \"tabIndex\",\n      expandedHeight: \"expandedHeight\",\n      collapsedHeight: \"collapsedHeight\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c4,\n    decls: 5,\n    vars: 1,\n    consts: [[1, \"mat-content\"], [\"class\", \"mat-expansion-indicator\", 4, \"ngIf\"], [1, \"mat-expansion-indicator\"]],\n    template: function MatExpansionPanelHeader_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c3);\n        i0.ɵɵelementStart(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵprojection(2, 1);\n        i0.ɵɵprojection(3, 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, MatExpansionPanelHeader_span_4_Template, 1, 1, \"span\", 1);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx._showToggle());\n      }\n    },\n    directives: [i3.NgIf],\n    styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;margin-right:16px}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header-description{flex-grow:2}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle}.cdk-high-contrast-active .mat-expansion-panel .mat-expansion-panel-header.cdk-keyboard-focused:not([aria-disabled=true])::before,.cdk-high-contrast-active .mat-expansion-panel .mat-expansion-panel-header.cdk-program-focused:not([aria-disabled=true])::before,.cdk-high-contrast-active .mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:hover:not([aria-disabled=true])::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;border:3px solid;border-radius:4px;content:\\\"\\\"}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [matExpansionAnimations.indicatorRotate]\n    },\n    changeDetection: 0\n  });\n  return MatExpansionPanelHeader;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\n\n\nlet MatExpansionPanelDescription = /*#__PURE__*/(() => {\n  class MatExpansionPanelDescription {}\n\n  MatExpansionPanelDescription.ɵfac = function MatExpansionPanelDescription_Factory(t) {\n    return new (t || MatExpansionPanelDescription)();\n  };\n\n  MatExpansionPanelDescription.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatExpansionPanelDescription,\n    selectors: [[\"mat-panel-description\"]],\n    hostAttrs: [1, \"mat-expansion-panel-header-description\"]\n  });\n  return MatExpansionPanelDescription;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\n\n\nlet MatExpansionPanelTitle = /*#__PURE__*/(() => {\n  class MatExpansionPanelTitle {}\n\n  MatExpansionPanelTitle.ɵfac = function MatExpansionPanelTitle_Factory(t) {\n    return new (t || MatExpansionPanelTitle)();\n  };\n\n  MatExpansionPanelTitle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatExpansionPanelTitle,\n    selectors: [[\"mat-panel-title\"]],\n    hostAttrs: [1, \"mat-expansion-panel-header-title\"]\n  });\n  return MatExpansionPanelTitle;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Directive for a Material Design Accordion.\n */\n\n\nlet MatAccordion = /*#__PURE__*/(() => {\n  class MatAccordion extends CdkAccordion {\n    constructor() {\n      super(...arguments);\n      /** Headers belonging to this accordion. */\n\n      this._ownHeaders = new QueryList();\n      this._hideToggle = false;\n      /**\n       * Display mode used for all expansion panels in the accordion. Currently two display\n       * modes exist:\n       *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n       *     panel at a different elevation from the rest of the accordion.\n       *  flat - no spacing is placed around expanded panels, showing all panels at the same\n       *     elevation.\n       */\n\n      this.displayMode = 'default';\n      /** The position of the expansion indicator. */\n\n      this.togglePosition = 'after';\n    }\n    /** Whether the expansion indicator should be hidden. */\n\n\n    get hideToggle() {\n      return this._hideToggle;\n    }\n\n    set hideToggle(show) {\n      this._hideToggle = coerceBooleanProperty(show);\n    }\n\n    ngAfterContentInit() {\n      this._headers.changes.pipe(startWith(this._headers)).subscribe(headers => {\n        this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n\n        this._ownHeaders.notifyOnChanges();\n      });\n\n      this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n    }\n    /** Handles keyboard events coming in from the panel headers. */\n\n\n    _handleHeaderKeydown(event) {\n      this._keyManager.onKeydown(event);\n    }\n\n    _handleHeaderFocus(header) {\n      this._keyManager.updateActiveItem(header);\n    }\n\n    ngOnDestroy() {\n      super.ngOnDestroy();\n\n      this._ownHeaders.destroy();\n    }\n\n  }\n\n  MatAccordion.ɵfac = /* @__PURE__ */function () {\n    let ɵMatAccordion_BaseFactory;\n    return function MatAccordion_Factory(t) {\n      return (ɵMatAccordion_BaseFactory || (ɵMatAccordion_BaseFactory = i0.ɵɵgetInheritedFactory(MatAccordion)))(t || MatAccordion);\n    };\n  }();\n\n  MatAccordion.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatAccordion,\n    selectors: [[\"mat-accordion\"]],\n    contentQueries: function MatAccordion_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelHeader, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headers = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-accordion\"],\n    hostVars: 2,\n    hostBindings: function MatAccordion_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-accordion-multi\", ctx.multi);\n      }\n    },\n    inputs: {\n      multi: \"multi\",\n      hideToggle: \"hideToggle\",\n      displayMode: \"displayMode\",\n      togglePosition: \"togglePosition\"\n    },\n    exportAs: [\"matAccordion\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_ACCORDION,\n      useExisting: MatAccordion\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return MatAccordion;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet MatExpansionModule = /*#__PURE__*/(() => {\n  class MatExpansionModule {}\n\n  MatExpansionModule.ɵfac = function MatExpansionModule_Factory(t) {\n    return new (t || MatExpansionModule)();\n  };\n\n  MatExpansionModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatExpansionModule\n  });\n  MatExpansionModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule, MatCommonModule, CdkAccordionModule, PortalModule]]\n  });\n  return MatExpansionModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations }; //# sourceMappingURL=expansion.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}