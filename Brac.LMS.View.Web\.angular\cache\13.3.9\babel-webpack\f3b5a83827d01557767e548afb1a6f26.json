{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Hebrew [he]\n//! author : <PERSON><PERSON> : https://github.com/tomer\n//! author : <PERSON><PERSON> : https://github.com/DevelopmentIL\n//! author : Tal Ater : https://github.com/TalAter\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var he = moment.defineLocale('he', {\n    months: 'ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר'.split('_'),\n    monthsShort: 'ינו׳_פבר׳_מרץ_אפר׳_מאי_יוני_יולי_אוג׳_ספט׳_אוק׳_נוב׳_דצמ׳'.split('_'),\n    weekdays: 'ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת'.split('_'),\n    weekdaysShort: 'א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳'.split('_'),\n    weekdaysMin: 'א_ב_ג_ד_ה_ו_ש'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D [ב]MMMM YYYY',\n      LLL: 'D [ב]MMMM YYYY HH:mm',\n      LLLL: 'dddd, D [ב]MMMM YYYY HH:mm',\n      l: 'D/M/YYYY',\n      ll: 'D MMM YYYY',\n      lll: 'D MMM YYYY HH:mm',\n      llll: 'ddd, D MMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[היום ב־]LT',\n      nextDay: '[מחר ב־]LT',\n      nextWeek: 'dddd [בשעה] LT',\n      lastDay: '[אתמול ב־]LT',\n      lastWeek: '[ביום] dddd [האחרון בשעה] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'בעוד %s',\n      past: 'לפני %s',\n      s: 'מספר שניות',\n      ss: '%d שניות',\n      m: 'דקה',\n      mm: '%d דקות',\n      h: 'שעה',\n      hh: function (number) {\n        if (number === 2) {\n          return 'שעתיים';\n        }\n\n        return number + ' שעות';\n      },\n      d: 'יום',\n      dd: function (number) {\n        if (number === 2) {\n          return 'יומיים';\n        }\n\n        return number + ' ימים';\n      },\n      M: 'חודש',\n      MM: function (number) {\n        if (number === 2) {\n          return 'חודשיים';\n        }\n\n        return number + ' חודשים';\n      },\n      y: 'שנה',\n      yy: function (number) {\n        if (number === 2) {\n          return 'שנתיים';\n        } else if (number % 10 === 0 && number !== 10) {\n          return number + ' שנה';\n        }\n\n        return number + ' שנים';\n      }\n    },\n    meridiemParse: /אחה\"צ|לפנה\"צ|אחרי הצהריים|לפני הצהריים|לפנות בוקר|בבוקר|בערב/i,\n    isPM: function (input) {\n      return /^(אחה\"צ|אחרי הצהריים|בערב)$/.test(input);\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 5) {\n        return 'לפנות בוקר';\n      } else if (hour < 10) {\n        return 'בבוקר';\n      } else if (hour < 12) {\n        return isLower ? 'לפנה\"צ' : 'לפני הצהריים';\n      } else if (hour < 18) {\n        return isLower ? 'אחה\"צ' : 'אחרי הצהריים';\n      } else {\n        return 'בערב';\n      }\n    }\n  });\n  return he;\n});", "map": null, "metadata": {}, "sourceType": "script"}