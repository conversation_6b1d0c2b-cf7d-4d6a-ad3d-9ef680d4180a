{"ast": null, "code": "import { isPlatformServer, is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, forwardRef, Component, Inject, Input, Output, Directive, Host, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\n/**\n * Slick component\n */\n\nconst _c0 = [\"*\"];\nlet SlickCarouselComponent = /*#__PURE__*/(() => {\n  class SlickCarouselComponent {\n    /**\n     * Constructor\n     */\n    constructor(el, zone, platformId) {\n      this.el = el;\n      this.zone = zone;\n      this.platformId = platformId;\n      this.afterChange = new EventEmitter();\n      this.beforeChange = new EventEmitter();\n      this.breakpoint = new EventEmitter();\n      this.destroy = new EventEmitter();\n      this.init = new EventEmitter(); // access from parent component can be a problem with change detection timing. Please use afterChange output\n\n      this.currentIndex = 0;\n      this.slides = [];\n      this.initialized = false;\n      this._removedSlides = [];\n      this._addedSlides = [];\n    }\n    /**\n     * On component destroy\n     */\n\n\n    ngOnDestroy() {\n      this.unslick();\n    }\n\n    ngAfterViewInit() {\n      this.ngAfterViewChecked();\n    }\n    /**\n     * On component view checked\n     */\n\n\n    ngAfterViewChecked() {\n      if (isPlatformServer(this.platformId)) {\n        return;\n      }\n\n      if (this._addedSlides.length > 0 || this._removedSlides.length > 0) {\n        const nextSlidesLength = this.slides.length - this._removedSlides.length + this._addedSlides.length;\n\n        if (!this.initialized) {\n          if (nextSlidesLength > 0) {\n            this.initSlick();\n          } // if nextSlidesLength is zere, do nothing\n\n        } else if (nextSlidesLength === 0) {\n          // unslick case\n          this.unslick();\n        } else {\n          this._addedSlides.forEach(slickItem => {\n            this.slides.push(slickItem);\n            this.zone.runOutsideAngular(() => {\n              this.$instance.slick('slickAdd', slickItem.el.nativeElement);\n            });\n          });\n\n          this._addedSlides = [];\n\n          this._removedSlides.forEach(slickItem => {\n            const idx = this.slides.indexOf(slickItem);\n            this.slides = this.slides.filter(s => s !== slickItem);\n            this.zone.runOutsideAngular(() => {\n              this.$instance.slick('slickRemove', idx);\n            });\n          });\n\n          this._removedSlides = [];\n        }\n      }\n    }\n    /**\n     * init slick\n     */\n\n\n    initSlick() {\n      this.slides = this._addedSlides;\n      this._addedSlides = [];\n      this._removedSlides = [];\n      this.zone.runOutsideAngular(() => {\n        this.$instance = jQuery(this.el.nativeElement);\n        this.$instance.on('init', (event, slick) => {\n          this.zone.run(() => {\n            this.init.emit({\n              event,\n              slick\n            });\n          });\n        });\n        this.$instance.slick(this.config);\n        this.zone.run(() => {\n          var _a;\n\n          this.initialized = true;\n          this.currentIndex = ((_a = this.config) === null || _a === void 0 ? void 0 : _a.initialSlide) || 0;\n        });\n        this.$instance.on('afterChange', (event, slick, currentSlide) => {\n          this.zone.run(() => {\n            this.afterChange.emit({\n              event,\n              slick,\n              currentSlide,\n              first: currentSlide === 0,\n              last: slick.$slides.length === currentSlide + slick.options.slidesToScroll\n            });\n            this.currentIndex = currentSlide;\n          });\n        });\n        this.$instance.on('beforeChange', (event, slick, currentSlide, nextSlide) => {\n          this.zone.run(() => {\n            this.beforeChange.emit({\n              event,\n              slick,\n              currentSlide,\n              nextSlide\n            });\n            this.currentIndex = nextSlide;\n          });\n        });\n        this.$instance.on('breakpoint', (event, slick, breakpoint) => {\n          this.zone.run(() => {\n            this.breakpoint.emit({\n              event,\n              slick,\n              breakpoint\n            });\n          });\n        });\n        this.$instance.on('destroy', (event, slick) => {\n          this.zone.run(() => {\n            this.destroy.emit({\n              event,\n              slick\n            });\n            this.initialized = false;\n          });\n        });\n      });\n    }\n\n    addSlide(slickItem) {\n      this._addedSlides.push(slickItem);\n    }\n\n    removeSlide(slickItem) {\n      this._removedSlides.push(slickItem);\n    }\n    /**\n     * Slick Method\n     */\n\n\n    slickGoTo(index) {\n      this.zone.runOutsideAngular(() => {\n        this.$instance.slick('slickGoTo', index);\n      });\n    }\n\n    slickNext() {\n      this.zone.runOutsideAngular(() => {\n        this.$instance.slick('slickNext');\n      });\n    }\n\n    slickPrev() {\n      this.zone.runOutsideAngular(() => {\n        this.$instance.slick('slickPrev');\n      });\n    }\n\n    slickPause() {\n      this.zone.runOutsideAngular(() => {\n        this.$instance.slick('slickPause');\n      });\n    }\n\n    slickPlay() {\n      this.zone.runOutsideAngular(() => {\n        this.$instance.slick('slickPlay');\n      });\n    }\n\n    unslick() {\n      if (this.$instance) {\n        this.zone.runOutsideAngular(() => {\n          this.$instance.slick('unslick');\n        });\n        this.$instance = undefined;\n      }\n\n      this.initialized = false;\n    }\n\n    ngOnChanges(changes) {\n      if (this.initialized) {\n        const config = changes['config'];\n\n        if (config.previousValue !== config.currentValue && config.currentValue !== undefined) {\n          const refresh = config.currentValue['refresh'];\n          const newOptions = Object.assign({}, config.currentValue);\n          delete newOptions['refresh'];\n          this.zone.runOutsideAngular(() => {\n            this.$instance.slick('slickSetOption', newOptions, refresh);\n          });\n        }\n      }\n    }\n\n  }\n\n  /** @nocollapse */\n  SlickCarouselComponent.ɵfac = function SlickCarouselComponent_Factory(t) {\n    return new (t || SlickCarouselComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  /** @nocollapse */\n\n\n  SlickCarouselComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: SlickCarouselComponent,\n    selectors: [[\"ngx-slick-carousel\"]],\n    inputs: {\n      config: \"config\"\n    },\n    outputs: {\n      afterChange: \"afterChange\",\n      beforeChange: \"beforeChange\",\n      breakpoint: \"breakpoint\",\n      destroy: \"destroy\",\n      init: \"init\"\n    },\n    exportAs: [\"slick-carousel\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => SlickCarouselComponent),\n      multi: true\n    }]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function SlickCarouselComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2\n  });\n  return SlickCarouselComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet SlickItemDirective = /*#__PURE__*/(() => {\n  class SlickItemDirective {\n    constructor(el, platformId, carousel) {\n      this.el = el;\n      this.platformId = platformId;\n      this.carousel = carousel;\n    }\n\n    ngOnInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        this.carousel.addSlide(this);\n      }\n    }\n\n    ngOnDestroy() {\n      if (isPlatformBrowser(this.platformId)) {\n        this.carousel.removeSlide(this);\n      }\n    }\n\n  }\n\n  /** @nocollapse */\n  SlickItemDirective.ɵfac = function SlickItemDirective_Factory(t) {\n    return new (t || SlickItemDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(SlickCarouselComponent, 1));\n  };\n  /** @nocollapse */\n\n\n  SlickItemDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: SlickItemDirective,\n    selectors: [[\"\", \"ngxSlickItem\", \"\"]]\n  });\n  return SlickItemDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet SlickCarouselModule = /*#__PURE__*/(() => {\n  class SlickCarouselModule {}\n\n  /** @nocollapse */\n  SlickCarouselModule.ɵfac = function SlickCarouselModule_Factory(t) {\n    return new (t || SlickCarouselModule)();\n  };\n  /** @nocollapse */\n\n\n  SlickCarouselModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: SlickCarouselModule\n  });\n  /** @nocollapse */\n\n  SlickCarouselModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule]]\n  });\n  return SlickCarouselModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { SlickCarouselComponent, SlickCarouselModule, SlickItemDirective }; //# sourceMappingURL=ngx-slick-carousel.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}