import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NumericDirective } from '../_helpers/numbers-only';
import { BlockUIModule } from 'ng-block-ui';
import { UploadService } from './../_services/upload.service';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { RatingModule } from 'ngx-bootstrap/rating';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MomentModule } from 'ngx-moment';
import { AccordionModule } from 'ngx-bootstrap/accordion';
import { NgxPaginationModule } from 'ngx-pagination';
import { ModalModule } from 'ngx-bootstrap/modal';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { BlockTemplateCmp } from '../_helpers/block-ui-template/block-ui-template';
import { SafePipe } from '../_helpers/safe-pipe';
import { TruncatePipe } from '../_helpers/truncate-pipe';
import { PaginationComponent } from './pagination/pagination.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    BlockUIModule.forRoot({
      template: BlockTemplateCmp,
    }),
    TabsModule.forRoot(),
    RatingModule.forRoot(),
    ModalModule.forRoot(),
    TooltipModule.forRoot(),
    NgSelectModule,
    FlexLayoutModule,
    MatDialogModule,
    MatButtonModule,
    MomentModule,
    AccordionModule.forRoot(),
    NgxPaginationModule,
    NgxDatatableModule,
  ],
  declarations: [NumericDirective, SafePipe, TruncatePipe, PaginationComponent],
  entryComponents: [],
  exports: [
    NumericDirective,
    SafePipe,
    TruncatePipe,
    FormsModule,
    ReactiveFormsModule,
    BlockUIModule,
    TabsModule,
    RatingModule,
    ModalModule,
    TooltipModule,
    NgSelectModule,
    FlexLayoutModule,
    MatDialogModule,
    MatButtonModule,
    MomentModule,
    AccordionModule,
    NgxPaginationModule,
    NgxDatatableModule,
    PaginationComponent,
  ],
  providers: [UploadService],
})
export class SharedModule {}
