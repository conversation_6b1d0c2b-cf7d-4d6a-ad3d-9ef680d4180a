{"ast": null, "code": "import { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function defaultIfEmpty(defaultValue) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      subscriber.next(value);\n    }, () => {\n      if (!hasValue) {\n        subscriber.next(defaultValue);\n      }\n\n      subscriber.complete();\n    }));\n  });\n} //# sourceMappingURL=defaultIfEmpty.js.map", "map": null, "metadata": {}, "sourceType": "module"}