{"ast": null, "code": "export const intervalProvider = {\n  setInterval(...args) {\n    const {\n      delegate\n    } = intervalProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.setInterval) || setInterval)(...args);\n  },\n\n  clearInterval(handle) {\n    const {\n      delegate\n    } = intervalProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearInterval) || clearInterval)(handle);\n  },\n\n  delegate: undefined\n}; //# sourceMappingURL=intervalProvider.js.map", "map": null, "metadata": {}, "sourceType": "module"}