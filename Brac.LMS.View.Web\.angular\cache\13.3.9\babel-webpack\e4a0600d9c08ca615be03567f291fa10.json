{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { CertificationsRoutes } from './certifications.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let CertificationsModule = /*#__PURE__*/(() => {\n  class CertificationsModule {}\n\n  CertificationsModule.ɵfac = function CertificationsModule_Factory(t) {\n    return new (t || CertificationsModule)();\n  };\n\n  CertificationsModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CertificationsModule\n  });\n  CertificationsModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(CertificationsRoutes), SharedModule]]\n  });\n  return CertificationsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}