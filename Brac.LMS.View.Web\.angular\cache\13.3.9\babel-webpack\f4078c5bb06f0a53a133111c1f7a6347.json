{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function repeatWhen(notifier) {\n  return operate((source, subscriber) => {\n    let innerSub;\n    let syncResub = false;\n    let completions$;\n    let isNotifierComplete = false;\n    let isMainComplete = false;\n\n    const checkComplete = () => isMainComplete && isNotifierComplete && (subscriber.complete(), true);\n\n    const getCompletionSubject = () => {\n      if (!completions$) {\n        completions$ = new Subject();\n        notifier(completions$).subscribe(new OperatorSubscriber(subscriber, () => {\n          if (innerSub) {\n            subscribeForRepeatWhen();\n          } else {\n            syncResub = true;\n          }\n        }, () => {\n          isNotifierComplete = true;\n          checkComplete();\n        }));\n      }\n\n      return completions$;\n    };\n\n    const subscribeForRepeatWhen = () => {\n      isMainComplete = false;\n      innerSub = source.subscribe(new OperatorSubscriber(subscriber, undefined, () => {\n        isMainComplete = true;\n        !checkComplete() && getCompletionSubject().next();\n      }));\n\n      if (syncResub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        syncResub = false;\n        subscribeForRepeatWhen();\n      }\n    };\n\n    subscribeForRepeatWhen();\n  });\n} //# sourceMappingURL=repeatWhen.js.map", "map": null, "metadata": {}, "sourceType": "module"}