{"ast": null, "code": "/**\n * @license Angular v13.1.3\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, ɵɵinject, Inject, Optional, EventEmitter, ɵfindLocaleData, ɵLocaleDataIndex, ɵgetLocaleCurrencyCode, ɵgetLocalePluralCase, LOCALE_ID, ɵregisterLocaleData, ɵisListLikeIterable, ɵstringify, Directive, Input, NgModuleRef, ComponentFactoryResolver, Host, ɵRuntimeError, Attribute, ɵisPromise, ɵisSubscribable, <PERSON><PERSON>, DEFAULT_CURRENCY_CODE, NgModule, Version, ɵɵdefineInjectable } from '@angular/core';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nlet _DOM = null;\n\nfunction getDOM() {\n  return _DOM;\n}\n\nfunction setDOM(adapter) {\n  _DOM = adapter;\n}\n\nfunction setRootDomAdapter(adapter) {\n  if (!_DOM) {\n    _DOM = adapter;\n  }\n}\n/* tslint:disable:requireParameterType */\n\n/**\n * Provides DOM operations in an environment-agnostic way.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n\n\nclass DomAdapter {}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A DI Token representing the main rendering context. In a browser this is the DOM Document.\n *\n * Note: Document might not be available in the Application Context when Application and Rendering\n * Contexts are not the same (e.g. when running the application in a Web Worker).\n *\n * @publicApi\n */\n\n\nconst DOCUMENT = /*#__PURE__*/new InjectionToken('DocumentToken');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * `PlatformLocation` encapsulates all calls to DOM APIs, which allows the Router to be\n * platform-agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that Angular supports. For example, `@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `@angular/platform-server` provides\n * one suitable for use with server-side rendering.\n *\n * The `PlatformLocation` class is used directly by all implementations of {@link LocationStrategy}\n * when they need to interact with the DOM APIs like pushState, popState, etc.\n *\n * {@link LocationStrategy} in turn is used by the {@link Location} service which is used directly\n * by the {@link Router} in order to navigate between routes. Since all interactions between {@link\n * Router} /\n * {@link Location} / {@link LocationStrategy} and DOM APIs flow through the `PlatformLocation`\n * class, they are all platform-agnostic.\n *\n * @publicApi\n */\n\nlet PlatformLocation = /*#__PURE__*/(() => {\n  class PlatformLocation {\n    historyGo(relativePosition) {\n      throw new Error('Not implemented');\n    }\n\n  }\n\n  PlatformLocation.ɵfac = function PlatformLocation_Factory(t) {\n    return new (t || PlatformLocation)();\n  };\n\n  PlatformLocation.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PlatformLocation,\n    factory: function () {\n      return useBrowserPlatformLocation();\n    },\n    providedIn: 'platform'\n  });\n  return PlatformLocation;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction useBrowserPlatformLocation() {\n  return ɵɵinject(BrowserPlatformLocation);\n}\n/**\n * @description\n * Indicates when a location is initialized.\n *\n * @publicApi\n */\n\n\nconst LOCATION_INITIALIZED = /*#__PURE__*/new InjectionToken('Location Initialized');\n/**\n * `PlatformLocation` encapsulates all of the direct calls to platform APIs.\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n */\n\nlet BrowserPlatformLocation = /*#__PURE__*/(() => {\n  class BrowserPlatformLocation extends PlatformLocation {\n    constructor(_doc) {\n      super();\n      this._doc = _doc;\n\n      this._init();\n    } // This is moved to its own method so that `MockPlatformLocationStrategy` can overwrite it\n\n    /** @internal */\n\n\n    _init() {\n      this.location = window.location;\n      this._history = window.history;\n    }\n\n    getBaseHrefFromDOM() {\n      return getDOM().getBaseHref(this._doc);\n    }\n\n    onPopState(fn) {\n      const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n      window.addEventListener('popstate', fn, false);\n      return () => window.removeEventListener('popstate', fn);\n    }\n\n    onHashChange(fn) {\n      const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n      window.addEventListener('hashchange', fn, false);\n      return () => window.removeEventListener('hashchange', fn);\n    }\n\n    get href() {\n      return this.location.href;\n    }\n\n    get protocol() {\n      return this.location.protocol;\n    }\n\n    get hostname() {\n      return this.location.hostname;\n    }\n\n    get port() {\n      return this.location.port;\n    }\n\n    get pathname() {\n      return this.location.pathname;\n    }\n\n    get search() {\n      return this.location.search;\n    }\n\n    get hash() {\n      return this.location.hash;\n    }\n\n    set pathname(newPath) {\n      this.location.pathname = newPath;\n    }\n\n    pushState(state, title, url) {\n      if (supportsState()) {\n        this._history.pushState(state, title, url);\n      } else {\n        this.location.hash = url;\n      }\n    }\n\n    replaceState(state, title, url) {\n      if (supportsState()) {\n        this._history.replaceState(state, title, url);\n      } else {\n        this.location.hash = url;\n      }\n    }\n\n    forward() {\n      this._history.forward();\n    }\n\n    back() {\n      this._history.back();\n    }\n\n    historyGo(relativePosition = 0) {\n      this._history.go(relativePosition);\n    }\n\n    getState() {\n      return this._history.state;\n    }\n\n  }\n\n  BrowserPlatformLocation.ɵfac = function BrowserPlatformLocation_Factory(t) {\n    return new (t || BrowserPlatformLocation)(i0.ɵɵinject(DOCUMENT));\n  };\n\n  BrowserPlatformLocation.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BrowserPlatformLocation,\n    factory: function () {\n      return createBrowserPlatformLocation();\n    },\n    providedIn: 'platform'\n  });\n  return BrowserPlatformLocation;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction supportsState() {\n  return !!window.history.pushState;\n}\n\nfunction createBrowserPlatformLocation() {\n  return new BrowserPlatformLocation(ɵɵinject(DOCUMENT));\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Joins two parts of a URL with a slash if needed.\n *\n * @param start  URL string\n * @param end    URL string\n *\n *\n * @returns The joined URL string.\n */\n\n\nfunction joinWithSlash(start, end) {\n  if (start.length == 0) {\n    return end;\n  }\n\n  if (end.length == 0) {\n    return start;\n  }\n\n  let slashes = 0;\n\n  if (start.endsWith('/')) {\n    slashes++;\n  }\n\n  if (end.startsWith('/')) {\n    slashes++;\n  }\n\n  if (slashes == 2) {\n    return start + end.substring(1);\n  }\n\n  if (slashes == 1) {\n    return start + end;\n  }\n\n  return start + '/' + end;\n}\n/**\n * Removes a trailing slash from a URL string if needed.\n * Looks for the first occurrence of either `#`, `?`, or the end of the\n * line as `/` characters and removes the trailing slash if one exists.\n *\n * @param url URL string.\n *\n * @returns The URL string, modified if needed.\n */\n\n\nfunction stripTrailingSlash(url) {\n  const match = url.match(/#|\\?|$/);\n  const pathEndIdx = match && match.index || url.length;\n  const droppedSlashIdx = pathEndIdx - (url[pathEndIdx - 1] === '/' ? 1 : 0);\n  return url.slice(0, droppedSlashIdx) + url.slice(pathEndIdx);\n}\n/**\n * Normalizes URL parameters by prepending with `?` if needed.\n *\n * @param  params String of URL parameters.\n *\n * @returns The normalized URL parameters string.\n */\n\n\nfunction normalizeQueryParams(params) {\n  return params && params[0] !== '?' ? '?' + params : params;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Enables the `Location` service to read route state from the browser's URL.\n * Angular provides two strategies:\n * `HashLocationStrategy` and `PathLocationStrategy`.\n *\n * Applications should use the `Router` or `Location` services to\n * interact with application route state.\n *\n * For instance, `HashLocationStrategy` produces URLs like\n * <code class=\"no-auto-link\">http://example.com#/foo</code>,\n * and `PathLocationStrategy` produces\n * <code class=\"no-auto-link\">http://example.com/foo</code> as an equivalent URL.\n *\n * See these two classes for more.\n *\n * @publicApi\n */\n\n\nlet LocationStrategy = /*#__PURE__*/(() => {\n  class LocationStrategy {\n    historyGo(relativePosition) {\n      throw new Error('Not implemented');\n    }\n\n  }\n\n  LocationStrategy.ɵfac = function LocationStrategy_Factory(t) {\n    return new (t || LocationStrategy)();\n  };\n\n  LocationStrategy.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LocationStrategy,\n    factory: function () {\n      return provideLocationStrategy();\n    },\n    providedIn: 'root'\n  });\n  return LocationStrategy;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction provideLocationStrategy(platformLocation) {\n  // See #23917\n  const location = ɵɵinject(DOCUMENT).location;\n  return new PathLocationStrategy(ɵɵinject(PlatformLocation), location && location.origin || '');\n}\n/**\n * A predefined [DI token](guide/glossary#di-token) for the base href\n * to be used with the `PathLocationStrategy`.\n * The base href is the URL prefix that should be preserved when generating\n * and recognizing URLs.\n *\n * @usageNotes\n *\n * The following example shows how to use this token to configure the root app injector\n * with a base href value, so that the DI framework can supply the dependency anywhere in the app.\n *\n * ```typescript\n * import {Component, NgModule} from '@angular/core';\n * import {APP_BASE_HREF} from '@angular/common';\n *\n * @NgModule({\n *   providers: [{provide: APP_BASE_HREF, useValue: '/my/app'}]\n * })\n * class AppModule {}\n * ```\n *\n * @publicApi\n */\n\n\nconst APP_BASE_HREF = /*#__PURE__*/new InjectionToken('appBaseHref');\n/**\n * @description\n * A {@link LocationStrategy} used to configure the {@link Location} service to\n * represent its state in the\n * [path](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax) of the\n * browser's URL.\n *\n * If you're using `PathLocationStrategy`, you must provide a {@link APP_BASE_HREF}\n * or add a `<base href>` element to the document.\n *\n * For instance, if you provide an `APP_BASE_HREF` of `'/my/app/'` and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`. To ensure all relative URIs resolve correctly,\n * the `<base href>` and/or `APP_BASE_HREF` should end with a `/`.\n *\n * Similarly, if you add `<base href='/my/app/'/>` to the document and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n *\n * Note that when using `PathLocationStrategy`, neither the query nor\n * the fragment in the `<base href>` will be preserved, as outlined\n * by the [RFC](https://tools.ietf.org/html/rfc3986#section-5.2.2).\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\n\nlet PathLocationStrategy = /*#__PURE__*/(() => {\n  class PathLocationStrategy extends LocationStrategy {\n    constructor(_platformLocation, href) {\n      super();\n      this._platformLocation = _platformLocation;\n      this._removeListenerFns = [];\n\n      if (href == null) {\n        href = this._platformLocation.getBaseHrefFromDOM();\n      }\n\n      if (href == null) {\n        throw new Error(`No base href set. Please provide a value for the APP_BASE_HREF token or add a base element to the document.`);\n      }\n\n      this._baseHref = href;\n    }\n\n    ngOnDestroy() {\n      while (this._removeListenerFns.length) {\n        this._removeListenerFns.pop()();\n      }\n    }\n\n    onPopState(fn) {\n      this._removeListenerFns.push(this._platformLocation.onPopState(fn), this._platformLocation.onHashChange(fn));\n    }\n\n    getBaseHref() {\n      return this._baseHref;\n    }\n\n    prepareExternalUrl(internal) {\n      return joinWithSlash(this._baseHref, internal);\n    }\n\n    path(includeHash = false) {\n      const pathname = this._platformLocation.pathname + normalizeQueryParams(this._platformLocation.search);\n      const hash = this._platformLocation.hash;\n      return hash && includeHash ? `${pathname}${hash}` : pathname;\n    }\n\n    pushState(state, title, url, queryParams) {\n      const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n\n      this._platformLocation.pushState(state, title, externalUrl);\n    }\n\n    replaceState(state, title, url, queryParams) {\n      const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n\n      this._platformLocation.replaceState(state, title, externalUrl);\n    }\n\n    forward() {\n      this._platformLocation.forward();\n    }\n\n    back() {\n      this._platformLocation.back();\n    }\n\n    historyGo(relativePosition = 0) {\n      var _a, _b;\n\n      (_b = (_a = this._platformLocation).historyGo) === null || _b === void 0 ? void 0 : _b.call(_a, relativePosition);\n    }\n\n  }\n\n  PathLocationStrategy.ɵfac = function PathLocationStrategy_Factory(t) {\n    return new (t || PathLocationStrategy)(i0.ɵɵinject(PlatformLocation), i0.ɵɵinject(APP_BASE_HREF, 8));\n  };\n\n  PathLocationStrategy.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PathLocationStrategy,\n    factory: PathLocationStrategy.ɵfac\n  });\n  return PathLocationStrategy;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @description\n * A {@link LocationStrategy} used to configure the {@link Location} service to\n * represent its state in the\n * [hash fragment](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax)\n * of the browser's URL.\n *\n * For instance, if you call `location.go('/foo')`, the browser's URL will become\n * `example.com#/foo`.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/location/ts/hash_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\n\n\nlet HashLocationStrategy = /*#__PURE__*/(() => {\n  class HashLocationStrategy extends LocationStrategy {\n    constructor(_platformLocation, _baseHref) {\n      super();\n      this._platformLocation = _platformLocation;\n      this._baseHref = '';\n      this._removeListenerFns = [];\n\n      if (_baseHref != null) {\n        this._baseHref = _baseHref;\n      }\n    }\n\n    ngOnDestroy() {\n      while (this._removeListenerFns.length) {\n        this._removeListenerFns.pop()();\n      }\n    }\n\n    onPopState(fn) {\n      this._removeListenerFns.push(this._platformLocation.onPopState(fn), this._platformLocation.onHashChange(fn));\n    }\n\n    getBaseHref() {\n      return this._baseHref;\n    }\n\n    path(includeHash = false) {\n      // the hash value is always prefixed with a `#`\n      // and if it is empty then it will stay empty\n      let path = this._platformLocation.hash;\n      if (path == null) path = '#';\n      return path.length > 0 ? path.substring(1) : path;\n    }\n\n    prepareExternalUrl(internal) {\n      const url = joinWithSlash(this._baseHref, internal);\n      return url.length > 0 ? '#' + url : url;\n    }\n\n    pushState(state, title, path, queryParams) {\n      let url = this.prepareExternalUrl(path + normalizeQueryParams(queryParams));\n\n      if (url.length == 0) {\n        url = this._platformLocation.pathname;\n      }\n\n      this._platformLocation.pushState(state, title, url);\n    }\n\n    replaceState(state, title, path, queryParams) {\n      let url = this.prepareExternalUrl(path + normalizeQueryParams(queryParams));\n\n      if (url.length == 0) {\n        url = this._platformLocation.pathname;\n      }\n\n      this._platformLocation.replaceState(state, title, url);\n    }\n\n    forward() {\n      this._platformLocation.forward();\n    }\n\n    back() {\n      this._platformLocation.back();\n    }\n\n    historyGo(relativePosition = 0) {\n      var _a, _b;\n\n      (_b = (_a = this._platformLocation).historyGo) === null || _b === void 0 ? void 0 : _b.call(_a, relativePosition);\n    }\n\n  }\n\n  HashLocationStrategy.ɵfac = function HashLocationStrategy_Factory(t) {\n    return new (t || HashLocationStrategy)(i0.ɵɵinject(PlatformLocation), i0.ɵɵinject(APP_BASE_HREF, 8));\n  };\n\n  HashLocationStrategy.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HashLocationStrategy,\n    factory: HashLocationStrategy.ɵfac\n  });\n  return HashLocationStrategy;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @description\n *\n * A service that applications can use to interact with a browser's URL.\n *\n * Depending on the `LocationStrategy` used, `Location` persists\n * to the URL's path or the URL's hash segment.\n *\n * @usageNotes\n *\n * It's better to use the `Router.navigate()` service to trigger route changes. Use\n * `Location` only if you need to interact with or create normalized URLs outside of\n * routing.\n *\n * `Location` is responsible for normalizing the URL against the application's base href.\n * A normalized URL is absolute from the URL host, includes the application's base href, and has no\n * trailing slash:\n * - `/my/app/user/123` is normalized\n * - `my/app/user/123` **is not** normalized\n * - `/my/app/user/123/` **is not** normalized\n *\n * ### Example\n *\n * <code-example path='common/location/ts/path_location_component.ts'\n * region='LocationComponent'></code-example>\n *\n * @publicApi\n */\n\n\nlet Location = /*#__PURE__*/(() => {\n  class Location {\n    constructor(platformStrategy, platformLocation) {\n      /** @internal */\n      this._subject = new EventEmitter();\n      /** @internal */\n\n      this._urlChangeListeners = [];\n      this._platformStrategy = platformStrategy;\n\n      const browserBaseHref = this._platformStrategy.getBaseHref();\n\n      this._platformLocation = platformLocation;\n      this._baseHref = stripTrailingSlash(_stripIndexHtml(browserBaseHref));\n\n      this._platformStrategy.onPopState(ev => {\n        this._subject.emit({\n          'url': this.path(true),\n          'pop': true,\n          'state': ev.state,\n          'type': ev.type\n        });\n      });\n    }\n    /**\n     * Normalizes the URL path for this location.\n     *\n     * @param includeHash True to include an anchor fragment in the path.\n     *\n     * @returns The normalized URL path.\n     */\n    // TODO: vsavkin. Remove the boolean flag and always include hash once the deprecated router is\n    // removed.\n\n\n    path(includeHash = false) {\n      return this.normalize(this._platformStrategy.path(includeHash));\n    }\n    /**\n     * Reports the current state of the location history.\n     * @returns The current value of the `history.state` object.\n     */\n\n\n    getState() {\n      return this._platformLocation.getState();\n    }\n    /**\n     * Normalizes the given path and compares to the current normalized path.\n     *\n     * @param path The given URL path.\n     * @param query Query parameters.\n     *\n     * @returns True if the given URL path is equal to the current normalized path, false\n     * otherwise.\n     */\n\n\n    isCurrentPathEqualTo(path, query = '') {\n      return this.path() == this.normalize(path + normalizeQueryParams(query));\n    }\n    /**\n     * Normalizes a URL path by stripping any trailing slashes.\n     *\n     * @param url String representing a URL.\n     *\n     * @returns The normalized URL string.\n     */\n\n\n    normalize(url) {\n      return Location.stripTrailingSlash(_stripBaseHref(this._baseHref, _stripIndexHtml(url)));\n    }\n    /**\n     * Normalizes an external URL path.\n     * If the given URL doesn't begin with a leading slash (`'/'`), adds one\n     * before normalizing. Adds a hash if `HashLocationStrategy` is\n     * in use, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n     *\n     * @param url String representing a URL.\n     *\n     * @returns  A normalized platform-specific URL.\n     */\n\n\n    prepareExternalUrl(url) {\n      if (url && url[0] !== '/') {\n        url = '/' + url;\n      }\n\n      return this._platformStrategy.prepareExternalUrl(url);\n    } // TODO: rename this method to pushState\n\n    /**\n     * Changes the browser's URL to a normalized version of a given URL, and pushes a\n     * new item onto the platform's history.\n     *\n     * @param path  URL path to normalize.\n     * @param query Query parameters.\n     * @param state Location history state.\n     *\n     */\n\n\n    go(path, query = '', state = null) {\n      this._platformStrategy.pushState(state, '', path, query);\n\n      this._notifyUrlChangeListeners(this.prepareExternalUrl(path + normalizeQueryParams(query)), state);\n    }\n    /**\n     * Changes the browser's URL to a normalized version of the given URL, and replaces\n     * the top item on the platform's history stack.\n     *\n     * @param path  URL path to normalize.\n     * @param query Query parameters.\n     * @param state Location history state.\n     */\n\n\n    replaceState(path, query = '', state = null) {\n      this._platformStrategy.replaceState(state, '', path, query);\n\n      this._notifyUrlChangeListeners(this.prepareExternalUrl(path + normalizeQueryParams(query)), state);\n    }\n    /**\n     * Navigates forward in the platform's history.\n     */\n\n\n    forward() {\n      this._platformStrategy.forward();\n    }\n    /**\n     * Navigates back in the platform's history.\n     */\n\n\n    back() {\n      this._platformStrategy.back();\n    }\n    /**\n     * Navigate to a specific page from session history, identified by its relative position to the\n     * current page.\n     *\n     * @param relativePosition  Position of the target page in the history relative to the current\n     *     page.\n     * A negative value moves backwards, a positive value moves forwards, e.g. `location.historyGo(2)`\n     * moves forward two pages and `location.historyGo(-2)` moves back two pages. When we try to go\n     * beyond what's stored in the history session, we stay in the current page. Same behaviour occurs\n     * when `relativePosition` equals 0.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/History_API#Moving_to_a_specific_point_in_history\n     */\n\n\n    historyGo(relativePosition = 0) {\n      var _a, _b;\n\n      (_b = (_a = this._platformStrategy).historyGo) === null || _b === void 0 ? void 0 : _b.call(_a, relativePosition);\n    }\n    /**\n     * Registers a URL change listener. Use to catch updates performed by the Angular\n     * framework that are not detectible through \"popstate\" or \"hashchange\" events.\n     *\n     * @param fn The change handler function, which take a URL and a location history state.\n     */\n\n\n    onUrlChange(fn) {\n      this._urlChangeListeners.push(fn);\n\n      if (!this._urlChangeSubscription) {\n        this._urlChangeSubscription = this.subscribe(v => {\n          this._notifyUrlChangeListeners(v.url, v.state);\n        });\n      }\n    }\n    /** @internal */\n\n\n    _notifyUrlChangeListeners(url = '', state) {\n      this._urlChangeListeners.forEach(fn => fn(url, state));\n    }\n    /**\n     * Subscribes to the platform's `popState` events.\n     *\n     * Note: `Location.go()` does not trigger the `popState` event in the browser. Use\n     * `Location.onUrlChange()` to subscribe to URL changes instead.\n     *\n     * @param value Event that is triggered when the state history changes.\n     * @param exception The exception to throw.\n     *\n     * @see [onpopstate](https://developer.mozilla.org/en-US/docs/Web/API/WindowEventHandlers/onpopstate)\n     *\n     * @returns Subscribed events.\n     */\n\n\n    subscribe(onNext, onThrow, onReturn) {\n      return this._subject.subscribe({\n        next: onNext,\n        error: onThrow,\n        complete: onReturn\n      });\n    }\n\n  }\n\n  /**\n   * Normalizes URL parameters by prepending with `?` if needed.\n   *\n   * @param  params String of URL parameters.\n   *\n   * @returns The normalized URL parameters string.\n   */\n  Location.normalizeQueryParams = normalizeQueryParams;\n  /**\n   * Joins two parts of a URL with a slash if needed.\n   *\n   * @param start  URL string\n   * @param end    URL string\n   *\n   *\n   * @returns The joined URL string.\n   */\n\n  Location.joinWithSlash = joinWithSlash;\n  /**\n   * Removes a trailing slash from a URL string if needed.\n   * Looks for the first occurrence of either `#`, `?`, or the end of the\n   * line as `/` characters and removes the trailing slash if one exists.\n   *\n   * @param url URL string.\n   *\n   * @returns The URL string, modified if needed.\n   */\n\n  Location.stripTrailingSlash = stripTrailingSlash;\n\n  Location.ɵfac = function Location_Factory(t) {\n    return new (t || Location)(i0.ɵɵinject(LocationStrategy), i0.ɵɵinject(PlatformLocation));\n  };\n\n  Location.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Location,\n    factory: function () {\n      return createLocation();\n    },\n    providedIn: 'root'\n  });\n  return Location;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction createLocation() {\n  return new Location(ɵɵinject(LocationStrategy), ɵɵinject(PlatformLocation));\n}\n\nfunction _stripBaseHref(baseHref, url) {\n  return baseHref && url.startsWith(baseHref) ? url.substring(baseHref.length) : url;\n}\n\nfunction _stripIndexHtml(url) {\n  return url.replace(/\\/index.html$/, '');\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** @internal */\n\n\nconst CURRENCIES_EN = {\n  \"ADP\": [undefined, undefined, 0],\n  \"AFN\": [undefined, \"؋\", 0],\n  \"ALL\": [undefined, undefined, 0],\n  \"AMD\": [undefined, \"֏\", 2],\n  \"AOA\": [undefined, \"Kz\"],\n  \"ARS\": [undefined, \"$\"],\n  \"AUD\": [\"A$\", \"$\"],\n  \"AZN\": [undefined, \"₼\"],\n  \"BAM\": [undefined, \"KM\"],\n  \"BBD\": [undefined, \"$\"],\n  \"BDT\": [undefined, \"৳\"],\n  \"BHD\": [undefined, undefined, 3],\n  \"BIF\": [undefined, undefined, 0],\n  \"BMD\": [undefined, \"$\"],\n  \"BND\": [undefined, \"$\"],\n  \"BOB\": [undefined, \"Bs\"],\n  \"BRL\": [\"R$\"],\n  \"BSD\": [undefined, \"$\"],\n  \"BWP\": [undefined, \"P\"],\n  \"BYN\": [undefined, \"р.\", 2],\n  \"BYR\": [undefined, undefined, 0],\n  \"BZD\": [undefined, \"$\"],\n  \"CAD\": [\"CA$\", \"$\", 2],\n  \"CHF\": [undefined, undefined, 2],\n  \"CLF\": [undefined, undefined, 4],\n  \"CLP\": [undefined, \"$\", 0],\n  \"CNY\": [\"CN¥\", \"¥\"],\n  \"COP\": [undefined, \"$\", 2],\n  \"CRC\": [undefined, \"₡\", 2],\n  \"CUC\": [undefined, \"$\"],\n  \"CUP\": [undefined, \"$\"],\n  \"CZK\": [undefined, \"Kč\", 2],\n  \"DJF\": [undefined, undefined, 0],\n  \"DKK\": [undefined, \"kr\", 2],\n  \"DOP\": [undefined, \"$\"],\n  \"EGP\": [undefined, \"E£\"],\n  \"ESP\": [undefined, \"₧\", 0],\n  \"EUR\": [\"€\"],\n  \"FJD\": [undefined, \"$\"],\n  \"FKP\": [undefined, \"£\"],\n  \"GBP\": [\"£\"],\n  \"GEL\": [undefined, \"₾\"],\n  \"GHS\": [undefined, \"GH₵\"],\n  \"GIP\": [undefined, \"£\"],\n  \"GNF\": [undefined, \"FG\", 0],\n  \"GTQ\": [undefined, \"Q\"],\n  \"GYD\": [undefined, \"$\", 2],\n  \"HKD\": [\"HK$\", \"$\"],\n  \"HNL\": [undefined, \"L\"],\n  \"HRK\": [undefined, \"kn\"],\n  \"HUF\": [undefined, \"Ft\", 2],\n  \"IDR\": [undefined, \"Rp\", 2],\n  \"ILS\": [\"₪\"],\n  \"INR\": [\"₹\"],\n  \"IQD\": [undefined, undefined, 0],\n  \"IRR\": [undefined, undefined, 0],\n  \"ISK\": [undefined, \"kr\", 0],\n  \"ITL\": [undefined, undefined, 0],\n  \"JMD\": [undefined, \"$\"],\n  \"JOD\": [undefined, undefined, 3],\n  \"JPY\": [\"¥\", undefined, 0],\n  \"KHR\": [undefined, \"៛\"],\n  \"KMF\": [undefined, \"CF\", 0],\n  \"KPW\": [undefined, \"₩\", 0],\n  \"KRW\": [\"₩\", undefined, 0],\n  \"KWD\": [undefined, undefined, 3],\n  \"KYD\": [undefined, \"$\"],\n  \"KZT\": [undefined, \"₸\"],\n  \"LAK\": [undefined, \"₭\", 0],\n  \"LBP\": [undefined, \"L£\", 0],\n  \"LKR\": [undefined, \"Rs\"],\n  \"LRD\": [undefined, \"$\"],\n  \"LTL\": [undefined, \"Lt\"],\n  \"LUF\": [undefined, undefined, 0],\n  \"LVL\": [undefined, \"Ls\"],\n  \"LYD\": [undefined, undefined, 3],\n  \"MGA\": [undefined, \"Ar\", 0],\n  \"MGF\": [undefined, undefined, 0],\n  \"MMK\": [undefined, \"K\", 0],\n  \"MNT\": [undefined, \"₮\", 2],\n  \"MRO\": [undefined, undefined, 0],\n  \"MUR\": [undefined, \"Rs\", 2],\n  \"MXN\": [\"MX$\", \"$\"],\n  \"MYR\": [undefined, \"RM\"],\n  \"NAD\": [undefined, \"$\"],\n  \"NGN\": [undefined, \"₦\"],\n  \"NIO\": [undefined, \"C$\"],\n  \"NOK\": [undefined, \"kr\", 2],\n  \"NPR\": [undefined, \"Rs\"],\n  \"NZD\": [\"NZ$\", \"$\"],\n  \"OMR\": [undefined, undefined, 3],\n  \"PHP\": [\"₱\"],\n  \"PKR\": [undefined, \"Rs\", 2],\n  \"PLN\": [undefined, \"zł\"],\n  \"PYG\": [undefined, \"₲\", 0],\n  \"RON\": [undefined, \"lei\"],\n  \"RSD\": [undefined, undefined, 0],\n  \"RUB\": [undefined, \"₽\"],\n  \"RUR\": [undefined, \"р.\"],\n  \"RWF\": [undefined, \"RF\", 0],\n  \"SBD\": [undefined, \"$\"],\n  \"SEK\": [undefined, \"kr\", 2],\n  \"SGD\": [undefined, \"$\"],\n  \"SHP\": [undefined, \"£\"],\n  \"SLL\": [undefined, undefined, 0],\n  \"SOS\": [undefined, undefined, 0],\n  \"SRD\": [undefined, \"$\"],\n  \"SSP\": [undefined, \"£\"],\n  \"STD\": [undefined, undefined, 0],\n  \"STN\": [undefined, \"Db\"],\n  \"SYP\": [undefined, \"£\", 0],\n  \"THB\": [undefined, \"฿\"],\n  \"TMM\": [undefined, undefined, 0],\n  \"TND\": [undefined, undefined, 3],\n  \"TOP\": [undefined, \"T$\"],\n  \"TRL\": [undefined, undefined, 0],\n  \"TRY\": [undefined, \"₺\"],\n  \"TTD\": [undefined, \"$\"],\n  \"TWD\": [\"NT$\", \"$\", 2],\n  \"TZS\": [undefined, undefined, 2],\n  \"UAH\": [undefined, \"₴\"],\n  \"UGX\": [undefined, undefined, 0],\n  \"USD\": [\"$\"],\n  \"UYI\": [undefined, undefined, 0],\n  \"UYU\": [undefined, \"$\"],\n  \"UYW\": [undefined, undefined, 4],\n  \"UZS\": [undefined, undefined, 2],\n  \"VEF\": [undefined, \"Bs\", 2],\n  \"VND\": [\"₫\", undefined, 0],\n  \"VUV\": [undefined, undefined, 0],\n  \"XAF\": [\"FCFA\", undefined, 0],\n  \"XCD\": [\"EC$\", \"$\"],\n  \"XOF\": [\"F CFA\", undefined, 0],\n  \"XPF\": [\"CFPF\", undefined, 0],\n  \"XXX\": [\"¤\"],\n  \"YER\": [undefined, undefined, 0],\n  \"ZAR\": [undefined, \"R\"],\n  \"ZMK\": [undefined, undefined, 0],\n  \"ZMW\": [undefined, \"ZK\"],\n  \"ZWD\": [undefined, undefined, 0]\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Format styles that can be used to represent numbers.\n * @see `getLocaleNumberFormat()`.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\nvar NumberFormatStyle = /*#__PURE__*/(() => {\n  NumberFormatStyle = NumberFormatStyle || {};\n  NumberFormatStyle[NumberFormatStyle[\"Decimal\"] = 0] = \"Decimal\";\n  NumberFormatStyle[NumberFormatStyle[\"Percent\"] = 1] = \"Percent\";\n  NumberFormatStyle[NumberFormatStyle[\"Currency\"] = 2] = \"Currency\";\n  NumberFormatStyle[NumberFormatStyle[\"Scientific\"] = 3] = \"Scientific\";\n  return NumberFormatStyle;\n})();\n\n/**\n * Plurality cases used for translating plurals to different languages.\n *\n * @see `NgPlural`\n * @see `NgPluralCase`\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\nvar Plural = /*#__PURE__*/(() => {\n  Plural = Plural || {};\n  Plural[Plural[\"Zero\"] = 0] = \"Zero\";\n  Plural[Plural[\"One\"] = 1] = \"One\";\n  Plural[Plural[\"Two\"] = 2] = \"Two\";\n  Plural[Plural[\"Few\"] = 3] = \"Few\";\n  Plural[Plural[\"Many\"] = 4] = \"Many\";\n  Plural[Plural[\"Other\"] = 5] = \"Other\";\n  return Plural;\n})();\n\n/**\n * Context-dependant translation forms for strings.\n * Typically the standalone version is for the nominative form of the word,\n * and the format version is used for the genitive case.\n * @see [CLDR website](http://cldr.unicode.org/translation/date-time-1/date-time#TOC-Standalone-vs.-Format-Styles)\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\nvar FormStyle = /*#__PURE__*/(() => {\n  FormStyle = FormStyle || {};\n  FormStyle[FormStyle[\"Format\"] = 0] = \"Format\";\n  FormStyle[FormStyle[\"Standalone\"] = 1] = \"Standalone\";\n  return FormStyle;\n})();\n\n/**\n * String widths available for translations.\n * The specific character widths are locale-specific.\n * Examples are given for the word \"Sunday\" in English.\n *\n * @publicApi\n */\nvar TranslationWidth = /*#__PURE__*/(() => {\n  TranslationWidth = TranslationWidth || {};\n\n  /** 1 character for `en-US`. For example: 'S' */\n  TranslationWidth[TranslationWidth[\"Narrow\"] = 0] = \"Narrow\";\n  /** 3 characters for `en-US`. For example: 'Sun' */\n\n  TranslationWidth[TranslationWidth[\"Abbreviated\"] = 1] = \"Abbreviated\";\n  /** Full length for `en-US`. For example: \"Sunday\" */\n\n  TranslationWidth[TranslationWidth[\"Wide\"] = 2] = \"Wide\";\n  /** 2 characters for `en-US`, For example: \"Su\" */\n\n  TranslationWidth[TranslationWidth[\"Short\"] = 3] = \"Short\";\n  return TranslationWidth;\n})();\n\n/**\n * String widths available for date-time formats.\n * The specific character widths are locale-specific.\n * Examples are given for `en-US`.\n *\n * @see `getLocaleDateFormat()`\n * @see `getLocaleTimeFormat()`\n * @see `getLocaleDateTimeFormat()`\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n * @publicApi\n */\nvar FormatWidth = /*#__PURE__*/(() => {\n  FormatWidth = FormatWidth || {};\n\n  /**\n   * For `en-US`, 'M/d/yy, h:mm a'`\n   * (Example: `6/15/15, 9:03 AM`)\n   */\n  FormatWidth[FormatWidth[\"Short\"] = 0] = \"Short\";\n  /**\n   * For `en-US`, `'MMM d, y, h:mm:ss a'`\n   * (Example: `Jun 15, 2015, 9:03:01 AM`)\n   */\n\n  FormatWidth[FormatWidth[\"Medium\"] = 1] = \"Medium\";\n  /**\n   * For `en-US`, `'MMMM d, y, h:mm:ss a z'`\n   * (Example: `June 15, 2015 at 9:03:01 AM GMT+1`)\n   */\n\n  FormatWidth[FormatWidth[\"Long\"] = 2] = \"Long\";\n  /**\n   * For `en-US`, `'EEEE, MMMM d, y, h:mm:ss a zzzz'`\n   * (Example: `Monday, June 15, 2015 at 9:03:01 AM GMT+01:00`)\n   */\n\n  FormatWidth[FormatWidth[\"Full\"] = 3] = \"Full\";\n  return FormatWidth;\n})();\n\n/**\n * Symbols that can be used to replace placeholders in number patterns.\n * Examples are based on `en-US` values.\n *\n * @see `getLocaleNumberSymbol()`\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\nvar NumberSymbol = /*#__PURE__*/(() => {\n  NumberSymbol = NumberSymbol || {};\n\n  /**\n   * Decimal separator.\n   * For `en-US`, the dot character.\n   * Example: 2,345`.`67\n   */\n  NumberSymbol[NumberSymbol[\"Decimal\"] = 0] = \"Decimal\";\n  /**\n   * Grouping separator, typically for thousands.\n   * For `en-US`, the comma character.\n   * Example: 2`,`345.67\n   */\n\n  NumberSymbol[NumberSymbol[\"Group\"] = 1] = \"Group\";\n  /**\n   * List-item separator.\n   * Example: \"one, two, and three\"\n   */\n\n  NumberSymbol[NumberSymbol[\"List\"] = 2] = \"List\";\n  /**\n   * Sign for percentage (out of 100).\n   * Example: 23.4%\n   */\n\n  NumberSymbol[NumberSymbol[\"PercentSign\"] = 3] = \"PercentSign\";\n  /**\n   * Sign for positive numbers.\n   * Example: +23\n   */\n\n  NumberSymbol[NumberSymbol[\"PlusSign\"] = 4] = \"PlusSign\";\n  /**\n   * Sign for negative numbers.\n   * Example: -23\n   */\n\n  NumberSymbol[NumberSymbol[\"MinusSign\"] = 5] = \"MinusSign\";\n  /**\n   * Computer notation for exponential value (n times a power of 10).\n   * Example: 1.2E3\n   */\n\n  NumberSymbol[NumberSymbol[\"Exponential\"] = 6] = \"Exponential\";\n  /**\n   * Human-readable format of exponential.\n   * Example: 1.2x103\n   */\n\n  NumberSymbol[NumberSymbol[\"SuperscriptingExponent\"] = 7] = \"SuperscriptingExponent\";\n  /**\n   * Sign for permille (out of 1000).\n   * Example: 23.4‰\n   */\n\n  NumberSymbol[NumberSymbol[\"PerMille\"] = 8] = \"PerMille\";\n  /**\n   * Infinity, can be used with plus and minus.\n   * Example: ∞, +∞, -∞\n   */\n\n  NumberSymbol[NumberSymbol[\"Infinity\"] = 9] = \"Infinity\";\n  /**\n   * Not a number.\n   * Example: NaN\n   */\n\n  NumberSymbol[NumberSymbol[\"NaN\"] = 10] = \"NaN\";\n  /**\n   * Symbol used between time units.\n   * Example: 10:52\n   */\n\n  NumberSymbol[NumberSymbol[\"TimeSeparator\"] = 11] = \"TimeSeparator\";\n  /**\n   * Decimal separator for currency values (fallback to `Decimal`).\n   * Example: $2,345.67\n   */\n\n  NumberSymbol[NumberSymbol[\"CurrencyDecimal\"] = 12] = \"CurrencyDecimal\";\n  /**\n   * Group separator for currency values (fallback to `Group`).\n   * Example: $2,345.67\n   */\n\n  NumberSymbol[NumberSymbol[\"CurrencyGroup\"] = 13] = \"CurrencyGroup\";\n  return NumberSymbol;\n})();\n\n/**\n * The value for each day of the week, based on the `en-US` locale\n *\n * @publicApi\n */\nvar WeekDay = /*#__PURE__*/(() => {\n  WeekDay = WeekDay || {};\n  WeekDay[WeekDay[\"Sunday\"] = 0] = \"Sunday\";\n  WeekDay[WeekDay[\"Monday\"] = 1] = \"Monday\";\n  WeekDay[WeekDay[\"Tuesday\"] = 2] = \"Tuesday\";\n  WeekDay[WeekDay[\"Wednesday\"] = 3] = \"Wednesday\";\n  WeekDay[WeekDay[\"Thursday\"] = 4] = \"Thursday\";\n  WeekDay[WeekDay[\"Friday\"] = 5] = \"Friday\";\n  WeekDay[WeekDay[\"Saturday\"] = 6] = \"Saturday\";\n  return WeekDay;\n})();\n\n/**\n * Retrieves the locale ID from the currently loaded locale.\n * The loaded locale could be, for example, a global one rather than a regional one.\n * @param locale A locale code, such as `fr-FR`.\n * @returns The locale code. For example, `fr`.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleId(locale) {\n  return ɵfindLocaleData(locale)[ɵLocaleDataIndex.LocaleId];\n}\n/**\n * Retrieves day period strings for the given locale.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns An array of localized period strings. For example, `[AM, PM]` for `en-US`.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleDayPeriods(locale, formStyle, width) {\n  const data = ɵfindLocaleData(locale);\n  const amPmData = [data[ɵLocaleDataIndex.DayPeriodsFormat], data[ɵLocaleDataIndex.DayPeriodsStandalone]];\n  const amPm = getLastDefinedValue(amPmData, formStyle);\n  return getLastDefinedValue(amPm, width);\n}\n/**\n * Retrieves days of the week for the given locale, using the Gregorian calendar.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns An array of localized name strings.\n * For example,`[Sunday, Monday, ... Saturday]` for `en-US`.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleDayNames(locale, formStyle, width) {\n  const data = ɵfindLocaleData(locale);\n  const daysData = [data[ɵLocaleDataIndex.DaysFormat], data[ɵLocaleDataIndex.DaysStandalone]];\n  const days = getLastDefinedValue(daysData, formStyle);\n  return getLastDefinedValue(days, width);\n}\n/**\n * Retrieves months of the year for the given locale, using the Gregorian calendar.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns An array of localized name strings.\n * For example,  `[January, February, ...]` for `en-US`.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleMonthNames(locale, formStyle, width) {\n  const data = ɵfindLocaleData(locale);\n  const monthsData = [data[ɵLocaleDataIndex.MonthsFormat], data[ɵLocaleDataIndex.MonthsStandalone]];\n  const months = getLastDefinedValue(monthsData, formStyle);\n  return getLastDefinedValue(months, width);\n}\n/**\n * Retrieves Gregorian-calendar eras for the given locale.\n * @param locale A locale code for the locale format rules to use.\n * @param width The required character width.\n\n * @returns An array of localized era strings.\n * For example, `[AD, BC]` for `en-US`.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleEraNames(locale, width) {\n  const data = ɵfindLocaleData(locale);\n  const erasData = data[ɵLocaleDataIndex.Eras];\n  return getLastDefinedValue(erasData, width);\n}\n/**\n * Retrieves the first day of the week for the given locale.\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns A day index number, using the 0-based week-day index for `en-US`\n * (Sunday = 0, Monday = 1, ...).\n * For example, for `fr-FR`, returns 1 to indicate that the first day is Monday.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleFirstDayOfWeek(locale) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.FirstDayOfWeek];\n}\n/**\n * Range of week days that are considered the week-end for the given locale.\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns The range of day values, `[startDay, endDay]`.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleWeekEndRange(locale) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.WeekendRange];\n}\n/**\n * Retrieves a localized date-value formating string.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param width The format type.\n * @returns The localized formating string.\n * @see `FormatWidth`\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleDateFormat(locale, width) {\n  const data = ɵfindLocaleData(locale);\n  return getLastDefinedValue(data[ɵLocaleDataIndex.DateFormat], width);\n}\n/**\n * Retrieves a localized time-value formatting string.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param width The format type.\n * @returns The localized formatting string.\n * @see `FormatWidth`\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n\n * @publicApi\n */\n\n\nfunction getLocaleTimeFormat(locale, width) {\n  const data = ɵfindLocaleData(locale);\n  return getLastDefinedValue(data[ɵLocaleDataIndex.TimeFormat], width);\n}\n/**\n * Retrieves a localized date-time formatting string.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param width The format type.\n * @returns The localized formatting string.\n * @see `FormatWidth`\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleDateTimeFormat(locale, width) {\n  const data = ɵfindLocaleData(locale);\n  const dateTimeFormatData = data[ɵLocaleDataIndex.DateTimeFormat];\n  return getLastDefinedValue(dateTimeFormatData, width);\n}\n/**\n * Retrieves a localized number symbol that can be used to replace placeholders in number formats.\n * @param locale The locale code.\n * @param symbol The symbol to localize.\n * @returns The character for the localized symbol.\n * @see `NumberSymbol`\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleNumberSymbol(locale, symbol) {\n  const data = ɵfindLocaleData(locale);\n  const res = data[ɵLocaleDataIndex.NumberSymbols][symbol];\n\n  if (typeof res === 'undefined') {\n    if (symbol === NumberSymbol.CurrencyDecimal) {\n      return data[ɵLocaleDataIndex.NumberSymbols][NumberSymbol.Decimal];\n    } else if (symbol === NumberSymbol.CurrencyGroup) {\n      return data[ɵLocaleDataIndex.NumberSymbols][NumberSymbol.Group];\n    }\n  }\n\n  return res;\n}\n/**\n * Retrieves a number format for a given locale.\n *\n * Numbers are formatted using patterns, like `#,###.00`. For example, the pattern `#,###.00`\n * when used to format the number 12345.678 could result in \"12'345,678\". That would happen if the\n * grouping separator for your language is an apostrophe, and the decimal separator is a comma.\n *\n * <b>Important:</b> The characters `.` `,` `0` `#` (and others below) are special placeholders\n * that stand for the decimal separator, and so on, and are NOT real characters.\n * You must NOT \"translate\" the placeholders. For example, don't change `.` to `,` even though in\n * your language the decimal point is written with a comma. The symbols should be replaced by the\n * local equivalents, using the appropriate `NumberSymbol` for your language.\n *\n * Here are the special characters used in number patterns:\n *\n * | Symbol | Meaning |\n * |--------|---------|\n * | . | Replaced automatically by the character used for the decimal point. |\n * | , | Replaced by the \"grouping\" (thousands) separator. |\n * | 0 | Replaced by a digit (or zero if there aren't enough digits). |\n * | # | Replaced by a digit (or nothing if there aren't enough). |\n * | ¤ | Replaced by a currency symbol, such as $ or USD. |\n * | % | Marks a percent format. The % symbol may change position, but must be retained. |\n * | E | Marks a scientific format. The E symbol may change position, but must be retained. |\n * | ' | Special characters used as literal characters are quoted with ASCII single quotes. |\n *\n * @param locale A locale code for the locale format rules to use.\n * @param type The type of numeric value to be formatted (such as `Decimal` or `Currency`.)\n * @returns The localized format string.\n * @see `NumberFormatStyle`\n * @see [CLDR website](http://cldr.unicode.org/translation/number-patterns)\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleNumberFormat(locale, type) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.NumberFormats][type];\n}\n/**\n * Retrieves the symbol used to represent the currency for the main country\n * corresponding to a given locale. For example, '$' for `en-US`.\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns The localized symbol character,\n * or `null` if the main country cannot be determined.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleCurrencySymbol(locale) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.CurrencySymbol] || null;\n}\n/**\n * Retrieves the name of the currency for the main country corresponding\n * to a given locale. For example, 'US Dollar' for `en-US`.\n * @param locale A locale code for the locale format rules to use.\n * @returns The currency name,\n * or `null` if the main country cannot be determined.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleCurrencyName(locale) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.CurrencyName] || null;\n}\n/**\n * Retrieves the default currency code for the given locale.\n *\n * The default is defined as the first currency which is still in use.\n *\n * @param locale The code of the locale whose currency code we want.\n * @returns The code of the default currency for the given locale.\n *\n * @publicApi\n */\n\n\nfunction getLocaleCurrencyCode(locale) {\n  return ɵgetLocaleCurrencyCode(locale);\n}\n/**\n * Retrieves the currency values for a given locale.\n * @param locale A locale code for the locale format rules to use.\n * @returns The currency values.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n */\n\n\nfunction getLocaleCurrencies(locale) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.Currencies];\n}\n/**\n * @alias core/ɵgetLocalePluralCase\n * @publicApi\n */\n\n\nconst getLocalePluralCase = ɵgetLocalePluralCase;\n\nfunction checkFullData(data) {\n  if (!data[ɵLocaleDataIndex.ExtraData]) {\n    throw new Error(`Missing extra locale data for the locale \"${data[ɵLocaleDataIndex.LocaleId]}\". Use \"registerLocaleData\" to load new data. See the \"I18n guide\" on angular.io to know more.`);\n  }\n}\n/**\n * Retrieves locale-specific rules used to determine which day period to use\n * when more than one period is defined for a locale.\n *\n * There is a rule for each defined day period. The\n * first rule is applied to the first day period and so on.\n * Fall back to AM/PM when no rules are available.\n *\n * A rule can specify a period as time range, or as a single time value.\n *\n * This functionality is only available when you have loaded the full locale data.\n * See the [\"I18n guide\"](guide/i18n-common-format-data-locale).\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns The rules for the locale, a single time value or array of *from-time, to-time*,\n * or null if no periods are available.\n *\n * @see `getLocaleExtraDayPeriods()`\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleExtraDayPeriodRules(locale) {\n  const data = ɵfindLocaleData(locale);\n  checkFullData(data);\n  const rules = data[ɵLocaleDataIndex.ExtraData][2\n  /* ExtraDayPeriodsRules */\n  ] || [];\n  return rules.map(rule => {\n    if (typeof rule === 'string') {\n      return extractTime(rule);\n    }\n\n    return [extractTime(rule[0]), extractTime(rule[1])];\n  });\n}\n/**\n * Retrieves locale-specific day periods, which indicate roughly how a day is broken up\n * in different languages.\n * For example, for `en-US`, periods are morning, noon, afternoon, evening, and midnight.\n *\n * This functionality is only available when you have loaded the full locale data.\n * See the [\"I18n guide\"](guide/i18n-common-format-data-locale).\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns The translated day-period strings.\n * @see `getLocaleExtraDayPeriodRules()`\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLocaleExtraDayPeriods(locale, formStyle, width) {\n  const data = ɵfindLocaleData(locale);\n  checkFullData(data);\n  const dayPeriodsData = [data[ɵLocaleDataIndex.ExtraData][0\n  /* ExtraDayPeriodFormats */\n  ], data[ɵLocaleDataIndex.ExtraData][1\n  /* ExtraDayPeriodStandalone */\n  ]];\n  const dayPeriods = getLastDefinedValue(dayPeriodsData, formStyle) || [];\n  return getLastDefinedValue(dayPeriods, width) || [];\n}\n/**\n * Retrieves the writing direction of a specified locale\n * @param locale A locale code for the locale format rules to use.\n * @publicApi\n * @returns 'rtl' or 'ltr'\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n */\n\n\nfunction getLocaleDirection(locale) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.Directionality];\n}\n/**\n * Retrieves the first value that is defined in an array, going backwards from an index position.\n *\n * To avoid repeating the same data (as when the \"format\" and \"standalone\" forms are the same)\n * add the first value to the locale data arrays, and add other values only if they are different.\n *\n * @param data The data array to retrieve from.\n * @param index A 0-based index into the array to start from.\n * @returns The value immediately before the given index position.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getLastDefinedValue(data, index) {\n  for (let i = index; i > -1; i--) {\n    if (typeof data[i] !== 'undefined') {\n      return data[i];\n    }\n  }\n\n  throw new Error('Locale data API: locale data undefined');\n}\n/**\n * Extracts the hours and minutes from a string like \"15:45\"\n */\n\n\nfunction extractTime(time) {\n  const [h, m] = time.split(':');\n  return {\n    hours: +h,\n    minutes: +m\n  };\n}\n/**\n * Retrieves the currency symbol for a given currency code.\n *\n * For example, for the default `en-US` locale, the code `USD` can\n * be represented by the narrow symbol `$` or the wide symbol `US$`.\n *\n * @param code The currency code.\n * @param format The format, `wide` or `narrow`.\n * @param locale A locale code for the locale format rules to use.\n *\n * @returns The symbol, or the currency code if no symbol is available.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction getCurrencySymbol(code, format, locale = 'en') {\n  const currency = getLocaleCurrencies(locale)[code] || CURRENCIES_EN[code] || [];\n  const symbolNarrow = currency[1\n  /* SymbolNarrow */\n  ];\n\n  if (format === 'narrow' && typeof symbolNarrow === 'string') {\n    return symbolNarrow;\n  }\n\n  return currency[0\n  /* Symbol */\n  ] || code;\n} // Most currencies have cents, that's why the default is 2\n\n\nconst DEFAULT_NB_OF_CURRENCY_DIGITS = 2;\n/**\n * Reports the number of decimal digits for a given currency.\n * The value depends upon the presence of cents in that particular currency.\n *\n * @param code The currency code.\n * @returns The number of decimal digits, typically 0 or 2.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\nfunction getNumberOfCurrencyDigits(code) {\n  let digits;\n  const currency = CURRENCIES_EN[code];\n\n  if (currency) {\n    digits = currency[2\n    /* NbOfDigits */\n    ];\n  }\n\n  return typeof digits === 'number' ? digits : DEFAULT_NB_OF_CURRENCY_DIGITS;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst ISO8601_DATE_REGEX = /^(\\d{4})-?(\\d\\d)-?(\\d\\d)(?:T(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:\\.(\\d+))?)?)?(Z|([+-])(\\d\\d):?(\\d\\d))?)?$/; //    1        2       3         4          5          6          7          8  9     10      11\n\nconst NAMED_FORMATS = {};\nconst DATE_FORMATS_SPLIT = /((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\\s\\S]*)/;\nvar ZoneWidth = /*#__PURE__*/(() => {\n  ZoneWidth = ZoneWidth || {};\n  ZoneWidth[ZoneWidth[\"Short\"] = 0] = \"Short\";\n  ZoneWidth[ZoneWidth[\"ShortGMT\"] = 1] = \"ShortGMT\";\n  ZoneWidth[ZoneWidth[\"Long\"] = 2] = \"Long\";\n  ZoneWidth[ZoneWidth[\"Extended\"] = 3] = \"Extended\";\n  return ZoneWidth;\n})();\nvar DateType = /*#__PURE__*/(() => {\n  DateType = DateType || {};\n  DateType[DateType[\"FullYear\"] = 0] = \"FullYear\";\n  DateType[DateType[\"Month\"] = 1] = \"Month\";\n  DateType[DateType[\"Date\"] = 2] = \"Date\";\n  DateType[DateType[\"Hours\"] = 3] = \"Hours\";\n  DateType[DateType[\"Minutes\"] = 4] = \"Minutes\";\n  DateType[DateType[\"Seconds\"] = 5] = \"Seconds\";\n  DateType[DateType[\"FractionalSeconds\"] = 6] = \"FractionalSeconds\";\n  DateType[DateType[\"Day\"] = 7] = \"Day\";\n  return DateType;\n})();\nvar TranslationType = /*#__PURE__*/(() => {\n  TranslationType = TranslationType || {};\n  TranslationType[TranslationType[\"DayPeriods\"] = 0] = \"DayPeriods\";\n  TranslationType[TranslationType[\"Days\"] = 1] = \"Days\";\n  TranslationType[TranslationType[\"Months\"] = 2] = \"Months\";\n  TranslationType[TranslationType[\"Eras\"] = 3] = \"Eras\";\n  return TranslationType;\n})();\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a date according to locale rules.\n *\n * @param value The date to format, as a Date, or a number (milliseconds since UTC epoch)\n * or an [ISO date-time string](https://www.w3.org/TR/NOTE-datetime).\n * @param format The date-time components to include. See `DatePipe` for details.\n * @param locale A locale code for the locale format rules to use.\n * @param timezone The time zone. A time zone offset from GMT (such as `'+0430'`),\n * or a standard UTC/GMT or continental US time zone abbreviation.\n * If not specified, uses host system settings.\n *\n * @returns The formatted date string.\n *\n * @see `DatePipe`\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction formatDate(value, format, locale, timezone) {\n  let date = toDate(value);\n  const namedFormat = getNamedFormat(locale, format);\n  format = namedFormat || format;\n  let parts = [];\n  let match;\n\n  while (format) {\n    match = DATE_FORMATS_SPLIT.exec(format);\n\n    if (match) {\n      parts = parts.concat(match.slice(1));\n      const part = parts.pop();\n\n      if (!part) {\n        break;\n      }\n\n      format = part;\n    } else {\n      parts.push(format);\n      break;\n    }\n  }\n\n  let dateTimezoneOffset = date.getTimezoneOffset();\n\n  if (timezone) {\n    dateTimezoneOffset = timezoneToOffset(timezone, dateTimezoneOffset);\n    date = convertTimezoneToLocal(date, timezone, true);\n  }\n\n  let text = '';\n  parts.forEach(value => {\n    const dateFormatter = getDateFormatter(value);\n    text += dateFormatter ? dateFormatter(date, locale, dateTimezoneOffset) : value === '\\'\\'' ? '\\'' : value.replace(/(^'|'$)/g, '').replace(/''/g, '\\'');\n  });\n  return text;\n}\n/**\n * Create a new Date object with the given date value, and the time set to midnight.\n *\n * We cannot use `new Date(year, month, date)` because it maps years between 0 and 99 to 1900-1999.\n * See: https://github.com/angular/angular/issues/40377\n *\n * Note that this function returns a Date object whose time is midnight in the current locale's\n * timezone. In the future we might want to change this to be midnight in UTC, but this would be a\n * considerable breaking change.\n */\n\n\nfunction createDate(year, month, date) {\n  // The `newDate` is set to midnight (UTC) on January 1st 1970.\n  // - In PST this will be December 31st 1969 at 4pm.\n  // - In GMT this will be January 1st 1970 at 1am.\n  // Note that they even have different years, dates and months!\n  const newDate = new Date(0); // `setFullYear()` allows years like 0001 to be set correctly. This function does not\n  // change the internal time of the date.\n  // Consider calling `setFullYear(2019, 8, 20)` (September 20, 2019).\n  // - In PST this will now be September 20, 2019 at 4pm\n  // - In GMT this will now be September 20, 2019 at 1am\n\n  newDate.setFullYear(year, month, date); // We want the final date to be at local midnight, so we reset the time.\n  // - In PST this will now be September 20, 2019 at 12am\n  // - In GMT this will now be September 20, 2019 at 12am\n\n  newDate.setHours(0, 0, 0);\n  return newDate;\n}\n\nfunction getNamedFormat(locale, format) {\n  const localeId = getLocaleId(locale);\n  NAMED_FORMATS[localeId] = NAMED_FORMATS[localeId] || {};\n\n  if (NAMED_FORMATS[localeId][format]) {\n    return NAMED_FORMATS[localeId][format];\n  }\n\n  let formatValue = '';\n\n  switch (format) {\n    case 'shortDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Short);\n      break;\n\n    case 'mediumDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Medium);\n      break;\n\n    case 'longDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Long);\n      break;\n\n    case 'fullDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Full);\n      break;\n\n    case 'shortTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Short);\n      break;\n\n    case 'mediumTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Medium);\n      break;\n\n    case 'longTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Long);\n      break;\n\n    case 'fullTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Full);\n      break;\n\n    case 'short':\n      const shortTime = getNamedFormat(locale, 'shortTime');\n      const shortDate = getNamedFormat(locale, 'shortDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Short), [shortTime, shortDate]);\n      break;\n\n    case 'medium':\n      const mediumTime = getNamedFormat(locale, 'mediumTime');\n      const mediumDate = getNamedFormat(locale, 'mediumDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Medium), [mediumTime, mediumDate]);\n      break;\n\n    case 'long':\n      const longTime = getNamedFormat(locale, 'longTime');\n      const longDate = getNamedFormat(locale, 'longDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Long), [longTime, longDate]);\n      break;\n\n    case 'full':\n      const fullTime = getNamedFormat(locale, 'fullTime');\n      const fullDate = getNamedFormat(locale, 'fullDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Full), [fullTime, fullDate]);\n      break;\n  }\n\n  if (formatValue) {\n    NAMED_FORMATS[localeId][format] = formatValue;\n  }\n\n  return formatValue;\n}\n\nfunction formatDateTime(str, opt_values) {\n  if (opt_values) {\n    str = str.replace(/\\{([^}]+)}/g, function (match, key) {\n      return opt_values != null && key in opt_values ? opt_values[key] : match;\n    });\n  }\n\n  return str;\n}\n\nfunction padNumber(num, digits, minusSign = '-', trim, negWrap) {\n  let neg = '';\n\n  if (num < 0 || negWrap && num <= 0) {\n    if (negWrap) {\n      num = -num + 1;\n    } else {\n      num = -num;\n      neg = minusSign;\n    }\n  }\n\n  let strNum = String(num);\n\n  while (strNum.length < digits) {\n    strNum = '0' + strNum;\n  }\n\n  if (trim) {\n    strNum = strNum.substr(strNum.length - digits);\n  }\n\n  return neg + strNum;\n}\n\nfunction formatFractionalSeconds(milliseconds, digits) {\n  const strMs = padNumber(milliseconds, 3);\n  return strMs.substr(0, digits);\n}\n/**\n * Returns a date formatter that transforms a date into its locale digit representation\n */\n\n\nfunction dateGetter(name, size, offset = 0, trim = false, negWrap = false) {\n  return function (date, locale) {\n    let part = getDatePart(name, date);\n\n    if (offset > 0 || part > -offset) {\n      part += offset;\n    }\n\n    if (name === DateType.Hours) {\n      if (part === 0 && offset === -12) {\n        part = 12;\n      }\n    } else if (name === DateType.FractionalSeconds) {\n      return formatFractionalSeconds(part, size);\n    }\n\n    const localeMinus = getLocaleNumberSymbol(locale, NumberSymbol.MinusSign);\n    return padNumber(part, size, localeMinus, trim, negWrap);\n  };\n}\n\nfunction getDatePart(part, date) {\n  switch (part) {\n    case DateType.FullYear:\n      return date.getFullYear();\n\n    case DateType.Month:\n      return date.getMonth();\n\n    case DateType.Date:\n      return date.getDate();\n\n    case DateType.Hours:\n      return date.getHours();\n\n    case DateType.Minutes:\n      return date.getMinutes();\n\n    case DateType.Seconds:\n      return date.getSeconds();\n\n    case DateType.FractionalSeconds:\n      return date.getMilliseconds();\n\n    case DateType.Day:\n      return date.getDay();\n\n    default:\n      throw new Error(`Unknown DateType value \"${part}\".`);\n  }\n}\n/**\n * Returns a date formatter that transforms a date into its locale string representation\n */\n\n\nfunction dateStrGetter(name, width, form = FormStyle.Format, extended = false) {\n  return function (date, locale) {\n    return getDateTranslation(date, locale, name, width, form, extended);\n  };\n}\n/**\n * Returns the locale translation of a date for a given form, type and width\n */\n\n\nfunction getDateTranslation(date, locale, name, width, form, extended) {\n  switch (name) {\n    case TranslationType.Months:\n      return getLocaleMonthNames(locale, form, width)[date.getMonth()];\n\n    case TranslationType.Days:\n      return getLocaleDayNames(locale, form, width)[date.getDay()];\n\n    case TranslationType.DayPeriods:\n      const currentHours = date.getHours();\n      const currentMinutes = date.getMinutes();\n\n      if (extended) {\n        const rules = getLocaleExtraDayPeriodRules(locale);\n        const dayPeriods = getLocaleExtraDayPeriods(locale, form, width);\n        const index = rules.findIndex(rule => {\n          if (Array.isArray(rule)) {\n            // morning, afternoon, evening, night\n            const [from, to] = rule;\n            const afterFrom = currentHours >= from.hours && currentMinutes >= from.minutes;\n            const beforeTo = currentHours < to.hours || currentHours === to.hours && currentMinutes < to.minutes; // We must account for normal rules that span a period during the day (e.g. 6am-9am)\n            // where `from` is less (earlier) than `to`. But also rules that span midnight (e.g.\n            // 10pm - 5am) where `from` is greater (later!) than `to`.\n            //\n            // In the first case the current time must be BOTH after `from` AND before `to`\n            // (e.g. 8am is after 6am AND before 10am).\n            //\n            // In the second case the current time must be EITHER after `from` OR before `to`\n            // (e.g. 4am is before 5am but not after 10pm; and 11pm is not before 5am but it is\n            // after 10pm).\n\n            if (from.hours < to.hours) {\n              if (afterFrom && beforeTo) {\n                return true;\n              }\n            } else if (afterFrom || beforeTo) {\n              return true;\n            }\n          } else {\n            // noon or midnight\n            if (rule.hours === currentHours && rule.minutes === currentMinutes) {\n              return true;\n            }\n          }\n\n          return false;\n        });\n\n        if (index !== -1) {\n          return dayPeriods[index];\n        }\n      } // if no rules for the day periods, we use am/pm by default\n\n\n      return getLocaleDayPeriods(locale, form, width)[currentHours < 12 ? 0 : 1];\n\n    case TranslationType.Eras:\n      return getLocaleEraNames(locale, width)[date.getFullYear() <= 0 ? 0 : 1];\n\n    default:\n      // This default case is not needed by TypeScript compiler, as the switch is exhaustive.\n      // However Closure Compiler does not understand that and reports an error in typed mode.\n      // The `throw new Error` below works around the problem, and the unexpected: never variable\n      // makes sure tsc still checks this code is unreachable.\n      const unexpected = name;\n      throw new Error(`unexpected translation type ${unexpected}`);\n  }\n}\n/**\n * Returns a date formatter that transforms a date and an offset into a timezone with ISO8601 or\n * GMT format depending on the width (eg: short = +0430, short:GMT = GMT+4, long = GMT+04:30,\n * extended = +04:30)\n */\n\n\nfunction timeZoneGetter(width) {\n  return function (date, locale, offset) {\n    const zone = -1 * offset;\n    const minusSign = getLocaleNumberSymbol(locale, NumberSymbol.MinusSign);\n    const hours = zone > 0 ? Math.floor(zone / 60) : Math.ceil(zone / 60);\n\n    switch (width) {\n      case ZoneWidth.Short:\n        return (zone >= 0 ? '+' : '') + padNumber(hours, 2, minusSign) + padNumber(Math.abs(zone % 60), 2, minusSign);\n\n      case ZoneWidth.ShortGMT:\n        return 'GMT' + (zone >= 0 ? '+' : '') + padNumber(hours, 1, minusSign);\n\n      case ZoneWidth.Long:\n        return 'GMT' + (zone >= 0 ? '+' : '') + padNumber(hours, 2, minusSign) + ':' + padNumber(Math.abs(zone % 60), 2, minusSign);\n\n      case ZoneWidth.Extended:\n        if (offset === 0) {\n          return 'Z';\n        } else {\n          return (zone >= 0 ? '+' : '') + padNumber(hours, 2, minusSign) + ':' + padNumber(Math.abs(zone % 60), 2, minusSign);\n        }\n\n      default:\n        throw new Error(`Unknown zone width \"${width}\"`);\n    }\n  };\n}\n\nconst JANUARY = 0;\nconst THURSDAY = 4;\n\nfunction getFirstThursdayOfYear(year) {\n  const firstDayOfYear = createDate(year, JANUARY, 1).getDay();\n  return createDate(year, 0, 1 + (firstDayOfYear <= THURSDAY ? THURSDAY : THURSDAY + 7) - firstDayOfYear);\n}\n\nfunction getThursdayThisWeek(datetime) {\n  return createDate(datetime.getFullYear(), datetime.getMonth(), datetime.getDate() + (THURSDAY - datetime.getDay()));\n}\n\nfunction weekGetter(size, monthBased = false) {\n  return function (date, locale) {\n    let result;\n\n    if (monthBased) {\n      const nbDaysBefore1stDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1).getDay() - 1;\n      const today = date.getDate();\n      result = 1 + Math.floor((today + nbDaysBefore1stDayOfMonth) / 7);\n    } else {\n      const thisThurs = getThursdayThisWeek(date); // Some days of a year are part of next year according to ISO 8601.\n      // Compute the firstThurs from the year of this week's Thursday\n\n      const firstThurs = getFirstThursdayOfYear(thisThurs.getFullYear());\n      const diff = thisThurs.getTime() - firstThurs.getTime();\n      result = 1 + Math.round(diff / 6.048e8); // 6.048e8 ms per week\n    }\n\n    return padNumber(result, size, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  };\n}\n/**\n * Returns a date formatter that provides the week-numbering year for the input date.\n */\n\n\nfunction weekNumberingYearGetter(size, trim = false) {\n  return function (date, locale) {\n    const thisThurs = getThursdayThisWeek(date);\n    const weekNumberingYear = thisThurs.getFullYear();\n    return padNumber(weekNumberingYear, size, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign), trim);\n  };\n}\n\nconst DATE_FORMATS = {}; // Based on CLDR formats:\n// See complete list: http://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n// See also explanations: http://cldr.unicode.org/translation/date-time\n// TODO(ocombe): support all missing cldr formats: U, Q, D, F, e, j, J, C, A, v, V, X, x\n\nfunction getDateFormatter(format) {\n  if (DATE_FORMATS[format]) {\n    return DATE_FORMATS[format];\n  }\n\n  let formatter;\n\n  switch (format) {\n    // Era name (AD/BC)\n    case 'G':\n    case 'GG':\n    case 'GGG':\n      formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Abbreviated);\n      break;\n\n    case 'GGGG':\n      formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Wide);\n      break;\n\n    case 'GGGGG':\n      formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Narrow);\n      break;\n    // 1 digit representation of the year, e.g. (AD 1 => 1, AD 199 => 199)\n\n    case 'y':\n      formatter = dateGetter(DateType.FullYear, 1, 0, false, true);\n      break;\n    // 2 digit representation of the year, padded (00-99). (e.g. AD 2001 => 01, AD 2010 => 10)\n\n    case 'yy':\n      formatter = dateGetter(DateType.FullYear, 2, 0, true, true);\n      break;\n    // 3 digit representation of the year, padded (000-999). (e.g. AD 2001 => 01, AD 2010 => 10)\n\n    case 'yyy':\n      formatter = dateGetter(DateType.FullYear, 3, 0, false, true);\n      break;\n    // 4 digit representation of the year (e.g. AD 1 => 0001, AD 2010 => 2010)\n\n    case 'yyyy':\n      formatter = dateGetter(DateType.FullYear, 4, 0, false, true);\n      break;\n    // 1 digit representation of the week-numbering year, e.g. (AD 1 => 1, AD 199 => 199)\n\n    case 'Y':\n      formatter = weekNumberingYearGetter(1);\n      break;\n    // 2 digit representation of the week-numbering year, padded (00-99). (e.g. AD 2001 => 01, AD\n    // 2010 => 10)\n\n    case 'YY':\n      formatter = weekNumberingYearGetter(2, true);\n      break;\n    // 3 digit representation of the week-numbering year, padded (000-999). (e.g. AD 1 => 001, AD\n    // 2010 => 2010)\n\n    case 'YYY':\n      formatter = weekNumberingYearGetter(3);\n      break;\n    // 4 digit representation of the week-numbering year (e.g. AD 1 => 0001, AD 2010 => 2010)\n\n    case 'YYYY':\n      formatter = weekNumberingYearGetter(4);\n      break;\n    // Month of the year (1-12), numeric\n\n    case 'M':\n    case 'L':\n      formatter = dateGetter(DateType.Month, 1, 1);\n      break;\n\n    case 'MM':\n    case 'LL':\n      formatter = dateGetter(DateType.Month, 2, 1);\n      break;\n    // Month of the year (January, ...), string, format\n\n    case 'MMM':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Abbreviated);\n      break;\n\n    case 'MMMM':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Wide);\n      break;\n\n    case 'MMMMM':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Narrow);\n      break;\n    // Month of the year (January, ...), string, standalone\n\n    case 'LLL':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Abbreviated, FormStyle.Standalone);\n      break;\n\n    case 'LLLL':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Wide, FormStyle.Standalone);\n      break;\n\n    case 'LLLLL':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Narrow, FormStyle.Standalone);\n      break;\n    // Week of the year (1, ... 52)\n\n    case 'w':\n      formatter = weekGetter(1);\n      break;\n\n    case 'ww':\n      formatter = weekGetter(2);\n      break;\n    // Week of the month (1, ...)\n\n    case 'W':\n      formatter = weekGetter(1, true);\n      break;\n    // Day of the month (1-31)\n\n    case 'd':\n      formatter = dateGetter(DateType.Date, 1);\n      break;\n\n    case 'dd':\n      formatter = dateGetter(DateType.Date, 2);\n      break;\n    // Day of the Week StandAlone (1, 1, Mon, Monday, M, Mo)\n\n    case 'c':\n    case 'cc':\n      formatter = dateGetter(DateType.Day, 1);\n      break;\n\n    case 'ccc':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Abbreviated, FormStyle.Standalone);\n      break;\n\n    case 'cccc':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Wide, FormStyle.Standalone);\n      break;\n\n    case 'ccccc':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Narrow, FormStyle.Standalone);\n      break;\n\n    case 'cccccc':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Short, FormStyle.Standalone);\n      break;\n    // Day of the Week\n\n    case 'E':\n    case 'EE':\n    case 'EEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Abbreviated);\n      break;\n\n    case 'EEEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Wide);\n      break;\n\n    case 'EEEEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Narrow);\n      break;\n\n    case 'EEEEEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Short);\n      break;\n    // Generic period of the day (am-pm)\n\n    case 'a':\n    case 'aa':\n    case 'aaa':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Abbreviated);\n      break;\n\n    case 'aaaa':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Wide);\n      break;\n\n    case 'aaaaa':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Narrow);\n      break;\n    // Extended period of the day (midnight, at night, ...), standalone\n\n    case 'b':\n    case 'bb':\n    case 'bbb':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Abbreviated, FormStyle.Standalone, true);\n      break;\n\n    case 'bbbb':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Wide, FormStyle.Standalone, true);\n      break;\n\n    case 'bbbbb':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Narrow, FormStyle.Standalone, true);\n      break;\n    // Extended period of the day (midnight, night, ...), standalone\n\n    case 'B':\n    case 'BB':\n    case 'BBB':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Abbreviated, FormStyle.Format, true);\n      break;\n\n    case 'BBBB':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Wide, FormStyle.Format, true);\n      break;\n\n    case 'BBBBB':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Narrow, FormStyle.Format, true);\n      break;\n    // Hour in AM/PM, (1-12)\n\n    case 'h':\n      formatter = dateGetter(DateType.Hours, 1, -12);\n      break;\n\n    case 'hh':\n      formatter = dateGetter(DateType.Hours, 2, -12);\n      break;\n    // Hour of the day (0-23)\n\n    case 'H':\n      formatter = dateGetter(DateType.Hours, 1);\n      break;\n    // Hour in day, padded (00-23)\n\n    case 'HH':\n      formatter = dateGetter(DateType.Hours, 2);\n      break;\n    // Minute of the hour (0-59)\n\n    case 'm':\n      formatter = dateGetter(DateType.Minutes, 1);\n      break;\n\n    case 'mm':\n      formatter = dateGetter(DateType.Minutes, 2);\n      break;\n    // Second of the minute (0-59)\n\n    case 's':\n      formatter = dateGetter(DateType.Seconds, 1);\n      break;\n\n    case 'ss':\n      formatter = dateGetter(DateType.Seconds, 2);\n      break;\n    // Fractional second\n\n    case 'S':\n      formatter = dateGetter(DateType.FractionalSeconds, 1);\n      break;\n\n    case 'SS':\n      formatter = dateGetter(DateType.FractionalSeconds, 2);\n      break;\n\n    case 'SSS':\n      formatter = dateGetter(DateType.FractionalSeconds, 3);\n      break;\n    // Timezone ISO8601 short format (-0430)\n\n    case 'Z':\n    case 'ZZ':\n    case 'ZZZ':\n      formatter = timeZoneGetter(ZoneWidth.Short);\n      break;\n    // Timezone ISO8601 extended format (-04:30)\n\n    case 'ZZZZZ':\n      formatter = timeZoneGetter(ZoneWidth.Extended);\n      break;\n    // Timezone GMT short format (GMT+4)\n\n    case 'O':\n    case 'OO':\n    case 'OOO': // Should be location, but fallback to format O instead because we don't have the data yet\n\n    case 'z':\n    case 'zz':\n    case 'zzz':\n      formatter = timeZoneGetter(ZoneWidth.ShortGMT);\n      break;\n    // Timezone GMT long format (GMT+0430)\n\n    case 'OOOO':\n    case 'ZZZZ': // Should be location, but fallback to format O instead because we don't have the data yet\n\n    case 'zzzz':\n      formatter = timeZoneGetter(ZoneWidth.Long);\n      break;\n\n    default:\n      return null;\n  }\n\n  DATE_FORMATS[format] = formatter;\n  return formatter;\n}\n\nfunction timezoneToOffset(timezone, fallback) {\n  // Support: IE 11 only, Edge 13-15+\n  // IE/Edge do not \"understand\" colon (`:`) in timezone\n  timezone = timezone.replace(/:/g, '');\n  const requestedTimezoneOffset = Date.parse('Jan 01, 1970 00:00:00 ' + timezone) / 60000;\n  return isNaN(requestedTimezoneOffset) ? fallback : requestedTimezoneOffset;\n}\n\nfunction addDateMinutes(date, minutes) {\n  date = new Date(date.getTime());\n  date.setMinutes(date.getMinutes() + minutes);\n  return date;\n}\n\nfunction convertTimezoneToLocal(date, timezone, reverse) {\n  const reverseValue = reverse ? -1 : 1;\n  const dateTimezoneOffset = date.getTimezoneOffset();\n  const timezoneOffset = timezoneToOffset(timezone, dateTimezoneOffset);\n  return addDateMinutes(date, reverseValue * (timezoneOffset - dateTimezoneOffset));\n}\n/**\n * Converts a value to date.\n *\n * Supported input formats:\n * - `Date`\n * - number: timestamp\n * - string: numeric (e.g. \"1234\"), ISO and date strings in a format supported by\n *   [Date.parse()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/parse).\n *   Note: ISO strings without time return a date without timeoffset.\n *\n * Throws if unable to convert to a date.\n */\n\n\nfunction toDate(value) {\n  if (isDate(value)) {\n    return value;\n  }\n\n  if (typeof value === 'number' && !isNaN(value)) {\n    return new Date(value);\n  }\n\n  if (typeof value === 'string') {\n    value = value.trim();\n\n    if (/^(\\d{4}(-\\d{1,2}(-\\d{1,2})?)?)$/.test(value)) {\n      /* For ISO Strings without time the day, month and year must be extracted from the ISO String\n      before Date creation to avoid time offset and errors in the new Date.\n      If we only replace '-' with ',' in the ISO String (\"2015,01,01\"), and try to create a new\n      date, some browsers (e.g. IE 9) will throw an invalid Date error.\n      If we leave the '-' (\"2015-01-01\") and try to create a new Date(\"2015-01-01\") the timeoffset\n      is applied.\n      Note: ISO months are 0 for January, 1 for February, ... */\n      const [y, m = 1, d = 1] = value.split('-').map(val => +val);\n      return createDate(y, m - 1, d);\n    }\n\n    const parsedNb = parseFloat(value); // any string that only contains numbers, like \"1234\" but not like \"1234hello\"\n\n    if (!isNaN(value - parsedNb)) {\n      return new Date(parsedNb);\n    }\n\n    let match;\n\n    if (match = value.match(ISO8601_DATE_REGEX)) {\n      return isoStringToDate(match);\n    }\n  }\n\n  const date = new Date(value);\n\n  if (!isDate(date)) {\n    throw new Error(`Unable to convert \"${value}\" into a date`);\n  }\n\n  return date;\n}\n/**\n * Converts a date in ISO8601 to a Date.\n * Used instead of `Date.parse` because of browser discrepancies.\n */\n\n\nfunction isoStringToDate(match) {\n  const date = new Date(0);\n  let tzHour = 0;\n  let tzMin = 0; // match[8] means that the string contains \"Z\" (UTC) or a timezone like \"+01:00\" or \"+0100\"\n\n  const dateSetter = match[8] ? date.setUTCFullYear : date.setFullYear;\n  const timeSetter = match[8] ? date.setUTCHours : date.setHours; // if there is a timezone defined like \"+01:00\" or \"+0100\"\n\n  if (match[9]) {\n    tzHour = Number(match[9] + match[10]);\n    tzMin = Number(match[9] + match[11]);\n  }\n\n  dateSetter.call(date, Number(match[1]), Number(match[2]) - 1, Number(match[3]));\n  const h = Number(match[4] || 0) - tzHour;\n  const m = Number(match[5] || 0) - tzMin;\n  const s = Number(match[6] || 0); // The ECMAScript specification (https://www.ecma-international.org/ecma-262/5.1/#sec-*********)\n  // defines that `DateTime` milliseconds should always be rounded down, so that `999.9ms`\n  // becomes `999ms`.\n\n  const ms = Math.floor(parseFloat('0.' + (match[7] || 0)) * 1000);\n  timeSetter.call(date, h, m, s, ms);\n  return date;\n}\n\nfunction isDate(value) {\n  return value instanceof Date && !isNaN(value.valueOf());\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst NUMBER_FORMAT_REGEXP = /^(\\d+)?\\.((\\d+)(-(\\d+))?)?$/;\nconst MAX_DIGITS = 22;\nconst DECIMAL_SEP = '.';\nconst ZERO_CHAR = '0';\nconst PATTERN_SEP = ';';\nconst GROUP_SEP = ',';\nconst DIGIT_CHAR = '#';\nconst CURRENCY_CHAR = '¤';\nconst PERCENT_CHAR = '%';\n/**\n * Transforms a number to a locale string based on a style and a format.\n */\n\nfunction formatNumberToLocaleString(value, pattern, locale, groupSymbol, decimalSymbol, digitsInfo, isPercent = false) {\n  let formattedText = '';\n  let isZero = false;\n\n  if (!isFinite(value)) {\n    formattedText = getLocaleNumberSymbol(locale, NumberSymbol.Infinity);\n  } else {\n    let parsedNumber = parseNumber(value);\n\n    if (isPercent) {\n      parsedNumber = toPercent(parsedNumber);\n    }\n\n    let minInt = pattern.minInt;\n    let minFraction = pattern.minFrac;\n    let maxFraction = pattern.maxFrac;\n\n    if (digitsInfo) {\n      const parts = digitsInfo.match(NUMBER_FORMAT_REGEXP);\n\n      if (parts === null) {\n        throw new Error(`${digitsInfo} is not a valid digit info`);\n      }\n\n      const minIntPart = parts[1];\n      const minFractionPart = parts[3];\n      const maxFractionPart = parts[5];\n\n      if (minIntPart != null) {\n        minInt = parseIntAutoRadix(minIntPart);\n      }\n\n      if (minFractionPart != null) {\n        minFraction = parseIntAutoRadix(minFractionPart);\n      }\n\n      if (maxFractionPart != null) {\n        maxFraction = parseIntAutoRadix(maxFractionPart);\n      } else if (minFractionPart != null && minFraction > maxFraction) {\n        maxFraction = minFraction;\n      }\n    }\n\n    roundNumber(parsedNumber, minFraction, maxFraction);\n    let digits = parsedNumber.digits;\n    let integerLen = parsedNumber.integerLen;\n    const exponent = parsedNumber.exponent;\n    let decimals = [];\n    isZero = digits.every(d => !d); // pad zeros for small numbers\n\n    for (; integerLen < minInt; integerLen++) {\n      digits.unshift(0);\n    } // pad zeros for small numbers\n\n\n    for (; integerLen < 0; integerLen++) {\n      digits.unshift(0);\n    } // extract decimals digits\n\n\n    if (integerLen > 0) {\n      decimals = digits.splice(integerLen, digits.length);\n    } else {\n      decimals = digits;\n      digits = [0];\n    } // format the integer digits with grouping separators\n\n\n    const groups = [];\n\n    if (digits.length >= pattern.lgSize) {\n      groups.unshift(digits.splice(-pattern.lgSize, digits.length).join(''));\n    }\n\n    while (digits.length > pattern.gSize) {\n      groups.unshift(digits.splice(-pattern.gSize, digits.length).join(''));\n    }\n\n    if (digits.length) {\n      groups.unshift(digits.join(''));\n    }\n\n    formattedText = groups.join(getLocaleNumberSymbol(locale, groupSymbol)); // append the decimal digits\n\n    if (decimals.length) {\n      formattedText += getLocaleNumberSymbol(locale, decimalSymbol) + decimals.join('');\n    }\n\n    if (exponent) {\n      formattedText += getLocaleNumberSymbol(locale, NumberSymbol.Exponential) + '+' + exponent;\n    }\n  }\n\n  if (value < 0 && !isZero) {\n    formattedText = pattern.negPre + formattedText + pattern.negSuf;\n  } else {\n    formattedText = pattern.posPre + formattedText + pattern.posSuf;\n  }\n\n  return formattedText;\n}\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a number as currency using locale rules.\n *\n * @param value The number to format.\n * @param locale A locale code for the locale format rules to use.\n * @param currency A string containing the currency symbol or its name,\n * such as \"$\" or \"Canadian Dollar\". Used in output string, but does not affect the operation\n * of the function.\n * @param currencyCode The [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217)\n * currency code, such as `USD` for the US dollar and `EUR` for the euro.\n * Used to determine the number of digits in the decimal part.\n * @param digitsInfo Decimal representation options, specified by a string in the following format:\n * `{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}`. See `DecimalPipe` for more details.\n *\n * @returns The formatted currency value.\n *\n * @see `formatNumber()`\n * @see `DecimalPipe`\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction formatCurrency(value, locale, currency, currencyCode, digitsInfo) {\n  const format = getLocaleNumberFormat(locale, NumberFormatStyle.Currency);\n  const pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  pattern.minFrac = getNumberOfCurrencyDigits(currencyCode);\n  pattern.maxFrac = pattern.minFrac;\n  const res = formatNumberToLocaleString(value, pattern, locale, NumberSymbol.CurrencyGroup, NumberSymbol.CurrencyDecimal, digitsInfo);\n  return res.replace(CURRENCY_CHAR, currency) // if we have 2 time the currency character, the second one is ignored\n  .replace(CURRENCY_CHAR, '') // If there is a spacing between currency character and the value and\n  // the currency character is supressed by passing an empty string, the\n  // spacing character would remain as part of the string. Then we\n  // should remove it.\n  .trim();\n}\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a number as a percentage according to locale rules.\n *\n * @param value The number to format.\n * @param locale A locale code for the locale format rules to use.\n * @param digitsInfo Decimal representation options, specified by a string in the following format:\n * `{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}`. See `DecimalPipe` for more details.\n *\n * @returns The formatted percentage value.\n *\n * @see `formatNumber()`\n * @see `DecimalPipe`\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n * @publicApi\n *\n */\n\n\nfunction formatPercent(value, locale, digitsInfo) {\n  const format = getLocaleNumberFormat(locale, NumberFormatStyle.Percent);\n  const pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  const res = formatNumberToLocaleString(value, pattern, locale, NumberSymbol.Group, NumberSymbol.Decimal, digitsInfo, true);\n  return res.replace(new RegExp(PERCENT_CHAR, 'g'), getLocaleNumberSymbol(locale, NumberSymbol.PercentSign));\n}\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a number as text, with group sizing, separator, and other\n * parameters based on the locale.\n *\n * @param value The number to format.\n * @param locale A locale code for the locale format rules to use.\n * @param digitsInfo Decimal representation options, specified by a string in the following format:\n * `{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}`. See `DecimalPipe` for more details.\n *\n * @returns The formatted text string.\n * @see [Internationalization (i18n) Guide](https://angular.io/guide/i18n-overview)\n *\n * @publicApi\n */\n\n\nfunction formatNumber(value, locale, digitsInfo) {\n  const format = getLocaleNumberFormat(locale, NumberFormatStyle.Decimal);\n  const pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  return formatNumberToLocaleString(value, pattern, locale, NumberSymbol.Group, NumberSymbol.Decimal, digitsInfo);\n}\n\nfunction parseNumberFormat(format, minusSign = '-') {\n  const p = {\n    minInt: 1,\n    minFrac: 0,\n    maxFrac: 0,\n    posPre: '',\n    posSuf: '',\n    negPre: '',\n    negSuf: '',\n    gSize: 0,\n    lgSize: 0\n  };\n  const patternParts = format.split(PATTERN_SEP);\n  const positive = patternParts[0];\n  const negative = patternParts[1];\n  const positiveParts = positive.indexOf(DECIMAL_SEP) !== -1 ? positive.split(DECIMAL_SEP) : [positive.substring(0, positive.lastIndexOf(ZERO_CHAR) + 1), positive.substring(positive.lastIndexOf(ZERO_CHAR) + 1)],\n        integer = positiveParts[0],\n        fraction = positiveParts[1] || '';\n  p.posPre = integer.substr(0, integer.indexOf(DIGIT_CHAR));\n\n  for (let i = 0; i < fraction.length; i++) {\n    const ch = fraction.charAt(i);\n\n    if (ch === ZERO_CHAR) {\n      p.minFrac = p.maxFrac = i + 1;\n    } else if (ch === DIGIT_CHAR) {\n      p.maxFrac = i + 1;\n    } else {\n      p.posSuf += ch;\n    }\n  }\n\n  const groups = integer.split(GROUP_SEP);\n  p.gSize = groups[1] ? groups[1].length : 0;\n  p.lgSize = groups[2] || groups[1] ? (groups[2] || groups[1]).length : 0;\n\n  if (negative) {\n    const trunkLen = positive.length - p.posPre.length - p.posSuf.length,\n          pos = negative.indexOf(DIGIT_CHAR);\n    p.negPre = negative.substr(0, pos).replace(/'/g, '');\n    p.negSuf = negative.substr(pos + trunkLen).replace(/'/g, '');\n  } else {\n    p.negPre = minusSign + p.posPre;\n    p.negSuf = p.posSuf;\n  }\n\n  return p;\n} // Transforms a parsed number into a percentage by multiplying it by 100\n\n\nfunction toPercent(parsedNumber) {\n  // if the number is 0, don't do anything\n  if (parsedNumber.digits[0] === 0) {\n    return parsedNumber;\n  } // Getting the current number of decimals\n\n\n  const fractionLen = parsedNumber.digits.length - parsedNumber.integerLen;\n\n  if (parsedNumber.exponent) {\n    parsedNumber.exponent += 2;\n  } else {\n    if (fractionLen === 0) {\n      parsedNumber.digits.push(0, 0);\n    } else if (fractionLen === 1) {\n      parsedNumber.digits.push(0);\n    }\n\n    parsedNumber.integerLen += 2;\n  }\n\n  return parsedNumber;\n}\n/**\n * Parses a number.\n * Significant bits of this parse algorithm came from https://github.com/MikeMcl/big.js/\n */\n\n\nfunction parseNumber(num) {\n  let numStr = Math.abs(num) + '';\n  let exponent = 0,\n      digits,\n      integerLen;\n  let i, j, zeros; // Decimal point?\n\n  if ((integerLen = numStr.indexOf(DECIMAL_SEP)) > -1) {\n    numStr = numStr.replace(DECIMAL_SEP, '');\n  } // Exponential form?\n\n\n  if ((i = numStr.search(/e/i)) > 0) {\n    // Work out the exponent.\n    if (integerLen < 0) integerLen = i;\n    integerLen += +numStr.slice(i + 1);\n    numStr = numStr.substring(0, i);\n  } else if (integerLen < 0) {\n    // There was no decimal point or exponent so it is an integer.\n    integerLen = numStr.length;\n  } // Count the number of leading zeros.\n\n\n  for (i = 0; numStr.charAt(i) === ZERO_CHAR; i++) {\n    /* empty */\n  }\n\n  if (i === (zeros = numStr.length)) {\n    // The digits are all zero.\n    digits = [0];\n    integerLen = 1;\n  } else {\n    // Count the number of trailing zeros\n    zeros--;\n\n    while (numStr.charAt(zeros) === ZERO_CHAR) zeros--; // Trailing zeros are insignificant so ignore them\n\n\n    integerLen -= i;\n    digits = []; // Convert string to array of digits without leading/trailing zeros.\n\n    for (j = 0; i <= zeros; i++, j++) {\n      digits[j] = Number(numStr.charAt(i));\n    }\n  } // If the number overflows the maximum allowed digits then use an exponent.\n\n\n  if (integerLen > MAX_DIGITS) {\n    digits = digits.splice(0, MAX_DIGITS - 1);\n    exponent = integerLen - 1;\n    integerLen = 1;\n  }\n\n  return {\n    digits,\n    exponent,\n    integerLen\n  };\n}\n/**\n * Round the parsed number to the specified number of decimal places\n * This function changes the parsedNumber in-place\n */\n\n\nfunction roundNumber(parsedNumber, minFrac, maxFrac) {\n  if (minFrac > maxFrac) {\n    throw new Error(`The minimum number of digits after fraction (${minFrac}) is higher than the maximum (${maxFrac}).`);\n  }\n\n  let digits = parsedNumber.digits;\n  let fractionLen = digits.length - parsedNumber.integerLen;\n  const fractionSize = Math.min(Math.max(minFrac, fractionLen), maxFrac); // The index of the digit to where rounding is to occur\n\n  let roundAt = fractionSize + parsedNumber.integerLen;\n  let digit = digits[roundAt];\n\n  if (roundAt > 0) {\n    // Drop fractional digits beyond `roundAt`\n    digits.splice(Math.max(parsedNumber.integerLen, roundAt)); // Set non-fractional digits beyond `roundAt` to 0\n\n    for (let j = roundAt; j < digits.length; j++) {\n      digits[j] = 0;\n    }\n  } else {\n    // We rounded to zero so reset the parsedNumber\n    fractionLen = Math.max(0, fractionLen);\n    parsedNumber.integerLen = 1;\n    digits.length = Math.max(1, roundAt = fractionSize + 1);\n    digits[0] = 0;\n\n    for (let i = 1; i < roundAt; i++) digits[i] = 0;\n  }\n\n  if (digit >= 5) {\n    if (roundAt - 1 < 0) {\n      for (let k = 0; k > roundAt; k--) {\n        digits.unshift(0);\n        parsedNumber.integerLen++;\n      }\n\n      digits.unshift(1);\n      parsedNumber.integerLen++;\n    } else {\n      digits[roundAt - 1]++;\n    }\n  } // Pad out with zeros to get the required fraction length\n\n\n  for (; fractionLen < Math.max(0, fractionSize); fractionLen++) digits.push(0);\n\n  let dropTrailingZeros = fractionSize !== 0; // Minimal length = nb of decimals required + current nb of integers\n  // Any number besides that is optional and can be removed if it's a trailing 0\n\n  const minLen = minFrac + parsedNumber.integerLen; // Do any carrying, e.g. a digit was rounded up to 10\n\n  const carry = digits.reduceRight(function (carry, d, i, digits) {\n    d = d + carry;\n    digits[i] = d < 10 ? d : d - 10; // d % 10\n\n    if (dropTrailingZeros) {\n      // Do not keep meaningless fractional trailing zeros (e.g. 15.52000 --> 15.52)\n      if (digits[i] === 0 && i >= minLen) {\n        digits.pop();\n      } else {\n        dropTrailingZeros = false;\n      }\n    }\n\n    return d >= 10 ? 1 : 0; // Math.floor(d / 10);\n  }, 0);\n\n  if (carry) {\n    digits.unshift(carry);\n    parsedNumber.integerLen++;\n  }\n}\n\nfunction parseIntAutoRadix(text) {\n  const result = parseInt(text);\n\n  if (isNaN(result)) {\n    throw new Error('Invalid integer literal when parsing ' + text);\n  }\n\n  return result;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nclass NgLocalization {}\n/**\n * Returns the plural category for a given value.\n * - \"=value\" when the case exists,\n * - the plural category otherwise\n */\n\n\nfunction getPluralCategory(value, cases, ngLocalization, locale) {\n  let key = `=${value}`;\n\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n\n  key = ngLocalization.getPluralCategory(value, locale);\n\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n\n  if (cases.indexOf('other') > -1) {\n    return 'other';\n  }\n\n  throw new Error(`No plural message found for value \"${value}\"`);\n}\n/**\n * Returns the plural case based on the locale\n *\n * @publicApi\n */\n\n\nlet NgLocaleLocalization = /*#__PURE__*/(() => {\n  class NgLocaleLocalization extends NgLocalization {\n    constructor(locale) {\n      super();\n      this.locale = locale;\n    }\n\n    getPluralCategory(value, locale) {\n      const plural = getLocalePluralCase(locale || this.locale)(value);\n\n      switch (plural) {\n        case Plural.Zero:\n          return 'zero';\n\n        case Plural.One:\n          return 'one';\n\n        case Plural.Two:\n          return 'two';\n\n        case Plural.Few:\n          return 'few';\n\n        case Plural.Many:\n          return 'many';\n\n        default:\n          return 'other';\n      }\n    }\n\n  }\n\n  NgLocaleLocalization.ɵfac = function NgLocaleLocalization_Factory(t) {\n    return new (t || NgLocaleLocalization)(i0.ɵɵinject(LOCALE_ID));\n  };\n\n  NgLocaleLocalization.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NgLocaleLocalization,\n    factory: NgLocaleLocalization.ɵfac\n  });\n  return NgLocaleLocalization;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Register global data to be used internally by Angular. See the\n * [\"I18n guide\"](guide/i18n-common-format-data-locale) to know how to import additional locale\n * data.\n *\n * The signature registerLocaleData(data: any, extraData?: any) is deprecated since v5.1\n *\n * @publicApi\n */\n\n\nfunction registerLocaleData(data, localeId, extraData) {\n  return ɵregisterLocaleData(data, localeId, extraData);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction parseCookieValue(cookieStr, name) {\n  name = encodeURIComponent(name);\n\n  for (const cookie of cookieStr.split(';')) {\n    const eqIndex = cookie.indexOf('=');\n    const [cookieName, cookieValue] = eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n\n    if (cookieName.trim() === name) {\n      return decodeURIComponent(cookieValue);\n    }\n  }\n\n  return null;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @ngModule CommonModule\n *\n * @usageNotes\n * ```\n *     <some-element [ngClass]=\"'first second'\">...</some-element>\n *\n *     <some-element [ngClass]=\"['first', 'second']\">...</some-element>\n *\n *     <some-element [ngClass]=\"{'first': true, 'second': true, 'third': false}\">...</some-element>\n *\n *     <some-element [ngClass]=\"stringExp|arrayExp|objExp\">...</some-element>\n *\n *     <some-element [ngClass]=\"{'class1 class2 class3' : true}\">...</some-element>\n * ```\n *\n * @description\n *\n * Adds and removes CSS classes on an HTML element.\n *\n * The CSS classes are updated as follows, depending on the type of the expression evaluation:\n * - `string` - the CSS classes listed in the string (space delimited) are added,\n * - `Array` - the CSS classes declared as Array elements are added,\n * - `Object` - keys are CSS classes that get added when the expression given in the value\n *              evaluates to a truthy value, otherwise they are removed.\n *\n * @publicApi\n */\n\n\nlet NgClass = /*#__PURE__*/(() => {\n  class NgClass {\n    constructor(_iterableDiffers, _keyValueDiffers, _ngEl, _renderer) {\n      this._iterableDiffers = _iterableDiffers;\n      this._keyValueDiffers = _keyValueDiffers;\n      this._ngEl = _ngEl;\n      this._renderer = _renderer;\n      this._iterableDiffer = null;\n      this._keyValueDiffer = null;\n      this._initialClasses = [];\n      this._rawClass = null;\n    }\n\n    set klass(value) {\n      this._removeClasses(this._initialClasses);\n\n      this._initialClasses = typeof value === 'string' ? value.split(/\\s+/) : [];\n\n      this._applyClasses(this._initialClasses);\n\n      this._applyClasses(this._rawClass);\n    }\n\n    set ngClass(value) {\n      this._removeClasses(this._rawClass);\n\n      this._applyClasses(this._initialClasses);\n\n      this._iterableDiffer = null;\n      this._keyValueDiffer = null;\n      this._rawClass = typeof value === 'string' ? value.split(/\\s+/) : value;\n\n      if (this._rawClass) {\n        if (ɵisListLikeIterable(this._rawClass)) {\n          this._iterableDiffer = this._iterableDiffers.find(this._rawClass).create();\n        } else {\n          this._keyValueDiffer = this._keyValueDiffers.find(this._rawClass).create();\n        }\n      }\n    }\n\n    ngDoCheck() {\n      if (this._iterableDiffer) {\n        const iterableChanges = this._iterableDiffer.diff(this._rawClass);\n\n        if (iterableChanges) {\n          this._applyIterableChanges(iterableChanges);\n        }\n      } else if (this._keyValueDiffer) {\n        const keyValueChanges = this._keyValueDiffer.diff(this._rawClass);\n\n        if (keyValueChanges) {\n          this._applyKeyValueChanges(keyValueChanges);\n        }\n      }\n    }\n\n    _applyKeyValueChanges(changes) {\n      changes.forEachAddedItem(record => this._toggleClass(record.key, record.currentValue));\n      changes.forEachChangedItem(record => this._toggleClass(record.key, record.currentValue));\n      changes.forEachRemovedItem(record => {\n        if (record.previousValue) {\n          this._toggleClass(record.key, false);\n        }\n      });\n    }\n\n    _applyIterableChanges(changes) {\n      changes.forEachAddedItem(record => {\n        if (typeof record.item === 'string') {\n          this._toggleClass(record.item, true);\n        } else {\n          throw new Error(`NgClass can only toggle CSS classes expressed as strings, got ${ɵstringify(record.item)}`);\n        }\n      });\n      changes.forEachRemovedItem(record => this._toggleClass(record.item, false));\n    }\n    /**\n     * Applies a collection of CSS classes to the DOM element.\n     *\n     * For argument of type Set and Array CSS class names contained in those collections are always\n     * added.\n     * For argument of type Map CSS class name in the map's key is toggled based on the value (added\n     * for truthy and removed for falsy).\n     */\n\n\n    _applyClasses(rawClassVal) {\n      if (rawClassVal) {\n        if (Array.isArray(rawClassVal) || rawClassVal instanceof Set) {\n          rawClassVal.forEach(klass => this._toggleClass(klass, true));\n        } else {\n          Object.keys(rawClassVal).forEach(klass => this._toggleClass(klass, !!rawClassVal[klass]));\n        }\n      }\n    }\n    /**\n     * Removes a collection of CSS classes from the DOM element. This is mostly useful for cleanup\n     * purposes.\n     */\n\n\n    _removeClasses(rawClassVal) {\n      if (rawClassVal) {\n        if (Array.isArray(rawClassVal) || rawClassVal instanceof Set) {\n          rawClassVal.forEach(klass => this._toggleClass(klass, false));\n        } else {\n          Object.keys(rawClassVal).forEach(klass => this._toggleClass(klass, false));\n        }\n      }\n    }\n\n    _toggleClass(klass, enabled) {\n      klass = klass.trim();\n\n      if (klass) {\n        klass.split(/\\s+/g).forEach(klass => {\n          if (enabled) {\n            this._renderer.addClass(this._ngEl.nativeElement, klass);\n          } else {\n            this._renderer.removeClass(this._ngEl.nativeElement, klass);\n          }\n        });\n      }\n    }\n\n  }\n\n  NgClass.ɵfac = function NgClass_Factory(t) {\n    return new (t || NgClass)(i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i0.KeyValueDiffers), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n\n  NgClass.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgClass,\n    selectors: [[\"\", \"ngClass\", \"\"]],\n    inputs: {\n      klass: [\"class\", \"klass\"],\n      ngClass: \"ngClass\"\n    }\n  });\n  return NgClass;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Instantiates a {@link Component} type and inserts its Host View into the current View.\n * `NgComponentOutlet` provides a declarative approach for dynamic component creation.\n *\n * `NgComponentOutlet` requires a component type, if a falsy value is set the view will clear and\n * any existing component will be destroyed.\n *\n * @usageNotes\n *\n * ### Fine tune control\n *\n * You can control the component creation process by using the following optional attributes:\n *\n * * `ngComponentOutletInjector`: Optional custom {@link Injector} that will be used as parent for\n * the Component. Defaults to the injector of the current view container.\n *\n * * `ngComponentOutletContent`: Optional list of projectable nodes to insert into the content\n * section of the component, if it exists.\n *\n * * `ngComponentOutletNgModuleFactory`: Optional module factory to allow loading another\n * module dynamically, then loading a component from that module.\n *\n * ### Syntax\n *\n * Simple\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression\"></ng-container>\n * ```\n *\n * Customized injector/content\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   injector: injectorExpression;\n *                                   content: contentNodesExpression;\">\n * </ng-container>\n * ```\n *\n * Customized ngModuleFactory\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   ngModuleFactory: moduleFactory;\">\n * </ng-container>\n * ```\n *\n * ### A simple example\n *\n * {@example common/ngComponentOutlet/ts/module.ts region='SimpleExample'}\n *\n * A more complete example with additional options:\n *\n * {@example common/ngComponentOutlet/ts/module.ts region='CompleteExample'}\n *\n * @publicApi\n * @ngModule CommonModule\n */\n\n\nlet NgComponentOutlet = /*#__PURE__*/(() => {\n  class NgComponentOutlet {\n    constructor(_viewContainerRef) {\n      this._viewContainerRef = _viewContainerRef;\n      this._componentRef = null;\n      this._moduleRef = null;\n    }\n\n    ngOnChanges(changes) {\n      this._viewContainerRef.clear();\n\n      this._componentRef = null;\n\n      if (this.ngComponentOutlet) {\n        const elInjector = this.ngComponentOutletInjector || this._viewContainerRef.parentInjector;\n\n        if (changes['ngComponentOutletNgModuleFactory']) {\n          if (this._moduleRef) this._moduleRef.destroy();\n\n          if (this.ngComponentOutletNgModuleFactory) {\n            const parentModule = elInjector.get(NgModuleRef);\n            this._moduleRef = this.ngComponentOutletNgModuleFactory.create(parentModule.injector);\n          } else {\n            this._moduleRef = null;\n          }\n        }\n\n        const componentFactoryResolver = this._moduleRef ? this._moduleRef.componentFactoryResolver : elInjector.get(ComponentFactoryResolver);\n        const componentFactory = componentFactoryResolver.resolveComponentFactory(this.ngComponentOutlet);\n        this._componentRef = this._viewContainerRef.createComponent(componentFactory, this._viewContainerRef.length, elInjector, this.ngComponentOutletContent);\n      }\n    }\n\n    ngOnDestroy() {\n      if (this._moduleRef) this._moduleRef.destroy();\n    }\n\n  }\n\n  NgComponentOutlet.ɵfac = function NgComponentOutlet_Factory(t) {\n    return new (t || NgComponentOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n  };\n\n  NgComponentOutlet.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgComponentOutlet,\n    selectors: [[\"\", \"ngComponentOutlet\", \"\"]],\n    inputs: {\n      ngComponentOutlet: \"ngComponentOutlet\",\n      ngComponentOutletInjector: \"ngComponentOutletInjector\",\n      ngComponentOutletContent: \"ngComponentOutletContent\",\n      ngComponentOutletNgModuleFactory: \"ngComponentOutletNgModuleFactory\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return NgComponentOutlet;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nclass NgForOfContext {\n  constructor($implicit, ngForOf, index, count) {\n    this.$implicit = $implicit;\n    this.ngForOf = ngForOf;\n    this.index = index;\n    this.count = count;\n  }\n\n  get first() {\n    return this.index === 0;\n  }\n\n  get last() {\n    return this.index === this.count - 1;\n  }\n\n  get even() {\n    return this.index % 2 === 0;\n  }\n\n  get odd() {\n    return !this.even;\n  }\n\n}\n/**\n * A [structural directive](guide/structural-directives) that renders\n * a template for each item in a collection.\n * The directive is placed on an element, which becomes the parent\n * of the cloned templates.\n *\n * The `ngForOf` directive is generally used in the\n * [shorthand form](guide/structural-directives#asterisk) `*ngFor`.\n * In this form, the template to be rendered for each iteration is the content\n * of an anchor element containing the directive.\n *\n * The following example shows the shorthand syntax with some options,\n * contained in an `<li>` element.\n *\n * ```\n * <li *ngFor=\"let item of items; index as i; trackBy: trackByFn\">...</li>\n * ```\n *\n * The shorthand form expands into a long form that uses the `ngForOf` selector\n * on an `<ng-template>` element.\n * The content of the `<ng-template>` element is the `<li>` element that held the\n * short-form directive.\n *\n * Here is the expanded version of the short-form example.\n *\n * ```\n * <ng-template ngFor let-item [ngForOf]=\"items\" let-i=\"index\" [ngForTrackBy]=\"trackByFn\">\n *   <li>...</li>\n * </ng-template>\n * ```\n *\n * Angular automatically expands the shorthand syntax as it compiles the template.\n * The context for each embedded view is logically merged to the current component\n * context according to its lexical position.\n *\n * When using the shorthand syntax, Angular allows only [one structural directive\n * on an element](guide/built-in-directives#one-per-element).\n * If you want to iterate conditionally, for example,\n * put the `*ngIf` on a container element that wraps the `*ngFor` element.\n * For futher discussion, see\n * [Structural Directives](guide/built-in-directives#one-per-element).\n *\n * @usageNotes\n *\n * ### Local variables\n *\n * `NgForOf` provides exported values that can be aliased to local variables.\n * For example:\n *\n *  ```\n * <li *ngFor=\"let user of users; index as i; first as isFirst\">\n *    {{i}}/{{users.length}}. {{user}} <span *ngIf=\"isFirst\">default</span>\n * </li>\n * ```\n *\n * The following exported values can be aliased to local variables:\n *\n * - `$implicit: T`: The value of the individual items in the iterable (`ngForOf`).\n * - `ngForOf: NgIterable<T>`: The value of the iterable expression. Useful when the expression is\n * more complex then a property access, for example when using the async pipe (`userStreams |\n * async`).\n * - `index: number`: The index of the current item in the iterable.\n * - `count: number`: The length of the iterable.\n * - `first: boolean`: True when the item is the first item in the iterable.\n * - `last: boolean`: True when the item is the last item in the iterable.\n * - `even: boolean`: True when the item has an even index in the iterable.\n * - `odd: boolean`: True when the item has an odd index in the iterable.\n *\n * ### Change propagation\n *\n * When the contents of the iterator changes, `NgForOf` makes the corresponding changes to the DOM:\n *\n * * When an item is added, a new instance of the template is added to the DOM.\n * * When an item is removed, its template instance is removed from the DOM.\n * * When items are reordered, their respective templates are reordered in the DOM.\n *\n * Angular uses object identity to track insertions and deletions within the iterator and reproduce\n * those changes in the DOM. This has important implications for animations and any stateful\n * controls that are present, such as `<input>` elements that accept user input. Inserted rows can\n * be animated in, deleted rows can be animated out, and unchanged rows retain any unsaved state\n * such as user input.\n * For more on animations, see [Transitions and Triggers](guide/transition-and-triggers).\n *\n * The identities of elements in the iterator can change while the data does not.\n * This can happen, for example, if the iterator is produced from an RPC to the server, and that\n * RPC is re-run. Even if the data hasn't changed, the second response produces objects with\n * different identities, and Angular must tear down the entire DOM and rebuild it (as if all old\n * elements were deleted and all new elements inserted).\n *\n * To avoid this expensive operation, you can customize the default tracking algorithm.\n * by supplying the `trackBy` option to `NgForOf`.\n * `trackBy` takes a function that has two arguments: `index` and `item`.\n * If `trackBy` is given, Angular tracks changes by the return value of the function.\n *\n * @see [Structural Directives](guide/structural-directives)\n * @ngModule CommonModule\n * @publicApi\n */\n\n\nlet NgForOf = /*#__PURE__*/(() => {\n  class NgForOf {\n    constructor(_viewContainer, _template, _differs) {\n      this._viewContainer = _viewContainer;\n      this._template = _template;\n      this._differs = _differs;\n      this._ngForOf = null;\n      this._ngForOfDirty = true;\n      this._differ = null;\n    }\n    /**\n     * The value of the iterable expression, which can be used as a\n     * [template input variable](guide/structural-directives#shorthand).\n     */\n\n\n    set ngForOf(ngForOf) {\n      this._ngForOf = ngForOf;\n      this._ngForOfDirty = true;\n    }\n    /**\n     * Specifies a custom `TrackByFunction` to compute the identity of items in an iterable.\n     *\n     * If a custom `TrackByFunction` is not provided, `NgForOf` will use the item's [object\n     * identity](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is)\n     * as the key.\n     *\n     * `NgForOf` uses the computed key to associate items in an iterable with DOM elements\n     * it produces for these items.\n     *\n     * A custom `TrackByFunction` is useful to provide good user experience in cases when items in an\n     * iterable rendered using `NgForOf` have a natural identifier (for example, custom ID or a\n     * primary key), and this iterable could be updated with new object instances that still\n     * represent the same underlying entity (for example, when data is re-fetched from the server,\n     * and the iterable is recreated and re-rendered, but most of the data is still the same).\n     *\n     * @see `TrackByFunction`\n     */\n\n\n    set ngForTrackBy(fn) {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n        // TODO(vicb): use a log service once there is a public one available\n        if (console && console.warn) {\n          console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}. ` + `See https://angular.io/api/common/NgForOf#change-propagation for more information.`);\n        }\n      }\n\n      this._trackByFn = fn;\n    }\n\n    get ngForTrackBy() {\n      return this._trackByFn;\n    }\n    /**\n     * A reference to the template that is stamped out for each item in the iterable.\n     * @see [template reference variable](guide/template-reference-variables)\n     */\n\n\n    set ngForTemplate(value) {\n      // TODO(TS2.1): make TemplateRef<Partial<NgForRowOf<T>>> once we move to TS v2.1\n      // The current type is too restrictive; a template that just uses index, for example,\n      // should be acceptable.\n      if (value) {\n        this._template = value;\n      }\n    }\n    /**\n     * Applies the changes when needed.\n     */\n\n\n    ngDoCheck() {\n      if (this._ngForOfDirty) {\n        this._ngForOfDirty = false; // React on ngForOf changes only once all inputs have been initialized\n\n        const value = this._ngForOf;\n\n        if (!this._differ && value) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            try {\n              // CAUTION: this logic is duplicated for production mode below, as the try-catch\n              // is only present in development builds.\n              this._differ = this._differs.find(value).create(this.ngForTrackBy);\n            } catch (_a) {\n              throw new Error(`Cannot find a differ supporting object '${value}' of type '${getTypeName(value)}'. NgFor only supports binding to Iterables such as Arrays.`);\n            }\n          } else {\n            // CAUTION: this logic is duplicated for development mode above, as the try-catch\n            // is only present in development builds.\n            this._differ = this._differs.find(value).create(this.ngForTrackBy);\n          }\n        }\n      }\n\n      if (this._differ) {\n        const changes = this._differ.diff(this._ngForOf);\n\n        if (changes) this._applyChanges(changes);\n      }\n    }\n\n    _applyChanges(changes) {\n      const viewContainer = this._viewContainer;\n      changes.forEachOperation((item, adjustedPreviousIndex, currentIndex) => {\n        if (item.previousIndex == null) {\n          // NgForOf is never \"null\" or \"undefined\" here because the differ detected\n          // that a new item needs to be inserted from the iterable. This implies that\n          // there is an iterable value for \"_ngForOf\".\n          viewContainer.createEmbeddedView(this._template, new NgForOfContext(item.item, this._ngForOf, -1, -1), currentIndex === null ? undefined : currentIndex);\n        } else if (currentIndex == null) {\n          viewContainer.remove(adjustedPreviousIndex === null ? undefined : adjustedPreviousIndex);\n        } else if (adjustedPreviousIndex !== null) {\n          const view = viewContainer.get(adjustedPreviousIndex);\n          viewContainer.move(view, currentIndex);\n          applyViewChange(view, item);\n        }\n      });\n\n      for (let i = 0, ilen = viewContainer.length; i < ilen; i++) {\n        const viewRef = viewContainer.get(i);\n        const context = viewRef.context;\n        context.index = i;\n        context.count = ilen;\n        context.ngForOf = this._ngForOf;\n      }\n\n      changes.forEachIdentityChange(record => {\n        const viewRef = viewContainer.get(record.currentIndex);\n        applyViewChange(viewRef, record);\n      });\n    }\n    /**\n     * Asserts the correct type of the context for the template that `NgForOf` will render.\n     *\n     * The presence of this method is a signal to the Ivy template type-check compiler that the\n     * `NgForOf` structural directive renders its template with a specific context type.\n     */\n\n\n    static ngTemplateContextGuard(dir, ctx) {\n      return true;\n    }\n\n  }\n\n  NgForOf.ɵfac = function NgForOf_Factory(t) {\n    return new (t || NgForOf)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers));\n  };\n\n  NgForOf.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgForOf,\n    selectors: [[\"\", \"ngFor\", \"\", \"ngForOf\", \"\"]],\n    inputs: {\n      ngForOf: \"ngForOf\",\n      ngForTrackBy: \"ngForTrackBy\",\n      ngForTemplate: \"ngForTemplate\"\n    }\n  });\n  return NgForOf;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction applyViewChange(view, record) {\n  view.context.$implicit = record.item;\n}\n\nfunction getTypeName(type) {\n  return type['name'] || typeof type;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A structural directive that conditionally includes a template based on the value of\n * an expression coerced to Boolean.\n * When the expression evaluates to true, Angular renders the template\n * provided in a `then` clause, and when  false or null,\n * Angular renders the template provided in an optional `else` clause. The default\n * template for the `else` clause is blank.\n *\n * A [shorthand form](guide/structural-directives#asterisk) of the directive,\n * `*ngIf=\"condition\"`, is generally used, provided\n * as an attribute of the anchor element for the inserted template.\n * Angular expands this into a more explicit version, in which the anchor element\n * is contained in an `<ng-template>` element.\n *\n * Simple form with shorthand syntax:\n *\n * ```\n * <div *ngIf=\"condition\">Content to render when condition is true.</div>\n * ```\n *\n * Simple form with expanded syntax:\n *\n * ```\n * <ng-template [ngIf]=\"condition\"><div>Content to render when condition is\n * true.</div></ng-template>\n * ```\n *\n * Form with an \"else\" block:\n *\n * ```\n * <div *ngIf=\"condition; else elseBlock\">Content to render when condition is true.</div>\n * <ng-template #elseBlock>Content to render when condition is false.</ng-template>\n * ```\n *\n * Shorthand form with \"then\" and \"else\" blocks:\n *\n * ```\n * <div *ngIf=\"condition; then thenBlock else elseBlock\"></div>\n * <ng-template #thenBlock>Content to render when condition is true.</ng-template>\n * <ng-template #elseBlock>Content to render when condition is false.</ng-template>\n * ```\n *\n * Form with storing the value locally:\n *\n * ```\n * <div *ngIf=\"condition as value; else elseBlock\">{{value}}</div>\n * <ng-template #elseBlock>Content to render when value is null.</ng-template>\n * ```\n *\n * @usageNotes\n *\n * The `*ngIf` directive is most commonly used to conditionally show an inline template,\n * as seen in the following  example.\n * The default `else` template is blank.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfSimple'}\n *\n * ### Showing an alternative template using `else`\n *\n * To display a template when `expression` evaluates to false, use an `else` template\n * binding as shown in the following example.\n * The `else` binding points to an `<ng-template>`  element labeled `#elseBlock`.\n * The template can be defined anywhere in the component view, but is typically placed right after\n * `ngIf` for readability.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfElse'}\n *\n * ### Using an external `then` template\n *\n * In the previous example, the then-clause template is specified inline, as the content of the\n * tag that contains the `ngIf` directive. You can also specify a template that is defined\n * externally, by referencing a labeled `<ng-template>` element. When you do this, you can\n * change which template to use at runtime, as shown in the following example.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfThenElse'}\n *\n * ### Storing a conditional result in a variable\n *\n * You might want to show a set of properties from the same object. If you are waiting\n * for asynchronous data, the object can be undefined.\n * In this case, you can use `ngIf` and store the result of the condition in a local\n * variable as shown in the following example.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfAs'}\n *\n * This code uses only one `AsyncPipe`, so only one subscription is created.\n * The conditional statement stores the result of `userStream|async` in the local variable `user`.\n * You can then bind the local `user` repeatedly.\n *\n * The conditional displays the data only if `userStream` returns a value,\n * so you don't need to use the\n * safe-navigation-operator (`?.`)\n * to guard against null values when accessing properties.\n * You can display an alternative template while waiting for the data.\n *\n * ### Shorthand syntax\n *\n * The shorthand syntax `*ngIf` expands into two separate template specifications\n * for the \"then\" and \"else\" clauses. For example, consider the following shorthand statement,\n * that is meant to show a loading page while waiting for data to be loaded.\n *\n * ```\n * <div class=\"hero-list\" *ngIf=\"heroes else loading\">\n *  ...\n * </div>\n *\n * <ng-template #loading>\n *  <div>Loading...</div>\n * </ng-template>\n * ```\n *\n * You can see that the \"else\" clause references the `<ng-template>`\n * with the `#loading` label, and the template for the \"then\" clause\n * is provided as the content of the anchor element.\n *\n * However, when Angular expands the shorthand syntax, it creates\n * another `<ng-template>` tag, with `ngIf` and `ngIfElse` directives.\n * The anchor element containing the template for the \"then\" clause becomes\n * the content of this unlabeled `<ng-template>` tag.\n *\n * ```\n * <ng-template [ngIf]=\"heroes\" [ngIfElse]=\"loading\">\n *  <div class=\"hero-list\">\n *   ...\n *  </div>\n * </ng-template>\n *\n * <ng-template #loading>\n *  <div>Loading...</div>\n * </ng-template>\n * ```\n *\n * The presence of the implicit template object has implications for the nesting of\n * structural directives. For more on this subject, see\n * [Structural Directives](https://angular.io/guide/built-in-directives#one-per-element).\n *\n * @ngModule CommonModule\n * @publicApi\n */\n\n\nlet NgIf = /*#__PURE__*/(() => {\n  class NgIf {\n    constructor(_viewContainer, templateRef) {\n      this._viewContainer = _viewContainer;\n      this._context = new NgIfContext();\n      this._thenTemplateRef = null;\n      this._elseTemplateRef = null;\n      this._thenViewRef = null;\n      this._elseViewRef = null;\n      this._thenTemplateRef = templateRef;\n    }\n    /**\n     * The Boolean expression to evaluate as the condition for showing a template.\n     */\n\n\n    set ngIf(condition) {\n      this._context.$implicit = this._context.ngIf = condition;\n\n      this._updateView();\n    }\n    /**\n     * A template to show if the condition expression evaluates to true.\n     */\n\n\n    set ngIfThen(templateRef) {\n      assertTemplate('ngIfThen', templateRef);\n      this._thenTemplateRef = templateRef;\n      this._thenViewRef = null; // clear previous view if any.\n\n      this._updateView();\n    }\n    /**\n     * A template to show if the condition expression evaluates to false.\n     */\n\n\n    set ngIfElse(templateRef) {\n      assertTemplate('ngIfElse', templateRef);\n      this._elseTemplateRef = templateRef;\n      this._elseViewRef = null; // clear previous view if any.\n\n      this._updateView();\n    }\n\n    _updateView() {\n      if (this._context.$implicit) {\n        if (!this._thenViewRef) {\n          this._viewContainer.clear();\n\n          this._elseViewRef = null;\n\n          if (this._thenTemplateRef) {\n            this._thenViewRef = this._viewContainer.createEmbeddedView(this._thenTemplateRef, this._context);\n          }\n        }\n      } else {\n        if (!this._elseViewRef) {\n          this._viewContainer.clear();\n\n          this._thenViewRef = null;\n\n          if (this._elseTemplateRef) {\n            this._elseViewRef = this._viewContainer.createEmbeddedView(this._elseTemplateRef, this._context);\n          }\n        }\n      }\n    }\n    /**\n     * Asserts the correct type of the context for the template that `NgIf` will render.\n     *\n     * The presence of this method is a signal to the Ivy template type-check compiler that the\n     * `NgIf` structural directive renders its template with a specific context type.\n     */\n\n\n    static ngTemplateContextGuard(dir, ctx) {\n      return true;\n    }\n\n  }\n\n  NgIf.ɵfac = function NgIf_Factory(t) {\n    return new (t || NgIf)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef));\n  };\n\n  NgIf.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgIf,\n    selectors: [[\"\", \"ngIf\", \"\"]],\n    inputs: {\n      ngIf: \"ngIf\",\n      ngIfThen: \"ngIfThen\",\n      ngIfElse: \"ngIfElse\"\n    }\n  });\n  return NgIf;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @publicApi\n */\n\n\nclass NgIfContext {\n  constructor() {\n    this.$implicit = null;\n    this.ngIf = null;\n  }\n\n}\n\nfunction assertTemplate(property, templateRef) {\n  const isTemplateRefOrNull = !!(!templateRef || templateRef.createEmbeddedView);\n\n  if (!isTemplateRefOrNull) {\n    throw new Error(`${property} must be a TemplateRef, but received '${ɵstringify(templateRef)}'.`);\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass SwitchView {\n  constructor(_viewContainerRef, _templateRef) {\n    this._viewContainerRef = _viewContainerRef;\n    this._templateRef = _templateRef;\n    this._created = false;\n  }\n\n  create() {\n    this._created = true;\n\n    this._viewContainerRef.createEmbeddedView(this._templateRef);\n  }\n\n  destroy() {\n    this._created = false;\n\n    this._viewContainerRef.clear();\n  }\n\n  enforceState(created) {\n    if (created && !this._created) {\n      this.create();\n    } else if (!created && this._created) {\n      this.destroy();\n    }\n  }\n\n}\n/**\n * @ngModule CommonModule\n *\n * @description\n * The `[ngSwitch]` directive on a container specifies an expression to match against.\n * The expressions to match are provided by `ngSwitchCase` directives on views within the container.\n * - Every view that matches is rendered.\n * - If there are no matches, a view with the `ngSwitchDefault` directive is rendered.\n * - Elements within the `[NgSwitch]` statement but outside of any `NgSwitchCase`\n * or `ngSwitchDefault` directive are preserved at the location.\n *\n * @usageNotes\n * Define a container element for the directive, and specify the switch expression\n * to match against as an attribute:\n *\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n * ```\n *\n * Within the container, `*ngSwitchCase` statements specify the match expressions\n * as attributes. Include `*ngSwitchDefault` as the final case.\n *\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *    <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n * ...\n *    <some-element *ngSwitchDefault>...</some-element>\n * </container-element>\n * ```\n *\n * ### Usage Examples\n *\n * The following example shows how to use more than one case to display the same view:\n *\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <!-- the same view can be shown in more than one case -->\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *   <some-element *ngSwitchCase=\"match_expression_2\">...</some-element>\n *   <some-other-element *ngSwitchCase=\"match_expression_3\">...</some-other-element>\n *   <!--default case when there are no matches -->\n *   <some-element *ngSwitchDefault>...</some-element>\n * </container-element>\n * ```\n *\n * The following example shows how cases can be nested:\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *       <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *       <some-element *ngSwitchCase=\"match_expression_2\">...</some-element>\n *       <some-other-element *ngSwitchCase=\"match_expression_3\">...</some-other-element>\n *       <ng-container *ngSwitchCase=\"match_expression_3\">\n *         <!-- use a ng-container to group multiple root nodes -->\n *         <inner-element></inner-element>\n *         <inner-other-element></inner-other-element>\n *       </ng-container>\n *       <some-element *ngSwitchDefault>...</some-element>\n *     </container-element>\n * ```\n *\n * @publicApi\n * @see `NgSwitchCase`\n * @see `NgSwitchDefault`\n * @see [Structural Directives](guide/structural-directives)\n *\n */\n\n\nlet NgSwitch = /*#__PURE__*/(() => {\n  class NgSwitch {\n    constructor() {\n      this._defaultUsed = false;\n      this._caseCount = 0;\n      this._lastCaseCheckIndex = 0;\n      this._lastCasesMatched = false;\n    }\n\n    set ngSwitch(newValue) {\n      this._ngSwitch = newValue;\n\n      if (this._caseCount === 0) {\n        this._updateDefaultCases(true);\n      }\n    }\n    /** @internal */\n\n\n    _addCase() {\n      return this._caseCount++;\n    }\n    /** @internal */\n\n\n    _addDefault(view) {\n      if (!this._defaultViews) {\n        this._defaultViews = [];\n      }\n\n      this._defaultViews.push(view);\n    }\n    /** @internal */\n\n\n    _matchCase(value) {\n      const matched = value == this._ngSwitch;\n      this._lastCasesMatched = this._lastCasesMatched || matched;\n      this._lastCaseCheckIndex++;\n\n      if (this._lastCaseCheckIndex === this._caseCount) {\n        this._updateDefaultCases(!this._lastCasesMatched);\n\n        this._lastCaseCheckIndex = 0;\n        this._lastCasesMatched = false;\n      }\n\n      return matched;\n    }\n\n    _updateDefaultCases(useDefault) {\n      if (this._defaultViews && useDefault !== this._defaultUsed) {\n        this._defaultUsed = useDefault;\n\n        for (let i = 0; i < this._defaultViews.length; i++) {\n          const defaultView = this._defaultViews[i];\n          defaultView.enforceState(useDefault);\n        }\n      }\n    }\n\n  }\n\n  NgSwitch.ɵfac = function NgSwitch_Factory(t) {\n    return new (t || NgSwitch)();\n  };\n\n  NgSwitch.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgSwitch,\n    selectors: [[\"\", \"ngSwitch\", \"\"]],\n    inputs: {\n      ngSwitch: \"ngSwitch\"\n    }\n  });\n  return NgSwitch;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @ngModule CommonModule\n *\n * @description\n * Provides a switch case expression to match against an enclosing `ngSwitch` expression.\n * When the expressions match, the given `NgSwitchCase` template is rendered.\n * If multiple match expressions match the switch expression value, all of them are displayed.\n *\n * @usageNotes\n *\n * Within a switch container, `*ngSwitchCase` statements specify the match expressions\n * as attributes. Include `*ngSwitchDefault` as the final case.\n *\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *   ...\n *   <some-element *ngSwitchDefault>...</some-element>\n * </container-element>\n * ```\n *\n * Each switch-case statement contains an in-line HTML template or template reference\n * that defines the subtree to be selected if the value of the match expression\n * matches the value of the switch expression.\n *\n * Unlike JavaScript, which uses strict equality, Angular uses loose equality.\n * This means that the empty string, `\"\"` matches 0.\n *\n * @publicApi\n * @see `NgSwitch`\n * @see `NgSwitchDefault`\n *\n */\n\n\nlet NgSwitchCase = /*#__PURE__*/(() => {\n  class NgSwitchCase {\n    constructor(viewContainer, templateRef, ngSwitch) {\n      this.ngSwitch = ngSwitch;\n\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !ngSwitch) {\n        throwNgSwitchProviderNotFoundError('ngSwitchCase', 'NgSwitchCase');\n      }\n\n      ngSwitch._addCase();\n\n      this._view = new SwitchView(viewContainer, templateRef);\n    }\n    /**\n     * Performs case matching. For internal use only.\n     */\n\n\n    ngDoCheck() {\n      this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase));\n    }\n\n  }\n\n  NgSwitchCase.ɵfac = function NgSwitchCase_Factory(t) {\n    return new (t || NgSwitchCase)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(NgSwitch, 9));\n  };\n\n  NgSwitchCase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgSwitchCase,\n    selectors: [[\"\", \"ngSwitchCase\", \"\"]],\n    inputs: {\n      ngSwitchCase: \"ngSwitchCase\"\n    }\n  });\n  return NgSwitchCase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @ngModule CommonModule\n *\n * @description\n *\n * Creates a view that is rendered when no `NgSwitchCase` expressions\n * match the `NgSwitch` expression.\n * This statement should be the final case in an `NgSwitch`.\n *\n * @publicApi\n * @see `NgSwitch`\n * @see `NgSwitchCase`\n *\n */\n\n\nlet NgSwitchDefault = /*#__PURE__*/(() => {\n  class NgSwitchDefault {\n    constructor(viewContainer, templateRef, ngSwitch) {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !ngSwitch) {\n        throwNgSwitchProviderNotFoundError('ngSwitchDefault', 'NgSwitchDefault');\n      }\n\n      ngSwitch._addDefault(new SwitchView(viewContainer, templateRef));\n    }\n\n  }\n\n  NgSwitchDefault.ɵfac = function NgSwitchDefault_Factory(t) {\n    return new (t || NgSwitchDefault)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(NgSwitch, 9));\n  };\n\n  NgSwitchDefault.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgSwitchDefault,\n    selectors: [[\"\", \"ngSwitchDefault\", \"\"]]\n  });\n  return NgSwitchDefault;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction throwNgSwitchProviderNotFoundError(attrName, directiveName) {\n  throw new ɵRuntimeError(2000\n  /* PARENT_NG_SWITCH_NOT_FOUND */\n  , `An element with the \"${attrName}\" attribute ` + `(matching the \"${directiveName}\" directive) must be located inside an element with the \"ngSwitch\" attribute ` + `(matching \"NgSwitch\" directive)`);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @ngModule CommonModule\n *\n * @usageNotes\n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">there is nothing</ng-template>\n *   <ng-template ngPluralCase=\"=1\">there is one</ng-template>\n *   <ng-template ngPluralCase=\"few\">there are a few</ng-template>\n * </some-element>\n * ```\n *\n * @description\n *\n * Adds / removes DOM sub-trees based on a numeric value. Tailored for pluralization.\n *\n * Displays DOM sub-trees that match the switch expression value, or failing that, DOM sub-trees\n * that match the switch expression's pluralization category.\n *\n * To use this directive you must provide a container element that sets the `[ngPlural]` attribute\n * to a switch expression. Inner elements with a `[ngPluralCase]` will display based on their\n * expression:\n * - if `[ngPluralCase]` is set to a value starting with `=`, it will only display if the value\n *   matches the switch expression exactly,\n * - otherwise, the view will be treated as a \"category match\", and will only display if exact\n *   value matches aren't found and the value maps to its category for the defined locale.\n *\n * See http://cldr.unicode.org/index/cldr-spec/plural-rules\n *\n * @publicApi\n */\n\n\nlet NgPlural = /*#__PURE__*/(() => {\n  class NgPlural {\n    constructor(_localization) {\n      this._localization = _localization;\n      this._caseViews = {};\n    }\n\n    set ngPlural(value) {\n      this._switchValue = value;\n\n      this._updateView();\n    }\n\n    addCase(value, switchView) {\n      this._caseViews[value] = switchView;\n    }\n\n    _updateView() {\n      this._clearViews();\n\n      const cases = Object.keys(this._caseViews);\n      const key = getPluralCategory(this._switchValue, cases, this._localization);\n\n      this._activateView(this._caseViews[key]);\n    }\n\n    _clearViews() {\n      if (this._activeView) this._activeView.destroy();\n    }\n\n    _activateView(view) {\n      if (view) {\n        this._activeView = view;\n\n        this._activeView.create();\n      }\n    }\n\n  }\n\n  NgPlural.ɵfac = function NgPlural_Factory(t) {\n    return new (t || NgPlural)(i0.ɵɵdirectiveInject(NgLocalization));\n  };\n\n  NgPlural.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgPlural,\n    selectors: [[\"\", \"ngPlural\", \"\"]],\n    inputs: {\n      ngPlural: \"ngPlural\"\n    }\n  });\n  return NgPlural;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @ngModule CommonModule\n *\n * @description\n *\n * Creates a view that will be added/removed from the parent {@link NgPlural} when the\n * given expression matches the plural expression according to CLDR rules.\n *\n * @usageNotes\n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">...</ng-template>\n *   <ng-template ngPluralCase=\"other\">...</ng-template>\n * </some-element>\n *```\n *\n * See {@link NgPlural} for more details and example.\n *\n * @publicApi\n */\n\n\nlet NgPluralCase = /*#__PURE__*/(() => {\n  class NgPluralCase {\n    constructor(value, template, viewContainer, ngPlural) {\n      this.value = value;\n      const isANumber = !isNaN(Number(value));\n      ngPlural.addCase(isANumber ? `=${value}` : value, new SwitchView(viewContainer, template));\n    }\n\n  }\n\n  NgPluralCase.ɵfac = function NgPluralCase_Factory(t) {\n    return new (t || NgPluralCase)(i0.ɵɵinjectAttribute('ngPluralCase'), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(NgPlural, 1));\n  };\n\n  NgPluralCase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgPluralCase,\n    selectors: [[\"\", \"ngPluralCase\", \"\"]]\n  });\n  return NgPluralCase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @ngModule CommonModule\n *\n * @usageNotes\n *\n * Set the font of the containing element to the result of an expression.\n *\n * ```\n * <some-element [ngStyle]=\"{'font-style': styleExp}\">...</some-element>\n * ```\n *\n * Set the width of the containing element to a pixel value returned by an expression.\n *\n * ```\n * <some-element [ngStyle]=\"{'max-width.px': widthExp}\">...</some-element>\n * ```\n *\n * Set a collection of style values using an expression that returns key-value pairs.\n *\n * ```\n * <some-element [ngStyle]=\"objExp\">...</some-element>\n * ```\n *\n * @description\n *\n * An attribute directive that updates styles for the containing HTML element.\n * Sets one or more style properties, specified as colon-separated key-value pairs.\n * The key is a style name, with an optional `.<unit>` suffix\n * (such as 'top.px', 'font-style.em').\n * The value is an expression to be evaluated.\n * The resulting non-null value, expressed in the given unit,\n * is assigned to the given style property.\n * If the result of evaluation is null, the corresponding style is removed.\n *\n * @publicApi\n */\n\n\nlet NgStyle = /*#__PURE__*/(() => {\n  class NgStyle {\n    constructor(_ngEl, _differs, _renderer) {\n      this._ngEl = _ngEl;\n      this._differs = _differs;\n      this._renderer = _renderer;\n      this._ngStyle = null;\n      this._differ = null;\n    }\n\n    set ngStyle(values) {\n      this._ngStyle = values;\n\n      if (!this._differ && values) {\n        this._differ = this._differs.find(values).create();\n      }\n    }\n\n    ngDoCheck() {\n      if (this._differ) {\n        const changes = this._differ.diff(this._ngStyle);\n\n        if (changes) {\n          this._applyChanges(changes);\n        }\n      }\n    }\n\n    _setStyle(nameAndUnit, value) {\n      const [name, unit] = nameAndUnit.split('.');\n      value = value != null && unit ? `${value}${unit}` : value;\n\n      if (value != null) {\n        this._renderer.setStyle(this._ngEl.nativeElement, name, value);\n      } else {\n        this._renderer.removeStyle(this._ngEl.nativeElement, name);\n      }\n    }\n\n    _applyChanges(changes) {\n      changes.forEachRemovedItem(record => this._setStyle(record.key, null));\n      changes.forEachAddedItem(record => this._setStyle(record.key, record.currentValue));\n      changes.forEachChangedItem(record => this._setStyle(record.key, record.currentValue));\n    }\n\n  }\n\n  NgStyle.ɵfac = function NgStyle_Factory(t) {\n    return new (t || NgStyle)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.KeyValueDiffers), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n\n  NgStyle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgStyle,\n    selectors: [[\"\", \"ngStyle\", \"\"]],\n    inputs: {\n      ngStyle: \"ngStyle\"\n    }\n  });\n  return NgStyle;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @ngModule CommonModule\n *\n * @description\n *\n * Inserts an embedded view from a prepared `TemplateRef`.\n *\n * You can attach a context object to the `EmbeddedViewRef` by setting `[ngTemplateOutletContext]`.\n * `[ngTemplateOutletContext]` should be an object, the object's keys will be available for binding\n * by the local template `let` declarations.\n *\n * @usageNotes\n * ```\n * <ng-container *ngTemplateOutlet=\"templateRefExp; context: contextExp\"></ng-container>\n * ```\n *\n * Using the key `$implicit` in the context object will set its value as default.\n *\n * ### Example\n *\n * {@example common/ngTemplateOutlet/ts/module.ts region='NgTemplateOutlet'}\n *\n * @publicApi\n */\n\n\nlet NgTemplateOutlet = /*#__PURE__*/(() => {\n  class NgTemplateOutlet {\n    constructor(_viewContainerRef) {\n      this._viewContainerRef = _viewContainerRef;\n      this._viewRef = null;\n      /**\n       * A context object to attach to the {@link EmbeddedViewRef}. This should be an\n       * object, the object's keys will be available for binding by the local template `let`\n       * declarations.\n       * Using the key `$implicit` in the context object will set its value as default.\n       */\n\n      this.ngTemplateOutletContext = null;\n      /**\n       * A string defining the template reference and optionally the context object for the template.\n       */\n\n      this.ngTemplateOutlet = null;\n    }\n\n    ngOnChanges(changes) {\n      if (changes['ngTemplateOutlet']) {\n        const viewContainerRef = this._viewContainerRef;\n\n        if (this._viewRef) {\n          viewContainerRef.remove(viewContainerRef.indexOf(this._viewRef));\n        }\n\n        this._viewRef = this.ngTemplateOutlet ? viewContainerRef.createEmbeddedView(this.ngTemplateOutlet, this.ngTemplateOutletContext) : null;\n      } else if (this._viewRef && changes['ngTemplateOutletContext'] && this.ngTemplateOutletContext) {\n        this._viewRef.context = this.ngTemplateOutletContext;\n      }\n    }\n\n  }\n\n  NgTemplateOutlet.ɵfac = function NgTemplateOutlet_Factory(t) {\n    return new (t || NgTemplateOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n  };\n\n  NgTemplateOutlet.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgTemplateOutlet,\n    selectors: [[\"\", \"ngTemplateOutlet\", \"\"]],\n    inputs: {\n      ngTemplateOutletContext: \"ngTemplateOutletContext\",\n      ngTemplateOutlet: \"ngTemplateOutlet\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return NgTemplateOutlet;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A collection of Angular directives that are likely to be used in each and every Angular\n * application.\n */\n\n\nconst COMMON_DIRECTIVES = [NgClass, NgComponentOutlet, NgForOf, NgIf, NgTemplateOutlet, NgStyle, NgSwitch, NgSwitchCase, NgSwitchDefault, NgPlural, NgPluralCase];\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nfunction invalidPipeArgumentError(type, value) {\n  const errorMessage = typeof ngDevMode === 'undefined' || ngDevMode ? `InvalidPipeArgument: '${value}' for pipe '${ɵstringify(type)}'` : '';\n  return new ɵRuntimeError(2100\n  /* INVALID_PIPE_ARGUMENT */\n  , errorMessage);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass SubscribableStrategy {\n  createSubscription(async, updateLatestValue) {\n    return async.subscribe({\n      next: updateLatestValue,\n      error: e => {\n        throw e;\n      }\n    });\n  }\n\n  dispose(subscription) {\n    subscription.unsubscribe();\n  }\n\n  onDestroy(subscription) {\n    subscription.unsubscribe();\n  }\n\n}\n\nclass PromiseStrategy {\n  createSubscription(async, updateLatestValue) {\n    return async.then(updateLatestValue, e => {\n      throw e;\n    });\n  }\n\n  dispose(subscription) {}\n\n  onDestroy(subscription) {}\n\n}\n\nconst _promiseStrategy = /*#__PURE__*/new PromiseStrategy();\n\nconst _subscribableStrategy = /*#__PURE__*/new SubscribableStrategy();\n/**\n * @ngModule CommonModule\n * @description\n *\n * Unwraps a value from an asynchronous primitive.\n *\n * The `async` pipe subscribes to an `Observable` or `Promise` and returns the latest value it has\n * emitted. When a new value is emitted, the `async` pipe marks the component to be checked for\n * changes. When the component gets destroyed, the `async` pipe unsubscribes automatically to avoid\n * potential memory leaks. When the reference of the expression changes, the `async` pipe\n * automatically unsubscribes from the old `Observable` or `Promise` and subscribes to the new one.\n *\n * @usageNotes\n *\n * ### Examples\n *\n * This example binds a `Promise` to the view. Clicking the `Resolve` button resolves the\n * promise.\n *\n * {@example common/pipes/ts/async_pipe.ts region='AsyncPipePromise'}\n *\n * It's also possible to use `async` with Observables. The example below binds the `time` Observable\n * to the view. The Observable continuously updates the view with the current time.\n *\n * {@example common/pipes/ts/async_pipe.ts region='AsyncPipeObservable'}\n *\n * @publicApi\n */\n\n\nlet AsyncPipe = /*#__PURE__*/(() => {\n  class AsyncPipe {\n    constructor(_ref) {\n      this._ref = _ref;\n      this._latestValue = null;\n      this._subscription = null;\n      this._obj = null;\n      this._strategy = null;\n    }\n\n    ngOnDestroy() {\n      if (this._subscription) {\n        this._dispose();\n      }\n    }\n\n    transform(obj) {\n      if (!this._obj) {\n        if (obj) {\n          this._subscribe(obj);\n        }\n\n        return this._latestValue;\n      }\n\n      if (obj !== this._obj) {\n        this._dispose();\n\n        return this.transform(obj);\n      }\n\n      return this._latestValue;\n    }\n\n    _subscribe(obj) {\n      this._obj = obj;\n      this._strategy = this._selectStrategy(obj);\n      this._subscription = this._strategy.createSubscription(obj, value => this._updateLatestValue(obj, value));\n    }\n\n    _selectStrategy(obj) {\n      if (ɵisPromise(obj)) {\n        return _promiseStrategy;\n      }\n\n      if (ɵisSubscribable(obj)) {\n        return _subscribableStrategy;\n      }\n\n      throw invalidPipeArgumentError(AsyncPipe, obj);\n    }\n\n    _dispose() {\n      this._strategy.dispose(this._subscription);\n\n      this._latestValue = null;\n      this._subscription = null;\n      this._obj = null;\n    }\n\n    _updateLatestValue(async, value) {\n      if (async === this._obj) {\n        this._latestValue = value;\n\n        this._ref.markForCheck();\n      }\n    }\n\n  }\n\n  AsyncPipe.ɵfac = function AsyncPipe_Factory(t) {\n    return new (t || AsyncPipe)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef, 16));\n  };\n\n  AsyncPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"async\",\n    type: AsyncPipe,\n    pure: false\n  });\n  return AsyncPipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Transforms text to all lower case.\n *\n * @see `UpperCasePipe`\n * @see `TitleCasePipe`\n * @usageNotes\n *\n * The following example defines a view that allows the user to enter\n * text, and then uses the pipe to convert the input text to all lower case.\n *\n * <code-example path=\"common/pipes/ts/lowerupper_pipe.ts\" region='LowerUpperPipe'></code-example>\n *\n * @ngModule CommonModule\n * @publicApi\n */\n\n\nlet LowerCasePipe = /*#__PURE__*/(() => {\n  class LowerCasePipe {\n    transform(value) {\n      if (value == null) return null;\n\n      if (typeof value !== 'string') {\n        throw invalidPipeArgumentError(LowerCasePipe, value);\n      }\n\n      return value.toLowerCase();\n    }\n\n  }\n\n  LowerCasePipe.ɵfac = function LowerCasePipe_Factory(t) {\n    return new (t || LowerCasePipe)();\n  };\n\n  LowerCasePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"lowercase\",\n    type: LowerCasePipe,\n    pure: true\n  });\n  return LowerCasePipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})(); //\n// Regex below matches any Unicode word and number compatible with ES5. In ES2018 the same result\n// can be achieved by using /[0-9\\p{L}]\\S*/gu and also known as Unicode Property Escapes\n// (https://2ality.com/2017/07/regexp-unicode-property-escapes.html). Since there is no\n// transpilation of this functionality down to ES5 without external tool, the only solution is\n// to use already transpiled form. Example can be found here -\n// https://mothereff.in/regexpu#input=var+regex+%3D+%2F%5B0-9%5Cp%7BL%7D%5D%5CS*%2Fgu%3B%0A%0A&unicodePropertyEscape=1\n//\n\n\nconst unicodeWordMatch = /(?:[0-9A-Za-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u08A0-\\u08C9\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C5D\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D04-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16F1-\\u16F8\\u1700-\\u1711\\u171F-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4C\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF3\\u1CF5\\u1CF6\\u1CFA\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184\\u2C00-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CA\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7D9\\uA7F2-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF40\\uDF42-\\uDF49\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDD70-\\uDD7A\\uDD7C-\\uDD8A\\uDD8C-\\uDD92\\uDD94\\uDD95\\uDD97-\\uDDA1\\uDDA3-\\uDDB1\\uDDB3-\\uDDB9\\uDDBB\\uDDBC\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67\\uDF80-\\uDF85\\uDF87-\\uDFB0\\uDFB2-\\uDFBA]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD23\\uDE80-\\uDEA9\\uDEB0\\uDEB1\\uDF00-\\uDF1C\\uDF27\\uDF30-\\uDF45\\uDF70-\\uDF81\\uDFB0-\\uDFC4\\uDFE0-\\uDFF6]|\\uD804[\\uDC03-\\uDC37\\uDC71\\uDC72\\uDC75\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD44\\uDD47\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC5F-\\uDC61\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDEB8\\uDF00-\\uDF1A\\uDF40-\\uDF46]|\\uD806[\\uDC00-\\uDC2B\\uDCA0-\\uDCDF\\uDCFF-\\uDD06\\uDD09\\uDD0C-\\uDD13\\uDD15\\uDD16\\uDD18-\\uDD2F\\uDD3F\\uDD41\\uDDA0-\\uDDA7\\uDDAA-\\uDDD0\\uDDE1\\uDDE3\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE89\\uDE9D\\uDEB0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD89\\uDD98\\uDEE0-\\uDEF2\\uDFB0]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC80-\\uDD43]|\\uD80B[\\uDF90-\\uDFF0]|[\\uD80C\\uD81C-\\uD820\\uD822\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879\\uD880-\\uD883][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE70-\\uDEBE\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDE40-\\uDE7F\\uDF00-\\uDF4A\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1\\uDFE3]|\\uD821[\\uDC00-\\uDFF7]|\\uD823[\\uDC00-\\uDCD5\\uDD00-\\uDD08]|\\uD82B[\\uDFF0-\\uDFF3\\uDFF5-\\uDFFB\\uDFFD\\uDFFE]|\\uD82C[\\uDC00-\\uDD22\\uDD50-\\uDD52\\uDD64-\\uDD67\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD837[\\uDF00-\\uDF1E]|\\uD838[\\uDD00-\\uDD2C\\uDD37-\\uDD3D\\uDD4E\\uDE90-\\uDEAD\\uDEC0-\\uDEEB]|\\uD839[\\uDFE0-\\uDFE6\\uDFE8-\\uDFEB\\uDFED\\uDFEE\\uDFF0-\\uDFFE]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43\\uDD4B]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDEDF\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF38\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uD884[\\uDC00-\\uDF4A])\\S*/g;\n/**\n * Transforms text to title case.\n * Capitalizes the first letter of each word and transforms the\n * rest of the word to lower case.\n * Words are delimited by any whitespace character, such as a space, tab, or line-feed character.\n *\n * @see `LowerCasePipe`\n * @see `UpperCasePipe`\n *\n * @usageNotes\n * The following example shows the result of transforming various strings into title case.\n *\n * <code-example path=\"common/pipes/ts/titlecase_pipe.ts\" region='TitleCasePipe'></code-example>\n *\n * @ngModule CommonModule\n * @publicApi\n */\n\nlet TitleCasePipe = /*#__PURE__*/(() => {\n  class TitleCasePipe {\n    transform(value) {\n      if (value == null) return null;\n\n      if (typeof value !== 'string') {\n        throw invalidPipeArgumentError(TitleCasePipe, value);\n      }\n\n      return value.replace(unicodeWordMatch, txt => txt[0].toUpperCase() + txt.substr(1).toLowerCase());\n    }\n\n  }\n\n  TitleCasePipe.ɵfac = function TitleCasePipe_Factory(t) {\n    return new (t || TitleCasePipe)();\n  };\n\n  TitleCasePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"titlecase\",\n    type: TitleCasePipe,\n    pure: true\n  });\n  return TitleCasePipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Transforms text to all upper case.\n * @see `LowerCasePipe`\n * @see `TitleCasePipe`\n *\n * @ngModule CommonModule\n * @publicApi\n */\n\n\nlet UpperCasePipe = /*#__PURE__*/(() => {\n  class UpperCasePipe {\n    transform(value) {\n      if (value == null) return null;\n\n      if (typeof value !== 'string') {\n        throw invalidPipeArgumentError(UpperCasePipe, value);\n      }\n\n      return value.toUpperCase();\n    }\n\n  }\n\n  UpperCasePipe.ɵfac = function UpperCasePipe_Factory(t) {\n    return new (t || UpperCasePipe)();\n  };\n\n  UpperCasePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"uppercase\",\n    type: UpperCasePipe,\n    pure: true\n  });\n  return UpperCasePipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Optionally-provided default timezone to use for all instances of `DatePipe` (such as `'+0430'`).\n * If the value isn't provided, the `DatePipe` will use the end-user's local system timezone.\n */\n\n\nconst DATE_PIPE_DEFAULT_TIMEZONE = /*#__PURE__*/new InjectionToken('DATE_PIPE_DEFAULT_TIMEZONE'); // clang-format off\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a date value according to locale rules.\n *\n * `DatePipe` is executed only when it detects a pure change to the input value.\n * A pure change is either a change to a primitive input value\n * (such as `String`, `Number`, `Boolean`, or `Symbol`),\n * or a changed object reference (such as `Date`, `Array`, `Function`, or `Object`).\n *\n * Note that mutating a `Date` object does not cause the pipe to be rendered again.\n * To ensure that the pipe is executed, you must create a new `Date` object.\n *\n * Only the `en-US` locale data comes with Angular. To localize dates\n * in another language, you must import the corresponding locale data.\n * See the [I18n guide](guide/i18n-common-format-data-locale) for more information.\n *\n * The time zone of the formatted value can be specified either by passing it in as the second\n * parameter of the pipe, or by setting the default through the `DATE_PIPE_DEFAULT_TIMEZONE`\n * injection token. The value that is passed in as the second parameter takes precedence over\n * the one defined using the injection token.\n *\n * @see `formatDate()`\n *\n *\n * @usageNotes\n *\n * The result of this pipe is not reevaluated when the input is mutated. To avoid the need to\n * reformat the date on every change-detection cycle, treat the date as an immutable object\n * and change the reference when the pipe needs to run again.\n *\n * ### Pre-defined format options\n *\n * | Option        | Equivalent to                       | Examples (given in `en-US` locale)              |\n * |---------------|-------------------------------------|-------------------------------------------------|\n * | `'short'`     | `'M/d/yy, h:mm a'`                  | `6/15/15, 9:03 AM`                              |\n * | `'medium'`    | `'MMM d, y, h:mm:ss a'`             | `Jun 15, 2015, 9:03:01 AM`                      |\n * | `'long'`      | `'MMMM d, y, h:mm:ss a z'`          | `June 15, 2015 at 9:03:01 AM GMT+1`             |\n * | `'full'`      | `'EEEE, MMMM d, y, h:mm:ss a zzzz'` | `Monday, June 15, 2015 at 9:03:01 AM GMT+01:00` |\n * | `'shortDate'` | `'M/d/yy'`                          | `6/15/15`                                       |\n * | `'mediumDate'`| `'MMM d, y'`                        | `Jun 15, 2015`                                  |\n * | `'longDate'`  | `'MMMM d, y'`                       | `June 15, 2015`                                 |\n * | `'fullDate'`  | `'EEEE, MMMM d, y'`                 | `Monday, June 15, 2015`                         |\n * | `'shortTime'` | `'h:mm a'`                          | `9:03 AM`                                       |\n * | `'mediumTime'`| `'h:mm:ss a'`                       | `9:03:01 AM`                                    |\n * | `'longTime'`  | `'h:mm:ss a z'`                     | `9:03:01 AM GMT+1`                              |\n * | `'fullTime'`  | `'h:mm:ss a zzzz'`                  | `9:03:01 AM GMT+01:00`                          |\n *\n * ### Custom format options\n *\n * You can construct a format string using symbols to specify the components\n * of a date-time value, as described in the following table.\n * Format details depend on the locale.\n * Fields marked with (*) are only available in the extra data set for the given locale.\n *\n *  | Field type          | Format      | Description                                                   | Example Value                                              |\n *  |-------------------- |-------------|---------------------------------------------------------------|------------------------------------------------------------|\n *  | Era                 | G, GG & GGG | Abbreviated                                                   | AD                                                         |\n *  |                     | GGGG        | Wide                                                          | Anno Domini                                                |\n *  |                     | GGGGG       | Narrow                                                        | A                                                          |\n *  | Year                | y           | Numeric: minimum digits                                       | 2, 20, 201, 2017, 20173                                    |\n *  |                     | yy          | Numeric: 2 digits + zero padded                               | 02, 20, 01, 17, 73                                         |\n *  |                     | yyy         | Numeric: 3 digits + zero padded                               | 002, 020, 201, 2017, 20173                                 |\n *  |                     | yyyy        | Numeric: 4 digits or more + zero padded                       | 0002, 0020, 0201, 2017, 20173                              |\n *  | Week-numbering year | Y           | Numeric: minimum digits                                       | 2, 20, 201, 2017, 20173                                    |\n *  |                     | YY          | Numeric: 2 digits + zero padded                               | 02, 20, 01, 17, 73                                         |\n *  |                     | YYY         | Numeric: 3 digits + zero padded                               | 002, 020, 201, 2017, 20173                                 |\n *  |                     | YYYY        | Numeric: 4 digits or more + zero padded                       | 0002, 0020, 0201, 2017, 20173                              |\n *  | Month               | M           | Numeric: 1 digit                                              | 9, 12                                                      |\n *  |                     | MM          | Numeric: 2 digits + zero padded                               | 09, 12                                                     |\n *  |                     | MMM         | Abbreviated                                                   | Sep                                                        |\n *  |                     | MMMM        | Wide                                                          | September                                                  |\n *  |                     | MMMMM       | Narrow                                                        | S                                                          |\n *  | Month standalone    | L           | Numeric: 1 digit                                              | 9, 12                                                      |\n *  |                     | LL          | Numeric: 2 digits + zero padded                               | 09, 12                                                     |\n *  |                     | LLL         | Abbreviated                                                   | Sep                                                        |\n *  |                     | LLLL        | Wide                                                          | September                                                  |\n *  |                     | LLLLL       | Narrow                                                        | S                                                          |\n *  | Week of year        | w           | Numeric: minimum digits                                       | 1... 53                                                    |\n *  |                     | ww          | Numeric: 2 digits + zero padded                               | 01... 53                                                   |\n *  | Week of month       | W           | Numeric: 1 digit                                              | 1... 5                                                     |\n *  | Day of month        | d           | Numeric: minimum digits                                       | 1                                                          |\n *  |                     | dd          | Numeric: 2 digits + zero padded                               | 01                                                         |\n *  | Week day            | E, EE & EEE | Abbreviated                                                   | Tue                                                        |\n *  |                     | EEEE        | Wide                                                          | Tuesday                                                    |\n *  |                     | EEEEE       | Narrow                                                        | T                                                          |\n *  |                     | EEEEEE      | Short                                                         | Tu                                                         |\n *  | Week day standalone | c, cc       | Numeric: 1 digit                                              | 2                                                          |\n *  |                     | ccc         | Abbreviated                                                   | Tue                                                        |\n *  |                     | cccc        | Wide                                                          | Tuesday                                                    |\n *  |                     | ccccc       | Narrow                                                        | T                                                          |\n *  |                     | cccccc      | Short                                                         | Tu                                                         |\n *  | Period              | a, aa & aaa | Abbreviated                                                   | am/pm or AM/PM                                             |\n *  |                     | aaaa        | Wide (fallback to `a` when missing)                           | ante meridiem/post meridiem                                |\n *  |                     | aaaaa       | Narrow                                                        | a/p                                                        |\n *  | Period*             | B, BB & BBB | Abbreviated                                                   | mid.                                                       |\n *  |                     | BBBB        | Wide                                                          | am, pm, midnight, noon, morning, afternoon, evening, night |\n *  |                     | BBBBB       | Narrow                                                        | md                                                         |\n *  | Period standalone*  | b, bb & bbb | Abbreviated                                                   | mid.                                                       |\n *  |                     | bbbb        | Wide                                                          | am, pm, midnight, noon, morning, afternoon, evening, night |\n *  |                     | bbbbb       | Narrow                                                        | md                                                         |\n *  | Hour 1-12           | h           | Numeric: minimum digits                                       | 1, 12                                                      |\n *  |                     | hh          | Numeric: 2 digits + zero padded                               | 01, 12                                                     |\n *  | Hour 0-23           | H           | Numeric: minimum digits                                       | 0, 23                                                      |\n *  |                     | HH          | Numeric: 2 digits + zero padded                               | 00, 23                                                     |\n *  | Minute              | m           | Numeric: minimum digits                                       | 8, 59                                                      |\n *  |                     | mm          | Numeric: 2 digits + zero padded                               | 08, 59                                                     |\n *  | Second              | s           | Numeric: minimum digits                                       | 0... 59                                                    |\n *  |                     | ss          | Numeric: 2 digits + zero padded                               | 00... 59                                                   |\n *  | Fractional seconds  | S           | Numeric: 1 digit                                              | 0... 9                                                     |\n *  |                     | SS          | Numeric: 2 digits + zero padded                               | 00... 99                                                   |\n *  |                     | SSS         | Numeric: 3 digits + zero padded (= milliseconds)              | 000... 999                                                 |\n *  | Zone                | z, zz & zzz | Short specific non location format (fallback to O)            | GMT-8                                                      |\n *  |                     | zzzz        | Long specific non location format (fallback to OOOO)          | GMT-08:00                                                  |\n *  |                     | Z, ZZ & ZZZ | ISO8601 basic format                                          | -0800                                                      |\n *  |                     | ZZZZ        | Long localized GMT format                                     | GMT-8:00                                                   |\n *  |                     | ZZZZZ       | ISO8601 extended format + Z indicator for offset 0 (= XXXXX)  | -08:00                                                     |\n *  |                     | O, OO & OOO | Short localized GMT format                                    | GMT-8                                                      |\n *  |                     | OOOO        | Long localized GMT format                                     | GMT-08:00                                                  |\n *\n *\n * ### Format examples\n *\n * These examples transform a date into various formats,\n * assuming that `dateObj` is a JavaScript `Date` object for\n * year: 2015, month: 6, day: 15, hour: 21, minute: 43, second: 11,\n * given in the local time for the `en-US` locale.\n *\n * ```\n * {{ dateObj | date }}               // output is 'Jun 15, 2015'\n * {{ dateObj | date:'medium' }}      // output is 'Jun 15, 2015, 9:43:11 PM'\n * {{ dateObj | date:'shortTime' }}   // output is '9:43 PM'\n * {{ dateObj | date:'mm:ss' }}       // output is '43:11'\n * ```\n *\n * ### Usage example\n *\n * The following component uses a date pipe to display the current date in different formats.\n *\n * ```\n * @Component({\n *  selector: 'date-pipe',\n *  template: `<div>\n *    <p>Today is {{today | date}}</p>\n *    <p>Or if you prefer, {{today | date:'fullDate'}}</p>\n *    <p>The time is {{today | date:'h:mm a z'}}</p>\n *  </div>`\n * })\n * // Get the current date and time as a date-time value.\n * export class DatePipeComponent {\n *   today: number = Date.now();\n * }\n * ```\n *\n * @publicApi\n */\n// clang-format on\n\nlet DatePipe = /*#__PURE__*/(() => {\n  class DatePipe {\n    constructor(locale, defaultTimezone) {\n      this.locale = locale;\n      this.defaultTimezone = defaultTimezone;\n    }\n\n    transform(value, format = 'mediumDate', timezone, locale) {\n      var _a;\n\n      if (value == null || value === '' || value !== value) return null;\n\n      try {\n        return formatDate(value, format, locale || this.locale, (_a = timezone !== null && timezone !== void 0 ? timezone : this.defaultTimezone) !== null && _a !== void 0 ? _a : undefined);\n      } catch (error) {\n        throw invalidPipeArgumentError(DatePipe, error.message);\n      }\n    }\n\n  }\n\n  DatePipe.ɵfac = function DatePipe_Factory(t) {\n    return new (t || DatePipe)(i0.ɵɵdirectiveInject(LOCALE_ID, 16), i0.ɵɵdirectiveInject(DATE_PIPE_DEFAULT_TIMEZONE, 24));\n  };\n\n  DatePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"date\",\n    type: DatePipe,\n    pure: true\n  });\n  return DatePipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst _INTERPOLATION_REGEXP = /#/g;\n/**\n * @ngModule CommonModule\n * @description\n *\n * Maps a value to a string that pluralizes the value according to locale rules.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/pipes/ts/i18n_pipe.ts region='I18nPluralPipeComponent'}\n *\n * @publicApi\n */\n\nlet I18nPluralPipe = /*#__PURE__*/(() => {\n  class I18nPluralPipe {\n    constructor(_localization) {\n      this._localization = _localization;\n    }\n    /**\n     * @param value the number to be formatted\n     * @param pluralMap an object that mimics the ICU format, see\n     * http://userguide.icu-project.org/formatparse/messages.\n     * @param locale a `string` defining the locale to use (uses the current {@link LOCALE_ID} by\n     * default).\n     */\n\n\n    transform(value, pluralMap, locale) {\n      if (value == null) return '';\n\n      if (typeof pluralMap !== 'object' || pluralMap === null) {\n        throw invalidPipeArgumentError(I18nPluralPipe, pluralMap);\n      }\n\n      const key = getPluralCategory(value, Object.keys(pluralMap), this._localization, locale);\n      return pluralMap[key].replace(_INTERPOLATION_REGEXP, value.toString());\n    }\n\n  }\n\n  I18nPluralPipe.ɵfac = function I18nPluralPipe_Factory(t) {\n    return new (t || I18nPluralPipe)(i0.ɵɵdirectiveInject(NgLocalization, 16));\n  };\n\n  I18nPluralPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"i18nPlural\",\n    type: I18nPluralPipe,\n    pure: true\n  });\n  return I18nPluralPipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Generic selector that displays the string that matches the current value.\n *\n * If none of the keys of the `mapping` match the `value`, then the content\n * of the `other` key is returned when present, otherwise an empty string is returned.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/pipes/ts/i18n_pipe.ts region='I18nSelectPipeComponent'}\n *\n * @publicApi\n */\n\n\nlet I18nSelectPipe = /*#__PURE__*/(() => {\n  class I18nSelectPipe {\n    /**\n     * @param value a string to be internationalized.\n     * @param mapping an object that indicates the text that should be displayed\n     * for different values of the provided `value`.\n     */\n    transform(value, mapping) {\n      if (value == null) return '';\n\n      if (typeof mapping !== 'object' || typeof value !== 'string') {\n        throw invalidPipeArgumentError(I18nSelectPipe, mapping);\n      }\n\n      if (mapping.hasOwnProperty(value)) {\n        return mapping[value];\n      }\n\n      if (mapping.hasOwnProperty('other')) {\n        return mapping['other'];\n      }\n\n      return '';\n    }\n\n  }\n\n  I18nSelectPipe.ɵfac = function I18nSelectPipe_Factory(t) {\n    return new (t || I18nSelectPipe)();\n  };\n\n  I18nSelectPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"i18nSelect\",\n    type: I18nSelectPipe,\n    pure: true\n  });\n  return I18nSelectPipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Converts a value into its JSON-format representation.  Useful for debugging.\n *\n * @usageNotes\n *\n * The following component uses a JSON pipe to convert an object\n * to JSON format, and displays the string in both formats for comparison.\n *\n * {@example common/pipes/ts/json_pipe.ts region='JsonPipe'}\n *\n * @publicApi\n */\n\n\nlet JsonPipe = /*#__PURE__*/(() => {\n  class JsonPipe {\n    /**\n     * @param value A value of any type to convert into a JSON-format string.\n     */\n    transform(value) {\n      return JSON.stringify(value, null, 2);\n    }\n\n  }\n\n  JsonPipe.ɵfac = function JsonPipe_Factory(t) {\n    return new (t || JsonPipe)();\n  };\n\n  JsonPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"json\",\n    type: JsonPipe,\n    pure: false\n  });\n  return JsonPipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction makeKeyValuePair(key, value) {\n  return {\n    key: key,\n    value: value\n  };\n}\n/**\n * @ngModule CommonModule\n * @description\n *\n * Transforms Object or Map into an array of key value pairs.\n *\n * The output array will be ordered by keys.\n * By default the comparator will be by Unicode point value.\n * You can optionally pass a compareFn if your keys are complex types.\n *\n * @usageNotes\n * ### Examples\n *\n * This examples show how an Object or a Map can be iterated by ngFor with the use of this\n * keyvalue pipe.\n *\n * {@example common/pipes/ts/keyvalue_pipe.ts region='KeyValuePipe'}\n *\n * @publicApi\n */\n\n\nlet KeyValuePipe = /*#__PURE__*/(() => {\n  class KeyValuePipe {\n    constructor(differs) {\n      this.differs = differs;\n      this.keyValues = [];\n      this.compareFn = defaultComparator;\n    }\n\n    transform(input, compareFn = defaultComparator) {\n      if (!input || !(input instanceof Map) && typeof input !== 'object') {\n        return null;\n      }\n\n      if (!this.differ) {\n        // make a differ for whatever type we've been passed in\n        this.differ = this.differs.find(input).create();\n      }\n\n      const differChanges = this.differ.diff(input);\n      const compareFnChanged = compareFn !== this.compareFn;\n\n      if (differChanges) {\n        this.keyValues = [];\n        differChanges.forEachItem(r => {\n          this.keyValues.push(makeKeyValuePair(r.key, r.currentValue));\n        });\n      }\n\n      if (differChanges || compareFnChanged) {\n        this.keyValues.sort(compareFn);\n        this.compareFn = compareFn;\n      }\n\n      return this.keyValues;\n    }\n\n  }\n\n  KeyValuePipe.ɵfac = function KeyValuePipe_Factory(t) {\n    return new (t || KeyValuePipe)(i0.ɵɵdirectiveInject(i0.KeyValueDiffers, 16));\n  };\n\n  KeyValuePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"keyvalue\",\n    type: KeyValuePipe,\n    pure: false\n  });\n  return KeyValuePipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction defaultComparator(keyValueA, keyValueB) {\n  const a = keyValueA.key;\n  const b = keyValueB.key; // if same exit with 0;\n\n  if (a === b) return 0; // make sure that undefined are at the end of the sort.\n\n  if (a === undefined) return 1;\n  if (b === undefined) return -1; // make sure that nulls are at the end of the sort.\n\n  if (a === null) return 1;\n  if (b === null) return -1;\n\n  if (typeof a == 'string' && typeof b == 'string') {\n    return a < b ? -1 : 1;\n  }\n\n  if (typeof a == 'number' && typeof b == 'number') {\n    return a - b;\n  }\n\n  if (typeof a == 'boolean' && typeof b == 'boolean') {\n    return a < b ? -1 : 1;\n  } // `a` and `b` are of different types. Compare their string values.\n\n\n  const aString = String(a);\n  const bString = String(b);\n  return aString == bString ? 0 : aString < bString ? -1 : 1;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a value according to digit options and locale rules.\n * Locale determines group sizing and separator,\n * decimal point character, and other locale-specific configurations.\n *\n * @see `formatNumber()`\n *\n * @usageNotes\n *\n * ### digitsInfo\n *\n * The value's decimal representation is specified by the `digitsInfo`\n * parameter, written in the following format:<br>\n *\n * ```\n * {minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}\n * ```\n *\n *  - `minIntegerDigits`:\n * The minimum number of integer digits before the decimal point.\n * Default is 1.\n *\n * - `minFractionDigits`:\n * The minimum number of digits after the decimal point.\n * Default is 0.\n *\n *  - `maxFractionDigits`:\n * The maximum number of digits after the decimal point.\n * Default is 3.\n *\n * If the formatted value is truncated it will be rounded using the \"to-nearest\" method:\n *\n * ```\n * {{3.6 | number: '1.0-0'}}\n * <!--will output '4'-->\n *\n * {{-3.6 | number:'1.0-0'}}\n * <!--will output '-4'-->\n * ```\n *\n * ### locale\n *\n * `locale` will format a value according to locale rules.\n * Locale determines group sizing and separator,\n * decimal point character, and other locale-specific configurations.\n *\n * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n *\n * See [Setting your app locale](guide/i18n-common-locale-id).\n *\n * ### Example\n *\n * The following code shows how the pipe transforms values\n * according to various format specifications,\n * where the caller's default locale is `en-US`.\n *\n * <code-example path=\"common/pipes/ts/number_pipe.ts\" region='NumberPipe'></code-example>\n *\n * @publicApi\n */\n\n\nlet DecimalPipe = /*#__PURE__*/(() => {\n  class DecimalPipe {\n    constructor(_locale) {\n      this._locale = _locale;\n    }\n    /**\n     * @param value The value to be formatted.\n     * @param digitsInfo Sets digit and decimal representation.\n     * [See more](#digitsinfo).\n     * @param locale Specifies what locale format rules to use.\n     * [See more](#locale).\n     */\n\n\n    transform(value, digitsInfo, locale) {\n      if (!isValue(value)) return null;\n      locale = locale || this._locale;\n\n      try {\n        const num = strToNumber(value);\n        return formatNumber(num, locale, digitsInfo);\n      } catch (error) {\n        throw invalidPipeArgumentError(DecimalPipe, error.message);\n      }\n    }\n\n  }\n\n  DecimalPipe.ɵfac = function DecimalPipe_Factory(t) {\n    return new (t || DecimalPipe)(i0.ɵɵdirectiveInject(LOCALE_ID, 16));\n  };\n\n  DecimalPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"number\",\n    type: DecimalPipe,\n    pure: true\n  });\n  return DecimalPipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @ngModule CommonModule\n * @description\n *\n * Transforms a number to a percentage\n * string, formatted according to locale rules that determine group sizing and\n * separator, decimal-point character, and other locale-specific\n * configurations.\n *\n * @see `formatPercent()`\n *\n * @usageNotes\n * The following code shows how the pipe transforms numbers\n * into text strings, according to various format specifications,\n * where the caller's default locale is `en-US`.\n *\n * <code-example path=\"common/pipes/ts/percent_pipe.ts\" region='PercentPipe'></code-example>\n *\n * @publicApi\n */\n\n\nlet PercentPipe = /*#__PURE__*/(() => {\n  class PercentPipe {\n    constructor(_locale) {\n      this._locale = _locale;\n    }\n    /**\n     *\n     * @param value The number to be formatted as a percentage.\n     * @param digitsInfo Decimal representation options, specified by a string\n     * in the following format:<br>\n     * <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>.\n     *   - `minIntegerDigits`: The minimum number of integer digits before the decimal point.\n     * Default is `1`.\n     *   - `minFractionDigits`: The minimum number of digits after the decimal point.\n     * Default is `0`.\n     *   - `maxFractionDigits`: The maximum number of digits after the decimal point.\n     * Default is `0`.\n     * @param locale A locale code for the locale format rules to use.\n     * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n     * See [Setting your app locale](guide/i18n-common-locale-id).\n     */\n\n\n    transform(value, digitsInfo, locale) {\n      if (!isValue(value)) return null;\n      locale = locale || this._locale;\n\n      try {\n        const num = strToNumber(value);\n        return formatPercent(num, locale, digitsInfo);\n      } catch (error) {\n        throw invalidPipeArgumentError(PercentPipe, error.message);\n      }\n    }\n\n  }\n\n  PercentPipe.ɵfac = function PercentPipe_Factory(t) {\n    return new (t || PercentPipe)(i0.ɵɵdirectiveInject(LOCALE_ID, 16));\n  };\n\n  PercentPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"percent\",\n    type: PercentPipe,\n    pure: true\n  });\n  return PercentPipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @ngModule CommonModule\n * @description\n *\n * Transforms a number to a currency string, formatted according to locale rules\n * that determine group sizing and separator, decimal-point character,\n * and other locale-specific configurations.\n *\n * {@a currency-code-deprecation}\n * <div class=\"alert is-helpful\">\n *\n * **Deprecation notice:**\n *\n * The default currency code is currently always `USD` but this is deprecated from v9.\n *\n * **In v11 the default currency code will be taken from the current locale identified by\n * the `LOCALE_ID` token. See the [i18n guide](guide/i18n-common-locale-id) for\n * more information.**\n *\n * If you need the previous behavior then set it by creating a `DEFAULT_CURRENCY_CODE` provider in\n * your application `NgModule`:\n *\n * ```ts\n * {provide: DEFAULT_CURRENCY_CODE, useValue: 'USD'}\n * ```\n *\n * </div>\n *\n * @see `getCurrencySymbol()`\n * @see `formatCurrency()`\n *\n * @usageNotes\n * The following code shows how the pipe transforms numbers\n * into text strings, according to various format specifications,\n * where the caller's default locale is `en-US`.\n *\n * <code-example path=\"common/pipes/ts/currency_pipe.ts\" region='CurrencyPipe'></code-example>\n *\n * @publicApi\n */\n\n\nlet CurrencyPipe = /*#__PURE__*/(() => {\n  class CurrencyPipe {\n    constructor(_locale, _defaultCurrencyCode = 'USD') {\n      this._locale = _locale;\n      this._defaultCurrencyCode = _defaultCurrencyCode;\n    }\n    /**\n     *\n     * @param value The number to be formatted as currency.\n     * @param currencyCode The [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) currency code,\n     * such as `USD` for the US dollar and `EUR` for the euro. The default currency code can be\n     * configured using the `DEFAULT_CURRENCY_CODE` injection token.\n     * @param display The format for the currency indicator. One of the following:\n     *   - `code`: Show the code (such as `USD`).\n     *   - `symbol`(default): Show the symbol (such as `$`).\n     *   - `symbol-narrow`: Use the narrow symbol for locales that have two symbols for their\n     * currency.\n     * For example, the Canadian dollar CAD has the symbol `CA$` and the symbol-narrow `$`. If the\n     * locale has no narrow symbol, uses the standard symbol for the locale.\n     *   - String: Use the given string value instead of a code or a symbol.\n     * For example, an empty string will suppress the currency & symbol.\n     *   - Boolean (marked deprecated in v5): `true` for symbol and false for `code`.\n     *\n     * @param digitsInfo Decimal representation options, specified by a string\n     * in the following format:<br>\n     * <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>.\n     *   - `minIntegerDigits`: The minimum number of integer digits before the decimal point.\n     * Default is `1`.\n     *   - `minFractionDigits`: The minimum number of digits after the decimal point.\n     * Default is `2`.\n     *   - `maxFractionDigits`: The maximum number of digits after the decimal point.\n     * Default is `2`.\n     * If not provided, the number will be formatted with the proper amount of digits,\n     * depending on what the [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) specifies.\n     * For example, the Canadian dollar has 2 digits, whereas the Chilean peso has none.\n     * @param locale A locale code for the locale format rules to use.\n     * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n     * See [Setting your app locale](guide/i18n-common-locale-id).\n     */\n\n\n    transform(value, currencyCode = this._defaultCurrencyCode, display = 'symbol', digitsInfo, locale) {\n      if (!isValue(value)) return null;\n      locale = locale || this._locale;\n\n      if (typeof display === 'boolean') {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && console && console.warn) {\n          console.warn(`Warning: the currency pipe has been changed in Angular v5. The symbolDisplay option (third parameter) is now a string instead of a boolean. The accepted values are \"code\", \"symbol\" or \"symbol-narrow\".`);\n        }\n\n        display = display ? 'symbol' : 'code';\n      }\n\n      let currency = currencyCode || this._defaultCurrencyCode;\n\n      if (display !== 'code') {\n        if (display === 'symbol' || display === 'symbol-narrow') {\n          currency = getCurrencySymbol(currency, display === 'symbol' ? 'wide' : 'narrow', locale);\n        } else {\n          currency = display;\n        }\n      }\n\n      try {\n        const num = strToNumber(value);\n        return formatCurrency(num, locale, currency, currencyCode, digitsInfo);\n      } catch (error) {\n        throw invalidPipeArgumentError(CurrencyPipe, error.message);\n      }\n    }\n\n  }\n\n  CurrencyPipe.ɵfac = function CurrencyPipe_Factory(t) {\n    return new (t || CurrencyPipe)(i0.ɵɵdirectiveInject(LOCALE_ID, 16), i0.ɵɵdirectiveInject(DEFAULT_CURRENCY_CODE, 16));\n  };\n\n  CurrencyPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"currency\",\n    type: CurrencyPipe,\n    pure: true\n  });\n  return CurrencyPipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction isValue(value) {\n  return !(value == null || value === '' || value !== value);\n}\n/**\n * Transforms a string into a number (if needed).\n */\n\n\nfunction strToNumber(value) {\n  // Convert strings to numbers\n  if (typeof value === 'string' && !isNaN(Number(value) - parseFloat(value))) {\n    return Number(value);\n  }\n\n  if (typeof value !== 'number') {\n    throw new Error(`${value} is not a number`);\n  }\n\n  return value;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Creates a new `Array` or `String` containing a subset (slice) of the elements.\n *\n * @usageNotes\n *\n * All behavior is based on the expected behavior of the JavaScript API `Array.prototype.slice()`\n * and `String.prototype.slice()`.\n *\n * When operating on an `Array`, the returned `Array` is always a copy even when all\n * the elements are being returned.\n *\n * When operating on a blank value, the pipe returns the blank value.\n *\n * ### List Example\n *\n * This `ngFor` example:\n *\n * {@example common/pipes/ts/slice_pipe.ts region='SlicePipe_list'}\n *\n * produces the following:\n *\n * ```html\n * <li>b</li>\n * <li>c</li>\n * ```\n *\n * ### String Examples\n *\n * {@example common/pipes/ts/slice_pipe.ts region='SlicePipe_string'}\n *\n * @publicApi\n */\n\n\nlet SlicePipe = /*#__PURE__*/(() => {\n  class SlicePipe {\n    transform(value, start, end) {\n      if (value == null) return null;\n\n      if (!this.supports(value)) {\n        throw invalidPipeArgumentError(SlicePipe, value);\n      }\n\n      return value.slice(start, end);\n    }\n\n    supports(obj) {\n      return typeof obj === 'string' || Array.isArray(obj);\n    }\n\n  }\n\n  SlicePipe.ɵfac = function SlicePipe_Factory(t) {\n    return new (t || SlicePipe)();\n  };\n\n  SlicePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"slice\",\n    type: SlicePipe,\n    pure: false\n  });\n  return SlicePipe;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A collection of Angular pipes that are likely to be used in each and every application.\n */\n\n\nconst COMMON_PIPES = [AsyncPipe, UpperCasePipe, LowerCasePipe, JsonPipe, SlicePipe, DecimalPipe, PercentPipe, TitleCasePipe, CurrencyPipe, DatePipe, I18nPluralPipe, I18nSelectPipe, KeyValuePipe];\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Note: This does not contain the location providers,\n// as they need some platform specific implementations to work.\n\n/**\n * Exports all the basic Angular directives and pipes,\n * such as `NgIf`, `NgForOf`, `DecimalPipe`, and so on.\n * Re-exported by `BrowserModule`, which is included automatically in the root\n * `AppModule` when you create a new app with the CLI `new` command.\n *\n * * The `providers` options configure the NgModule's injector to provide\n * localization dependencies to members.\n * * The `exports` options make the declared directives and pipes available for import\n * by other NgModules.\n *\n * @publicApi\n */\n\nlet CommonModule = /*#__PURE__*/(() => {\n  class CommonModule {}\n\n  CommonModule.ɵfac = function CommonModule_Factory(t) {\n    return new (t || CommonModule)();\n  };\n\n  CommonModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CommonModule\n  });\n  CommonModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: NgLocalization,\n      useClass: NgLocaleLocalization\n    }]\n  });\n  return CommonModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst PLATFORM_BROWSER_ID = 'browser';\nconst PLATFORM_SERVER_ID = 'server';\nconst PLATFORM_WORKER_APP_ID = 'browserWorkerApp';\nconst PLATFORM_WORKER_UI_ID = 'browserWorkerUi';\n/**\n * Returns whether a platform id represents a browser platform.\n * @publicApi\n */\n\nfunction isPlatformBrowser(platformId) {\n  return platformId === PLATFORM_BROWSER_ID;\n}\n/**\n * Returns whether a platform id represents a server platform.\n * @publicApi\n */\n\n\nfunction isPlatformServer(platformId) {\n  return platformId === PLATFORM_SERVER_ID;\n}\n/**\n * Returns whether a platform id represents a web worker app platform.\n * @publicApi\n */\n\n\nfunction isPlatformWorkerApp(platformId) {\n  return platformId === PLATFORM_WORKER_APP_ID;\n}\n/**\n * Returns whether a platform id represents a web worker UI platform.\n * @publicApi\n */\n\n\nfunction isPlatformWorkerUi(platformId) {\n  return platformId === PLATFORM_WORKER_UI_ID;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nconst VERSION = /*#__PURE__*/new Version('13.1.3');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Defines a scroll position manager. Implemented by `BrowserViewportScroller`.\n *\n * @publicApi\n */\n\nlet ViewportScroller = /*#__PURE__*/(() => {\n  class ViewportScroller {}\n\n  // De-sugared tree-shakable injection\n  // See #23917\n\n  /** @nocollapse */\n  ViewportScroller.ɵprov = ɵɵdefineInjectable({\n    token: ViewportScroller,\n    providedIn: 'root',\n    factory: () => new BrowserViewportScroller(ɵɵinject(DOCUMENT), window)\n  });\n  /**\n   * Manages the scroll position for a browser window.\n   */\n\n  return ViewportScroller;\n})();\n\nclass BrowserViewportScroller {\n  constructor(document, window) {\n    this.document = document;\n    this.window = window;\n\n    this.offset = () => [0, 0];\n  }\n  /**\n   * Configures the top offset used when scrolling to an anchor.\n   * @param offset A position in screen coordinates (a tuple with x and y values)\n   * or a function that returns the top offset position.\n   *\n   */\n\n\n  setOffset(offset) {\n    if (Array.isArray(offset)) {\n      this.offset = () => offset;\n    } else {\n      this.offset = offset;\n    }\n  }\n  /**\n   * Retrieves the current scroll position.\n   * @returns The position in screen coordinates.\n   */\n\n\n  getScrollPosition() {\n    if (this.supportsScrolling()) {\n      return [this.window.pageXOffset, this.window.pageYOffset];\n    } else {\n      return [0, 0];\n    }\n  }\n  /**\n   * Sets the scroll position.\n   * @param position The new position in screen coordinates.\n   */\n\n\n  scrollToPosition(position) {\n    if (this.supportsScrolling()) {\n      this.window.scrollTo(position[0], position[1]);\n    }\n  }\n  /**\n   * Scrolls to an element and attempts to focus the element.\n   *\n   * Note that the function name here is misleading in that the target string may be an ID for a\n   * non-anchor element.\n   *\n   * @param target The ID of an element or name of the anchor.\n   *\n   * @see https://html.spec.whatwg.org/#the-indicated-part-of-the-document\n   * @see https://html.spec.whatwg.org/#scroll-to-fragid\n   */\n\n\n  scrollToAnchor(target) {\n    if (!this.supportsScrolling()) {\n      return;\n    }\n\n    const elSelected = findAnchorFromDocument(this.document, target);\n\n    if (elSelected) {\n      this.scrollToElement(elSelected); // After scrolling to the element, the spec dictates that we follow the focus steps for the\n      // target. Rather than following the robust steps, simply attempt focus.\n\n      this.attemptFocus(elSelected);\n    }\n  }\n  /**\n   * Disables automatic scroll restoration provided by the browser.\n   */\n\n\n  setHistoryScrollRestoration(scrollRestoration) {\n    if (this.supportScrollRestoration()) {\n      const history = this.window.history;\n\n      if (history && history.scrollRestoration) {\n        history.scrollRestoration = scrollRestoration;\n      }\n    }\n  }\n  /**\n   * Scrolls to an element using the native offset and the specified offset set on this scroller.\n   *\n   * The offset can be used when we know that there is a floating header and scrolling naively to an\n   * element (ex: `scrollIntoView`) leaves the element hidden behind the floating header.\n   */\n\n\n  scrollToElement(el) {\n    const rect = el.getBoundingClientRect();\n    const left = rect.left + this.window.pageXOffset;\n    const top = rect.top + this.window.pageYOffset;\n    const offset = this.offset();\n    this.window.scrollTo(left - offset[0], top - offset[1]);\n  }\n  /**\n   * Calls `focus` on the `focusTarget` and returns `true` if the element was focused successfully.\n   *\n   * If `false`, further steps may be necessary to determine a valid substitute to be focused\n   * instead.\n   *\n   * @see https://html.spec.whatwg.org/#get-the-focusable-area\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLOrForeignElement/focus\n   * @see https://html.spec.whatwg.org/#focusable-area\n   */\n\n\n  attemptFocus(focusTarget) {\n    focusTarget.focus();\n    return this.document.activeElement === focusTarget;\n  }\n  /**\n   * We only support scroll restoration when we can get a hold of window.\n   * This means that we do not support this behavior when running in a web worker.\n   *\n   * Lifting this restriction right now would require more changes in the dom adapter.\n   * Since webworkers aren't widely used, we will lift it once RouterScroller is\n   * battle-tested.\n   */\n\n\n  supportScrollRestoration() {\n    try {\n      if (!this.supportsScrolling()) {\n        return false;\n      } // The `scrollRestoration` property could be on the `history` instance or its prototype.\n\n\n      const scrollRestorationDescriptor = getScrollRestorationProperty(this.window.history) || getScrollRestorationProperty(Object.getPrototypeOf(this.window.history)); // We can write to the `scrollRestoration` property if it is a writable data field or it has a\n      // setter function.\n\n      return !!scrollRestorationDescriptor && !!(scrollRestorationDescriptor.writable || scrollRestorationDescriptor.set);\n    } catch (_a) {\n      return false;\n    }\n  }\n\n  supportsScrolling() {\n    try {\n      return !!this.window && !!this.window.scrollTo && 'pageXOffset' in this.window;\n    } catch (_a) {\n      return false;\n    }\n  }\n\n}\n\nfunction getScrollRestorationProperty(obj) {\n  return Object.getOwnPropertyDescriptor(obj, 'scrollRestoration');\n}\n\nfunction findAnchorFromDocument(document, target) {\n  const documentResult = document.getElementById(target) || document.getElementsByName(target)[0];\n\n  if (documentResult) {\n    return documentResult;\n  } // `getElementById` and `getElementsByName` won't pierce through the shadow DOM so we\n  // have to traverse the DOM manually and do the lookup through the shadow roots.\n\n\n  if (typeof document.createTreeWalker === 'function' && document.body && (document.body.createShadowRoot || document.body.attachShadow)) {\n    const treeWalker = document.createTreeWalker(document.body, NodeFilter.SHOW_ELEMENT);\n    let currentNode = treeWalker.currentNode;\n\n    while (currentNode) {\n      const shadowRoot = currentNode.shadowRoot;\n\n      if (shadowRoot) {\n        // Note that `ShadowRoot` doesn't support `getElementsByName`\n        // so we have to fall back to `querySelector`.\n        const result = shadowRoot.getElementById(target) || shadowRoot.querySelector(`[name=\"${target}\"]`);\n\n        if (result) {\n          return result;\n        }\n      }\n\n      currentNode = treeWalker.nextNode();\n    }\n  }\n\n  return null;\n}\n/**\n * Provides an empty implementation of the viewport scroller.\n */\n\n\nclass NullViewportScroller {\n  /**\n   * Empty implementation\n   */\n  setOffset(offset) {}\n  /**\n   * Empty implementation\n   */\n\n\n  getScrollPosition() {\n    return [0, 0];\n  }\n  /**\n   * Empty implementation\n   */\n\n\n  scrollToPosition(position) {}\n  /**\n   * Empty implementation\n   */\n\n\n  scrollToAnchor(anchor) {}\n  /**\n   * Empty implementation\n   */\n\n\n  setHistoryScrollRestoration(scrollRestoration) {}\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n */\n\n\nclass XhrFactory {}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { APP_BASE_HREF, AsyncPipe, CommonModule, CurrencyPipe, DATE_PIPE_DEFAULT_TIMEZONE, DOCUMENT, DatePipe, DecimalPipe, FormStyle, FormatWidth, HashLocationStrategy, I18nPluralPipe, I18nSelectPipe, JsonPipe, KeyValuePipe, LOCATION_INITIALIZED, Location, LocationStrategy, LowerCasePipe, NgClass, NgComponentOutlet, NgForOf, NgForOfContext, NgIf, NgIfContext, NgLocaleLocalization, NgLocalization, NgPlural, NgPluralCase, NgStyle, NgSwitch, NgSwitchCase, NgSwitchDefault, NgTemplateOutlet, NumberFormatStyle, NumberSymbol, PathLocationStrategy, PercentPipe, PlatformLocation, Plural, SlicePipe, TitleCasePipe, TranslationWidth, UpperCasePipe, VERSION, ViewportScroller, WeekDay, XhrFactory, formatCurrency, formatDate, formatNumber, formatPercent, getCurrencySymbol, getLocaleCurrencyCode, getLocaleCurrencyName, getLocaleCurrencySymbol, getLocaleDateFormat, getLocaleDateTimeFormat, getLocaleDayNames, getLocaleDayPeriods, getLocaleDirection, getLocaleEraNames, getLocaleExtraDayPeriodRules, getLocaleExtraDayPeriods, getLocaleFirstDayOfWeek, getLocaleId, getLocaleMonthNames, getLocaleNumberFormat, getLocaleNumberSymbol, getLocalePluralCase, getLocaleTimeFormat, getLocaleWeekEndRange, getNumberOfCurrencyDigits, isPlatformBrowser, isPlatformServer, isPlatformWorkerApp, isPlatformWorkerUi, registerLocaleData, BrowserPlatformLocation as ɵBrowserPlatformLocation, DomAdapter as ɵDomAdapter, NullViewportScroller as ɵNullViewportScroller, PLATFORM_BROWSER_ID as ɵPLATFORM_BROWSER_ID, PLATFORM_SERVER_ID as ɵPLATFORM_SERVER_ID, PLATFORM_WORKER_APP_ID as ɵPLATFORM_WORKER_APP_ID, PLATFORM_WORKER_UI_ID as ɵPLATFORM_WORKER_UI_ID, getDOM as ɵgetDOM, parseCookieValue as ɵparseCookieValue, setRootDomAdapter as ɵsetRootDomAdapter }; //# sourceMappingURL=common.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}