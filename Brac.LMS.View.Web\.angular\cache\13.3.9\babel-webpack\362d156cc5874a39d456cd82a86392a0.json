{"ast": null, "code": "import * as moment from 'moment';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { environment } from '../../../environments/environment';\nimport $ from \"jquery\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../_services/authentication.service\";\nimport * as i2 from \"src/app/_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"./title/title.component\";\nimport * as i5 from \"./navigation/side-menu/side-menu.component\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"ngx-scrolltop\";\nconst _c0 = [\"navbarContent\"];\n\nfunction WebLayoutComponent_li_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵelementStart(1, \"p\", 18);\n    i0.ɵɵelementStart(2, \"a\", 19);\n    i0.ɵɵelement(3, \"i\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.details.Address);\n  }\n}\n\nfunction WebLayoutComponent_li_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵelementStart(1, \"p\", 18);\n    i0.ɵɵelementStart(2, \"a\", 21);\n    i0.ɵɵelement(3, \"i\", 22);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", ctx_r1.details.InstituteEmail, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.details.InstituteEmail);\n  }\n}\n\nfunction WebLayoutComponent_li_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵelementStart(1, \"p\", 18);\n    i0.ɵɵelementStart(2, \"a\", 21);\n    i0.ɵɵelement(3, \"i\", 23);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"href\", \"tel:\", ctx_r2.details.ContactNo, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.details.ContactNo);\n  }\n}\n\nfunction WebLayoutComponent_div_21_div_1_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 29);\n    i0.ɵɵelementStart(1, \"span\", 30);\n    i0.ɵɵtext(2, \"Download on the\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 31);\n    i0.ɵɵtext(4, \"App Store\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"href\", ctx_r6.details.iOSAppLink, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction WebLayoutComponent_div_21_div_1_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 32);\n    i0.ɵɵelementStart(1, \"span\", 30);\n    i0.ɵɵtext(2, \"Download on the\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 31);\n    i0.ɵɵtext(4, \"Google Play\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"href\", ctx_r7.details.AndoridAppLink, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction WebLayoutComponent_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelementStart(1, \"h4\", 11);\n    i0.ɵɵtext(2, \"Experience our Mobile App\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, WebLayoutComponent_div_21_div_1_a_3_Template, 5, 1, \"a\", 27);\n    i0.ɵɵtemplate(4, WebLayoutComponent_div_21_div_1_a_4_Template, 5, 1, \"a\", 28);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.details.iOSAppLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.details.AndoridAppLink);\n  }\n}\n\nfunction WebLayoutComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, WebLayoutComponent_div_21_div_1_Template, 5, 2, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.details.iOSAppLink || ctx_r3.details.AndoridAppLink);\n  }\n}\n\nfunction WebLayoutComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.details.Name);\n  }\n}\n\nexport let WebLayoutComponent = /*#__PURE__*/(() => {\n  class WebLayoutComponent {\n    constructor(authService, _service, toastr) {\n      this.authService = authService;\n      this._service = _service;\n      this.toastr = toastr;\n      this.baseUrl = environment.baseUrl;\n      this.isAuthincate = false;\n      this.currentUser = null;\n      this.userType = null;\n      this.collapsed = true;\n      this.loadingIndicator = false;\n      this.now = moment();\n      this.year = this.now.get('year');\n      this.details = null;\n      this.authService.getCurrentUser().subscribe(user => {\n        this.currentUser = user;\n        this.isAuthincate = !!user;\n      });\n    }\n\n    ngOnInit() {\n      this.getDetails();\n      setTimeout(() => {\n        this.loadJquery();\n      }, 400);\n    }\n\n    ngOnDestroy() {}\n\n    getDetails() {\n      this._service.get('configuration/get-institute-info').subscribe({\n        next: res => {\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              closeButton: true,\n              disableTimeOut: false,\n              enableHtml: true\n            });\n            return;\n          }\n\n          this.details = res.Data;\n        },\n        error: err => {\n          this.toastr.warning(err.Messaage || err, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n        },\n        complete: () => {}\n      });\n    }\n\n    loadJquery() {\n      (function ($) {\n        \"use strict\";\n\n        $(window).on(\"load\", function () {});\n        $(document).ready(function () {// $(document).on(\"click\", \".menu-toggler\", function () {\n          //   $(this).toggleClass(\"active\");\n          //   $(\".main-menu\").slideToggle(200);\n          // });\n        });\n      })($);\n    }\n\n  }\n\n  WebLayoutComponent.ɵfac = function WebLayoutComponent_Factory(t) {\n    return new (t || WebLayoutComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService));\n  };\n\n  WebLayoutComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: WebLayoutComponent,\n    selectors: [[\"app-web-layout\"]],\n    viewQuery: function WebLayoutComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.navbarContent = _t.first);\n      }\n    },\n    decls: 29,\n    vars: 6,\n    consts: [[1, \"row\"], [1, \"col-lg-2\", \"wlc-style-1\"], [1, \"col-lg-10\", \"p-5\"], [1, \"footer\", \"bg-dark\", \"pt-5\", \"pt-md-6\"], [1, \"container-fluid\", \"pt-3\", \"pb-0\", \"pt-md-0\", \"pb-md-3\"], [1, \"row\", \"mb-4\"], [1, \"col-md-5\", \"mt-n2\", \"pb-3\", \"pb-md-0\", \"mb-4\"], [\"href\", \"https://www.bracbank.com/\", 1, \"d-block\", \"mb-3\", \"mt-4\", \"wlc-style-2\"], [\"src\", \"assets/img/logo/brac_logo_white.png\", \"alt\", \"BRAC Bank\"], [1, \"col-md-3\"], [1, \"widget\", \"widget-light\"], [1, \"widget-title\"], [4, \"ngIf\"], [\"class\", \"col-md-4\", 4, \"ngIf\"], [1, \"fs-sm\", \"mb-0\", \"order-md-1\", \"text-light\", \"text-center\", \"opacity-50\", \"border-top\", \"pt-3\"], [1, \"text-light\", \"me-1\"], [\"class\", \"text-light\", 4, \"ngIf\"], [1, \"ai-arrow-up\"], [1, \"widget-link\"], [\"href\", \"https://goo.gl/maps/EttuEED8UEBcBcgV6\", 1, \"cursor-pointer\", \"text-decoration-none\", \"text-white\"], [1, \"ai-map-pin\", \"fs-lg\", \"me-2\"], [1, \"cursor-pointer\", \"text-decoration-none\", \"text-white\", 3, \"href\"], [1, \"ai-mail\", \"fs-lg\", \"me-2\"], [1, \"ai-phone\", \"fs-lg\", \"me-2\"], [1, \"col-md-4\"], [\"class\", \"widget widget-light pb-3 mb-4\", 4, \"ngIf\"], [1, \"widget\", \"widget-light\", \"pb-3\", \"mb-4\"], [\"class\", \"btn-market btn-outline btn-apple me-2 mb-3\", \"target\", \"_blank\", \"rel\", \"noopener\", \"role\", \"button\", 3, \"href\", 4, \"ngIf\"], [\"class\", \"btn-market btn-outline btn-google me-2 mb-3\", \"target\", \"_blank\", \"rel\", \"noopener\", \"role\", \"button\", 3, \"href\", 4, \"ngIf\"], [\"target\", \"_blank\", \"rel\", \"noopener\", \"role\", \"button\", 1, \"btn-market\", \"btn-outline\", \"btn-apple\", \"me-2\", \"mb-3\", 3, \"href\"], [1, \"btn-market-subtitle\"], [1, \"btn-market-title\"], [\"target\", \"_blank\", \"rel\", \"noopener\", \"role\", \"button\", 1, \"btn-market\", \"btn-outline\", \"btn-google\", \"me-2\", \"mb-3\", 3, \"href\"], [1, \"text-light\"]],\n    template: function WebLayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"main\");\n        i0.ɵɵelement(1, \"app-title\");\n        i0.ɵɵelementStart(2, \"div\", 0);\n        i0.ɵɵelementStart(3, \"div\", 1);\n        i0.ɵɵelement(4, \"app-web-side-menu\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 2);\n        i0.ɵɵelement(6, \"router-outlet\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"footer\", 3);\n        i0.ɵɵelementStart(8, \"div\", 4);\n        i0.ɵɵelementStart(9, \"div\", 5);\n        i0.ɵɵelementStart(10, \"div\", 6);\n        i0.ɵɵelementStart(11, \"a\", 7);\n        i0.ɵɵelement(12, \"img\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"div\", 9);\n        i0.ɵɵelementStart(14, \"div\", 10);\n        i0.ɵɵelementStart(15, \"h4\", 11);\n        i0.ɵɵtext(16, \"Contacts\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"ul\");\n        i0.ɵɵtemplate(18, WebLayoutComponent_li_18_Template, 5, 1, \"li\", 12);\n        i0.ɵɵtemplate(19, WebLayoutComponent_li_19_Template, 5, 2, \"li\", 12);\n        i0.ɵɵtemplate(20, WebLayoutComponent_li_20_Template, 5, 2, \"li\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(21, WebLayoutComponent_div_21_Template, 2, 1, \"div\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"p\", 14);\n        i0.ɵɵelementStart(23, \"span\", 15);\n        i0.ɵɵtext(24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(25, WebLayoutComponent_span_25_Template, 2, 1, \"span\", 16);\n        i0.ɵɵtext(26, \" ALL RIGHTS RESERVED. \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"ngx-scrolltop\");\n        i0.ɵɵelement(28, \"i\", 17);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(18);\n        i0.ɵɵproperty(\"ngIf\", ctx.details && ctx.details.Address);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.details && ctx.details.InstituteEmail);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.details && ctx.details.ContactNo);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.details);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\"COPYRIGHT \\u00A9 \", ctx.year, \" \");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.details);\n      }\n    },\n    directives: [i4.TitleComponent, i5.SideMenuComponent, i6.RouterOutlet, i7.NgIf, i8.NgxScrollTopComponent],\n    styles: [\".wlc-style-1{background-image:linear-gradient(140deg,#F5C253 0%,#F5C253 50%,#F5BD00 75%)}.wlc-style-2{width:400px}\\n\"],\n    encapsulation: 2\n  });\n  return WebLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}