{"ast": null, "code": "import { isDevMode } from '@angular/core';\n/**\n * @copyright Valor Software\n * @copyright Angular ng-bootstrap team\n */\n\nclass Trigger {\n  constructor(open, close) {\n    this.open = open;\n    this.close = close || open;\n  }\n\n  isManual() {\n    return this.open === 'manual' || this.close === 'manual';\n  }\n\n}\n\nconst DEFAULT_ALIASES = {\n  hover: ['mouseover', 'mouseout'],\n  focus: ['focusin', 'focusout']\n}; // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\nfunction parseTriggers(triggers, aliases = DEFAULT_ALIASES) {\n  const trimmedTriggers = (triggers || '').trim();\n\n  if (trimmedTriggers.length === 0) {\n    return [];\n  }\n\n  const parsedTriggers = trimmedTriggers.split(/\\s+/).map(trigger => trigger.split(':')).map(triggerPair => {\n    const alias = aliases[triggerPair[0]] || triggerPair;\n    return new Trigger(alias[0], alias[1]);\n  });\n  const manualTriggers = parsedTriggers.filter(triggerPair => triggerPair.isManual());\n\n  if (manualTriggers.length > 1) {\n    throw new Error('Triggers parse error: only one manual trigger is allowed');\n  }\n\n  if (manualTriggers.length === 1 && parsedTriggers.length > 1) {\n    throw new Error('Triggers parse error: manual trigger can\\'t be mixed with other triggers');\n  }\n\n  return parsedTriggers;\n}\n\nfunction listenToTriggers(renderer, // eslint-disable-next-line @typescript-eslint/no-explicit-any\ntarget, triggers, showFn, hideFn, toggleFn) {\n  const parsedTriggers = parseTriggers(triggers);\n  const listeners = [];\n\n  if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n    return Function.prototype;\n  }\n\n  parsedTriggers.forEach(trigger => {\n    if (trigger.open === trigger.close) {\n      listeners.push(renderer.listen(target, trigger.open, toggleFn));\n      return;\n    }\n\n    listeners.push(renderer.listen(target, trigger.open, showFn));\n\n    if (trigger.close) {\n      listeners.push(renderer.listen(target, trigger.close, hideFn));\n    }\n  });\n  return () => {\n    listeners.forEach(unsubscribeFn => unsubscribeFn());\n  };\n}\n\nfunction listenToTriggersV2(renderer, options) {\n  const parsedTriggers = parseTriggers(options.triggers);\n  const target = options.target; // do nothing\n\n  if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n    return Function.prototype;\n  } // all listeners\n\n\n  const listeners = []; // lazy listeners registration\n\n  const _registerHide = [];\n\n  const registerHide = () => {\n    // add hide listeners to unregister array\n    _registerHide.forEach(fn => listeners.push(fn())); // register hide events only once\n\n\n    _registerHide.length = 0;\n  }; // register open\\close\\toggle listeners\n\n\n  parsedTriggers.forEach(trigger => {\n    const useToggle = trigger.open === trigger.close;\n    const showFn = useToggle ? options.toggle : options.show;\n\n    if (!useToggle && trigger.close && options.hide) {\n      const _hide = renderer.listen(target, trigger.close, options.hide);\n\n      _registerHide.push(() => _hide);\n    }\n\n    if (showFn) {\n      listeners.push(renderer.listen(target, trigger.open, () => showFn(registerHide)));\n    }\n  });\n  return () => {\n    listeners.forEach(unsubscribeFn => unsubscribeFn());\n  };\n}\n\nfunction registerOutsideClick(renderer, options) {\n  if (!options.outsideClick) {\n    return Function.prototype;\n  } // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\n  return renderer.listen('document', 'click', event => {\n    if (options.target && options.target.contains(event.target)) {\n      return;\n    }\n\n    if (options.targets && options.targets.some(target => target.contains(event.target))) {\n      return;\n    }\n\n    if (options.hide) {\n      options.hide();\n    }\n  });\n}\n\nfunction registerEscClick(renderer, options) {\n  if (!options.outsideEsc) {\n    return Function.prototype;\n  }\n\n  return renderer.listen('document', 'keyup.esc', event => {\n    if (options.target && options.target.contains(event.target)) {\n      return;\n    }\n\n    if (options.targets && options.targets.some(target => target.contains(event.target))) {\n      return;\n    }\n\n    if (options.hide) {\n      options.hide();\n    }\n  });\n}\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * JS version of browser APIs. This library can only run in the browser.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\nconst win = typeof window !== 'undefined' && window || {};\nconst document = win.document;\nconst location = win.location; // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\nconst gc = win.gc ? () => win.gc() : () => null;\nconst performance = win.performance ? win.performance : null;\nconst Event = win.Event;\nconst MouseEvent = win.MouseEvent;\nconst KeyboardEvent = win.KeyboardEvent;\nconst EventTarget = win.EventTarget;\nconst History = win.History;\nconst Location = win.Location;\nconst EventListener = win.EventListener;\nvar BsVerions = /*#__PURE__*/(() => {\n  (function (BsVerions) {\n    BsVerions[\"isBs3\"] = \"bs3\";\n    BsVerions[\"isBs4\"] = \"bs4\";\n    BsVerions[\"isBs5\"] = \"bs5\";\n  })(BsVerions || (BsVerions = {}));\n\n  return BsVerions;\n})();\nlet guessedVersion;\n\nfunction _guessBsVersion() {\n  if (typeof win.document === 'undefined') {\n    return 'bs4';\n  }\n\n  const spanEl = win.document.createElement('span');\n  spanEl.innerText = 'testing bs version';\n  spanEl.classList.add('d-none');\n  spanEl.classList.add('pl-1');\n  win.document.head.appendChild(spanEl);\n  const rect = spanEl.getBoundingClientRect();\n  const checkPadding = win.getComputedStyle(spanEl).paddingLeft;\n\n  if (!rect || rect && rect.top !== 0) {\n    win.document.head.removeChild(spanEl);\n    return 'bs3';\n  }\n\n  if (checkPadding && parseFloat(checkPadding)) {\n    win.document.head.removeChild(spanEl);\n    return 'bs4';\n  }\n\n  win.document.head.removeChild(spanEl);\n  return 'bs5';\n}\n\nfunction setTheme(theme) {\n  guessedVersion = theme;\n} // todo: in ngx-bootstrap, bs4 will became a default one\n\n\nfunction isBs3() {\n  if (typeof win === 'undefined') {\n    return true;\n  }\n\n  if (typeof win.__theme === 'undefined') {\n    if (guessedVersion) {\n      return guessedVersion === 'bs3';\n    }\n\n    guessedVersion = _guessBsVersion();\n    return guessedVersion === 'bs3';\n  }\n\n  return win.__theme === 'bs3';\n}\n\nfunction isBs4() {\n  if (isBs3()) return false;\n  if (guessedVersion) return guessedVersion === 'bs4';\n  guessedVersion = _guessBsVersion();\n  return guessedVersion === 'bs4';\n}\n\nfunction isBs5() {\n  if (isBs3() || isBs4()) return false;\n  if (guessedVersion) return guessedVersion === 'bs5';\n  guessedVersion = _guessBsVersion();\n  return guessedVersion === 'bs5';\n}\n\nfunction getBsVer() {\n  return {\n    isBs3: isBs3(),\n    isBs4: isBs4(),\n    isBs5: isBs5()\n  };\n}\n\nfunction currentBsVersion() {\n  const bsVer = getBsVer();\n  const resVersion = Object.keys(bsVer).find(key => bsVer[key]);\n  return BsVerions[resVersion];\n}\n\nclass LinkedList {\n  constructor() {\n    this.length = 0;\n    this.asArray = []; // Array methods overriding END\n  }\n\n  get(position) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      return void 0;\n    }\n\n    let current = this.head;\n\n    for (let index = 0; index < position; index++) {\n      current = current === null || current === void 0 ? void 0 : current.next;\n    }\n\n    return current === null || current === void 0 ? void 0 : current.value;\n  }\n\n  add(value, position = this.length) {\n    if (position < 0 || position > this.length) {\n      throw new Error('Position is out of the list');\n    }\n\n    const node = {\n      value,\n      next: undefined,\n      previous: undefined\n    };\n\n    if (this.length === 0) {\n      this.head = node;\n      this.tail = node;\n      this.current = node;\n    } else {\n      if (position === 0 && this.head) {\n        // first node\n        node.next = this.head;\n        this.head.previous = node;\n        this.head = node;\n      } else if (position === this.length && this.tail) {\n        // last node\n        this.tail.next = node;\n        node.previous = this.tail;\n        this.tail = node;\n      } else {\n        // node in middle\n        const currentPreviousNode = this.getNode(position - 1);\n        const currentNextNode = currentPreviousNode === null || currentPreviousNode === void 0 ? void 0 : currentPreviousNode.next;\n\n        if (currentPreviousNode && currentNextNode) {\n          currentPreviousNode.next = node;\n          currentNextNode.previous = node;\n          node.previous = currentPreviousNode;\n          node.next = currentNextNode;\n        }\n      }\n    }\n\n    this.length++;\n    this.createInternalArrayRepresentation();\n  }\n\n  remove(position = 0) {\n    var _a;\n\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      throw new Error('Position is out of the list');\n    }\n\n    if (position === 0 && this.head) {\n      // first node\n      this.head = this.head.next;\n\n      if (this.head) {\n        // there is no second node\n        this.head.previous = undefined;\n      } else {\n        // there is no second node\n        this.tail = undefined;\n      }\n    } else if (position === this.length - 1 && ((_a = this.tail) === null || _a === void 0 ? void 0 : _a.previous)) {\n      // last node\n      this.tail = this.tail.previous;\n      this.tail.next = undefined;\n    } else {\n      // middle node\n      const removedNode = this.getNode(position);\n\n      if ((removedNode === null || removedNode === void 0 ? void 0 : removedNode.next) && removedNode.previous) {\n        removedNode.next.previous = removedNode.previous;\n        removedNode.previous.next = removedNode.next;\n      }\n    }\n\n    this.length--;\n    this.createInternalArrayRepresentation();\n  }\n\n  set(position, value) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      throw new Error('Position is out of the list');\n    }\n\n    const node = this.getNode(position);\n\n    if (node) {\n      node.value = value;\n      this.createInternalArrayRepresentation();\n    }\n  }\n\n  toArray() {\n    return this.asArray;\n  }\n\n  findAll(fn) {\n    let current = this.head;\n    const result = [];\n\n    if (!current) {\n      return result;\n    }\n\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return result;\n      }\n\n      if (fn(current.value, index)) {\n        result.push({\n          index,\n          value: current.value\n        });\n      }\n\n      current = current.next;\n    }\n\n    return result;\n  } // Array methods overriding start\n\n\n  push(...args) {\n    args.forEach(arg => {\n      this.add(arg);\n    });\n    return this.length;\n  }\n\n  pop() {\n    if (this.length === 0) {\n      return undefined;\n    }\n\n    const last = this.tail;\n    this.remove(this.length - 1);\n    return last === null || last === void 0 ? void 0 : last.value;\n  }\n\n  unshift(...args) {\n    args.reverse();\n    args.forEach(arg => {\n      this.add(arg, 0);\n    });\n    return this.length;\n  }\n\n  shift() {\n    var _a;\n\n    if (this.length === 0) {\n      return undefined;\n    }\n\n    const lastItem = (_a = this.head) === null || _a === void 0 ? void 0 : _a.value;\n    this.remove();\n    return lastItem;\n  }\n\n  forEach(fn) {\n    let current = this.head;\n\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return;\n      }\n\n      fn(current.value, index);\n      current = current.next;\n    }\n  }\n\n  indexOf(value) {\n    let current = this.head;\n    let position = -1;\n\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return position;\n      }\n\n      if (current.value === value) {\n        position = index;\n        break;\n      }\n\n      current = current.next;\n    }\n\n    return position;\n  }\n\n  some(fn) {\n    let current = this.head;\n    let result = false;\n\n    while (current && !result) {\n      if (fn(current.value)) {\n        result = true;\n        break;\n      }\n\n      current = current.next;\n    }\n\n    return result;\n  }\n\n  every(fn) {\n    let current = this.head;\n    let result = true;\n\n    while (current && result) {\n      if (!fn(current.value)) {\n        result = false;\n      }\n\n      current = current.next;\n    }\n\n    return result;\n  }\n\n  toString() {\n    return '[Linked List]';\n  }\n\n  find(fn) {\n    let current = this.head;\n\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return;\n      }\n\n      if (fn(current.value, index)) {\n        return current.value;\n      }\n\n      current = current.next;\n    }\n  }\n\n  findIndex(fn) {\n    let current = this.head;\n\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return -1;\n      }\n\n      if (fn(current.value, index)) {\n        return index;\n      }\n\n      current = current.next;\n    }\n\n    return -1;\n  }\n\n  getNode(position) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      throw new Error('Position is out of the list');\n    }\n\n    let current = this.head;\n\n    for (let index = 0; index < position; index++) {\n      current = current === null || current === void 0 ? void 0 : current.next;\n    }\n\n    return current;\n  }\n\n  createInternalArrayRepresentation() {\n    const outArray = [];\n    let current = this.head;\n\n    while (current) {\n      outArray.push(current.value);\n      current = current.next;\n    }\n\n    this.asArray = outArray;\n  }\n\n} // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\nfunction OnChange() {\n  const sufix = 'Change'; // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n  return function OnChangeHandler(target, propertyKey) {\n    const _key = ` __${propertyKey}Value`;\n    Object.defineProperty(target, propertyKey, {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      get() {\n        return this[_key];\n      },\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      set(value) {\n        const prevValue = this[_key];\n        this[_key] = value;\n\n        if (prevValue !== value && this[propertyKey + sufix]) {\n          this[propertyKey + sufix].emit(value);\n        }\n      }\n\n    });\n  };\n}\n\nclass Utils {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  static reflow(element) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (bs => bs)(element.offsetHeight);\n  } // source: https://github.com/jquery/jquery/blob/master/src/css/var/getStyles.js\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\n  static getStyles(elem) {\n    // Support: IE <=11 only, Firefox <=30 (#15098, #14150)\n    // IE throws on elements created in popups\n    // FF meanwhile throws on frame elements through \"defaultView.getComputedStyle\"\n    let view = elem.ownerDocument.defaultView;\n\n    if (!view || !view.opener) {\n      view = win;\n    }\n\n    return view.getComputedStyle(elem);\n  }\n\n  static stackOverflowConfig() {\n    const bsVer = currentBsVersion();\n    return {\n      crossorigin: bsVer !== 'bs3' ? \"anonymous\" : undefined,\n      integrity: bsVer === 'bs5' ? 'sha384-KyZXEAg3QhqLMpG8r+8fhAXLRk2vvoC2f3B09zVXn8CA5QIVfZOJ3BCsw2P0p/We' : bsVer === 'bs4' ? 'sha384-TX8t27EcRE3e/ihU7zmQxVncDAy5uIKz4rEkgIXeMed4M0jlfIDPvg6uqKI2xXr2' : undefined,\n      cdnLink: bsVer === 'bs5' ? 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.0/dist/css/bootstrap.min.css' : bsVer === 'bs4' ? 'https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/css/bootstrap.min.css' : 'https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css'\n    };\n  }\n\n}\n\nconst _messagesHash = {};\n\nconst _hideMsg = typeof console === 'undefined' || !('warn' in console);\n\nfunction warnOnce(msg) {\n  if (!isDevMode() || _hideMsg || msg in _messagesHash) {\n    return;\n  }\n\n  _messagesHash[msg] = true;\n  console.warn(msg);\n}\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BsVerions, LinkedList, OnChange, Trigger, Utils, currentBsVersion, document, getBsVer, isBs3, listenToTriggers, listenToTriggersV2, parseTriggers, registerEscClick, registerOutsideClick, setTheme, warnOnce, win as window }; //# sourceMappingURL=ngx-bootstrap-utils.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}