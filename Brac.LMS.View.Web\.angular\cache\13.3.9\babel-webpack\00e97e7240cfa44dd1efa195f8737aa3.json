{"ast": null, "code": "import { observeNotification } from '../Notification';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function dematerialize() {\n  return operate((source, subscriber) => {\n    source.subscribe(new OperatorSubscriber(subscriber, notification => observeNotification(notification, subscriber)));\n  });\n} //# sourceMappingURL=dematerialize.js.map", "map": null, "metadata": {}, "sourceType": "module"}