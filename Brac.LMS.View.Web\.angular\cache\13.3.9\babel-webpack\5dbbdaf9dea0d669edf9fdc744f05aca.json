{"ast": null, "code": "export var ResponseStatus = /*#__PURE__*/(() => {\n  (function (ResponseStatus) {\n    ResponseStatus[ResponseStatus[\"Error\"] = 0] = \"Error\";\n    ResponseStatus[ResponseStatus[\"Success\"] = 1] = \"Success\";\n    ResponseStatus[ResponseStatus[\"Warning\"] = 2] = \"Warning\";\n  })(ResponseStatus || (ResponseStatus = {}));\n\n  return ResponseStatus;\n})();\nexport var NotificationType = /*#__PURE__*/(() => {\n  (function (NotificationType) {\n    NotificationType[NotificationType[\"Random\"] = 0] = \"Random\";\n    NotificationType[NotificationType[\"CourseEnrolmentByAdmin\"] = 1] = \"CourseEnrolmentByAdmin\";\n    NotificationType[NotificationType[\"SelfEnrolmentInCourse\"] = 2] = \"SelfEnrolmentInCourse\";\n    NotificationType[NotificationType[\"NewAvailableCourse\"] = 3] = \"NewAvailableCourse\";\n    NotificationType[NotificationType[\"SequenceCompletion\"] = 4] = \"SequenceCompletion\";\n    NotificationType[NotificationType[\"NoProgress30\"] = 5] = \"NoProgress30\";\n    NotificationType[NotificationType[\"LastWeekReminder\"] = 6] = \"LastWeekReminder\";\n    NotificationType[NotificationType[\"LastDayReminder\"] = 7] = \"LastDayReminder\";\n    NotificationType[NotificationType[\"FailureToComplete\"] = 8] = \"FailureToComplete\";\n    NotificationType[NotificationType[\"SuccessfullCompletion\"] = 9] = \"SuccessfullCompletion\";\n    NotificationType[NotificationType[\"OneWeekRemain\"] = 10] = \"OneWeekRemain\";\n    NotificationType[NotificationType[\"CertificateExpiry\"] = 11] = \"CertificateExpiry\";\n    NotificationType[NotificationType[\"ForumComment\"] = 12] = \"ForumComment\";\n    NotificationType[NotificationType[\"ForumReply\"] = 13] = \"ForumReply\";\n    NotificationType[NotificationType[\"ForumTopicCreated\"] = 14] = \"ForumTopicCreated\";\n    NotificationType[NotificationType[\"ForumApproval\"] = 15] = \"ForumApproval\";\n    NotificationType[NotificationType[\"CertificateTestAnswerSubmission\"] = 16] = \"CertificateTestAnswerSubmission\";\n    NotificationType[NotificationType[\"EvaluationTestAnswerSubmission\"] = 17] = \"EvaluationTestAnswerSubmission\";\n    NotificationType[NotificationType[\"CertificateTestResultPublish\"] = 18] = \"CertificateTestResultPublish\";\n    NotificationType[NotificationType[\"EvaluationTestResultPublish\"] = 19] = \"EvaluationTestResultPublish\";\n  })(NotificationType || (NotificationType = {}));\n\n  return NotificationType;\n})();", "map": null, "metadata": {}, "sourceType": "module"}