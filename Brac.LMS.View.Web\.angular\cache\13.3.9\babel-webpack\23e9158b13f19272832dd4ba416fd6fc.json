{"ast": null, "code": "import { Injectable, Inject, Directive, TemplateRef, EventEmitter, ElementRef, NgZone, HostBinding, Output, Input, Renderer2, HostListener, KeyValueDiffers, ContentChildren, Component, ChangeDetectionStrategy, ContentChild, ChangeDetectorRef, ViewChild, ViewEncapsulation, SkipSelf, Optional, ViewContainerRef, NgModule } from '@angular/core';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { Subject, fromEvent, BehaviorSubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { __decorate } from 'tslib';\n/**\n * Gets the width of the scrollbar.  Nesc for windows\n * http://stackoverflow.com/a/13382873/888165\n */\n\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/common';\nconst _c0 = [\"*\"];\n\nfunction DataTableBodyComponent_datatable_progress_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"datatable-progress\");\n  }\n}\n\nfunction DataTableBodyComponent_datatable_scroller_3_datatable_summary_row_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"datatable-summary-row\", 9);\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"rowHeight\", ctx_r4.summaryHeight)(\"offsetX\", ctx_r4.offsetX)(\"innerWidth\", ctx_r4.innerWidth)(\"rows\", ctx_r4.rows)(\"columns\", ctx_r4.columns);\n  }\n}\n\nfunction DataTableBodyComponent_datatable_scroller_3_datatable_row_wrapper_2_datatable_body_row_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"datatable-body-row\", 13);\n    ɵngcc0.ɵɵlistener(\"treeAction\", function DataTableBodyComponent_datatable_scroller_3_datatable_row_wrapper_2_datatable_body_row_1_Template_datatable_body_row_treeAction_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r14);\n      const group_r7 = ɵngcc0.ɵɵnextContext().$implicit;\n      const ctx_r12 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r12.onTreeAction(group_r7);\n    })(\"activate\", function DataTableBodyComponent_datatable_scroller_3_datatable_row_wrapper_2_datatable_body_row_1_Template_datatable_body_row_activate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r14);\n      const i_r8 = ɵngcc0.ɵɵnextContext().index;\n      const ctx_r15 = ɵngcc0.ɵɵnextContext(2);\n\n      const _r1 = ɵngcc0.ɵɵreference(2);\n\n      return _r1.onActivate($event, ctx_r15.indexes.first + i_r8);\n    });\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const group_r7 = ɵngcc0.ɵɵnextContext().$implicit;\n    const ctx_r9 = ɵngcc0.ɵɵnextContext(2);\n\n    const _r1 = ɵngcc0.ɵɵreference(2);\n\n    ɵngcc0.ɵɵproperty(\"isSelected\", _r1.getRowSelected(group_r7))(\"innerWidth\", ctx_r9.innerWidth)(\"offsetX\", ctx_r9.offsetX)(\"columns\", ctx_r9.columns)(\"rowHeight\", ctx_r9.getRowHeight(group_r7))(\"row\", group_r7)(\"rowIndex\", ctx_r9.getRowIndex(group_r7))(\"expanded\", ctx_r9.getRowExpanded(group_r7))(\"rowClass\", ctx_r9.rowClass)(\"displayCheck\", ctx_r9.displayCheck)(\"treeStatus\", group_r7 && group_r7.treeStatus);\n  }\n}\n\nfunction DataTableBodyComponent_datatable_scroller_3_datatable_row_wrapper_2_ng_template_2_datatable_body_row_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"datatable-body-row\", 15);\n    ɵngcc0.ɵɵlistener(\"activate\", function DataTableBodyComponent_datatable_scroller_3_datatable_row_wrapper_2_ng_template_2_datatable_body_row_0_Template_datatable_body_row_activate_0_listener($event) {\n      const restoredCtx = ɵngcc0.ɵɵrestoreView(_r22);\n      const i_r20 = restoredCtx.index;\n      ɵngcc0.ɵɵnextContext(4);\n\n      const _r1 = ɵngcc0.ɵɵreference(2);\n\n      return _r1.onActivate($event, i_r20);\n    });\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r19 = ctx.$implicit;\n    const group_r7 = ɵngcc0.ɵɵnextContext(2).$implicit;\n    const ctx_r18 = ɵngcc0.ɵɵnextContext(2);\n\n    const _r1 = ɵngcc0.ɵɵreference(2);\n\n    ɵngcc0.ɵɵproperty(\"isSelected\", _r1.getRowSelected(row_r19))(\"innerWidth\", ctx_r18.innerWidth)(\"offsetX\", ctx_r18.offsetX)(\"columns\", ctx_r18.columns)(\"rowHeight\", ctx_r18.getRowHeight(row_r19))(\"row\", row_r19)(\"group\", group_r7.value)(\"rowIndex\", ctx_r18.getRowIndex(row_r19))(\"expanded\", ctx_r18.getRowExpanded(row_r19))(\"rowClass\", ctx_r18.rowClass);\n  }\n}\n\nfunction DataTableBodyComponent_datatable_scroller_3_datatable_row_wrapper_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, DataTableBodyComponent_datatable_scroller_3_datatable_row_wrapper_2_ng_template_2_datatable_body_row_0_Template, 1, 10, \"datatable-body-row\", 14);\n  }\n\n  if (rf & 2) {\n    const group_r7 = ɵngcc0.ɵɵnextContext().$implicit;\n    const ctx_r11 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", group_r7.value)(\"ngForTrackBy\", ctx_r11.rowTrackingFn);\n  }\n}\n\nfunction DataTableBodyComponent_datatable_scroller_3_datatable_row_wrapper_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"datatable-row-wrapper\", 10);\n    ɵngcc0.ɵɵlistener(\"rowContextmenu\", function DataTableBodyComponent_datatable_scroller_3_datatable_row_wrapper_2_Template_datatable_row_wrapper_rowContextmenu_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r26);\n      const ctx_r25 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r25.rowContextmenu.emit($event);\n    });\n    ɵngcc0.ɵɵtemplate(1, DataTableBodyComponent_datatable_scroller_3_datatable_row_wrapper_2_datatable_body_row_1_Template, 1, 11, \"datatable-body-row\", 11);\n    ɵngcc0.ɵɵtemplate(2, DataTableBodyComponent_datatable_scroller_3_datatable_row_wrapper_2_ng_template_2_Template, 1, 2, \"ng-template\", null, 12, ɵngcc0.ɵɵtemplateRefExtractor);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const group_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n\n    const _r10 = ɵngcc0.ɵɵreference(3);\n\n    const ctx_r5 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"groupedRows\", ctx_r5.groupedRows)(\"innerWidth\", ctx_r5.innerWidth)(\"ngStyle\", ctx_r5.getRowsStyles(group_r7))(\"rowDetail\", ctx_r5.rowDetail)(\"groupHeader\", ctx_r5.groupHeader)(\"offsetX\", ctx_r5.offsetX)(\"detailRowHeight\", ctx_r5.getDetailRowHeight(group_r7 && group_r7[i_r8], i_r8))(\"row\", group_r7)(\"expanded\", ctx_r5.getRowExpanded(group_r7))(\"rowIndex\", ctx_r5.getRowIndex(group_r7 && group_r7[i_r8]));\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", !ctx_r5.groupedRows)(\"ngIfElse\", _r10);\n  }\n}\n\nfunction DataTableBodyComponent_datatable_scroller_3_datatable_summary_row_3_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"datatable-summary-row\", 16);\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"ngStyle\", ctx_r6.getBottomSummaryRowStyles())(\"rowHeight\", ctx_r6.summaryHeight)(\"offsetX\", ctx_r6.offsetX)(\"innerWidth\", ctx_r6.innerWidth)(\"rows\", ctx_r6.rows)(\"columns\", ctx_r6.columns);\n  }\n}\n\nfunction DataTableBodyComponent_datatable_scroller_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"datatable-scroller\", 5);\n    ɵngcc0.ɵɵlistener(\"scroll\", function DataTableBodyComponent_datatable_scroller_3_Template_datatable_scroller_scroll_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r28);\n      const ctx_r27 = ɵngcc0.ɵɵnextContext();\n      return ctx_r27.onBodyScroll($event);\n    });\n    ɵngcc0.ɵɵtemplate(1, DataTableBodyComponent_datatable_scroller_3_datatable_summary_row_1_Template, 1, 5, \"datatable-summary-row\", 6);\n    ɵngcc0.ɵɵtemplate(2, DataTableBodyComponent_datatable_scroller_3_datatable_row_wrapper_2_Template, 4, 12, \"datatable-row-wrapper\", 7);\n    ɵngcc0.ɵɵtemplate(3, DataTableBodyComponent_datatable_scroller_3_datatable_summary_row_3_Template, 1, 6, \"datatable-summary-row\", 8);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"scrollbarV\", ctx_r2.scrollbarV)(\"scrollbarH\", ctx_r2.scrollbarH)(\"scrollHeight\", ctx_r2.scrollHeight)(\"scrollWidth\", ctx_r2.columnGroupWidths == null ? null : ctx_r2.columnGroupWidths.total);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r2.summaryRow && ctx_r2.summaryPosition === \"top\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ctx_r2.temp)(\"ngForTrackBy\", ctx_r2.rowTrackingFn);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r2.summaryRow && ctx_r2.summaryPosition === \"bottom\");\n  }\n}\n\nfunction DataTableBodyComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"div\", 17);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"innerHTML\", ctx_r3.emptyMessage, ɵngcc0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction DataTableHeaderComponent_div_1_datatable_header_cell_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"datatable-header-cell\", 4);\n    ɵngcc0.ɵɵlistener(\"resize\", function DataTableHeaderComponent_div_1_datatable_header_cell_1_Template_datatable_header_cell_resize_0_listener($event) {\n      const restoredCtx = ɵngcc0.ɵɵrestoreView(_r5);\n      const column_r3 = restoredCtx.$implicit;\n      const ctx_r4 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r4.onColumnResized($event, column_r3);\n    })(\"longPressStart\", function DataTableHeaderComponent_div_1_datatable_header_cell_1_Template_datatable_header_cell_longPressStart_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r5);\n      const ctx_r6 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r6.onLongPressStart($event);\n    })(\"longPressEnd\", function DataTableHeaderComponent_div_1_datatable_header_cell_1_Template_datatable_header_cell_longPressEnd_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r5);\n      const ctx_r7 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r7.onLongPressEnd($event);\n    })(\"sort\", function DataTableHeaderComponent_div_1_datatable_header_cell_1_Template_datatable_header_cell_sort_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r5);\n      const ctx_r8 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r8.onSort($event);\n    })(\"select\", function DataTableHeaderComponent_div_1_datatable_header_cell_1_Template_datatable_header_cell_select_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r5);\n      const ctx_r9 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r9.select.emit($event);\n    })(\"columnContextmenu\", function DataTableHeaderComponent_div_1_datatable_header_cell_1_Template_datatable_header_cell_columnContextmenu_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r5);\n      const ctx_r10 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r10.columnContextmenu.emit($event);\n    });\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const column_r3 = ctx.$implicit;\n    const ctx_r2 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"resizeEnabled\", column_r3.resizeable)(\"pressModel\", column_r3)(\"pressEnabled\", ctx_r2.reorderable && column_r3.draggable)(\"dragX\", ctx_r2.reorderable && column_r3.draggable && column_r3.dragging)(\"dragY\", false)(\"dragModel\", column_r3)(\"dragEventTarget\", ctx_r2.dragEventTarget)(\"headerHeight\", ctx_r2.headerHeight)(\"isTarget\", column_r3.isTarget)(\"targetMarkerTemplate\", ctx_r2.targetMarkerTemplate)(\"targetMarkerContext\", column_r3.targetMarkerContext)(\"column\", column_r3)(\"sortType\", ctx_r2.sortType)(\"sorts\", ctx_r2.sorts)(\"selectionType\", ctx_r2.selectionType)(\"sortAscendingIcon\", ctx_r2.sortAscendingIcon)(\"sortDescendingIcon\", ctx_r2.sortDescendingIcon)(\"sortUnsetIcon\", ctx_r2.sortUnsetIcon)(\"allRowsSelected\", ctx_r2.allRowsSelected);\n  }\n}\n\nfunction DataTableHeaderComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 2);\n    ɵngcc0.ɵɵtemplate(1, DataTableHeaderComponent_div_1_datatable_header_cell_1_Template, 1, 19, \"datatable-header-cell\", 3);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const colGroup_r1 = ctx.$implicit;\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(\"datatable-row-\" + colGroup_r1.type);\n    ɵngcc0.ɵɵproperty(\"ngStyle\", ctx_r0._styleByGroup[colGroup_r1.type]);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", colGroup_r1.columns)(\"ngForTrackBy\", ctx_r0.columnTrackingFn);\n  }\n}\n\nfunction DatatableComponent_datatable_header_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"datatable-header\", 4);\n    ɵngcc0.ɵɵlistener(\"sort\", function DatatableComponent_datatable_header_1_Template_datatable_header_sort_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r3);\n      const ctx_r2 = ɵngcc0.ɵɵnextContext();\n      return ctx_r2.onColumnSort($event);\n    })(\"resize\", function DatatableComponent_datatable_header_1_Template_datatable_header_resize_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r3);\n      const ctx_r4 = ɵngcc0.ɵɵnextContext();\n      return ctx_r4.onColumnResize($event);\n    })(\"reorder\", function DatatableComponent_datatable_header_1_Template_datatable_header_reorder_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r3);\n      const ctx_r5 = ɵngcc0.ɵɵnextContext();\n      return ctx_r5.onColumnReorder($event);\n    })(\"select\", function DatatableComponent_datatable_header_1_Template_datatable_header_select_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r3);\n      const ctx_r6 = ɵngcc0.ɵɵnextContext();\n      return ctx_r6.onHeaderSelect($event);\n    })(\"columnContextmenu\", function DatatableComponent_datatable_header_1_Template_datatable_header_columnContextmenu_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r3);\n      const ctx_r7 = ɵngcc0.ɵɵnextContext();\n      return ctx_r7.onColumnContextmenu($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"sorts\", ctx_r0.sorts)(\"sortType\", ctx_r0.sortType)(\"scrollbarH\", ctx_r0.scrollbarH)(\"innerWidth\", ctx_r0._innerWidth)(\"offsetX\", ɵngcc0.ɵɵpipeBind1(1, 15, ctx_r0._offsetX))(\"dealsWithGroup\", ctx_r0.groupedRows !== undefined)(\"columns\", ctx_r0._internalColumns)(\"headerHeight\", ctx_r0.headerHeight)(\"reorderable\", ctx_r0.reorderable)(\"targetMarkerTemplate\", ctx_r0.targetMarkerTemplate)(\"sortAscendingIcon\", ctx_r0.cssClasses.sortAscending)(\"sortDescendingIcon\", ctx_r0.cssClasses.sortDescending)(\"sortUnsetIcon\", ctx_r0.cssClasses.sortUnset)(\"allRowsSelected\", ctx_r0.allRowsSelected)(\"selectionType\", ctx_r0.selectionType);\n  }\n}\n\nfunction DatatableComponent_datatable_footer_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"datatable-footer\", 5);\n    ɵngcc0.ɵɵlistener(\"page\", function DatatableComponent_datatable_footer_4_Template_datatable_footer_page_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r8 = ɵngcc0.ɵɵnextContext();\n      return ctx_r8.onFooterPage($event);\n    });\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"rowCount\", ctx_r1.rowCount)(\"pageSize\", ctx_r1.pageSize)(\"offset\", ctx_r1.offset)(\"footerHeight\", ctx_r1.footerHeight)(\"footerTemplate\", ctx_r1.footer)(\"totalMessage\", ctx_r1.messages.totalMessage)(\"pagerLeftArrowIcon\", ctx_r1.cssClasses.pagerLeftArrow)(\"pagerRightArrowIcon\", ctx_r1.cssClasses.pagerRightArrow)(\"pagerPreviousIcon\", ctx_r1.cssClasses.pagerPrevious)(\"selectedCount\", ctx_r1.selected.length)(\"selectedMessage\", !!ctx_r1.selectionType && ctx_r1.messages.selectedMessage)(\"pagerNextIcon\", ctx_r1.cssClasses.pagerNext);\n  }\n}\n\nfunction DataTableHeaderCellComponent_1_ng_template_0_Template(rf, ctx) {}\n\nfunction DataTableHeaderCellComponent_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, DataTableHeaderCellComponent_1_ng_template_0_Template, 0, 0, \"ng-template\", 5);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.targetMarkerTemplate)(\"ngTemplateOutletContext\", ctx_r0.targetMarkerContext);\n  }\n}\n\nfunction DataTableHeaderCellComponent_label_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"label\", 6);\n    ɵngcc0.ɵɵelementStart(1, \"input\", 7);\n    ɵngcc0.ɵɵlistener(\"change\", function DataTableHeaderCellComponent_label_2_Template_input_change_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r6);\n      const ctx_r5 = ɵngcc0.ɵɵnextContext();\n      return ctx_r5.select.emit(!ctx_r5.allRowsSelected);\n    });\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"checked\", ctx_r1.allRowsSelected);\n  }\n}\n\nfunction DataTableHeaderCellComponent_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"span\", 8);\n    ɵngcc0.ɵɵelementStart(1, \"span\", 9);\n    ɵngcc0.ɵɵlistener(\"click\", function DataTableHeaderCellComponent_span_3_Template_span_click_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r8);\n      const ctx_r7 = ɵngcc0.ɵɵnextContext();\n      return ctx_r7.onSort();\n    });\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"innerHTML\", ctx_r2.name, ɵngcc0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction DataTableHeaderCellComponent_4_ng_template_0_Template(rf, ctx) {}\n\nfunction DataTableHeaderCellComponent_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, DataTableHeaderCellComponent_4_ng_template_0_Template, 0, 0, \"ng-template\", 5);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.column.headerTemplate)(\"ngTemplateOutletContext\", ctx_r3.cellContext);\n  }\n}\n\nfunction DataTableFooterComponent_1_ng_template_0_Template(rf, ctx) {}\n\nconst _c1 = function (a0, a1, a2, a3, a4) {\n  return {\n    rowCount: a0,\n    pageSize: a1,\n    selectedCount: a2,\n    curPage: a3,\n    offset: a4\n  };\n};\n\nfunction DataTableFooterComponent_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, DataTableFooterComponent_1_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.footerTemplate.template)(\"ngTemplateOutletContext\", ɵngcc0.ɵɵpureFunction5(2, _c1, ctx_r0.rowCount, ctx_r0.pageSize, ctx_r0.selectedCount, ctx_r0.curPage, ctx_r0.offset));\n  }\n}\n\nfunction DataTableFooterComponent_div_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"span\");\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate2(\" \", ctx_r4.selectedCount == null ? null : ctx_r4.selectedCount.toLocaleString(), \" \", ctx_r4.selectedMessage, \" / \");\n  }\n}\n\nfunction DataTableFooterComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 5);\n    ɵngcc0.ɵɵtemplate(1, DataTableFooterComponent_div_2_span_1_Template, 2, 2, \"span\", 1);\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r1.selectedMessage);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate2(\" \", ctx_r1.rowCount == null ? null : ctx_r1.rowCount.toLocaleString(), \" \", ctx_r1.totalMessage, \" \");\n  }\n}\n\nfunction DataTableFooterComponent_datatable_pager_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"datatable-pager\", 6);\n    ɵngcc0.ɵɵlistener(\"change\", function DataTableFooterComponent_datatable_pager_3_Template_datatable_pager_change_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r6);\n      const ctx_r5 = ɵngcc0.ɵɵnextContext();\n      return ctx_r5.page.emit($event);\n    });\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"pagerLeftArrowIcon\", ctx_r2.pagerLeftArrowIcon)(\"pagerRightArrowIcon\", ctx_r2.pagerRightArrowIcon)(\"pagerPreviousIcon\", ctx_r2.pagerPreviousIcon)(\"pagerNextIcon\", ctx_r2.pagerNextIcon)(\"page\", ctx_r2.curPage)(\"size\", ctx_r2.pageSize)(\"count\", ctx_r2.rowCount)(\"hidden\", !ctx_r2.isVisible);\n  }\n}\n\nconst _c2 = function (a0) {\n  return {\n    \"selected-count\": a0\n  };\n};\n\nfunction DataTablePagerComponent_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"li\", 6);\n    ɵngcc0.ɵɵelementStart(1, \"a\", 7);\n    ɵngcc0.ɵɵlistener(\"click\", function DataTablePagerComponent_li_7_Template_a_click_1_listener() {\n      const restoredCtx = ɵngcc0.ɵɵrestoreView(_r3);\n      const pg_r1 = restoredCtx.$implicit;\n      const ctx_r2 = ɵngcc0.ɵɵnextContext();\n      return ctx_r2.selectPage(pg_r1.number);\n    });\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const pg_r1 = ctx.$implicit;\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassProp(\"active\", pg_r1.number === ctx_r0.page);\n    ɵngcc0.ɵɵattribute(\"aria-label\", \"page \" + pg_r1.number);\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", pg_r1.text, \" \");\n  }\n}\n\nfunction DataTableBodyRowComponent_div_0_datatable_body_cell_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"datatable-body-cell\", 3);\n    ɵngcc0.ɵɵlistener(\"activate\", function DataTableBodyRowComponent_div_0_datatable_body_cell_1_Template_datatable_body_cell_activate_0_listener($event) {\n      const restoredCtx = ɵngcc0.ɵɵrestoreView(_r7);\n      const ii_r5 = restoredCtx.index;\n      const ctx_r6 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r6.onActivate($event, ii_r5);\n    })(\"treeAction\", function DataTableBodyRowComponent_div_0_datatable_body_cell_1_Template_datatable_body_cell_treeAction_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r7);\n      const ctx_r8 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r8.onTreeAction();\n    });\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const column_r4 = ctx.$implicit;\n    const ctx_r3 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"row\", ctx_r3.row)(\"group\", ctx_r3.group)(\"expanded\", ctx_r3.expanded)(\"isSelected\", ctx_r3.isSelected)(\"rowIndex\", ctx_r3.rowIndex)(\"column\", column_r4)(\"rowHeight\", ctx_r3.rowHeight)(\"displayCheck\", ctx_r3.displayCheck)(\"treeStatus\", ctx_r3.treeStatus);\n  }\n}\n\nfunction DataTableBodyRowComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 1);\n    ɵngcc0.ɵɵtemplate(1, DataTableBodyRowComponent_div_0_datatable_body_cell_1_Template, 1, 9, \"datatable-body-cell\", 2);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const colGroup_r1 = ctx.$implicit;\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMapInterpolate1(\"datatable-row-\", colGroup_r1.type, \" datatable-row-group\");\n    ɵngcc0.ɵɵproperty(\"ngStyle\", ctx_r0._groupStyles[colGroup_r1.type]);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", colGroup_r1.columns)(\"ngForTrackBy\", ctx_r0.columnTrackingFn);\n  }\n}\n\nfunction DataTableRowWrapperComponent_div_0_1_ng_template_0_Template(rf, ctx) {}\n\nfunction DataTableRowWrapperComponent_div_0_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, DataTableRowWrapperComponent_div_0_1_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.groupHeader.template)(\"ngTemplateOutletContext\", ctx_r3.groupContext);\n  }\n}\n\nfunction DataTableRowWrapperComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 3);\n    ɵngcc0.ɵɵtemplate(1, DataTableRowWrapperComponent_div_0_1_Template, 1, 2, undefined, 1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngStyle\", ctx_r0.getGroupHeaderStyle());\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r0.groupHeader && ctx_r0.groupHeader.template);\n  }\n}\n\nfunction DataTableRowWrapperComponent_ng_content_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵprojection(0, 0, [\"*ngIf\", \"(groupHeader && groupHeader.template && expanded) || !groupHeader || !groupHeader.template\"]);\n  }\n}\n\nfunction DataTableRowWrapperComponent_div_2_1_ng_template_0_Template(rf, ctx) {}\n\nfunction DataTableRowWrapperComponent_div_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, DataTableRowWrapperComponent_div_2_1_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r5.rowDetail.template)(\"ngTemplateOutletContext\", ctx_r5.rowContext);\n  }\n}\n\nfunction DataTableRowWrapperComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 5);\n    ɵngcc0.ɵɵtemplate(1, DataTableRowWrapperComponent_div_2_1_Template, 1, 2, undefined, 1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵstyleProp(\"height\", ctx_r2.detailRowHeight, \"px\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r2.rowDetail && ctx_r2.rowDetail.template);\n  }\n}\n\nconst _c3 = [\"cellTemplate\"];\n\nfunction DataTableBodyCellComponent_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"label\", 4);\n    ɵngcc0.ɵɵelementStart(1, \"input\", 5);\n    ɵngcc0.ɵɵlistener(\"click\", function DataTableBodyCellComponent_label_1_Template_input_click_1_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r5);\n      const ctx_r4 = ɵngcc0.ɵɵnextContext();\n      return ctx_r4.onCheckboxChange($event);\n    });\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"checked\", ctx_r0.isSelected);\n  }\n}\n\nfunction DataTableBodyCellComponent_ng_container_2_button_1_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"i\", 11);\n  }\n}\n\nfunction DataTableBodyCellComponent_ng_container_2_button_1_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"i\", 12);\n  }\n}\n\nfunction DataTableBodyCellComponent_ng_container_2_button_1_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"i\", 13);\n  }\n}\n\nfunction DataTableBodyCellComponent_ng_container_2_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"button\", 7);\n    ɵngcc0.ɵɵlistener(\"click\", function DataTableBodyCellComponent_ng_container_2_button_1_Template_button_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r12);\n      const ctx_r11 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r11.onTreeAction();\n    });\n    ɵngcc0.ɵɵelementStart(1, \"span\");\n    ɵngcc0.ɵɵtemplate(2, DataTableBodyCellComponent_ng_container_2_button_1_i_2_Template, 1, 0, \"i\", 8);\n    ɵngcc0.ɵɵtemplate(3, DataTableBodyCellComponent_ng_container_2_button_1_i_3_Template, 1, 0, \"i\", 9);\n    ɵngcc0.ɵɵtemplate(4, DataTableBodyCellComponent_ng_container_2_button_1_i_4_Template, 1, 0, \"i\", 10);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"disabled\", ctx_r6.treeStatus === \"disabled\");\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r6.treeStatus === \"loading\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r6.treeStatus === \"collapsed\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r6.treeStatus === \"expanded\" || ctx_r6.treeStatus === \"disabled\");\n  }\n}\n\nfunction DataTableBodyCellComponent_ng_container_2_2_ng_template_0_Template(rf, ctx) {}\n\nconst _c4 = function (a0) {\n  return {\n    cellContext: a0\n  };\n};\n\nfunction DataTableBodyCellComponent_ng_container_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, DataTableBodyCellComponent_ng_container_2_2_ng_template_0_Template, 0, 0, \"ng-template\", 14);\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r7.column.treeToggleTemplate)(\"ngTemplateOutletContext\", ɵngcc0.ɵɵpureFunction1(2, _c4, ctx_r7.cellContext));\n  }\n}\n\nfunction DataTableBodyCellComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementContainerStart(0);\n    ɵngcc0.ɵɵtemplate(1, DataTableBodyCellComponent_ng_container_2_button_1_Template, 5, 4, \"button\", 6);\n    ɵngcc0.ɵɵtemplate(2, DataTableBodyCellComponent_ng_container_2_2_Template, 1, 4, undefined, 2);\n    ɵngcc0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", !ctx_r1.column.treeToggleTemplate);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r1.column.treeToggleTemplate);\n  }\n}\n\nfunction DataTableBodyCellComponent_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"span\", 15);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"title\", ctx_r2.sanitizedValue)(\"innerHTML\", ctx_r2.value, ɵngcc0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction DataTableBodyCellComponent_4_ng_template_0_Template(rf, ctx) {}\n\nfunction DataTableBodyCellComponent_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, DataTableBodyCellComponent_4_ng_template_0_Template, 0, 0, \"ng-template\", 14, 16, ɵngcc0.ɵɵtemplateRefExtractor);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.column.cellTemplate)(\"ngTemplateOutletContext\", ctx_r3.cellContext);\n  }\n}\n\nfunction DataTableSummaryRowComponent_datatable_body_row_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"datatable-body-row\", 1);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"innerWidth\", ctx_r0.innerWidth)(\"offsetX\", ctx_r0.offsetX)(\"columns\", ctx_r0._internalColumns)(\"rowHeight\", ctx_r0.rowHeight)(\"row\", ctx_r0.summaryRow)(\"rowIndex\", -1);\n  }\n}\n\nlet ScrollbarHelper = /*#__PURE__*/(() => {\n  class ScrollbarHelper {\n    constructor(document) {\n      this.document = document;\n      this.width = this.getWidth();\n    }\n\n    getWidth() {\n      const outer = this.document.createElement('div');\n      outer.style.visibility = 'hidden';\n      outer.style.width = '100px';\n      outer.style.msOverflowStyle = 'scrollbar';\n      this.document.body.appendChild(outer);\n      const widthNoScroll = outer.offsetWidth;\n      outer.style.overflow = 'scroll';\n      const inner = this.document.createElement('div');\n      inner.style.width = '100%';\n      outer.appendChild(inner);\n      const widthWithScroll = inner.offsetWidth;\n      outer.parentNode.removeChild(outer);\n      return widthNoScroll - widthWithScroll;\n    }\n\n  }\n\n  ScrollbarHelper.ɵfac = function ScrollbarHelper_Factory(t) {\n    return new (t || ScrollbarHelper)(ɵngcc0.ɵɵinject(DOCUMENT));\n  };\n\n  ScrollbarHelper.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n    token: ScrollbarHelper,\n    factory: ScrollbarHelper.ɵfac\n  });\n  return ScrollbarHelper;\n})();\n\n/**\n * Gets the width of the scrollbar.  Nesc for windows\n * http://stackoverflow.com/a/13382873/888165\n */\nlet DimensionsHelper = /*#__PURE__*/(() => {\n  class DimensionsHelper {\n    getDimensions(element) {\n      return element.getBoundingClientRect();\n    }\n\n  }\n\n  DimensionsHelper.ɵfac = function DimensionsHelper_Factory(t) {\n    return new (t || DimensionsHelper)();\n  };\n\n  DimensionsHelper.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n    token: DimensionsHelper,\n    factory: DimensionsHelper.ɵfac\n  });\n  /**\n   * service to make DatatableComponent aware of changes to\n   * input bindings of DataTableColumnDirective\n   */\n\n  return DimensionsHelper;\n})();\nlet ColumnChangesService = /*#__PURE__*/(() => {\n  class ColumnChangesService {\n    constructor() {\n      this.columnInputChanges = new Subject();\n    }\n\n    get columnInputChanges$() {\n      return this.columnInputChanges.asObservable();\n    }\n\n    onInputChange() {\n      this.columnInputChanges.next();\n    }\n\n  }\n\n  ColumnChangesService.ɵfac = function ColumnChangesService_Factory(t) {\n    return new (t || ColumnChangesService)();\n  };\n\n  ColumnChangesService.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n    token: ColumnChangesService,\n    factory: ColumnChangesService.ɵfac\n  });\n  return ColumnChangesService;\n})();\nlet DataTableFooterTemplateDirective = /*#__PURE__*/(() => {\n  class DataTableFooterTemplateDirective {\n    constructor(template) {\n      this.template = template;\n    }\n\n  }\n\n  DataTableFooterTemplateDirective.ɵfac = function DataTableFooterTemplateDirective_Factory(t) {\n    return new (t || DataTableFooterTemplateDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.TemplateRef));\n  };\n\n  DataTableFooterTemplateDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: DataTableFooterTemplateDirective,\n    selectors: [[\"\", \"ngx-datatable-footer-template\", \"\"]]\n  });\n  return DataTableFooterTemplateDirective;\n})();\n\n/**\n * Visibility Observer Directive\n *\n * Usage:\n *\n * \t\t<div\n * \t\t\tvisibilityObserver\n * \t\t\t(visible)=\"onVisible($event)\">\n * \t\t</div>\n *\n */\nlet VisibilityDirective = /*#__PURE__*/(() => {\n  class VisibilityDirective {\n    constructor(element, zone) {\n      this.element = element;\n      this.zone = zone;\n      this.isVisible = false;\n      this.visible = new EventEmitter();\n    }\n\n    ngOnInit() {\n      this.runCheck();\n    }\n\n    ngOnDestroy() {\n      clearTimeout(this.timeout);\n    }\n\n    onVisibilityChange() {\n      // trigger zone recalc for columns\n      this.zone.run(() => {\n        this.isVisible = true;\n        this.visible.emit(true);\n      });\n    }\n\n    runCheck() {\n      const check = () => {\n        // https://davidwalsh.name/offsetheight-visibility\n        const {\n          offsetHeight,\n          offsetWidth\n        } = this.element.nativeElement;\n\n        if (offsetHeight && offsetWidth) {\n          clearTimeout(this.timeout);\n          this.onVisibilityChange();\n        } else {\n          clearTimeout(this.timeout);\n          this.zone.runOutsideAngular(() => {\n            this.timeout = setTimeout(() => check(), 50);\n          });\n        }\n      };\n\n      this.timeout = setTimeout(() => check());\n    }\n\n  }\n\n  VisibilityDirective.ɵfac = function VisibilityDirective_Factory(t) {\n    return new (t || VisibilityDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.NgZone));\n  };\n\n  VisibilityDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: VisibilityDirective,\n    selectors: [[\"\", \"visibilityObserver\", \"\"]],\n    hostVars: 2,\n    hostBindings: function VisibilityDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"visible\", ctx.isVisible);\n      }\n    },\n    outputs: {\n      visible: \"visible\"\n    }\n  });\n  return VisibilityDirective;\n})();\n\n/**\n * Draggable Directive for Angular2\n *\n * Inspiration:\n *   https://github.com/AngularClass/angular2-examples/blob/master/rx-draggable/directives/draggable.ts\n *   http://stackoverflow.com/questions/35662530/how-to-implement-drag-and-drop-in-angular2\n *\n */\nlet DraggableDirective = /*#__PURE__*/(() => {\n  class DraggableDirective {\n    constructor(element) {\n      this.dragX = true;\n      this.dragY = true;\n      this.dragStart = new EventEmitter();\n      this.dragging = new EventEmitter();\n      this.dragEnd = new EventEmitter();\n      this.isDragging = false;\n      this.element = element.nativeElement;\n    }\n\n    ngOnChanges(changes) {\n      if (changes['dragEventTarget'] && changes['dragEventTarget'].currentValue && this.dragModel.dragging) {\n        this.onMousedown(changes['dragEventTarget'].currentValue);\n      }\n    }\n\n    ngOnDestroy() {\n      this._destroySubscription();\n    }\n\n    onMouseup(event) {\n      if (!this.isDragging) return;\n      this.isDragging = false;\n      this.element.classList.remove('dragging');\n\n      if (this.subscription) {\n        this._destroySubscription();\n\n        this.dragEnd.emit({\n          event,\n          element: this.element,\n          model: this.dragModel\n        });\n      }\n    }\n\n    onMousedown(event) {\n      // we only want to drag the inner header text\n      const isDragElm = event.target.classList.contains('draggable');\n\n      if (isDragElm && (this.dragX || this.dragY)) {\n        event.preventDefault();\n        this.isDragging = true;\n        const mouseDownPos = {\n          x: event.clientX,\n          y: event.clientY\n        };\n        const mouseup = fromEvent(document, 'mouseup');\n        this.subscription = mouseup.subscribe(ev => this.onMouseup(ev));\n        const mouseMoveSub = fromEvent(document, 'mousemove').pipe(takeUntil(mouseup)).subscribe(ev => this.move(ev, mouseDownPos));\n        this.subscription.add(mouseMoveSub);\n        this.dragStart.emit({\n          event,\n          element: this.element,\n          model: this.dragModel\n        });\n      }\n    }\n\n    move(event, mouseDownPos) {\n      if (!this.isDragging) return;\n      const x = event.clientX - mouseDownPos.x;\n      const y = event.clientY - mouseDownPos.y;\n      if (this.dragX) this.element.style.left = `${x}px`;\n      if (this.dragY) this.element.style.top = `${y}px`;\n      this.element.classList.add('dragging');\n      this.dragging.emit({\n        event,\n        element: this.element,\n        model: this.dragModel\n      });\n    }\n\n    _destroySubscription() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n        this.subscription = undefined;\n      }\n    }\n\n  }\n\n  DraggableDirective.ɵfac = function DraggableDirective_Factory(t) {\n    return new (t || DraggableDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef));\n  };\n\n  DraggableDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: DraggableDirective,\n    selectors: [[\"\", \"draggable\", \"\"]],\n    inputs: {\n      dragX: \"dragX\",\n      dragY: \"dragY\",\n      dragEventTarget: \"dragEventTarget\",\n      dragModel: \"dragModel\"\n    },\n    outputs: {\n      dragStart: \"dragStart\",\n      dragging: \"dragging\",\n      dragEnd: \"dragEnd\"\n    },\n    features: [ɵngcc0.ɵɵNgOnChangesFeature]\n  });\n  return DraggableDirective;\n})();\nlet ResizeableDirective = /*#__PURE__*/(() => {\n  class ResizeableDirective {\n    constructor(element, renderer) {\n      this.renderer = renderer;\n      this.resizeEnabled = true;\n      this.resize = new EventEmitter();\n      this.resizing = false;\n      this.element = element.nativeElement;\n    }\n\n    ngAfterViewInit() {\n      const renderer2 = this.renderer;\n      this.resizeHandle = renderer2.createElement('span');\n\n      if (this.resizeEnabled) {\n        renderer2.addClass(this.resizeHandle, 'resize-handle');\n      } else {\n        renderer2.addClass(this.resizeHandle, 'resize-handle--not-resizable');\n      }\n\n      renderer2.appendChild(this.element, this.resizeHandle);\n    }\n\n    ngOnDestroy() {\n      this._destroySubscription();\n\n      if (this.renderer.destroyNode) {\n        this.renderer.destroyNode(this.resizeHandle);\n      } else if (this.resizeHandle) {\n        this.renderer.removeChild(this.renderer.parentNode(this.resizeHandle), this.resizeHandle);\n      }\n    }\n\n    onMouseup() {\n      this.resizing = false;\n\n      if (this.subscription && !this.subscription.closed) {\n        this._destroySubscription();\n\n        this.resize.emit(this.element.clientWidth);\n      }\n    }\n\n    onMousedown(event) {\n      const isHandle = event.target.classList.contains('resize-handle');\n      const initialWidth = this.element.clientWidth;\n      const mouseDownScreenX = event.screenX;\n\n      if (isHandle) {\n        event.stopPropagation();\n        this.resizing = true;\n        const mouseup = fromEvent(document, 'mouseup');\n        this.subscription = mouseup.subscribe(ev => this.onMouseup());\n        const mouseMoveSub = fromEvent(document, 'mousemove').pipe(takeUntil(mouseup)).subscribe(e => this.move(e, initialWidth, mouseDownScreenX));\n        this.subscription.add(mouseMoveSub);\n      }\n    }\n\n    move(event, initialWidth, mouseDownScreenX) {\n      const movementX = event.screenX - mouseDownScreenX;\n      const newWidth = initialWidth + movementX;\n      const overMinWidth = !this.minWidth || newWidth >= this.minWidth;\n      const underMaxWidth = !this.maxWidth || newWidth <= this.maxWidth;\n\n      if (overMinWidth && underMaxWidth) {\n        this.element.style.width = `${newWidth}px`;\n      }\n    }\n\n    _destroySubscription() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n        this.subscription = undefined;\n      }\n    }\n\n  }\n\n  ResizeableDirective.ɵfac = function ResizeableDirective_Factory(t) {\n    return new (t || ResizeableDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2));\n  };\n\n  ResizeableDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: ResizeableDirective,\n    selectors: [[\"\", \"resizeable\", \"\"]],\n    hostVars: 2,\n    hostBindings: function ResizeableDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"mousedown\", function ResizeableDirective_mousedown_HostBindingHandler($event) {\n          return ctx.onMousedown($event);\n        });\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"resizeable\", ctx.resizeEnabled);\n      }\n    },\n    inputs: {\n      resizeEnabled: \"resizeEnabled\",\n      minWidth: \"minWidth\",\n      maxWidth: \"maxWidth\"\n    },\n    outputs: {\n      resize: \"resize\"\n    }\n  });\n  return ResizeableDirective;\n})();\nlet OrderableDirective = /*#__PURE__*/(() => {\n  class OrderableDirective {\n    constructor(differs, document) {\n      this.document = document;\n      this.reorder = new EventEmitter();\n      this.targetChanged = new EventEmitter();\n      this.differ = differs.find({}).create();\n    }\n\n    ngAfterContentInit() {\n      // HACK: Investigate Better Way\n      this.updateSubscriptions();\n      this.draggables.changes.subscribe(this.updateSubscriptions.bind(this));\n    }\n\n    ngOnDestroy() {\n      this.draggables.forEach(d => {\n        d.dragStart.unsubscribe();\n        d.dragging.unsubscribe();\n        d.dragEnd.unsubscribe();\n      });\n    }\n\n    updateSubscriptions() {\n      const diffs = this.differ.diff(this.createMapDiffs());\n\n      if (diffs) {\n        const subscribe = ({\n          currentValue,\n          previousValue\n        }) => {\n          unsubscribe({\n            previousValue\n          });\n\n          if (currentValue) {\n            currentValue.dragStart.subscribe(this.onDragStart.bind(this));\n            currentValue.dragging.subscribe(this.onDragging.bind(this));\n            currentValue.dragEnd.subscribe(this.onDragEnd.bind(this));\n          }\n        };\n\n        const unsubscribe = ({\n          previousValue\n        }) => {\n          if (previousValue) {\n            previousValue.dragStart.unsubscribe();\n            previousValue.dragging.unsubscribe();\n            previousValue.dragEnd.unsubscribe();\n          }\n        };\n\n        diffs.forEachAddedItem(subscribe); // diffs.forEachChangedItem(subscribe.bind(this));\n\n        diffs.forEachRemovedItem(unsubscribe);\n      }\n    }\n\n    onDragStart() {\n      this.positions = {};\n      let i = 0;\n\n      for (const dragger of this.draggables.toArray()) {\n        const elm = dragger.element;\n        const left = parseInt(elm.offsetLeft.toString(), 0);\n        this.positions[dragger.dragModel.prop] = {\n          left,\n          right: left + parseInt(elm.offsetWidth.toString(), 0),\n          index: i++,\n          element: elm\n        };\n      }\n    }\n\n    onDragging({\n      element,\n      model,\n      event\n    }) {\n      const prevPos = this.positions[model.prop];\n      const target = this.isTarget(model, event);\n\n      if (target) {\n        if (this.lastDraggingIndex !== target.i) {\n          this.targetChanged.emit({\n            prevIndex: this.lastDraggingIndex,\n            newIndex: target.i,\n            initialIndex: prevPos.index\n          });\n          this.lastDraggingIndex = target.i;\n        }\n      } else if (this.lastDraggingIndex !== prevPos.index) {\n        this.targetChanged.emit({\n          prevIndex: this.lastDraggingIndex,\n          initialIndex: prevPos.index\n        });\n        this.lastDraggingIndex = prevPos.index;\n      }\n    }\n\n    onDragEnd({\n      element,\n      model,\n      event\n    }) {\n      const prevPos = this.positions[model.prop];\n      const target = this.isTarget(model, event);\n\n      if (target) {\n        this.reorder.emit({\n          prevIndex: prevPos.index,\n          newIndex: target.i,\n          model\n        });\n      }\n\n      this.lastDraggingIndex = undefined;\n      element.style.left = 'auto';\n    }\n\n    isTarget(model, event) {\n      let i = 0;\n      const x = event.x || event.clientX;\n      const y = event.y || event.clientY;\n      const targets = this.document.elementsFromPoint(x, y);\n\n      for (const prop in this.positions) {\n        // current column position which throws event.\n        const pos = this.positions[prop]; // since we drag the inner span, we need to find it in the elements at the cursor\n\n        if (model.prop !== prop && targets.find(el => el === pos.element)) {\n          return {\n            pos,\n            i\n          };\n        }\n\n        i++;\n      }\n    }\n\n    createMapDiffs() {\n      return this.draggables.toArray().reduce((acc, curr) => {\n        acc[curr.dragModel.$$id] = curr;\n        return acc;\n      }, {});\n    }\n\n  }\n\n  OrderableDirective.ɵfac = function OrderableDirective_Factory(t) {\n    return new (t || OrderableDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.KeyValueDiffers), ɵngcc0.ɵɵdirectiveInject(DOCUMENT));\n  };\n\n  OrderableDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: OrderableDirective,\n    selectors: [[\"\", \"orderable\", \"\"]],\n    contentQueries: function OrderableDirective_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵcontentQuery(dirIndex, DraggableDirective, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.draggables = _t);\n      }\n    },\n    outputs: {\n      reorder: \"reorder\",\n      targetChanged: \"targetChanged\"\n    }\n  });\n  return OrderableDirective;\n})();\nlet LongPressDirective = /*#__PURE__*/(() => {\n  class LongPressDirective {\n    constructor() {\n      this.pressEnabled = true;\n      this.duration = 500;\n      this.longPressStart = new EventEmitter();\n      this.longPressing = new EventEmitter();\n      this.longPressEnd = new EventEmitter();\n      this.mouseX = 0;\n      this.mouseY = 0;\n    }\n\n    get press() {\n      return this.pressing;\n    }\n\n    get isLongPress() {\n      return this.isLongPressing;\n    }\n\n    onMouseDown(event) {\n      // don't do right/middle clicks\n      if (event.which !== 1 || !this.pressEnabled) return; // don't start drag if its on resize handle\n\n      const target = event.target;\n      if (target.classList.contains('resize-handle')) return;\n      this.mouseX = event.clientX;\n      this.mouseY = event.clientY;\n      this.pressing = true;\n      this.isLongPressing = false;\n      const mouseup = fromEvent(document, 'mouseup');\n      this.subscription = mouseup.subscribe(ev => this.onMouseup());\n      this.timeout = setTimeout(() => {\n        this.isLongPressing = true;\n        this.longPressStart.emit({\n          event,\n          model: this.pressModel\n        });\n        this.subscription.add(fromEvent(document, 'mousemove').pipe(takeUntil(mouseup)).subscribe(mouseEvent => this.onMouseMove(mouseEvent)));\n        this.loop(event);\n      }, this.duration);\n      this.loop(event);\n    }\n\n    onMouseMove(event) {\n      if (this.pressing && !this.isLongPressing) {\n        const xThres = Math.abs(event.clientX - this.mouseX) > 10;\n        const yThres = Math.abs(event.clientY - this.mouseY) > 10;\n\n        if (xThres || yThres) {\n          this.endPress();\n        }\n      }\n    }\n\n    loop(event) {\n      if (this.isLongPressing) {\n        this.timeout = setTimeout(() => {\n          this.longPressing.emit({\n            event,\n            model: this.pressModel\n          });\n          this.loop(event);\n        }, 50);\n      }\n    }\n\n    endPress() {\n      clearTimeout(this.timeout);\n      this.isLongPressing = false;\n      this.pressing = false;\n\n      this._destroySubscription();\n\n      this.longPressEnd.emit({\n        model: this.pressModel\n      });\n    }\n\n    onMouseup() {\n      this.endPress();\n    }\n\n    ngOnDestroy() {\n      this._destroySubscription();\n    }\n\n    _destroySubscription() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n        this.subscription = undefined;\n      }\n    }\n\n  }\n\n  LongPressDirective.ɵfac = function LongPressDirective_Factory(t) {\n    return new (t || LongPressDirective)();\n  };\n\n  LongPressDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: LongPressDirective,\n    selectors: [[\"\", \"long-press\", \"\"]],\n    hostVars: 4,\n    hostBindings: function LongPressDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"mousedown\", function LongPressDirective_mousedown_HostBindingHandler($event) {\n          return ctx.onMouseDown($event);\n        });\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"press\", ctx.press)(\"longpress\", ctx.isLongPress);\n      }\n    },\n    inputs: {\n      pressEnabled: \"pressEnabled\",\n      duration: \"duration\",\n      pressModel: \"pressModel\"\n    },\n    outputs: {\n      longPressStart: \"longPressStart\",\n      longPressing: \"longPressing\",\n      longPressEnd: \"longPressEnd\"\n    }\n  });\n  return LongPressDirective;\n})();\nlet ScrollerComponent = /*#__PURE__*/(() => {\n  class ScrollerComponent {\n    constructor(ngZone, element, renderer) {\n      this.ngZone = ngZone;\n      this.renderer = renderer;\n      this.scrollbarV = false;\n      this.scrollbarH = false;\n      this.scroll = new EventEmitter();\n      this.scrollYPos = 0;\n      this.scrollXPos = 0;\n      this.prevScrollYPos = 0;\n      this.prevScrollXPos = 0;\n      this._scrollEventListener = null;\n      this.element = element.nativeElement;\n    }\n\n    ngOnInit() {\n      // manual bind so we don't always listen\n      if (this.scrollbarV || this.scrollbarH) {\n        const renderer = this.renderer;\n        this.parentElement = renderer.parentNode(renderer.parentNode(this.element));\n        this._scrollEventListener = this.onScrolled.bind(this);\n        this.parentElement.addEventListener('scroll', this._scrollEventListener);\n      }\n    }\n\n    ngOnDestroy() {\n      if (this._scrollEventListener) {\n        this.parentElement.removeEventListener('scroll', this._scrollEventListener);\n        this._scrollEventListener = null;\n      }\n    }\n\n    setOffset(offsetY) {\n      if (this.parentElement) {\n        this.parentElement.scrollTop = offsetY;\n      }\n    }\n\n    onScrolled(event) {\n      const dom = event.currentTarget;\n      requestAnimationFrame(() => {\n        this.scrollYPos = dom.scrollTop;\n        this.scrollXPos = dom.scrollLeft;\n        this.updateOffset();\n      });\n    }\n\n    updateOffset() {\n      let direction;\n\n      if (this.scrollYPos < this.prevScrollYPos) {\n        direction = 'down';\n      } else if (this.scrollYPos > this.prevScrollYPos) {\n        direction = 'up';\n      }\n\n      this.scroll.emit({\n        direction,\n        scrollYPos: this.scrollYPos,\n        scrollXPos: this.scrollXPos\n      });\n      this.prevScrollYPos = this.scrollYPos;\n      this.prevScrollXPos = this.scrollXPos;\n    }\n\n  }\n\n  ScrollerComponent.ɵfac = function ScrollerComponent_Factory(t) {\n    return new (t || ScrollerComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.NgZone), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2));\n  };\n\n  ScrollerComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: ScrollerComponent,\n    selectors: [[\"datatable-scroller\"]],\n    hostAttrs: [1, \"datatable-scroll\"],\n    hostVars: 4,\n    hostBindings: function ScrollerComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵstyleProp(\"height\", ctx.scrollHeight, \"px\")(\"width\", ctx.scrollWidth, \"px\");\n      }\n    },\n    inputs: {\n      scrollbarV: \"scrollbarV\",\n      scrollbarH: \"scrollbarH\",\n      scrollHeight: \"scrollHeight\",\n      scrollWidth: \"scrollWidth\"\n    },\n    outputs: {\n      scroll: \"scroll\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function ScrollerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return ScrollerComponent;\n})();\nlet DatatableGroupHeaderTemplateDirective = /*#__PURE__*/(() => {\n  class DatatableGroupHeaderTemplateDirective {\n    constructor(template) {\n      this.template = template;\n    }\n\n  }\n\n  DatatableGroupHeaderTemplateDirective.ɵfac = function DatatableGroupHeaderTemplateDirective_Factory(t) {\n    return new (t || DatatableGroupHeaderTemplateDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.TemplateRef));\n  };\n\n  DatatableGroupHeaderTemplateDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: DatatableGroupHeaderTemplateDirective,\n    selectors: [[\"\", \"ngx-datatable-group-header-template\", \"\"]]\n  });\n  return DatatableGroupHeaderTemplateDirective;\n})();\nlet DatatableGroupHeaderDirective = /*#__PURE__*/(() => {\n  class DatatableGroupHeaderDirective {\n    constructor() {\n      /**\n       * Row height is required when virtual scroll is enabled.\n       */\n      this.rowHeight = 0;\n      /**\n       * Track toggling of group visibility\n       */\n\n      this.toggle = new EventEmitter();\n    }\n\n    get template() {\n      return this._templateInput || this._templateQuery;\n    }\n    /**\n     * Toggle the expansion of a group\n     */\n\n\n    toggleExpandGroup(group) {\n      this.toggle.emit({\n        type: 'group',\n        value: group\n      });\n    }\n    /**\n     * Expand all groups\n     */\n\n\n    expandAllGroups() {\n      this.toggle.emit({\n        type: 'all',\n        value: true\n      });\n    }\n    /**\n     * Collapse all groups\n     */\n\n\n    collapseAllGroups() {\n      this.toggle.emit({\n        type: 'all',\n        value: false\n      });\n    }\n\n  }\n\n  DatatableGroupHeaderDirective.ɵfac = function DatatableGroupHeaderDirective_Factory(t) {\n    return new (t || DatatableGroupHeaderDirective)();\n  };\n\n  DatatableGroupHeaderDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: DatatableGroupHeaderDirective,\n    selectors: [[\"ngx-datatable-group-header\"]],\n    contentQueries: function DatatableGroupHeaderDirective_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵcontentQuery(dirIndex, DatatableGroupHeaderTemplateDirective, 7, TemplateRef);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx._templateQuery = _t.first);\n      }\n    },\n    inputs: {\n      rowHeight: \"rowHeight\",\n      _templateInput: [\"template\", \"_templateInput\"]\n    },\n    outputs: {\n      toggle: \"toggle\"\n    }\n  });\n  return DatatableGroupHeaderDirective;\n})();\n\n/**\n * Always returns the empty string ''\n */\nfunction emptyStringGetter() {\n  return '';\n}\n/**\n * Returns the appropriate getter function for this kind of prop.\n * If prop == null, returns the emptyStringGetter.\n */\n\n\nfunction getterForProp(prop) {\n  if (prop == null) {\n    return emptyStringGetter;\n  }\n\n  if (typeof prop === 'number') {\n    return numericIndexGetter;\n  } else {\n    // deep or simple\n    if (prop.indexOf('.') !== -1) {\n      return deepValueGetter;\n    } else {\n      return shallowValueGetter;\n    }\n  }\n}\n/**\n * Returns the value at this numeric index.\n * @param row array of values\n * @param index numeric index\n * @returns any or '' if invalid index\n */\n\n\nfunction numericIndexGetter(row, index) {\n  if (row == null) {\n    return '';\n  } // mimic behavior of deepValueGetter\n\n\n  if (!row || index == null) {\n    return row;\n  }\n\n  const value = row[index];\n\n  if (value == null) {\n    return '';\n  }\n\n  return value;\n}\n/**\n * Returns the value of a field.\n * (more efficient than deepValueGetter)\n * @param obj object containing the field\n * @param fieldName field name string\n */\n\n\nfunction shallowValueGetter(obj, fieldName) {\n  if (obj == null) {\n    return '';\n  }\n\n  if (!obj || !fieldName) {\n    return obj;\n  }\n\n  const value = obj[fieldName];\n\n  if (value == null) {\n    return '';\n  }\n\n  return value;\n}\n/**\n * Returns a deep object given a string. zoo['animal.type']\n */\n\n\nfunction deepValueGetter(obj, path) {\n  if (obj == null) {\n    return '';\n  }\n\n  if (!obj || !path) {\n    return obj;\n  } // check if path matches a root-level field\n  // { \"a.b.c\": 123 }\n\n\n  let current = obj[path];\n\n  if (current !== undefined) {\n    return current;\n  }\n\n  current = obj;\n  const split = path.split('.');\n\n  if (split.length) {\n    for (let i = 0; i < split.length; i++) {\n      current = current[split[i]]; // if found undefined, return empty string\n\n      if (current === undefined || current === null) {\n        return '';\n      }\n    }\n  }\n\n  return current;\n}\n\nfunction optionalGetterForProp(prop) {\n  return prop && (row => getterForProp(prop)(row, prop));\n}\n/**\n * This functions rearrange items by their parents\n * Also sets the level value to each of the items\n *\n * Note: Expecting each item has a property called parentId\n * Note: This algorithm will fail if a list has two or more items with same ID\n * NOTE: This algorithm will fail if there is a deadlock of relationship\n *\n * For example,\n *\n * Input\n *\n * id -> parent\n * 1  -> 0\n * 2  -> 0\n * 3  -> 1\n * 4  -> 1\n * 5  -> 2\n * 7  -> 8\n * 6  -> 3\n *\n *\n * Output\n * id -> level\n * 1      -> 0\n * --3    -> 1\n * ----6  -> 2\n * --4    -> 1\n * 2      -> 0\n * --5    -> 1\n * 7     -> 8\n *\n *\n * @param rows\n *\n */\n\n\nfunction groupRowsByParents(rows, from, to) {\n  if (from && to) {\n    const nodeById = {};\n    const l = rows.length;\n    let node = null;\n    nodeById[0] = new TreeNode(); // that's the root node\n\n    const uniqIDs = rows.reduce((arr, item) => {\n      const toValue = to(item);\n\n      if (arr.indexOf(toValue) === -1) {\n        arr.push(toValue);\n      }\n\n      return arr;\n    }, []);\n\n    for (let i = 0; i < l; i++) {\n      // make TreeNode objects for each item\n      nodeById[to(rows[i])] = new TreeNode(rows[i]);\n    }\n\n    for (let i = 0; i < l; i++) {\n      // link all TreeNode objects\n      node = nodeById[to(rows[i])];\n      let parent = 0;\n      const fromValue = from(node.row);\n\n      if (!!fromValue && uniqIDs.indexOf(fromValue) > -1) {\n        parent = fromValue;\n      }\n\n      node.parent = nodeById[parent];\n      node.row['level'] = node.parent.row['level'] + 1;\n      node.parent.children.push(node);\n    }\n\n    let resolvedRows = [];\n    nodeById[0].flatten(function () {\n      resolvedRows = [...resolvedRows, this.row];\n    }, true);\n    return resolvedRows;\n  } else {\n    return rows;\n  }\n}\n\nclass TreeNode {\n  constructor(row = null) {\n    if (!row) {\n      row = {\n        level: -1,\n        treeStatus: 'expanded'\n      };\n    }\n\n    this.row = row;\n    this.parent = null;\n    this.children = [];\n  }\n\n  flatten(f, recursive) {\n    if (this.row['treeStatus'] === 'expanded') {\n      for (let i = 0, l = this.children.length; i < l; i++) {\n        const child = this.children[i];\n        f.apply(child, Array.prototype.slice.call(arguments, 2));\n        if (recursive) child.flatten.apply(child, arguments);\n      }\n    }\n  }\n\n}\n/**\n * Converts strings from something to camel case\n * http://stackoverflow.com/questions/10425287/convert-dash-separated-string-to-camelcase\n */\n\n\nfunction camelCase(str) {\n  // Replace special characters with a space\n  str = str.replace(/[^a-zA-Z0-9 ]/g, ' '); // put a space before an uppercase letter\n\n  str = str.replace(/([a-z](?=[A-Z]))/g, '$1 '); // Lower case first character and some other stuff\n\n  str = str.replace(/([^a-zA-Z0-9 ])|^[0-9]+/g, '').trim().toLowerCase(); // uppercase characters preceded by a space or number\n\n  str = str.replace(/([ 0-9]+)([a-zA-Z])/g, function (a, b, c) {\n    return b.trim() + c.toUpperCase();\n  });\n  return str;\n}\n/**\n * Converts strings from camel case to words\n * http://stackoverflow.com/questions/7225407/convert-camelcasetext-to-camel-case-text\n */\n\n\nfunction deCamelCase(str) {\n  return str.replace(/([A-Z])/g, match => ` ${match}`).replace(/^./, match => match.toUpperCase());\n}\n/**\n * Creates a unique object id.\n * http://stackoverflow.com/questions/6248666/how-to-generate-short-uid-like-ax4j9z-in-js\n */\n\n\nfunction id() {\n  return ('0000' + (Math.random() * Math.pow(36, 4) << 0).toString(36)).slice(-4);\n}\n/**\n * Sets the column defaults\n */\n\n\nfunction setColumnDefaults(columns) {\n  if (!columns) return; // Only one column should hold the tree view\n  // Thus if multiple columns are provided with\n  // isTreeColumn as true we take only the first one\n\n  let treeColumnFound = false;\n\n  for (const column of columns) {\n    if (!column.$$id) {\n      column.$$id = id();\n    } // prop can be numeric; zero is valid not a missing prop\n    // translate name => prop\n\n\n    if (isNullOrUndefined(column.prop) && column.name) {\n      column.prop = camelCase(column.name);\n    }\n\n    if (!column.$$valueGetter) {\n      column.$$valueGetter = getterForProp(column.prop);\n    } // format props if no name passed\n\n\n    if (!isNullOrUndefined(column.prop) && isNullOrUndefined(column.name)) {\n      column.name = deCamelCase(String(column.prop));\n    }\n\n    if (isNullOrUndefined(column.prop) && isNullOrUndefined(column.name)) {\n      column.name = ''; // Fixes IE and Edge displaying `null`\n    }\n\n    if (!column.hasOwnProperty('resizeable')) {\n      column.resizeable = true;\n    }\n\n    if (!column.hasOwnProperty('sortable')) {\n      column.sortable = true;\n    }\n\n    if (!column.hasOwnProperty('draggable')) {\n      column.draggable = true;\n    }\n\n    if (!column.hasOwnProperty('canAutoResize')) {\n      column.canAutoResize = true;\n    }\n\n    if (!column.hasOwnProperty('width')) {\n      column.width = 150;\n    }\n\n    if (!column.hasOwnProperty('isTreeColumn')) {\n      column.isTreeColumn = false;\n    } else {\n      if (column.isTreeColumn && !treeColumnFound) {\n        // If the first column with isTreeColumn is true found\n        // we mark that treeCoulmn is found\n        treeColumnFound = true;\n      } else {\n        // After that isTreeColumn property for any other column\n        // will be set as false\n        column.isTreeColumn = false;\n      }\n    }\n  }\n}\n\nfunction isNullOrUndefined(value) {\n  return value === null || value === undefined;\n}\n/**\n * Translates templates definitions to objects\n */\n\n\nfunction translateTemplates(templates) {\n  const result = [];\n\n  for (const temp of templates) {\n    const col = {};\n    const props = Object.getOwnPropertyNames(temp);\n\n    for (const prop of props) {\n      col[prop] = temp[prop];\n    }\n\n    if (temp.headerTemplate) {\n      col.headerTemplate = temp.headerTemplate;\n    }\n\n    if (temp.cellTemplate) {\n      col.cellTemplate = temp.cellTemplate;\n    }\n\n    if (temp.summaryFunc) {\n      col.summaryFunc = temp.summaryFunc;\n    }\n\n    if (temp.summaryTemplate) {\n      col.summaryTemplate = temp.summaryTemplate;\n    }\n\n    result.push(col);\n  }\n\n  return result;\n}\n\nvar ColumnMode = /*#__PURE__*/(() => {\n  (function (ColumnMode) {\n    ColumnMode[\"standard\"] = \"standard\";\n    ColumnMode[\"flex\"] = \"flex\";\n    ColumnMode[\"force\"] = \"force\";\n  })(ColumnMode || (ColumnMode = {}));\n\n  return ColumnMode;\n})();\nvar SelectionType = /*#__PURE__*/(() => {\n  (function (SelectionType) {\n    SelectionType[\"single\"] = \"single\";\n    SelectionType[\"multi\"] = \"multi\";\n    SelectionType[\"multiClick\"] = \"multiClick\";\n    SelectionType[\"cell\"] = \"cell\";\n    SelectionType[\"checkbox\"] = \"checkbox\";\n  })(SelectionType || (SelectionType = {}));\n\n  return SelectionType;\n})();\nvar SortType = /*#__PURE__*/(() => {\n  (function (SortType) {\n    SortType[\"single\"] = \"single\";\n    SortType[\"multi\"] = \"multi\";\n  })(SortType || (SortType = {}));\n\n  return SortType;\n})();\nvar ContextmenuType = /*#__PURE__*/(() => {\n  (function (ContextmenuType) {\n    ContextmenuType[\"header\"] = \"header\";\n    ContextmenuType[\"body\"] = \"body\";\n  })(ContextmenuType || (ContextmenuType = {}));\n\n  return ContextmenuType;\n})();\nlet DataTableColumnHeaderDirective = /*#__PURE__*/(() => {\n  class DataTableColumnHeaderDirective {\n    constructor(template) {\n      this.template = template;\n    }\n\n  }\n\n  DataTableColumnHeaderDirective.ɵfac = function DataTableColumnHeaderDirective_Factory(t) {\n    return new (t || DataTableColumnHeaderDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.TemplateRef));\n  };\n\n  DataTableColumnHeaderDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: DataTableColumnHeaderDirective,\n    selectors: [[\"\", \"ngx-datatable-header-template\", \"\"]]\n  });\n  return DataTableColumnHeaderDirective;\n})();\nlet DataTableColumnCellDirective = /*#__PURE__*/(() => {\n  class DataTableColumnCellDirective {\n    constructor(template) {\n      this.template = template;\n    }\n\n  }\n\n  DataTableColumnCellDirective.ɵfac = function DataTableColumnCellDirective_Factory(t) {\n    return new (t || DataTableColumnCellDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.TemplateRef));\n  };\n\n  DataTableColumnCellDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: DataTableColumnCellDirective,\n    selectors: [[\"\", \"ngx-datatable-cell-template\", \"\"]]\n  });\n  return DataTableColumnCellDirective;\n})();\nlet DataTableColumnCellTreeToggle = /*#__PURE__*/(() => {\n  class DataTableColumnCellTreeToggle {\n    constructor(template) {\n      this.template = template;\n    }\n\n  }\n\n  DataTableColumnCellTreeToggle.ɵfac = function DataTableColumnCellTreeToggle_Factory(t) {\n    return new (t || DataTableColumnCellTreeToggle)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.TemplateRef));\n  };\n\n  DataTableColumnCellTreeToggle.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: DataTableColumnCellTreeToggle,\n    selectors: [[\"\", \"ngx-datatable-tree-toggle\", \"\"]]\n  });\n  return DataTableColumnCellTreeToggle;\n})();\nlet DataTableColumnDirective = /*#__PURE__*/(() => {\n  class DataTableColumnDirective {\n    constructor(columnChangesService) {\n      this.columnChangesService = columnChangesService;\n      this.isFirstChange = true;\n    }\n\n    get cellTemplate() {\n      return this._cellTemplateInput || this._cellTemplateQuery;\n    }\n\n    get headerTemplate() {\n      return this._headerTemplateInput || this._headerTemplateQuery;\n    }\n\n    get treeToggleTemplate() {\n      return this._treeToggleTemplateInput || this._treeToggleTemplateQuery;\n    }\n\n    ngOnChanges() {\n      if (this.isFirstChange) {\n        this.isFirstChange = false;\n      } else {\n        this.columnChangesService.onInputChange();\n      }\n    }\n\n  }\n\n  DataTableColumnDirective.ɵfac = function DataTableColumnDirective_Factory(t) {\n    return new (t || DataTableColumnDirective)(ɵngcc0.ɵɵdirectiveInject(ColumnChangesService));\n  };\n\n  DataTableColumnDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: DataTableColumnDirective,\n    selectors: [[\"ngx-datatable-column\"]],\n    contentQueries: function DataTableColumnDirective_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵcontentQuery(dirIndex, DataTableColumnCellDirective, 7, TemplateRef);\n        ɵngcc0.ɵɵcontentQuery(dirIndex, DataTableColumnHeaderDirective, 7, TemplateRef);\n        ɵngcc0.ɵɵcontentQuery(dirIndex, DataTableColumnCellTreeToggle, 7, TemplateRef);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx._cellTemplateQuery = _t.first);\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx._headerTemplateQuery = _t.first);\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx._treeToggleTemplateQuery = _t.first);\n      }\n    },\n    inputs: {\n      name: \"name\",\n      prop: \"prop\",\n      frozenLeft: \"frozenLeft\",\n      frozenRight: \"frozenRight\",\n      flexGrow: \"flexGrow\",\n      resizeable: \"resizeable\",\n      comparator: \"comparator\",\n      pipe: \"pipe\",\n      sortable: \"sortable\",\n      draggable: \"draggable\",\n      canAutoResize: \"canAutoResize\",\n      minWidth: \"minWidth\",\n      width: \"width\",\n      maxWidth: \"maxWidth\",\n      checkboxable: \"checkboxable\",\n      headerCheckboxable: \"headerCheckboxable\",\n      headerClass: \"headerClass\",\n      cellClass: \"cellClass\",\n      isTreeColumn: \"isTreeColumn\",\n      treeLevelIndent: \"treeLevelIndent\",\n      summaryFunc: \"summaryFunc\",\n      summaryTemplate: \"summaryTemplate\",\n      _cellTemplateInput: [\"cellTemplate\", \"_cellTemplateInput\"],\n      _headerTemplateInput: [\"headerTemplate\", \"_headerTemplateInput\"],\n      _treeToggleTemplateInput: [\"treeToggleTemplate\", \"_treeToggleTemplateInput\"]\n    },\n    features: [ɵngcc0.ɵɵNgOnChangesFeature]\n  });\n  return DataTableColumnDirective;\n})();\nlet DatatableRowDetailTemplateDirective = /*#__PURE__*/(() => {\n  class DatatableRowDetailTemplateDirective {\n    constructor(template) {\n      this.template = template;\n    }\n\n  }\n\n  DatatableRowDetailTemplateDirective.ɵfac = function DatatableRowDetailTemplateDirective_Factory(t) {\n    return new (t || DatatableRowDetailTemplateDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.TemplateRef));\n  };\n\n  DatatableRowDetailTemplateDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: DatatableRowDetailTemplateDirective,\n    selectors: [[\"\", \"ngx-datatable-row-detail-template\", \"\"]]\n  });\n  return DatatableRowDetailTemplateDirective;\n})();\nlet DatatableRowDetailDirective = /*#__PURE__*/(() => {\n  class DatatableRowDetailDirective {\n    constructor() {\n      /**\n       * The detail row height is required especially\n       * when virtual scroll is enabled.\n       */\n      this.rowHeight = 0;\n      /**\n       * Row detail row visbility was toggled.\n       */\n\n      this.toggle = new EventEmitter();\n    }\n\n    get template() {\n      return this._templateInput || this._templateQuery;\n    }\n    /**\n     * Toggle the expansion of the row\n     */\n\n\n    toggleExpandRow(row) {\n      this.toggle.emit({\n        type: 'row',\n        value: row\n      });\n    }\n    /**\n     * API method to expand all the rows.\n     */\n\n\n    expandAllRows() {\n      this.toggle.emit({\n        type: 'all',\n        value: true\n      });\n    }\n    /**\n     * API method to collapse all the rows.\n     */\n\n\n    collapseAllRows() {\n      this.toggle.emit({\n        type: 'all',\n        value: false\n      });\n    }\n\n  }\n\n  DatatableRowDetailDirective.ɵfac = function DatatableRowDetailDirective_Factory(t) {\n    return new (t || DatatableRowDetailDirective)();\n  };\n\n  DatatableRowDetailDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: DatatableRowDetailDirective,\n    selectors: [[\"ngx-datatable-row-detail\"]],\n    contentQueries: function DatatableRowDetailDirective_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵcontentQuery(dirIndex, DatatableRowDetailTemplateDirective, 7, TemplateRef);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx._templateQuery = _t.first);\n      }\n    },\n    inputs: {\n      rowHeight: \"rowHeight\",\n      _templateInput: [\"template\", \"_templateInput\"]\n    },\n    outputs: {\n      toggle: \"toggle\"\n    }\n  });\n  return DatatableRowDetailDirective;\n})();\nlet DatatableFooterDirective = /*#__PURE__*/(() => {\n  class DatatableFooterDirective {\n    get template() {\n      return this._templateInput || this._templateQuery;\n    }\n\n  }\n\n  DatatableFooterDirective.ɵfac = function DatatableFooterDirective_Factory(t) {\n    return new (t || DatatableFooterDirective)();\n  };\n\n  DatatableFooterDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: DatatableFooterDirective,\n    selectors: [[\"ngx-datatable-footer\"]],\n    contentQueries: function DatatableFooterDirective_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵcontentQuery(dirIndex, DataTableFooterTemplateDirective, 5, TemplateRef);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx._templateQuery = _t.first);\n      }\n    },\n    inputs: {\n      footerHeight: \"footerHeight\",\n      totalMessage: \"totalMessage\",\n      selectedMessage: \"selectedMessage\",\n      pagerLeftArrowIcon: \"pagerLeftArrowIcon\",\n      pagerRightArrowIcon: \"pagerRightArrowIcon\",\n      pagerPreviousIcon: \"pagerPreviousIcon\",\n      pagerNextIcon: \"pagerNextIcon\",\n      _templateInput: [\"template\", \"_templateInput\"]\n    }\n  });\n  return DatatableFooterDirective;\n})();\n\n/**\n * Returns the columns by pin.\n */\nfunction columnsByPin(cols) {\n  const ret = {\n    left: [],\n    center: [],\n    right: []\n  };\n\n  if (cols) {\n    for (const col of cols) {\n      if (col.frozenLeft) {\n        ret.left.push(col);\n      } else if (col.frozenRight) {\n        ret.right.push(col);\n      } else {\n        ret.center.push(col);\n      }\n    }\n  }\n\n  return ret;\n}\n/**\n * Returns the widths of all group sets of a column\n */\n\n\nfunction columnGroupWidths(groups, all) {\n  return {\n    left: columnTotalWidth(groups.left),\n    center: columnTotalWidth(groups.center),\n    right: columnTotalWidth(groups.right),\n    total: Math.floor(columnTotalWidth(all))\n  };\n}\n/**\n * Calculates the total width of all columns and their groups\n */\n\n\nfunction columnTotalWidth(columns, prop) {\n  let totalWidth = 0;\n\n  if (columns) {\n    for (const c of columns) {\n      const has = prop && c[prop];\n      const width = has ? c[prop] : c.width;\n      totalWidth = totalWidth + parseFloat(width);\n    }\n  }\n\n  return totalWidth;\n}\n/**\n * Calculates the total width of all columns and their groups\n */\n\n\nfunction columnsTotalWidth(columns, prop) {\n  let totalWidth = 0;\n\n  for (const column of columns) {\n    const has = prop && column[prop];\n    totalWidth = totalWidth + (has ? column[prop] : column.width);\n  }\n\n  return totalWidth;\n}\n\nfunction columnsByPinArr(val) {\n  const colsByPinArr = [];\n  const colsByPin = columnsByPin(val);\n  colsByPinArr.push({\n    type: 'left',\n    columns: colsByPin['left']\n  });\n  colsByPinArr.push({\n    type: 'center',\n    columns: colsByPin['center']\n  });\n  colsByPinArr.push({\n    type: 'right',\n    columns: colsByPin['right']\n  });\n  return colsByPinArr;\n}\n/**\n * This object contains the cache of the various row heights that are present inside\n * the data table.   Its based on Fenwick tree data structure that helps with\n * querying sums that have time complexity of log n.\n *\n * Fenwick Tree Credits: http://petr-mitrichev.blogspot.com/2013/05/fenwick-tree-range-updates.html\n * https://github.com/mikolalysenko/fenwick-tree\n *\n */\n\n\nclass RowHeightCache {\n  constructor() {\n    /**\n     * Tree Array stores the cumulative information of the row heights to perform efficient\n     * range queries and updates.  Currently the tree is initialized to the base row\n     * height instead of the detail row height.\n     */\n    this.treeArray = [];\n  }\n  /**\n   * Clear the Tree array.\n   */\n\n\n  clearCache() {\n    this.treeArray = [];\n  }\n  /**\n   * Initialize the Fenwick tree with row Heights.\n   *\n   * @param rows The array of rows which contain the expanded status.\n   * @param rowHeight The row height.\n   * @param detailRowHeight The detail row height.\n   */\n\n\n  initCache(details) {\n    const {\n      rows,\n      rowHeight,\n      detailRowHeight,\n      externalVirtual,\n      rowCount,\n      rowIndexes,\n      rowExpansions\n    } = details;\n    const isFn = typeof rowHeight === 'function';\n    const isDetailFn = typeof detailRowHeight === 'function';\n\n    if (!isFn && isNaN(rowHeight)) {\n      throw new Error(`Row Height cache initialization failed. Please ensure that 'rowHeight' is a\n        valid number or function value: (${rowHeight}) when 'scrollbarV' is enabled.`);\n    } // Add this additional guard in case detailRowHeight is set to 'auto' as it wont work.\n\n\n    if (!isDetailFn && isNaN(detailRowHeight)) {\n      throw new Error(`Row Height cache initialization failed. Please ensure that 'detailRowHeight' is a\n        valid number or function value: (${detailRowHeight}) when 'scrollbarV' is enabled.`);\n    }\n\n    const n = externalVirtual ? rowCount : rows.length;\n    this.treeArray = new Array(n);\n\n    for (let i = 0; i < n; ++i) {\n      this.treeArray[i] = 0;\n    }\n\n    for (let i = 0; i < n; ++i) {\n      const row = rows[i];\n      let currentRowHeight = rowHeight;\n\n      if (isFn) {\n        currentRowHeight = rowHeight(row);\n      } // Add the detail row height to the already expanded rows.\n      // This is useful for the table that goes through a filter or sort.\n\n\n      const expanded = rowExpansions.has(row);\n\n      if (row && expanded) {\n        if (isDetailFn) {\n          const index = rowIndexes.get(row);\n          currentRowHeight += detailRowHeight(row, index);\n        } else {\n          currentRowHeight += detailRowHeight;\n        }\n      }\n\n      this.update(i, currentRowHeight);\n    }\n  }\n  /**\n   * Given the ScrollY position i.e. sum, provide the rowIndex\n   * that is present in the current view port.  Below handles edge cases.\n   */\n\n\n  getRowIndex(scrollY) {\n    if (scrollY === 0) return 0;\n    return this.calcRowIndex(scrollY);\n  }\n  /**\n   * When a row is expanded or rowHeight is changed, update the height.  This can\n   * be utilized in future when Angular Data table supports dynamic row heights.\n   */\n\n\n  update(atRowIndex, byRowHeight) {\n    if (!this.treeArray.length) {\n      throw new Error(`Update at index ${atRowIndex} with value ${byRowHeight} failed:\n        Row Height cache not initialized.`);\n    }\n\n    const n = this.treeArray.length;\n    atRowIndex |= 0;\n\n    while (atRowIndex < n) {\n      this.treeArray[atRowIndex] += byRowHeight;\n      atRowIndex |= atRowIndex + 1;\n    }\n  }\n  /**\n   * Range Sum query from 1 to the rowIndex\n   */\n\n\n  query(atIndex) {\n    if (!this.treeArray.length) {\n      throw new Error(`query at index ${atIndex} failed: Fenwick tree array not initialized.`);\n    }\n\n    let sum = 0;\n    atIndex |= 0;\n\n    while (atIndex >= 0) {\n      sum += this.treeArray[atIndex];\n      atIndex = (atIndex & atIndex + 1) - 1;\n    }\n\n    return sum;\n  }\n  /**\n   * Find the total height between 2 row indexes\n   */\n\n\n  queryBetween(atIndexA, atIndexB) {\n    return this.query(atIndexB) - this.query(atIndexA - 1);\n  }\n  /**\n   * Given the ScrollY position i.e. sum, provide the rowIndex\n   * that is present in the current view port.\n   */\n\n\n  calcRowIndex(sum) {\n    if (!this.treeArray.length) return 0;\n    let pos = -1;\n    const dataLength = this.treeArray.length; // Get the highest bit for the block size.\n\n    const highestBit = Math.pow(2, dataLength.toString(2).length - 1);\n\n    for (let blockSize = highestBit; blockSize !== 0; blockSize >>= 1) {\n      const nextPos = pos + blockSize;\n\n      if (nextPos < dataLength && sum >= this.treeArray[nextPos]) {\n        sum -= this.treeArray[nextPos];\n        pos = nextPos;\n      }\n    }\n\n    return pos + 1;\n  }\n\n}\n\nconst cache = {};\nconst testStyle = typeof document !== 'undefined' ? document.createElement('div').style : undefined;\n\nconst ɵ0 = function () {\n  const styles = typeof window !== 'undefined' ? window.getComputedStyle(document.documentElement, '') : undefined;\n  const match = typeof styles !== 'undefined' ? Array.prototype.slice.call(styles).join('').match(/-(moz|webkit|ms)-/) : null;\n  const pre = match !== null ? match[1] : undefined; // tslint:disable-next-line: tsr-detect-non-literal-regexp\n\n  const dom = typeof pre !== 'undefined' ? 'WebKit|Moz|MS|O'.match(new RegExp('(' + pre + ')', 'i'))[1] : undefined;\n  return dom ? {\n    dom,\n    lowercase: pre,\n    css: `-${pre}-`,\n    js: pre[0].toUpperCase() + pre.substr(1)\n  } : undefined;\n}; // Get Prefix\n// http://davidwalsh.name/vendor-prefix\n\n\nconst prefix = ɵ0();\n\nfunction getVendorPrefixedName(property) {\n  const name = camelCase(property);\n\n  if (!cache[name]) {\n    if (prefix !== undefined && testStyle[prefix.css + property] !== undefined) {\n      cache[name] = prefix.css + property;\n    } else if (testStyle[property] !== undefined) {\n      cache[name] = property;\n    }\n  }\n\n  return cache[name];\n} // browser detection and prefixing tools\n\n\nconst transform = typeof window !== 'undefined' ? getVendorPrefixedName('transform') : undefined;\nconst backfaceVisibility = typeof window !== 'undefined' ? getVendorPrefixedName('backfaceVisibility') : undefined;\nconst hasCSSTransforms = typeof window !== 'undefined' ? !!getVendorPrefixedName('transform') : undefined;\nconst hasCSS3DTransforms = typeof window !== 'undefined' ? !!getVendorPrefixedName('perspective') : undefined;\nconst ua = typeof window !== 'undefined' ? window.navigator.userAgent : 'Chrome';\nconst isSafari = /Safari\\//.test(ua) && !/Chrome\\//.test(ua);\n\nfunction translateXY(styles, x, y) {\n  if (typeof transform !== 'undefined' && hasCSSTransforms) {\n    if (!isSafari && hasCSS3DTransforms) {\n      styles[transform] = `translate3d(${x}px, ${y}px, 0)`;\n      styles[backfaceVisibility] = 'hidden';\n    } else {\n      styles[camelCase(transform)] = `translate(${x}px, ${y}px)`;\n    }\n  } else {\n    styles.top = `${y}px`;\n    styles.left = `${x}px`;\n  }\n}\n\nlet DataTableBodyComponent = /*#__PURE__*/(() => {\n  class DataTableBodyComponent {\n    /**\n     * Creates an instance of DataTableBodyComponent.\n     */\n    constructor(cd) {\n      this.cd = cd;\n      this.selected = [];\n      this.scroll = new EventEmitter();\n      this.page = new EventEmitter();\n      this.activate = new EventEmitter();\n      this.select = new EventEmitter();\n      this.detailToggle = new EventEmitter();\n      this.rowContextmenu = new EventEmitter(false);\n      this.treeAction = new EventEmitter();\n      this.rowHeightsCache = new RowHeightCache();\n      this.temp = [];\n      this.offsetY = 0;\n      this.indexes = {};\n      this.rowIndexes = new WeakMap();\n      this.rowExpansions = [];\n      /**\n       * Get the height of the detail row.\n       */\n\n      this.getDetailRowHeight = (row, index) => {\n        if (!this.rowDetail) {\n          return 0;\n        }\n\n        const rowHeight = this.rowDetail.rowHeight;\n        return typeof rowHeight === 'function' ? rowHeight(row, index) : rowHeight;\n      }; // declare fn here so we can get access to the `this` property\n\n\n      this.rowTrackingFn = (index, row) => {\n        const idx = this.getRowIndex(row);\n\n        if (this.trackByProp) {\n          return row[this.trackByProp];\n        } else {\n          return idx;\n        }\n      };\n    }\n\n    set pageSize(val) {\n      this._pageSize = val;\n      this.recalcLayout();\n    }\n\n    get pageSize() {\n      return this._pageSize;\n    }\n\n    set rows(val) {\n      this._rows = val;\n      this.recalcLayout();\n    }\n\n    get rows() {\n      return this._rows;\n    }\n\n    set columns(val) {\n      this._columns = val;\n      const colsByPin = columnsByPin(val);\n      this.columnGroupWidths = columnGroupWidths(colsByPin, val);\n    }\n\n    get columns() {\n      return this._columns;\n    }\n\n    set offset(val) {\n      this._offset = val;\n      if (!this.scrollbarV || this.scrollbarV && !this.virtualization) this.recalcLayout();\n    }\n\n    get offset() {\n      return this._offset;\n    }\n\n    set rowCount(val) {\n      this._rowCount = val;\n      this.recalcLayout();\n    }\n\n    get rowCount() {\n      return this._rowCount;\n    }\n\n    get bodyWidth() {\n      if (this.scrollbarH) {\n        return this.innerWidth + 'px';\n      } else {\n        return '100%';\n      }\n    }\n\n    set bodyHeight(val) {\n      if (this.scrollbarV) {\n        this._bodyHeight = val + 'px';\n      } else {\n        this._bodyHeight = 'auto';\n      }\n\n      this.recalcLayout();\n    }\n\n    get bodyHeight() {\n      return this._bodyHeight;\n    }\n    /**\n     * Returns if selection is enabled.\n     */\n\n\n    get selectEnabled() {\n      return !!this.selectionType;\n    }\n    /**\n     * Property that would calculate the height of scroll bar\n     * based on the row heights cache for virtual scroll and virtualization. Other scenarios\n     * calculate scroll height automatically (as height will be undefined).\n     */\n\n\n    get scrollHeight() {\n      if (this.scrollbarV && this.virtualization && this.rowCount) {\n        return this.rowHeightsCache.query(this.rowCount - 1);\n      } // avoid TS7030: Not all code paths return a value.\n\n\n      return undefined;\n    }\n    /**\n     * Called after the constructor, initializing input properties\n     */\n\n\n    ngOnInit() {\n      if (this.rowDetail) {\n        this.listener = this.rowDetail.toggle.subscribe(({\n          type,\n          value\n        }) => {\n          if (type === 'row') {\n            this.toggleRowExpansion(value);\n          }\n\n          if (type === 'all') {\n            this.toggleAllRows(value);\n          } // Refresh rows after toggle\n          // Fixes #883\n\n\n          this.updateIndexes();\n          this.updateRows();\n          this.cd.markForCheck();\n        });\n      }\n\n      if (this.groupHeader) {\n        this.listener = this.groupHeader.toggle.subscribe(({\n          type,\n          value\n        }) => {\n          if (type === 'group') {\n            this.toggleRowExpansion(value);\n          }\n\n          if (type === 'all') {\n            this.toggleAllRows(value);\n          } // Refresh rows after toggle\n          // Fixes #883\n\n\n          this.updateIndexes();\n          this.updateRows();\n          this.cd.markForCheck();\n        });\n      }\n    }\n    /**\n     * Called once, before the instance is destroyed.\n     */\n\n\n    ngOnDestroy() {\n      if (this.rowDetail || this.groupHeader) {\n        this.listener.unsubscribe();\n      }\n    }\n    /**\n     * Updates the Y offset given a new offset.\n     */\n\n\n    updateOffsetY(offset) {\n      // scroller is missing on empty table\n      if (!this.scroller) {\n        return;\n      }\n\n      if (this.scrollbarV && this.virtualization && offset) {\n        // First get the row Index that we need to move to.\n        const rowIndex = this.pageSize * offset;\n        offset = this.rowHeightsCache.query(rowIndex - 1);\n      } else if (this.scrollbarV && !this.virtualization) {\n        offset = 0;\n      }\n\n      this.scroller.setOffset(offset || 0);\n    }\n    /**\n     * Body was scrolled, this is mainly useful for\n     * when a user is server-side pagination via virtual scroll.\n     */\n\n\n    onBodyScroll(event) {\n      const scrollYPos = event.scrollYPos;\n      const scrollXPos = event.scrollXPos; // if scroll change, trigger update\n      // this is mainly used for header cell positions\n\n      if (this.offsetY !== scrollYPos || this.offsetX !== scrollXPos) {\n        this.scroll.emit({\n          offsetY: scrollYPos,\n          offsetX: scrollXPos\n        });\n      }\n\n      this.offsetY = scrollYPos;\n      this.offsetX = scrollXPos;\n      this.updateIndexes();\n      this.updatePage(event.direction);\n      this.updateRows();\n    }\n    /**\n     * Updates the page given a direction.\n     */\n\n\n    updatePage(direction) {\n      let offset = this.indexes.first / this.pageSize;\n\n      if (direction === 'up') {\n        offset = Math.ceil(offset);\n      } else if (direction === 'down') {\n        offset = Math.floor(offset);\n      }\n\n      if (direction !== undefined && !isNaN(offset)) {\n        this.page.emit({\n          offset\n        });\n      }\n    }\n    /**\n     * Updates the rows in the view port\n     */\n\n\n    updateRows() {\n      const {\n        first,\n        last\n      } = this.indexes;\n      let rowIndex = first;\n      let idx = 0;\n      const temp = []; // if grouprowsby has been specified treat row paging\n      // parameters as group paging parameters ie if limit 10 has been\n      // specified treat it as 10 groups rather than 10 rows\n\n      if (this.groupedRows) {\n        let maxRowsPerGroup = 3; // if there is only one group set the maximum number of\n        // rows per group the same as the total number of rows\n\n        if (this.groupedRows.length === 1) {\n          maxRowsPerGroup = this.groupedRows[0].value.length;\n        }\n\n        while (rowIndex < last && rowIndex < this.groupedRows.length) {\n          // Add the groups into this page\n          const group = this.groupedRows[rowIndex];\n          this.rowIndexes.set(group, rowIndex);\n\n          if (group.value) {\n            // add indexes for each group item\n            group.value.forEach((g, i) => {\n              const _idx = `${rowIndex}-${i}`;\n              this.rowIndexes.set(g, _idx);\n            });\n          }\n\n          temp[idx] = group;\n          idx++; // Group index in this context\n\n          rowIndex++;\n        }\n      } else {\n        while (rowIndex < last && rowIndex < this.rowCount) {\n          const row = this.rows[rowIndex];\n\n          if (row) {\n            // add indexes for each row\n            this.rowIndexes.set(row, rowIndex);\n            temp[idx] = row;\n          }\n\n          idx++;\n          rowIndex++;\n        }\n      }\n\n      this.temp = temp;\n    }\n    /**\n     * Get the row height\n     */\n\n\n    getRowHeight(row) {\n      // if its a function return it\n      if (typeof this.rowHeight === 'function') {\n        return this.rowHeight(row);\n      }\n\n      return this.rowHeight;\n    }\n    /**\n     * @param group the group with all rows\n     */\n\n\n    getGroupHeight(group) {\n      let rowHeight = 0;\n\n      if (group.value) {\n        for (let index = 0; index < group.value.length; index++) {\n          rowHeight += this.getRowAndDetailHeight(group.value[index]);\n        }\n      }\n\n      return rowHeight;\n    }\n    /**\n     * Calculate row height based on the expanded state of the row.\n     */\n\n\n    getRowAndDetailHeight(row) {\n      let rowHeight = this.getRowHeight(row);\n      const expanded = this.getRowExpanded(row); // Adding detail row height if its expanded.\n\n      if (expanded) {\n        rowHeight += this.getDetailRowHeight(row);\n      }\n\n      return rowHeight;\n    }\n    /**\n     * Calculates the styles for the row so that the rows can be moved in 2D space\n     * during virtual scroll inside the DOM.   In the below case the Y position is\n     * manipulated.   As an example, if the height of row 0 is 30 px and row 1 is\n     * 100 px then following styles are generated:\n     *\n     * transform: translate3d(0px, 0px, 0px);    ->  row0\n     * transform: translate3d(0px, 30px, 0px);   ->  row1\n     * transform: translate3d(0px, 130px, 0px);  ->  row2\n     *\n     * Row heights have to be calculated based on the row heights cache as we wont\n     * be able to determine which row is of what height before hand.  In the above\n     * case the positionY of the translate3d for row2 would be the sum of all the\n     * heights of the rows before it (i.e. row0 and row1).\n     *\n     * @param rows the row that needs to be placed in the 2D space.\n     * @returns the CSS3 style to be applied\n     *\n     * @memberOf DataTableBodyComponent\n     */\n\n\n    getRowsStyles(rows) {\n      const styles = {}; // only add styles for the group if there is a group\n\n      if (this.groupedRows) {\n        styles.width = this.columnGroupWidths.total;\n      }\n\n      if (this.scrollbarV && this.virtualization) {\n        let idx = 0;\n\n        if (this.groupedRows) {\n          // Get the latest row rowindex in a group\n          const row = rows[rows.length - 1];\n          idx = row ? this.getRowIndex(row) : 0;\n        } else {\n          idx = this.getRowIndex(rows);\n        } // const pos = idx * rowHeight;\n        // The position of this row would be the sum of all row heights\n        // until the previous row position.\n\n\n        const pos = this.rowHeightsCache.query(idx - 1);\n        translateXY(styles, 0, pos);\n      }\n\n      return styles;\n    }\n    /**\n     * Calculate bottom summary row offset for scrollbar mode.\n     * For more information about cache and offset calculation\n     * see description for `getRowsStyles` method\n     *\n     * @returns the CSS3 style to be applied\n     *\n     * @memberOf DataTableBodyComponent\n     */\n\n\n    getBottomSummaryRowStyles() {\n      if (!this.scrollbarV || !this.rows || !this.rows.length) {\n        return null;\n      }\n\n      const styles = {\n        position: 'absolute'\n      };\n      const pos = this.rowHeightsCache.query(this.rows.length - 1);\n      translateXY(styles, 0, pos);\n      return styles;\n    }\n    /**\n     * Hides the loading indicator\n     */\n\n\n    hideIndicator() {\n      setTimeout(() => this.loadingIndicator = false, 500);\n    }\n    /**\n     * Updates the index of the rows in the viewport\n     */\n\n\n    updateIndexes() {\n      let first = 0;\n      let last = 0;\n\n      if (this.scrollbarV) {\n        if (this.virtualization) {\n          // Calculation of the first and last indexes will be based on where the\n          // scrollY position would be at.  The last index would be the one\n          // that shows up inside the view port the last.\n          const height = parseInt(this.bodyHeight, 0);\n          first = this.rowHeightsCache.getRowIndex(this.offsetY);\n          last = this.rowHeightsCache.getRowIndex(height + this.offsetY) + 1;\n        } else {\n          // If virtual rows are not needed\n          // We render all in one go\n          first = 0;\n          last = this.rowCount;\n        }\n      } else {\n        // The server is handling paging and will pass an array that begins with the\n        // element at a specified offset.  first should always be 0 with external paging.\n        if (!this.externalPaging) {\n          first = Math.max(this.offset * this.pageSize, 0);\n        }\n\n        last = Math.min(first + this.pageSize, this.rowCount);\n      }\n\n      this.indexes = {\n        first,\n        last\n      };\n    }\n    /**\n     * Refreshes the full Row Height cache.  Should be used\n     * when the entire row array state has changed.\n     */\n\n\n    refreshRowHeightCache() {\n      if (!this.scrollbarV || this.scrollbarV && !this.virtualization) {\n        return;\n      } // clear the previous row height cache if already present.\n      // this is useful during sorts, filters where the state of the\n      // rows array is changed.\n\n\n      this.rowHeightsCache.clearCache(); // Initialize the tree only if there are rows inside the tree.\n\n      if (this.rows && this.rows.length) {\n        const rowExpansions = new Set();\n\n        for (const row of this.rows) {\n          if (this.getRowExpanded(row)) {\n            rowExpansions.add(row);\n          }\n        }\n\n        this.rowHeightsCache.initCache({\n          rows: this.rows,\n          rowHeight: this.rowHeight,\n          detailRowHeight: this.getDetailRowHeight,\n          externalVirtual: this.scrollbarV && this.externalPaging,\n          rowCount: this.rowCount,\n          rowIndexes: this.rowIndexes,\n          rowExpansions\n        });\n      }\n    }\n    /**\n     * Gets the index for the view port\n     */\n\n\n    getAdjustedViewPortIndex() {\n      // Capture the row index of the first row that is visible on the viewport.\n      // If the scroll bar is just below the row which is highlighted then make that as the\n      // first index.\n      const viewPortFirstRowIndex = this.indexes.first;\n\n      if (this.scrollbarV && this.virtualization) {\n        const offsetScroll = this.rowHeightsCache.query(viewPortFirstRowIndex - 1);\n        return offsetScroll <= this.offsetY ? viewPortFirstRowIndex - 1 : viewPortFirstRowIndex;\n      }\n\n      return viewPortFirstRowIndex;\n    }\n    /**\n     * Toggle the Expansion of the row i.e. if the row is expanded then it will\n     * collapse and vice versa.   Note that the expanded status is stored as\n     * a part of the row object itself as we have to preserve the expanded row\n     * status in case of sorting and filtering of the row set.\n     */\n\n\n    toggleRowExpansion(row) {\n      // Capture the row index of the first row that is visible on the viewport.\n      const viewPortFirstRowIndex = this.getAdjustedViewPortIndex();\n      const rowExpandedIdx = this.getRowExpandedIdx(row, this.rowExpansions);\n      const expanded = rowExpandedIdx > -1; // If the detailRowHeight is auto --> only in case of non-virtualized scroll\n\n      if (this.scrollbarV && this.virtualization) {\n        const detailRowHeight = this.getDetailRowHeight(row) * (expanded ? -1 : 1); // const idx = this.rowIndexes.get(row) || 0;\n\n        const idx = this.getRowIndex(row);\n        this.rowHeightsCache.update(idx, detailRowHeight);\n      } // Update the toggled row and update thive nevere heights in the cache.\n\n\n      if (expanded) {\n        this.rowExpansions.splice(rowExpandedIdx, 1);\n      } else {\n        this.rowExpansions.push(row);\n      }\n\n      this.detailToggle.emit({\n        rows: [row],\n        currentIndex: viewPortFirstRowIndex\n      });\n    }\n    /**\n     * Expand/Collapse all the rows no matter what their state is.\n     */\n\n\n    toggleAllRows(expanded) {\n      // clear prev expansions\n      this.rowExpansions = []; // Capture the row index of the first row that is visible on the viewport.\n\n      const viewPortFirstRowIndex = this.getAdjustedViewPortIndex();\n\n      if (expanded) {\n        for (const row of this.rows) {\n          this.rowExpansions.push(row);\n        }\n      }\n\n      if (this.scrollbarV) {\n        // Refresh the full row heights cache since every row was affected.\n        this.recalcLayout();\n      } // Emit all rows that have been expanded.\n\n\n      this.detailToggle.emit({\n        rows: this.rows,\n        currentIndex: viewPortFirstRowIndex\n      });\n    }\n    /**\n     * Recalculates the table\n     */\n\n\n    recalcLayout() {\n      this.refreshRowHeightCache();\n      this.updateIndexes();\n      this.updateRows();\n    }\n    /**\n     * Tracks the column\n     */\n\n\n    columnTrackingFn(index, column) {\n      return column.$$id;\n    }\n    /**\n     * Gets the row pinning group styles\n     */\n\n\n    stylesByGroup(group) {\n      const widths = this.columnGroupWidths;\n      const offsetX = this.offsetX;\n      const styles = {\n        width: `${widths[group]}px`\n      };\n\n      if (group === 'left') {\n        translateXY(styles, offsetX, 0);\n      } else if (group === 'right') {\n        const bodyWidth = parseInt(this.innerWidth + '', 0);\n        const totalDiff = widths.total - bodyWidth;\n        const offsetDiff = totalDiff - offsetX;\n        const offset = offsetDiff * -1;\n        translateXY(styles, offset, 0);\n      }\n\n      return styles;\n    }\n    /**\n     * Returns if the row was expanded and set default row expansion when row expansion is empty\n     */\n\n\n    getRowExpanded(row) {\n      if (this.rowExpansions.length === 0 && this.groupExpansionDefault) {\n        for (const group of this.groupedRows) {\n          this.rowExpansions.push(group);\n        }\n      }\n\n      return this.getRowExpandedIdx(row, this.rowExpansions) > -1;\n    }\n\n    getRowExpandedIdx(row, expanded) {\n      if (!expanded || !expanded.length) return -1;\n      const rowId = this.rowIdentity(row);\n      return expanded.findIndex(r => {\n        const id = this.rowIdentity(r);\n        return id === rowId;\n      });\n    }\n    /**\n     * Gets the row index given a row\n     */\n\n\n    getRowIndex(row) {\n      return this.rowIndexes.get(row) || 0;\n    }\n\n    onTreeAction(row) {\n      this.treeAction.emit({\n        row\n      });\n    }\n\n  }\n\n  DataTableBodyComponent.ɵfac = function DataTableBodyComponent_Factory(t) {\n    return new (t || DataTableBodyComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef));\n  };\n\n  DataTableBodyComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: DataTableBodyComponent,\n    selectors: [[\"datatable-body\"]],\n    viewQuery: function DataTableBodyComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵviewQuery(ScrollerComponent, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n      }\n    },\n    hostAttrs: [1, \"datatable-body\"],\n    hostVars: 4,\n    hostBindings: function DataTableBodyComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵstyleProp(\"width\", ctx.bodyWidth)(\"height\", ctx.bodyHeight);\n      }\n    },\n    inputs: {\n      selected: \"selected\",\n      pageSize: \"pageSize\",\n      rows: \"rows\",\n      columns: \"columns\",\n      offset: \"offset\",\n      rowCount: \"rowCount\",\n      bodyHeight: \"bodyHeight\",\n      offsetX: \"offsetX\",\n      loadingIndicator: \"loadingIndicator\",\n      scrollbarV: \"scrollbarV\",\n      scrollbarH: \"scrollbarH\",\n      externalPaging: \"externalPaging\",\n      rowHeight: \"rowHeight\",\n      emptyMessage: \"emptyMessage\",\n      selectionType: \"selectionType\",\n      rowIdentity: \"rowIdentity\",\n      rowDetail: \"rowDetail\",\n      groupHeader: \"groupHeader\",\n      selectCheck: \"selectCheck\",\n      displayCheck: \"displayCheck\",\n      trackByProp: \"trackByProp\",\n      rowClass: \"rowClass\",\n      groupedRows: \"groupedRows\",\n      groupExpansionDefault: \"groupExpansionDefault\",\n      innerWidth: \"innerWidth\",\n      groupRowsBy: \"groupRowsBy\",\n      virtualization: \"virtualization\",\n      summaryRow: \"summaryRow\",\n      summaryPosition: \"summaryPosition\",\n      summaryHeight: \"summaryHeight\"\n    },\n    outputs: {\n      scroll: \"scroll\",\n      page: \"page\",\n      activate: \"activate\",\n      select: \"select\",\n      detailToggle: \"detailToggle\",\n      rowContextmenu: \"rowContextmenu\",\n      treeAction: \"treeAction\"\n    },\n    decls: 5,\n    vars: 9,\n    consts: [[4, \"ngIf\"], [3, \"selected\", \"rows\", \"selectCheck\", \"selectEnabled\", \"selectionType\", \"rowIdentity\", \"select\", \"activate\"], [\"selector\", \"\"], [3, \"scrollbarV\", \"scrollbarH\", \"scrollHeight\", \"scrollWidth\", \"scroll\", 4, \"ngIf\"], [\"class\", \"empty-row\", 3, \"innerHTML\", 4, \"ngIf\"], [3, \"scrollbarV\", \"scrollbarH\", \"scrollHeight\", \"scrollWidth\", \"scroll\"], [3, \"rowHeight\", \"offsetX\", \"innerWidth\", \"rows\", \"columns\", 4, \"ngIf\"], [3, \"groupedRows\", \"innerWidth\", \"ngStyle\", \"rowDetail\", \"groupHeader\", \"offsetX\", \"detailRowHeight\", \"row\", \"expanded\", \"rowIndex\", \"rowContextmenu\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"role\", \"row\", 3, \"ngStyle\", \"rowHeight\", \"offsetX\", \"innerWidth\", \"rows\", \"columns\", 4, \"ngIf\"], [3, \"rowHeight\", \"offsetX\", \"innerWidth\", \"rows\", \"columns\"], [3, \"groupedRows\", \"innerWidth\", \"ngStyle\", \"rowDetail\", \"groupHeader\", \"offsetX\", \"detailRowHeight\", \"row\", \"expanded\", \"rowIndex\", \"rowContextmenu\"], [\"role\", \"row\", \"tabindex\", \"-1\", 3, \"isSelected\", \"innerWidth\", \"offsetX\", \"columns\", \"rowHeight\", \"row\", \"rowIndex\", \"expanded\", \"rowClass\", \"displayCheck\", \"treeStatus\", \"treeAction\", \"activate\", 4, \"ngIf\", \"ngIfElse\"], [\"groupedRowsTemplate\", \"\"], [\"role\", \"row\", \"tabindex\", \"-1\", 3, \"isSelected\", \"innerWidth\", \"offsetX\", \"columns\", \"rowHeight\", \"row\", \"rowIndex\", \"expanded\", \"rowClass\", \"displayCheck\", \"treeStatus\", \"treeAction\", \"activate\"], [\"role\", \"row\", \"tabindex\", \"-1\", 3, \"isSelected\", \"innerWidth\", \"offsetX\", \"columns\", \"rowHeight\", \"row\", \"group\", \"rowIndex\", \"expanded\", \"rowClass\", \"activate\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"role\", \"row\", \"tabindex\", \"-1\", 3, \"isSelected\", \"innerWidth\", \"offsetX\", \"columns\", \"rowHeight\", \"row\", \"group\", \"rowIndex\", \"expanded\", \"rowClass\", \"activate\"], [\"role\", \"row\", 3, \"ngStyle\", \"rowHeight\", \"offsetX\", \"innerWidth\", \"rows\", \"columns\"], [1, \"empty-row\", 3, \"innerHTML\"]],\n    template: function DataTableBodyComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, DataTableBodyComponent_datatable_progress_0_Template, 1, 0, \"datatable-progress\", 0);\n        ɵngcc0.ɵɵelementStart(1, \"datatable-selection\", 1, 2);\n        ɵngcc0.ɵɵlistener(\"select\", function DataTableBodyComponent_Template_datatable_selection_select_1_listener($event) {\n          return ctx.select.emit($event);\n        })(\"activate\", function DataTableBodyComponent_Template_datatable_selection_activate_1_listener($event) {\n          return ctx.activate.emit($event);\n        });\n        ɵngcc0.ɵɵtemplate(3, DataTableBodyComponent_datatable_scroller_3_Template, 4, 8, \"datatable-scroller\", 3);\n        ɵngcc0.ɵɵtemplate(4, DataTableBodyComponent_div_4_Template, 1, 1, \"div\", 4);\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.loadingIndicator);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"selected\", ctx.selected)(\"rows\", ctx.rows)(\"selectCheck\", ctx.selectCheck)(\"selectEnabled\", ctx.selectEnabled)(\"selectionType\", ctx.selectionType)(\"rowIdentity\", ctx.rowIdentity);\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.rows == null ? null : ctx.rows.length);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", !(ctx.rows == null ? null : ctx.rows.length) && !ctx.loadingIndicator);\n      }\n    },\n    directives: function () {\n      return [ɵngcc1.NgIf, ProgressBarComponent, DataTableSelectionComponent, ScrollerComponent, DataTableSummaryRowComponent, ɵngcc1.NgForOf, DataTableRowWrapperComponent, ɵngcc1.NgStyle, DataTableBodyRowComponent];\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return DataTableBodyComponent;\n})();\nlet DataTableHeaderComponent = /*#__PURE__*/(() => {\n  class DataTableHeaderComponent {\n    constructor(cd) {\n      this.cd = cd;\n      this.sort = new EventEmitter();\n      this.reorder = new EventEmitter();\n      this.resize = new EventEmitter();\n      this.select = new EventEmitter();\n      this.columnContextmenu = new EventEmitter(false);\n      this._columnGroupWidths = {\n        total: 100\n      };\n      this._styleByGroup = {\n        left: {},\n        center: {},\n        right: {}\n      };\n      this.destroyed = false;\n    }\n\n    set innerWidth(val) {\n      this._innerWidth = val;\n      setTimeout(() => {\n        if (this._columns) {\n          const colByPin = columnsByPin(this._columns);\n          this._columnGroupWidths = columnGroupWidths(colByPin, this._columns);\n          this.setStylesByGroup();\n        }\n      });\n    }\n\n    get innerWidth() {\n      return this._innerWidth;\n    }\n\n    set headerHeight(val) {\n      if (val !== 'auto') {\n        this._headerHeight = `${val}px`;\n      } else {\n        this._headerHeight = val;\n      }\n    }\n\n    get headerHeight() {\n      return this._headerHeight;\n    }\n\n    set columns(val) {\n      this._columns = val;\n      const colsByPin = columnsByPin(val);\n      this._columnsByPin = columnsByPinArr(val);\n      setTimeout(() => {\n        this._columnGroupWidths = columnGroupWidths(colsByPin, val);\n        this.setStylesByGroup();\n      });\n    }\n\n    get columns() {\n      return this._columns;\n    }\n\n    set offsetX(val) {\n      this._offsetX = val;\n      this.setStylesByGroup();\n    }\n\n    get offsetX() {\n      return this._offsetX;\n    }\n\n    ngOnDestroy() {\n      this.destroyed = true;\n    }\n\n    onLongPressStart({\n      event,\n      model\n    }) {\n      model.dragging = true;\n      this.dragEventTarget = event;\n    }\n\n    onLongPressEnd({\n      event,\n      model\n    }) {\n      this.dragEventTarget = event; // delay resetting so sort can be\n      // prevented if we were dragging\n\n      setTimeout(() => {\n        // datatable component creates copies from columns on reorder\n        // set dragging to false on new objects\n        const column = this._columns.find(c => c.$$id === model.$$id);\n\n        if (column) {\n          column.dragging = false;\n        }\n      }, 5);\n    }\n\n    get headerWidth() {\n      if (this.scrollbarH) {\n        return this.innerWidth + 'px';\n      }\n\n      return '100%';\n    }\n\n    trackByGroups(index, colGroup) {\n      return colGroup.type;\n    }\n\n    columnTrackingFn(index, column) {\n      return column.$$id;\n    }\n\n    onColumnResized(width, column) {\n      if (width <= column.minWidth) {\n        width = column.minWidth;\n      } else if (width >= column.maxWidth) {\n        width = column.maxWidth;\n      }\n\n      this.resize.emit({\n        column,\n        prevValue: column.width,\n        newValue: width\n      });\n    }\n\n    onColumnReordered({\n      prevIndex,\n      newIndex,\n      model\n    }) {\n      const column = this.getColumn(newIndex);\n      column.isTarget = false;\n      column.targetMarkerContext = undefined;\n      this.reorder.emit({\n        column: model,\n        prevValue: prevIndex,\n        newValue: newIndex\n      });\n    }\n\n    onTargetChanged({\n      prevIndex,\n      newIndex,\n      initialIndex\n    }) {\n      if (prevIndex || prevIndex === 0) {\n        const oldColumn = this.getColumn(prevIndex);\n        oldColumn.isTarget = false;\n        oldColumn.targetMarkerContext = undefined;\n      }\n\n      if (newIndex || newIndex === 0) {\n        const newColumn = this.getColumn(newIndex);\n        newColumn.isTarget = true;\n\n        if (initialIndex !== newIndex) {\n          newColumn.targetMarkerContext = {\n            class: 'targetMarker '.concat(initialIndex > newIndex ? 'dragFromRight' : 'dragFromLeft')\n          };\n        }\n      }\n    }\n\n    getColumn(index) {\n      const leftColumnCount = this._columnsByPin[0].columns.length;\n\n      if (index < leftColumnCount) {\n        return this._columnsByPin[0].columns[index];\n      }\n\n      const centerColumnCount = this._columnsByPin[1].columns.length;\n\n      if (index < leftColumnCount + centerColumnCount) {\n        return this._columnsByPin[1].columns[index - leftColumnCount];\n      }\n\n      return this._columnsByPin[2].columns[index - leftColumnCount - centerColumnCount];\n    }\n\n    onSort({\n      column,\n      prevValue,\n      newValue\n    }) {\n      // if we are dragging don't sort!\n      if (column.dragging) {\n        return;\n      }\n\n      const sorts = this.calcNewSorts(column, prevValue, newValue);\n      this.sort.emit({\n        sorts,\n        column,\n        prevValue,\n        newValue\n      });\n    }\n\n    calcNewSorts(column, prevValue, newValue) {\n      let idx = 0;\n\n      if (!this.sorts) {\n        this.sorts = [];\n      }\n\n      const sorts = this.sorts.map((s, i) => {\n        s = Object.assign({}, s);\n\n        if (s.prop === column.prop) {\n          idx = i;\n        }\n\n        return s;\n      });\n\n      if (newValue === undefined) {\n        sorts.splice(idx, 1);\n      } else if (prevValue) {\n        sorts[idx].dir = newValue;\n      } else {\n        if (this.sortType === SortType.single) {\n          sorts.splice(0, this.sorts.length);\n        }\n\n        sorts.push({\n          dir: newValue,\n          prop: column.prop\n        });\n      }\n\n      return sorts;\n    }\n\n    setStylesByGroup() {\n      this._styleByGroup.left = this.calcStylesByGroup('left');\n      this._styleByGroup.center = this.calcStylesByGroup('center');\n      this._styleByGroup.right = this.calcStylesByGroup('right');\n\n      if (!this.destroyed) {\n        this.cd.detectChanges();\n      }\n    }\n\n    calcStylesByGroup(group) {\n      const widths = this._columnGroupWidths;\n      const offsetX = this.offsetX;\n      const styles = {\n        width: `${widths[group]}px`\n      };\n\n      if (group === 'center') {\n        translateXY(styles, offsetX * -1, 0);\n      } else if (group === 'right') {\n        const totalDiff = widths.total - this.innerWidth;\n        const offset = totalDiff * -1;\n        translateXY(styles, offset, 0);\n      }\n\n      return styles;\n    }\n\n  }\n\n  DataTableHeaderComponent.ɵfac = function DataTableHeaderComponent_Factory(t) {\n    return new (t || DataTableHeaderComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef));\n  };\n\n  DataTableHeaderComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: DataTableHeaderComponent,\n    selectors: [[\"datatable-header\"]],\n    hostAttrs: [1, \"datatable-header\"],\n    hostVars: 4,\n    hostBindings: function DataTableHeaderComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵstyleProp(\"height\", ctx.headerHeight)(\"width\", ctx.headerWidth);\n      }\n    },\n    inputs: {\n      innerWidth: \"innerWidth\",\n      headerHeight: \"headerHeight\",\n      columns: \"columns\",\n      offsetX: \"offsetX\",\n      sorts: \"sorts\",\n      sortAscendingIcon: \"sortAscendingIcon\",\n      sortDescendingIcon: \"sortDescendingIcon\",\n      sortUnsetIcon: \"sortUnsetIcon\",\n      scrollbarH: \"scrollbarH\",\n      dealsWithGroup: \"dealsWithGroup\",\n      targetMarkerTemplate: \"targetMarkerTemplate\",\n      sortType: \"sortType\",\n      allRowsSelected: \"allRowsSelected\",\n      selectionType: \"selectionType\",\n      reorderable: \"reorderable\"\n    },\n    outputs: {\n      sort: \"sort\",\n      reorder: \"reorder\",\n      resize: \"resize\",\n      select: \"select\",\n      columnContextmenu: \"columnContextmenu\"\n    },\n    decls: 2,\n    vars: 4,\n    consts: [[\"role\", \"row\", \"orderable\", \"\", 1, \"datatable-header-inner\", 3, \"reorder\", \"targetChanged\"], [3, \"class\", \"ngStyle\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"ngStyle\"], [\"role\", \"columnheader\", \"resizeable\", \"\", \"long-press\", \"\", \"draggable\", \"\", 3, \"resizeEnabled\", \"pressModel\", \"pressEnabled\", \"dragX\", \"dragY\", \"dragModel\", \"dragEventTarget\", \"headerHeight\", \"isTarget\", \"targetMarkerTemplate\", \"targetMarkerContext\", \"column\", \"sortType\", \"sorts\", \"selectionType\", \"sortAscendingIcon\", \"sortDescendingIcon\", \"sortUnsetIcon\", \"allRowsSelected\", \"resize\", \"longPressStart\", \"longPressEnd\", \"sort\", \"select\", \"columnContextmenu\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"role\", \"columnheader\", \"resizeable\", \"\", \"long-press\", \"\", \"draggable\", \"\", 3, \"resizeEnabled\", \"pressModel\", \"pressEnabled\", \"dragX\", \"dragY\", \"dragModel\", \"dragEventTarget\", \"headerHeight\", \"isTarget\", \"targetMarkerTemplate\", \"targetMarkerContext\", \"column\", \"sortType\", \"sorts\", \"selectionType\", \"sortAscendingIcon\", \"sortDescendingIcon\", \"sortUnsetIcon\", \"allRowsSelected\", \"resize\", \"longPressStart\", \"longPressEnd\", \"sort\", \"select\", \"columnContextmenu\"]],\n    template: function DataTableHeaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"div\", 0);\n        ɵngcc0.ɵɵlistener(\"reorder\", function DataTableHeaderComponent_Template_div_reorder_0_listener($event) {\n          return ctx.onColumnReordered($event);\n        })(\"targetChanged\", function DataTableHeaderComponent_Template_div_targetChanged_0_listener($event) {\n          return ctx.onTargetChanged($event);\n        });\n        ɵngcc0.ɵɵtemplate(1, DataTableHeaderComponent_div_1_Template, 2, 5, \"div\", 1);\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵstyleProp(\"width\", ctx._columnGroupWidths.total, \"px\");\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngForOf\", ctx._columnsByPin)(\"ngForTrackBy\", ctx.trackByGroups);\n      }\n    },\n    directives: function () {\n      return [OrderableDirective, ɵngcc1.NgForOf, ɵngcc1.NgStyle, DataTableHeaderCellComponent, ResizeableDirective, LongPressDirective, DraggableDirective];\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return DataTableHeaderComponent;\n})();\n\n/**\n * Throttle a function\n */\nfunction throttle(func, wait, options) {\n  options = options || {};\n  let context;\n  let args;\n  let result;\n  let timeout = null;\n  let previous = 0;\n\n  function later() {\n    previous = options.leading === false ? 0 : +new Date();\n    timeout = null;\n    result = func.apply(context, args);\n  }\n\n  return function () {\n    const now = +new Date();\n\n    if (!previous && options.leading === false) {\n      previous = now;\n    }\n\n    const remaining = wait - (now - previous);\n    context = this;\n    args = arguments;\n\n    if (remaining <= 0) {\n      clearTimeout(timeout);\n      timeout = null;\n      previous = now;\n      result = func.apply(context, args);\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(later, remaining);\n    }\n\n    return result;\n  };\n}\n/**\n * Throttle decorator\n *\n *  class MyClass {\n *    throttleable(10)\n *    myFn() { ... }\n *  }\n */\n\n\nfunction throttleable(duration, options) {\n  return function innerDecorator(target, key, descriptor) {\n    return {\n      configurable: true,\n      enumerable: descriptor.enumerable,\n      get: function getter() {\n        Object.defineProperty(this, key, {\n          configurable: true,\n          enumerable: descriptor.enumerable,\n          value: throttle(descriptor.value, duration, options)\n        });\n        return this[key];\n      }\n    };\n  };\n}\n/**\n * Calculates the Total Flex Grow\n */\n\n\nfunction getTotalFlexGrow(columns) {\n  let totalFlexGrow = 0;\n\n  for (const c of columns) {\n    totalFlexGrow += c.flexGrow || 0;\n  }\n\n  return totalFlexGrow;\n}\n/**\n * Adjusts the column widths.\n * Inspired by: https://github.com/facebook/fixed-data-table/blob/master/src/FixedDataTableWidthHelper.js\n */\n\n\nfunction adjustColumnWidths(allColumns, expectedWidth) {\n  const columnsWidth = columnsTotalWidth(allColumns);\n  const totalFlexGrow = getTotalFlexGrow(allColumns);\n  const colsByGroup = columnsByPin(allColumns);\n\n  if (columnsWidth !== expectedWidth) {\n    scaleColumns(colsByGroup, expectedWidth, totalFlexGrow);\n  }\n}\n/**\n * Resizes columns based on the flexGrow property, while respecting manually set widths\n */\n\n\nfunction scaleColumns(colsByGroup, maxWidth, totalFlexGrow) {\n  // calculate total width and flexgrow points for coulumns that can be resized\n  for (const attr in colsByGroup) {\n    for (const column of colsByGroup[attr]) {\n      if (!column.canAutoResize) {\n        maxWidth -= column.width;\n        totalFlexGrow -= column.flexGrow ? column.flexGrow : 0;\n      } else {\n        column.width = 0;\n      }\n    }\n  }\n\n  const hasMinWidth = {};\n  let remainingWidth = maxWidth; // resize columns until no width is left to be distributed\n\n  do {\n    const widthPerFlexPoint = remainingWidth / totalFlexGrow;\n    remainingWidth = 0;\n\n    for (const attr in colsByGroup) {\n      for (const column of colsByGroup[attr]) {\n        // if the column can be resize and it hasn't reached its minimum width yet\n        if (column.canAutoResize && !hasMinWidth[column.prop]) {\n          const newWidth = column.width + column.flexGrow * widthPerFlexPoint;\n\n          if (column.minWidth !== undefined && newWidth < column.minWidth) {\n            remainingWidth += newWidth - column.minWidth;\n            column.width = column.minWidth;\n            hasMinWidth[column.prop] = true;\n          } else {\n            column.width = newWidth;\n          }\n        }\n      }\n    }\n  } while (remainingWidth !== 0);\n}\n/**\n * Forces the width of the columns to\n * distribute equally but overflowing when necessary\n *\n * Rules:\n *\n *  - If combined withs are less than the total width of the grid,\n *    proportion the widths given the min / max / normal widths to fill the width.\n *\n *  - If the combined widths, exceed the total width of the grid,\n *    use the standard widths.\n *\n *  - If a column is resized, it should always use that width\n *\n *  - The proportional widths should never fall below min size if specified.\n *\n *  - If the grid starts off small but then becomes greater than the size ( + / - )\n *    the width should use the original width; not the newly proportioned widths.\n */\n\n\nfunction forceFillColumnWidths(allColumns, expectedWidth, startIdx, allowBleed, defaultColWidth = 300) {\n  const columnsToResize = allColumns.slice(startIdx + 1, allColumns.length).filter(c => {\n    return c.canAutoResize !== false;\n  });\n\n  for (const column of columnsToResize) {\n    if (!column.$$oldWidth) {\n      column.$$oldWidth = column.width;\n    }\n  }\n\n  let additionWidthPerColumn = 0;\n  let exceedsWindow = false;\n  let contentWidth = getContentWidth(allColumns, defaultColWidth);\n  let remainingWidth = expectedWidth - contentWidth;\n  const columnsProcessed = [];\n  const remainingWidthLimit = 1; // when to stop\n  // This loop takes care of the\n\n  do {\n    additionWidthPerColumn = remainingWidth / columnsToResize.length;\n    exceedsWindow = contentWidth >= expectedWidth;\n\n    for (const column of columnsToResize) {\n      if (exceedsWindow && allowBleed) {\n        column.width = column.$$oldWidth || column.width || defaultColWidth;\n      } else {\n        const newSize = (column.width || defaultColWidth) + additionWidthPerColumn;\n\n        if (column.minWidth && newSize < column.minWidth) {\n          column.width = column.minWidth;\n          columnsProcessed.push(column);\n        } else if (column.maxWidth && newSize > column.maxWidth) {\n          column.width = column.maxWidth;\n          columnsProcessed.push(column);\n        } else {\n          column.width = newSize;\n        }\n      }\n\n      column.width = Math.max(0, column.width);\n    }\n\n    contentWidth = getContentWidth(allColumns);\n    remainingWidth = expectedWidth - contentWidth;\n    removeProcessedColumns(columnsToResize, columnsProcessed);\n  } while (remainingWidth > remainingWidthLimit && columnsToResize.length !== 0);\n}\n/**\n * Remove the processed columns from the current active columns.\n */\n\n\nfunction removeProcessedColumns(columnsToResize, columnsProcessed) {\n  for (const column of columnsProcessed) {\n    const index = columnsToResize.indexOf(column);\n    columnsToResize.splice(index, 1);\n  }\n}\n/**\n * Gets the width of the columns\n */\n\n\nfunction getContentWidth(allColumns, defaultColWidth = 300) {\n  let contentWidth = 0;\n\n  for (const column of allColumns) {\n    contentWidth += column.width || defaultColWidth;\n  }\n\n  return contentWidth;\n}\n\nvar SortDirection = /*#__PURE__*/(() => {\n  (function (SortDirection) {\n    SortDirection[\"asc\"] = \"asc\";\n    SortDirection[\"desc\"] = \"desc\";\n  })(SortDirection || (SortDirection = {}));\n\n  return SortDirection;\n})();\n\n/**\n * Gets the next sort direction\n */\nfunction nextSortDir(sortType, current) {\n  if (sortType === SortType.single) {\n    if (current === SortDirection.asc) {\n      return SortDirection.desc;\n    } else {\n      return SortDirection.asc;\n    }\n  } else {\n    if (!current) {\n      return SortDirection.asc;\n    } else if (current === SortDirection.asc) {\n      return SortDirection.desc;\n    } else if (current === SortDirection.desc) {\n      return undefined;\n    } // avoid TS7030: Not all code paths return a value.\n\n\n    return undefined;\n  }\n}\n/**\n * Adapted from fueld-ui on 6/216\n * https://github.com/FuelInteractive/fuel-ui/tree/master/src/pipes/OrderBy\n */\n\n\nfunction orderByComparator(a, b) {\n  if (a === null || typeof a === 'undefined') a = 0;\n  if (b === null || typeof b === 'undefined') b = 0;\n\n  if (a instanceof Date && b instanceof Date) {\n    if (a < b) return -1;\n    if (a > b) return 1;\n  } else if (isNaN(parseFloat(a)) || !isFinite(a) || isNaN(parseFloat(b)) || !isFinite(b)) {\n    // Convert to string in case of a=0 or b=0\n    a = String(a);\n    b = String(b); // Isn't a number so lowercase the string to properly compare\n\n    if (a.toLowerCase() < b.toLowerCase()) return -1;\n    if (a.toLowerCase() > b.toLowerCase()) return 1;\n  } else {\n    // Parse strings as numbers to compare properly\n    if (parseFloat(a) < parseFloat(b)) return -1;\n    if (parseFloat(a) > parseFloat(b)) return 1;\n  } // equal each other\n\n\n  return 0;\n}\n/**\n * creates a shallow copy of the `rows` input and returns the sorted copy. this function\n * does not sort the `rows` argument in place\n */\n\n\nfunction sortRows(rows, columns, dirs) {\n  if (!rows) return [];\n  if (!dirs || !dirs.length || !columns) return [...rows];\n  /**\n   * record the row ordering of results from prior sort operations (if applicable)\n   * this is necessary to guarantee stable sorting behavior\n   */\n\n  const rowToIndexMap = new Map();\n  rows.forEach((row, index) => rowToIndexMap.set(row, index));\n  const temp = [...rows];\n  const cols = columns.reduce((obj, col) => {\n    if (col.comparator && typeof col.comparator === 'function') {\n      obj[col.prop] = col.comparator;\n    }\n\n    return obj;\n  }, {}); // cache valueGetter and compareFn so that they\n  // do not need to be looked-up in the sort function body\n\n  const cachedDirs = dirs.map(dir => {\n    const prop = dir.prop;\n    return {\n      prop,\n      dir: dir.dir,\n      valueGetter: getterForProp(prop),\n      compareFn: cols[prop] || orderByComparator\n    };\n  });\n  return temp.sort(function (rowA, rowB) {\n    for (const cachedDir of cachedDirs) {\n      // Get property and valuegetters for column to be sorted\n      const {\n        prop,\n        valueGetter\n      } = cachedDir; // Get A and B cell values from rows based on properties of the columns\n\n      const propA = valueGetter(rowA, prop);\n      const propB = valueGetter(rowB, prop); // Compare function gets five parameters:\n      // Two cell values to be compared as propA and propB\n      // Two rows corresponding to the cells as rowA and rowB\n      // Direction of the sort for this column as SortDirection\n      // Compare can be a standard JS comparison function (a,b) => -1|0|1\n      // as additional parameters are silently ignored. The whole row and sort\n      // direction enable more complex sort logic.\n\n      const comparison = cachedDir.dir !== SortDirection.desc ? cachedDir.compareFn(propA, propB, rowA, rowB, cachedDir.dir) : -cachedDir.compareFn(propA, propB, rowA, rowB, cachedDir.dir); // Don't return 0 yet in case of needing to sort by next property\n\n      if (comparison !== 0) return comparison;\n    }\n\n    if (!(rowToIndexMap.has(rowA) && rowToIndexMap.has(rowB))) return 0;\n    /**\n     * all else being equal, preserve original order of the rows (stable sort)\n     */\n\n    return rowToIndexMap.get(rowA) < rowToIndexMap.get(rowB) ? -1 : 1;\n  });\n}\n\nlet DatatableComponent = /*#__PURE__*/(() => {\n  class DatatableComponent {\n    constructor(scrollbarHelper, dimensionsHelper, cd, element, differs, columnChangesService, configuration) {\n      this.scrollbarHelper = scrollbarHelper;\n      this.dimensionsHelper = dimensionsHelper;\n      this.cd = cd;\n      this.columnChangesService = columnChangesService;\n      this.configuration = configuration;\n      /**\n       * List of row objects that should be\n       * represented as selected in the grid.\n       * Default value: `[]`\n       */\n\n      this.selected = [];\n      /**\n       * Enable vertical scrollbars\n       */\n\n      this.scrollbarV = false;\n      /**\n       * Enable horz scrollbars\n       */\n\n      this.scrollbarH = false;\n      /**\n       * The row height; which is necessary\n       * to calculate the height for the lazy rendering.\n       */\n\n      this.rowHeight = 30;\n      /**\n       * Type of column width distribution formula.\n       * Example: flex, force, standard\n       */\n\n      this.columnMode = ColumnMode.standard;\n      /**\n       * The minimum header height in pixels.\n       * Pass a falsey for no header\n       */\n\n      this.headerHeight = 30;\n      /**\n       * The minimum footer height in pixels.\n       * Pass falsey for no footer\n       */\n\n      this.footerHeight = 0;\n      /**\n       * If the table should use external paging\n       * otherwise its assumed that all data is preloaded.\n       */\n\n      this.externalPaging = false;\n      /**\n       * If the table should use external sorting or\n       * the built-in basic sorting.\n       */\n\n      this.externalSorting = false;\n      /**\n       * Show the linear loading bar.\n       * Default value: `false`\n       */\n\n      this.loadingIndicator = false;\n      /**\n       * Enable/Disable ability to re-order columns\n       * by dragging them.\n       */\n\n      this.reorderable = true;\n      /**\n       * Swap columns on re-order columns or\n       * move them.\n       */\n\n      this.swapColumns = true;\n      /**\n       * The type of sorting\n       */\n\n      this.sortType = SortType.single;\n      /**\n       * Array of sorted columns by property and type.\n       * Default value: `[]`\n       */\n\n      this.sorts = [];\n      /**\n       * Css class overrides\n       */\n\n      this.cssClasses = {\n        sortAscending: 'datatable-icon-up',\n        sortDescending: 'datatable-icon-down',\n        sortUnset: 'datatable-icon-sort-unset',\n        pagerLeftArrow: 'datatable-icon-left',\n        pagerRightArrow: 'datatable-icon-right',\n        pagerPrevious: 'datatable-icon-prev',\n        pagerNext: 'datatable-icon-skip'\n      };\n      /**\n       * Message overrides for localization\n       *\n       * emptyMessage     [default] = 'No data to display'\n       * totalMessage     [default] = 'total'\n       * selectedMessage  [default] = 'selected'\n       */\n\n      this.messages = {\n        // Message to show when array is presented\n        // but contains no values\n        emptyMessage: 'No data to display',\n        // Footer total message\n        totalMessage: 'total',\n        // Footer selected message\n        selectedMessage: 'selected'\n      };\n      /**\n       * A boolean you can use to set the detault behaviour of rows and groups\n       * whether they will start expanded or not. If ommited the default is NOT expanded.\n       *\n       */\n\n      this.groupExpansionDefault = false;\n      /**\n       * Property to which you can use for determining select all\n       * rows on current page or not.\n       *\n       * @memberOf DatatableComponent\n       */\n\n      this.selectAllRowsOnPage = false;\n      /**\n       * A flag for row virtualization on / off\n       */\n\n      this.virtualization = true;\n      /**\n       * A flag for switching summary row on / off\n       */\n\n      this.summaryRow = false;\n      /**\n       * A height of summary row\n       */\n\n      this.summaryHeight = 30;\n      /**\n       * A property holds a summary row position: top/bottom\n       */\n\n      this.summaryPosition = 'top';\n      /**\n       * Body was scrolled typically in a `scrollbarV:true` scenario.\n       */\n\n      this.scroll = new EventEmitter();\n      /**\n       * A cell or row was focused via keyboard or mouse click.\n       */\n\n      this.activate = new EventEmitter();\n      /**\n       * A cell or row was selected.\n       */\n\n      this.select = new EventEmitter();\n      /**\n       * Column sort was invoked.\n       */\n\n      this.sort = new EventEmitter();\n      /**\n       * The table was paged either triggered by the pager or the body scroll.\n       */\n\n      this.page = new EventEmitter();\n      /**\n       * Columns were re-ordered.\n       */\n\n      this.reorder = new EventEmitter();\n      /**\n       * Column was resized.\n       */\n\n      this.resize = new EventEmitter();\n      /**\n       * The context menu was invoked on the table.\n       * type indicates whether the header or the body was clicked.\n       * content contains either the column or the row that was clicked.\n       */\n\n      this.tableContextmenu = new EventEmitter(false);\n      /**\n       * A row was expanded ot collapsed for tree\n       */\n\n      this.treeAction = new EventEmitter();\n      this.rowCount = 0;\n      this._offsetX = new BehaviorSubject(0);\n      this._count = 0;\n      this._offset = 0;\n      this._subscriptions = [];\n      /**\n       * This will be used when displaying or selecting rows.\n       * when tracking/comparing them, we'll use the value of this fn,\n       *\n       * (`fn(x) === fn(y)` instead of `x === y`)\n       */\n\n      this.rowIdentity = x => {\n        if (this._groupRowsBy) {\n          // each group in groupedRows are stored as {key, value: [rows]},\n          // where key is the groupRowsBy index\n          return x.key;\n        } else {\n          return x;\n        }\n      }; // get ref to elm for measuring\n\n\n      this.element = element.nativeElement;\n      this.rowDiffer = differs.find({}).create(); // apply global settings from Module.forRoot\n\n      if (this.configuration && this.configuration.messages) {\n        this.messages = Object.assign({}, this.configuration.messages);\n      }\n    }\n    /**\n     * Rows that are displayed in the table.\n     */\n\n\n    set rows(val) {\n      this._rows = val;\n\n      if (val) {\n        this._internalRows = [...val];\n      } // auto sort on new updates\n\n\n      if (!this.externalSorting) {\n        this.sortInternalRows();\n      } // auto group by parent on new update\n\n\n      this._internalRows = groupRowsByParents(this._internalRows, optionalGetterForProp(this.treeFromRelation), optionalGetterForProp(this.treeToRelation)); // recalculate sizes/etc\n\n      this.recalculate();\n\n      if (this._rows && this._groupRowsBy) {\n        // If a column has been specified in _groupRowsBy created a new array with the data grouped by that row\n        this.groupedRows = this.groupArrayBy(this._rows, this._groupRowsBy);\n      }\n\n      this.cd.markForCheck();\n    }\n    /**\n     * Gets the rows.\n     */\n\n\n    get rows() {\n      return this._rows;\n    }\n    /**\n     * This attribute allows the user to set the name of the column to group the data with\n     */\n\n\n    set groupRowsBy(val) {\n      if (val) {\n        this._groupRowsBy = val;\n\n        if (this._rows && this._groupRowsBy) {\n          // cretes a new array with the data grouped\n          this.groupedRows = this.groupArrayBy(this._rows, this._groupRowsBy);\n        }\n      }\n    }\n\n    get groupRowsBy() {\n      return this._groupRowsBy;\n    }\n    /**\n     * Columns to be displayed.\n     */\n\n\n    set columns(val) {\n      if (val) {\n        this._internalColumns = [...val];\n        setColumnDefaults(this._internalColumns);\n        this.recalculateColumns();\n      }\n\n      this._columns = val;\n    }\n    /**\n     * Get the columns.\n     */\n\n\n    get columns() {\n      return this._columns;\n    }\n    /**\n     * The page size to be shown.\n     * Default value: `undefined`\n     */\n\n\n    set limit(val) {\n      this._limit = val; // recalculate sizes/etc\n\n      this.recalculate();\n    }\n    /**\n     * Gets the limit.\n     */\n\n\n    get limit() {\n      return this._limit;\n    }\n    /**\n     * The total count of all rows.\n     * Default value: `0`\n     */\n\n\n    set count(val) {\n      this._count = val; // recalculate sizes/etc\n\n      this.recalculate();\n    }\n    /**\n     * Gets the count.\n     */\n\n\n    get count() {\n      return this._count;\n    }\n    /**\n     * The current offset ( page - 1 ) shown.\n     * Default value: `0`\n     */\n\n\n    set offset(val) {\n      this._offset = val;\n    }\n\n    get offset() {\n      return Math.max(Math.min(this._offset, Math.ceil(this.rowCount / this.pageSize) - 1), 0);\n    }\n    /**\n     * CSS class applied if the header height if fixed height.\n     */\n\n\n    get isFixedHeader() {\n      const headerHeight = this.headerHeight;\n      return typeof headerHeight === 'string' ? headerHeight !== 'auto' : true;\n    }\n    /**\n     * CSS class applied to the root element if\n     * the row heights are fixed heights.\n     */\n\n\n    get isFixedRow() {\n      return this.rowHeight !== 'auto';\n    }\n    /**\n     * CSS class applied to root element if\n     * vertical scrolling is enabled.\n     */\n\n\n    get isVertScroll() {\n      return this.scrollbarV;\n    }\n    /**\n     * CSS class applied to root element if\n     * virtualization is enabled.\n     */\n\n\n    get isVirtualized() {\n      return this.virtualization;\n    }\n    /**\n     * CSS class applied to the root element\n     * if the horziontal scrolling is enabled.\n     */\n\n\n    get isHorScroll() {\n      return this.scrollbarH;\n    }\n    /**\n     * CSS class applied to root element is selectable.\n     */\n\n\n    get isSelectable() {\n      return this.selectionType !== undefined;\n    }\n    /**\n     * CSS class applied to root is checkbox selection.\n     */\n\n\n    get isCheckboxSelection() {\n      return this.selectionType === SelectionType.checkbox;\n    }\n    /**\n     * CSS class applied to root if cell selection.\n     */\n\n\n    get isCellSelection() {\n      return this.selectionType === SelectionType.cell;\n    }\n    /**\n     * CSS class applied to root if single select.\n     */\n\n\n    get isSingleSelection() {\n      return this.selectionType === SelectionType.single;\n    }\n    /**\n     * CSS class added to root element if mulit select\n     */\n\n\n    get isMultiSelection() {\n      return this.selectionType === SelectionType.multi;\n    }\n    /**\n     * CSS class added to root element if mulit click select\n     */\n\n\n    get isMultiClickSelection() {\n      return this.selectionType === SelectionType.multiClick;\n    }\n    /**\n     * Column templates gathered from `ContentChildren`\n     * if described in your markup.\n     */\n\n\n    set columnTemplates(val) {\n      this._columnTemplates = val;\n      this.translateColumns(val);\n    }\n    /**\n     * Returns the column templates.\n     */\n\n\n    get columnTemplates() {\n      return this._columnTemplates;\n    }\n    /**\n     * Returns if all rows are selected.\n     */\n\n\n    get allRowsSelected() {\n      let allRowsSelected = this.rows && this.selected && this.selected.length === this.rows.length;\n\n      if (this.bodyComponent && this.selectAllRowsOnPage) {\n        const indexes = this.bodyComponent.indexes;\n        const rowsOnPage = indexes.last - indexes.first;\n        allRowsSelected = this.selected.length === rowsOnPage;\n      }\n\n      return this.selected && this.rows && this.rows.length !== 0 && allRowsSelected;\n    }\n    /**\n     * Lifecycle hook that is called after data-bound\n     * properties of a directive are initialized.\n     */\n\n\n    ngOnInit() {\n      // need to call this immediatly to size\n      // if the table is hidden the visibility\n      // listener will invoke this itself upon show\n      this.recalculate();\n    }\n    /**\n     * Lifecycle hook that is called after a component's\n     * view has been fully initialized.\n     */\n\n\n    ngAfterViewInit() {\n      if (!this.externalSorting) {\n        this.sortInternalRows();\n      } // this has to be done to prevent the change detection\n      // tree from freaking out because we are readjusting\n\n\n      if (typeof requestAnimationFrame === 'undefined') {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        this.recalculate(); // emit page for virtual server-side kickoff\n\n        if (this.externalPaging && this.scrollbarV) {\n          this.page.emit({\n            count: this.count,\n            pageSize: this.pageSize,\n            limit: this.limit,\n            offset: 0\n          });\n        }\n      });\n    }\n    /**\n     * Lifecycle hook that is called after a component's\n     * content has been fully initialized.\n     */\n\n\n    ngAfterContentInit() {\n      this.columnTemplates.changes.subscribe(v => this.translateColumns(v));\n      this.listenForColumnInputChanges();\n    }\n    /**\n     * Translates the templates to the column objects\n     */\n\n\n    translateColumns(val) {\n      if (val) {\n        const arr = val.toArray();\n\n        if (arr.length) {\n          this._internalColumns = translateTemplates(arr);\n          setColumnDefaults(this._internalColumns);\n          this.recalculateColumns();\n          this.sortInternalRows();\n          this.cd.markForCheck();\n        }\n      }\n    }\n    /**\n     * Creates a map with the data grouped by the user choice of grouping index\n     *\n     * @param originalArray the original array passed via parameter\n     * @param groupByIndex  the index of the column to group the data by\n     */\n\n\n    groupArrayBy(originalArray, groupBy) {\n      // create a map to hold groups with their corresponding results\n      const map = new Map();\n      let i = 0;\n      originalArray.forEach(item => {\n        const key = item[groupBy];\n\n        if (!map.has(key)) {\n          map.set(key, [item]);\n        } else {\n          map.get(key).push(item);\n        }\n\n        i++;\n      });\n\n      const addGroup = (key, value) => {\n        return {\n          key,\n          value\n        };\n      }; // convert map back to a simple array of objects\n\n\n      return Array.from(map, x => addGroup(x[0], x[1]));\n    }\n    /*\n     * Lifecycle hook that is called when Angular dirty checks a directive.\n     */\n\n\n    ngDoCheck() {\n      if (this.rowDiffer.diff(this.rows)) {\n        if (!this.externalSorting) {\n          this.sortInternalRows();\n        } else {\n          this._internalRows = [...this.rows];\n        } // auto group by parent on new update\n\n\n        this._internalRows = groupRowsByParents(this._internalRows, optionalGetterForProp(this.treeFromRelation), optionalGetterForProp(this.treeToRelation));\n        this.recalculatePages();\n        this.cd.markForCheck();\n      }\n    }\n    /**\n     * Recalc's the sizes of the grid.\n     *\n     * Updated automatically on changes to:\n     *\n     *  - Columns\n     *  - Rows\n     *  - Paging related\n     *\n     * Also can be manually invoked or upon window resize.\n     */\n\n\n    recalculate() {\n      this.recalculateDims();\n      this.recalculateColumns();\n      this.cd.markForCheck();\n    }\n    /**\n     * Window resize handler to update sizes.\n     */\n\n\n    onWindowResize() {\n      this.recalculate();\n    }\n    /**\n     * Recalulcates the column widths based on column width\n     * distribution mode and scrollbar offsets.\n     */\n\n\n    recalculateColumns(columns = this._internalColumns, forceIdx = -1, allowBleed = this.scrollbarH) {\n      if (!columns) return undefined;\n      let width = this._innerWidth;\n\n      if (this.scrollbarV) {\n        width = width - this.scrollbarHelper.width;\n      }\n\n      if (this.columnMode === ColumnMode.force) {\n        forceFillColumnWidths(columns, width, forceIdx, allowBleed);\n      } else if (this.columnMode === ColumnMode.flex) {\n        adjustColumnWidths(columns, width);\n      }\n\n      return columns;\n    }\n    /**\n     * Recalculates the dimensions of the table size.\n     * Internally calls the page size and row count calcs too.\n     *\n     */\n\n\n    recalculateDims() {\n      const dims = this.dimensionsHelper.getDimensions(this.element);\n      this._innerWidth = Math.floor(dims.width);\n\n      if (this.scrollbarV) {\n        let height = dims.height;\n        if (this.headerHeight) height = height - this.headerHeight;\n        if (this.footerHeight) height = height - this.footerHeight;\n        this.bodyHeight = height;\n      }\n\n      this.recalculatePages();\n    }\n    /**\n     * Recalculates the pages after a update.\n     */\n\n\n    recalculatePages() {\n      this.pageSize = this.calcPageSize();\n      this.rowCount = this.calcRowCount();\n    }\n    /**\n     * Body triggered a page event.\n     */\n\n\n    onBodyPage({\n      offset\n    }) {\n      // Avoid pagination caming from body events like scroll when the table\n      // has no virtualization and the external paging is enable.\n      // This means, let's the developer handle pagination by my him(her) self\n      if (this.externalPaging && !this.virtualization) {\n        return;\n      }\n\n      this.offset = offset;\n      this.page.emit({\n        count: this.count,\n        pageSize: this.pageSize,\n        limit: this.limit,\n        offset: this.offset\n      });\n    }\n    /**\n     * The body triggered a scroll event.\n     */\n\n\n    onBodyScroll(event) {\n      this._offsetX.next(event.offsetX);\n\n      this.scroll.emit(event);\n      this.cd.detectChanges();\n    }\n    /**\n     * The footer triggered a page event.\n     */\n\n\n    onFooterPage(event) {\n      this.offset = event.page - 1;\n      this.bodyComponent.updateOffsetY(this.offset);\n      this.page.emit({\n        count: this.count,\n        pageSize: this.pageSize,\n        limit: this.limit,\n        offset: this.offset\n      });\n\n      if (this.selectAllRowsOnPage) {\n        this.selected = [];\n        this.select.emit({\n          selected: this.selected\n        });\n      }\n    }\n    /**\n     * Recalculates the sizes of the page\n     */\n\n\n    calcPageSize(val = this.rows) {\n      // Keep the page size constant even if the row has been expanded.\n      // This is because an expanded row is still considered to be a child of\n      // the original row.  Hence calculation would use rowHeight only.\n      if (this.scrollbarV && this.virtualization) {\n        const size = Math.ceil(this.bodyHeight / this.rowHeight);\n        return Math.max(size, 0);\n      } // if limit is passed, we are paging\n\n\n      if (this.limit !== undefined) {\n        return this.limit;\n      } // otherwise use row length\n\n\n      if (val) {\n        return val.length;\n      } // other empty :(\n\n\n      return 0;\n    }\n    /**\n     * Calculates the row count.\n     */\n\n\n    calcRowCount(val = this.rows) {\n      if (!this.externalPaging) {\n        if (!val) return 0;\n\n        if (this.groupedRows) {\n          return this.groupedRows.length;\n        } else if (this.treeFromRelation != null && this.treeToRelation != null) {\n          return this._internalRows.length;\n        } else {\n          return val.length;\n        }\n      }\n\n      return this.count;\n    }\n    /**\n     * The header triggered a contextmenu event.\n     */\n\n\n    onColumnContextmenu({\n      event,\n      column\n    }) {\n      this.tableContextmenu.emit({\n        event,\n        type: ContextmenuType.header,\n        content: column\n      });\n    }\n    /**\n     * The body triggered a contextmenu event.\n     */\n\n\n    onRowContextmenu({\n      event,\n      row\n    }) {\n      this.tableContextmenu.emit({\n        event,\n        type: ContextmenuType.body,\n        content: row\n      });\n    }\n    /**\n     * The header triggered a column resize event.\n     */\n\n\n    onColumnResize({\n      column,\n      newValue\n    }) {\n      /* Safari/iOS 10.2 workaround */\n      if (column === undefined) {\n        return;\n      }\n\n      let idx;\n\n      const cols = this._internalColumns.map((c, i) => {\n        c = Object.assign({}, c);\n\n        if (c.$$id === column.$$id) {\n          idx = i;\n          c.width = newValue; // set this so we can force the column\n          // width distribution to be to this value\n\n          c.$$oldWidth = newValue;\n        }\n\n        return c;\n      });\n\n      this.recalculateColumns(cols, idx);\n      this._internalColumns = cols;\n      this.resize.emit({\n        column,\n        newValue\n      });\n    }\n    /**\n     * The header triggered a column re-order event.\n     */\n\n\n    onColumnReorder({\n      column,\n      newValue,\n      prevValue\n    }) {\n      const cols = this._internalColumns.map(c => {\n        return Object.assign({}, c);\n      });\n\n      if (this.swapColumns) {\n        const prevCol = cols[newValue];\n        cols[newValue] = column;\n        cols[prevValue] = prevCol;\n      } else {\n        if (newValue > prevValue) {\n          const movedCol = cols[prevValue];\n\n          for (let i = prevValue; i < newValue; i++) {\n            cols[i] = cols[i + 1];\n          }\n\n          cols[newValue] = movedCol;\n        } else {\n          const movedCol = cols[prevValue];\n\n          for (let i = prevValue; i > newValue; i--) {\n            cols[i] = cols[i - 1];\n          }\n\n          cols[newValue] = movedCol;\n        }\n      }\n\n      this._internalColumns = cols;\n      this.reorder.emit({\n        column,\n        newValue,\n        prevValue\n      });\n    }\n    /**\n     * The header triggered a column sort event.\n     */\n\n\n    onColumnSort(event) {\n      // clean selected rows\n      if (this.selectAllRowsOnPage) {\n        this.selected = [];\n        this.select.emit({\n          selected: this.selected\n        });\n      }\n\n      this.sorts = event.sorts; // this could be optimized better since it will resort\n      // the rows again on the 'push' detection...\n\n      if (this.externalSorting === false) {\n        // don't use normal setter so we don't resort\n        this.sortInternalRows();\n      } // auto group by parent on new update\n\n\n      this._internalRows = groupRowsByParents(this._internalRows, optionalGetterForProp(this.treeFromRelation), optionalGetterForProp(this.treeToRelation)); // Always go to first page when sorting to see the newly sorted data\n\n      this.offset = 0;\n      this.bodyComponent.updateOffsetY(this.offset);\n      this.sort.emit(event);\n    }\n    /**\n     * Toggle all row selection\n     */\n\n\n    onHeaderSelect(event) {\n      if (this.bodyComponent && this.selectAllRowsOnPage) {\n        // before we splice, chk if we currently have all selected\n        const first = this.bodyComponent.indexes.first;\n        const last = this.bodyComponent.indexes.last;\n        const allSelected = this.selected.length === last - first; // remove all existing either way\n\n        this.selected = []; // do the opposite here\n\n        if (!allSelected) {\n          this.selected.push(...this._internalRows.slice(first, last));\n        }\n      } else {\n        // before we splice, chk if we currently have all selected\n        const allSelected = this.selected.length === this.rows.length; // remove all existing either way\n\n        this.selected = []; // do the opposite here\n\n        if (!allSelected) {\n          this.selected.push(...this.rows);\n        }\n      }\n\n      this.select.emit({\n        selected: this.selected\n      });\n    }\n    /**\n     * A row was selected from body\n     */\n\n\n    onBodySelect(event) {\n      this.select.emit(event);\n    }\n    /**\n     * A row was expanded or collapsed for tree\n     */\n\n\n    onTreeAction(event) {\n      const row = event.row; // TODO: For duplicated items this will not work\n\n      const rowIndex = this._rows.findIndex(r => r[this.treeToRelation] === event.row[this.treeToRelation]);\n\n      this.treeAction.emit({\n        row,\n        rowIndex\n      });\n    }\n\n    ngOnDestroy() {\n      this._subscriptions.forEach(subscription => subscription.unsubscribe());\n    }\n    /**\n     * listen for changes to input bindings of all DataTableColumnDirective and\n     * trigger the columnTemplates.changes observable to emit\n     */\n\n\n    listenForColumnInputChanges() {\n      this._subscriptions.push(this.columnChangesService.columnInputChanges$.subscribe(() => {\n        if (this.columnTemplates) {\n          this.columnTemplates.notifyOnChanges();\n        }\n      }));\n    }\n\n    sortInternalRows() {\n      this._internalRows = sortRows(this._internalRows, this._internalColumns, this.sorts);\n    }\n\n  }\n\n  DatatableComponent.ɵfac = function DatatableComponent_Factory(t) {\n    return new (t || DatatableComponent)(ɵngcc0.ɵɵdirectiveInject(ScrollbarHelper, 4), ɵngcc0.ɵɵdirectiveInject(DimensionsHelper, 4), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.KeyValueDiffers), ɵngcc0.ɵɵdirectiveInject(ColumnChangesService), ɵngcc0.ɵɵdirectiveInject('configuration', 8));\n  };\n\n  DatatableComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: DatatableComponent,\n    selectors: [[\"ngx-datatable\"]],\n    contentQueries: function DatatableComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵcontentQuery(dirIndex, DatatableRowDetailDirective, 5);\n        ɵngcc0.ɵɵcontentQuery(dirIndex, DatatableGroupHeaderDirective, 5);\n        ɵngcc0.ɵɵcontentQuery(dirIndex, DatatableFooterDirective, 5);\n        ɵngcc0.ɵɵcontentQuery(dirIndex, DataTableColumnDirective, 4);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.rowDetail = _t.first);\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.groupHeader = _t.first);\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.footer = _t.first);\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.columnTemplates = _t);\n      }\n    },\n    viewQuery: function DatatableComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵviewQuery(DataTableBodyComponent, 5);\n        ɵngcc0.ɵɵviewQuery(DataTableHeaderComponent, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.bodyComponent = _t.first);\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.headerComponent = _t.first);\n      }\n    },\n    hostAttrs: [1, \"ngx-datatable\"],\n    hostVars: 22,\n    hostBindings: function DatatableComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"resize\", function DatatableComponent_resize_HostBindingHandler() {\n          return ctx.onWindowResize();\n        }, false, ɵngcc0.ɵɵresolveWindow);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"fixed-header\", ctx.isFixedHeader)(\"fixed-row\", ctx.isFixedRow)(\"scroll-vertical\", ctx.isVertScroll)(\"virtualized\", ctx.isVirtualized)(\"scroll-horz\", ctx.isHorScroll)(\"selectable\", ctx.isSelectable)(\"checkbox-selection\", ctx.isCheckboxSelection)(\"cell-selection\", ctx.isCellSelection)(\"single-selection\", ctx.isSingleSelection)(\"multi-selection\", ctx.isMultiSelection)(\"multi-click-selection\", ctx.isMultiClickSelection);\n      }\n    },\n    inputs: {\n      selected: \"selected\",\n      scrollbarV: \"scrollbarV\",\n      scrollbarH: \"scrollbarH\",\n      rowHeight: \"rowHeight\",\n      columnMode: \"columnMode\",\n      headerHeight: \"headerHeight\",\n      footerHeight: \"footerHeight\",\n      externalPaging: \"externalPaging\",\n      externalSorting: \"externalSorting\",\n      loadingIndicator: \"loadingIndicator\",\n      reorderable: \"reorderable\",\n      swapColumns: \"swapColumns\",\n      sortType: \"sortType\",\n      sorts: \"sorts\",\n      cssClasses: \"cssClasses\",\n      messages: \"messages\",\n      groupExpansionDefault: \"groupExpansionDefault\",\n      selectAllRowsOnPage: \"selectAllRowsOnPage\",\n      virtualization: \"virtualization\",\n      summaryRow: \"summaryRow\",\n      summaryHeight: \"summaryHeight\",\n      summaryPosition: \"summaryPosition\",\n      rowIdentity: \"rowIdentity\",\n      rows: \"rows\",\n      groupedRows: \"groupedRows\",\n      groupRowsBy: \"groupRowsBy\",\n      columns: \"columns\",\n      limit: \"limit\",\n      count: \"count\",\n      offset: \"offset\",\n      targetMarkerTemplate: \"targetMarkerTemplate\",\n      selectionType: \"selectionType\",\n      rowClass: \"rowClass\",\n      selectCheck: \"selectCheck\",\n      displayCheck: \"displayCheck\",\n      trackByProp: \"trackByProp\",\n      treeFromRelation: \"treeFromRelation\",\n      treeToRelation: \"treeToRelation\"\n    },\n    outputs: {\n      scroll: \"scroll\",\n      activate: \"activate\",\n      select: \"select\",\n      sort: \"sort\",\n      page: \"page\",\n      reorder: \"reorder\",\n      resize: \"resize\",\n      tableContextmenu: \"tableContextmenu\",\n      treeAction: \"treeAction\"\n    },\n    decls: 5,\n    vars: 34,\n    consts: [[\"role\", \"table\", \"visibilityObserver\", \"\", 3, \"visible\"], [\"role\", \"rowgroup\", 3, \"sorts\", \"sortType\", \"scrollbarH\", \"innerWidth\", \"offsetX\", \"dealsWithGroup\", \"columns\", \"headerHeight\", \"reorderable\", \"targetMarkerTemplate\", \"sortAscendingIcon\", \"sortDescendingIcon\", \"sortUnsetIcon\", \"allRowsSelected\", \"selectionType\", \"sort\", \"resize\", \"reorder\", \"select\", \"columnContextmenu\", 4, \"ngIf\"], [\"role\", \"rowgroup\", 3, \"groupRowsBy\", \"groupedRows\", \"rows\", \"groupExpansionDefault\", \"scrollbarV\", \"scrollbarH\", \"virtualization\", \"loadingIndicator\", \"externalPaging\", \"rowHeight\", \"rowCount\", \"offset\", \"trackByProp\", \"columns\", \"pageSize\", \"offsetX\", \"rowDetail\", \"groupHeader\", \"selected\", \"innerWidth\", \"bodyHeight\", \"selectionType\", \"emptyMessage\", \"rowIdentity\", \"rowClass\", \"selectCheck\", \"displayCheck\", \"summaryRow\", \"summaryHeight\", \"summaryPosition\", \"page\", \"activate\", \"rowContextmenu\", \"select\", \"scroll\", \"treeAction\"], [3, \"rowCount\", \"pageSize\", \"offset\", \"footerHeight\", \"footerTemplate\", \"totalMessage\", \"pagerLeftArrowIcon\", \"pagerRightArrowIcon\", \"pagerPreviousIcon\", \"selectedCount\", \"selectedMessage\", \"pagerNextIcon\", \"page\", 4, \"ngIf\"], [\"role\", \"rowgroup\", 3, \"sorts\", \"sortType\", \"scrollbarH\", \"innerWidth\", \"offsetX\", \"dealsWithGroup\", \"columns\", \"headerHeight\", \"reorderable\", \"targetMarkerTemplate\", \"sortAscendingIcon\", \"sortDescendingIcon\", \"sortUnsetIcon\", \"allRowsSelected\", \"selectionType\", \"sort\", \"resize\", \"reorder\", \"select\", \"columnContextmenu\"], [3, \"rowCount\", \"pageSize\", \"offset\", \"footerHeight\", \"footerTemplate\", \"totalMessage\", \"pagerLeftArrowIcon\", \"pagerRightArrowIcon\", \"pagerPreviousIcon\", \"selectedCount\", \"selectedMessage\", \"pagerNextIcon\", \"page\"]],\n    template: function DatatableComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"div\", 0);\n        ɵngcc0.ɵɵlistener(\"visible\", function DatatableComponent_Template_div_visible_0_listener() {\n          return ctx.recalculate();\n        });\n        ɵngcc0.ɵɵtemplate(1, DatatableComponent_datatable_header_1_Template, 2, 17, \"datatable-header\", 1);\n        ɵngcc0.ɵɵelementStart(2, \"datatable-body\", 2);\n        ɵngcc0.ɵɵlistener(\"page\", function DatatableComponent_Template_datatable_body_page_2_listener($event) {\n          return ctx.onBodyPage($event);\n        })(\"activate\", function DatatableComponent_Template_datatable_body_activate_2_listener($event) {\n          return ctx.activate.emit($event);\n        })(\"rowContextmenu\", function DatatableComponent_Template_datatable_body_rowContextmenu_2_listener($event) {\n          return ctx.onRowContextmenu($event);\n        })(\"select\", function DatatableComponent_Template_datatable_body_select_2_listener($event) {\n          return ctx.onBodySelect($event);\n        })(\"scroll\", function DatatableComponent_Template_datatable_body_scroll_2_listener($event) {\n          return ctx.onBodyScroll($event);\n        })(\"treeAction\", function DatatableComponent_Template_datatable_body_treeAction_2_listener($event) {\n          return ctx.onTreeAction($event);\n        });\n        ɵngcc0.ɵɵpipe(3, \"async\");\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵtemplate(4, DatatableComponent_datatable_footer_4_Template, 1, 12, \"datatable-footer\", 3);\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.headerHeight);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"groupRowsBy\", ctx.groupRowsBy)(\"groupedRows\", ctx.groupedRows)(\"rows\", ctx._internalRows)(\"groupExpansionDefault\", ctx.groupExpansionDefault)(\"scrollbarV\", ctx.scrollbarV)(\"scrollbarH\", ctx.scrollbarH)(\"virtualization\", ctx.virtualization)(\"loadingIndicator\", ctx.loadingIndicator)(\"externalPaging\", ctx.externalPaging)(\"rowHeight\", ctx.rowHeight)(\"rowCount\", ctx.rowCount)(\"offset\", ctx.offset)(\"trackByProp\", ctx.trackByProp)(\"columns\", ctx._internalColumns)(\"pageSize\", ctx.pageSize)(\"offsetX\", ɵngcc0.ɵɵpipeBind1(3, 32, ctx._offsetX))(\"rowDetail\", ctx.rowDetail)(\"groupHeader\", ctx.groupHeader)(\"selected\", ctx.selected)(\"innerWidth\", ctx._innerWidth)(\"bodyHeight\", ctx.bodyHeight)(\"selectionType\", ctx.selectionType)(\"emptyMessage\", ctx.messages.emptyMessage)(\"rowIdentity\", ctx.rowIdentity)(\"rowClass\", ctx.rowClass)(\"selectCheck\", ctx.selectCheck)(\"displayCheck\", ctx.displayCheck)(\"summaryRow\", ctx.summaryRow)(\"summaryHeight\", ctx.summaryHeight)(\"summaryPosition\", ctx.summaryPosition);\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.footerHeight);\n      }\n    },\n    directives: function () {\n      return [VisibilityDirective, ɵngcc1.NgIf, DataTableHeaderComponent, DataTableBodyComponent, DataTableFooterComponent];\n    },\n    pipes: function () {\n      return [ɵngcc1.AsyncPipe];\n    },\n    styles: [\".ngx-datatable{display:block;overflow:hidden;justify-content:center;position:relative;transform:translateZ(0)}.ngx-datatable [hidden]{display:none!important}.ngx-datatable *,.ngx-datatable :after,.ngx-datatable :before{box-sizing:border-box}.ngx-datatable.scroll-vertical .datatable-body{overflow-y:auto}.ngx-datatable.scroll-vertical.virtualized .datatable-body .datatable-row-wrapper{position:absolute}.ngx-datatable.scroll-horz .datatable-body{overflow-x:auto;-webkit-overflow-scrolling:touch}.ngx-datatable.fixed-header .datatable-header .datatable-header-inner{white-space:nowrap}.ngx-datatable.fixed-header .datatable-header .datatable-header-inner .datatable-header-cell{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ngx-datatable.fixed-row .datatable-scroll,.ngx-datatable.fixed-row .datatable-scroll .datatable-body-row{white-space:nowrap}.ngx-datatable.fixed-row .datatable-scroll .datatable-body-row .datatable-body-cell,.ngx-datatable.fixed-row .datatable-scroll .datatable-body-row .datatable-body-group-cell{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.ngx-datatable .datatable-body-row,.ngx-datatable .datatable-header-inner,.ngx-datatable .datatable-row-center{display:flex;flex-direction:row;-o-flex-flow:row;flex-flow:row}.ngx-datatable .datatable-body-cell,.ngx-datatable .datatable-header-cell{overflow-x:hidden;vertical-align:top;display:inline-block;line-height:1.625}.ngx-datatable .datatable-body-cell:focus,.ngx-datatable .datatable-header-cell:focus{outline:none}.ngx-datatable .datatable-row-left,.ngx-datatable .datatable-row-right{z-index:9}.ngx-datatable .datatable-row-center,.ngx-datatable .datatable-row-group,.ngx-datatable .datatable-row-left,.ngx-datatable .datatable-row-right{position:relative}.ngx-datatable .datatable-header{display:block;overflow:hidden}.ngx-datatable .datatable-header .datatable-header-inner{align-items:stretch;-webkit-align-items:stretch}.ngx-datatable .datatable-header .datatable-header-cell{position:relative;display:inline-block}.ngx-datatable .datatable-header .datatable-header-cell.sortable .datatable-header-cell-wrapper{cursor:pointer}.ngx-datatable .datatable-header .datatable-header-cell.longpress .datatable-header-cell-wrapper{cursor:move}.ngx-datatable .datatable-header .datatable-header-cell .sort-btn{line-height:100%;vertical-align:middle;display:inline-block;cursor:pointer}.ngx-datatable .datatable-header .datatable-header-cell .resize-handle,.ngx-datatable .datatable-header .datatable-header-cell .resize-handle--not-resizable{display:inline-block;position:absolute;right:0;top:0;bottom:0;width:5px;padding:0 4px;visibility:hidden}.ngx-datatable .datatable-header .datatable-header-cell .resize-handle{cursor:ew-resize}.ngx-datatable .datatable-header .datatable-header-cell.resizeable:hover .resize-handle,.ngx-datatable .datatable-header .datatable-header-cell:hover .resize-handle--not-resizable{visibility:visible}.ngx-datatable .datatable-header .datatable-header-cell .targetMarker{position:absolute;top:0;bottom:0}.ngx-datatable .datatable-header .datatable-header-cell .targetMarker.dragFromLeft{right:0}.ngx-datatable .datatable-header .datatable-header-cell .targetMarker.dragFromRight{left:0}.ngx-datatable .datatable-header .datatable-header-cell .datatable-header-cell-template-wrap{height:inherit}.ngx-datatable .datatable-body{position:relative;z-index:10;display:block}.ngx-datatable .datatable-body .datatable-scroll{display:inline-block}.ngx-datatable .datatable-body .datatable-row-detail{overflow-y:hidden}.ngx-datatable .datatable-body .datatable-row-wrapper{display:flex;flex-direction:column}.ngx-datatable .datatable-body .datatable-body-row{outline:none}.ngx-datatable .datatable-body .datatable-body-row>div{display:flex}.ngx-datatable .datatable-footer{display:block;width:100%;overflow:auto}.ngx-datatable .datatable-footer .datatable-footer-inner{display:flex;align-items:center;width:100%}.ngx-datatable .datatable-footer .selected-count .page-count{flex:1 1 40%}.ngx-datatable .datatable-footer .selected-count .datatable-pager{flex:1 1 60%}.ngx-datatable .datatable-footer .page-count{flex:1 1 20%}.ngx-datatable .datatable-footer .datatable-pager{flex:1 1 80%;text-align:right}.ngx-datatable .datatable-footer .datatable-pager .pager,.ngx-datatable .datatable-footer .datatable-pager .pager li{padding:0;margin:0;display:inline-block;list-style:none}.ngx-datatable .datatable-footer .datatable-pager .pager li,.ngx-datatable .datatable-footer .datatable-pager .pager li a{outline:none}.ngx-datatable .datatable-footer .datatable-pager .pager li a{cursor:pointer;display:inline-block}.ngx-datatable .datatable-footer .datatable-pager .pager li.disabled a{cursor:not-allowed}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n\n  __decorate([throttleable(5)], DatatableComponent.prototype, \"onWindowResize\", null);\n\n  return DatatableComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet DataTableHeaderCellComponent = /*#__PURE__*/(() => {\n  class DataTableHeaderCellComponent {\n    constructor(cd) {\n      this.cd = cd;\n      this.sort = new EventEmitter();\n      this.select = new EventEmitter();\n      this.columnContextmenu = new EventEmitter(false);\n      this.sortFn = this.onSort.bind(this);\n      this.selectFn = this.select.emit.bind(this.select);\n      this.cellContext = {\n        column: this.column,\n        sortDir: this.sortDir,\n        sortFn: this.sortFn,\n        allRowsSelected: this.allRowsSelected,\n        selectFn: this.selectFn\n      };\n    }\n\n    set allRowsSelected(value) {\n      this._allRowsSelected = value;\n      this.cellContext.allRowsSelected = value;\n    }\n\n    get allRowsSelected() {\n      return this._allRowsSelected;\n    }\n\n    set column(column) {\n      this._column = column;\n      this.cellContext.column = column;\n      this.cd.markForCheck();\n    }\n\n    get column() {\n      return this._column;\n    }\n\n    set sorts(val) {\n      this._sorts = val;\n      this.sortDir = this.calcSortDir(val);\n      this.cellContext.sortDir = this.sortDir;\n      this.sortClass = this.calcSortClass(this.sortDir);\n      this.cd.markForCheck();\n    }\n\n    get sorts() {\n      return this._sorts;\n    }\n\n    get columnCssClasses() {\n      let cls = 'datatable-header-cell';\n      if (this.column.sortable) cls += ' sortable';\n      if (this.column.resizeable) cls += ' resizeable';\n\n      if (this.column.headerClass) {\n        if (typeof this.column.headerClass === 'string') {\n          cls += ' ' + this.column.headerClass;\n        } else if (typeof this.column.headerClass === 'function') {\n          const res = this.column.headerClass({\n            column: this.column\n          });\n\n          if (typeof res === 'string') {\n            cls += res;\n          } else if (typeof res === 'object') {\n            const keys = Object.keys(res);\n\n            for (const k of keys) {\n              if (res[k] === true) cls += ` ${k}`;\n            }\n          }\n        }\n      }\n\n      const sortDir = this.sortDir;\n\n      if (sortDir) {\n        cls += ` sort-active sort-${sortDir}`;\n      }\n\n      return cls;\n    }\n\n    get name() {\n      // guaranteed to have a value by setColumnDefaults() in column-helper.ts\n      return this.column.headerTemplate === undefined ? this.column.name : undefined;\n    }\n\n    get minWidth() {\n      return this.column.minWidth;\n    }\n\n    get maxWidth() {\n      return this.column.maxWidth;\n    }\n\n    get width() {\n      return this.column.width;\n    }\n\n    get isCheckboxable() {\n      return this.column.checkboxable && this.column.headerCheckboxable && this.selectionType === SelectionType.checkbox;\n    }\n\n    onContextmenu($event) {\n      this.columnContextmenu.emit({\n        event: $event,\n        column: this.column\n      });\n    }\n\n    ngOnInit() {\n      this.sortClass = this.calcSortClass(this.sortDir);\n    }\n\n    calcSortDir(sorts) {\n      if (sorts && this.column) {\n        const sort = sorts.find(s => {\n          return s.prop === this.column.prop;\n        });\n        if (sort) return sort.dir;\n      }\n    }\n\n    onSort() {\n      if (!this.column.sortable) return;\n      const newValue = nextSortDir(this.sortType, this.sortDir);\n      this.sort.emit({\n        column: this.column,\n        prevValue: this.sortDir,\n        newValue\n      });\n    }\n\n    calcSortClass(sortDir) {\n      if (!this.cellContext.column.sortable) return;\n\n      if (sortDir === SortDirection.asc) {\n        return `sort-btn sort-asc ${this.sortAscendingIcon}`;\n      } else if (sortDir === SortDirection.desc) {\n        return `sort-btn sort-desc ${this.sortDescendingIcon}`;\n      } else {\n        return `sort-btn ${this.sortUnsetIcon}`;\n      }\n    }\n\n  }\n\n  DataTableHeaderCellComponent.ɵfac = function DataTableHeaderCellComponent_Factory(t) {\n    return new (t || DataTableHeaderCellComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef));\n  };\n\n  DataTableHeaderCellComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: DataTableHeaderCellComponent,\n    selectors: [[\"datatable-header-cell\"]],\n    hostAttrs: [1, \"datatable-header-cell\"],\n    hostVars: 11,\n    hostBindings: function DataTableHeaderCellComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"contextmenu\", function DataTableHeaderCellComponent_contextmenu_HostBindingHandler($event) {\n          return ctx.onContextmenu($event);\n        });\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵattribute(\"title\", ctx.name);\n        ɵngcc0.ɵɵclassMap(ctx.columnCssClasses);\n        ɵngcc0.ɵɵstyleProp(\"min-width\", ctx.minWidth, \"px\")(\"max-width\", ctx.maxWidth, \"px\")(\"width\", ctx.width, \"px\")(\"height\", ctx.headerHeight, \"px\");\n      }\n    },\n    inputs: {\n      allRowsSelected: \"allRowsSelected\",\n      column: \"column\",\n      sorts: \"sorts\",\n      sortType: \"sortType\",\n      sortAscendingIcon: \"sortAscendingIcon\",\n      sortDescendingIcon: \"sortDescendingIcon\",\n      sortUnsetIcon: \"sortUnsetIcon\",\n      isTarget: \"isTarget\",\n      targetMarkerTemplate: \"targetMarkerTemplate\",\n      targetMarkerContext: \"targetMarkerContext\",\n      selectionType: \"selectionType\",\n      headerHeight: \"headerHeight\"\n    },\n    outputs: {\n      sort: \"sort\",\n      select: \"select\",\n      columnContextmenu: \"columnContextmenu\"\n    },\n    decls: 6,\n    vars: 6,\n    consts: [[1, \"datatable-header-cell-template-wrap\"], [4, \"ngIf\"], [\"class\", \"datatable-checkbox\", 4, \"ngIf\"], [\"class\", \"datatable-header-cell-wrapper\", 4, \"ngIf\"], [3, \"click\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"datatable-checkbox\"], [\"type\", \"checkbox\", 3, \"checked\", \"change\"], [1, \"datatable-header-cell-wrapper\"], [1, \"datatable-header-cell-label\", \"draggable\", 3, \"innerHTML\", \"click\"]],\n    template: function DataTableHeaderCellComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"div\", 0);\n        ɵngcc0.ɵɵtemplate(1, DataTableHeaderCellComponent_1_Template, 1, 2, undefined, 1);\n        ɵngcc0.ɵɵtemplate(2, DataTableHeaderCellComponent_label_2_Template, 2, 1, \"label\", 2);\n        ɵngcc0.ɵɵtemplate(3, DataTableHeaderCellComponent_span_3_Template, 2, 1, \"span\", 3);\n        ɵngcc0.ɵɵtemplate(4, DataTableHeaderCellComponent_4_Template, 1, 2, undefined, 1);\n        ɵngcc0.ɵɵelementStart(5, \"span\", 4);\n        ɵngcc0.ɵɵlistener(\"click\", function DataTableHeaderCellComponent_Template_span_click_5_listener() {\n          return ctx.onSort();\n        });\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.isTarget);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.isCheckboxable);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", !ctx.column.headerTemplate);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.column.headerTemplate);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵclassMap(ctx.sortClass);\n      }\n    },\n    directives: [ɵngcc1.NgIf, ɵngcc1.NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return DataTableHeaderCellComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet DataTableFooterComponent = /*#__PURE__*/(() => {\n  class DataTableFooterComponent {\n    constructor() {\n      this.selectedCount = 0;\n      this.page = new EventEmitter();\n    }\n\n    get isVisible() {\n      return this.rowCount / this.pageSize > 1;\n    }\n\n    get curPage() {\n      return this.offset + 1;\n    }\n\n  }\n\n  DataTableFooterComponent.ɵfac = function DataTableFooterComponent_Factory(t) {\n    return new (t || DataTableFooterComponent)();\n  };\n\n  DataTableFooterComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: DataTableFooterComponent,\n    selectors: [[\"datatable-footer\"]],\n    hostAttrs: [1, \"datatable-footer\"],\n    inputs: {\n      selectedCount: \"selectedCount\",\n      footerHeight: \"footerHeight\",\n      rowCount: \"rowCount\",\n      pageSize: \"pageSize\",\n      offset: \"offset\",\n      pagerLeftArrowIcon: \"pagerLeftArrowIcon\",\n      pagerRightArrowIcon: \"pagerRightArrowIcon\",\n      pagerPreviousIcon: \"pagerPreviousIcon\",\n      pagerNextIcon: \"pagerNextIcon\",\n      totalMessage: \"totalMessage\",\n      footerTemplate: \"footerTemplate\",\n      selectedMessage: \"selectedMessage\"\n    },\n    outputs: {\n      page: \"page\"\n    },\n    decls: 4,\n    vars: 8,\n    consts: [[1, \"datatable-footer-inner\", 3, \"ngClass\"], [4, \"ngIf\"], [\"class\", \"page-count\", 4, \"ngIf\"], [3, \"pagerLeftArrowIcon\", \"pagerRightArrowIcon\", \"pagerPreviousIcon\", \"pagerNextIcon\", \"page\", \"size\", \"count\", \"hidden\", \"change\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"page-count\"], [3, \"pagerLeftArrowIcon\", \"pagerRightArrowIcon\", \"pagerPreviousIcon\", \"pagerNextIcon\", \"page\", \"size\", \"count\", \"hidden\", \"change\"]],\n    template: function DataTableFooterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"div\", 0);\n        ɵngcc0.ɵɵtemplate(1, DataTableFooterComponent_1_Template, 1, 8, undefined, 1);\n        ɵngcc0.ɵɵtemplate(2, DataTableFooterComponent_div_2_Template, 3, 3, \"div\", 2);\n        ɵngcc0.ɵɵtemplate(3, DataTableFooterComponent_datatable_pager_3_Template, 1, 8, \"datatable-pager\", 3);\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵstyleProp(\"height\", ctx.footerHeight, \"px\");\n        ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpureFunction1(6, _c2, ctx.selectedMessage));\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.footerTemplate);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", !ctx.footerTemplate);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", !ctx.footerTemplate);\n      }\n    },\n    directives: function () {\n      return [ɵngcc1.NgClass, ɵngcc1.NgIf, ɵngcc1.NgTemplateOutlet, DataTablePagerComponent];\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return DataTableFooterComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet DataTablePagerComponent = /*#__PURE__*/(() => {\n  class DataTablePagerComponent {\n    constructor() {\n      this.change = new EventEmitter();\n      this._count = 0;\n      this._page = 1;\n      this._size = 0;\n    }\n\n    set size(val) {\n      this._size = val;\n      this.pages = this.calcPages();\n    }\n\n    get size() {\n      return this._size;\n    }\n\n    set count(val) {\n      this._count = val;\n      this.pages = this.calcPages();\n    }\n\n    get count() {\n      return this._count;\n    }\n\n    set page(val) {\n      this._page = val;\n      this.pages = this.calcPages();\n    }\n\n    get page() {\n      return this._page;\n    }\n\n    get totalPages() {\n      const count = this.size < 1 ? 1 : Math.ceil(this.count / this.size);\n      return Math.max(count || 0, 1);\n    }\n\n    canPrevious() {\n      return this.page > 1;\n    }\n\n    canNext() {\n      return this.page < this.totalPages;\n    }\n\n    prevPage() {\n      this.selectPage(this.page - 1);\n    }\n\n    nextPage() {\n      this.selectPage(this.page + 1);\n    }\n\n    selectPage(page) {\n      if (page > 0 && page <= this.totalPages && page !== this.page) {\n        this.page = page;\n        this.change.emit({\n          page\n        });\n      }\n    }\n\n    calcPages(page) {\n      const pages = [];\n      let startPage = 1;\n      let endPage = this.totalPages;\n      const maxSize = 5;\n      const isMaxSized = maxSize < this.totalPages;\n      page = page || this.page;\n\n      if (isMaxSized) {\n        startPage = page - Math.floor(maxSize / 2);\n        endPage = page + Math.floor(maxSize / 2);\n\n        if (startPage < 1) {\n          startPage = 1;\n          endPage = Math.min(startPage + maxSize - 1, this.totalPages);\n        } else if (endPage > this.totalPages) {\n          startPage = Math.max(this.totalPages - maxSize + 1, 1);\n          endPage = this.totalPages;\n        }\n      }\n\n      for (let num = startPage; num <= endPage; num++) {\n        pages.push({\n          number: num,\n          text: num\n        });\n      }\n\n      return pages;\n    }\n\n  }\n\n  DataTablePagerComponent.ɵfac = function DataTablePagerComponent_Factory(t) {\n    return new (t || DataTablePagerComponent)();\n  };\n\n  DataTablePagerComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: DataTablePagerComponent,\n    selectors: [[\"datatable-pager\"]],\n    hostAttrs: [1, \"datatable-pager\"],\n    inputs: {\n      size: \"size\",\n      count: \"count\",\n      page: \"page\",\n      pagerLeftArrowIcon: \"pagerLeftArrowIcon\",\n      pagerRightArrowIcon: \"pagerRightArrowIcon\",\n      pagerPreviousIcon: \"pagerPreviousIcon\",\n      pagerNextIcon: \"pagerNextIcon\"\n    },\n    outputs: {\n      change: \"change\"\n    },\n    decls: 14,\n    vars: 21,\n    consts: [[1, \"pager\"], [\"role\", \"button\", \"aria-label\", \"go to first page\", \"href\", \"javascript:void(0)\", 3, \"click\"], [\"role\", \"button\", \"aria-label\", \"go to previous page\", \"href\", \"javascript:void(0)\", 3, \"click\"], [\"role\", \"button\", \"class\", \"pages\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"button\", \"aria-label\", \"go to next page\", \"href\", \"javascript:void(0)\", 3, \"click\"], [\"role\", \"button\", \"aria-label\", \"go to last page\", \"href\", \"javascript:void(0)\", 3, \"click\"], [\"role\", \"button\", 1, \"pages\"], [\"href\", \"javascript:void(0)\", 3, \"click\"]],\n    template: function DataTablePagerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"ul\", 0);\n        ɵngcc0.ɵɵelementStart(1, \"li\");\n        ɵngcc0.ɵɵelementStart(2, \"a\", 1);\n        ɵngcc0.ɵɵlistener(\"click\", function DataTablePagerComponent_Template_a_click_2_listener() {\n          return ctx.selectPage(1);\n        });\n        ɵngcc0.ɵɵelement(3, \"i\");\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementStart(4, \"li\");\n        ɵngcc0.ɵɵelementStart(5, \"a\", 2);\n        ɵngcc0.ɵɵlistener(\"click\", function DataTablePagerComponent_Template_a_click_5_listener() {\n          return ctx.prevPage();\n        });\n        ɵngcc0.ɵɵelement(6, \"i\");\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵtemplate(7, DataTablePagerComponent_li_7_Template, 3, 4, \"li\", 3);\n        ɵngcc0.ɵɵelementStart(8, \"li\");\n        ɵngcc0.ɵɵelementStart(9, \"a\", 4);\n        ɵngcc0.ɵɵlistener(\"click\", function DataTablePagerComponent_Template_a_click_9_listener() {\n          return ctx.nextPage();\n        });\n        ɵngcc0.ɵɵelement(10, \"i\");\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementStart(11, \"li\");\n        ɵngcc0.ɵɵelementStart(12, \"a\", 5);\n        ɵngcc0.ɵɵlistener(\"click\", function DataTablePagerComponent_Template_a_click_12_listener() {\n          return ctx.selectPage(ctx.totalPages);\n        });\n        ɵngcc0.ɵɵelement(13, \"i\");\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵclassProp(\"disabled\", !ctx.canPrevious());\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵclassMap(ctx.pagerPreviousIcon);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵclassProp(\"disabled\", !ctx.canPrevious());\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵclassMap(ctx.pagerLeftArrowIcon);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngForOf\", ctx.pages);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵclassProp(\"disabled\", !ctx.canNext());\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵclassMap(ctx.pagerRightArrowIcon);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵclassProp(\"disabled\", !ctx.canNext());\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵclassMap(ctx.pagerNextIcon);\n      }\n    },\n    directives: [ɵngcc1.NgForOf],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return DataTablePagerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet ProgressBarComponent = /*#__PURE__*/(() => {\n  class ProgressBarComponent {}\n\n  ProgressBarComponent.ɵfac = function ProgressBarComponent_Factory(t) {\n    return new (t || ProgressBarComponent)();\n  };\n\n  ProgressBarComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: ProgressBarComponent,\n    selectors: [[\"datatable-progress\"]],\n    decls: 3,\n    vars: 0,\n    consts: [[\"role\", \"progressbar\", 1, \"progress-linear\"], [1, \"container\"], [1, \"bar\"]],\n    template: function ProgressBarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"div\", 0);\n        ɵngcc0.ɵɵelementStart(1, \"div\", 1);\n        ɵngcc0.ɵɵelement(2, \"div\", 2);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return ProgressBarComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nvar Keys = /*#__PURE__*/(() => {\n  (function (Keys) {\n    Keys[Keys[\"up\"] = 38] = \"up\";\n    Keys[Keys[\"down\"] = 40] = \"down\";\n    Keys[Keys[\"return\"] = 13] = \"return\";\n    Keys[Keys[\"escape\"] = 27] = \"escape\";\n    Keys[Keys[\"left\"] = 37] = \"left\";\n    Keys[Keys[\"right\"] = 39] = \"right\";\n  })(Keys || (Keys = {}));\n\n  return Keys;\n})();\nlet DataTableBodyRowComponent = /*#__PURE__*/(() => {\n  class DataTableBodyRowComponent {\n    constructor(differs, scrollbarHelper, cd, element) {\n      this.differs = differs;\n      this.scrollbarHelper = scrollbarHelper;\n      this.cd = cd;\n      this.treeStatus = 'collapsed';\n      this.activate = new EventEmitter();\n      this.treeAction = new EventEmitter();\n      this._groupStyles = {\n        left: {},\n        center: {},\n        right: {}\n      };\n      this._element = element.nativeElement;\n      this._rowDiffer = differs.find({}).create();\n    }\n\n    set columns(val) {\n      this._columns = val;\n      this.recalculateColumns(val);\n      this.buildStylesByGroup();\n    }\n\n    get columns() {\n      return this._columns;\n    }\n\n    set innerWidth(val) {\n      if (this._columns) {\n        const colByPin = columnsByPin(this._columns);\n        this._columnGroupWidths = columnGroupWidths(colByPin, this._columns);\n      }\n\n      this._innerWidth = val;\n      this.recalculateColumns();\n      this.buildStylesByGroup();\n    }\n\n    get innerWidth() {\n      return this._innerWidth;\n    }\n\n    set offsetX(val) {\n      this._offsetX = val;\n      this.buildStylesByGroup();\n    }\n\n    get offsetX() {\n      return this._offsetX;\n    }\n\n    get cssClass() {\n      let cls = 'datatable-body-row';\n\n      if (this.isSelected) {\n        cls += ' active';\n      }\n\n      if (this.rowIndex % 2 !== 0) {\n        cls += ' datatable-row-odd';\n      }\n\n      if (this.rowIndex % 2 === 0) {\n        cls += ' datatable-row-even';\n      }\n\n      if (this.rowClass) {\n        const res = this.rowClass(this.row);\n\n        if (typeof res === 'string') {\n          cls += ` ${res}`;\n        } else if (typeof res === 'object') {\n          const keys = Object.keys(res);\n\n          for (const k of keys) {\n            if (res[k] === true) {\n              cls += ` ${k}`;\n            }\n          }\n        }\n      }\n\n      return cls;\n    }\n\n    get columnsTotalWidths() {\n      return this._columnGroupWidths.total;\n    }\n\n    ngDoCheck() {\n      if (this._rowDiffer.diff(this.row)) {\n        this.cd.markForCheck();\n      }\n    }\n\n    trackByGroups(index, colGroup) {\n      return colGroup.type;\n    }\n\n    columnTrackingFn(index, column) {\n      return column.$$id;\n    }\n\n    buildStylesByGroup() {\n      this._groupStyles.left = this.calcStylesByGroup('left');\n      this._groupStyles.center = this.calcStylesByGroup('center');\n      this._groupStyles.right = this.calcStylesByGroup('right');\n      this.cd.markForCheck();\n    }\n\n    calcStylesByGroup(group) {\n      const widths = this._columnGroupWidths;\n      const offsetX = this.offsetX;\n      const styles = {\n        width: `${widths[group]}px`\n      };\n\n      if (group === 'left') {\n        translateXY(styles, offsetX, 0);\n      } else if (group === 'right') {\n        const bodyWidth = parseInt(this.innerWidth + '', 0);\n        const totalDiff = widths.total - bodyWidth;\n        const offsetDiff = totalDiff - offsetX;\n        const offset = (offsetDiff + this.scrollbarHelper.width) * -1;\n        translateXY(styles, offset, 0);\n      }\n\n      return styles;\n    }\n\n    onActivate(event, index) {\n      event.cellIndex = index;\n      event.rowElement = this._element;\n      this.activate.emit(event);\n    }\n\n    onKeyDown(event) {\n      const keyCode = event.keyCode;\n      const isTargetRow = event.target === this._element;\n      const isAction = keyCode === Keys.return || keyCode === Keys.down || keyCode === Keys.up || keyCode === Keys.left || keyCode === Keys.right;\n\n      if (isAction && isTargetRow) {\n        event.preventDefault();\n        event.stopPropagation();\n        this.activate.emit({\n          type: 'keydown',\n          event,\n          row: this.row,\n          rowElement: this._element\n        });\n      }\n    }\n\n    onMouseenter(event) {\n      this.activate.emit({\n        type: 'mouseenter',\n        event,\n        row: this.row,\n        rowElement: this._element\n      });\n    }\n\n    recalculateColumns(val = this.columns) {\n      this._columns = val;\n      const colsByPin = columnsByPin(this._columns);\n      this._columnsByPin = columnsByPinArr(this._columns);\n      this._columnGroupWidths = columnGroupWidths(colsByPin, this._columns);\n    }\n\n    onTreeAction() {\n      this.treeAction.emit();\n    }\n\n  }\n\n  DataTableBodyRowComponent.ɵfac = function DataTableBodyRowComponent_Factory(t) {\n    return new (t || DataTableBodyRowComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.KeyValueDiffers), ɵngcc0.ɵɵdirectiveInject(ScrollbarHelper, 4), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef));\n  };\n\n  DataTableBodyRowComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: DataTableBodyRowComponent,\n    selectors: [[\"datatable-body-row\"]],\n    hostVars: 6,\n    hostBindings: function DataTableBodyRowComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"keydown\", function DataTableBodyRowComponent_keydown_HostBindingHandler($event) {\n          return ctx.onKeyDown($event);\n        })(\"mouseenter\", function DataTableBodyRowComponent_mouseenter_HostBindingHandler($event) {\n          return ctx.onMouseenter($event);\n        });\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassMap(ctx.cssClass);\n        ɵngcc0.ɵɵstyleProp(\"width\", ctx.columnsTotalWidths, \"px\")(\"height\", ctx.rowHeight, \"px\");\n      }\n    },\n    inputs: {\n      treeStatus: \"treeStatus\",\n      columns: \"columns\",\n      innerWidth: \"innerWidth\",\n      offsetX: \"offsetX\",\n      expanded: \"expanded\",\n      rowClass: \"rowClass\",\n      row: \"row\",\n      group: \"group\",\n      isSelected: \"isSelected\",\n      rowIndex: \"rowIndex\",\n      displayCheck: \"displayCheck\",\n      rowHeight: \"rowHeight\"\n    },\n    outputs: {\n      activate: \"activate\",\n      treeAction: \"treeAction\"\n    },\n    decls: 1,\n    vars: 2,\n    consts: [[3, \"class\", \"ngStyle\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"ngStyle\"], [\"role\", \"cell\", \"tabindex\", \"-1\", 3, \"row\", \"group\", \"expanded\", \"isSelected\", \"rowIndex\", \"column\", \"rowHeight\", \"displayCheck\", \"treeStatus\", \"activate\", \"treeAction\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"role\", \"cell\", \"tabindex\", \"-1\", 3, \"row\", \"group\", \"expanded\", \"isSelected\", \"rowIndex\", \"column\", \"rowHeight\", \"displayCheck\", \"treeStatus\", \"activate\", \"treeAction\"]],\n    template: function DataTableBodyRowComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, DataTableBodyRowComponent_div_0_Template, 2, 6, \"div\", 0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngForOf\", ctx._columnsByPin)(\"ngForTrackBy\", ctx.trackByGroups);\n      }\n    },\n    directives: function () {\n      return [ɵngcc1.NgForOf, ɵngcc1.NgStyle, DataTableBodyCellComponent];\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return DataTableBodyRowComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet DataTableRowWrapperComponent = /*#__PURE__*/(() => {\n  class DataTableRowWrapperComponent {\n    constructor(cd, differs) {\n      this.cd = cd;\n      this.differs = differs;\n      this.rowContextmenu = new EventEmitter(false);\n      this._expanded = false;\n      this.groupContext = {\n        group: this.row,\n        expanded: this.expanded,\n        rowIndex: this.rowIndex\n      };\n      this.rowContext = {\n        row: this.row,\n        expanded: this.expanded,\n        rowIndex: this.rowIndex\n      };\n      this.rowDiffer = differs.find({}).create();\n    }\n\n    set rowIndex(val) {\n      this._rowIndex = val;\n      this.rowContext.rowIndex = val;\n      this.groupContext.rowIndex = val;\n      this.cd.markForCheck();\n    }\n\n    get rowIndex() {\n      return this._rowIndex;\n    }\n\n    set expanded(val) {\n      this._expanded = val;\n      this.groupContext.expanded = val;\n      this.rowContext.expanded = val;\n      this.cd.markForCheck();\n    }\n\n    get expanded() {\n      return this._expanded;\n    }\n\n    ngDoCheck() {\n      if (this.rowDiffer.diff(this.row)) {\n        this.rowContext.row = this.row;\n        this.groupContext.group = this.row;\n        this.cd.markForCheck();\n      }\n    }\n\n    onContextmenu($event) {\n      this.rowContextmenu.emit({\n        event: $event,\n        row: this.row\n      });\n    }\n\n    getGroupHeaderStyle() {\n      const styles = {};\n      styles['transform'] = 'translate3d(' + this.offsetX + 'px, 0px, 0px)';\n      styles['backface-visibility'] = 'hidden';\n      styles['width'] = this.innerWidth;\n      return styles;\n    }\n\n  }\n\n  DataTableRowWrapperComponent.ɵfac = function DataTableRowWrapperComponent_Factory(t) {\n    return new (t || DataTableRowWrapperComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.KeyValueDiffers));\n  };\n\n  DataTableRowWrapperComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: DataTableRowWrapperComponent,\n    selectors: [[\"datatable-row-wrapper\"]],\n    hostAttrs: [1, \"datatable-row-wrapper\"],\n    hostBindings: function DataTableRowWrapperComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"contextmenu\", function DataTableRowWrapperComponent_contextmenu_HostBindingHandler($event) {\n          return ctx.onContextmenu($event);\n        });\n      }\n    },\n    inputs: {\n      rowIndex: \"rowIndex\",\n      expanded: \"expanded\",\n      innerWidth: \"innerWidth\",\n      rowDetail: \"rowDetail\",\n      groupHeader: \"groupHeader\",\n      offsetX: \"offsetX\",\n      detailRowHeight: \"detailRowHeight\",\n      row: \"row\",\n      groupedRows: \"groupedRows\"\n    },\n    outputs: {\n      rowContextmenu: \"rowContextmenu\"\n    },\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 3,\n    consts: [[\"class\", \"datatable-group-header\", 3, \"ngStyle\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"datatable-row-detail\", 3, \"height\", 4, \"ngIf\"], [1, \"datatable-group-header\", 3, \"ngStyle\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"datatable-row-detail\"]],\n    template: function DataTableRowWrapperComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵtemplate(0, DataTableRowWrapperComponent_div_0_Template, 2, 2, \"div\", 0);\n        ɵngcc0.ɵɵtemplate(1, DataTableRowWrapperComponent_ng_content_1_Template, 1, 0, \"ng-content\", 1);\n        ɵngcc0.ɵɵtemplate(2, DataTableRowWrapperComponent_div_2_Template, 2, 3, \"div\", 2);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.groupHeader && ctx.groupHeader.template);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.groupHeader && ctx.groupHeader.template && ctx.expanded || !ctx.groupHeader || !ctx.groupHeader.template);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.rowDetail && ctx.rowDetail.template && ctx.expanded);\n      }\n    },\n    directives: [ɵngcc1.NgIf, ɵngcc1.NgStyle, ɵngcc1.NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return DataTableRowWrapperComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet DataTableBodyCellComponent = /*#__PURE__*/(() => {\n  class DataTableBodyCellComponent {\n    constructor(element, cd) {\n      this.cd = cd;\n      this.activate = new EventEmitter();\n      this.treeAction = new EventEmitter();\n      this.isFocused = false;\n      this.onCheckboxChangeFn = this.onCheckboxChange.bind(this);\n      this.activateFn = this.activate.emit.bind(this.activate);\n      this.cellContext = {\n        onCheckboxChangeFn: this.onCheckboxChangeFn,\n        activateFn: this.activateFn,\n        row: this.row,\n        group: this.group,\n        value: this.value,\n        column: this.column,\n        rowHeight: this.rowHeight,\n        isSelected: this.isSelected,\n        rowIndex: this.rowIndex,\n        treeStatus: this.treeStatus,\n        onTreeAction: this.onTreeAction.bind(this)\n      };\n      this._element = element.nativeElement;\n    }\n\n    set group(group) {\n      this._group = group;\n      this.cellContext.group = group;\n      this.checkValueUpdates();\n      this.cd.markForCheck();\n    }\n\n    get group() {\n      return this._group;\n    }\n\n    set rowHeight(val) {\n      this._rowHeight = val;\n      this.cellContext.rowHeight = val;\n      this.checkValueUpdates();\n      this.cd.markForCheck();\n    }\n\n    get rowHeight() {\n      return this._rowHeight;\n    }\n\n    set isSelected(val) {\n      this._isSelected = val;\n      this.cellContext.isSelected = val;\n      this.cd.markForCheck();\n    }\n\n    get isSelected() {\n      return this._isSelected;\n    }\n\n    set expanded(val) {\n      this._expanded = val;\n      this.cellContext.expanded = val;\n      this.cd.markForCheck();\n    }\n\n    get expanded() {\n      return this._expanded;\n    }\n\n    set rowIndex(val) {\n      this._rowIndex = val;\n      this.cellContext.rowIndex = val;\n      this.checkValueUpdates();\n      this.cd.markForCheck();\n    }\n\n    get rowIndex() {\n      return this._rowIndex;\n    }\n\n    set column(column) {\n      this._column = column;\n      this.cellContext.column = column;\n      this.checkValueUpdates();\n      this.cd.markForCheck();\n    }\n\n    get column() {\n      return this._column;\n    }\n\n    set row(row) {\n      this._row = row;\n      this.cellContext.row = row;\n      this.checkValueUpdates();\n      this.cd.markForCheck();\n    }\n\n    get row() {\n      return this._row;\n    }\n\n    set sorts(val) {\n      this._sorts = val;\n      this.calcSortDir = this.calcSortDir(val);\n    }\n\n    get sorts() {\n      return this._sorts;\n    }\n\n    set treeStatus(status) {\n      if (status !== 'collapsed' && status !== 'expanded' && status !== 'loading' && status !== 'disabled') {\n        this._treeStatus = 'collapsed';\n      } else {\n        this._treeStatus = status;\n      }\n\n      this.cellContext.treeStatus = this._treeStatus;\n      this.checkValueUpdates();\n      this.cd.markForCheck();\n    }\n\n    get treeStatus() {\n      return this._treeStatus;\n    }\n\n    get columnCssClasses() {\n      let cls = 'datatable-body-cell';\n\n      if (this.column.cellClass) {\n        if (typeof this.column.cellClass === 'string') {\n          cls += ' ' + this.column.cellClass;\n        } else if (typeof this.column.cellClass === 'function') {\n          const res = this.column.cellClass({\n            row: this.row,\n            group: this.group,\n            column: this.column,\n            value: this.value,\n            rowHeight: this.rowHeight\n          });\n\n          if (typeof res === 'string') {\n            cls += ' ' + res;\n          } else if (typeof res === 'object') {\n            const keys = Object.keys(res);\n\n            for (const k of keys) {\n              if (res[k] === true) {\n                cls += ` ${k}`;\n              }\n            }\n          }\n        }\n      }\n\n      if (!this.sortDir) {\n        cls += ' sort-active';\n      }\n\n      if (this.isFocused) {\n        cls += ' active';\n      }\n\n      if (this.sortDir === SortDirection.asc) {\n        cls += ' sort-asc';\n      }\n\n      if (this.sortDir === SortDirection.desc) {\n        cls += ' sort-desc';\n      }\n\n      return cls;\n    }\n\n    get width() {\n      return this.column.width;\n    }\n\n    get minWidth() {\n      return this.column.minWidth;\n    }\n\n    get maxWidth() {\n      return this.column.maxWidth;\n    }\n\n    get height() {\n      const height = this.rowHeight;\n\n      if (isNaN(height)) {\n        return height;\n      }\n\n      return height + 'px';\n    }\n\n    ngDoCheck() {\n      this.checkValueUpdates();\n    }\n\n    ngOnDestroy() {\n      if (this.cellTemplate) {\n        this.cellTemplate.clear();\n      }\n    }\n\n    checkValueUpdates() {\n      let value = '';\n\n      if (!this.row || !this.column) {\n        value = '';\n      } else {\n        const val = this.column.$$valueGetter(this.row, this.column.prop);\n        const userPipe = this.column.pipe;\n\n        if (userPipe) {\n          value = userPipe.transform(val);\n        } else if (value !== undefined) {\n          value = val;\n        }\n      }\n\n      if (this.value !== value) {\n        this.value = value;\n        this.cellContext.value = value;\n        this.sanitizedValue = value !== null && value !== undefined ? this.stripHtml(value) : value;\n        this.cd.markForCheck();\n      }\n    }\n\n    onFocus() {\n      this.isFocused = true;\n    }\n\n    onBlur() {\n      this.isFocused = false;\n    }\n\n    onClick(event) {\n      this.activate.emit({\n        type: 'click',\n        event,\n        row: this.row,\n        group: this.group,\n        rowHeight: this.rowHeight,\n        column: this.column,\n        value: this.value,\n        cellElement: this._element\n      });\n    }\n\n    onDblClick(event) {\n      this.activate.emit({\n        type: 'dblclick',\n        event,\n        row: this.row,\n        group: this.group,\n        rowHeight: this.rowHeight,\n        column: this.column,\n        value: this.value,\n        cellElement: this._element\n      });\n    }\n\n    onKeyDown(event) {\n      const keyCode = event.keyCode;\n      const isTargetCell = event.target === this._element;\n      const isAction = keyCode === Keys.return || keyCode === Keys.down || keyCode === Keys.up || keyCode === Keys.left || keyCode === Keys.right;\n\n      if (isAction && isTargetCell) {\n        event.preventDefault();\n        event.stopPropagation();\n        this.activate.emit({\n          type: 'keydown',\n          event,\n          row: this.row,\n          group: this.group,\n          rowHeight: this.rowHeight,\n          column: this.column,\n          value: this.value,\n          cellElement: this._element\n        });\n      }\n    }\n\n    onCheckboxChange(event) {\n      this.activate.emit({\n        type: 'checkbox',\n        event,\n        row: this.row,\n        group: this.group,\n        rowHeight: this.rowHeight,\n        column: this.column,\n        value: this.value,\n        cellElement: this._element,\n        treeStatus: 'collapsed'\n      });\n    }\n\n    calcSortDir(sorts) {\n      if (!sorts) {\n        return;\n      }\n\n      const sort = sorts.find(s => {\n        return s.prop === this.column.prop;\n      });\n\n      if (sort) {\n        return sort.dir;\n      }\n    }\n\n    stripHtml(html) {\n      if (!html.replace) {\n        return html;\n      }\n\n      return html.replace(/<\\/?[^>]+(>|$)/g, '');\n    }\n\n    onTreeAction() {\n      this.treeAction.emit(this.row);\n    }\n\n    calcLeftMargin(column, row) {\n      const levelIndent = column.treeLevelIndent != null ? column.treeLevelIndent : 50;\n      return column.isTreeColumn ? row.level * levelIndent : 0;\n    }\n\n  }\n\n  DataTableBodyCellComponent.ɵfac = function DataTableBodyCellComponent_Factory(t) {\n    return new (t || DataTableBodyCellComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef));\n  };\n\n  DataTableBodyCellComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: DataTableBodyCellComponent,\n    selectors: [[\"datatable-body-cell\"]],\n    viewQuery: function DataTableBodyCellComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵviewQuery(_c3, 7, ViewContainerRef);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.cellTemplate = _t.first);\n      }\n    },\n    hostVars: 10,\n    hostBindings: function DataTableBodyCellComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"focus\", function DataTableBodyCellComponent_focus_HostBindingHandler() {\n          return ctx.onFocus();\n        })(\"blur\", function DataTableBodyCellComponent_blur_HostBindingHandler() {\n          return ctx.onBlur();\n        })(\"click\", function DataTableBodyCellComponent_click_HostBindingHandler($event) {\n          return ctx.onClick($event);\n        })(\"dblclick\", function DataTableBodyCellComponent_dblclick_HostBindingHandler($event) {\n          return ctx.onDblClick($event);\n        })(\"keydown\", function DataTableBodyCellComponent_keydown_HostBindingHandler($event) {\n          return ctx.onKeyDown($event);\n        });\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassMap(ctx.columnCssClasses);\n        ɵngcc0.ɵɵstyleProp(\"width\", ctx.width, \"px\")(\"min-width\", ctx.minWidth, \"px\")(\"max-width\", ctx.maxWidth, \"px\")(\"height\", ctx.height);\n      }\n    },\n    inputs: {\n      group: \"group\",\n      rowHeight: \"rowHeight\",\n      isSelected: \"isSelected\",\n      expanded: \"expanded\",\n      rowIndex: \"rowIndex\",\n      column: \"column\",\n      row: \"row\",\n      sorts: \"sorts\",\n      treeStatus: \"treeStatus\",\n      displayCheck: \"displayCheck\"\n    },\n    outputs: {\n      activate: \"activate\",\n      treeAction: \"treeAction\"\n    },\n    decls: 5,\n    vars: 6,\n    consts: [[1, \"datatable-body-cell-label\"], [\"class\", \"datatable-checkbox\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"title\", \"innerHTML\", 4, \"ngIf\"], [1, \"datatable-checkbox\"], [\"type\", \"checkbox\", 3, \"checked\", \"click\"], [\"class\", \"datatable-tree-button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"datatable-tree-button\", 3, \"disabled\", \"click\"], [\"class\", \"icon datatable-icon-collapse\", 4, \"ngIf\"], [\"class\", \"icon datatable-icon-up\", 4, \"ngIf\"], [\"class\", \"icon datatable-icon-down\", 4, \"ngIf\"], [1, \"icon\", \"datatable-icon-collapse\"], [1, \"icon\", \"datatable-icon-up\"], [1, \"icon\", \"datatable-icon-down\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"title\", \"innerHTML\"], [\"cellTemplate\", \"\"]],\n    template: function DataTableBodyCellComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"div\", 0);\n        ɵngcc0.ɵɵtemplate(1, DataTableBodyCellComponent_label_1_Template, 2, 1, \"label\", 1);\n        ɵngcc0.ɵɵtemplate(2, DataTableBodyCellComponent_ng_container_2_Template, 3, 2, \"ng-container\", 2);\n        ɵngcc0.ɵɵtemplate(3, DataTableBodyCellComponent_span_3_Template, 1, 2, \"span\", 3);\n        ɵngcc0.ɵɵtemplate(4, DataTableBodyCellComponent_4_Template, 2, 2, undefined, 2);\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵstyleProp(\"margin-left\", ctx.calcLeftMargin(ctx.column, ctx.row), \"px\");\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.column.checkboxable && (!ctx.displayCheck || ctx.displayCheck(ctx.row, ctx.column, ctx.value)));\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.column.isTreeColumn);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", !ctx.column.cellTemplate);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.column.cellTemplate);\n      }\n    },\n    directives: [ɵngcc1.NgIf, ɵngcc1.NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return DataTableBodyCellComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction selectRows(selected, row, comparefn) {\n  const selectedIndex = comparefn(row, selected);\n\n  if (selectedIndex > -1) {\n    selected.splice(selectedIndex, 1);\n  } else {\n    selected.push(row);\n  }\n\n  return selected;\n}\n\nfunction selectRowsBetween(selected, rows, index, prevIndex, comparefn) {\n  const reverse = index < prevIndex;\n\n  for (let i = 0; i < rows.length; i++) {\n    const row = rows[i];\n    const greater = i >= prevIndex && i <= index;\n    const lesser = i <= prevIndex && i >= index;\n    let range = {\n      start: 0,\n      end: 0\n    };\n\n    if (reverse) {\n      range = {\n        start: index,\n        end: prevIndex\n      };\n    } else {\n      range = {\n        start: prevIndex,\n        end: index + 1\n      };\n    }\n\n    if (reverse && lesser || !reverse && greater) {\n      // if in the positive range to be added to `selected`, and\n      // not already in the selected array, add it\n      if (i >= range.start && i <= range.end) {\n        selected.push(row);\n      }\n    }\n  }\n\n  return selected;\n}\n\nlet DataTableSelectionComponent = /*#__PURE__*/(() => {\n  class DataTableSelectionComponent {\n    constructor() {\n      this.activate = new EventEmitter();\n      this.select = new EventEmitter();\n    }\n\n    selectRow(event, index, row) {\n      if (!this.selectEnabled) return;\n      const chkbox = this.selectionType === SelectionType.checkbox;\n      const multi = this.selectionType === SelectionType.multi;\n      const multiClick = this.selectionType === SelectionType.multiClick;\n      let selected = [];\n\n      if (multi || chkbox || multiClick) {\n        if (event.shiftKey) {\n          selected = selectRowsBetween([], this.rows, index, this.prevIndex, this.getRowSelectedIdx.bind(this));\n        } else if (event.ctrlKey || event.metaKey || multiClick || chkbox) {\n          selected = selectRows([...this.selected], row, this.getRowSelectedIdx.bind(this));\n        } else {\n          selected = selectRows([], row, this.getRowSelectedIdx.bind(this));\n        }\n      } else {\n        selected = selectRows([], row, this.getRowSelectedIdx.bind(this));\n      }\n\n      if (typeof this.selectCheck === 'function') {\n        selected = selected.filter(this.selectCheck.bind(this));\n      }\n\n      this.selected.splice(0, this.selected.length);\n      this.selected.push(...selected);\n      this.prevIndex = index;\n      this.select.emit({\n        selected\n      });\n    }\n\n    onActivate(model, index) {\n      const {\n        type,\n        event,\n        row\n      } = model;\n      const chkbox = this.selectionType === SelectionType.checkbox;\n      const select = !chkbox && (type === 'click' || type === 'dblclick') || chkbox && type === 'checkbox';\n\n      if (select) {\n        this.selectRow(event, index, row);\n      } else if (type === 'keydown') {\n        if (event.keyCode === Keys.return) {\n          this.selectRow(event, index, row);\n        } else {\n          this.onKeyboardFocus(model);\n        }\n      }\n\n      this.activate.emit(model);\n    }\n\n    onKeyboardFocus(model) {\n      const {\n        keyCode\n      } = model.event;\n      const shouldFocus = keyCode === Keys.up || keyCode === Keys.down || keyCode === Keys.right || keyCode === Keys.left;\n\n      if (shouldFocus) {\n        const isCellSelection = this.selectionType === SelectionType.cell;\n\n        if (!model.cellElement || !isCellSelection) {\n          this.focusRow(model.rowElement, keyCode);\n        } else if (isCellSelection) {\n          this.focusCell(model.cellElement, model.rowElement, keyCode, model.cellIndex);\n        }\n      }\n    }\n\n    focusRow(rowElement, keyCode) {\n      const nextRowElement = this.getPrevNextRow(rowElement, keyCode);\n      if (nextRowElement) nextRowElement.focus();\n    }\n\n    getPrevNextRow(rowElement, keyCode) {\n      const parentElement = rowElement.parentElement;\n\n      if (parentElement) {\n        let focusElement;\n\n        if (keyCode === Keys.up) {\n          focusElement = parentElement.previousElementSibling;\n        } else if (keyCode === Keys.down) {\n          focusElement = parentElement.nextElementSibling;\n        }\n\n        if (focusElement && focusElement.children.length) {\n          return focusElement.children[0];\n        }\n      }\n    }\n\n    focusCell(cellElement, rowElement, keyCode, cellIndex) {\n      let nextCellElement;\n\n      if (keyCode === Keys.left) {\n        nextCellElement = cellElement.previousElementSibling;\n      } else if (keyCode === Keys.right) {\n        nextCellElement = cellElement.nextElementSibling;\n      } else if (keyCode === Keys.up || keyCode === Keys.down) {\n        const nextRowElement = this.getPrevNextRow(rowElement, keyCode);\n\n        if (nextRowElement) {\n          const children = nextRowElement.getElementsByClassName('datatable-body-cell');\n          if (children.length) nextCellElement = children[cellIndex];\n        }\n      }\n\n      if (nextCellElement) nextCellElement.focus();\n    }\n\n    getRowSelected(row) {\n      return this.getRowSelectedIdx(row, this.selected) > -1;\n    }\n\n    getRowSelectedIdx(row, selected) {\n      if (!selected || !selected.length) return -1;\n      const rowId = this.rowIdentity(row);\n      return selected.findIndex(r => {\n        const id = this.rowIdentity(r);\n        return id === rowId;\n      });\n    }\n\n  }\n\n  DataTableSelectionComponent.ɵfac = function DataTableSelectionComponent_Factory(t) {\n    return new (t || DataTableSelectionComponent)();\n  };\n\n  DataTableSelectionComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: DataTableSelectionComponent,\n    selectors: [[\"datatable-selection\"]],\n    inputs: {\n      rows: \"rows\",\n      selected: \"selected\",\n      selectEnabled: \"selectEnabled\",\n      selectionType: \"selectionType\",\n      rowIdentity: \"rowIdentity\",\n      selectCheck: \"selectCheck\"\n    },\n    outputs: {\n      activate: \"activate\",\n      select: \"select\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function DataTableSelectionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return DataTableSelectionComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction defaultSumFunc(cells) {\n  const cellsWithValues = cells.filter(cell => !!cell);\n\n  if (!cellsWithValues.length) {\n    return null;\n  }\n\n  if (cellsWithValues.some(cell => typeof cell !== 'number')) {\n    return null;\n  }\n\n  return cellsWithValues.reduce((res, cell) => res + cell);\n}\n\nfunction noopSumFunc(cells) {\n  return null;\n}\n\nlet DataTableSummaryRowComponent = /*#__PURE__*/(() => {\n  class DataTableSummaryRowComponent {\n    constructor() {\n      this.summaryRow = {};\n    }\n\n    ngOnChanges() {\n      if (!this.columns || !this.rows) {\n        return;\n      }\n\n      this.updateInternalColumns();\n      this.updateValues();\n    }\n\n    updateInternalColumns() {\n      this._internalColumns = this.columns.map(col => Object.assign(Object.assign({}, col), {\n        cellTemplate: col.summaryTemplate\n      }));\n    }\n\n    updateValues() {\n      this.summaryRow = {};\n      this.columns.filter(col => !col.summaryTemplate).forEach(col => {\n        const cellsFromSingleColumn = this.rows.map(row => row[col.prop]);\n        const sumFunc = this.getSummaryFunction(col);\n        this.summaryRow[col.prop] = col.pipe ? col.pipe.transform(sumFunc(cellsFromSingleColumn)) : sumFunc(cellsFromSingleColumn);\n      });\n    }\n\n    getSummaryFunction(column) {\n      if (column.summaryFunc === undefined) {\n        return defaultSumFunc;\n      } else if (column.summaryFunc === null) {\n        return noopSumFunc;\n      } else {\n        return column.summaryFunc;\n      }\n    }\n\n  }\n\n  DataTableSummaryRowComponent.ɵfac = function DataTableSummaryRowComponent_Factory(t) {\n    return new (t || DataTableSummaryRowComponent)();\n  };\n\n  DataTableSummaryRowComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: DataTableSummaryRowComponent,\n    selectors: [[\"datatable-summary-row\"]],\n    hostAttrs: [1, \"datatable-summary-row\"],\n    inputs: {\n      rows: \"rows\",\n      columns: \"columns\",\n      rowHeight: \"rowHeight\",\n      offsetX: \"offsetX\",\n      innerWidth: \"innerWidth\"\n    },\n    features: [ɵngcc0.ɵɵNgOnChangesFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"tabindex\", \"-1\", 3, \"innerWidth\", \"offsetX\", \"columns\", \"rowHeight\", \"row\", \"rowIndex\", 4, \"ngIf\"], [\"tabindex\", \"-1\", 3, \"innerWidth\", \"offsetX\", \"columns\", \"rowHeight\", \"row\", \"rowIndex\"]],\n    template: function DataTableSummaryRowComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, DataTableSummaryRowComponent_datatable_body_row_0_Template, 1, 6, \"datatable-body-row\", 0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.summaryRow && ctx._internalColumns);\n      }\n    },\n    directives: [ɵngcc1.NgIf, DataTableBodyRowComponent],\n    encapsulation: 2\n  });\n  return DataTableSummaryRowComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet NgxDatatableModule = /*#__PURE__*/(() => {\n  class NgxDatatableModule {\n    /**\n     * Configure global configuration via INgxDatatableConfig\n     * @param configuration\n     */\n    static forRoot(configuration) {\n      return {\n        ngModule: NgxDatatableModule,\n        providers: [{\n          provide: 'configuration',\n          useValue: configuration\n        }]\n      };\n    }\n\n  }\n\n  NgxDatatableModule.ɵfac = function NgxDatatableModule_Factory(t) {\n    return new (t || NgxDatatableModule)();\n  };\n\n  NgxDatatableModule.ɵmod = /*@__PURE__*/ɵngcc0.ɵɵdefineNgModule({\n    type: NgxDatatableModule\n  });\n  NgxDatatableModule.ɵinj = /*@__PURE__*/ɵngcc0.ɵɵdefineInjector({\n    providers: [ScrollbarHelper, DimensionsHelper, ColumnChangesService],\n    imports: [[CommonModule]]\n  });\n  return NgxDatatableModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(NgxDatatableModule, {\n    declarations: function () {\n      return [DataTableFooterTemplateDirective, VisibilityDirective, DraggableDirective, ResizeableDirective, OrderableDirective, LongPressDirective, ScrollerComponent, DatatableComponent, DataTableColumnDirective, DataTableHeaderComponent, DataTableHeaderCellComponent, DataTableBodyComponent, DataTableFooterComponent, DataTablePagerComponent, ProgressBarComponent, DataTableBodyRowComponent, DataTableRowWrapperComponent, DatatableRowDetailDirective, DatatableGroupHeaderDirective, DatatableRowDetailTemplateDirective, DataTableBodyCellComponent, DataTableSelectionComponent, DataTableColumnHeaderDirective, DataTableColumnCellDirective, DataTableColumnCellTreeToggle, DatatableFooterDirective, DatatableGroupHeaderTemplateDirective, DataTableSummaryRowComponent];\n    },\n    imports: function () {\n      return [CommonModule];\n    },\n    exports: function () {\n      return [DatatableComponent, DatatableRowDetailDirective, DatatableGroupHeaderDirective, DatatableRowDetailTemplateDirective, DataTableColumnDirective, DataTableColumnHeaderDirective, DataTableColumnCellDirective, DataTableColumnCellTreeToggle, DataTableFooterTemplateDirective, DatatableFooterDirective, DataTablePagerComponent, DatatableGroupHeaderTemplateDirective];\n    }\n  });\n})();\n\nvar ClickType = /*#__PURE__*/(() => {\n  (function (ClickType) {\n    ClickType[\"single\"] = \"single\";\n    ClickType[\"double\"] = \"double\";\n  })(ClickType || (ClickType = {}));\n\n  return ClickType;\n})();\n\nif (typeof document !== 'undefined' && !document.elementsFromPoint) {\n  document.elementsFromPoint = elementsFromPoint;\n}\n/*tslint:disable*/\n\n/**\n * Polyfill for `elementsFromPoint`\n *\n * https://developer.mozilla.org/en-US/docs/Web/API/Document/elementsFromPoint\n * https://gist.github.com/iddan/54d5d9e58311b0495a91bf06de661380\n * https://gist.github.com/oslego/7265412\n */\n\n\nfunction elementsFromPoint(x, y) {\n  const elements = [];\n  const previousPointerEvents = [];\n  let current; // TODO: window.getComputedStyle should be used with inferred type (Element)\n\n  let i;\n  let d; //if (document === undefined) return elements;\n  // get all elements via elementFromPoint, and remove them from hit-testing in order\n\n  while ((current = document.elementFromPoint(x, y)) && elements.indexOf(current) === -1 && current != null) {\n    // push the element and its current style\n    elements.push(current);\n    previousPointerEvents.push({\n      value: current.style.getPropertyValue('pointer-events'),\n      priority: current.style.getPropertyPriority('pointer-events')\n    }); // add \"pointer-events: none\", to get to the underlying element\n\n    current.style.setProperty('pointer-events', 'none', 'important');\n  } // restore the previous pointer-events values\n\n\n  for (i = previousPointerEvents.length; d = previousPointerEvents[--i];) {\n    elements[i].style.setProperty('pointer-events', d.value ? d.value : '', d.priority);\n  } // return our results\n\n\n  return elements;\n}\n/*tslint:enable*/\n\n/*\n * Public API Surface of ngx-datatable\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ClickType, ColumnChangesService, ColumnMode, ContextmenuType, DataTableBodyCellComponent, DataTableBodyComponent, DataTableBodyRowComponent, DataTableColumnCellDirective, DataTableColumnCellTreeToggle, DataTableColumnDirective, DataTableColumnHeaderDirective, DataTableFooterComponent, DataTableFooterTemplateDirective, DataTableHeaderCellComponent, DataTableHeaderComponent, DataTablePagerComponent, DataTableRowWrapperComponent, DataTableSelectionComponent, DataTableSummaryRowComponent, DatatableComponent, DatatableFooterDirective, DatatableGroupHeaderDirective, DatatableGroupHeaderTemplateDirective, DatatableRowDetailDirective, DatatableRowDetailTemplateDirective, DimensionsHelper, DraggableDirective, Keys, LongPressDirective, NgxDatatableModule, OrderableDirective, ProgressBarComponent, ResizeableDirective, RowHeightCache, ScrollbarHelper, ScrollerComponent, SelectionType, SortDirection, SortType, VisibilityDirective, adjustColumnWidths, camelCase, columnGroupWidths, columnTotalWidth, columnsByPin, columnsByPinArr, columnsTotalWidth, deCamelCase, deepValueGetter, elementsFromPoint, emptyStringGetter, forceFillColumnWidths, getTotalFlexGrow, getVendorPrefixedName, getterForProp, groupRowsByParents, id, isNullOrUndefined, nextSortDir, numericIndexGetter, optionalGetterForProp, orderByComparator, selectRows, selectRowsBetween, setColumnDefaults, shallowValueGetter, sortRows, throttle, throttleable, translateTemplates, translateXY, ɵ0 }; //# sourceMappingURL=swimlane-ngx-datatable.js.map", "map": null, "metadata": {}, "sourceType": "module"}