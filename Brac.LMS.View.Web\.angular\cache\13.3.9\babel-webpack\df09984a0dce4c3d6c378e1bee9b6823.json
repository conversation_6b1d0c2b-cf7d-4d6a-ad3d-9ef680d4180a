{"ast": null, "code": "import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function exhaustMap(project, resultSelector) {\n  if (resultSelector) {\n    return source => source.pipe(exhaustMap((a, i) => innerFrom(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii)))));\n  }\n\n  return operate((source, subscriber) => {\n    let index = 0;\n    let innerSub = null;\n    let isComplete = false;\n    source.subscribe(new OperatorSubscriber(subscriber, outerValue => {\n      if (!innerSub) {\n        innerSub = new OperatorSubscriber(subscriber, undefined, () => {\n          innerSub = null;\n          isComplete && subscriber.complete();\n        });\n        innerFrom(project(outerValue, index++)).subscribe(innerSub);\n      }\n    }, () => {\n      isComplete = true;\n      !innerSub && subscriber.complete();\n    }));\n  });\n} //# sourceMappingURL=exhaustMap.js.map", "map": null, "metadata": {}, "sourceType": "module"}