{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Norwegian Bokmål [nb]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/rexxars\n//!           <PERSON><PERSON><PERSON> : https://github.com/sigurdga\n//!           <PERSON> : https://github.com/stephenramthun\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var nb = moment.defineLocale('nb', {\n    months: 'januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember'.split('_'),\n    monthsShort: 'jan._feb._mars_apr._mai_juni_juli_aug._sep._okt._nov._des.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag'.split('_'),\n    weekdaysShort: 'sø._ma._ti._on._to._fr._lø.'.split('_'),\n    weekdaysMin: 'sø_ma_ti_on_to_fr_lø'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY [kl.] HH:mm',\n      LLLL: 'dddd D. MMMM YYYY [kl.] HH:mm'\n    },\n    calendar: {\n      sameDay: '[i dag kl.] LT',\n      nextDay: '[i morgen kl.] LT',\n      nextWeek: 'dddd [kl.] LT',\n      lastDay: '[i går kl.] LT',\n      lastWeek: '[forrige] dddd [kl.] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'om %s',\n      past: '%s siden',\n      s: 'noen sekunder',\n      ss: '%d sekunder',\n      m: 'ett minutt',\n      mm: '%d minutter',\n      h: 'en time',\n      hh: '%d timer',\n      d: 'en dag',\n      dd: '%d dager',\n      w: 'en uke',\n      ww: '%d uker',\n      M: 'en måned',\n      MM: '%d måneder',\n      y: 'ett år',\n      yy: '%d år'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return nb;\n});", "map": null, "metadata": {}, "sourceType": "script"}