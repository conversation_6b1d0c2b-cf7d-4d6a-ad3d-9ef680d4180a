{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Inject, Component, ChangeDetectionStrategy, Input, HostListener, Directive, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { polyfill } from 'smoothscroll-polyfill';\n\nfunction NgxScrollTopComponent_button_0__svg_svg_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 6);\n    i0.ɵɵelement(1, \"path\", 7);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"fill\", ctx_r3.symbolColor);\n  }\n}\n\nfunction NgxScrollTopComponent_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 1, 2);\n    i0.ɵɵlistener(\"click\", function NgxScrollTopComponent_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return ctx_r4.scrollToTop();\n    });\n    i0.ɵɵelementStart(2, \"div\", 3);\n    i0.ɵɵelementStart(3, \"span\", null, 4);\n    i0.ɵɵprojection(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, NgxScrollTopComponent_button_0__svg_svg_6_Template, 2, 2, \"svg\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r2 = i0.ɵɵreference(4);\n\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"left\", ctx_r0.position === \"left\" ? \"20px\" : \"\")(\"background-color\", ctx_r0.backgroundColor)(\"width\", ctx_r0.size, \"px\")(\"height\", ctx_r0.size, \"px\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.theme);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", _r2.childNodes.length === 0);\n  }\n}\n\nconst _c0 = [\"*\"];\nlet NgxScrollTopCoreService = /*#__PURE__*/(() => {\n  class NgxScrollTopCoreService {\n    constructor(document) {\n      this.document = document;\n      this.scrolledFromTop = false;\n      this.isBrowser = typeof window !== 'undefined';\n      this.alreadyActivated = false;\n    }\n\n    onWindowScroll(mode) {\n      var _a, _b;\n\n      const position = ((_a = this.document.documentElement) === null || _a === void 0 ? void 0 : _a.scrollTop) || ((_b = this.document.scrollingElement) === null || _b === void 0 ? void 0 : _b.scrollTop);\n\n      switch (mode) {\n        case 'classic':\n          return this.classicMode(position);\n\n        case 'smart':\n          return this.smartMode(position);\n      }\n    }\n\n    classicMode(position) {\n      if (this.isBrowser && position > window.innerHeight) {\n        return true;\n      } else {\n        return false;\n      }\n    }\n\n    smartMode(position) {\n      let show = false;\n\n      if (position === 0) {\n        show = false;\n        this.scrolledFromTop = false;\n      }\n\n      if (this.scrolledFromTop && this.scrollOffset > position) {\n        show = true;\n      }\n\n      if (this.isBrowser && position > window.innerHeight * 2) {\n        this.scrolledFromTop = true;\n        this.scrollOffset = position;\n      }\n\n      return show;\n    }\n\n    scrollToTop() {\n      if (this.isBrowser) {\n        // Kick off the polyfill for iOS Safari\n        if (!this.alreadyActivated) {\n          polyfill();\n          this.alreadyActivated = true;\n        } // Scroll to the top\n\n\n        window.scroll({\n          top: 0,\n          left: 0,\n          behavior: 'smooth'\n        });\n      }\n    }\n\n  }\n\n  NgxScrollTopCoreService.ɵfac = function NgxScrollTopCoreService_Factory(t) {\n    return new (t || NgxScrollTopCoreService)(i0.ɵɵinject(DOCUMENT));\n  };\n\n  NgxScrollTopCoreService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NgxScrollTopCoreService,\n    factory: NgxScrollTopCoreService.ɵfac\n  });\n  return NgxScrollTopCoreService;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet NgxScrollTopComponent = /*#__PURE__*/(() => {\n  class NgxScrollTopComponent {\n    constructor(core, cdr) {\n      this.core = core;\n      this.cdr = cdr;\n      this.position = 'right';\n      this.theme = 'gray';\n      this.mode = 'classic';\n      this.show = false;\n    }\n\n    onWindowScroll() {\n      const show = this.core.onWindowScroll(this.mode); // Performance boost. Only update the state if it has changed.\n\n      if (this.show !== show) {\n        this.show = show;\n        this.cdr.markForCheck();\n      }\n    }\n\n    ngOnChanges(changes) {\n      // Deprecation warning. It will be removed soon.\n      if (changes.symbol) {\n        console.error(`NgxScrollTop: You are trying to set \\`${changes['symbol'].currentValue}\\` as your symbol but Input \\`[symbol]=\"\\'↑\\'\"\\` is deprecated now.\\n\\r`, `Use \\`Content projection\\` method, like this:\\n\\r\\n\\r`, `<ngx-scrolltop>${changes['symbol'].currentValue}</ngx-scrolltop>\\n\\r\\n\\r`, `More info: https://github.com/bartholomej/ngx-scrolltop#symbol`);\n      }\n    }\n\n    scrollToTop() {\n      this.core.scrollToTop();\n    }\n\n  }\n\n  NgxScrollTopComponent.ɵfac = function NgxScrollTopComponent_Factory(t) {\n    return new (t || NgxScrollTopComponent)(i0.ɵɵdirectiveInject(NgxScrollTopCoreService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n\n  NgxScrollTopComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NgxScrollTopComponent,\n    selectors: [[\"ngx-scrolltop\"]],\n    hostBindings: function NgxScrollTopComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"scroll\", function NgxScrollTopComponent_scroll_HostBindingHandler() {\n          return ctx.onWindowScroll();\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      backgroundColor: \"backgroundColor\",\n      symbolColor: \"symbolColor\",\n      size: \"size\",\n      symbol: \"symbol\",\n      position: \"position\",\n      theme: \"theme\",\n      mode: \"mode\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 1,\n    consts: [[\"type\", \"button\", \"role\", \"button\", \"aria-label\", \"Scroll to top of the page\", \"tabindex\", \"0\", \"class\", \"scrolltop-button\", 3, \"ngClass\", \"left\", \"backgroundColor\", \"width\", \"height\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"role\", \"button\", \"aria-label\", \"Scroll to top of the page\", \"tabindex\", \"0\", 1, \"scrolltop-button\", 3, \"ngClass\", \"click\"], [\"scrollTopButton\", \"\"], [1, \"symbol-container\"], [\"ref\", \"\"], [\"aria-hidden\", \"true\", \"focusable\", \"false\", \"role\", \"img\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 448 512\", 3, \"fill\", 4, \"ngIf\"], [\"aria-hidden\", \"true\", \"focusable\", \"false\", \"role\", \"img\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 448 512\"], [\"d\", \"M240.971 130.524l194.343 194.343c9.373 9.373 9.373 24.569 0 33.941l-22.667 22.667c-9.357 9.357-24.522 9.375-33.901.04L224 227.495 69.255 381.516c-9.379 9.335-24.544 9.317-33.901-.04l-22.667-22.667c-9.373-9.373-9.373-24.569 0-33.941L207.03 130.525c9.372-9.373 24.568-9.373 33.941-.001z\"]],\n    template: function NgxScrollTopComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, NgxScrollTopComponent_button_0_Template, 7, 10, \"button\", 0);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.show);\n      }\n    },\n    directives: [i2.NgIf, i2.NgClass],\n    styles: [\"button[_ngcontent-%COMP%]{outline:0;-webkit-user-select:none;user-select:none}.scrolltop-button[_ngcontent-%COMP%]{position:fixed;display:flex;justify-content:center;align-items:center;border-radius:50%;padding:0;width:40px;height:40px;right:20px;bottom:20px;cursor:pointer;border:none;transition:opacity .1s linear;z-index:10000;box-shadow:0 5px 5px -3px #0003,0 8px 10px 1px #00000024,0 3px 14px 2px #0000001f}.scrolltop-button[_ngcontent-%COMP%]:hover{opacity:.92}.scrolltop-button[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:15px}.scrolltop-button[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{transform:translateY(10%);width:35%;vertical-align:baseline}.scrolltop-button.black[_ngcontent-%COMP%]{background-color:#000;color:#fff}.scrolltop-button.black[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.black[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#fff}.scrolltop-button.black[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#fff}.scrolltop-button.white[_ngcontent-%COMP%]{background-color:#fff;color:#000}.scrolltop-button.white[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.white[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#000}.scrolltop-button.white[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#000}.scrolltop-button.gray[_ngcontent-%COMP%]{background-color:#212121;color:#fafafa}.scrolltop-button.gray[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.gray[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#fafafa}.scrolltop-button.gray[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#fafafa}.scrolltop-button.grey[_ngcontent-%COMP%]{background-color:#212121;color:#fafafa}.scrolltop-button.grey[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.grey[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#fafafa}.scrolltop-button.grey[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#fafafa}.scrolltop-button.brown[_ngcontent-%COMP%]{background-color:#3e2723;color:#efebe9}.scrolltop-button.brown[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.brown[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#efebe9}.scrolltop-button.brown[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#efebe9}.scrolltop-button.deeporange[_ngcontent-%COMP%]{background-color:#bf360c;color:#fbe9e7}.scrolltop-button.deeporange[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.deeporange[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#fbe9e7}.scrolltop-button.deeporange[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#fbe9e7}.scrolltop-button.orange[_ngcontent-%COMP%]{background-color:#ff6d00;color:#fff3e0}.scrolltop-button.orange[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.orange[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#fff3e0}.scrolltop-button.orange[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#fff3e0}.scrolltop-button.yellow[_ngcontent-%COMP%]{background-color:#ffd600;color:#fffde7}.scrolltop-button.yellow[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.yellow[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#fffde7}.scrolltop-button.yellow[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#fffde7}.scrolltop-button.green[_ngcontent-%COMP%]{background-color:#1b5e20;color:#e8f5e9}.scrolltop-button.green[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.green[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#e8f5e9}.scrolltop-button.green[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#e8f5e9}.scrolltop-button.blue[_ngcontent-%COMP%]{background-color:#2962ff;color:#e3f2fd}.scrolltop-button.blue[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.blue[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#e3f2fd}.scrolltop-button.blue[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#e3f2fd}.scrolltop-button.purple[_ngcontent-%COMP%]{background-color:#4a148c;color:#f3e5f5}.scrolltop-button.purple[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.purple[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#f3e5f5}.scrolltop-button.purple[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#f3e5f5}.scrolltop-button.deeppurple[_ngcontent-%COMP%]{background-color:#311b92;color:#ede7f6}.scrolltop-button.deeppurple[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.deeppurple[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#ede7f6}.scrolltop-button.deeppurple[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#ede7f6}.scrolltop-button.pink[_ngcontent-%COMP%]{background-color:#880e4f;color:#fce4ec}.scrolltop-button.pink[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.pink[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#fce4ec}.scrolltop-button.pink[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#fce4ec}.scrolltop-button.red[_ngcontent-%COMP%]{background-color:#b71c1c;color:#ffebee}.scrolltop-button.red[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.red[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#ffebee}.scrolltop-button.red[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#ffebee}.scrolltop-button.indigo[_ngcontent-%COMP%]{background-color:#1a237e;color:#e8eaf6}.scrolltop-button.indigo[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.indigo[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#e8eaf6}.scrolltop-button.indigo[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#e8eaf6}.scrolltop-button.lightblue[_ngcontent-%COMP%]{background-color:#01579b;color:#e1f5fe}.scrolltop-button.lightblue[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.lightblue[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#e1f5fe}.scrolltop-button.lightblue[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#e1f5fe}.scrolltop-button.cyan[_ngcontent-%COMP%]{background-color:#006064;color:#e0f7fa}.scrolltop-button.cyan[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.cyan[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#e0f7fa}.scrolltop-button.cyan[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#e0f7fa}.scrolltop-button.teal[_ngcontent-%COMP%]{background-color:#004d40;color:#e0f2f1}.scrolltop-button.teal[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.teal[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#e0f2f1}.scrolltop-button.teal[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#e0f2f1}.scrolltop-button.lightgreen[_ngcontent-%COMP%]{background-color:#33691e;color:#f1f8e9}.scrolltop-button.lightgreen[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.lightgreen[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#f1f8e9}.scrolltop-button.lightgreen[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#f1f8e9}.scrolltop-button.lime[_ngcontent-%COMP%]{background-color:#827717;color:#f9fbe7}.scrolltop-button.lime[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.lime[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#f9fbe7}.scrolltop-button.lime[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#f9fbe7}.scrolltop-button.amber[_ngcontent-%COMP%]{background-color:#ff6f00;color:#fff8e1}.scrolltop-button.amber[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.amber[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#fff8e1}.scrolltop-button.amber[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#fff8e1}.scrolltop-button.bluegrey[_ngcontent-%COMP%]{background-color:#263238;color:#eceff1}.scrolltop-button.bluegrey[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%], .scrolltop-button.bluegrey[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#eceff1}.scrolltop-button.bluegrey[_ngcontent-%COMP%]   .symbol-container[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{fill:#eceff1}\"],\n    changeDetection: 0\n  });\n  return NgxScrollTopComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet NgxScrollTopDirective = /*#__PURE__*/(() => {\n  class NgxScrollTopDirective {\n    constructor(el, core) {\n      this.el = el;\n      this.core = core;\n      this.mode = 'classic';\n      this.show = false;\n      this.hideElement();\n    }\n\n    onWindowScroll() {\n      const show = this.core.onWindowScroll(this.mode); // Performance boost. Only update the DOM when the state changes.\n\n      if (this.show !== show) {\n        show ? this.showElement() : this.hideElement();\n        this.show = show;\n      }\n    }\n\n    onClick() {\n      this.scrollToTop();\n    }\n\n    hideElement() {\n      this.el.nativeElement.style.display = 'none';\n    }\n\n    showElement() {\n      this.el.nativeElement.style.display = '';\n    }\n\n    scrollToTop() {\n      this.core.scrollToTop();\n    }\n\n  }\n\n  NgxScrollTopDirective.ɵfac = function NgxScrollTopDirective_Factory(t) {\n    return new (t || NgxScrollTopDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NgxScrollTopCoreService));\n  };\n\n  NgxScrollTopDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgxScrollTopDirective,\n    selectors: [[\"\", \"ngxScrollTop\", \"\"]],\n    hostBindings: function NgxScrollTopDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"scroll\", function NgxScrollTopDirective_scroll_HostBindingHandler() {\n          return ctx.onWindowScroll();\n        }, false, i0.ɵɵresolveWindow)(\"click\", function NgxScrollTopDirective_click_HostBindingHandler() {\n          return ctx.onClick();\n        });\n      }\n    },\n    inputs: {\n      mode: [\"ngxScrollTopMode\", \"mode\"]\n    }\n  });\n  return NgxScrollTopDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet NgxScrollTopModule = /*#__PURE__*/(() => {\n  class NgxScrollTopModule {}\n\n  NgxScrollTopModule.ɵfac = function NgxScrollTopModule_Factory(t) {\n    return new (t || NgxScrollTopModule)();\n  };\n\n  NgxScrollTopModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NgxScrollTopModule\n  });\n  NgxScrollTopModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [NgxScrollTopCoreService],\n    imports: [[CommonModule]]\n  });\n  return NgxScrollTopModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/*\n * Public API Surface of ngx-scrolltop\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { NgxScrollTopComponent, NgxScrollTopDirective, NgxScrollTopModule }; //# sourceMappingURL=ngx-scrolltop.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}