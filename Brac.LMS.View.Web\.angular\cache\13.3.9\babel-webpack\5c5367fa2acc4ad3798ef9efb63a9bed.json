{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { MyProgressRoutes } from './my-progress.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let MyProgressModule = /*#__PURE__*/(() => {\n  class MyProgressModule {}\n\n  MyProgressModule.ɵfac = function MyProgressModule_Factory(t) {\n    return new (t || MyProgressModule)();\n  };\n\n  MyProgressModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: MyProgressModule\n  });\n  MyProgressModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(MyProgressRoutes), SharedModule, WebLayoutModule]]\n  });\n  return MyProgressModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}