{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Bulgarian [bg]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/kraz\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var bg = moment.defineLocale('bg', {\n    months: 'януари_февруари_март_април_май_юни_юли_август_септември_октомври_ноември_декември'.split('_'),\n    monthsShort: 'яну_фев_мар_апр_май_юни_юли_авг_сеп_окт_ное_дек'.split('_'),\n    weekdays: 'неделя_понеделник_вторник_сряда_четвъртък_петък_събота'.split('_'),\n    weekdaysShort: 'нед_пон_вто_сря_чет_пет_съб'.split('_'),\n    weekdaysMin: 'нд_пн_вт_ср_чт_пт_сб'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'D.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY H:mm',\n      LLLL: 'dddd, D MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[Днес в] LT',\n      nextDay: '[Утре в] LT',\n      nextWeek: 'dddd [в] LT',\n      lastDay: '[Вчера в] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n          case 3:\n          case 6:\n            return '[Миналата] dddd [в] LT';\n\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[Миналия] dddd [в] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'след %s',\n      past: 'преди %s',\n      s: 'няколко секунди',\n      ss: '%d секунди',\n      m: 'минута',\n      mm: '%d минути',\n      h: 'час',\n      hh: '%d часа',\n      d: 'ден',\n      dd: '%d дена',\n      w: 'седмица',\n      ww: '%d седмици',\n      M: 'месец',\n      MM: '%d месеца',\n      y: 'година',\n      yy: '%d години'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(ев|ен|ти|ви|ри|ми)/,\n    ordinal: function (number) {\n      var lastDigit = number % 10,\n          last2Digits = number % 100;\n\n      if (number === 0) {\n        return number + '-ев';\n      } else if (last2Digits === 0) {\n        return number + '-ен';\n      } else if (last2Digits > 10 && last2Digits < 20) {\n        return number + '-ти';\n      } else if (lastDigit === 1) {\n        return number + '-ви';\n      } else if (lastDigit === 2) {\n        return number + '-ри';\n      } else if (lastDigit === 7 || lastDigit === 8) {\n        return number + '-ми';\n      } else {\n        return number + '-ти';\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n\n    }\n  });\n  return bg;\n});", "map": null, "metadata": {}, "sourceType": "script"}