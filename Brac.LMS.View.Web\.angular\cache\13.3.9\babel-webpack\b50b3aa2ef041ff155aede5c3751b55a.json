{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport { NavigationEnd } from '@angular/router';\nimport { debounceTime } from 'rxjs';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { ColumnMode } from '@swimlane/ngx-datatable';\nimport { Page } from '../_models/page';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../app.component\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../_services/common.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"@ng-select/ng-select\";\nimport * as i7 from \"@swimlane/ngx-datatable\";\nimport * as i8 from \"@angular/common\";\n\nconst _c0 = function (a1) {\n  return [\"/course-details\", a1];\n};\n\nfunction MyProgressComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r8 = ctx.row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r8[\"Title\"]);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, row_r8.Id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", row_r8[\"Title\"], \" \");\n  }\n}\n\nfunction MyProgressComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r9 = ctx.row;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", row_r9[\"NoOfContentsStudied\"], \" / \", row_r9[\"NoOfContents\"], \"\");\n  }\n}\n\nfunction MyProgressComponent_ng_template_31_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1, \"Yes\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction MyProgressComponent_ng_template_31_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 34);\n    i0.ɵɵtext(1, \"No\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction MyProgressComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MyProgressComponent_ng_template_31_span_0_Template, 2, 0, \"span\", 31);\n    i0.ɵɵtemplate(1, MyProgressComponent_ng_template_31_span_1_Template, 2, 0, \"span\", 32);\n  }\n\n  if (rf & 2) {\n    const row_r10 = ctx.row;\n    i0.ɵɵproperty(\"ngIf\", row_r10[\"Progress\"] === 100);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r10[\"Progress\"] !== 100);\n  }\n}\n\nfunction MyProgressComponent_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"div\");\n    i0.ɵɵtext(2, \"Certificate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4, \"Achieved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction MyProgressComponent_ng_template_34_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1, \"Yes\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction MyProgressComponent_ng_template_34_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 34);\n    i0.ɵɵtext(1, \"No\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction MyProgressComponent_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MyProgressComponent_ng_template_34_span_0_Template, 2, 0, \"span\", 31);\n    i0.ɵɵtemplate(1, MyProgressComponent_ng_template_34_span_1_Template, 2, 0, \"span\", 32);\n  }\n\n  if (rf & 2) {\n    const row_r14 = ctx.row;\n    i0.ɵɵproperty(\"ngIf\", row_r14[\"CertificateAchieved\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !row_r14[\"CertificateAchieved\"]);\n  }\n}\n\nfunction MyProgressComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r17 = ctx.value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r17);\n  }\n}\n\nfunction MyProgressComponent_ng_template_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r18 = ctx.value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r18);\n  }\n}\n\nexport let MyProgressComponent = /*#__PURE__*/(() => {\n  class MyProgressComponent {\n    constructor(appComponent, formBuilder, router, _service, toastr, route) {\n      this.appComponent = appComponent;\n      this.formBuilder = formBuilder;\n      this.router = router;\n      this._service = _service;\n      this.toastr = toastr;\n      this.route = route;\n      this.baseUrl = environment.baseUrl;\n      this.mediaBaseUrl = environment.mediaBaseUrl;\n      this.timestamp = new Date().getTime();\n      this.courseList = [];\n      this.categoryList = [];\n      this.page = new Page(); // @ViewChild(DatatableComponent) tableComponent: DatatableComponent;\n\n      this.rows = [];\n      this.loadingIndicator = false;\n      this.ColumnMode = ColumnMode;\n      this.scrollBarHorizontal = window.innerWidth < 1200;\n      this.page.pageNumber = 0;\n      this.page.size = 10;\n      this.router.events.subscribe(evt => {\n        if (!(evt instanceof NavigationEnd)) {\n          return;\n        }\n\n        window.scrollTo(0, 0);\n      });\n    }\n\n    handleSelectClick(selectElement) {\n      this.appComponent.handleSelectClick(selectElement);\n    }\n\n    ngOnInit() {\n      this.filterForm = this.formBuilder.group({\n        name: [null],\n        categoryId: [null]\n      });\n      this.filterForm.get(\"name\").valueChanges.pipe(debounceTime(700)).subscribe(() => {\n        this.getList();\n      });\n      this.getList();\n      this.getCategoryList();\n    }\n\n    getCategoryList() {\n      this._service.get('course-category/dropdown-list').subscribe(res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.categoryList = res.Data;\n      }, err => {});\n    }\n\n    setPage(pageInfo) {\n      this.page.pageNumber = pageInfo.offset;\n      this.getList();\n    }\n\n    getList() {\n      this.loadingIndicator = true;\n      let obj = {\n        name: this.filterForm.value.name ? this.filterForm.value.name.trim() : null,\n        categoryId: this.filterForm.value.categoryId,\n        size: this.page.size,\n        pageNumber: this.page.pageNumber\n      };\n\n      this._service.get('course/my-progress', obj).subscribe({\n        next: res => {\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              timeOut: 2000\n            });\n            return;\n          }\n\n          this.rows = res.Data.Records;\n          console.log(this.rows);\n          this.page.totalElements = res.Data.Total;\n          this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n          setTimeout(() => {\n            this.loadingIndicator = false; // this.tableComponent.recalculate();\n          }, 500);\n        },\n        error: err => {\n          this.toastr.error(err.message || err, 'Error!', {\n            timeOut: 2000\n          });\n          setTimeout(() => {\n            this.loadingIndicator = false;\n          }, 1000);\n        },\n        complete: () => {}\n      });\n    }\n\n  }\n\n  MyProgressComponent.ɵfac = function MyProgressComponent_Factory(t) {\n    return new (t || MyProgressComponent)(i0.ɵɵdirectiveInject(i1.AppComponent), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.CommonService), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n  };\n\n  MyProgressComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MyProgressComponent,\n    selectors: [[\"app-my-progress\"]],\n    decls: 39,\n    vars: 29,\n    consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"mt-1\", \"mb-4\"], [1, \"row\"], [\"autocomplete\", \"off\", 1, \"col-lg-8\", \"col-12\", 3, \"formGroup\"], [1, \"col-lg-8\", \"col-12\", \"mb-3\"], [1, \"input-group\"], [\"for\", \"name\", 1, \"visually-hidden\"], [\"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Type name to search...\", 1, \"form-control\", \"rounded-custom\", \"pe-5\"], [1, \"ai-search\", \"position-absolute\", \"top-50\", \"end-0\", \"translate-middle-y\", \"me-3\"], [1, \"col-lg-4\", \"col-12\", \"mb-3\"], [\"for\", \"categoryId\", 1, \"visually-hidden\"], [\"formControlName\", \"categoryId\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Category\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\", \"change\"], [\"selectElement\", \"\"], [1, \"section\"], [1, \"col-12\"], [1, \"row\", \"justify-content-center\"], [1, \"col-lg-12\", \"min-height-400\"], [\"rowHeight\", \"auto\", 1, \"material\", 3, \"scrollbarH\", \"rows\", \"loadingIndicator\", \"columnMode\", \"headerHeight\", \"footerHeight\", \"externalPaging\", \"count\", \"offset\", \"limit\", \"page\"], [\"name\", \"Course\", 3, \"width\", \"draggable\", \"sortable\"], [\"ngx-datatable-cell-template\", \"\"], [\"name\", \"Contents\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"name\", \"Completed\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"width\", \"draggable\", \"sortable\"], [\"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"width\", \"draggable\", \"sortable\"], [\"ngx-datatable-header-template\", \"\"], [\"name\", \"Score %\", \"prop\", \"GainedPercentage\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"name\", \"Result\", \"prop\", \"Result\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"routerLinkActive\", \"router-link-active\", 1, \"fs-6\", 3, \"routerLink\", \"title\"], [\"class\", \"text-success\", 4, \"ngIf\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-success\"], [1, \"text-danger\"]],\n    template: function MyProgressComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r19 = i0.ɵɵgetCurrentView();\n\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵelementStart(4, \"h1\", 4);\n        i0.ɵɵtext(5, \"My Progress\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(6, \"hr\", 5);\n        i0.ɵɵelementStart(7, \"div\", 6);\n        i0.ɵɵelementStart(8, \"form\", 7);\n        i0.ɵɵelementStart(9, \"div\", 6);\n        i0.ɵɵelementStart(10, \"div\", 8);\n        i0.ɵɵelementStart(11, \"div\", 9);\n        i0.ɵɵelementStart(12, \"label\", 10);\n        i0.ɵɵtext(13, \"Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(14, \"input\", 11);\n        i0.ɵɵelement(15, \"i\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"div\", 13);\n        i0.ɵɵelementStart(17, \"label\", 14);\n        i0.ɵɵtext(18, \"Category\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"ng-select\", 15, 16);\n        i0.ɵɵlistener(\"click\", function MyProgressComponent_Template_ng_select_click_19_listener() {\n          i0.ɵɵrestoreView(_r19);\n\n          const _r0 = i0.ɵɵreference(20);\n\n          return ctx.handleSelectClick(_r0);\n        })(\"change\", function MyProgressComponent_Template_ng_select_change_19_listener() {\n          return ctx.getList();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"div\", 17);\n        i0.ɵɵelementStart(22, \"div\", 18);\n        i0.ɵɵelementStart(23, \"div\", 19);\n        i0.ɵɵelementStart(24, \"div\", 20);\n        i0.ɵɵelementStart(25, \"ngx-datatable\", 21);\n        i0.ɵɵlistener(\"page\", function MyProgressComponent_Template_ngx_datatable_page_25_listener($event) {\n          return ctx.setPage($event);\n        });\n        i0.ɵɵelementStart(26, \"ngx-datatable-column\", 22);\n        i0.ɵɵtemplate(27, MyProgressComponent_ng_template_27_Template, 2, 5, \"ng-template\", 23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"ngx-datatable-column\", 24);\n        i0.ɵɵtemplate(29, MyProgressComponent_ng_template_29_Template, 2, 2, \"ng-template\", 23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"ngx-datatable-column\", 25);\n        i0.ɵɵtemplate(31, MyProgressComponent_ng_template_31_Template, 2, 2, \"ng-template\", 23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"ngx-datatable-column\", 26);\n        i0.ɵɵtemplate(33, MyProgressComponent_ng_template_33_Template, 5, 0, \"ng-template\", 27);\n        i0.ɵɵtemplate(34, MyProgressComponent_ng_template_34_Template, 2, 2, \"ng-template\", 23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(35, \"ngx-datatable-column\", 28);\n        i0.ɵɵtemplate(36, MyProgressComponent_ng_template_36_Template, 2, 1, \"ng-template\", 23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(37, \"ngx-datatable-column\", 29);\n        i0.ɵɵtemplate(38, MyProgressComponent_ng_template_38_Template, 2, 1, \"ng-template\", 23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"clearable\", true)(\"clearOnBackspace\", true)(\"items\", ctx.categoryList);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"scrollbarH\", ctx.scrollBarHorizontal)(\"rows\", ctx.rows)(\"loadingIndicator\", ctx.loadingIndicator)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 70)(\"footerHeight\", 50)(\"externalPaging\", true)(\"count\", ctx.page.totalElements)(\"offset\", ctx.page.pageNumber)(\"limit\", ctx.page.size);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"width\", 400)(\"draggable\", false)(\"sortable\", false);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"width\", 200)(\"draggable\", false)(\"sortable\", false);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"width\", 250)(\"draggable\", false)(\"sortable\", false);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      }\n    },\n    directives: [i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.DefaultValueAccessor, i2.NgControlStatus, i2.FormControlName, i6.NgSelectComponent, i7.DatatableComponent, i7.DataTableColumnDirective, i7.DataTableColumnCellDirective, i3.RouterLinkWithHref, i3.RouterLinkActive, i8.NgIf, i7.DataTableColumnHeaderDirective],\n    styles: [\".min-height-400{min-height:400px}\\n\"],\n    encapsulation: 2\n  });\n  return MyProgressComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}