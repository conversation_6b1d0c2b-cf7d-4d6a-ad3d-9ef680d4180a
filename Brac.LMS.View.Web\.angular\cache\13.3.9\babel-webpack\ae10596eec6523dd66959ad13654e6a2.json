{"ast": null, "code": "import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function takeLast(count) {\n  return count <= 0 ? () => EMPTY : operate((source, subscriber) => {\n    let buffer = [];\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      buffer.push(value);\n      count < buffer.length && buffer.shift();\n    }, () => {\n      for (const value of buffer) {\n        subscriber.next(value);\n      }\n\n      subscriber.complete();\n    }, undefined, () => {\n      buffer = null;\n    }));\n  });\n} //# sourceMappingURL=takeLast.js.map", "map": null, "metadata": {}, "sourceType": "module"}