{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { ProfileRoutes } from './profile.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let ProfileModule = /*#__PURE__*/(() => {\n  class ProfileModule {}\n\n  ProfileModule.ɵfac = function ProfileModule_Factory(t) {\n    return new (t || ProfileModule)();\n  };\n\n  ProfileModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ProfileModule\n  });\n  ProfileModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(ProfileRoutes), SharedModule, WebLayoutModule]]\n  });\n  return ProfileModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}