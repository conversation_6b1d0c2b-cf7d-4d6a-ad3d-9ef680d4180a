{"ast": null, "code": "import { coerceNumberProperty, coerceElement, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, Injectable, Optional, Inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, SkipSelf, NgModule } from '@angular/core';\nimport { Subject, of, Observable, fromEvent, animationFrameScheduler, asapScheduler, Subscription, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, takeUntil, startWith, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType, supportsScrollBehavior, PlatformModule } from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/cdk/collections';\nimport { isDataSource, ArrayDataSource, _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy } from '@angular/cdk/collections';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** The injection token used to specify the virtual scrolling strategy. */\n\nconst _c0 = [\"contentWrapper\"];\nconst _c1 = [\"*\"];\nconst VIRTUAL_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\n\nclass FixedSizeVirtualScrollStrategy {\n  /**\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  constructor(itemSize, minBufferPx, maxBufferPx) {\n    this._scrolledIndexChange = new Subject();\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n    this.scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n    /** The attached viewport. */\n\n    this._viewport = null;\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n  }\n  /**\n   * Attaches this scroll strategy to a viewport.\n   * @param viewport The viewport to attach this strategy to.\n   */\n\n\n  attach(viewport) {\n    this._viewport = viewport;\n\n    this._updateTotalContentSize();\n\n    this._updateRenderedRange();\n  }\n  /** Detaches this scroll strategy from the currently attached viewport. */\n\n\n  detach() {\n    this._scrolledIndexChange.complete();\n\n    this._viewport = null;\n  }\n  /**\n   * Update the item size and buffer size.\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n\n\n  updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n    if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n    }\n\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n\n    this._updateTotalContentSize();\n\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n\n  onContentScrolled() {\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n\n  onDataLengthChanged() {\n    this._updateTotalContentSize();\n\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n\n  onContentRendered() {\n    /* no-op */\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n\n  onRenderedOffsetChanged() {\n    /* no-op */\n  }\n  /**\n   * Scroll to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling.\n   */\n\n\n  scrollToIndex(index, behavior) {\n    if (this._viewport) {\n      this._viewport.scrollToOffset(index * this._itemSize, behavior);\n    }\n  }\n  /** Update the viewport's total content size. */\n\n\n  _updateTotalContentSize() {\n    if (!this._viewport) {\n      return;\n    }\n\n    this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n  }\n  /** Update the viewport's rendered range. */\n\n\n  _updateRenderedRange() {\n    if (!this._viewport) {\n      return;\n    }\n\n    const renderedRange = this._viewport.getRenderedRange();\n\n    const newRange = {\n      start: renderedRange.start,\n      end: renderedRange.end\n    };\n\n    const viewportSize = this._viewport.getViewportSize();\n\n    const dataLength = this._viewport.getDataLength();\n\n    let scrollOffset = this._viewport.measureScrollOffset(); // Prevent NaN as result when dividing by zero.\n\n\n    let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0; // If user scrolls to the bottom of the list and data changes to a smaller list\n\n    if (newRange.end > dataLength) {\n      // We have to recalculate the first visible index based on new data length and viewport size.\n      const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n      const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems)); // If first visible index changed we must update scroll offset to handle start/end buffers\n      // Current range must also be adjusted to cover the new position (bottom of new list).\n\n      if (firstVisibleIndex != newVisibleIndex) {\n        firstVisibleIndex = newVisibleIndex;\n        scrollOffset = newVisibleIndex * this._itemSize;\n        newRange.start = Math.floor(firstVisibleIndex);\n      }\n\n      newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n    }\n\n    const startBuffer = scrollOffset - newRange.start * this._itemSize;\n\n    if (startBuffer < this._minBufferPx && newRange.start != 0) {\n      const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n      newRange.start = Math.max(0, newRange.start - expandStart);\n      newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n    } else {\n      const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n\n      if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n        const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n\n        if (expandEnd > 0) {\n          newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n          newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n        }\n      }\n    }\n\n    this._viewport.setRenderedRange(newRange);\n\n    this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n\n    this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n  }\n\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\n\n\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n  return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\n\n\nlet CdkFixedSizeVirtualScroll = /*#__PURE__*/(() => {\n  class CdkFixedSizeVirtualScroll {\n    constructor() {\n      this._itemSize = 20;\n      this._minBufferPx = 100;\n      this._maxBufferPx = 200;\n      /** The scroll strategy used by this directive. */\n\n      this._scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    /** The size of the items in the list (in pixels). */\n\n\n    get itemSize() {\n      return this._itemSize;\n    }\n\n    set itemSize(value) {\n      this._itemSize = coerceNumberProperty(value);\n    }\n    /**\n     * The minimum amount of buffer rendered beyond the viewport (in pixels).\n     * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n     */\n\n\n    get minBufferPx() {\n      return this._minBufferPx;\n    }\n\n    set minBufferPx(value) {\n      this._minBufferPx = coerceNumberProperty(value);\n    }\n    /**\n     * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n     */\n\n\n    get maxBufferPx() {\n      return this._maxBufferPx;\n    }\n\n    set maxBufferPx(value) {\n      this._maxBufferPx = coerceNumberProperty(value);\n    }\n\n    ngOnChanges() {\n      this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n\n  }\n\n  CdkFixedSizeVirtualScroll.ɵfac = function CdkFixedSizeVirtualScroll_Factory(t) {\n    return new (t || CdkFixedSizeVirtualScroll)();\n  };\n\n  CdkFixedSizeVirtualScroll.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkFixedSizeVirtualScroll,\n    selectors: [[\"cdk-virtual-scroll-viewport\", \"itemSize\", \"\"]],\n    inputs: {\n      itemSize: \"itemSize\",\n      minBufferPx: \"minBufferPx\",\n      maxBufferPx: \"maxBufferPx\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: VIRTUAL_SCROLL_STRATEGY,\n      useFactory: _fixedSizeVirtualScrollStrategyFactory,\n      deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n    }]), i0.ɵɵNgOnChangesFeature]\n  });\n  return CdkFixedSizeVirtualScroll;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Time in ms to throttle the scrolling events by default. */\n\n\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\n\nlet ScrollDispatcher = /*#__PURE__*/(() => {\n  class ScrollDispatcher {\n    constructor(_ngZone, _platform, document) {\n      this._ngZone = _ngZone;\n      this._platform = _platform;\n      /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n\n      this._scrolled = new Subject();\n      /** Keeps track of the global `scroll` and `resize` subscriptions. */\n\n      this._globalSubscription = null;\n      /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n\n      this._scrolledCount = 0;\n      /**\n       * Map of all the scrollable references that are registered with the service and their\n       * scroll event subscriptions.\n       */\n\n      this.scrollContainers = new Map();\n      this._document = document;\n    }\n    /**\n     * Registers a scrollable instance with the service and listens for its scrolled events. When the\n     * scrollable is scrolled, the service emits the event to its scrolled observable.\n     * @param scrollable Scrollable instance to be registered.\n     */\n\n\n    register(scrollable) {\n      if (!this.scrollContainers.has(scrollable)) {\n        this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n      }\n    }\n    /**\n     * Deregisters a Scrollable reference and unsubscribes from its scroll event observable.\n     * @param scrollable Scrollable instance to be deregistered.\n     */\n\n\n    deregister(scrollable) {\n      const scrollableReference = this.scrollContainers.get(scrollable);\n\n      if (scrollableReference) {\n        scrollableReference.unsubscribe();\n        this.scrollContainers.delete(scrollable);\n      }\n    }\n    /**\n     * Returns an observable that emits an event whenever any of the registered Scrollable\n     * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n     * to override the default \"throttle\" time.\n     *\n     * **Note:** in order to avoid hitting change detection for every scroll event,\n     * all of the events emitted from this stream will be run outside the Angular zone.\n     * If you need to update any data bindings as a result of a scroll event, you have\n     * to run the callback using `NgZone.run`.\n     */\n\n\n    scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n      if (!this._platform.isBrowser) {\n        return of();\n      }\n\n      return new Observable(observer => {\n        if (!this._globalSubscription) {\n          this._addGlobalListener();\n        } // In the case of a 0ms delay, use an observable without auditTime\n        // since it does add a perceptible delay in processing overhead.\n\n\n        const subscription = auditTimeInMs > 0 ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer) : this._scrolled.subscribe(observer);\n        this._scrolledCount++;\n        return () => {\n          subscription.unsubscribe();\n          this._scrolledCount--;\n\n          if (!this._scrolledCount) {\n            this._removeGlobalListener();\n          }\n        };\n      });\n    }\n\n    ngOnDestroy() {\n      this._removeGlobalListener();\n\n      this.scrollContainers.forEach((_, container) => this.deregister(container));\n\n      this._scrolled.complete();\n    }\n    /**\n     * Returns an observable that emits whenever any of the\n     * scrollable ancestors of an element are scrolled.\n     * @param elementOrElementRef Element whose ancestors to listen for.\n     * @param auditTimeInMs Time to throttle the scroll events.\n     */\n\n\n    ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n      const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n      return this.scrolled(auditTimeInMs).pipe(filter(target => {\n        return !target || ancestors.indexOf(target) > -1;\n      }));\n    }\n    /** Returns all registered Scrollables that contain the provided element. */\n\n\n    getAncestorScrollContainers(elementOrElementRef) {\n      const scrollingContainers = [];\n      this.scrollContainers.forEach((_subscription, scrollable) => {\n        if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n          scrollingContainers.push(scrollable);\n        }\n      });\n      return scrollingContainers;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n\n\n    _getWindow() {\n      return this._document.defaultView || window;\n    }\n    /** Returns true if the element is contained within the provided Scrollable. */\n\n\n    _scrollableContainsElement(scrollable, elementOrElementRef) {\n      let element = coerceElement(elementOrElementRef);\n      let scrollableElement = scrollable.getElementRef().nativeElement; // Traverse through the element parents until we reach null, checking if any of the elements\n      // are the scrollable's element.\n\n      do {\n        if (element == scrollableElement) {\n          return true;\n        }\n      } while (element = element.parentElement);\n\n      return false;\n    }\n    /** Sets up the global scroll listeners. */\n\n\n    _addGlobalListener() {\n      this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n\n        return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n      });\n    }\n    /** Cleans up the global scroll listener. */\n\n\n    _removeGlobalListener() {\n      if (this._globalSubscription) {\n        this._globalSubscription.unsubscribe();\n\n        this._globalSubscription = null;\n      }\n    }\n\n  }\n\n  ScrollDispatcher.ɵfac = function ScrollDispatcher_Factory(t) {\n    return new (t || ScrollDispatcher)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Platform), i0.ɵɵinject(DOCUMENT, 8));\n  };\n\n  ScrollDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ScrollDispatcher,\n    factory: ScrollDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n  return ScrollDispatcher;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\n\n\nlet CdkScrollable = /*#__PURE__*/(() => {\n  class CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n      this.elementRef = elementRef;\n      this.scrollDispatcher = scrollDispatcher;\n      this.ngZone = ngZone;\n      this.dir = dir;\n      this._destroyed = new Subject();\n      this._elementScrolled = new Observable(observer => this.ngZone.runOutsideAngular(() => fromEvent(this.elementRef.nativeElement, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n    }\n\n    ngOnInit() {\n      this.scrollDispatcher.register(this);\n    }\n\n    ngOnDestroy() {\n      this.scrollDispatcher.deregister(this);\n\n      this._destroyed.next();\n\n      this._destroyed.complete();\n    }\n    /** Returns observable that emits when a scroll event is fired on the host element. */\n\n\n    elementScrolled() {\n      return this._elementScrolled;\n    }\n    /** Gets the ElementRef for the viewport. */\n\n\n    getElementRef() {\n      return this.elementRef;\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param options specified the offsets to scroll to.\n     */\n\n\n    scrollTo(options) {\n      const el = this.elementRef.nativeElement;\n      const isRtl = this.dir && this.dir.value == 'rtl'; // Rewrite start & end offsets as right or left offsets.\n\n      if (options.left == null) {\n        options.left = isRtl ? options.end : options.start;\n      }\n\n      if (options.right == null) {\n        options.right = isRtl ? options.start : options.end;\n      } // Rewrite the bottom offset as a top offset.\n\n\n      if (options.bottom != null) {\n        options.top = el.scrollHeight - el.clientHeight - options.bottom;\n      } // Rewrite the right offset as a left offset.\n\n\n      if (isRtl && getRtlScrollAxisType() != 0\n      /* NORMAL */\n      ) {\n        if (options.left != null) {\n          options.right = el.scrollWidth - el.clientWidth - options.left;\n        }\n\n        if (getRtlScrollAxisType() == 2\n        /* INVERTED */\n        ) {\n          options.left = options.right;\n        } else if (getRtlScrollAxisType() == 1\n        /* NEGATED */\n        ) {\n          options.left = options.right ? -options.right : options.right;\n        }\n      } else {\n        if (options.right != null) {\n          options.left = el.scrollWidth - el.clientWidth - options.right;\n        }\n      }\n\n      this._applyScrollToOptions(options);\n    }\n\n    _applyScrollToOptions(options) {\n      const el = this.elementRef.nativeElement;\n\n      if (supportsScrollBehavior()) {\n        el.scrollTo(options);\n      } else {\n        if (options.top != null) {\n          el.scrollTop = options.top;\n        }\n\n        if (options.left != null) {\n          el.scrollLeft = options.left;\n        }\n      }\n    }\n    /**\n     * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n     * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n     * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param from The edge to measure from.\n     */\n\n\n    measureScrollOffset(from) {\n      const LEFT = 'left';\n      const RIGHT = 'right';\n      const el = this.elementRef.nativeElement;\n\n      if (from == 'top') {\n        return el.scrollTop;\n      }\n\n      if (from == 'bottom') {\n        return el.scrollHeight - el.clientHeight - el.scrollTop;\n      } // Rewrite start & end as left or right offsets.\n\n\n      const isRtl = this.dir && this.dir.value == 'rtl';\n\n      if (from == 'start') {\n        from = isRtl ? RIGHT : LEFT;\n      } else if (from == 'end') {\n        from = isRtl ? LEFT : RIGHT;\n      }\n\n      if (isRtl && getRtlScrollAxisType() == 2\n      /* INVERTED */\n      ) {\n        // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n        // 0 when scrolled all the way right.\n        if (from == LEFT) {\n          return el.scrollWidth - el.clientWidth - el.scrollLeft;\n        } else {\n          return el.scrollLeft;\n        }\n      } else if (isRtl && getRtlScrollAxisType() == 1\n      /* NEGATED */\n      ) {\n        // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n        // 0 when scrolled all the way right.\n        if (from == LEFT) {\n          return el.scrollLeft + el.scrollWidth - el.clientWidth;\n        } else {\n          return -el.scrollLeft;\n        }\n      } else {\n        // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n        // (scrollWidth - clientWidth) when scrolled all the way right.\n        if (from == LEFT) {\n          return el.scrollLeft;\n        } else {\n          return el.scrollWidth - el.clientWidth - el.scrollLeft;\n        }\n      }\n    }\n\n  }\n\n  CdkScrollable.ɵfac = function CdkScrollable_Factory(t) {\n    return new (t || CdkScrollable)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n  };\n\n  CdkScrollable.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkScrollable,\n    selectors: [[\"\", \"cdk-scrollable\", \"\"], [\"\", \"cdkScrollable\", \"\"]]\n  });\n  return CdkScrollable;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Time in ms to throttle the resize events by default. */\n\n\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\n\nlet ViewportRuler = /*#__PURE__*/(() => {\n  class ViewportRuler {\n    constructor(_platform, ngZone, document) {\n      this._platform = _platform;\n      /** Stream of viewport change events. */\n\n      this._change = new Subject();\n      /** Event listener that will be used to handle the viewport change events. */\n\n      this._changeListener = event => {\n        this._change.next(event);\n      };\n\n      this._document = document;\n      ngZone.runOutsideAngular(() => {\n        if (_platform.isBrowser) {\n          const window = this._getWindow(); // Note that bind the events ourselves, rather than going through something like RxJS's\n          // `fromEvent` so that we can ensure that they're bound outside of the NgZone.\n\n\n          window.addEventListener('resize', this._changeListener);\n          window.addEventListener('orientationchange', this._changeListener);\n        } // Clear the cached position so that the viewport is re-measured next time it is required.\n        // We don't need to keep track of the subscription, because it is completed on destroy.\n\n\n        this.change().subscribe(() => this._viewportSize = null);\n      });\n    }\n\n    ngOnDestroy() {\n      if (this._platform.isBrowser) {\n        const window = this._getWindow();\n\n        window.removeEventListener('resize', this._changeListener);\n        window.removeEventListener('orientationchange', this._changeListener);\n      }\n\n      this._change.complete();\n    }\n    /** Returns the viewport's width and height. */\n\n\n    getViewportSize() {\n      if (!this._viewportSize) {\n        this._updateViewportSize();\n      }\n\n      const output = {\n        width: this._viewportSize.width,\n        height: this._viewportSize.height\n      }; // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n\n      if (!this._platform.isBrowser) {\n        this._viewportSize = null;\n      }\n\n      return output;\n    }\n    /** Gets a ClientRect for the viewport's bounds. */\n\n\n    getViewportRect() {\n      // Use the document element's bounding rect rather than the window scroll properties\n      // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n      // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n      // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n      // can disagree when the page is pinch-zoomed (on devices that support touch).\n      // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n      // We use the documentElement instead of the body because, by default (without a css reset)\n      // browsers typically give the document body an 8px margin, which is not included in\n      // getBoundingClientRect().\n      const scrollPosition = this.getViewportScrollPosition();\n      const {\n        width,\n        height\n      } = this.getViewportSize();\n      return {\n        top: scrollPosition.top,\n        left: scrollPosition.left,\n        bottom: scrollPosition.top + height,\n        right: scrollPosition.left + width,\n        height,\n        width\n      };\n    }\n    /** Gets the (top, left) scroll position of the viewport. */\n\n\n    getViewportScrollPosition() {\n      // While we can get a reference to the fake document\n      // during SSR, it doesn't have getBoundingClientRect.\n      if (!this._platform.isBrowser) {\n        return {\n          top: 0,\n          left: 0\n        };\n      } // The top-left-corner of the viewport is determined by the scroll position of the document\n      // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n      // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n      // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n      // `document.documentElement` works consistently, where the `top` and `left` values will\n      // equal negative the scroll position.\n\n\n      const document = this._document;\n\n      const window = this._getWindow();\n\n      const documentElement = document.documentElement;\n      const documentRect = documentElement.getBoundingClientRect();\n      const top = -documentRect.top || document.body.scrollTop || window.scrollY || documentElement.scrollTop || 0;\n      const left = -documentRect.left || document.body.scrollLeft || window.scrollX || documentElement.scrollLeft || 0;\n      return {\n        top,\n        left\n      };\n    }\n    /**\n     * Returns a stream that emits whenever the size of the viewport changes.\n     * This stream emits outside of the Angular zone.\n     * @param throttleTime Time in milliseconds to throttle the stream.\n     */\n\n\n    change(throttleTime = DEFAULT_RESIZE_TIME) {\n      return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n\n\n    _getWindow() {\n      return this._document.defaultView || window;\n    }\n    /** Updates the cached viewport size. */\n\n\n    _updateViewportSize() {\n      const window = this._getWindow();\n\n      this._viewportSize = this._platform.isBrowser ? {\n        width: window.innerWidth,\n        height: window.innerHeight\n      } : {\n        width: 0,\n        height: 0\n      };\n    }\n\n  }\n\n  ViewportRuler.ɵfac = function ViewportRuler_Factory(t) {\n    return new (t || ViewportRuler)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT, 8));\n  };\n\n  ViewportRuler.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ViewportRuler,\n    factory: ViewportRuler.ɵfac,\n    providedIn: 'root'\n  });\n  return ViewportRuler;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Checks if the given ranges are equal. */\n\n\nfunction rangesEqual(r1, r2) {\n  return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\n\n\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\n\nlet CdkVirtualScrollViewport = /*#__PURE__*/(() => {\n  class CdkVirtualScrollViewport extends CdkScrollable {\n    constructor(elementRef, _changeDetectorRef, ngZone, _scrollStrategy, dir, scrollDispatcher, viewportRuler) {\n      super(elementRef, scrollDispatcher, ngZone, dir);\n      this.elementRef = elementRef;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._scrollStrategy = _scrollStrategy;\n      /** Emits when the viewport is detached from a CdkVirtualForOf. */\n\n      this._detachedSubject = new Subject();\n      /** Emits when the rendered range changes. */\n\n      this._renderedRangeSubject = new Subject();\n      this._orientation = 'vertical';\n      this._appendOnly = false; // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n      // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n      // depending on how the strategy calculates the scrolled index, it may come at a cost to\n      // performance.\n\n      /** Emits when the index of the first element visible in the viewport changes. */\n\n      this.scrolledIndexChange = new Observable(observer => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n      /** A stream that emits whenever the rendered range changes. */\n\n      this.renderedRangeStream = this._renderedRangeSubject;\n      /**\n       * The total size of all content (in pixels), including content that is not currently rendered.\n       */\n\n      this._totalContentSize = 0;\n      /** A string representing the `style.width` property value to be used for the spacer element. */\n\n      this._totalContentWidth = '';\n      /** A string representing the `style.height` property value to be used for the spacer element. */\n\n      this._totalContentHeight = '';\n      /** The currently rendered range of indices. */\n\n      this._renderedRange = {\n        start: 0,\n        end: 0\n      };\n      /** The length of the data bound to this viewport (in number of items). */\n\n      this._dataLength = 0;\n      /** The size of the viewport (in pixels). */\n\n      this._viewportSize = 0;\n      /** The last rendered content offset that was set. */\n\n      this._renderedContentOffset = 0;\n      /**\n       * Whether the last rendered content offset was to the end of the content (and therefore needs to\n       * be rewritten as an offset to the start of the content).\n       */\n\n      this._renderedContentOffsetNeedsRewrite = false;\n      /** Whether there is a pending change detection cycle. */\n\n      this._isChangeDetectionPending = false;\n      /** A list of functions to run after the next change detection cycle. */\n\n      this._runAfterChangeDetection = [];\n      /** Subscription to changes in the viewport size. */\n\n      this._viewportChanges = Subscription.EMPTY;\n\n      if (!_scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n      }\n\n      this._viewportChanges = viewportRuler.change().subscribe(() => {\n        this.checkViewportSize();\n      });\n    }\n    /** The direction the viewport scrolls. */\n\n\n    get orientation() {\n      return this._orientation;\n    }\n\n    set orientation(orientation) {\n      if (this._orientation !== orientation) {\n        this._orientation = orientation;\n\n        this._calculateSpacerSize();\n      }\n    }\n    /**\n     * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n     * will be removed.\n     */\n\n\n    get appendOnly() {\n      return this._appendOnly;\n    }\n\n    set appendOnly(value) {\n      this._appendOnly = coerceBooleanProperty(value);\n    }\n\n    ngOnInit() {\n      super.ngOnInit(); // It's still too early to measure the viewport at this point. Deferring with a promise allows\n      // the Viewport to be rendered with the correct size before we measure. We run this outside the\n      // zone to avoid causing more change detection cycles. We handle the change detection loop\n      // ourselves instead.\n\n      this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n        this._measureViewportSize();\n\n        this._scrollStrategy.attach(this);\n\n        this.elementScrolled().pipe( // Start off with a fake scroll event so we properly detect our initial position.\n        startWith(null), // Collect multiple events into one until the next animation frame. This way if\n        // there are multiple scroll events in the same frame we only need to recheck\n        // our layout once.\n        auditTime(0, SCROLL_SCHEDULER)).subscribe(() => this._scrollStrategy.onContentScrolled());\n\n        this._markChangeDetectionNeeded();\n      }));\n    }\n\n    ngOnDestroy() {\n      this.detach();\n\n      this._scrollStrategy.detach(); // Complete all subjects\n\n\n      this._renderedRangeSubject.complete();\n\n      this._detachedSubject.complete();\n\n      this._viewportChanges.unsubscribe();\n\n      super.ngOnDestroy();\n    }\n    /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n\n\n    attach(forOf) {\n      if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('CdkVirtualScrollViewport is already attached.');\n      } // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n      // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n      // change detection loop ourselves.\n\n\n      this.ngZone.runOutsideAngular(() => {\n        this._forOf = forOf;\n\n        this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n          const newLength = data.length;\n\n          if (newLength !== this._dataLength) {\n            this._dataLength = newLength;\n\n            this._scrollStrategy.onDataLengthChanged();\n          }\n\n          this._doChangeDetection();\n        });\n      });\n    }\n    /** Detaches the current `CdkVirtualForOf`. */\n\n\n    detach() {\n      this._forOf = null;\n\n      this._detachedSubject.next();\n    }\n    /** Gets the length of the data bound to this viewport (in number of items). */\n\n\n    getDataLength() {\n      return this._dataLength;\n    }\n    /** Gets the size of the viewport (in pixels). */\n\n\n    getViewportSize() {\n      return this._viewportSize;\n    } // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n    // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n    // setting it to something else, but its error prone and should probably be split into\n    // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n\n    /** Get the current rendered range of items. */\n\n\n    getRenderedRange() {\n      return this._renderedRange;\n    }\n    /**\n     * Sets the total size of all content (in pixels), including content that is not currently\n     * rendered.\n     */\n\n\n    setTotalContentSize(size) {\n      if (this._totalContentSize !== size) {\n        this._totalContentSize = size;\n\n        this._calculateSpacerSize();\n\n        this._markChangeDetectionNeeded();\n      }\n    }\n    /** Sets the currently rendered range of indices. */\n\n\n    setRenderedRange(range) {\n      if (!rangesEqual(this._renderedRange, range)) {\n        if (this.appendOnly) {\n          range = {\n            start: 0,\n            end: Math.max(this._renderedRange.end, range.end)\n          };\n        }\n\n        this._renderedRangeSubject.next(this._renderedRange = range);\n\n        this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n      }\n    }\n    /**\n     * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n     */\n\n\n    getOffsetToRenderedContentStart() {\n      return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n    }\n    /**\n     * Sets the offset from the start of the viewport to either the start or end of the rendered data\n     * (in pixels).\n     */\n\n\n    setRenderedContentOffset(offset, to = 'to-start') {\n      // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n      // in the negative direction.\n      const isRtl = this.dir && this.dir.value == 'rtl';\n      const isHorizontal = this.orientation == 'horizontal';\n      const axis = isHorizontal ? 'X' : 'Y';\n      const axisDirection = isHorizontal && isRtl ? -1 : 1;\n      let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n      this._renderedContentOffset = offset;\n\n      if (to === 'to-end') {\n        transform += ` translate${axis}(-100%)`; // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n        // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n        // expand upward).\n\n        this._renderedContentOffsetNeedsRewrite = true;\n      }\n\n      if (this._renderedContentTransform != transform) {\n        // We know this value is safe because we parse `offset` with `Number()` before passing it\n        // into the string.\n        this._renderedContentTransform = transform;\n\n        this._markChangeDetectionNeeded(() => {\n          if (this._renderedContentOffsetNeedsRewrite) {\n            this._renderedContentOffset -= this.measureRenderedContentSize();\n            this._renderedContentOffsetNeedsRewrite = false;\n            this.setRenderedContentOffset(this._renderedContentOffset);\n          } else {\n            this._scrollStrategy.onRenderedOffsetChanged();\n          }\n        });\n      }\n    }\n    /**\n     * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n     * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n     * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n     * @param offset The offset to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n\n\n    scrollToOffset(offset, behavior = 'auto') {\n      const options = {\n        behavior\n      };\n\n      if (this.orientation === 'horizontal') {\n        options.start = offset;\n      } else {\n        options.top = offset;\n      }\n\n      this.scrollTo(options);\n    }\n    /**\n     * Scrolls to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n\n\n    scrollToIndex(index, behavior = 'auto') {\n      this._scrollStrategy.scrollToIndex(index, behavior);\n    }\n    /**\n     * Gets the current scroll offset from the start of the viewport (in pixels).\n     * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n     *     in horizontal mode.\n     */\n\n\n    measureScrollOffset(from) {\n      return from ? super.measureScrollOffset(from) : super.measureScrollOffset(this.orientation === 'horizontal' ? 'start' : 'top');\n    }\n    /** Measure the combined size of all of the rendered items. */\n\n\n    measureRenderedContentSize() {\n      const contentEl = this._contentWrapper.nativeElement;\n      return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n    }\n    /**\n     * Measure the total combined size of the given range. Throws if the range includes items that are\n     * not rendered.\n     */\n\n\n    measureRangeSize(range) {\n      if (!this._forOf) {\n        return 0;\n      }\n\n      return this._forOf.measureRangeSize(range, this.orientation);\n    }\n    /** Update the viewport dimensions and re-render. */\n\n\n    checkViewportSize() {\n      // TODO: Cleanup later when add logic for handling content resize\n      this._measureViewportSize();\n\n      this._scrollStrategy.onDataLengthChanged();\n    }\n    /** Measure the viewport size. */\n\n\n    _measureViewportSize() {\n      const viewportEl = this.elementRef.nativeElement;\n      this._viewportSize = this.orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n    }\n    /** Queue up change detection to run. */\n\n\n    _markChangeDetectionNeeded(runAfter) {\n      if (runAfter) {\n        this._runAfterChangeDetection.push(runAfter);\n      } // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n      // properties sequentially we only have to run `_doChangeDetection` once at the end.\n\n\n      if (!this._isChangeDetectionPending) {\n        this._isChangeDetectionPending = true;\n        this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n          this._doChangeDetection();\n        }));\n      }\n    }\n    /** Run change detection. */\n\n\n    _doChangeDetection() {\n      this._isChangeDetectionPending = false; // Apply the content transform. The transform can't be set via an Angular binding because\n      // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n      // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n      // the `Number` function first to coerce it to a numeric value.\n\n      this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform; // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n      // from the root, since the repeated items are content projected in. Calling `detectChanges`\n      // instead does not properly check the projected content.\n\n      this.ngZone.run(() => this._changeDetectorRef.markForCheck());\n      const runAfterChangeDetection = this._runAfterChangeDetection;\n      this._runAfterChangeDetection = [];\n\n      for (const fn of runAfterChangeDetection) {\n        fn();\n      }\n    }\n    /** Calculates the `style.width` and `style.height` for the spacer element. */\n\n\n    _calculateSpacerSize() {\n      this._totalContentHeight = this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n      this._totalContentWidth = this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n    }\n\n  }\n\n  CdkVirtualScrollViewport.ɵfac = function CdkVirtualScrollViewport_Factory(t) {\n    return new (t || CdkVirtualScrollViewport)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(VIRTUAL_SCROLL_STRATEGY, 8), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(ViewportRuler));\n  };\n\n  CdkVirtualScrollViewport.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkVirtualScrollViewport,\n    selectors: [[\"cdk-virtual-scroll-viewport\"]],\n    viewQuery: function CdkVirtualScrollViewport_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentWrapper = _t.first);\n      }\n    },\n    hostAttrs: [1, \"cdk-virtual-scroll-viewport\"],\n    hostVars: 4,\n    hostBindings: function CdkVirtualScrollViewport_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"cdk-virtual-scroll-orientation-horizontal\", ctx.orientation === \"horizontal\")(\"cdk-virtual-scroll-orientation-vertical\", ctx.orientation !== \"horizontal\");\n      }\n    },\n    inputs: {\n      orientation: \"orientation\",\n      appendOnly: \"appendOnly\"\n    },\n    outputs: {\n      scrolledIndexChange: \"scrolledIndexChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkScrollable,\n      useExisting: CdkVirtualScrollViewport\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c1,\n    decls: 4,\n    vars: 4,\n    consts: [[1, \"cdk-virtual-scroll-content-wrapper\"], [\"contentWrapper\", \"\"], [1, \"cdk-virtual-scroll-spacer\"]],\n    template: function CdkVirtualScrollViewport_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0, 1);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(3, \"div\", 2);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵstyleProp(\"width\", ctx._totalContentWidth)(\"height\", ctx._totalContentHeight);\n      }\n    },\n    styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0}[dir=rtl] .cdk-virtual-scroll-spacer{right:0;left:auto;transform-origin:100% 0}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return CdkVirtualScrollViewport;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\n\n\nfunction getOffset(orientation, direction, node) {\n  const el = node;\n\n  if (!el.getBoundingClientRect) {\n    return 0;\n  }\n\n  const rect = el.getBoundingClientRect();\n\n  if (orientation === 'horizontal') {\n    return direction === 'start' ? rect.left : rect.right;\n  }\n\n  return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\n\n\nlet CdkVirtualForOf = /*#__PURE__*/(() => {\n  class CdkVirtualForOf {\n    constructor(\n    /** The view container to add items to. */\n    _viewContainerRef,\n    /** The template to use when stamping out new items. */\n    _template,\n    /** The set of available differs. */\n    _differs,\n    /** The strategy used to render items in the virtual scroll viewport. */\n    _viewRepeater,\n    /** The virtual scrolling viewport that these items are being rendered in. */\n    _viewport, ngZone) {\n      this._viewContainerRef = _viewContainerRef;\n      this._template = _template;\n      this._differs = _differs;\n      this._viewRepeater = _viewRepeater;\n      this._viewport = _viewport;\n      /** Emits when the rendered view of the data changes. */\n\n      this.viewChange = new Subject();\n      /** Subject that emits when a new DataSource instance is given. */\n\n      this._dataSourceChanges = new Subject();\n      /** Emits whenever the data in the current DataSource changes. */\n\n      this.dataStream = this._dataSourceChanges.pipe( // Start off with null `DataSource`.\n      startWith(null), // Bundle up the previous and current data sources so we can work with both.\n      pairwise(), // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n      // new one, passing back a stream of data changes which we run through `switchMap` to give\n      // us a data stream that emits the latest data from whatever the current `DataSource` is.\n      switchMap(([prev, cur]) => this._changeDataSource(prev, cur)), // Replay the last emitted data when someone subscribes.\n      shareReplay(1));\n      /** The differ used to calculate changes to the data. */\n\n      this._differ = null;\n      /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n\n      this._needsUpdate = false;\n      this._destroyed = new Subject();\n      this.dataStream.subscribe(data => {\n        this._data = data;\n\n        this._onRenderedDataChange();\n      });\n\n      this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n        this._renderedRange = range;\n        ngZone.run(() => this.viewChange.next(this._renderedRange));\n\n        this._onRenderedDataChange();\n      });\n\n      this._viewport.attach(this);\n    }\n    /** The DataSource to display. */\n\n\n    get cdkVirtualForOf() {\n      return this._cdkVirtualForOf;\n    }\n\n    set cdkVirtualForOf(value) {\n      this._cdkVirtualForOf = value;\n\n      if (isDataSource(value)) {\n        this._dataSourceChanges.next(value);\n      } else {\n        // If value is an an NgIterable, convert it to an array.\n        this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n      }\n    }\n    /**\n     * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n     * the item and produces a value to be used as the item's identity when tracking changes.\n     */\n\n\n    get cdkVirtualForTrackBy() {\n      return this._cdkVirtualForTrackBy;\n    }\n\n    set cdkVirtualForTrackBy(fn) {\n      this._needsUpdate = true;\n      this._cdkVirtualForTrackBy = fn ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item) : undefined;\n    }\n    /** The template used to stamp out new elements. */\n\n\n    set cdkVirtualForTemplate(value) {\n      if (value) {\n        this._needsUpdate = true;\n        this._template = value;\n      }\n    }\n    /**\n     * The size of the cache used to store templates that are not being used for re-use later.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n     */\n\n\n    get cdkVirtualForTemplateCacheSize() {\n      return this._viewRepeater.viewCacheSize;\n    }\n\n    set cdkVirtualForTemplateCacheSize(size) {\n      this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n    }\n    /**\n     * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n     * in the specified range. Throws an error if the range includes items that are not currently\n     * rendered.\n     */\n\n\n    measureRangeSize(range, orientation) {\n      if (range.start >= range.end) {\n        return 0;\n      }\n\n      if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(`Error: attempted to measure an item that isn't rendered.`);\n      } // The index into the list of rendered views for the first item in the range.\n\n\n      const renderedStartIndex = range.start - this._renderedRange.start; // The length of the range we're measuring.\n\n      const rangeLen = range.end - range.start; // Loop over all the views, find the first and land node and compute the size by subtracting\n      // the top of the first node from the bottom of the last one.\n\n      let firstNode;\n      let lastNode; // Find the first node by starting from the beginning and going forwards.\n\n      for (let i = 0; i < rangeLen; i++) {\n        const view = this._viewContainerRef.get(i + renderedStartIndex);\n\n        if (view && view.rootNodes.length) {\n          firstNode = lastNode = view.rootNodes[0];\n          break;\n        }\n      } // Find the last node by starting from the end and going backwards.\n\n\n      for (let i = rangeLen - 1; i > -1; i--) {\n        const view = this._viewContainerRef.get(i + renderedStartIndex);\n\n        if (view && view.rootNodes.length) {\n          lastNode = view.rootNodes[view.rootNodes.length - 1];\n          break;\n        }\n      }\n\n      return firstNode && lastNode ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode) : 0;\n    }\n\n    ngDoCheck() {\n      if (this._differ && this._needsUpdate) {\n        // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n        // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n        // changing (need to do this diff).\n        const changes = this._differ.diff(this._renderedItems);\n\n        if (!changes) {\n          this._updateContext();\n        } else {\n          this._applyChanges(changes);\n        }\n\n        this._needsUpdate = false;\n      }\n    }\n\n    ngOnDestroy() {\n      this._viewport.detach();\n\n      this._dataSourceChanges.next(undefined);\n\n      this._dataSourceChanges.complete();\n\n      this.viewChange.complete();\n\n      this._destroyed.next();\n\n      this._destroyed.complete();\n\n      this._viewRepeater.detach();\n    }\n    /** React to scroll state changes in the viewport. */\n\n\n    _onRenderedDataChange() {\n      if (!this._renderedRange) {\n        return;\n      }\n\n      this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n\n      if (!this._differ) {\n        // Use a wrapper function for the `trackBy` so any new values are\n        // picked up automatically without having to recreate the differ.\n        this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n          return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n        });\n      }\n\n      this._needsUpdate = true;\n    }\n    /** Swap out one `DataSource` for another. */\n\n\n    _changeDataSource(oldDs, newDs) {\n      if (oldDs) {\n        oldDs.disconnect(this);\n      }\n\n      this._needsUpdate = true;\n      return newDs ? newDs.connect(this) : of();\n    }\n    /** Update the `CdkVirtualForOfContext` for all views. */\n\n\n    _updateContext() {\n      const count = this._data.length;\n      let i = this._viewContainerRef.length;\n\n      while (i--) {\n        const view = this._viewContainerRef.get(i);\n\n        view.context.index = this._renderedRange.start + i;\n        view.context.count = count;\n\n        this._updateComputedContextProperties(view.context);\n\n        view.detectChanges();\n      }\n    }\n    /** Apply changes to the DOM. */\n\n\n    _applyChanges(changes) {\n      this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item); // Update $implicit for any items that had an identity change.\n\n\n      changes.forEachIdentityChange(record => {\n        const view = this._viewContainerRef.get(record.currentIndex);\n\n        view.context.$implicit = record.item;\n      }); // Update the context variables on all items.\n\n      const count = this._data.length;\n      let i = this._viewContainerRef.length;\n\n      while (i--) {\n        const view = this._viewContainerRef.get(i);\n\n        view.context.index = this._renderedRange.start + i;\n        view.context.count = count;\n\n        this._updateComputedContextProperties(view.context);\n      }\n    }\n    /** Update the computed properties on the `CdkVirtualForOfContext`. */\n\n\n    _updateComputedContextProperties(context) {\n      context.first = context.index === 0;\n      context.last = context.index === context.count - 1;\n      context.even = context.index % 2 === 0;\n      context.odd = !context.even;\n    }\n\n    _getEmbeddedViewArgs(record, index) {\n      // Note that it's important that we insert the item directly at the proper index,\n      // rather than inserting it and the moving it in place, because if there's a directive\n      // on the same node that injects the `ViewContainerRef`, Angular will insert another\n      // comment node which can throw off the move when it's being repeated for all items.\n      return {\n        templateRef: this._template,\n        context: {\n          $implicit: record.item,\n          // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n          // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n          cdkVirtualForOf: this._cdkVirtualForOf,\n          index: -1,\n          count: -1,\n          first: false,\n          last: false,\n          odd: false,\n          even: false\n        },\n        index\n      };\n    }\n\n  }\n\n  CdkVirtualForOf.ɵfac = function CdkVirtualForOf_Factory(t) {\n    return new (t || CdkVirtualForOf)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(_VIEW_REPEATER_STRATEGY), i0.ɵɵdirectiveInject(CdkVirtualScrollViewport, 4), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n\n  CdkVirtualForOf.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkVirtualForOf,\n    selectors: [[\"\", \"cdkVirtualFor\", \"\", \"cdkVirtualForOf\", \"\"]],\n    inputs: {\n      cdkVirtualForOf: \"cdkVirtualForOf\",\n      cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\",\n      cdkVirtualForTemplate: \"cdkVirtualForTemplate\",\n      cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: _VIEW_REPEATER_STRATEGY,\n      useClass: _RecycleViewRepeaterStrategy\n    }])]\n  });\n  return CdkVirtualForOf;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet CdkScrollableModule = /*#__PURE__*/(() => {\n  class CdkScrollableModule {}\n\n  CdkScrollableModule.ɵfac = function CdkScrollableModule_Factory(t) {\n    return new (t || CdkScrollableModule)();\n  };\n\n  CdkScrollableModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CdkScrollableModule\n  });\n  CdkScrollableModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return CdkScrollableModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @docs-primary-export\n */\n\n\nlet ScrollingModule = /*#__PURE__*/(() => {\n  class ScrollingModule {}\n\n  ScrollingModule.ɵfac = function ScrollingModule_Factory(t) {\n    return new (t || ScrollingModule)();\n  };\n\n  ScrollingModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ScrollingModule\n  });\n  ScrollingModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[BidiModule, PlatformModule, CdkScrollableModule], BidiModule, CdkScrollableModule]\n  });\n  return ScrollingModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory }; //# sourceMappingURL=scrolling.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}