{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { CourseDetailsRoutes } from './course-details.routing';\nimport { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let CourseDetailsModule = /*#__PURE__*/(() => {\n  class CourseDetailsModule {}\n\n  CourseDetailsModule.ɵfac = function CourseDetailsModule_Factory(t) {\n    return new (t || CourseDetailsModule)();\n  };\n\n  CourseDetailsModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CourseDetailsModule\n  });\n  CourseDetailsModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(CourseDetailsRoutes), SharedModule, WebLayoutModule, NgxExtendedPdfViewerModule]]\n  });\n  return CourseDetailsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}