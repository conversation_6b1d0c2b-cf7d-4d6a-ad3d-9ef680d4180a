{"ast": null, "code": "import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function repeat(count = Infinity) {\n  return count <= 0 ? () => EMPTY : operate((source, subscriber) => {\n    let soFar = 0;\n    let innerSub;\n\n    const subscribeForRepeat = () => {\n      let syncUnsub = false;\n      innerSub = source.subscribe(new OperatorSubscriber(subscriber, undefined, () => {\n        if (++soFar < count) {\n          if (innerSub) {\n            innerSub.unsubscribe();\n            innerSub = null;\n            subscribeForRepeat();\n          } else {\n            syncUnsub = true;\n          }\n        } else {\n          subscriber.complete();\n        }\n      }));\n\n      if (syncUnsub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        subscribeForRepeat();\n      }\n    };\n\n    subscribeForRepeat();\n  });\n} //# sourceMappingURL=repeat.js.map", "map": null, "metadata": {}, "sourceType": "module"}