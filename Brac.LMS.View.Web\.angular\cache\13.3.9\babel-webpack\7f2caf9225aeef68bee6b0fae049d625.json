{"ast": null, "code": "import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Directive, EventEmitter, Input, NgModule, Output, Pipe, ViewEncapsulation } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/common';\n\nfunction PaginationControlsComponent_ul_2_li_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    var _r8 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"a\", 11);\n    ɵngcc0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_2_li_1_a_1_Template_a_keyup_enter_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r8);\n      ɵngcc0.ɵɵnextContext(3);\n\n      var _r0 = ɵngcc0.ɵɵreference(1);\n\n      return _r0.previous();\n    })(\"click\", function PaginationControlsComponent_ul_2_li_1_a_1_Template_a_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r8);\n      ɵngcc0.ɵɵnextContext(3);\n\n      var _r0 = ɵngcc0.ɵɵreference(1);\n\n      return _r0.previous();\n    });\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementStart(2, \"span\", 12);\n    ɵngcc0.ɵɵtext(3);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    var ctx_r5 = ɵngcc0.ɵɵnextContext(3);\n    ɵngcc0.ɵɵattribute(\"aria-label\", ctx_r5.previousLabel + \" \" + ctx_r5.screenReaderPageLabel);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r5.previousLabel, \" \");\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r5.screenReaderPageLabel);\n  }\n}\n\nfunction PaginationControlsComponent_ul_2_li_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"span\");\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementStart(2, \"span\", 12);\n    ɵngcc0.ɵɵtext(3);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    var ctx_r6 = ɵngcc0.ɵɵnextContext(3);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r6.previousLabel, \" \");\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r6.screenReaderPageLabel);\n  }\n}\n\nfunction PaginationControlsComponent_ul_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"li\", 8);\n    ɵngcc0.ɵɵtemplate(1, PaginationControlsComponent_ul_2_li_1_a_1_Template, 4, 3, \"a\", 9);\n    ɵngcc0.ɵɵtemplate(2, PaginationControlsComponent_ul_2_li_1_span_2_Template, 4, 2, \"span\", 10);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    ɵngcc0.ɵɵnextContext(2);\n\n    var _r0 = ɵngcc0.ɵɵreference(1);\n\n    ɵngcc0.ɵɵclassProp(\"disabled\", _r0.isFirstPage());\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", 1 < _r0.getCurrent());\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", _r0.isFirstPage());\n  }\n}\n\nfunction PaginationControlsComponent_ul_2_li_4_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    var _r15 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"a\", 11);\n    ɵngcc0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_2_li_4_a_1_Template_a_keyup_enter_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r15);\n      var page_r10 = ɵngcc0.ɵɵnextContext().$implicit;\n      ɵngcc0.ɵɵnextContext(2);\n\n      var _r0 = ɵngcc0.ɵɵreference(1);\n\n      return _r0.setCurrent(page_r10.value);\n    })(\"click\", function PaginationControlsComponent_ul_2_li_4_a_1_Template_a_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r15);\n      var page_r10 = ɵngcc0.ɵɵnextContext().$implicit;\n      ɵngcc0.ɵɵnextContext(2);\n\n      var _r0 = ɵngcc0.ɵɵreference(1);\n\n      return _r0.setCurrent(page_r10.value);\n    });\n    ɵngcc0.ɵɵelementStart(1, \"span\", 12);\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(3, \"span\");\n    ɵngcc0.ɵɵtext(4);\n    ɵngcc0.ɵɵpipe(5, \"number\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    var page_r10 = ɵngcc0.ɵɵnextContext().$implicit;\n    var ctx_r11 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate1(\"\", ctx_r11.screenReaderPageLabel, \" \");\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(page_r10.label === \"...\" ? page_r10.label : ɵngcc0.ɵɵpipeBind2(5, 2, page_r10.label, \"\"));\n  }\n}\n\nfunction PaginationControlsComponent_ul_2_li_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementContainerStart(0);\n    ɵngcc0.ɵɵelementStart(1, \"span\", 12);\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(3, \"span\");\n    ɵngcc0.ɵɵtext(4);\n    ɵngcc0.ɵɵpipe(5, \"number\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    var page_r10 = ɵngcc0.ɵɵnextContext().$implicit;\n    var ctx_r12 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate1(\"\", ctx_r12.screenReaderCurrentLabel, \" \");\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(page_r10.label === \"...\" ? page_r10.label : ɵngcc0.ɵɵpipeBind2(5, 2, page_r10.label, \"\"));\n  }\n}\n\nfunction PaginationControlsComponent_ul_2_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"li\");\n    ɵngcc0.ɵɵtemplate(1, PaginationControlsComponent_ul_2_li_4_a_1_Template, 6, 5, \"a\", 9);\n    ɵngcc0.ɵɵtemplate(2, PaginationControlsComponent_ul_2_li_4_ng_container_2_Template, 6, 5, \"ng-container\", 10);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    var page_r10 = ctx.$implicit;\n    ɵngcc0.ɵɵnextContext(2);\n\n    var _r0 = ɵngcc0.ɵɵreference(1);\n\n    ɵngcc0.ɵɵclassProp(\"current\", _r0.getCurrent() === page_r10.value)(\"ellipsis\", page_r10.label === \"...\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", _r0.getCurrent() !== page_r10.value);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", _r0.getCurrent() === page_r10.value);\n  }\n}\n\nfunction PaginationControlsComponent_ul_2_li_5_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    var _r23 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"a\", 11);\n    ɵngcc0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_2_li_5_a_1_Template_a_keyup_enter_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r23);\n      ɵngcc0.ɵɵnextContext(3);\n\n      var _r0 = ɵngcc0.ɵɵreference(1);\n\n      return _r0.next();\n    })(\"click\", function PaginationControlsComponent_ul_2_li_5_a_1_Template_a_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r23);\n      ɵngcc0.ɵɵnextContext(3);\n\n      var _r0 = ɵngcc0.ɵɵreference(1);\n\n      return _r0.next();\n    });\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementStart(2, \"span\", 12);\n    ɵngcc0.ɵɵtext(3);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    var ctx_r20 = ɵngcc0.ɵɵnextContext(3);\n    ɵngcc0.ɵɵattribute(\"aria-label\", ctx_r20.nextLabel + \" \" + ctx_r20.screenReaderPageLabel);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r20.nextLabel, \" \");\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r20.screenReaderPageLabel);\n  }\n}\n\nfunction PaginationControlsComponent_ul_2_li_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"span\");\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementStart(2, \"span\", 12);\n    ɵngcc0.ɵɵtext(3);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    var ctx_r21 = ɵngcc0.ɵɵnextContext(3);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r21.nextLabel, \" \");\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r21.screenReaderPageLabel);\n  }\n}\n\nfunction PaginationControlsComponent_ul_2_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"li\", 13);\n    ɵngcc0.ɵɵtemplate(1, PaginationControlsComponent_ul_2_li_5_a_1_Template, 4, 3, \"a\", 9);\n    ɵngcc0.ɵɵtemplate(2, PaginationControlsComponent_ul_2_li_5_span_2_Template, 4, 2, \"span\", 10);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    ɵngcc0.ɵɵnextContext(2);\n\n    var _r0 = ɵngcc0.ɵɵreference(1);\n\n    ɵngcc0.ɵɵclassProp(\"disabled\", _r0.isLastPage());\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", !_r0.isLastPage());\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", _r0.isLastPage());\n  }\n}\n\nfunction PaginationControlsComponent_ul_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"ul\", 3);\n    ɵngcc0.ɵɵtemplate(1, PaginationControlsComponent_ul_2_li_1_Template, 3, 4, \"li\", 4);\n    ɵngcc0.ɵɵelementStart(2, \"li\", 5);\n    ɵngcc0.ɵɵtext(3);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵtemplate(4, PaginationControlsComponent_ul_2_li_4_Template, 3, 6, \"li\", 6);\n    ɵngcc0.ɵɵtemplate(5, PaginationControlsComponent_ul_2_li_5_Template, 3, 4, \"li\", 7);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    var ctx_r1 = ɵngcc0.ɵɵnextContext();\n\n    var _r0 = ɵngcc0.ɵɵreference(1);\n\n    ɵngcc0.ɵɵclassProp(\"responsive\", ctx_r1.responsive);\n    ɵngcc0.ɵɵattribute(\"aria-label\", ctx_r1.screenReaderPaginationLabel);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r1.directionLinks);\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate2(\" \", _r0.getCurrent(), \" / \", _r0.getLastPage(), \" \");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", _r0.pages)(\"ngForTrackBy\", ctx_r1.trackByIndex);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r1.directionLinks);\n  }\n}\n\nvar PaginationService =\n/** @class */\nfunction () {\n  function PaginationService() {\n    this.change = new EventEmitter();\n    this.instances = {};\n    this.DEFAULT_ID = 'DEFAULT_PAGINATION_ID';\n  }\n\n  PaginationService.prototype.defaultId = function () {\n    return this.DEFAULT_ID;\n  };\n  /**\r\n   * Register a PaginationInstance with this service. Returns a\r\n   * boolean value signifying whether the instance is new or\r\n   * updated (true = new or updated, false = unchanged).\r\n   */\n\n\n  PaginationService.prototype.register = function (instance) {\n    if (instance.id == null) {\n      instance.id = this.DEFAULT_ID;\n    }\n\n    if (!this.instances[instance.id]) {\n      this.instances[instance.id] = instance;\n      return true;\n    } else {\n      return this.updateInstance(instance);\n    }\n  };\n  /**\r\n   * Check each property of the instance and update any that have changed. Return\r\n   * true if any changes were made, else return false.\r\n   */\n\n\n  PaginationService.prototype.updateInstance = function (instance) {\n    var changed = false;\n\n    for (var prop in this.instances[instance.id]) {\n      if (instance[prop] !== this.instances[instance.id][prop]) {\n        this.instances[instance.id][prop] = instance[prop];\n        changed = true;\n      }\n    }\n\n    return changed;\n  };\n  /**\r\n   * Returns the current page number.\r\n   */\n\n\n  PaginationService.prototype.getCurrentPage = function (id) {\n    if (this.instances[id]) {\n      return this.instances[id].currentPage;\n    }\n  };\n  /**\r\n   * Sets the current page number.\r\n   */\n\n\n  PaginationService.prototype.setCurrentPage = function (id, page) {\n    if (this.instances[id]) {\n      var instance = this.instances[id];\n      var maxPage = Math.ceil(instance.totalItems / instance.itemsPerPage);\n\n      if (page <= maxPage && 1 <= page) {\n        this.instances[id].currentPage = page;\n        this.change.emit(id);\n      }\n    }\n  };\n  /**\r\n   * Sets the value of instance.totalItems\r\n   */\n\n\n  PaginationService.prototype.setTotalItems = function (id, totalItems) {\n    if (this.instances[id] && 0 <= totalItems) {\n      this.instances[id].totalItems = totalItems;\n      this.change.emit(id);\n    }\n  };\n  /**\r\n   * Sets the value of instance.itemsPerPage.\r\n   */\n\n\n  PaginationService.prototype.setItemsPerPage = function (id, itemsPerPage) {\n    if (this.instances[id]) {\n      this.instances[id].itemsPerPage = itemsPerPage;\n      this.change.emit(id);\n    }\n  };\n  /**\r\n   * Returns a clone of the pagination instance object matching the id. If no\r\n   * id specified, returns the instance corresponding to the default id.\r\n   */\n\n\n  PaginationService.prototype.getInstance = function (id) {\n    if (id === void 0) {\n      id = this.DEFAULT_ID;\n    }\n\n    if (this.instances[id]) {\n      return this.clone(this.instances[id]);\n    }\n\n    return {};\n  };\n  /**\r\n   * Perform a shallow clone of an object.\r\n   */\n\n\n  PaginationService.prototype.clone = function (obj) {\n    var target = {};\n\n    for (var i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        target[i] = obj[i];\n      }\n    }\n\n    return target;\n  };\n\n  PaginationService.ɵfac = function PaginationService_Factory(t) {\n    return new (t || PaginationService)();\n  };\n\n  PaginationService.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n    token: PaginationService,\n    factory: function (t) {\n      return PaginationService.ɵfac(t);\n    }\n  });\n  return PaginationService;\n}();\n\nvar __decorate$1 = undefined && undefined.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n      r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n      d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n\nvar __metadata = undefined && undefined.__metadata || function (k, v) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(k, v);\n};\n\nvar LARGE_NUMBER = Number.MAX_SAFE_INTEGER;\n\nvar PaginatePipe =\n/** @class */\nfunction () {\n  function PaginatePipe(service) {\n    this.service = service; // store the values from the last time the pipe was invoked\n\n    this.state = {};\n  }\n\n  PaginatePipe.prototype.transform = function (collection, args) {\n    // When an observable is passed through the AsyncPipe, it will output\n    // `null` until the subscription resolves. In this case, we want to\n    // use the cached data from the `state` object to prevent the NgFor\n    // from flashing empty until the real values arrive.\n    if (!(collection instanceof Array)) {\n      var _id = args.id || this.service.defaultId();\n\n      if (this.state[_id]) {\n        return this.state[_id].slice;\n      } else {\n        return collection;\n      }\n    }\n\n    var serverSideMode = args.totalItems && args.totalItems !== collection.length;\n    var instance = this.createInstance(collection, args);\n    var id = instance.id;\n    var start, end;\n    var perPage = instance.itemsPerPage;\n    var emitChange = this.service.register(instance);\n\n    if (!serverSideMode && collection instanceof Array) {\n      perPage = +perPage || LARGE_NUMBER;\n      start = (instance.currentPage - 1) * perPage;\n      end = start + perPage;\n      var isIdentical = this.stateIsIdentical(id, collection, start, end);\n\n      if (isIdentical) {\n        return this.state[id].slice;\n      } else {\n        var slice = collection.slice(start, end);\n        this.saveState(id, collection, slice, start, end);\n        this.service.change.emit(id);\n        return slice;\n      }\n    } else {\n      if (emitChange) {\n        this.service.change.emit(id);\n      } // save the state for server-side collection to avoid null\n      // flash as new data loads.\n\n\n      this.saveState(id, collection, collection, start, end);\n      return collection;\n    }\n  };\n  /**\r\n   * Create an PaginationInstance object, using defaults for any optional properties not supplied.\r\n   */\n\n\n  PaginatePipe.prototype.createInstance = function (collection, config) {\n    this.checkConfig(config);\n    return {\n      id: config.id != null ? config.id : this.service.defaultId(),\n      itemsPerPage: +config.itemsPerPage || 0,\n      currentPage: +config.currentPage || 1,\n      totalItems: +config.totalItems || collection.length\n    };\n  };\n  /**\r\n   * Ensure the argument passed to the filter contains the required properties.\r\n   */\n\n\n  PaginatePipe.prototype.checkConfig = function (config) {\n    var required = ['itemsPerPage', 'currentPage'];\n    var missing = required.filter(function (prop) {\n      return !(prop in config);\n    });\n\n    if (0 < missing.length) {\n      throw new Error(\"PaginatePipe: Argument is missing the following required properties: \" + missing.join(', '));\n    }\n  };\n  /**\r\n   * To avoid returning a brand new array each time the pipe is run, we store the state of the sliced\r\n   * array for a given id. This means that the next time the pipe is run on this collection & id, we just\r\n   * need to check that the collection, start and end points are all identical, and if so, return the\r\n   * last sliced array.\r\n   */\n\n\n  PaginatePipe.prototype.saveState = function (id, collection, slice, start, end) {\n    this.state[id] = {\n      collection: collection,\n      size: collection.length,\n      slice: slice,\n      start: start,\n      end: end\n    };\n  };\n  /**\r\n   * For a given id, returns true if the collection, size, start and end values are identical.\r\n   */\n\n\n  PaginatePipe.prototype.stateIsIdentical = function (id, collection, start, end) {\n    var state = this.state[id];\n\n    if (!state) {\n      return false;\n    }\n\n    var isMetaDataIdentical = state.size === collection.length && state.start === start && state.end === end;\n\n    if (!isMetaDataIdentical) {\n      return false;\n    }\n\n    return state.slice.every(function (element, index) {\n      return element === collection[start + index];\n    });\n  };\n\n  PaginatePipe = __decorate$1([__metadata(\"design:paramtypes\", [PaginationService])], PaginatePipe);\n\n  PaginatePipe.ɵfac = function PaginatePipe_Factory(t) {\n    return new (t || PaginatePipe)(ɵngcc0.ɵɵdirectiveInject(PaginationService, 16));\n  };\n\n  PaginatePipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n    name: \"paginate\",\n    type: PaginatePipe,\n    pure: false\n  });\n\n  (function () {\n    (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n  })();\n\n  return PaginatePipe;\n}();\n/**\r\n * The default template and styles for the pagination links are borrowed directly\r\n * from Zurb Foundation 6: http://foundation.zurb.com/sites/docs/pagination.html\r\n */\n\n\nvar DEFAULT_TEMPLATE = \"\\n    <pagination-template  #p=\\\"paginationApi\\\"\\n                         [id]=\\\"id\\\"\\n                         [maxSize]=\\\"maxSize\\\"\\n                         (pageChange)=\\\"pageChange.emit($event)\\\"\\n                         (pageBoundsCorrection)=\\\"pageBoundsCorrection.emit($event)\\\">\\n    <ul class=\\\"ngx-pagination\\\"\\n        [attr.aria-label]=\\\"screenReaderPaginationLabel\\\" \\n        [class.responsive]=\\\"responsive\\\"\\n        *ngIf=\\\"!(autoHide && p.pages.length <= 1)\\\">\\n\\n        <li class=\\\"pagination-previous\\\" [class.disabled]=\\\"p.isFirstPage()\\\" *ngIf=\\\"directionLinks\\\"> \\n            <a tabindex=\\\"0\\\" *ngIf=\\\"1 < p.getCurrent()\\\" (keyup.enter)=\\\"p.previous()\\\" (click)=\\\"p.previous()\\\" [attr.aria-label]=\\\"previousLabel + ' ' + screenReaderPageLabel\\\">\\n                {{ previousLabel }} <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }}</span>\\n            </a>\\n            <span *ngIf=\\\"p.isFirstPage()\\\">\\n                {{ previousLabel }} <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }}</span>\\n            </span>\\n        </li> \\n\\n        <li class=\\\"small-screen\\\">\\n            {{ p.getCurrent() }} / {{ p.getLastPage() }}\\n        </li>\\n\\n        <li [class.current]=\\\"p.getCurrent() === page.value\\\" \\n            [class.ellipsis]=\\\"page.label === '...'\\\"\\n            *ngFor=\\\"let page of p.pages; trackBy: trackByIndex\\\">\\n            <a tabindex=\\\"0\\\" (keyup.enter)=\\\"p.setCurrent(page.value)\\\" (click)=\\\"p.setCurrent(page.value)\\\" *ngIf=\\\"p.getCurrent() !== page.value\\\">\\n                <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }} </span>\\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span>\\n            </a>\\n            <ng-container *ngIf=\\\"p.getCurrent() === page.value\\\">\\n                <span class=\\\"show-for-sr\\\">{{ screenReaderCurrentLabel }} </span>\\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span> \\n            </ng-container>\\n        </li>\\n\\n        <li class=\\\"pagination-next\\\" [class.disabled]=\\\"p.isLastPage()\\\" *ngIf=\\\"directionLinks\\\">\\n            <a tabindex=\\\"0\\\" *ngIf=\\\"!p.isLastPage()\\\" (keyup.enter)=\\\"p.next()\\\" (click)=\\\"p.next()\\\" [attr.aria-label]=\\\"nextLabel + ' ' + screenReaderPageLabel\\\">\\n                 {{ nextLabel }} <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }}</span>\\n            </a>\\n            <span *ngIf=\\\"p.isLastPage()\\\">\\n                 {{ nextLabel }} <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }}</span>\\n            </span>\\n        </li>\\n\\n    </ul>\\n    </pagination-template>\\n    \";\nvar DEFAULT_STYLES = \"\\n.ngx-pagination {\\n  margin-left: 0;\\n  margin-bottom: 1rem; }\\n  .ngx-pagination::before, .ngx-pagination::after {\\n    content: ' ';\\n    display: table; }\\n  .ngx-pagination::after {\\n    clear: both; }\\n  .ngx-pagination li {\\n    -moz-user-select: none;\\n    -webkit-user-select: none;\\n    -ms-user-select: none;\\n    margin-right: 0.0625rem;\\n    border-radius: 0; }\\n  .ngx-pagination li {\\n    display: inline-block; }\\n  .ngx-pagination a,\\n  .ngx-pagination button {\\n    color: #0a0a0a; \\n    display: block;\\n    padding: 0.1875rem 0.625rem;\\n    border-radius: 0; }\\n    .ngx-pagination a:hover,\\n    .ngx-pagination button:hover {\\n      background: #e6e6e6; }\\n  .ngx-pagination .current {\\n    padding: 0.1875rem 0.625rem;\\n    background: #2199e8;\\n    color: #fefefe;\\n    cursor: default; }\\n  .ngx-pagination .disabled {\\n    padding: 0.1875rem 0.625rem;\\n    color: #cacaca;\\n    cursor: default; } \\n    .ngx-pagination .disabled:hover {\\n      background: transparent; }\\n  .ngx-pagination a, .ngx-pagination button {\\n    cursor: pointer; }\\n\\n.ngx-pagination .pagination-previous a::before,\\n.ngx-pagination .pagination-previous.disabled::before { \\n  content: '\\u00AB';\\n  display: inline-block;\\n  margin-right: 0.5rem; }\\n\\n.ngx-pagination .pagination-next a::after,\\n.ngx-pagination .pagination-next.disabled::after {\\n  content: '\\u00BB';\\n  display: inline-block;\\n  margin-left: 0.5rem; }\\n\\n.ngx-pagination .show-for-sr {\\n  position: absolute !important;\\n  width: 1px;\\n  height: 1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0); }\\n.ngx-pagination .small-screen {\\n  display: none; }\\n@media screen and (max-width: 601px) {\\n  .ngx-pagination.responsive .small-screen {\\n    display: inline-block; } \\n  .ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next) {\\n    display: none; }\\n}\\n  \";\n\nvar __decorate$2 = undefined && undefined.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n      r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n      d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n\nvar __metadata$1 = undefined && undefined.__metadata || function (k, v) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(k, v);\n};\n\nfunction coerceToBoolean(input) {\n  return !!input && input !== 'false';\n}\n/**\r\n * The default pagination controls component. Actually just a default implementation of a custom template.\r\n */\n\n\nvar PaginationControlsComponent =\n/** @class */\nfunction () {\n  function PaginationControlsComponent() {\n    this.maxSize = 7;\n    this.previousLabel = 'Previous';\n    this.nextLabel = 'Next';\n    this.screenReaderPaginationLabel = 'Pagination';\n    this.screenReaderPageLabel = 'page';\n    this.screenReaderCurrentLabel = \"You're on page\";\n    this.pageChange = new EventEmitter();\n    this.pageBoundsCorrection = new EventEmitter();\n    this._directionLinks = true;\n    this._autoHide = false;\n    this._responsive = false;\n  }\n\n  Object.defineProperty(PaginationControlsComponent.prototype, \"directionLinks\", {\n    get: function () {\n      return this._directionLinks;\n    },\n    set: function (value) {\n      this._directionLinks = coerceToBoolean(value);\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(PaginationControlsComponent.prototype, \"autoHide\", {\n    get: function () {\n      return this._autoHide;\n    },\n    set: function (value) {\n      this._autoHide = coerceToBoolean(value);\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(PaginationControlsComponent.prototype, \"responsive\", {\n    get: function () {\n      return this._responsive;\n    },\n    set: function (value) {\n      this._responsive = coerceToBoolean(value);\n    },\n    enumerable: true,\n    configurable: true\n  });\n\n  PaginationControlsComponent.prototype.trackByIndex = function (index) {\n    return index;\n  };\n\n  __decorate$2([Input(), __metadata$1(\"design:type\", String)], PaginationControlsComponent.prototype, \"id\", void 0);\n\n  __decorate$2([Input(), __metadata$1(\"design:type\", Number)], PaginationControlsComponent.prototype, \"maxSize\", void 0);\n\n  __decorate$2([Input(), __metadata$1(\"design:type\", Boolean), __metadata$1(\"design:paramtypes\", [Boolean])], PaginationControlsComponent.prototype, \"directionLinks\", null);\n\n  __decorate$2([Input(), __metadata$1(\"design:type\", Boolean), __metadata$1(\"design:paramtypes\", [Boolean])], PaginationControlsComponent.prototype, \"autoHide\", null);\n\n  __decorate$2([Input(), __metadata$1(\"design:type\", Boolean), __metadata$1(\"design:paramtypes\", [Boolean])], PaginationControlsComponent.prototype, \"responsive\", null);\n\n  __decorate$2([Input(), __metadata$1(\"design:type\", String)], PaginationControlsComponent.prototype, \"previousLabel\", void 0);\n\n  __decorate$2([Input(), __metadata$1(\"design:type\", String)], PaginationControlsComponent.prototype, \"nextLabel\", void 0);\n\n  __decorate$2([Input(), __metadata$1(\"design:type\", String)], PaginationControlsComponent.prototype, \"screenReaderPaginationLabel\", void 0);\n\n  __decorate$2([Input(), __metadata$1(\"design:type\", String)], PaginationControlsComponent.prototype, \"screenReaderPageLabel\", void 0);\n\n  __decorate$2([Input(), __metadata$1(\"design:type\", String)], PaginationControlsComponent.prototype, \"screenReaderCurrentLabel\", void 0);\n\n  __decorate$2([Output(), __metadata$1(\"design:type\", EventEmitter)], PaginationControlsComponent.prototype, \"pageChange\", void 0);\n\n  __decorate$2([Output(), __metadata$1(\"design:type\", EventEmitter)], PaginationControlsComponent.prototype, \"pageBoundsCorrection\", void 0);\n\n  PaginationControlsComponent.ɵfac = function PaginationControlsComponent_Factory(t) {\n    return new (t || PaginationControlsComponent)();\n  };\n\n  PaginationControlsComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: PaginationControlsComponent,\n    selectors: [[\"pagination-controls\"]],\n    inputs: {\n      maxSize: \"maxSize\",\n      previousLabel: \"previousLabel\",\n      nextLabel: \"nextLabel\",\n      screenReaderPaginationLabel: \"screenReaderPaginationLabel\",\n      screenReaderPageLabel: \"screenReaderPageLabel\",\n      screenReaderCurrentLabel: \"screenReaderCurrentLabel\",\n      directionLinks: \"directionLinks\",\n      autoHide: \"autoHide\",\n      responsive: \"responsive\",\n      id: \"id\"\n    },\n    outputs: {\n      pageChange: \"pageChange\",\n      pageBoundsCorrection: \"pageBoundsCorrection\"\n    },\n    decls: 3,\n    vars: 3,\n    consts: [[3, \"id\", \"maxSize\", \"pageChange\", \"pageBoundsCorrection\"], [\"p\", \"paginationApi\"], [\"class\", \"ngx-pagination\", 3, \"responsive\", 4, \"ngIf\"], [1, \"ngx-pagination\"], [\"class\", \"pagination-previous\", 3, \"disabled\", 4, \"ngIf\"], [1, \"small-screen\"], [3, \"current\", \"ellipsis\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"pagination-next\", 3, \"disabled\", 4, \"ngIf\"], [1, \"pagination-previous\"], [\"tabindex\", \"0\", 3, \"keyup.enter\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [\"tabindex\", \"0\", 3, \"keyup.enter\", \"click\"], [1, \"show-for-sr\"], [1, \"pagination-next\"]],\n    template: function PaginationControlsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"pagination-template\", 0, 1);\n        ɵngcc0.ɵɵlistener(\"pageChange\", function PaginationControlsComponent_Template_pagination_template_pageChange_0_listener($event) {\n          return ctx.pageChange.emit($event);\n        })(\"pageBoundsCorrection\", function PaginationControlsComponent_Template_pagination_template_pageBoundsCorrection_0_listener($event) {\n          return ctx.pageBoundsCorrection.emit($event);\n        });\n        ɵngcc0.ɵɵtemplate(2, PaginationControlsComponent_ul_2_Template, 6, 9, \"ul\", 2);\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        var _r0 = ɵngcc0.ɵɵreference(1);\n\n        ɵngcc0.ɵɵproperty(\"id\", ctx.id)(\"maxSize\", ctx.maxSize);\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵproperty(\"ngIf\", !(ctx.autoHide && _r0.pages.length <= 1));\n      }\n    },\n    directives: function () {\n      return [PaginationControlsDirective, ɵngcc1.NgIf, ɵngcc1.NgForOf];\n    },\n    pipes: function () {\n      return [ɵngcc1.DecimalPipe];\n    },\n    styles: [\"\\n.ngx-pagination {\\n  margin-left: 0;\\n  margin-bottom: 1rem; }\\n  .ngx-pagination::before, .ngx-pagination::after {\\n    content: ' ';\\n    display: table; }\\n  .ngx-pagination::after {\\n    clear: both; }\\n  .ngx-pagination li {\\n    -moz-user-select: none;\\n    -webkit-user-select: none;\\n    -ms-user-select: none;\\n    margin-right: 0.0625rem;\\n    border-radius: 0; }\\n  .ngx-pagination li {\\n    display: inline-block; }\\n  .ngx-pagination a,\\n  .ngx-pagination button {\\n    color: #0a0a0a; \\n    display: block;\\n    padding: 0.1875rem 0.625rem;\\n    border-radius: 0; }\\n    .ngx-pagination a:hover,\\n    .ngx-pagination button:hover {\\n      background: #e6e6e6; }\\n  .ngx-pagination .current {\\n    padding: 0.1875rem 0.625rem;\\n    background: #2199e8;\\n    color: #fefefe;\\n    cursor: default; }\\n  .ngx-pagination .disabled {\\n    padding: 0.1875rem 0.625rem;\\n    color: #cacaca;\\n    cursor: default; } \\n    .ngx-pagination .disabled:hover {\\n      background: transparent; }\\n  .ngx-pagination a, .ngx-pagination button {\\n    cursor: pointer; }\\n\\n.ngx-pagination .pagination-previous a::before,\\n.ngx-pagination .pagination-previous.disabled::before { \\n  content: '\\u00AB';\\n  display: inline-block;\\n  margin-right: 0.5rem; }\\n\\n.ngx-pagination .pagination-next a::after,\\n.ngx-pagination .pagination-next.disabled::after {\\n  content: '\\u00BB';\\n  display: inline-block;\\n  margin-left: 0.5rem; }\\n\\n.ngx-pagination .show-for-sr {\\n  position: absolute !important;\\n  width: 1px;\\n  height: 1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0); }\\n.ngx-pagination .small-screen {\\n  display: none; }\\n@media screen and (max-width: 601px) {\\n  .ngx-pagination.responsive .small-screen {\\n    display: inline-block; } \\n  .ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next) {\\n    display: none; }\\n}\\n  \"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n\n  (function () {\n    (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n  })();\n\n  return PaginationControlsComponent;\n}();\n\nvar __decorate$3 = undefined && undefined.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n      r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n      d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n\nvar __metadata$2 = undefined && undefined.__metadata || function (k, v) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(k, v);\n};\n/**\r\n * This directive is what powers all pagination controls components, including the default one.\r\n * It exposes an API which is hooked up to the PaginationService to keep the PaginatePipe in sync\r\n * with the pagination controls.\r\n */\n\n\nvar PaginationControlsDirective =\n/** @class */\nfunction () {\n  function PaginationControlsDirective(service, changeDetectorRef) {\n    var _this = this;\n\n    this.service = service;\n    this.changeDetectorRef = changeDetectorRef;\n    this.maxSize = 7;\n    this.pageChange = new EventEmitter();\n    this.pageBoundsCorrection = new EventEmitter();\n    this.pages = [];\n    this.changeSub = this.service.change.subscribe(function (id) {\n      if (_this.id === id) {\n        _this.updatePageLinks();\n\n        _this.changeDetectorRef.markForCheck();\n\n        _this.changeDetectorRef.detectChanges();\n      }\n    });\n  }\n\n  PaginationControlsDirective.prototype.ngOnInit = function () {\n    if (this.id === undefined) {\n      this.id = this.service.defaultId();\n    }\n\n    this.updatePageLinks();\n  };\n\n  PaginationControlsDirective.prototype.ngOnChanges = function (changes) {\n    this.updatePageLinks();\n  };\n\n  PaginationControlsDirective.prototype.ngOnDestroy = function () {\n    this.changeSub.unsubscribe();\n  };\n  /**\r\n   * Go to the previous page\r\n   */\n\n\n  PaginationControlsDirective.prototype.previous = function () {\n    this.checkValidId();\n    this.setCurrent(this.getCurrent() - 1);\n  };\n  /**\r\n   * Go to the next page\r\n   */\n\n\n  PaginationControlsDirective.prototype.next = function () {\n    this.checkValidId();\n    this.setCurrent(this.getCurrent() + 1);\n  };\n  /**\r\n   * Returns true if current page is first page\r\n   */\n\n\n  PaginationControlsDirective.prototype.isFirstPage = function () {\n    return this.getCurrent() === 1;\n  };\n  /**\r\n   * Returns true if current page is last page\r\n   */\n\n\n  PaginationControlsDirective.prototype.isLastPage = function () {\n    return this.getLastPage() === this.getCurrent();\n  };\n  /**\r\n   * Set the current page number.\r\n   */\n\n\n  PaginationControlsDirective.prototype.setCurrent = function (page) {\n    this.pageChange.emit(page);\n  };\n  /**\r\n   * Get the current page number.\r\n   */\n\n\n  PaginationControlsDirective.prototype.getCurrent = function () {\n    return this.service.getCurrentPage(this.id);\n  };\n  /**\r\n   * Returns the last page number\r\n   */\n\n\n  PaginationControlsDirective.prototype.getLastPage = function () {\n    var inst = this.service.getInstance(this.id);\n\n    if (inst.totalItems < 1) {\n      // when there are 0 or fewer (an error case) items, there are no \"pages\" as such,\n      // but it makes sense to consider a single, empty page as the last page.\n      return 1;\n    }\n\n    return Math.ceil(inst.totalItems / inst.itemsPerPage);\n  };\n\n  PaginationControlsDirective.prototype.getTotalItems = function () {\n    return this.service.getInstance(this.id).totalItems;\n  };\n\n  PaginationControlsDirective.prototype.checkValidId = function () {\n    if (this.service.getInstance(this.id).id == null) {\n      console.warn(\"PaginationControlsDirective: the specified id \\\"\" + this.id + \"\\\" does not match any registered PaginationInstance\");\n    }\n  };\n  /**\r\n   * Updates the page links and checks that the current page is valid. Should run whenever the\r\n   * PaginationService.change stream emits a value matching the current ID, or when any of the\r\n   * input values changes.\r\n   */\n\n\n  PaginationControlsDirective.prototype.updatePageLinks = function () {\n    var _this = this;\n\n    var inst = this.service.getInstance(this.id);\n    var correctedCurrentPage = this.outOfBoundCorrection(inst);\n\n    if (correctedCurrentPage !== inst.currentPage) {\n      setTimeout(function () {\n        _this.pageBoundsCorrection.emit(correctedCurrentPage);\n\n        _this.pages = _this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, _this.maxSize);\n      });\n    } else {\n      this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\n    }\n  };\n  /**\r\n   * Checks that the instance.currentPage property is within bounds for the current page range.\r\n   * If not, return a correct value for currentPage, or the current value if OK.\r\n   */\n\n\n  PaginationControlsDirective.prototype.outOfBoundCorrection = function (instance) {\n    var totalPages = Math.ceil(instance.totalItems / instance.itemsPerPage);\n\n    if (totalPages < instance.currentPage && 0 < totalPages) {\n      return totalPages;\n    } else if (instance.currentPage < 1) {\n      return 1;\n    }\n\n    return instance.currentPage;\n  };\n  /**\r\n   * Returns an array of Page objects to use in the pagination controls.\r\n   */\n\n\n  PaginationControlsDirective.prototype.createPageArray = function (currentPage, itemsPerPage, totalItems, paginationRange) {\n    // paginationRange could be a string if passed from attribute, so cast to number.\n    paginationRange = +paginationRange;\n    var pages = []; // Return 1 as default page number\n    // Make sense to show 1 instead of empty when there are no items\n\n    var totalPages = Math.max(Math.ceil(totalItems / itemsPerPage), 1);\n    var halfWay = Math.ceil(paginationRange / 2);\n    var isStart = currentPage <= halfWay;\n    var isEnd = totalPages - halfWay < currentPage;\n    var isMiddle = !isStart && !isEnd;\n    var ellipsesNeeded = paginationRange < totalPages;\n    var i = 1;\n\n    while (i <= totalPages && i <= paginationRange) {\n      var label = void 0;\n      var pageNumber = this.calculatePageNumber(i, currentPage, paginationRange, totalPages);\n      var openingEllipsesNeeded = i === 2 && (isMiddle || isEnd);\n      var closingEllipsesNeeded = i === paginationRange - 1 && (isMiddle || isStart);\n\n      if (ellipsesNeeded && (openingEllipsesNeeded || closingEllipsesNeeded)) {\n        label = '...';\n      } else {\n        label = pageNumber;\n      }\n\n      pages.push({\n        label: label,\n        value: pageNumber\n      });\n      i++;\n    }\n\n    return pages;\n  };\n  /**\r\n   * Given the position in the sequence of pagination links [i],\r\n   * figure out what page number corresponds to that position.\r\n   */\n\n\n  PaginationControlsDirective.prototype.calculatePageNumber = function (i, currentPage, paginationRange, totalPages) {\n    var halfWay = Math.ceil(paginationRange / 2);\n\n    if (i === paginationRange) {\n      return totalPages;\n    } else if (i === 1) {\n      return i;\n    } else if (paginationRange < totalPages) {\n      if (totalPages - halfWay < currentPage) {\n        return totalPages - paginationRange + i;\n      } else if (halfWay < currentPage) {\n        return currentPage - halfWay + i;\n      } else {\n        return i;\n      }\n    } else {\n      return i;\n    }\n  };\n\n  __decorate$3([Input(), __metadata$2(\"design:type\", String)], PaginationControlsDirective.prototype, \"id\", void 0);\n\n  __decorate$3([Input(), __metadata$2(\"design:type\", Number)], PaginationControlsDirective.prototype, \"maxSize\", void 0);\n\n  __decorate$3([Output(), __metadata$2(\"design:type\", EventEmitter)], PaginationControlsDirective.prototype, \"pageChange\", void 0);\n\n  __decorate$3([Output(), __metadata$2(\"design:type\", EventEmitter)], PaginationControlsDirective.prototype, \"pageBoundsCorrection\", void 0);\n\n  PaginationControlsDirective = __decorate$3([__metadata$2(\"design:paramtypes\", [PaginationService, ChangeDetectorRef])], PaginationControlsDirective);\n\n  PaginationControlsDirective.ɵfac = function PaginationControlsDirective_Factory(t) {\n    return new (t || PaginationControlsDirective)(ɵngcc0.ɵɵdirectiveInject(PaginationService), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef));\n  };\n\n  PaginationControlsDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n    type: PaginationControlsDirective,\n    selectors: [[\"pagination-template\"], [\"\", \"pagination-template\", \"\"]],\n    inputs: {\n      maxSize: \"maxSize\",\n      id: \"id\"\n    },\n    outputs: {\n      pageChange: \"pageChange\",\n      pageBoundsCorrection: \"pageBoundsCorrection\"\n    },\n    exportAs: [\"paginationApi\"],\n    features: [ɵngcc0.ɵɵNgOnChangesFeature]\n  });\n\n  (function () {\n    (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n  })();\n\n  return PaginationControlsDirective;\n}();\n\nvar __decorate = undefined && undefined.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n      r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n      d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n\nvar NgxPaginationModule =\n/** @class */\nfunction () {\n  function NgxPaginationModule() {}\n\n  NgxPaginationModule.ɵfac = function NgxPaginationModule_Factory(t) {\n    return new (t || NgxPaginationModule)();\n  };\n\n  NgxPaginationModule.ɵmod = /*@__PURE__*/ɵngcc0.ɵɵdefineNgModule({\n    type: NgxPaginationModule\n  });\n  NgxPaginationModule.ɵinj = /*@__PURE__*/ɵngcc0.ɵɵdefineInjector({\n    providers: [PaginationService],\n    imports: [[CommonModule]]\n  });\n\n  (function () {\n    (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n  })();\n\n  (function () {\n    (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(NgxPaginationModule, {\n      declarations: function () {\n        return [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective];\n      },\n      imports: function () {\n        return [CommonModule];\n      },\n      exports: function () {\n        return [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective];\n      }\n    });\n  })();\n\n  return NgxPaginationModule;\n}();\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\n\nexport { DEFAULT_STYLES as ɵb, DEFAULT_TEMPLATE as ɵa, NgxPaginationModule, PaginationService, PaginationControlsComponent, PaginationControlsDirective, PaginatePipe }; //# sourceMappingURL=data:application/json;charset=utf-8;base64,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", "map": null, "metadata": {}, "sourceType": "module"}