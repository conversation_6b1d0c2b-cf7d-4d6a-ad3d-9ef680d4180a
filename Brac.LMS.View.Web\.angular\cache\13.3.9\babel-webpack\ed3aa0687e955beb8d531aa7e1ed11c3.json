{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, Injectable, Optional, Inject, Directive, Output, Input, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-brower because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\n\nconst DIR_DOCUMENT = /*#__PURE__*/new InjectionToken('cdk-dir-doc', {\n  providedIn: 'root',\n  factory: DIR_DOCUMENT_FACTORY\n});\n/** @docs-private */\n\nfunction DIR_DOCUMENT_FACTORY() {\n  return inject(DOCUMENT);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\n\n\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\n\nfunction _resolveDirectionality(rawValue) {\n  const value = (rawValue === null || rawValue === void 0 ? void 0 : rawValue.toLowerCase()) || '';\n\n  if (value === 'auto' && typeof navigator !== 'undefined' && (navigator === null || navigator === void 0 ? void 0 : navigator.language)) {\n    return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n  }\n\n  return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\n\n\nlet Directionality = /*#__PURE__*/(() => {\n  class Directionality {\n    constructor(_document) {\n      /** The current 'ltr' or 'rtl' value. */\n      this.value = 'ltr';\n      /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n\n      this.change = new EventEmitter();\n\n      if (_document) {\n        const bodyDir = _document.body ? _document.body.dir : null;\n        const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n        this.value = _resolveDirectionality(bodyDir || htmlDir || 'ltr');\n      }\n    }\n\n    ngOnDestroy() {\n      this.change.complete();\n    }\n\n  }\n\n  Directionality.ɵfac = function Directionality_Factory(t) {\n    return new (t || Directionality)(i0.ɵɵinject(DIR_DOCUMENT, 8));\n  };\n\n  Directionality.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Directionality,\n    factory: Directionality.ɵfac,\n    providedIn: 'root'\n  });\n  return Directionality;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\n\n\nlet Dir = /*#__PURE__*/(() => {\n  class Dir {\n    constructor() {\n      /** Normalized direction that accounts for invalid/unsupported values. */\n      this._dir = 'ltr';\n      /** Whether the `value` has been set to its initial value. */\n\n      this._isInitialized = false;\n      /** Event emitted when the direction changes. */\n\n      this.change = new EventEmitter();\n    }\n    /** @docs-private */\n\n\n    get dir() {\n      return this._dir;\n    }\n\n    set dir(value) {\n      const previousValue = this._dir; // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n      // whereas the browser does it based on the content of the element. Since doing so based\n      // on the content can be expensive, for now we're doing the simpler matching.\n\n      this._dir = _resolveDirectionality(value);\n      this._rawDir = value;\n\n      if (previousValue !== this._dir && this._isInitialized) {\n        this.change.emit(this._dir);\n      }\n    }\n    /** Current layout direction of the element. */\n\n\n    get value() {\n      return this.dir;\n    }\n    /** Initialize once default value has been set. */\n\n\n    ngAfterContentInit() {\n      this._isInitialized = true;\n    }\n\n    ngOnDestroy() {\n      this.change.complete();\n    }\n\n  }\n\n  Dir.ɵfac = function Dir_Factory(t) {\n    return new (t || Dir)();\n  };\n\n  Dir.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: Dir,\n    selectors: [[\"\", \"dir\", \"\"]],\n    hostVars: 1,\n    hostBindings: function Dir_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"dir\", ctx._rawDir);\n      }\n    },\n    inputs: {\n      dir: \"dir\"\n    },\n    outputs: {\n      change: \"dirChange\"\n    },\n    exportAs: [\"dir\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: Directionality,\n      useExisting: Dir\n    }])]\n  });\n  return Dir;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet BidiModule = /*#__PURE__*/(() => {\n  class BidiModule {}\n\n  BidiModule.ɵfac = function BidiModule_Factory(t) {\n    return new (t || BidiModule)();\n  };\n\n  BidiModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BidiModule\n  });\n  BidiModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return BidiModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BidiModule, DIR_DOCUMENT, Dir, Directionality }; //# sourceMappingURL=bidi.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}