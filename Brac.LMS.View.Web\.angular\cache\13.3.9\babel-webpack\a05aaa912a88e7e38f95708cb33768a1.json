{"ast": null, "code": "import { Component, EventEmitter, HostListener, Input, NgModule, Output, forwardRef } from '@angular/core';\nimport { FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/common';\n\nfunction UiSwitchComponent_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"span\", 3);\n    ɵngcc0.ɵɵelement(1, \"span\", 4);\n    ɵngcc0.ɵɵelement(2, \"span\", 5);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"innerHtml\", ctx_r0.labelOn, ɵngcc0.ɵɵsanitizeHtml);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"innerHtml\", ctx_r0.labelOff, ɵngcc0.ɵɵsanitizeHtml);\n  }\n}\n\nconst UI_SWITCH_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n\n  /* tslint:disable-next-line: no-use-before-declare */\n  useExisting: forwardRef(() => UiSwitchComponent),\n  multi: true\n};\nlet UiSwitchComponent = /*#__PURE__*/(() => {\n  class UiSwitchComponent {\n    constructor() {\n      this.size = 'medium';\n      this.change = new EventEmitter();\n      this.color = 'rgb(100, 189, 99)';\n      this.switchOffColor = '';\n      this.switchColor = '#fff';\n      this.defaultBgColor = '#fff';\n      this.defaultBoColor = '#dfdfdf';\n      this.labelOn = '';\n      this.labelOff = '';\n\n      this.onTouchedCallback = v => {};\n\n      this.onChangeCallback = v => {};\n    }\n    /**\n     * @param {?} v\n     * @return {?}\n     */\n\n\n    set checked(v) {\n      this._checked = v !== false;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    get checked() {\n      return this._checked;\n    }\n    /**\n     * @param {?} v\n     * @return {?}\n     */\n\n\n    set disabled(v) {\n      this._disabled = v !== false;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    get disabled() {\n      return this._disabled;\n    }\n    /**\n     * @param {?} v\n     * @return {?}\n     */\n\n\n    set reverse(v) {\n      this._reverse = v !== false;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    get reverse() {\n      return this._reverse;\n    }\n    /**\n     * @param {?=} flag\n     * @return {?}\n     */\n\n\n    getColor(flag = '') {\n      if (flag === 'borderColor') {\n        return this.defaultBoColor;\n      }\n\n      if (flag === 'switchColor') {\n        if (this.reverse) {\n          return !this.checked ? this.switchColor : this.switchOffColor || this.switchColor;\n        }\n\n        return this.checked ? this.switchColor : this.switchOffColor || this.switchColor;\n      }\n\n      if (this.reverse) {\n        return !this.checked ? this.color : this.defaultBgColor;\n      }\n\n      return this.checked ? this.color : this.defaultBgColor;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    onToggle() {\n      if (this.disabled) {\n        return;\n      }\n\n      this.checked = !this.checked;\n      this.change.emit(this.checked);\n      this.onChangeCallback(this.checked);\n      this.onTouchedCallback(this.checked);\n    }\n    /**\n     * @param {?} obj\n     * @return {?}\n     */\n\n\n    writeValue(obj) {\n      if (obj !== this.checked) {\n        this.checked = !!obj;\n      }\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n\n\n    registerOnChange(fn) {\n      this.onChangeCallback = fn;\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n\n\n    registerOnTouched(fn) {\n      this.onTouchedCallback = fn;\n    }\n    /**\n     * @param {?} isDisabled\n     * @return {?}\n     */\n\n\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n    }\n\n  }\n\n  UiSwitchComponent.ɵfac = function UiSwitchComponent_Factory(t) {\n    return new (t || UiSwitchComponent)();\n  };\n\n  UiSwitchComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: UiSwitchComponent,\n    selectors: [[\"ui-switch\"]],\n    hostBindings: function UiSwitchComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function UiSwitchComponent_click_HostBindingHandler() {\n          return ctx.onToggle();\n        });\n      }\n    },\n    inputs: {\n      size: \"size\",\n      color: \"color\",\n      switchOffColor: \"switchOffColor\",\n      switchColor: \"switchColor\",\n      defaultBgColor: \"defaultBgColor\",\n      defaultBoColor: \"defaultBoColor\",\n      labelOn: \"labelOn\",\n      labelOff: \"labelOff\",\n      checked: \"checked\",\n      disabled: \"disabled\",\n      reverse: \"reverse\"\n    },\n    outputs: {\n      change: \"change\"\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([UI_SWITCH_CONTROL_VALUE_ACCESSOR])],\n    decls: 4,\n    vars: 20,\n    consts: [[1, \"switch\"], [\"type\", \"checkbox\", \"id\", \"enabled\", \"name\", \"enabled\", \"aria-invalid\", \"false\", 2, \"display\", \"none\", 3, \"checked\"], [\"class\", \"switch-text\", 4, \"ngIf\"], [1, \"switch-text\"], [1, \"on\", 3, \"innerHtml\"], [1, \"off\", 3, \"innerHtml\"]],\n    template: function UiSwitchComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"span\", 0);\n        ɵngcc0.ɵɵelement(1, \"input\", 1);\n        ɵngcc0.ɵɵelement(2, \"small\");\n        ɵngcc0.ɵɵtemplate(3, UiSwitchComponent_span_3_Template, 3, 2, \"span\", 2);\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵstyleProp(\"background-color\", ctx.getColor())(\"border-color\", ctx.getColor(\"borderColor\"));\n        ɵngcc0.ɵɵclassProp(\"checked\", ctx.checked)(\"disabled\", ctx.disabled)(\"switch-large\", ctx.size === \"large\")(\"switch-medium\", ctx.size === \"medium\")(\"switch-small\", ctx.size === \"small\")(\"switch-labeled\", !!ctx.labelOn || !!ctx.labelOff);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"checked\", ctx.checked);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵstyleProp(\"background\", ctx.getColor(\"switchColor\"));\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", !!ctx.labelOn || !!ctx.labelOff);\n      }\n    },\n    directives: [ɵngcc1.NgIf],\n    styles: [\".switch[_ngcontent-%COMP%] {\\n    background: #f00;\\n    border: 1px solid #dfdfdf;\\n    position: relative;\\n    display: inline-block;\\n    box-sizing: content-box;\\n    overflow: visible;\\n    padding: 0;\\n    margin: 0;\\n    cursor: pointer;\\n    box-shadow: rgb(223, 223, 223) 0 0 0 0 inset;\\n    transition: 0.3s ease-out all;\\n    -webkit-transition: 0.3s ease-out all;\\n    }\\n\\n    small[_ngcontent-%COMP%] {\\n    border-radius: 100%;\\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    transition: 0.3s ease-out all;\\n    -webkit-transition: 0.3s ease-out all;\\n    }\\n\\n    .switch-large[_ngcontent-%COMP%] {\\n    width: 66px;\\n    height: 40px;\\n    border-radius: 40px;\\n    }\\n\\n    .switch-large[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    }\\n\\n    .switch-medium[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 30px;\\n    border-radius: 30px;\\n    }\\n\\n    .switch-medium.switch-labeled[_ngcontent-%COMP%] {\\n      width: 60px;\\n    }\\n\\n    .switch-medium[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n    }\\n\\n    .switch-small[_ngcontent-%COMP%] {\\n    width: 33px;\\n    height: 20px;\\n    border-radius: 20px;\\n    }\\n\\n    .switch-small[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n    }\\n\\n    .switch-labeled[_ngcontent-%COMP%] {\\n      cursor: pointer;\\n    }\\n\\n    .checked[_ngcontent-%COMP%] {\\n    background: rgb(100, 189, 99);\\n    border-color: rgb(100, 189, 99);\\n    }\\n\\n    .switch-large.checked[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n    left: 26px;\\n    }\\n\\n    .switch-medium.checked[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n    left: 20px;\\n    }\\n\\n    .switch-medium.switch-labeled.checked[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n      left: 30px;\\n    }\\n\\n    .switch-small.checked[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n    left: 13px;\\n    }\\n\\n    .disabled[_ngcontent-%COMP%] {\\n    opacity: .50;\\n    cursor: not-allowed;\\n    }\\n\\n    .switch[_ngcontent-%COMP%]   .switch-text[_ngcontent-%COMP%] {\\n      font-size: 13px;\\n    }\\n\\n    .switch[_ngcontent-%COMP%]   .off[_ngcontent-%COMP%] {\\n      opacity: 1;\\n      position: absolute;\\n      right: 10%;\\n      top: 25%;\\n      z-index: 0;\\n      color:#A9A9A9;\\n      transition: 0.4s ease-out all;\\n    }\\n\\n    .switch[_ngcontent-%COMP%]   .on[_ngcontent-%COMP%] {\\n      opacity:0;\\n      z-index: 0;\\n      color:#fff;\\n      position: absolute;\\n      top: 25%;\\n      left: 9%;\\n      transition: 0.4s ease-out all;\\n    }\\n\\n    .switch.checked[_ngcontent-%COMP%]   .off[_ngcontent-%COMP%] {\\n      opacity:0;\\n    }\\n\\n    .switch.checked[_ngcontent-%COMP%]   .on[_ngcontent-%COMP%] {\\n      opacity:1;\\n    }\"]\n  });\n  /** @nocollapse */\n\n  return UiSwitchComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\n\nlet UiSwitchModule = /*#__PURE__*/(() => {\n  class UiSwitchModule {}\n\n  UiSwitchModule.ɵfac = function UiSwitchModule_Factory(t) {\n    return new (t || UiSwitchModule)();\n  };\n\n  UiSwitchModule.ɵmod = /*@__PURE__*/ɵngcc0.ɵɵdefineNgModule({\n    type: UiSwitchModule\n  });\n  UiSwitchModule.ɵinj = /*@__PURE__*/ɵngcc0.ɵɵdefineInjector({\n    imports: [[CommonModule, FormsModule], FormsModule]\n  });\n  /** @nocollapse */\n\n  return UiSwitchModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(UiSwitchModule, {\n    declarations: function () {\n      return [UiSwitchComponent];\n    },\n    imports: function () {\n      return [CommonModule, FormsModule];\n    },\n    exports: function () {\n      return [FormsModule, UiSwitchComponent];\n    }\n  });\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { UiSwitchComponent, UiSwitchModule }; //# sourceMappingURL=ui-switch.es2015.js.map", "map": null, "metadata": {}, "sourceType": "module"}