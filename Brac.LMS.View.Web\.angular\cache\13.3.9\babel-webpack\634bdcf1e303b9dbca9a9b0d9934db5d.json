{"ast": null, "code": "import { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function skipLast(skipCount) {\n  return skipCount <= 0 ? identity : operate((source, subscriber) => {\n    let ring = new Array(skipCount);\n    let seen = 0;\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      const valueIndex = seen++;\n\n      if (valueIndex < skipCount) {\n        ring[valueIndex] = value;\n      } else {\n        const index = valueIndex % skipCount;\n        const oldValue = ring[index];\n        ring[index] = value;\n        subscriber.next(oldValue);\n      }\n    }));\n    return () => {\n      ring = null;\n    };\n  });\n} //# sourceMappingURL=skipLast.js.map", "map": null, "metadata": {}, "sourceType": "module"}