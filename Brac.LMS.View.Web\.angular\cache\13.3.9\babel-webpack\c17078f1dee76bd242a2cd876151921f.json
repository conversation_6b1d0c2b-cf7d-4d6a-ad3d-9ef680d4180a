{"ast": null, "code": "import { AsyncScheduler } from './AsyncScheduler';\nexport class AsapScheduler extends AsyncScheduler {\n  flush(action) {\n    this._active = true;\n    this._scheduled = undefined;\n    const {\n      actions\n    } = this;\n    let error;\n    let index = -1;\n    action = action || actions.shift();\n    const count = actions.length;\n\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while (++index < count && (action = actions.shift()));\n\n    this._active = false;\n\n    if (error) {\n      while (++index < count && (action = actions.shift())) {\n        action.unsubscribe();\n      }\n\n      throw error;\n    }\n  }\n\n} //# sourceMappingURL=AsapScheduler.js.map", "map": null, "metadata": {}, "sourceType": "module"}