{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function window(windowBoundaries) {\n  return operate((source, subscriber) => {\n    let windowSubject = new Subject();\n    subscriber.next(windowSubject.asObservable());\n\n    const errorHandler = err => {\n      windowSubject.error(err);\n      subscriber.error(err);\n    };\n\n    source.subscribe(new OperatorSubscriber(subscriber, value => windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.next(value), () => {\n      windowSubject.complete();\n      subscriber.complete();\n    }, errorHandler));\n    windowBoundaries.subscribe(new OperatorSubscriber(subscriber, () => {\n      windowSubject.complete();\n      subscriber.next(windowSubject = new Subject());\n    }, noop, errorHandler));\n    return () => {\n      windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.unsubscribe();\n      windowSubject = null;\n    };\n  });\n} //# sourceMappingURL=window.js.map", "map": null, "metadata": {}, "sourceType": "module"}