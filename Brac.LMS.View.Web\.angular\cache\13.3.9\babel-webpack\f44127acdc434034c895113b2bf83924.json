{"ast": null, "code": "import { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function map(project, thisArg) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      subscriber.next(project.call(thisArg, value, index++));\n    }));\n  });\n} //# sourceMappingURL=map.js.map", "map": null, "metadata": {}, "sourceType": "module"}