{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { CourseMockTestRoutes } from './course-mock-test.routing';\nimport { AccordionModule } from 'ngx-bootstrap/accordion';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { UiSwitchModule } from 'ngx-toggle-switch';\nimport { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-bootstrap/accordion\";\nimport * as i3 from \"ngx-smart-modal\";\nexport let CourseMockTestModule = /*#__PURE__*/(() => {\n  class CourseMockTestModule {}\n\n  CourseMockTestModule.ɵfac = function CourseMockTestModule_Factory(t) {\n    return new (t || CourseMockTestModule)();\n  };\n\n  CourseMockTestModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CourseMockTestModule\n  });\n  CourseMockTestModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [NgxSmartModalService],\n    imports: [[CommonModule, RouterModule.forChild(CourseMockTestRoutes), SharedModule, WebLayoutModule, AccordionModule.forRoot(), DragDropModule, UiSwitchModule, NgxSmartModalModule.forRoot()]]\n  });\n  return CourseMockTestModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}