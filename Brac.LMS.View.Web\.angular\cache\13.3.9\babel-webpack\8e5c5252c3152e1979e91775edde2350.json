{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { RouterModule } from '@angular/router';\nimport { AppRoutes } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { LocationStrategy, PathLocationStrategy } from '@angular/common';\nimport { ErrorInterceptor } from './_helpers/error.interceptor';\nimport { ToastrModule } from 'ngx-toastr';\nimport { SharedModule } from './shared/shared.module';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { NgxScrollTopModule } from 'ngx-scrolltop';\nimport { ConfirmService } from './_helpers/confirm-dialog/confirm.service';\nimport { environment } from 'src/environments/environment';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { ServiceWorkerModule } from '@angular/service-worker';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"@angular/service-worker\"; //import { AllCoursesComponent } from './all-courses/all-courses.component';\n\nexport let AppModule = /*#__PURE__*/(() => {\n  class AppModule {}\n\n  AppModule.ɵfac = function AppModule_Factory(t) {\n    return new (t || AppModule)();\n  };\n\n  AppModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppModule,\n    bootstrap: [AppComponent]\n  });\n  AppModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [ConfirmService, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: ErrorInterceptor,\n      multi: true\n    }, {\n      provide: LocationStrategy,\n      useClass: PathLocationStrategy\n    }],\n    imports: [[BrowserModule.withServerTransition({\n      appId: 'serverApp'\n    }), BrowserAnimationsModule, RouterModule.forRoot(AppRoutes), // NgxPaginationModule,\n    HttpClientModule, SharedModule, ToastrModule.forRoot({\n      preventDuplicates: true,\n      countDuplicates: true\n    }), ReactiveFormsModule, NgxScrollTopModule, MatSlideToggleModule, MatExpansionModule, ServiceWorkerModule.register('ngsw-worker.js', {\n      enabled: environment.production,\n      // Register the ServiceWorker as soon as the application is stable\n      // or after 30 seconds (whichever comes first).\n      registrationStrategy: 'registerWhenStable:30000'\n    })]]\n  });\n  return AppModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}