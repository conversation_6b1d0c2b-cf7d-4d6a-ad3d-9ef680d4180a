{"ast": null, "code": "import { __awaiter, __decorate } from \"tslib\";\nimport { Validators } from '@angular/forms';\nimport { environment } from '../../environments/environment';\nimport { NavigationEnd } from '@angular/router';\nimport { BlockUI } from 'ng-block-ui';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { Timer } from '../_models/timer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"ngx-bootstrap/modal\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"ng-block-ui\";\nimport * as i8 from \"ngx-extended-pdf-viewer\";\nimport * as i9 from \"ngx-bootstrap/tabs\";\nimport * as i10 from \"ngx-bootstrap/rating\";\nimport * as i11 from \"@ng-select/ng-select\";\nimport * as i12 from \"ngx-bootstrap/accordion\";\nimport * as i13 from \"@angular/flex-layout/extended\";\nimport * as i14 from \"../_helpers/safe-pipe\";\nimport * as i15 from \"ngx-moment\";\nconst _c0 = [\"courseTabs\"];\nconst _c1 = [\"commentButton\"];\n\nfunction CourseDetailsComponent_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵelementStart(3, \"span\", 15);\n    i0.ɵɵtext(4, \"Course\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.courseData.Title, \" \");\n  }\n}\n\nfunction CourseDetailsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelementStart(1, \"h2\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 55);\n    i0.ɵɵelementStart(4, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_div_12_div_1_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return ctx_r24.onSelectContent(ctx_r24.selectedIndex - 1);\n    });\n    i0.ɵɵelement(5, \"i\", 57);\n    i0.ɵɵtext(6, \" Previous \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_div_12_div_1_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return ctx_r26.onSelectContent(ctx_r26.selectedIndex + 1);\n    });\n    i0.ɵɵtext(8, \" Next \");\n    i0.ɵɵelement(9, \"i\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r8.selectedContent.Title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r8.selectedIndex === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r8.selectedIndex === ctx_r8.courseData.Contents.length - 1 || ctx_r8.courseData.Contents[ctx_r8.selectedIndex + 1].Restricted);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_container_2_video_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"video\", 62);\n    i0.ɵɵlistener(\"loadedmetadata\", function CourseDetailsComponent_div_12_ng_container_2_video_1_Template_video_loadedmetadata_0_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(3);\n      return ctx_r30.onLoadedMetadata($event);\n    })(\"timeupdate\", function CourseDetailsComponent_div_12_ng_container_2_video_1_Template_video_timeupdate_0_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r32 = i0.ɵɵnextContext(3);\n      return ctx_r32.onTimeUpdate($event);\n    })(\"seeking\", function CourseDetailsComponent_div_12_ng_container_2_video_1_Template_video_seeking_0_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r33 = i0.ɵɵnextContext(3);\n      return ctx_r33.onSeeking($event);\n    })(\"pause\", function CourseDetailsComponent_div_12_ng_container_2_video_1_Template_video_pause_0_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r34 = i0.ɵɵnextContext(3);\n      return ctx_r34.onVideoPause($event);\n    });\n    i0.ɵɵelement(1, \"source\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"src\", \"\", ctx_r27.mediaBaseUrl, \"\", ctx_r27.vidObj.FilePath, \"\", i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_container_2_iframe_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"iframe\", 64, 65);\n    i0.ɵɵpipe(2, \"safe\");\n  }\n\n  if (rf & 2) {\n    const _r35 = i0.ɵɵreference(1);\n\n    const ctx_r28 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"height\", _r35.offsetWidth * 0.564, \"px\");\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind2(2, 3, \"https://www.youtube.com/embed/\" + ctx_r28.vidObj.YoutubeID + \"?rel=0&modestbranding=1&iv_load_policy=3&cc_load_policy=0&autoplay=1&showsearch=0&showinfo=0&autohide=2\", \"resourceUrl\"), i0.ɵɵsanitizeResourceUrl);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_container_2_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelementStart(1, \"div\", 70);\n    i0.ɵɵelementStart(2, \"div\", 71);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 72);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 73);\n    i0.ɵɵelementStart(7, \"div\", 74);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 75);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 76);\n    i0.ɵɵelementStart(12, \"div\", 77);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 78);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r36.timer.hourString().split(\"\")[0]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r36.timer.hourString().split(\"\")[1]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r36.timer.minuteString().split(\"\")[0]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r36.timer.minuteString().split(\"\")[1]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r36.timer.secondString().split(\"\")[0]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r36.timer.secondString().split(\"\")[1]);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_container_2_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵelementStart(1, \"ngx-extended-pdf-viewer\", 80);\n    i0.ɵɵlistener(\"srcChange\", function CourseDetailsComponent_div_12_ng_container_2_div_3_div_2_Template_ngx_extended_pdf_viewer_srcChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext(4);\n      return ctx_r39.pdfSrc = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r37.pdfSrc)(\"zoom\", \"page-width\")(\"showPrintButton\", false)(\"showRotateButton\", false)(\"showOpenFileButton\", false)(\"showDownloadButton\", ctx_r37.selectedContent.CanDownload)(\"showBookmarkButton\", true)(\"showSecondaryToolbarButton\", false)(\"useBrowserLocale\", true);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_container_2_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵelementStart(1, \"div\", 82);\n    i0.ɵɵelementStart(2, \"p\", 83);\n    i0.ɵɵtext(3, \" This document can't preview here. You may download the document. Please click below button to download the document. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_div_12_ng_container_2_div_3_div_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext(4);\n      return ctx_r41.downloadDoc();\n    });\n    i0.ɵɵelement(5, \"i\", 85);\n    i0.ɵɵtext(6, \" Download \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_container_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CourseDetailsComponent_div_12_ng_container_2_div_3_div_1_Template, 16, 6, \"div\", 66);\n    i0.ɵɵtemplate(2, CourseDetailsComponent_div_12_ng_container_2_div_3_div_2_Template, 2, 9, \"div\", 67);\n    i0.ɵɵtemplate(3, CourseDetailsComponent_div_12_ng_container_2_div_3_div_3_Template, 7, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.timer && ctx_r29.timer.timeLeft > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.selectedContent.Type == \"Document\" && ctx_r29.docObj.PDF);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r29.docObj.PDF);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CourseDetailsComponent_div_12_ng_container_2_video_1_Template, 2, 2, \"video\", 59);\n    i0.ɵɵtemplate(2, CourseDetailsComponent_div_12_ng_container_2_iframe_2_Template, 3, 6, \"iframe\", 60);\n    i0.ɵɵtemplate(3, CourseDetailsComponent_div_12_ng_container_2_div_3_Template, 4, 3, \"div\", 61);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.vidObj && !ctx_r9.vidObj.YoutubeID);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.vidObj && ctx_r9.vidObj.YoutubeID);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.docObj);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelement(1, \"img\", 87);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"src\", \"\", ctx_r11.mediaBaseUrl, \"\", ctx_r11.courseData.ImagePath, \"\", i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Overview \");\n  }\n}\n\nfunction CourseDetailsComponent_div_12_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_div_12_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r43 = i0.ɵɵnextContext(2);\n\n      const _r6 = i0.ɵɵreference(17);\n\n      return ctx_r43.openFeedBackModal(_r6);\n    });\n    i0.ɵɵelement(1, \"i\", 89);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r14.courseData.FeedbackGiven);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.courseData.FeedbackGiven ? \"Feedback Given\" : \"Give Your Feedback\", \" \");\n  }\n}\n\nfunction CourseDetailsComponent_div_12_i_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 90);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_div_12_i_36_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return ctx_r45.createBookmarkOrUnbookmark();\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_12_i_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 91);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_div_12_i_37_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext(2);\n      return ctx_r47.createBookmarkOrUnbookmark();\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Discussion \");\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 112);\n    i0.ɵɵtext(1, \" Type is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtemplate(1, CourseDetailsComponent_div_12_ng_template_54_div_12_span_1_Template, 2, 0, \"span\", 111);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r49 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r49.f[\"DiscussionType\"].errors[\"required\"]);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_13_div_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 112);\n    i0.ɵɵtext(1, \" Video is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_13_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtemplate(1, CourseDetailsComponent_div_12_ng_template_54_div_13_div_4_span_1_Template, 2, 0, \"span\", 111);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r57 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r57.f[\"MaterialId\"].errors[\"required\"]);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelementStart(1, \"label\", 97);\n    i0.ɵɵtext(2, \"Video\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ng-select\", 113);\n    i0.ɵɵtemplate(4, CourseDetailsComponent_div_12_ng_template_54_div_13_div_4_Template, 2, 1, \"div\", 100);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r50 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r50.MaterialList);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r50.submitted && ctx_r50.f[\"MaterialId\"].errors);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_14_div_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 112);\n    i0.ɵɵtext(1, \" Document is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtemplate(1, CourseDetailsComponent_div_12_ng_template_54_div_14_div_4_span_1_Template, 2, 0, \"span\", 111);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r59.f[\"MaterialId\"].errors[\"required\"]);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelementStart(1, \"label\", 114);\n    i0.ɵɵtext(2, \" Document \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ng-select\", 113);\n    i0.ɵɵtemplate(4, CourseDetailsComponent_div_12_ng_template_54_div_14_div_4_Template, 2, 1, \"div\", 100);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r51.MaterialList);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r51.submitted && ctx_r51.f[\"MaterialId\"].errors);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_15_div_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 112);\n    i0.ɵɵtext(1, \" Mock test is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_15_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtemplate(1, CourseDetailsComponent_div_12_ng_template_54_div_15_div_4_span_1_Template, 2, 0, \"span\", 111);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r61 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r61.f[\"MockTestId\"].errors[\"required\"]);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115);\n    i0.ɵɵelementStart(1, \"label\", 116);\n    i0.ɵɵtext(2, \" Mock Test Test \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ng-select\", 117);\n    i0.ɵɵtemplate(4, CourseDetailsComponent_div_12_ng_template_54_div_15_div_4_Template, 2, 1, \"div\", 100);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r52.MaterialList);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r52.submitted && ctx_r52.f[\"MockTestId\"].errors);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_16_div_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 112);\n    i0.ɵɵtext(1, \" Certificate test is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtemplate(1, CourseDetailsComponent_div_12_ng_template_54_div_16_div_4_span_1_Template, 2, 0, \"span\", 111);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r63 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r63.f[\"ExamId\"].errors[\"required\"]);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115);\n    i0.ɵɵelementStart(1, \"label\", 116);\n    i0.ɵɵtext(2, \" Certification Test \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ng-select\", 118);\n    i0.ɵɵtemplate(4, CourseDetailsComponent_div_12_ng_template_54_div_16_div_4_Template, 2, 1, \"div\", 100);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r53.MaterialList);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.submitted && ctx_r53.f[\"ExamId\"].errors);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_23_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 112);\n    i0.ɵɵtext(1, \" Comment is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtemplate(1, CourseDetailsComponent_div_12_ng_template_54_div_23_span_1_Template, 2, 0, \"span\", 111);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r54.f[\"Comment\"].errors[\"required\"]);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵelementStart(1, \"div\");\n    i0.ɵɵelementStart(2, \"form\", 93);\n    i0.ɵɵelement(3, \"input\", 94);\n    i0.ɵɵelement(4, \"input\", 95);\n    i0.ɵɵelementStart(5, \"div\", 9);\n    i0.ɵɵelementStart(6, \"div\", 96);\n    i0.ɵɵelementStart(7, \"label\", 97);\n    i0.ɵɵtext(8, \" Comment On\");\n    i0.ɵɵelementStart(9, \"sup\", 98);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"ng-select\", 99);\n    i0.ɵɵlistener(\"change\", function CourseDetailsComponent_div_12_ng_template_54_Template_ng_select_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext(2);\n      return ctx_r66.onChangeCourse($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, CourseDetailsComponent_div_12_ng_template_54_div_12_Template, 2, 1, \"div\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, CourseDetailsComponent_div_12_ng_template_54_div_13_Template, 5, 4, \"div\", 101);\n    i0.ɵɵtemplate(14, CourseDetailsComponent_div_12_ng_template_54_div_14_Template, 5, 4, \"div\", 101);\n    i0.ɵɵtemplate(15, CourseDetailsComponent_div_12_ng_template_54_div_15_Template, 5, 4, \"div\", 102);\n    i0.ɵɵtemplate(16, CourseDetailsComponent_div_12_ng_template_54_div_16_Template, 5, 4, \"div\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 103);\n    i0.ɵɵelementStart(18, \"label\", 104);\n    i0.ɵɵtext(19, \" Your Comment \");\n    i0.ɵɵelementStart(20, \"sup\", 98);\n    i0.ɵɵtext(21, \"*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"textarea\", 105);\n    i0.ɵɵtemplate(23, CourseDetailsComponent_div_12_ng_template_54_div_23_Template, 2, 1, \"div\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 106);\n    i0.ɵɵelementStart(25, \"div\", 107);\n    i0.ɵɵelementStart(26, \"button\", 108, 46);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_div_12_ng_template_54_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r68 = i0.ɵɵnextContext(2);\n      return ctx_r68.closeFeedbackForm();\n    });\n    i0.ɵɵtext(28, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_div_12_ng_template_54_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      return ctx_r69.onFormSubmit();\n    });\n    i0.ɵɵtext(30, \" Post \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r20.discussionForm);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r20.courseId);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"clearable\", true)(\"clearOnBackspace\", false)(\"items\", ctx_r20.DiscussionTypeList);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r20.submitted && ctx_r20.f[\"DiscussionType\"].errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r20.f[\"CourseId\"] && ctx_r20.f[\"DiscussionType\"].value == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r20.f[\"CourseId\"] && ctx_r20.f[\"DiscussionType\"].value == 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r20.f[\"CourseId\"] && ctx_r20.f[\"DiscussionType\"].value == 2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r20.f[\"CourseId\"] && ctx_r20.f[\"DiscussionType\"].value == 3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r20.submitted && ctx_r20.f[\"Comment\"].errors);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_div_57_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 131);\n  }\n\n  if (rf & 2) {\n    const item_r70 = i0.ɵɵnextContext().$implicit;\n    const ctx_r71 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate2(\"src\", \"\", ctx_r71.mediaBaseUrl, \"\", item_r70.ImagePath, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r70.Commentator);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_div_57_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 132);\n  }\n\n  if (rf & 2) {\n    const item_r70 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r70.Commentator);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_div_57_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵelement(1, \"i\", 133);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r70 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r70.Material, \" \");\n  }\n}\n\nfunction CourseDetailsComponent_div_12_div_57_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵelement(1, \"i\", 134);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r70 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r70.MockTest, \" \");\n  }\n}\n\nfunction CourseDetailsComponent_div_12_div_57_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵelement(1, \"i\", 135);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r70 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r70.Material, \" \");\n  }\n}\n\nfunction CourseDetailsComponent_div_12_div_57_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵelement(1, \"i\", 136);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r70 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r70.Exam, \" \");\n  }\n}\n\nfunction CourseDetailsComponent_div_12_div_57_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵelement(1, \"i\", 136);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r77 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r77.courseData.Title, \" \");\n  }\n}\n\nfunction CourseDetailsComponent_div_12_div_57_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r86 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 137);\n    i0.ɵɵelementStart(1, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_div_12_div_57_span_22_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r86);\n      const item_r70 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵnextContext();\n\n      const _r19 = i0.ɵɵreference(55);\n\n      const ctx_r85 = i0.ɵɵnextContext();\n      return ctx_r85.editDiscussion(_r19, item_r70.Id);\n    });\n    i0.ɵɵelement(2, \"i\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_12_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelementStart(1, \"div\", 119);\n    i0.ɵɵelementStart(2, \"div\", 120);\n    i0.ɵɵtemplate(3, CourseDetailsComponent_div_12_div_57_img_3_Template, 1, 3, \"img\", 121);\n    i0.ɵɵtemplate(4, CourseDetailsComponent_div_12_div_57_img_4_Template, 1, 1, \"img\", 122);\n    i0.ɵɵelementStart(5, \"div\", 123);\n    i0.ɵɵelementStart(6, \"h4\", 124);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 125);\n    i0.ɵɵtemplate(9, CourseDetailsComponent_div_12_div_57_div_9_Template, 3, 1, \"div\", 126);\n    i0.ɵɵtemplate(10, CourseDetailsComponent_div_12_div_57_div_10_Template, 3, 1, \"div\", 126);\n    i0.ɵɵtemplate(11, CourseDetailsComponent_div_12_div_57_div_11_Template, 3, 1, \"div\", 126);\n    i0.ɵɵtemplate(12, CourseDetailsComponent_div_12_div_57_div_12_Template, 3, 1, \"div\", 126);\n    i0.ɵɵtemplate(13, CourseDetailsComponent_div_12_div_57_div_13_Template, 3, 1, \"div\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"br\");\n    i0.ɵɵelementStart(15, \"span\", 125);\n    i0.ɵɵelementStart(16, \"div\", 127);\n    i0.ɵɵelement(17, \"i\", 128);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"amTimeAgo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 129);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, CourseDetailsComponent_div_12_div_57_span_22_Template, 3, 0, \"span\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r70 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r70.ImagePath);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r70.ImagePath);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r70.Commentator, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r70.DisscussionType == \"Video\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r70.DisscussionType == \"MockTest\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r70.DisscussionType == \"Document\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r70.DisscussionType == \"CertificationTest\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r70.DisscussionType == \"Course\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 11, item_r70.CommentTime), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r70.Comment);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r70.IsSelfComment);\n  }\n}\n\nfunction CourseDetailsComponent_div_12_ng_template_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" FAQ \");\n  }\n}\n\nfunction CourseDetailsComponent_div_12_accordion_group_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"accordion-group\", 140);\n    i0.ɵɵelementStart(1, \"button\", 141);\n    i0.ɵɵelementStart(2, \"div\", 142);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 143);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r88 = ctx.$implicit;\n    const i_r89 = ctx.index;\n    i0.ɵɵproperty(\"isOpen\", i_r89 == 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Question: \", item_r88.Question, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r88.Answer, \" \");\n  }\n}\n\nconst _c2 = function () {\n  return [\"Not satisfied at all\", \"It was okay\", \"Satisfactory\", \"Very Good\", \"Great\"];\n};\n\nfunction CourseDetailsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r91 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, CourseDetailsComponent_div_12_div_1_Template, 10, 3, \"div\", 18);\n    i0.ɵɵtemplate(2, CourseDetailsComponent_div_12_ng_container_2_Template, 4, 3, \"ng-container\", 19);\n    i0.ɵɵtemplate(3, CourseDetailsComponent_div_12_ng_template_3_Template, 2, 2, \"ng-template\", null, 20, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(5, \"div\", 21);\n    i0.ɵɵelementStart(6, \"tabset\", 22, 23);\n    i0.ɵɵelementStart(8, \"tab\", 24);\n    i0.ɵɵlistener(\"selectTab\", function CourseDetailsComponent_div_12_Template_tab_selectTab_8_listener($event) {\n      i0.ɵɵrestoreView(_r91);\n      const ctx_r90 = i0.ɵɵnextContext();\n      return ctx_r90.changeTab(\"Overview\", $event);\n    });\n    i0.ɵɵtemplate(9, CourseDetailsComponent_div_12_ng_template_9_Template, 1, 0, \"ng-template\", 25);\n    i0.ɵɵelementStart(10, \"div\", 9);\n    i0.ɵɵelementStart(11, \"div\", 26);\n    i0.ɵɵelementStart(12, \"div\", 27);\n    i0.ɵɵelementStart(13, \"h1\", 28);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 9);\n    i0.ɵɵelementStart(16, \"div\", 29);\n    i0.ɵɵelementStart(17, \"rating\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseDetailsComponent_div_12_Template_rating_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r91);\n      const ctx_r92 = i0.ɵɵnextContext();\n      return ctx_r92.courseData.Rating = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 31);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, CourseDetailsComponent_div_12_button_22_Template, 3, 2, \"button\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 33);\n    i0.ɵɵelementStart(24, \"span\", 34);\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵelementStart(26, \"small\");\n    i0.ɵɵtext(27, \"Total Lectures:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"br\");\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵelementStart(31, \"small\");\n    i0.ɵɵtext(32, \"Total Durations:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 35);\n    i0.ɵɵelementStart(35, \"div\", 36);\n    i0.ɵɵtemplate(36, CourseDetailsComponent_div_12_i_36_Template, 1, 0, \"i\", 37);\n    i0.ɵɵtemplate(37, CourseDetailsComponent_div_12_i_37_Template, 1, 0, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 9);\n    i0.ɵɵelementStart(39, \"div\", 39);\n    i0.ɵɵelement(40, \"p\", 40);\n    i0.ɵɵpipe(41, \"safe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"tab\", 24);\n    i0.ɵɵlistener(\"selectTab\", function CourseDetailsComponent_div_12_Template_tab_selectTab_42_listener($event) {\n      i0.ɵɵrestoreView(_r91);\n      const ctx_r93 = i0.ɵɵnextContext();\n      return ctx_r93.changeTab(\"Discussion\", $event);\n    });\n    i0.ɵɵtemplate(43, CourseDetailsComponent_div_12_ng_template_43_Template, 1, 0, \"ng-template\", 25);\n    i0.ɵɵelementStart(44, \"div\", 9);\n    i0.ɵɵelementStart(45, \"div\", 41);\n    i0.ɵɵelementStart(46, \"div\", 27);\n    i0.ɵɵelementStart(47, \"h1\", 42);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 43);\n    i0.ɵɵelementStart(50, \"div\", 44);\n    i0.ɵɵelementStart(51, \"a\", 45, 46);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_div_12_Template_a_click_51_listener() {\n      i0.ɵɵrestoreView(_r91);\n\n      const _r19 = i0.ɵɵreference(55);\n\n      const ctx_r94 = i0.ɵɵnextContext();\n      return ctx_r94.openNewFeedbackForm(_r19);\n    });\n    i0.ɵɵtext(53, \"Write A Comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, CourseDetailsComponent_div_12_ng_template_54_Template, 31, 11, \"ng-template\", null, 47, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(56, \"div\", 9);\n    i0.ɵɵtemplate(57, CourseDetailsComponent_div_12_div_57_Template, 23, 13, \"div\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"tab\", 24);\n    i0.ɵɵlistener(\"selectTab\", function CourseDetailsComponent_div_12_Template_tab_selectTab_58_listener($event) {\n      i0.ɵɵrestoreView(_r91);\n      const ctx_r95 = i0.ɵɵnextContext();\n      return ctx_r95.changeTab(\"FAQ\", $event);\n    });\n    i0.ɵɵtemplate(59, CourseDetailsComponent_div_12_ng_template_59_Template, 1, 0, \"ng-template\", 25);\n    i0.ɵɵelementStart(60, \"div\", 9);\n    i0.ɵɵelementStart(61, \"div\", 49);\n    i0.ɵɵelementStart(62, \"div\", 50);\n    i0.ɵɵelementStart(63, \"accordion\", 51);\n    i0.ɵɵtemplate(64, CourseDetailsComponent_div_12_accordion_group_64_Template, 6, 3, \"accordion-group\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r10 = i0.ɵɵreference(4);\n\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedContent);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedContent)(\"ngIfElse\", _r10);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"justified\", true);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.courseData.Title, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.courseData.Rating)(\"titles\", i0.ɵɵpureFunction0(24, _c2))(\"readonly\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.courseData.Rating, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r2.courseData.NoOfRating, \") \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.courseData.CertificateAchieved);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.courseData.TotalLecture, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.courseData.TotalDuration, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.courseData.Bookmarked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.courseData.Bookmarked);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(41, 21, ctx_r2.courseData.Description, \"html\"), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.courseDiscussionList.length, \" Comments \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.courseDiscussionList);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"isAnimated\", true)(\"closeOthers\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.courseFAQList);\n  }\n}\n\nfunction CourseDetailsComponent_div_13_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 161);\n    i0.ɵɵelement(1, \"i\", 162);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_13_div_3_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 161);\n    i0.ɵɵelement(1, \"i\", 163);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_13_div_3_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 161);\n    i0.ɵɵelement(1, \"i\", 164);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_13_div_3_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 161);\n    i0.ɵɵelement(1, \"i\", 165);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_13_div_3_p_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r97 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, item_r97.VideoDurationSecond * 1000, \"mm:ss\"), \" \");\n  }\n}\n\nfunction CourseDetailsComponent_div_13_div_3_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 166);\n    i0.ɵɵtext(1, \" Mock Test \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_13_div_3_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 166);\n    i0.ɵɵtext(1, \" Document \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_13_div_3_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 167);\n    i0.ɵɵtext(1, \" Final Certification Exam \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_13_div_3_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r112 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 168);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_div_13_div_3_div_16_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r112);\n      const item_r97 = i0.ɵɵnextContext().$implicit;\n      const ctx_r111 = i0.ɵɵnextContext(2);\n\n      const _r4 = i0.ɵɵreference(15);\n\n      return ctx_r111.openModalResource(_r4, item_r97);\n    });\n    i0.ɵɵelement(1, \"i\", 169);\n    i0.ɵɵtext(2, \" Resources \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsComponent_div_13_div_3_i_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 170);\n  }\n}\n\nfunction CourseDetailsComponent_div_13_div_3_i_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 171);\n  }\n}\n\nconst _c3 = function (a0) {\n  return {\n    \"content-div-active\": a0\n  };\n};\n\nfunction CourseDetailsComponent_div_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r115 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 147);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_div_13_div_3_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r115);\n      const i_r98 = restoredCtx.index;\n      const ctx_r114 = i0.ɵɵnextContext(2);\n      return ctx_r114.onSelectContent(i_r98);\n    });\n    i0.ɵɵelementStart(1, \"div\", 148);\n    i0.ɵɵelementStart(2, \"div\", 149);\n    i0.ɵɵelementStart(3, \"div\", 150);\n    i0.ɵɵtemplate(4, CourseDetailsComponent_div_13_div_3_span_4_Template, 2, 0, \"span\", 151);\n    i0.ɵɵtemplate(5, CourseDetailsComponent_div_13_div_3_span_5_Template, 2, 0, \"span\", 151);\n    i0.ɵɵtemplate(6, CourseDetailsComponent_div_13_div_3_span_6_Template, 2, 0, \"span\", 151);\n    i0.ɵɵtemplate(7, CourseDetailsComponent_div_13_div_3_span_7_Template, 2, 0, \"span\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 152);\n    i0.ɵɵelementStart(9, \"div\", 153);\n    i0.ɵɵelementStart(10, \"div\", 154);\n    i0.ɵɵtext(11);\n    i0.ɵɵtemplate(12, CourseDetailsComponent_div_13_div_3_p_12_Template, 3, 4, \"p\", 155);\n    i0.ɵɵtemplate(13, CourseDetailsComponent_div_13_div_3_p_13_Template, 2, 0, \"p\", 155);\n    i0.ɵɵtemplate(14, CourseDetailsComponent_div_13_div_3_p_14_Template, 2, 0, \"p\", 155);\n    i0.ɵɵtemplate(15, CourseDetailsComponent_div_13_div_3_p_15_Template, 2, 0, \"p\", 156);\n    i0.ɵɵtemplate(16, CourseDetailsComponent_div_13_div_3_div_16_Template, 3, 0, \"div\", 157);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 158);\n    i0.ɵɵtemplate(18, CourseDetailsComponent_div_13_div_3_i_18_Template, 1, 0, \"i\", 159);\n    i0.ɵɵtemplate(19, CourseDetailsComponent_div_13_div_3_i_19_Template, 1, 0, \"i\", 160);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r97 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c3, item_r97.Selected));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r97.Type == \"Video\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r97.Type == \"MockTest\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r97.Type == \"Document\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r97.Type == \"CertificateTest\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r97.Title, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r97.Type == \"Video\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r97.Type == \"MockTest\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r97.Type == \"Document\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r97.Type == \"CertificateTest\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r97.Resources.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r97.Restricted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r97.Studied);\n  }\n}\n\nfunction CourseDetailsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144);\n    i0.ɵɵelementStart(1, \"h1\", 145);\n    i0.ɵɵtext(2, \"Contents\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CourseDetailsComponent_div_13_div_3_Template, 20, 15, \"div\", 146);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.courseData.Contents);\n  }\n}\n\nfunction CourseDetailsComponent_ng_template_14_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelementStart(1, \"th\", 186);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵelementStart(8, \"div\", 187);\n    i0.ɵɵelementStart(9, \"a\", 188);\n    i0.ɵɵelement(10, \"i\", 189);\n    i0.ɵɵtext(11, \" Download \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r117 = ctx.$implicit;\n    const i_r118 = ctx.index;\n    const ctx_r116 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r118 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r117.Title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r116.formatFileSize(item_r117.FileSizeKb));\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate2(\"href\", \"\", ctx_r116.mediaBaseUrl, \"\", item_r117.FilePath, \"\", i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction CourseDetailsComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r120 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 172);\n    i0.ɵɵelementStart(1, \"h4\", 173);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 174);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_ng_template_14_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r120);\n      const ctx_r119 = i0.ɵɵnextContext();\n      return ctx_r119.modalHideResource();\n    });\n    i0.ɵɵelement(4, \"i\", 175);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 176);\n    i0.ɵɵelementStart(6, \"div\", 177);\n    i0.ɵɵelementStart(7, \"div\", 178);\n    i0.ɵɵelementStart(8, \"div\", 82);\n    i0.ɵɵelementStart(9, \"table\", 179);\n    i0.ɵɵelementStart(10, \"thead\");\n    i0.ɵɵelementStart(11, \"tr\");\n    i0.ɵɵelementStart(12, \"th\", 180);\n    i0.ɵɵtext(13, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 180);\n    i0.ɵɵtext(15, \"Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 180);\n    i0.ɵɵtext(17, \"File Size\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 180);\n    i0.ɵɵtext(19, \"Download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"tbody\");\n    i0.ɵɵtemplate(21, CourseDetailsComponent_ng_template_14_tr_21_Template, 12, 5, \"tr\", 181);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 182);\n    i0.ɵɵelementStart(23, \"div\", 183);\n    i0.ɵɵelementStart(24, \"button\", 184);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_ng_template_14_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r120);\n      const ctx_r121 = i0.ɵɵnextContext();\n      return ctx_r121.modalHideResource();\n    });\n    i0.ɵɵelement(25, \"i\", 185);\n    i0.ɵɵtext(26, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.resourceParent.Title, \" || Resources \");\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.resourceParent.Resources);\n  }\n}\n\nconst _c4 = function () {\n  return {\n    standalone: true\n  };\n};\n\nfunction CourseDetailsComponent_ng_template_16_div_19_div_5_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r137 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 215);\n    i0.ɵɵelementStart(1, \"input\", 216);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseDetailsComponent_ng_template_16_div_19_div_5_div_7_div_2_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r137);\n      const question_r125 = i0.ɵɵnextContext(2).$implicit;\n      return question_r125.Answers = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 217);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const radio_r133 = ctx.$implicit;\n    const r_r134 = ctx.index;\n    const question_r125 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"id\", \"q-ra-\", question_r125.Id, \"-\", r_r134, \"\");\n    i0.ɵɵpropertyInterpolate1(\"name\", \"radio-\", question_r125.Id, \"\");\n    i0.ɵɵproperty(\"value\", radio_r133)(\"ngModel\", question_r125.Answers)(\"ngModelOptions\", i0.ɵɵpureFunction0(9, _c4));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"for\", \"q-ra-\", question_r125.Id, \"-\", r_r134, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(radio_r133);\n  }\n}\n\nfunction CourseDetailsComponent_ng_template_16_div_19_div_5_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"div\", 213);\n    i0.ɵɵtemplate(2, CourseDetailsComponent_ng_template_16_div_19_div_5_div_7_div_2_Template, 4, 10, \"div\", 214);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const question_r125 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", question_r125.Options);\n  }\n}\n\nfunction CourseDetailsComponent_ng_template_16_div_19_div_5_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r145 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 219);\n    i0.ɵɵelementStart(1, \"input\", 220);\n    i0.ɵɵlistener(\"change\", function CourseDetailsComponent_ng_template_16_div_19_div_5_div_8_div_1_Template_input_change_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const ckbox_r141 = restoredCtx.$implicit;\n      const question_r125 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r143 = i0.ɵɵnextContext(3);\n      return ctx_r143.onChangeCheckBox($event, question_r125, ckbox_r141);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 217);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ckbox_r141 = ctx.$implicit;\n    const c_r142 = ctx.index;\n    const question_r125 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"id\", \"q-ck-\", question_r125.Id, \"-\", c_r142, \"\");\n    i0.ɵɵproperty(\"checked\", question_r125.Answers.indexOf(ckbox_r141) !== -1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"for\", \"q-ck-\", question_r125.Id, \"-\", c_r142, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ckbox_r141);\n  }\n}\n\nfunction CourseDetailsComponent_ng_template_16_div_19_div_5_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CourseDetailsComponent_ng_template_16_div_19_div_5_div_8_div_1_Template, 4, 6, \"div\", 218);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const question_r125 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", question_r125.Options);\n  }\n}\n\nfunction CourseDetailsComponent_ng_template_16_div_19_div_5_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r150 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"ng-select\", 221);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseDetailsComponent_ng_template_16_div_19_div_5_div_9_Template_ng_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r150);\n      const question_r125 = i0.ɵɵnextContext().$implicit;\n      return question_r125.Answers = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const question_r125 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"clearable\", false)(\"ngModel\", question_r125.Answers)(\"ngModelOptions\", i0.ɵɵpureFunction0(5, _c4))(\"clearOnBackspace\", false)(\"items\", question_r125.Options);\n  }\n}\n\nfunction CourseDetailsComponent_ng_template_16_div_19_div_5_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r154 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"div\", 213);\n    i0.ɵɵelementStart(2, \"rating\", 222);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseDetailsComponent_ng_template_16_div_19_div_5_div_10_Template_rating_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r154);\n      const question_r125 = i0.ɵɵnextContext().$implicit;\n      return question_r125.Answers = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const question_r125 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", question_r125.Answers)(\"ngModelOptions\", i0.ɵɵpureFunction0(4, _c4))(\"titles\", i0.ɵɵpureFunction0(5, _c2))(\"readonly\", false);\n  }\n}\n\nfunction CourseDetailsComponent_ng_template_16_div_19_div_5_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r158 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"div\", 213);\n    i0.ɵɵelementStart(2, \"textarea\", 223);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseDetailsComponent_ng_template_16_div_19_div_5_div_11_Template_textarea_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r158);\n      const question_r125 = i0.ɵɵnextContext().$implicit;\n      return question_r125.Answers = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const question_r125 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", question_r125.Answers)(\"ngModelOptions\", i0.ɵɵpureFunction0(2, _c4));\n  }\n}\n\nfunction CourseDetailsComponent_ng_template_16_div_19_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 208);\n    i0.ɵɵelementStart(1, \"div\", 209);\n    i0.ɵɵelementStart(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 210);\n    i0.ɵɵelementStart(6, \"div\", 211);\n    i0.ɵɵtemplate(7, CourseDetailsComponent_ng_template_16_div_19_div_5_div_7_Template, 3, 1, \"div\", 212);\n    i0.ɵɵtemplate(8, CourseDetailsComponent_ng_template_16_div_19_div_5_div_8_Template, 2, 1, \"div\", 212);\n    i0.ɵɵtemplate(9, CourseDetailsComponent_ng_template_16_div_19_div_5_div_9_Template, 2, 6, \"div\", 212);\n    i0.ɵɵtemplate(10, CourseDetailsComponent_ng_template_16_div_19_div_5_div_10_Template, 3, 6, \"div\", 212);\n    i0.ɵɵtemplate(11, CourseDetailsComponent_ng_template_16_div_19_div_5_div_11_Template, 3, 3, \"div\", 212);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const question_r125 = ctx.$implicit;\n    const i_r126 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i_r126 + 1, \".\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", question_r125.Question, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", question_r125.QuestionType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Radio\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Checkbox\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Dropdown\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Rating\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Textbox\");\n  }\n}\n\nfunction CourseDetailsComponent_ng_template_16_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 203);\n    i0.ɵɵelementStart(1, \"div\", 204);\n    i0.ɵɵelementStart(2, \"h5\", 205);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 206);\n    i0.ɵɵtemplate(5, CourseDetailsComponent_ng_template_16_div_19_div_5_Template, 12, 8, \"div\", 207);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r123 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", item_r123.Group, \" - Feedbacks\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r123.Questions);\n  }\n}\n\nfunction CourseDetailsComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r161 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 172);\n    i0.ɵɵelementStart(1, \"h4\", 173);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 190);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_ng_template_16_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r161);\n      const ctx_r160 = i0.ɵɵnextContext();\n      return ctx_r160.modalHideFeedBack();\n    });\n    i0.ɵɵelement(4, \"i\", 175);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 176);\n    i0.ɵɵelementStart(6, \"form\", 191);\n    i0.ɵɵelementStart(7, \"div\", 192);\n    i0.ɵɵelementStart(8, \"div\", 193);\n    i0.ɵɵelementStart(9, \"label\", 194);\n    i0.ɵɵtext(10, \"Rating on course\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 193);\n    i0.ɵɵelement(12, \"rating\", 195);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 196);\n    i0.ɵɵelementStart(14, \"div\", 193);\n    i0.ɵɵelementStart(15, \"label\", 197);\n    i0.ɵɵtext(16, \"Comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 198);\n    i0.ɵɵelement(18, \"textarea\", 199);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, CourseDetailsComponent_ng_template_16_div_19_Template, 6, 2, \"div\", 200);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 182);\n    i0.ɵɵelementStart(21, \"button\", 201);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_ng_template_16_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r161);\n      const ctx_r162 = i0.ɵɵnextContext();\n      return ctx_r162.modalHideFeedBack();\n    });\n    i0.ɵɵelement(22, \"i\", 175);\n    i0.ɵɵtext(23, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function CourseDetailsComponent_ng_template_16_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r161);\n      const ctx_r163 = i0.ɵɵnextContext();\n      return ctx_r163.onSubmitFeedback();\n    });\n    i0.ɵɵelement(25, \"i\", 202);\n    i0.ɵɵtext(26, \" Submit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Feedbacks on \", ctx_r7.courseData.Title, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"formGroup\", ctx_r7.ratingForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"titles\", i0.ɵɵpureFunction0(5, _c2))(\"readonly\", false);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.feedBackQuestionList);\n  }\n}\n\nexport class CourseDetailsComponent {\n  constructor( //private ngSelect: NgSelectComponent,\n  router, cdr, _service, toastr, modalService, route, formBuilder, _location) {\n    this.router = router;\n    this.cdr = cdr;\n    this._service = _service;\n    this.toastr = toastr;\n    this.modalService = modalService;\n    this.route = route;\n    this.formBuilder = formBuilder;\n    this._location = _location;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.submitted = false;\n    this.timestamp = new Date().getTime();\n    this.courseData = null;\n    this.feedBackQuestionList = [];\n    this.rate = 0;\n    this.pdfSrc = null;\n    this.timer = new Timer();\n    this.selectedContent = null;\n    this.selectedDiscussion = null;\n    this.selectedIndex = -1;\n    this.contentStudy = null;\n    this.courseDiscussionList = [];\n    this.courseFAQList = [];\n    this.DiscussionTypeList = [{\n      Id: 0,\n      Name: 'Video'\n    }, {\n      Id: 1,\n      Name: 'Document'\n    }, {\n      Id: 2,\n      Name: 'Mock Test'\n    }, {\n      Id: 3,\n      Name: 'Certification Test'\n    }];\n    this.MaterialList = [];\n    this.ResourseList = [];\n    this.MockTestList = [];\n    this.CertificationTestList = [];\n    this.currentContentIndex = 0;\n    this.startedTime = null;\n    this.supposedCurrentTime = 0;\n    this.modalMdConfig = {\n      class: 'gray modal-md',\n      backdrop: 'static',\n      onHidden: modalRef => {\n        this.modalHideFeedBack();\n      }\n    };\n    this.modalLgConfig = {\n      class: 'gray modal-lg',\n      backdrop: 'static',\n      onHidden: modalRef => {\n        this.modalHideFeedBack();\n      }\n    };\n    this.courseId = this.route.snapshot.paramMap.get('courseId');\n    this.router.events.subscribe(evt => {\n      if (!(evt instanceof NavigationEnd)) {\n        return;\n      }\n\n      window.scrollTo(0, 0);\n    }); // =================== TAB Change Actions ==========================\n\n    let self = this;\n    window.addEventListener(\"visibilitychange\", function (event) {\n      if (self.selectedContent && self.selectedContent.Type === 'Video' && self.video) {\n        if (document.hidden) self.video.pause(); // else self.video.play();\n      } // console.log('visibilitychange: '+ JSON.stringify(document.hidden));\n\n    }); // ================ End of TAB Change Actions ======================\n  }\n\n  ngOnInit() {\n    this.ratingForm = this.formBuilder.group({\n      rating: [null, [Validators.required]],\n      comment: [null]\n    });\n    this.discussionForm = this.formBuilder.group({\n      Id: [null],\n      Comment: [null, [Validators.required, Validators.minLength(2)]],\n      CourseId: this.courseId,\n      MaterialId: [null],\n      ExamId: [null],\n      MockTestId: [null],\n      //DiscussionType: [null, [Validators.required]],\n      DiscussionType: {\n        value: null,\n        updateOn: 'change',\n        // Use a setter to execute a function when the value changes\n        setValue: function (newValue) {\n          this.patchValue(newValue);\n          this.onChangeCourse(newValue);\n        }\n      },\n      CourseDiscussionId: [null],\n      ResourceId: [null]\n    });\n    this.getCourseDetails();\n    this.getCourseDiscussions();\n    this.getCourseFAQs();\n  }\n\n  ngOnDestroy() {\n    return __awaiter(this, void 0, void 0, function* () {\n      if (this.selectedContent) switch (this.selectedContent.Type) {\n        case 'Document':\n          let diff = new Date().valueOf() - this.startedTime;\n          yield this.contentStudied(this.selectedContent.Id, Math.floor(diff / 1000));\n          break;\n\n        case 'Video':\n          yield this.contentStudied(this.selectedContent.Id, Math.floor(this.video.currentTime));\n          break;\n      }\n      if (this.timerSubscription) this.timerSubscription.unsubscribe();\n    });\n  }\n\n  get f() {\n    return this.discussionForm.controls;\n  }\n\n  downloadDoc() {\n    return __awaiter(this, void 0, void 0, function* () {\n      yield this.contentStudied(this.docObj.Id, 0);\n      yield this.updateCourseDetails();\n      var link = document.createElement('a');\n      link.href = this.mediaBaseUrl + this.docObj.FilePath;\n      link.target = '_blank';\n      link.rel = 'noopener';\n      link.download = this.docObj.Title + '.' + this.docObj.FilePath.split('.').pop();\n      link.click();\n      link.remove();\n    });\n  }\n\n  backClicked() {\n    localStorage.setItem('reload-with-page', '1');\n\n    this._location.back();\n  }\n\n  openModalResource(template, item) {\n    this.resourceParent = item;\n    this.modalRef = this.modalService.show(template, this.modalMdConfig);\n  }\n\n  openNewFeedbackForm(template) {\n    this.selectedDiscussion = null;\n    this.modalRef = this.modalService.show(template, this.modalMdConfig);\n  }\n\n  closeFeedbackForm() {\n    this.selectedDiscussion = null;\n    this.discussionForm.reset();\n    this.submitted = false;\n    this.modalService.hide();\n  }\n\n  editDiscussion(template, discussionId) {\n    if (discussionId) {\n      // edit mode\n      this._service.get('course/get-course-discussion-by-id/' + discussionId).subscribe({\n        next: res => {\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              timeOut: 2000\n            });\n            return;\n          }\n\n          for (const key in res.Data) {\n            if (res.Data.hasOwnProperty(key)) {\n              const value = res.Data[key]; //this.discussionForm.controls[key].setValue(value);\n\n              console.log(`Key: ${key}, Value: ${value}`);\n            }\n          }\n\n          let data = res.Data;\n          data.DiscussionType = res.Data.DisscussionTypeId;\n\n          if (data.MaterialId != null) {\n            this.selectedDiscussion = {\n              Id: data.MaterialId,\n              Title: data.Material\n            };\n          } else if (data.MockTestId != null) {\n            this.selectedDiscussion = {\n              Id: data.MockTestId,\n              Title: data.MockTest\n            };\n          } else if (data.ExamId != null) {\n            this.selectedDiscussion = {\n              Id: data.ExamId,\n              Title: data.Exam\n            };\n          }\n\n          this.discussionForm.get('DiscussionType').valueChanges.subscribe(newValue => {\n            this.onChangeCourse(newValue);\n          });\n          this.discussionForm.patchValue(data);\n          this.modalRef = this.modalService.show(template, this.modalMdConfig);\n        },\n        error: err => {\n          this.toastr.warning(err.Messaage || err, 'Warning!', {});\n        },\n        complete: () => {}\n      });\n    }\n  }\n\n  modalHideResource() {\n    this.modalRef.hide();\n    setTimeout(() => {\n      this.resourceParent = {};\n    }, 500);\n  }\n\n  changeTab(type, e) {\n    switch (type) {\n      case 'Discussion':\n        // this.getCourseDiscussions();\n        break;\n\n      default:\n        break;\n    }\n  }\n\n  getCourseDetails() {\n    this.blockUI.start('Loading data. Please wait...');\n\n    this._service.get('course/get-course-details/' + this.courseId).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.courseData = res.Data;\n        console.log(this.courseData);\n        this.rate = this.courseData.Rating;\n        this.courseData.Contents.forEach(element => {\n          element.Selected = false;\n        });\n        this.currentContentIndex = 0; //open next item\n\n        let autoOpenNextItem = localStorage.getItem('source-id');\n\n        if (autoOpenNextItem) {\n          let ind = Object.assign({}, this.courseData.Contents.find(x => x.Id == autoOpenNextItem));\n          const sortedList = [...this.courseData.Contents].sort((a, b) => a.Sequence - b.Sequence);\n\n          if (ind.Sequence <= sortedList[sortedList.length - 1].Sequence) {\n            this.onSelectContent(ind.Sequence);\n          }\n        }\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.blockUI.stop();\n      },\n      complete: () => {\n        this.blockUI.stop();\n      }\n    });\n  }\n\n  getCourseDiscussions() {\n    this.courseDiscussionList = [];\n\n    this._service.get('course/get-course-discussions/' + this.courseId).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.courseDiscussionList = res.Data;\n        console.log(this.courseDiscussionList);\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n      },\n      complete: () => {}\n    });\n  }\n\n  getCourseFAQs() {\n    this._service.get('course/get-faqs/' + this.courseId).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.courseFAQList = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n      },\n      complete: () => {}\n    });\n  }\n\n  formatFileSize(fileSizeKb) {\n    if (fileSizeKb >= 1048576) {\n      fileSizeKb = (fileSizeKb / 1048576).toFixed(2) + ' GB';\n    } else if (fileSizeKb >= 1024) {\n      fileSizeKb = (fileSizeKb / 1024).toFixed(2) + ' MB';\n    } else if (fileSizeKb > 1) {\n      fileSizeKb = fileSizeKb + ' KB';\n    } else {\n      fileSizeKb = '0 Kb';\n    }\n\n    return fileSizeKb;\n  }\n\n  createBookmarkOrUnbookmark() {\n    this.blockUI.start('loading...');\n\n    this._service.get('course/bookmark-or-unbookmark/' + this.courseId).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.getCourseDetails();\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.blockUI.stop();\n      },\n      complete: () => {\n        this.blockUI.stop();\n      }\n    });\n  } // async onSelectContent(i: number) {\n  //   if (this.courseData.Contents[i].Restricted) return;\n  //   if (this.selectedContent) {\n  //     if (this.selectedContent.Id === this.courseData.Contents[i].Id) return;\n  //     let study: { Current?: boolean; NextUnlock?: boolean } = null;\n  //     switch (this.selectedContent.Type) {\n  //       case 'Document':\n  //         if (this.timerSubscription) this.timerSubscription.unsubscribe();\n  //         let diff = new Date().valueOf() - this.startedTime;\n  //         study = await this.contentStudied(\n  //           this.selectedContent.Id,\n  //           Math.floor(diff / 1000)\n  //         );\n  //         break;\n  //       case 'Video':\n  //         study = await this.contentStudied(\n  //           this.selectedContent.Id,\n  //           Math.floor(this.video.currentTime)\n  //         );\n  //         // this.video.stop();\n  //         this.vidObj = null;\n  //         this.video = null;\n  //         this.supposedCurrentTime = 0;\n  //         break;\n  //     }\n  //     // if (this.selectedContent.Restricted && study.Current) {\n  //     //   let index = this.courseData.Contents.map((x) => x.Id).indexOf(\n  //     //     this.selectedContent.Id\n  //     //   );\n  //     //   if (index != -1 && index < this.courseData.Contents.length - 1){\n  //     //     let nextLockedItem = this.courseData.Contents.find((x,j)=> j>index && x.Restricted);\n  //     //     if(nextLockedItem) nextLockedItem.Restricted = !study.NextUnlock;\n  //     //   }\n  //     //   this.courseData.Contents[index].Studied = study.Current;\n  //     // }\n  //     await this.updateCourseDetails();\n  //   }\n  //   if (this.courseData.Contents[i].Restricted) {\n  //     if (this.selectedContent && this.selectedContent.Type === 'Document') {\n  //       this.startedTime = new Date().valueOf();\n  //     }\n  //     return;\n  //   }\n  //   this.selectedContent = this.courseData.Contents[i];\n  //   this.selectedIndex = i;\n  //   if (this.selectedContent.Type === 'Document') {\n  //     this.startedTime = new Date().valueOf();\n  //     if (!this.selectedContent.Studied)\n  //       this.timerSubscription = this.timer\n  //         .start(this.selectedContent.RequiredStudyTimeSec - this.selectedContent.LastStudyTimeSec)\n  //         .subscribe(async (status) => {\n  //           if (status === 'ended') {\n  //             this.timerSubscription.unsubscribe();\n  //             let diff = new Date().valueOf() - this.startedTime;\n  //             await this.contentStudied(\n  //               this.selectedContent.Id,\n  //               Math.floor(diff / 1000)\n  //             );\n  //             await this.updateCourseDetails();\n  //             this.startedTime = new Date().valueOf();\n  //           }\n  //         });\n  //   } else if (this.selectedContent.Type === 'Video') {\n  //     this.startedTime = this.selectedContent.LastStudyTimeSec ?? 0;\n  //   }\n  //   this.courseData.Contents.forEach((element) => {\n  //     element.Selected = this.courseData.Contents[i].Id == element.Id;\n  //   });\n  //   switch (this.courseData.Contents[i].Type) {\n  //     case 'Document':\n  //       this.onDocClick(this.courseData.Contents[i]);\n  //       break;\n  //     case 'Video':\n  //       this.vidObj = this.selectedContent;\n  //       break;\n  //     case 'MockTest':\n  //       this.router.navigate([\n  //         'course-mock-test',\n  //         this.courseData.Contents[i].Id,\n  //       ]);\n  //       break;\n  //     case 'CertificateTest':\n  //       this.router.navigate([\n  //         'course-certificate-test',\n  //         this.courseData.Contents[i].Id,\n  //       ]);\n  //       break;\n  //   }\n  // }\n\n\n  onSelectContent(i) {\n    var _a;\n\n    return __awaiter(this, void 0, void 0, function* () {\n      if (this.selectedContent && this.selectedContent.Id === this.courseData.Contents[i].Id || this.courseData.Contents[i].Restricted) return;\n      this.currentContentIndex = i; // Study time track for previous content\n\n      if (this.selectedContent) {\n        let study;\n\n        switch (this.selectedContent.Type) {\n          case 'Document':\n            if (this.timerSubscription) {\n              this.timer.stop();\n              this.timerSubscription.unsubscribe();\n            }\n\n            let diff = new Date().valueOf() - this.startedTime;\n            study = yield this.contentStudied(this.selectedContent.Id, Math.floor(diff / 1000));\n            this.selectedContent.LastStudyTimeSec = study.LastStudyTimeSec;\n            break;\n\n          case 'Video':\n            if (this.selectedContent.YoutubeID) break;\n            study = yield this.contentStudied(this.selectedContent.Id, Math.floor(this.video.currentTime));\n            this.selectedContent.LastStudyTimeSec = study.LastStudyTimeSec; // this.video.stop();\n\n            this.vidObj = null;\n            this.video = null;\n            this.supposedCurrentTime = 0;\n            break;\n        }\n      } // .End of Study time track for previous content\n\n\n      this.selectedContent = this.courseData.Contents[i];\n      this.selectedIndex = i; // Start tracking of study time for next content\n\n      if (this.selectedContent.Type === 'Document') {\n        this.startedTime = new Date().valueOf();\n        if (!this.selectedContent.Studied && this.selectedContent.FilePath.split('.').pop() === 'pdf') this.timerSubscription = this.timer.start(this.selectedContent.RequiredStudyTimeSec - this.selectedContent.LastStudyTimeSec).subscribe(status => __awaiter(this, void 0, void 0, function* () {\n          if (status === 'ended') {\n            this.timerSubscription.unsubscribe();\n            let diff = new Date().valueOf() - this.startedTime;\n            yield this.contentStudied(this.selectedContent.Id, Math.floor(diff / 1000));\n            yield this.updateCourseDetails();\n          }\n        }));\n      } else if (this.selectedContent.Type === 'Video') {\n        this.startedTime = (_a = this.selectedContent.LastStudyTimeSec) !== null && _a !== void 0 ? _a : 0;\n      } // .End of Start tracking of study time for next content\n      // Selection setting for newly clicked content\n\n\n      this.courseData.Contents.forEach(element => {\n        element.Selected = this.courseData.Contents[i].Id == element.Id;\n      }); // Open/Navigate to content\n\n      switch (this.courseData.Contents[i].Type) {\n        case 'Document':\n          this.onDocClick(this.courseData.Contents[i]);\n          break;\n\n        case 'Video':\n          setTimeout(() => {\n            this.vidObj = this.selectedContent;\n          }, 500);\n\n          if (this.selectedContent.YoutubeID && !this.selectedContent.Studied) {\n            yield this.contentStudied(this.selectedContent.Id, 0);\n            yield this.updateCourseDetails();\n          }\n\n          break;\n\n        case 'MockTest':\n          this.router.navigate(['course-mock-test', this.courseData.Contents[i].Id]);\n          break;\n\n        case 'CertificateTest':\n          this.router.navigate(['course-certificate-test', this.courseData.Contents[i].Id]);\n          break;\n      }\n    });\n  }\n\n  updateCourseDetails() {\n    this.blockUI.start('Loading data. Please wait...');\n    return new Promise(resolve => {\n      this._service.get('course/get-course-contents/' + this.courseId).subscribe({\n        next: res => {\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              timeOut: 2000\n            });\n            return;\n          }\n\n          this.courseData.Contents.forEach(element => {\n            const content = res.Data.find(x => x.Id === element.Id);\n\n            if (content) {\n              element.Restricted = content.Restricted;\n              element.Studied = content.Studied;\n              element.LastStudyTimeSec = content.LastStudyTimeSec;\n            }\n          });\n        },\n        error: err => {\n          this.toastr.error(err.Messaage || err, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false\n          });\n          this.blockUI.stop();\n          resolve(null);\n        },\n        complete: () => {\n          this.blockUI.stop();\n          resolve(null);\n        }\n      });\n    });\n  }\n\n  onDocClick(doc) {\n    this.docObj = null; //  if (doc.Restricted) return;\n\n    this.docObj = doc;\n\n    if (doc.FilePath.split('.').pop() === 'pdf') {\n      this.openPdf(doc.FilePath);\n      this.docObj.PDF = true;\n    } else {\n      this.docObj.PDF = false;\n    }\n  }\n\n  openPdf(path) {\n    this._service.getPDFFile(this.mediaBaseUrl + '/api/course/download-document-file?partialPath=' + path).subscribe(res => {\n      // this.pdfViewerOnDemand.pdfSrc = res; // pdfSrc can be Blob or Uint8Array\n      this.pdfSrc = res; //URL.createObjectURL(res); // pdfSrc can be Blob or Uint8Array\n      // this.pdfViewerOnDemand.refresh(); // Ask pdf viewer to load/reresh pdf\n    });\n  }\n\n  getVideoLeftHistory(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      this.blockUI.start('Loading some data. Please wait...');\n      return new Promise(resolve => {\n        this._service.get('course/get-video-left-history/' + id).subscribe({\n          next: res => {\n            if (res.Status === ResponseStatus.Warning) {\n              this.toastr.warning(res.Message, 'Warning!', {\n                timeOut: 2000\n              });\n              return;\n            } else if (res.Status === ResponseStatus.Error) {\n              this.toastr.error(res.Message, 'Error!', {\n                timeOut: 2000\n              });\n              return;\n            }\n\n            resolve(res.Data);\n          },\n          error: err => {\n            this.toastr.warning(err.Messaage || err, 'Warning!', {\n              closeButton: true,\n              disableTimeOut: false\n            });\n            this.blockUI.stop();\n            resolve(0);\n          },\n          complete: () => {\n            this.blockUI.stop();\n          }\n        });\n      });\n    });\n  }\n\n  contentStudied(id, studyTimeSec) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const obj = {\n        materialId: id,\n        studyTimeSec: studyTimeSec\n      };\n      this.blockUI.start('Saving study data. Please wait...');\n      return new Promise(resolve => {\n        this._service.get('course/content-study', obj).subscribe({\n          next: res => {\n            if (res.Status === ResponseStatus.Warning) {\n              this.toastr.warning(res.Message, 'Warning!', {\n                timeOut: 2000\n              });\n              return;\n            } else if (res.Status === ResponseStatus.Error) {\n              this.toastr.error(res.Message, 'Error!', {\n                timeOut: 2000\n              });\n              return;\n            }\n\n            resolve(res.Data);\n          },\n          error: err => {\n            this.toastr.error(err.Messaage || err, 'Warning!', {\n              closeButton: true,\n              disableTimeOut: false\n            });\n            this.blockUI.stop();\n            resolve(null);\n          },\n          complete: () => {\n            this.blockUI.stop();\n          }\n        });\n      });\n    });\n  }\n\n  onChangeCourse(event) {\n    if (event) {\n      this.MaterialList = [];\n      this.discussionForm.get('MaterialId').setValidators(null);\n      this.discussionForm.get('MockTestId').setValidators(null);\n      this.discussionForm.get('ExamId').setValidators(null);\n      this.discussionForm.get('ExamId').updateValueAndValidity();\n      this.discussionForm.controls['MaterialId'].setValue(null);\n      this.discussionForm.controls['MockTestId'].setValue(null);\n      this.discussionForm.controls['ExamId'].setValue(null);\n      this.discussionForm.controls['CourseId'].setValue(this.courseId);\n      const obj = {\n        disscussionType: this.discussionForm.value.DiscussionType,\n        courseId: this.courseId\n      };\n\n      this._service.get('course/contents/dropdown-list', obj).subscribe({\n        next: res => {\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              closeButton: true,\n              disableTimeOut: false,\n              enableHtml: true\n            });\n            return;\n          }\n\n          this.MaterialList = res.Data;\n          let selectedData = this.MaterialList.find(x => x.Id == this.selectedDiscussion.Id);\n\n          if (event == 0 || event == 1) {\n            this.discussionForm.get('MaterialId').setValidators([Validators.required]);\n            this.discussionForm.get('MaterialId').updateValueAndValidity();\n            this.discussionForm.controls['MaterialId'].setValue(selectedData.Id);\n          } else if (event == 2) {\n            this.discussionForm.get('MockTestId').setValidators([Validators.required]);\n            this.discussionForm.get('MockTestId').updateValueAndValidity();\n            this.discussionForm.controls['MockTestId'].setValue(selectedData.Id);\n          } else if (event == 3) {\n            this.discussionForm.get('ExamId').setValidators([Validators.required]);\n            this.discussionForm.get('ExamId').updateValueAndValidity();\n            this.discussionForm.controls['ExamId'].setValue(selectedData.Id);\n          }\n        },\n        error: err => {\n          this.toastr.warning(err.Messaage || err, 'Warning!', {\n            closeButton: true,\n            disableTimeOut: false\n          });\n        },\n        complete: () => {}\n      });\n    }\n  }\n\n  onFormSubmit() {\n    this.submitted = true;\n\n    if (this.discussionForm.invalid) {\n      return;\n    }\n\n    this.blockUI.start('Saving...'); //const obj = {\n    //  Id: this.discussionForm.value.id,\n    //  DiscussionType: this.discussionForm.value.discussionType,\n    //  Comment: this.discussionForm.value.comment\n    //    ? this.discussionForm.value.comment.trim()\n    //    : '',\n    //  CourseId: this.courseId,\n    //  MaterialId: this.discussionForm.value.materialId\n    //    ? this.discussionForm.value.materialId\n    //    : null,\n    //  ExamId: this.discussionForm.value.examId\n    //    ? this.discussionForm.value.examId\n    //    : null,\n    //  MockTestId: this.discussionForm.value.mockTestId\n    //    ? this.discussionForm.value.mockTestId\n    //    : null,\n    //  CourseDiscussionId: null,\n    //  ResourceId: null,\n    //};\n\n    const request = this._service.post('course/discussion-save', this.discussionForm.value);\n\n    request.subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, 'Success!', {\n          timeOut: 2000\n        });\n        this.getCourseDiscussions();\n        this.commentButton.nativeElement.click();\n        this.modalService.hide();\n        this.clearForm();\n      },\n      error: err => {\n        this.toastr.warning(err.Message || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n      },\n      complete: () => {\n        this.blockUI.stop();\n      }\n    });\n  }\n\n  clearForm() {\n    this.discussionForm.reset();\n    this.submitted = false;\n  }\n\n  onLoadedMetadata(e) {\n    this.video = e.target;\n    this.video.currentTime = this.startedTime;\n    this.supposedCurrentTime = this.startedTime;\n    this.video.play();\n  }\n\n  onTimeUpdate(e) {\n    if (this.video && !this.video.seeking && this.supposedCurrentTime < this.video.currentTime) {\n      this.supposedCurrentTime = this.video.currentTime;\n    }\n  }\n\n  onSeeking(e) {\n    var delta = this.video.currentTime - this.supposedCurrentTime;\n\n    if (this.video.currentTime > this.supposedCurrentTime && Math.abs(delta) > 0.01) {\n      this.video.currentTime = this.supposedCurrentTime;\n    }\n  }\n\n  onVideoPause(e) {\n    return __awaiter(this, void 0, void 0, function* () {\n      if (e.target.duration === e.target.currentTime && !this.selectedContent.Studied) {\n        yield this.contentStudied(this.selectedContent.Id, Math.floor(this.video.currentTime));\n        this.supposedCurrentTime = e.target.currentTime;\n        yield this.updateCourseDetails(); //this.onSelectContent(this.selectedIndex+1);\n      }\n    });\n  }\n\n  goToPageFeedback(id) {\n    this.router.navigate(['course-feedback/' + id]);\n  }\n\n  openFeedBackModal(template) {\n    this.feedBackQuestionList = [];\n\n    if (!this.myFeedBack) {\n      this.blockUI.start('Getting feedback questions. Pleae wait...');\n\n      this._service.get('course/get-feedback-questions/' + this.courseData.Id).subscribe({\n        next: res => {\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              timeOut: 2000\n            });\n            return;\n          }\n\n          this.myFeedBack = res.Data;\n          this.myFeedBack.Feedbacks.forEach(group => {\n            group.Questions.forEach(element => {\n              if (element.QuestionType !== 'Checkbox') element.Answers = element.Answers.length > 0 ? element.Answers.join() : null;\n              if (element.QuestionType === 'Rating') element.Answers = element.Answers ? Number(element.Answers) : null;\n            });\n          });\n          this.feedBackQuestionList = this.myFeedBack.Feedbacks;\n          this.ratingForm.controls['rating'].setValue(this.myFeedBack.Rating);\n          this.ratingForm.controls['comment'].setValue(this.myFeedBack.Comment);\n        },\n        error: err => {\n          this.toastr.warning(err.Messaage || err, 'Warning!', {\n            closeButton: true,\n            disableTimeOut: false\n          });\n          this.blockUI.stop();\n        },\n        complete: () => {\n          this.blockUI.stop();\n        }\n      });\n    } else {\n      this.feedBackQuestionList = this.myFeedBack.Feedbacks;\n      this.ratingForm.controls['rating'].setValue(this.myFeedBack.Rating);\n      this.ratingForm.controls['comment'].setValue(this.myFeedBack.Comment);\n    }\n\n    this.modalRef = this.modalService.show(template, this.modalLgConfig);\n  }\n\n  modalHideFeedBack() {\n    this.feedBackQuestionList = [];\n    this.ratingForm.reset();\n    this.modalRef.hide();\n  }\n\n  onChangeCheckBox(event, question, option) {\n    if (event.target.checked) {\n      question.Answers.push(option);\n    } else question.Answers.splice(question.Answers.indexOf(option), 1);\n  }\n\n  onSubmitFeedback() {\n    this.submitted = true;\n    if (this.ratingForm.invalid) return;\n    let feedbacks = [];\n    this.feedBackQuestionList.forEach(group => {\n      group.Questions.forEach(element => {\n        if (element.QuestionType !== 'Checkbox') element.Answers = element.Answers ? [element.Answers.toString()] : [];\n        feedbacks.push({\n          QuestionId: element.Id,\n          Answers: element.Answers\n        });\n      });\n    });\n    const obj = {\n      Id: this.courseData.Id,\n      CourseId: this.courseData.Id,\n      Rating: this.ratingForm.value.rating,\n      Comment: this.ratingForm.value.comment ? this.ratingForm.value.comment.trim() : null,\n      Feedbacks: feedbacks\n    };\n    this.blockUI.start('Saving feedbacks. Please wait...');\n\n    this._service.post('feedback/course/save-or-update', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, 'SUCCESS!', {\n          timeOut: 2000\n        });\n        this.getCourseDetails();\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.blockUI.stop();\n      },\n      complete: () => {\n        this.blockUI.stop();\n        this.modalHideFeedBack();\n      }\n    });\n  }\n\n}\n\nCourseDetailsComponent.ɵfac = function CourseDetailsComponent_Factory(t) {\n  return new (t || CourseDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i4.BsModalService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.Location));\n};\n\nCourseDetailsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CourseDetailsComponent,\n  selectors: [[\"app-course-details\"]],\n  viewQuery: function CourseDetailsComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.courseTabs = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.commentButton = _t.first);\n    }\n  },\n  decls: 18,\n  vars: 4,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"col-12\", \"d-flex\", \"justify-content-between\"], [\"class\", \"h3 text-break mb-0\", 4, \"ngIf\"], [1, \"btn\", \"btn-link\", \"text-decoration-none\", \"fs-4\", \"fw-bold\", \"btn-sm\", \"d-flex\", \"align-items-center\", \"border-start\", 3, \"click\"], [1, \"fs-4\", \"ai-arrow-left\", \"fs-base\", \"me-2\"], [1, \"mt-1\", \"mb-4\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"row\"], [\"class\", \"col-lg-8 border-end\", 4, \"ngIf\"], [\"class\", \"col-lg-4\", \"id\", \"scrollable-content\", 4, \"ngIf\"], [\"templateResourceModal\", \"\"], [\"templateFeedBackModal\", \"\"], [1, \"h3\", \"text-break\", \"mb-0\"], [1, \"fw-bold\", \"text-uppercase\", \"fs-6\"], [1, \"col-md-12\"], [1, \"col-lg-8\", \"border-end\"], [\"class\", \"d-flex\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"elseNotSelectedContent\", \"\"], [1, \"mt-3\"], [3, \"justified\"], [\"courseTabs\", \"\"], [3, \"selectTab\"], [\"tabHeading\", \"\"], [1, \"col-md-10\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-2\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"text-wrap\"], [1, \"col-8\", \"pe-0\"], [1, \"fs-3\", \"text-blue\", 3, \"ngModel\", \"titles\", \"readonly\", \"ngModelChange\"], [1, \"h4\", \"pt-1\", \"text-blue\", \"me-2\"], [\"type\", \"button\", \"class\", \"btn btn-mini btn-primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"col-4\"], [1, \"text-blue\"], [1, \"col-md-2\"], [1, \"d-inline-block\", \"fw-normal\", \"px-2\", \"py-1\", \"rounded-1\", \"float-end\"], [\"class\", \"fa fa-bookmark text-gold fs-3 div-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"fa-regular fa-bookmark text-muted fs-3 div-pointer\", 3, \"click\", 4, \"ngIf\"], [1, \"col-md-12\", \"py-3\"], [1, \"text-justify\", 3, \"innerHTML\"], [1, \"col-md-8\"], [1, \"h3\", \"mb-3\", \"text-nowrap\"], [1, \"col-md-4\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\"], [1, \"btn\", \"btn-primary\", \"d-block\", \"w-100\", 3, \"click\"], [\"commentButton\", \"\"], [\"commentForm\", \"\"], [\"class\", \"col-md-12\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"justify-content-center\", \"mt-3\"], [1, \"col-lg-12\"], [3, \"isAnimated\", \"closeOthers\"], [3, \"isOpen\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\"], [1, \"h4\", \"nav-heading\", \"mb-2\", \"flex-grow-1\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-mini\", \"mb-2\", \"d-flex\", \"align-items-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"rounded-0\", 3, \"disabled\", \"click\"], [1, \"fa-solid\", \"fa-arrow-left\"], [1, \"fa-solid\", \"fa-arrow-right\"], [\"class\", \"w-100\", \"controlslist\", \"nodownload\", \"controls\", \"\", 3, \"loadedmetadata\", \"timeupdate\", \"seeking\", \"pause\", 4, \"ngIf\"], [\"class\", \"w-100\", \"frameborder\", \"0\", \"allow\", \"accelerometer; encrypted-media; gyroscope; picture-in-picture\", \"allowfullscreen\", \"\", 3, \"height\", \"src\", 4, \"ngIf\"], [4, \"ngIf\"], [\"controlslist\", \"nodownload\", \"controls\", \"\", 1, \"w-100\", 3, \"loadedmetadata\", \"timeupdate\", \"seeking\", \"pause\"], [\"type\", \"video/mp4\", 3, \"src\"], [\"frameborder\", \"0\", \"allow\", \"accelerometer; encrypted-media; gyroscope; picture-in-picture\", \"allowfullscreen\", \"\", 1, \"w-100\", 3, \"src\"], [\"youtubeFrame\", \"\"], [\"class\", \"d-flex justify-content-center mb-2\", 4, \"ngIf\"], [\"class\", \"w-100 cdc-style-1\", 4, \"ngIf\"], [\"class\", \"card py-3 mb-2 cdc-style-2\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"mb-2\"], [1, \"hours\", \"parent_time\"], [1, \"hours_0\", \"child_time\", \"child_time0\"], [1, \"hours_1\", \"child_time\", \"child_time1\"], [1, \"minutes\", \"parent_time\"], [1, \"minutes_0\", \"child_time\", \"child_time0\"], [1, \"minutes_1\", \"child_time\", \"child_time1\"], [1, \"seconds\", \"parent_time\"], [1, \"seconds_0\", \"child_time\", \"child_time0\"], [1, \"seconds_1\", \"child_time\", \"child_time1\"], [1, \"w-100\", \"cdc-style-1\"], [3, \"src\", \"zoom\", \"showPrintButton\", \"showRotateButton\", \"showOpenFileButton\", \"showDownloadButton\", \"showBookmarkButton\", \"showSecondaryToolbarButton\", \"useBrowserLocale\", \"srcChange\"], [1, \"card\", \"py-3\", \"mb-2\", \"cdc-style-2\"], [1, \"card-body\"], [1, \"font-size-20\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"mt-2\", 3, \"click\"], [1, \"fa\", \"fa-download\"], [1, \"card-img-top\", \"card-img-bottom\", \"w-100\"], [3, \"src\"], [\"type\", \"button\", 1, \"btn\", \"btn-mini\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-tasks\"], [1, \"fa\", \"fa-bookmark\", \"text-gold\", \"fs-3\", \"div-pointer\", 3, \"click\"], [1, \"fa-regular\", \"fa-bookmark\", \"text-muted\", \"fs-3\", \"div-pointer\", 3, \"click\"], [1, \"col-md-12\", \"py-3\", \"pe-md-3\"], [\"autocomplete\", \"off\", \"novalidate\", \"\", 1, \"needs-validation\", \"bg-light\", \"rounded-3\", \"shadow\", \"p-4\", 3, \"formGroup\"], [\"type\", \"hidden\", \"formControlName\", \"CourseId\", 3, \"value\"], [\"type\", \"hidden\", \"formControlName\", \"Id\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"com-name\", 1, \"form-label\"], [1, \"text-danger\", \"ms-1\"], [\"formControlName\", \"DiscussionType\", \"placeholder\", \"Select a type\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"change\"], [\"class\", \"error-text\", 4, \"ngIf\"], [\"class\", \"col-md-6 mb-3\", 4, \"ngIf\"], [\"class\", \"mb-3 col-md-6\", 4, \"ngIf\"], [1, \"mb-3\"], [\"for\", \"com-text\", 1, \"form-label\"], [\"formControlName\", \"Comment\", \"id\", \"com-text\", \"rows\", \"4\", \"placeholder\", \"Write your comment  here\", \"required\", \"\", 1, \"form-control\"], [1, \"\"], [1, \"d-flex\", \"justify-content-end\"], [\"type\", \"Button\", \"area-label\", \"Close\", 1, \"btn\", \"btn-danger\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"error-text\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [\"formControlName\", \"MaterialId\", \"bindLabel\", \"Title\", \"bindValue\", \"Id\", \"placeholder\", \"Select\", 3, \"clearable\", \"clearOnBackspace\", \"items\"], [1, \"form-label\"], [1, \"mb-3\", \"col-md-6\"], [1, \"form-label\", \"required\"], [\"formControlName\", \"MockTestId\", \"bindLabel\", \"Title\", \"bindValue\", \"Id\", \"placeholder\", \"Select\", 3, \"clearable\", \"clearOnBackspace\", \"items\"], [\"formControlName\", \"ExamId\", \"bindLabel\", \"Title\", \"bindValue\", \"Id\", \"placeholder\", \"Select\", 3, \"clearable\", \"clearOnBackspace\", \"items\"], [1, \"comment\", \"mb-3\", \"pb-1\", \"cdc-style-3\"], [1, \"d-flex\", \"align-items-center\", \"mb-3\"], [\"class\", \"rounded-circle\", \"width\", \"80\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle\", \"src\", \"assets/img/demo/profile.jpg\", \"width\", \"80\", 3, \"alt\", 4, \"ngIf\"], [1, \"ps-2\", \"ms-1\"], [1, \"fs-4\", \"mb-0\"], [1, \"fs-xs\", \"text-muted\"], [\"class\", \"meta-link fs-xs\", 4, \"ngIf\"], [1, \"meta-link\", \"fs-xs\"], [1, \"ai-calendar\", \"me-1\", \"align-vertical\"], [1, \"comment-text\"], [\"class\", \"cdc-style-4\", 4, \"ngIf\"], [\"width\", \"80\", 1, \"rounded-circle\", 3, \"src\", \"alt\"], [\"src\", \"assets/img/demo/profile.jpg\", \"width\", \"80\", 1, \"rounded-circle\", 3, \"alt\"], [1, \"fa\", \"fa-play\", \"me-1\", \"align-vertical\"], [1, \"fa\", \"fa-clock\", \"me-1\", \"align-vertical\"], [1, \"fa\", \"fa-file\", \"me-1\", \"align-vertical\"], [1, \"ai-award\", \"me-1\", \"align-vertical\"], [1, \"cdc-style-4\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [3, \"isOpen\"], [\"accordion-heading\", \"\", \"type\", \"button\", 1, \"btn\", \"fs-5\", \"fw-bold\", \"text-primary\", \"btn-block\", \"justify-content-between\", \"d-flex\", \"w-100\", \"shadow-none\"], [1, \"pull-left\", \"float-left\"], [1, \"text-justify\"], [\"id\", \"scrollable-content\", 1, \"col-lg-4\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [\"class\", \"border-bottom\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"border-bottom\", 3, \"click\"], [1, \"row\", \"py-3\", \"content-div\", 3, \"ngClass\"], [1, \"col-10\", \"div-pointer\"], [1, \"left-icon-div\", \"me-2\"], [\"class\", \"card-floating-icon-custom\", 4, \"ngIf\"], [1, \"fs-sm\", \"ps-sm-3\"], [1, \"d-flex\", \"flex-column\", \"text-heading\"], [1, \"fw-bold\", \"fs-6\"], [\"class\", \"text-primary fw-bold fs-xs pt-2 mb-1\", 4, \"ngIf\"], [\"class\", \"text-primary fw-bold fs-lg pt-2 mb-1\", 4, \"ngIf\"], [\"role\", \"button\", \"class\", \"mt--2 cursor d-inline-block fs-xs fw-bold bg-faded-blue text-primary px-2 py-1 rounded-1 float-end\", 3, \"click\", 4, \"ngIf\"], [1, \"col-2\", \"align-self-center\"], [\"class\", \"ai-lock fw-bold fs-3\", 4, \"ngIf\"], [\"class\", \"ai-check fw-bold fs-3 text-primary\", 4, \"ngIf\"], [1, \"card-floating-icon-custom\"], [1, \"fa\", \"fa-play\"], [1, \"fa\", \"fa-clock\"], [1, \"fa\", \"fa-file\"], [1, \"ai-award\"], [1, \"text-primary\", \"fw-bold\", \"fs-xs\", \"pt-2\", \"mb-1\"], [1, \"text-primary\", \"fw-bold\", \"fs-lg\", \"pt-2\", \"mb-1\"], [\"role\", \"button\", 1, \"mt--2\", \"cursor\", \"d-inline-block\", \"fs-xs\", \"fw-bold\", \"bg-faded-blue\", \"text-primary\", \"px-2\", \"py-1\", \"rounded-1\", \"float-end\", 3, \"click\"], [1, \"fa\", \"fa-folder\"], [1, \"ai-lock\", \"fw-bold\", \"fs-3\"], [1, \"ai-check\", \"fw-bold\", \"fs-3\", \"text-primary\"], [1, \"modal-header\"], [1, \"modal-title\", \"float-start\"], [\"type\", \"button \", \"aria-label\", \"Close \", 1, \"close\", \"float-end\", 3, \"click\"], [1, \"fa\", \"fa-close\"], [1, \"modal-body\"], [1, \"col-sm-12\"], [1, \"card\"], [1, \"table\"], [\"scope\", \"col\"], [4, \"ngFor\", \"ngForOf\"], [1, \"modal-footer\"], [1, \"pr-4\"], [1, \"btn\", \"btn-outline-danger\", \"mr-2\", 3, \"click\"], [1, \"feather\", \"icon-close\"], [\"scope\", \"row\"], [1, \"dropdown\"], [\"href\", \"javascript:;\", \"rel\", \"noopener\", 1, \"theme-btn-custom\", \"btn-xs\", \"theme-btn-light\", 3, \"href\"], [1, \"fa\", \"fa-download\", \"mr-1\"], [\"type\", \"button \", \"aria-label\", \"Close \", 1, \"close\", \"btn\", \"btn-mini\", \"btn-outline-danger\", \"float-end\", 3, \"click\"], [\"autocomplete\", \"off\", 1, \"col-12\", 3, \"formGroup\"], [1, \"row\", \"g-3\", \"align-items-center\"], [1, \"col-auto\"], [\"for\", \"rating\", 1, \"col-form-label\"], [\"formControlName\", \"rating\", 1, \"fs-3\", \"text-gold\", 3, \"titles\", \"readonly\"], [1, \"row\", \"g-3\", \"align-items-center\", \"mb-3\"], [\"for\", \"comment\", 1, \"col-form-label\"], [1, \"col-auto\", \"w-100\", \"mt-0\"], [\"rows\", \"3\", \"formControlName\", \"comment\", 1, \"form-control\"], [\"class\", \"card card-active mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-outline-danger\", \"me-2\", 3, \"click\"], [1, \"fa\", \"fa-save\"], [1, \"card\", \"card-active\", \"mb-3\"], [1, \"card-header\", \"py-2\", \"bg-primary\", \"text-dark\", \"bg-opacity-25\"], [1, \"card-title\", \"mb-0\"], [1, \"card-body\", \"py-2\"], [\"class\", \"row mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"mb-3\"], [1, \"col-lg-7\", \"col-md-6\", \"col-12\"], [1, \"col-lg-5\", \"col-md-4\", \"col-12\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [1, \"input-group\"], [\"class\", \"form-check form-check-inline\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-check\", \"form-check-inline\"], [\"type\", \"radio\", 1, \"form-check-input\", 3, \"id\", \"name\", \"value\", \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"form-check-label\", 3, \"for\"], [\"class\", \"form-check mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-check\", \"mb-1\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"id\", \"checked\", \"change\"], [\"placeholder\", \"Select\", 1, \"rounded-2\", \"w-100\", 3, \"clearable\", \"ngModel\", \"ngModelOptions\", \"clearOnBackspace\", \"items\", \"ngModelChange\"], [1, \"fs-3\", \"text-gold\", 3, \"ngModel\", \"ngModelOptions\", \"titles\", \"readonly\", \"ngModelChange\"], [\"rows\", \"3\", \"placeholder\", \"Write anser here\", 1, \"form-control\", \"px-2\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"]],\n  template: function CourseDetailsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵtemplate(5, CourseDetailsComponent_p_5_Template, 5, 1, \"p\", 4);\n      i0.ɵɵelementStart(6, \"a\", 5);\n      i0.ɵɵlistener(\"click\", function CourseDetailsComponent_Template_a_click_6_listener() {\n        return ctx.backClicked();\n      });\n      i0.ɵɵelement(7, \"i\", 6);\n      i0.ɵɵtext(8, \"Go Back\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(9, \"hr\", 7);\n      i0.ɵɵtemplate(10, CourseDetailsComponent_div_10_Template, 2, 0, \"div\", 8);\n      i0.ɵɵelementStart(11, \"div\", 9);\n      i0.ɵɵtemplate(12, CourseDetailsComponent_div_12_Template, 65, 25, \"div\", 10);\n      i0.ɵɵtemplate(13, CourseDetailsComponent_div_13_Template, 4, 1, \"div\", 11);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(14, CourseDetailsComponent_ng_template_14_Template, 27, 2, \"ng-template\", null, 12, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(16, CourseDetailsComponent_ng_template_16_Template, 27, 6, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.courseData);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedContent);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.courseData);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.courseData);\n    }\n  },\n  directives: [i7.BlockUIComponent, i6.NgIf, i8.NgxExtendedPdfViewerComponent, i9.TabsetComponent, i9.TabDirective, i9.TabHeadingDirective, i10.RatingComponent, i5.NgControlStatus, i5.NgModel, i5.ɵNgNoValidate, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.DefaultValueAccessor, i5.FormControlName, i11.NgSelectComponent, i5.RequiredValidator, i6.NgForOf, i12.AccordionComponent, i12.AccordionPanelComponent, i6.NgClass, i13.DefaultClassDirective, i6.NgSwitch, i6.NgSwitchCase, i5.RadioControlValueAccessor],\n  pipes: [i14.SafePipe, i15.TimeAgoPipe, i6.DatePipe],\n  styles: [\".parent_time{margin:0 5px}.child_time0{background:#f39a3f;border-radius:5px 0 0 5px}.child_time1{background:#ff7c0c;border-radius:0 5px 5px 0}.child_time{float:left;color:#fff;padding:6px;margin:0}.text-justify{text-align:justify;text-justify:inter-word}.comment-text{font-size:x-large}.cdc-style-1{height:605px}.cdc-style-2{height:205px}.cdc-style-3{position:relative}.cdc-style-4{position:absolute;right:0;top:0}\\n\"],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], CourseDetailsComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}