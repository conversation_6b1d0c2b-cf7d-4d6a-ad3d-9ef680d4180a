{"ast": null, "code": "import * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport * as i3 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Optional, Inject, ViewChild, Component, ViewEncapsulation, ChangeDetectionStrategy, InjectionToken, Injector, TemplateRef, InjectFlags, Injectable, SkipSelf, Input, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { Subject, defer, Subscription, of } from 'rxjs';\nimport { filter, take, startWith } from 'rxjs/operators';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/cdk/a11y';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\n\nfunction MatDialogContainer_ng_template_0_Template(rf, ctx) {}\n\nclass MatDialogConfig {\n  constructor() {\n    /** The ARIA role of the dialog element. */\n    this.role = 'dialog';\n    /** Custom class for the overlay pane. */\n\n    this.panelClass = '';\n    /** Whether the dialog has a backdrop. */\n\n    this.hasBackdrop = true;\n    /** Custom class for the backdrop. */\n\n    this.backdropClass = '';\n    /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n\n    this.disableClose = false;\n    /** Width of the dialog. */\n\n    this.width = '';\n    /** Height of the dialog. */\n\n    this.height = '';\n    /** Max-width of the dialog. If a number is provided, assumes pixel units. Defaults to 80vw. */\n\n    this.maxWidth = '80vw';\n    /** Data being injected into the child component. */\n\n    this.data = null;\n    /** ID of the element that describes the dialog. */\n\n    this.ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n\n    this.ariaLabelledBy = null;\n    /** Aria label to assign to the dialog element. */\n\n    this.ariaLabel = null;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n\n    this.autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the\n     * previously-focused element, after it's closed.\n     */\n\n    this.restoreFocus = true;\n    /**\n     * Whether the dialog should close when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n\n    this.closeOnNavigation = true; // TODO(jelbourn): add configuration for lifecycle hooks, ARIA labelling.\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Animations used by MatDialog.\n * @docs-private\n */\n\n\nconst matDialogAnimations = {\n  /** Animation that is applied on the dialog container by default. */\n  dialogContainer: /*#__PURE__*/trigger('dialogContainer', [\n  /*#__PURE__*/\n  // Note: The `enter` animation transitions to `transform: none`, because for some reason\n  // specifying the transform explicitly, causes IE both to blur the dialog content and\n  // decimate the animation performance. Leaving it as `none` solves both issues.\n  state('void, exit', /*#__PURE__*/style({\n    opacity: 0,\n    transform: 'scale(0.7)'\n  })), /*#__PURE__*/state('enter', /*#__PURE__*/style({\n    transform: 'none'\n  })), /*#__PURE__*/transition('* => enter', /*#__PURE__*/animate('150ms cubic-bezier(0, 0, 0.2, 1)', /*#__PURE__*/style({\n    transform: 'none',\n    opacity: 1\n  }))), /*#__PURE__*/transition('* => void, * => exit', /*#__PURE__*/animate('75ms cubic-bezier(0.4, 0.0, 0.2, 1)', /*#__PURE__*/style({\n    opacity: 0\n  })))])\n};\n/**\n * Throws an exception for the case when a ComponentPortal is\n * attached to a DomPortalOutlet without an origin.\n * @docs-private\n */\n\nfunction throwMatDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Base class for the `MatDialogContainer`. The base class does not implement\n * animations as these are left to implementers of the dialog container.\n */\n\n\nlet _MatDialogContainerBase = /*#__PURE__*/(() => {\n  class _MatDialogContainerBase extends BasePortalOutlet {\n    constructor(_elementRef, _focusTrapFactory, _changeDetectorRef, _document,\n    /** The dialog configuration. */\n    _config, _interactivityChecker, _ngZone, _focusMonitor) {\n      super();\n      this._elementRef = _elementRef;\n      this._focusTrapFactory = _focusTrapFactory;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._config = _config;\n      this._interactivityChecker = _interactivityChecker;\n      this._ngZone = _ngZone;\n      this._focusMonitor = _focusMonitor;\n      /** Emits when an animation state changes. */\n\n      this._animationStateChanged = new EventEmitter();\n      /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n\n      this._elementFocusedBeforeDialogWasOpened = null;\n      /**\n       * Type of interaction that led to the dialog being closed. This is used to determine\n       * whether the focus style will be applied when returning focus to its original location\n       * after the dialog is closed.\n       */\n\n      this._closeInteractionType = null;\n      /**\n       * Attaches a DOM portal to the dialog container.\n       * @param portal Portal to be attached.\n       * @deprecated To be turned into a method.\n       * @breaking-change 10.0.0\n       */\n\n      this.attachDomPortal = portal => {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatDialogContentAlreadyAttachedError();\n        }\n\n        return this._portalOutlet.attachDomPortal(portal);\n      };\n\n      this._ariaLabelledBy = _config.ariaLabelledBy || null;\n      this._document = _document;\n    }\n    /** Initializes the dialog container with the attached content. */\n\n\n    _initializeWithAttachedContent() {\n      this._setupFocusTrap(); // Save the previously focused element. This element will be re-focused\n      // when the dialog closes.\n\n\n      this._capturePreviouslyFocusedElement();\n    }\n    /**\n     * Attach a ComponentPortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n\n\n    attachComponentPortal(portal) {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatDialogContentAlreadyAttachedError();\n      }\n\n      return this._portalOutlet.attachComponentPortal(portal);\n    }\n    /**\n     * Attach a TemplatePortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n\n\n    attachTemplatePortal(portal) {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatDialogContentAlreadyAttachedError();\n      }\n\n      return this._portalOutlet.attachTemplatePortal(portal);\n    }\n    /** Moves focus back into the dialog if it was moved out. */\n\n\n    _recaptureFocus() {\n      if (!this._containsFocus()) {\n        this._trapFocus();\n      }\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n\n\n    _forceFocus(element, options) {\n      if (!this._interactivityChecker.isFocusable(element)) {\n        element.tabIndex = -1; // The tabindex attribute should be removed to avoid navigating to that element again\n\n        this._ngZone.runOutsideAngular(() => {\n          element.addEventListener('blur', () => element.removeAttribute('tabindex'));\n          element.addEventListener('mousedown', () => element.removeAttribute('tabindex'));\n        });\n      }\n\n      element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n\n\n    _focusByCssSelector(selector, options) {\n      let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n\n      if (elementToFocus) {\n        this._forceFocus(elementToFocus, options);\n      }\n    }\n    /**\n     * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n     * cannot be moved then focus will go to the dialog container.\n     */\n\n\n    _trapFocus() {\n      const element = this._elementRef.nativeElement; // If were to attempt to focus immediately, then the content of the dialog would not yet be\n      // ready in instances where change detection has to run first. To deal with this, we simply\n      // wait for the microtask queue to be empty when setting focus when autoFocus isn't set to\n      // dialog. If the element inside the dialog can't be focused, then the container is focused\n      // so the user can't tab into other elements behind it.\n\n      switch (this._config.autoFocus) {\n        case false:\n        case 'dialog':\n          // Ensure that focus is on the dialog container. It's possible that a different\n          // component tried to move focus while the open animation was running. See:\n          // https://github.com/angular/components/issues/16215. Note that we only want to do this\n          // if the focus isn't inside the dialog already, because it's possible that the consumer\n          // turned off `autoFocus` in order to move focus themselves.\n          if (!this._containsFocus()) {\n            element.focus();\n          }\n\n          break;\n\n        case true:\n        case 'first-tabbable':\n          this._focusTrap.focusInitialElementWhenReady().then(focusedSuccessfully => {\n            // If we weren't able to find a focusable element in the dialog, then focus the dialog\n            // container instead.\n            if (!focusedSuccessfully) {\n              this._focusDialogContainer();\n            }\n          });\n\n          break;\n\n        case 'first-heading':\n          this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n\n          break;\n\n        default:\n          this._focusByCssSelector(this._config.autoFocus);\n\n          break;\n      }\n    }\n    /** Restores focus to the element that was focused before the dialog opened. */\n\n\n    _restoreFocus() {\n      const previousElement = this._elementFocusedBeforeDialogWasOpened; // We need the extra check, because IE can set the `activeElement` to null in some cases.\n\n      if (this._config.restoreFocus && previousElement && typeof previousElement.focus === 'function') {\n        const activeElement = _getFocusedElementPierceShadowDom();\n\n        const element = this._elementRef.nativeElement; // Make sure that focus is still inside the dialog or is on the body (usually because a\n        // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n        // the consumer moved it themselves before the animation was done, in which case we shouldn't\n        // do anything.\n\n        if (!activeElement || activeElement === this._document.body || activeElement === element || element.contains(activeElement)) {\n          if (this._focusMonitor) {\n            this._focusMonitor.focusVia(previousElement, this._closeInteractionType);\n\n            this._closeInteractionType = null;\n          } else {\n            previousElement.focus();\n          }\n        }\n      }\n\n      if (this._focusTrap) {\n        this._focusTrap.destroy();\n      }\n    }\n    /** Sets up the focus trap. */\n\n\n    _setupFocusTrap() {\n      this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n    }\n    /** Captures the element that was focused before the dialog was opened. */\n\n\n    _capturePreviouslyFocusedElement() {\n      if (this._document) {\n        this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n      }\n    }\n    /** Focuses the dialog container. */\n\n\n    _focusDialogContainer() {\n      // Note that there is no focus method when rendering on the server.\n      if (this._elementRef.nativeElement.focus) {\n        this._elementRef.nativeElement.focus();\n      }\n    }\n    /** Returns whether focus is inside the dialog. */\n\n\n    _containsFocus() {\n      const element = this._elementRef.nativeElement;\n\n      const activeElement = _getFocusedElementPierceShadowDom();\n\n      return element === activeElement || element.contains(activeElement);\n    }\n\n  }\n\n  _MatDialogContainerBase.ɵfac = function _MatDialogContainerBase_Factory(t) {\n    return new (t || _MatDialogContainerBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusTrapFactory), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(MatDialogConfig), i0.ɵɵdirectiveInject(i1.InteractivityChecker), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.FocusMonitor));\n  };\n\n  _MatDialogContainerBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatDialogContainerBase,\n    viewQuery: function _MatDialogContainerBase_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return _MatDialogContainerBase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Internal component that wraps user-provided dialog content.\n * Animation is based on https://material.io/guidelines/motion/choreography.html.\n * @docs-private\n */\n\n\nlet MatDialogContainer = /*#__PURE__*/(() => {\n  class MatDialogContainer extends _MatDialogContainerBase {\n    constructor() {\n      super(...arguments);\n      /** State of the dialog animation. */\n\n      this._state = 'enter';\n    }\n    /** Callback, invoked whenever an animation on the host completes. */\n\n\n    _onAnimationDone({\n      toState,\n      totalTime\n    }) {\n      if (toState === 'enter') {\n        this._trapFocus();\n\n        this._animationStateChanged.next({\n          state: 'opened',\n          totalTime\n        });\n      } else if (toState === 'exit') {\n        this._restoreFocus();\n\n        this._animationStateChanged.next({\n          state: 'closed',\n          totalTime\n        });\n      }\n    }\n    /** Callback, invoked when an animation on the host starts. */\n\n\n    _onAnimationStart({\n      toState,\n      totalTime\n    }) {\n      if (toState === 'enter') {\n        this._animationStateChanged.next({\n          state: 'opening',\n          totalTime\n        });\n      } else if (toState === 'exit' || toState === 'void') {\n        this._animationStateChanged.next({\n          state: 'closing',\n          totalTime\n        });\n      }\n    }\n    /** Starts the dialog exit animation. */\n\n\n    _startExitAnimation() {\n      this._state = 'exit'; // Mark the container for check so it can react if the\n      // view container is using OnPush change detection.\n\n      this._changeDetectorRef.markForCheck();\n    }\n\n  }\n\n  MatDialogContainer.ɵfac = /* @__PURE__ */function () {\n    let ɵMatDialogContainer_BaseFactory;\n    return function MatDialogContainer_Factory(t) {\n      return (ɵMatDialogContainer_BaseFactory || (ɵMatDialogContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogContainer)))(t || MatDialogContainer);\n    };\n  }();\n\n  MatDialogContainer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDialogContainer,\n    selectors: [[\"mat-dialog-container\"]],\n    hostAttrs: [\"tabindex\", \"-1\", \"aria-modal\", \"true\", 1, \"mat-dialog-container\"],\n    hostVars: 6,\n    hostBindings: function MatDialogContainer_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵsyntheticHostListener(\"@dialogContainer.start\", function MatDialogContainer_animation_dialogContainer_start_HostBindingHandler($event) {\n          return ctx._onAnimationStart($event);\n        })(\"@dialogContainer.done\", function MatDialogContainer_animation_dialogContainer_done_HostBindingHandler($event) {\n          return ctx._onAnimationDone($event);\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx._id);\n        i0.ɵɵattribute(\"role\", ctx._config.role)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledBy)(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n        i0.ɵɵsyntheticHostProperty(\"@dialogContainer\", ctx._state);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkPortalOutlet\", \"\"]],\n    template: function MatDialogContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MatDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n      }\n    },\n    directives: [i3.CdkPortalOutlet],\n    styles: [\".mat-dialog-container{display:block;padding:24px;border-radius:4px;box-sizing:border-box;overflow:auto;outline:0;width:100%;height:100%;min-height:inherit;max-height:inherit}.cdk-high-contrast-active .mat-dialog-container{outline:solid 1px}.mat-dialog-content{display:block;margin:0 -24px;padding:0 24px;max-height:65vh;overflow:auto;-webkit-overflow-scrolling:touch}.mat-dialog-title{margin:0 0 20px;display:block}.mat-dialog-actions{padding:8px 0;display:flex;flex-wrap:wrap;min-height:52px;align-items:center;box-sizing:content-box;margin-bottom:-24px}.mat-dialog-actions[align=end]{justify-content:flex-end}.mat-dialog-actions[align=center]{justify-content:center}.mat-dialog-actions .mat-button-base+.mat-button-base,.mat-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [matDialogAnimations.dialogContainer]\n    }\n  });\n  return MatDialogContainer;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// TODO(jelbourn): resizing\n// Counter for unique dialog ids.\n\n\nlet uniqueId = 0;\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\n\nclass MatDialogRef {\n  constructor(_overlayRef, _containerInstance,\n  /** Id of the dialog. */\n  id = `mat-dialog-${uniqueId++}`) {\n    this._overlayRef = _overlayRef;\n    this._containerInstance = _containerInstance;\n    this.id = id;\n    /** Whether the user is allowed to close the dialog. */\n\n    this.disableClose = this._containerInstance._config.disableClose;\n    /** Subject for notifying the user that the dialog has finished opening. */\n\n    this._afterOpened = new Subject();\n    /** Subject for notifying the user that the dialog has finished closing. */\n\n    this._afterClosed = new Subject();\n    /** Subject for notifying the user that the dialog has started closing. */\n\n    this._beforeClosed = new Subject();\n    /** Current state of the dialog. */\n\n    this._state = 0\n    /* OPEN */\n    ; // Pass the id along to the container.\n\n    _containerInstance._id = id; // Emit when opening animation completes\n\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'opened'), take(1)).subscribe(() => {\n      this._afterOpened.next();\n\n      this._afterOpened.complete();\n    }); // Dispose overlay when closing animation is complete\n\n\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closed'), take(1)).subscribe(() => {\n      clearTimeout(this._closeFallbackTimeout);\n\n      this._finishDialogClose();\n    });\n\n    _overlayRef.detachments().subscribe(() => {\n      this._beforeClosed.next(this._result);\n\n      this._beforeClosed.complete();\n\n      this._afterClosed.next(this._result);\n\n      this._afterClosed.complete();\n\n      this.componentInstance = null;\n\n      this._overlayRef.dispose();\n    });\n\n    _overlayRef.keydownEvents().pipe(filter(event => {\n      return event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event);\n    })).subscribe(event => {\n      event.preventDefault();\n\n      _closeDialogVia(this, 'keyboard');\n    });\n\n    _overlayRef.backdropClick().subscribe(() => {\n      if (this.disableClose) {\n        this._containerInstance._recaptureFocus();\n      } else {\n        _closeDialogVia(this, 'mouse');\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param dialogResult Optional result to return to the dialog opener.\n   */\n\n\n  close(dialogResult) {\n    this._result = dialogResult; // Transition the backdrop in parallel to the dialog.\n\n    this._containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closing'), take(1)).subscribe(event => {\n      this._beforeClosed.next(dialogResult);\n\n      this._beforeClosed.complete();\n\n      this._overlayRef.detachBackdrop(); // The logic that disposes of the overlay depends on the exit animation completing, however\n      // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n      // timeout which will clean everything up if the animation hasn't fired within the specified\n      // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n      // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n\n\n      this._closeFallbackTimeout = setTimeout(() => this._finishDialogClose(), event.totalTime + 100);\n    });\n\n    this._state = 1\n    /* CLOSING */\n    ;\n\n    this._containerInstance._startExitAnimation();\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished opening.\n   */\n\n\n  afterOpened() {\n    return this._afterOpened;\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished closing.\n   */\n\n\n  afterClosed() {\n    return this._afterClosed;\n  }\n  /**\n   * Gets an observable that is notified when the dialog has started closing.\n   */\n\n\n  beforeClosed() {\n    return this._beforeClosed;\n  }\n  /**\n   * Gets an observable that emits when the overlay's backdrop has been clicked.\n   */\n\n\n  backdropClick() {\n    return this._overlayRef.backdropClick();\n  }\n  /**\n   * Gets an observable that emits when keydown events are targeted on the overlay.\n   */\n\n\n  keydownEvents() {\n    return this._overlayRef.keydownEvents();\n  }\n  /**\n   * Updates the dialog's position.\n   * @param position New dialog position.\n   */\n\n\n  updatePosition(position) {\n    let strategy = this._getPositionStrategy();\n\n    if (position && (position.left || position.right)) {\n      position.left ? strategy.left(position.left) : strategy.right(position.right);\n    } else {\n      strategy.centerHorizontally();\n    }\n\n    if (position && (position.top || position.bottom)) {\n      position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n    } else {\n      strategy.centerVertically();\n    }\n\n    this._overlayRef.updatePosition();\n\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n\n\n  updateSize(width = '', height = '') {\n    this._overlayRef.updateSize({\n      width,\n      height\n    });\n\n    this._overlayRef.updatePosition();\n\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n\n\n  addPanelClass(classes) {\n    this._overlayRef.addPanelClass(classes);\n\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n\n\n  removePanelClass(classes) {\n    this._overlayRef.removePanelClass(classes);\n\n    return this;\n  }\n  /** Gets the current state of the dialog's lifecycle. */\n\n\n  getState() {\n    return this._state;\n  }\n  /**\n   * Finishes the dialog close by updating the state of the dialog\n   * and disposing the overlay.\n   */\n\n\n  _finishDialogClose() {\n    this._state = 2\n    /* CLOSED */\n    ;\n\n    this._overlayRef.dispose();\n  }\n  /** Fetches the position strategy object from the overlay ref. */\n\n\n  _getPositionStrategy() {\n    return this._overlayRef.getConfig().positionStrategy;\n  }\n\n}\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\n\n\nfunction _closeDialogVia(ref, interactionType, result) {\n  // Some mock dialog ref instances in tests do not have the `_containerInstance` property.\n  // For those, we keep the behavior as is and do not deal with the interaction type.\n  if (ref._containerInstance !== undefined) {\n    ref._containerInstance._closeInteractionType = interactionType;\n  }\n\n  return ref.close(result);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\n\n\nconst MAT_DIALOG_DATA = /*#__PURE__*/new InjectionToken('MatDialogData');\n/** Injection token that can be used to specify default dialog options. */\n\nconst MAT_DIALOG_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-dialog-default-options');\n/** Injection token that determines the scroll handling while the dialog is open. */\n\nconst MAT_DIALOG_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('mat-dialog-scroll-strategy');\n/** @docs-private */\n\nfunction MAT_DIALOG_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/** @docs-private */\n\n\nfunction MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/** @docs-private */\n\n\nconst MAT_DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Base class for dialog services. The base dialog service allows\n * for arbitrary dialog refs and dialog container components.\n */\n\nlet _MatDialogBase = /*#__PURE__*/(() => {\n  class _MatDialogBase {\n    constructor(_overlay, _injector, _defaultOptions, _parentDialog, _overlayContainer, scrollStrategy, _dialogRefConstructor, _dialogContainerType, _dialogDataToken, _animationMode) {\n      this._overlay = _overlay;\n      this._injector = _injector;\n      this._defaultOptions = _defaultOptions;\n      this._parentDialog = _parentDialog;\n      this._overlayContainer = _overlayContainer;\n      this._dialogRefConstructor = _dialogRefConstructor;\n      this._dialogContainerType = _dialogContainerType;\n      this._dialogDataToken = _dialogDataToken;\n      this._animationMode = _animationMode;\n      this._openDialogsAtThisLevel = [];\n      this._afterAllClosedAtThisLevel = new Subject();\n      this._afterOpenedAtThisLevel = new Subject();\n      this._ariaHiddenElements = new Map();\n      this._dialogAnimatingOpen = false; // TODO (jelbourn): tighten the typing right-hand side of this expression.\n\n      /**\n       * Stream that emits when all open dialog have finished closing.\n       * Will emit on subscribe if there are no open dialogs to begin with.\n       */\n\n      this.afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n      this._scrollStrategy = scrollStrategy;\n    }\n    /** Keeps track of the currently-open dialogs. */\n\n\n    get openDialogs() {\n      return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n\n\n    get afterOpened() {\n      return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n\n    _getAfterAllClosed() {\n      const parent = this._parentDialog;\n      return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n\n    open(componentOrTemplateRef, config) {\n      config = _applyConfigDefaults(config, this._defaultOptions || new MatDialogConfig());\n\n      if (config.id && this.getDialogById(config.id) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n      } // If there is a dialog that is currently animating open, return the MatDialogRef of that dialog\n\n\n      if (this._dialogAnimatingOpen) {\n        return this._lastDialogRef;\n      }\n\n      const overlayRef = this._createOverlay(config);\n\n      const dialogContainer = this._attachDialogContainer(overlayRef, config);\n\n      if (this._animationMode !== 'NoopAnimations') {\n        const animationStateSubscription = dialogContainer._animationStateChanged.subscribe(dialogAnimationEvent => {\n          if (dialogAnimationEvent.state === 'opening') {\n            this._dialogAnimatingOpen = true;\n          }\n\n          if (dialogAnimationEvent.state === 'opened') {\n            this._dialogAnimatingOpen = false;\n            animationStateSubscription.unsubscribe();\n          }\n        });\n\n        if (!this._animationStateSubscriptions) {\n          this._animationStateSubscriptions = new Subscription();\n        }\n\n        this._animationStateSubscriptions.add(animationStateSubscription);\n      }\n\n      const dialogRef = this._attachDialogContent(componentOrTemplateRef, dialogContainer, overlayRef, config);\n\n      this._lastDialogRef = dialogRef; // If this is the first dialog that we're opening, hide all the non-overlay content.\n\n      if (!this.openDialogs.length) {\n        this._hideNonDialogContentFromAssistiveTechnology();\n      }\n\n      this.openDialogs.push(dialogRef);\n      dialogRef.afterClosed().subscribe(() => this._removeOpenDialog(dialogRef));\n      this.afterOpened.next(dialogRef); // Notify the dialog container that the content has been attached.\n\n      dialogContainer._initializeWithAttachedContent();\n\n      return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n\n\n    closeAll() {\n      this._closeDialogs(this.openDialogs);\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n\n\n    getDialogById(id) {\n      return this.openDialogs.find(dialog => dialog.id === id);\n    }\n\n    ngOnDestroy() {\n      // Only close the dialogs at this level on destroy\n      // since the parent service may still be active.\n      this._closeDialogs(this._openDialogsAtThisLevel);\n\n      this._afterAllClosedAtThisLevel.complete();\n\n      this._afterOpenedAtThisLevel.complete(); // Clean up any subscriptions to dialogs that never finished opening.\n\n\n      if (this._animationStateSubscriptions) {\n        this._animationStateSubscriptions.unsubscribe();\n      }\n    }\n    /**\n     * Creates the overlay into which the dialog will be loaded.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to the OverlayRef for the created overlay.\n     */\n\n\n    _createOverlay(config) {\n      const overlayConfig = this._getOverlayConfig(config);\n\n      return this._overlay.create(overlayConfig);\n    }\n    /**\n     * Creates an overlay config from a dialog config.\n     * @param dialogConfig The dialog configuration.\n     * @returns The overlay configuration.\n     */\n\n\n    _getOverlayConfig(dialogConfig) {\n      const state = new OverlayConfig({\n        positionStrategy: this._overlay.position().global(),\n        scrollStrategy: dialogConfig.scrollStrategy || this._scrollStrategy(),\n        panelClass: dialogConfig.panelClass,\n        hasBackdrop: dialogConfig.hasBackdrop,\n        direction: dialogConfig.direction,\n        minWidth: dialogConfig.minWidth,\n        minHeight: dialogConfig.minHeight,\n        maxWidth: dialogConfig.maxWidth,\n        maxHeight: dialogConfig.maxHeight,\n        disposeOnNavigation: dialogConfig.closeOnNavigation\n      });\n\n      if (dialogConfig.backdropClass) {\n        state.backdropClass = dialogConfig.backdropClass;\n      }\n\n      return state;\n    }\n    /**\n     * Attaches a dialog container to a dialog's already-created overlay.\n     * @param overlay Reference to the dialog's underlying overlay.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to a ComponentRef for the attached container.\n     */\n\n\n    _attachDialogContainer(overlay, config) {\n      const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n      const injector = Injector.create({\n        parent: userInjector || this._injector,\n        providers: [{\n          provide: MatDialogConfig,\n          useValue: config\n        }]\n      });\n      const containerPortal = new ComponentPortal(this._dialogContainerType, config.viewContainerRef, injector, config.componentFactoryResolver);\n      const containerRef = overlay.attach(containerPortal);\n      return containerRef.instance;\n    }\n    /**\n     * Attaches the user-provided component to the already-created dialog container.\n     * @param componentOrTemplateRef The type of component being loaded into the dialog,\n     *     or a TemplateRef to instantiate as the content.\n     * @param dialogContainer Reference to the wrapping dialog container.\n     * @param overlayRef Reference to the overlay in which the dialog resides.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to the MatDialogRef that should be returned to the user.\n     */\n\n\n    _attachDialogContent(componentOrTemplateRef, dialogContainer, overlayRef, config) {\n      // Create a reference to the dialog we're creating in order to give the user a handle\n      // to modify and close it.\n      const dialogRef = new this._dialogRefConstructor(overlayRef, dialogContainer, config.id);\n\n      if (componentOrTemplateRef instanceof TemplateRef) {\n        dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, {\n          $implicit: config.data,\n          dialogRef\n        }));\n      } else {\n        const injector = this._createInjector(config, dialogRef, dialogContainer);\n\n        const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector));\n        dialogRef.componentInstance = contentRef.instance;\n      }\n\n      dialogRef.updateSize(config.width, config.height).updatePosition(config.position);\n      return dialogRef;\n    }\n    /**\n     * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n     * of a dialog to close itself and, optionally, to return a value.\n     * @param config Config object that is used to construct the dialog.\n     * @param dialogRef Reference to the dialog.\n     * @param dialogContainer Dialog container element that wraps all of the contents.\n     * @returns The custom injector that can be used inside the dialog.\n     */\n\n\n    _createInjector(config, dialogRef, dialogContainer) {\n      const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector; // The dialog container should be provided as the dialog container and the dialog's\n      // content are created out of the same `ViewContainerRef` and as such, are siblings\n      // for injector purposes. To allow the hierarchy that is expected, the dialog\n      // container is explicitly provided in the injector.\n\n      const providers = [{\n        provide: this._dialogContainerType,\n        useValue: dialogContainer\n      }, {\n        provide: this._dialogDataToken,\n        useValue: config.data\n      }, {\n        provide: this._dialogRefConstructor,\n        useValue: dialogRef\n      }];\n\n      if (config.direction && (!userInjector || !userInjector.get(Directionality, null, InjectFlags.Optional))) {\n        providers.push({\n          provide: Directionality,\n          useValue: {\n            value: config.direction,\n            change: of()\n          }\n        });\n      }\n\n      return Injector.create({\n        parent: userInjector || this._injector,\n        providers\n      });\n    }\n    /**\n     * Removes a dialog from the array of open dialogs.\n     * @param dialogRef Dialog to be removed.\n     */\n\n\n    _removeOpenDialog(dialogRef) {\n      const index = this.openDialogs.indexOf(dialogRef);\n\n      if (index > -1) {\n        this.openDialogs.splice(index, 1); // If all the dialogs were closed, remove/restore the `aria-hidden`\n        // to a the siblings and emit to the `afterAllClosed` stream.\n\n        if (!this.openDialogs.length) {\n          this._ariaHiddenElements.forEach((previousValue, element) => {\n            if (previousValue) {\n              element.setAttribute('aria-hidden', previousValue);\n            } else {\n              element.removeAttribute('aria-hidden');\n            }\n          });\n\n          this._ariaHiddenElements.clear();\n\n          this._getAfterAllClosed().next();\n        }\n      }\n    }\n    /**\n     * Hides all of the content that isn't an overlay from assistive technology.\n     */\n\n\n    _hideNonDialogContentFromAssistiveTechnology() {\n      const overlayContainer = this._overlayContainer.getContainerElement(); // Ensure that the overlay container is attached to the DOM.\n\n\n      if (overlayContainer.parentElement) {\n        const siblings = overlayContainer.parentElement.children;\n\n        for (let i = siblings.length - 1; i > -1; i--) {\n          let sibling = siblings[i];\n\n          if (sibling !== overlayContainer && sibling.nodeName !== 'SCRIPT' && sibling.nodeName !== 'STYLE' && !sibling.hasAttribute('aria-live')) {\n            this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n\n            sibling.setAttribute('aria-hidden', 'true');\n          }\n        }\n      }\n    }\n    /** Closes all of the dialogs in an array. */\n\n\n    _closeDialogs(dialogs) {\n      let i = dialogs.length;\n\n      while (i--) {\n        // The `_openDialogs` property isn't updated after close until the rxjs subscription\n        // runs on the next microtask, in addition to modifying the array as we're going\n        // through it. We loop through all of them and call close without assuming that\n        // they'll be removed from the list instantaneously.\n        dialogs[i].close();\n      }\n    }\n\n  }\n\n  _MatDialogBase.ɵfac = function _MatDialogBase_Factory(t) {\n    i0.ɵɵinvalidFactory();\n  };\n\n  _MatDialogBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatDialogBase\n  });\n  return _MatDialogBase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Service to open Material Design modal dialogs.\n */\n\n\nlet MatDialog = /*#__PURE__*/(() => {\n  class MatDialog extends _MatDialogBase {\n    constructor(overlay, injector,\n    /**\n     * @deprecated `_location` parameter to be removed.\n     * @breaking-change 10.0.0\n     */\n    location, defaultOptions, scrollStrategy, parentDialog, overlayContainer, animationMode) {\n      super(overlay, injector, defaultOptions, parentDialog, overlayContainer, scrollStrategy, MatDialogRef, MatDialogContainer, MAT_DIALOG_DATA, animationMode);\n    }\n\n  }\n\n  MatDialog.ɵfac = function MatDialog_Factory(t) {\n    return new (t || MatDialog)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i2.Location, 8), i0.ɵɵinject(MAT_DIALOG_DEFAULT_OPTIONS, 8), i0.ɵɵinject(MAT_DIALOG_SCROLL_STRATEGY), i0.ɵɵinject(MatDialog, 12), i0.ɵɵinject(i1$1.OverlayContainer), i0.ɵɵinject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  MatDialog.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatDialog,\n    factory: MatDialog.ɵfac\n  });\n  return MatDialog;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Applies default options to the dialog config.\n * @param config Config to be modified.\n * @param defaultOptions Default options provided.\n * @returns The new configuration object.\n */\n\n\nfunction _applyConfigDefaults(config, defaultOptions) {\n  return Object.assign(Object.assign({}, defaultOptions), config);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Counter used to generate unique IDs for dialog elements. */\n\n\nlet dialogElementUid = 0;\n/**\n * Button that will close the current dialog.\n */\n\nlet MatDialogClose = /*#__PURE__*/(() => {\n  class MatDialogClose {\n    constructor(\n    /**\n     * Reference to the containing dialog.\n     * @deprecated `dialogRef` property to become private.\n     * @breaking-change 13.0.0\n     */\n    // The dialog title directive is always used in combination with a `MatDialogRef`.\n    // tslint:disable-next-line: lightweight-tokens\n    dialogRef, _elementRef, _dialog) {\n      this.dialogRef = dialogRef;\n      this._elementRef = _elementRef;\n      this._dialog = _dialog;\n      /** Default to \"button\" to prevents accidental form submits. */\n\n      this.type = 'button';\n    }\n\n    ngOnInit() {\n      if (!this.dialogRef) {\n        // When this directive is included in a dialog via TemplateRef (rather than being\n        // in a Component), the DialogRef isn't available via injection because embedded\n        // views cannot be given a custom injector. Instead, we look up the DialogRef by\n        // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n        // be resolved at constructor time.\n        this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n      }\n    }\n\n    ngOnChanges(changes) {\n      const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n\n      if (proxiedChange) {\n        this.dialogResult = proxiedChange.currentValue;\n      }\n    }\n\n    _onButtonClick(event) {\n      // Determinate the focus origin using the click event, because using the FocusMonitor will\n      // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n      // dialog, and therefore clicking the button won't result in a focus change. This means that\n      // the FocusMonitor won't detect any origin change, and will always output `program`.\n      _closeDialogVia(this.dialogRef, event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse', this.dialogResult);\n    }\n\n  }\n\n  MatDialogClose.ɵfac = function MatDialogClose_Factory(t) {\n    return new (t || MatDialogClose)(i0.ɵɵdirectiveInject(MatDialogRef, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MatDialog));\n  };\n\n  MatDialogClose.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogClose,\n    selectors: [[\"\", \"mat-dialog-close\", \"\"], [\"\", \"matDialogClose\", \"\"]],\n    hostVars: 2,\n    hostBindings: function MatDialogClose_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatDialogClose_click_HostBindingHandler($event) {\n          return ctx._onButtonClick($event);\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"type\", ctx.type);\n      }\n    },\n    inputs: {\n      ariaLabel: [\"aria-label\", \"ariaLabel\"],\n      type: \"type\",\n      dialogResult: [\"mat-dialog-close\", \"dialogResult\"],\n      _matDialogClose: [\"matDialogClose\", \"_matDialogClose\"]\n    },\n    exportAs: [\"matDialogClose\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return MatDialogClose;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\n\n\nlet MatDialogTitle = /*#__PURE__*/(() => {\n  class MatDialogTitle {\n    constructor( // The dialog title directive is always used in combination with a `MatDialogRef`.\n    // tslint:disable-next-line: lightweight-tokens\n    _dialogRef, _elementRef, _dialog) {\n      this._dialogRef = _dialogRef;\n      this._elementRef = _elementRef;\n      this._dialog = _dialog;\n      /** Unique id for the dialog title. If none is supplied, it will be auto-generated. */\n\n      this.id = `mat-dialog-title-${dialogElementUid++}`;\n    }\n\n    ngOnInit() {\n      if (!this._dialogRef) {\n        this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n      }\n\n      if (this._dialogRef) {\n        Promise.resolve().then(() => {\n          const container = this._dialogRef._containerInstance;\n\n          if (container && !container._ariaLabelledBy) {\n            container._ariaLabelledBy = this.id;\n          }\n        });\n      }\n    }\n\n  }\n\n  MatDialogTitle.ɵfac = function MatDialogTitle_Factory(t) {\n    return new (t || MatDialogTitle)(i0.ɵɵdirectiveInject(MatDialogRef, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MatDialog));\n  };\n\n  MatDialogTitle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogTitle,\n    selectors: [[\"\", \"mat-dialog-title\", \"\"], [\"\", \"matDialogTitle\", \"\"]],\n    hostAttrs: [1, \"mat-dialog-title\"],\n    hostVars: 1,\n    hostBindings: function MatDialogTitle_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n      }\n    },\n    inputs: {\n      id: \"id\"\n    },\n    exportAs: [\"matDialogTitle\"]\n  });\n  return MatDialogTitle;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Scrollable content container of a dialog.\n */\n\n\nlet MatDialogContent = /*#__PURE__*/(() => {\n  class MatDialogContent {}\n\n  MatDialogContent.ɵfac = function MatDialogContent_Factory(t) {\n    return new (t || MatDialogContent)();\n  };\n\n  MatDialogContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogContent,\n    selectors: [[\"\", \"mat-dialog-content\", \"\"], [\"mat-dialog-content\"], [\"\", \"matDialogContent\", \"\"]],\n    hostAttrs: [1, \"mat-dialog-content\"]\n  });\n  return MatDialogContent;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\n\n\nlet MatDialogActions = /*#__PURE__*/(() => {\n  class MatDialogActions {}\n\n  MatDialogActions.ɵfac = function MatDialogActions_Factory(t) {\n    return new (t || MatDialogActions)();\n  };\n\n  MatDialogActions.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogActions,\n    selectors: [[\"\", \"mat-dialog-actions\", \"\"], [\"mat-dialog-actions\"], [\"\", \"matDialogActions\", \"\"]],\n    hostAttrs: [1, \"mat-dialog-actions\"]\n  });\n  return MatDialogActions;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\n\n\nfunction getClosestDialog(element, openDialogs) {\n  let parent = element.nativeElement.parentElement;\n\n  while (parent && !parent.classList.contains('mat-dialog-container')) {\n    parent = parent.parentElement;\n  }\n\n  return parent ? openDialogs.find(dialog => dialog.id === parent.id) : null;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet MatDialogModule = /*#__PURE__*/(() => {\n  class MatDialogModule {}\n\n  MatDialogModule.ɵfac = function MatDialogModule_Factory(t) {\n    return new (t || MatDialogModule)();\n  };\n\n  MatDialogModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatDialogModule\n  });\n  MatDialogModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MatDialog, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER],\n    imports: [[OverlayModule, PortalModule, MatCommonModule], MatCommonModule]\n  });\n  return MatDialogModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_DIALOG_DATA, MAT_DIALOG_DEFAULT_OPTIONS, MAT_DIALOG_SCROLL_STRATEGY, MAT_DIALOG_SCROLL_STRATEGY_FACTORY, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, MatDialog, MatDialogActions, MatDialogClose, MatDialogConfig, MatDialogContainer, MatDialogContent, MatDialogModule, MatDialogRef, MatDialogTitle, _MatDialogBase, _MatDialogContainerBase, _closeDialogVia, matDialogAnimations, throwMatDialogContentAlreadyAttachedError }; //# sourceMappingURL=dialog.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}