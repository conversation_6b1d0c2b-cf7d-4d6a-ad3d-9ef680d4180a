{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { ELibraryRoutes } from './e-library.routing';\nimport { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';\nimport { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-smart-modal\";\nexport let ELibraryModule = /*#__PURE__*/(() => {\n  class ELibraryModule {}\n\n  ELibraryModule.ɵfac = function ELibraryModule_Factory(t) {\n    return new (t || ELibraryModule)();\n  };\n\n  ELibraryModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ELibraryModule\n  });\n  ELibraryModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [NgxSmartModalService],\n    imports: [[CommonModule, RouterModule.forChild(ELibraryRoutes), SharedModule, NgxSmartModalModule.forRoot(), NgxExtendedPdfViewerModule]]\n  });\n  return ELibraryModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}