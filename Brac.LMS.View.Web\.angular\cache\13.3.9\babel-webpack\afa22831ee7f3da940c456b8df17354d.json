{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { environment } from '../../environments/environment';\nimport { NavigationEnd } from '@angular/router';\nimport { BlockUI } from 'ng-block-ui';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport $ from 'jquery';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"../_helpers/confirm-dialog/confirm.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"ngx-bootstrap/modal\";\nimport * as i7 from \"ng-block-ui\";\nimport * as i8 from \"ngx-bootstrap/rating\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@angular/flex-layout/extended\";\nimport * as i11 from \"../_helpers/safe-pipe\";\n\nfunction CourseDetailsPreviewComponent_div_1_i_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 35);\n    i0.ɵɵlistener(\"click\", function CourseDetailsPreviewComponent_div_1_i_26_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return ctx_r7.createBookmarkOrUnbookmark();\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsPreviewComponent_div_1_i_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 36);\n    i0.ɵɵlistener(\"click\", function CourseDetailsPreviewComponent_div_1_i_27_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return ctx_r9.createBookmarkOrUnbookmark();\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsPreviewComponent_div_1_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelementStart(1, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function CourseDetailsPreviewComponent_div_1_div_38_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n\n      const _r1 = i0.ɵɵreference(3);\n\n      return ctx_r11.openTeammatesEnrollment(_r1);\n    });\n    i0.ɵɵtext(2, \"Enroll My Teammates\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsPreviewComponent_div_1_div_43_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsPreviewComponent_div_1_div_43_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsPreviewComponent_div_1_div_43_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsPreviewComponent_div_1_div_43_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsPreviewComponent_div_1_div_43_p_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, item_r13.VideoDurationSecond * 1000, \"mm:ss\"), \"\");\n  }\n}\n\nfunction CourseDetailsPreviewComponent_div_1_div_43_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 51);\n    i0.ɵɵtext(1, \"Mock Test \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsPreviewComponent_div_1_div_43_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.formatFileSize(item_r13.FileSizeKb), \" \");\n  }\n}\n\nfunction CourseDetailsPreviewComponent_div_1_div_43_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 51);\n    i0.ɵɵtext(1, \" Certification Test\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseDetailsPreviewComponent_div_1_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelementStart(1, \"div\", 38);\n    i0.ɵɵelementStart(2, \"div\", 39);\n    i0.ɵɵtemplate(3, CourseDetailsPreviewComponent_div_1_div_43_span_3_Template, 2, 0, \"span\", 40);\n    i0.ɵɵtemplate(4, CourseDetailsPreviewComponent_div_1_div_43_span_4_Template, 2, 0, \"span\", 40);\n    i0.ɵɵtemplate(5, CourseDetailsPreviewComponent_div_1_div_43_span_5_Template, 2, 0, \"span\", 40);\n    i0.ɵɵtemplate(6, CourseDetailsPreviewComponent_div_1_div_43_span_6_Template, 2, 0, \"span\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 41);\n    i0.ɵɵelementStart(8, \"div\", 42);\n    i0.ɵɵelementStart(9, \"div\", 43);\n    i0.ɵɵelementStart(10, \"div\", 44);\n    i0.ɵɵtext(11);\n    i0.ɵɵtemplate(12, CourseDetailsPreviewComponent_div_1_div_43_p_12_Template, 3, 4, \"p\", 45);\n    i0.ɵɵtemplate(13, CourseDetailsPreviewComponent_div_1_div_43_p_13_Template, 2, 0, \"p\", 45);\n    i0.ɵɵtemplate(14, CourseDetailsPreviewComponent_div_1_div_43_p_14_Template, 2, 1, \"p\", 45);\n    i0.ɵɵtemplate(15, CourseDetailsPreviewComponent_div_1_div_43_p_15_Template, 2, 0, \"p\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r13 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r13.Type == \"Video\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r13.Type == \"MockTest\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r13.Type == \"Document\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r13.Type == \"CertificateTest\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", item_r13.Title, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r13.Type == \"Video\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r13.Type == \"MockTest\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r13.Type == \"Document\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r13.Type == \"CertificateTest\");\n  }\n}\n\nfunction CourseDetailsPreviewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelementStart(1, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 4);\n    i0.ɵɵelementStart(3, \"div\", 5);\n    i0.ɵɵelementStart(4, \"div\", 6);\n    i0.ɵɵelementStart(5, \"div\", 7);\n    i0.ɵɵelementStart(6, \"h1\", 8);\n    i0.ɵɵtext(7, \"Course Overview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"a\", 9);\n    i0.ɵɵlistener(\"click\", function CourseDetailsPreviewComponent_div_1_Template_a_click_8_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return ctx_r24.backClicked();\n    });\n    i0.ɵɵelement(9, \"i\", 10);\n    i0.ɵɵtext(10, \"Go Back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"hr\", 11);\n    i0.ɵɵelementStart(12, \"div\", 3);\n    i0.ɵɵelementStart(13, \"div\", 12);\n    i0.ɵɵelementStart(14, \"div\", 3);\n    i0.ɵɵelementStart(15, \"div\", 13);\n    i0.ɵɵelementStart(16, \"div\", 14);\n    i0.ɵɵelementStart(17, \"h1\", 15);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 16);\n    i0.ɵɵelementStart(20, \"div\", 17);\n    i0.ɵɵelementStart(21, \"rating\", 18);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseDetailsPreviewComponent_div_1_Template_rating_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return ctx_r26.courseData.Rating = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 19);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 20);\n    i0.ɵɵelementStart(25, \"div\", 21);\n    i0.ɵɵtemplate(26, CourseDetailsPreviewComponent_div_1_i_26_Template, 1, 0, \"i\", 22);\n    i0.ɵɵtemplate(27, CourseDetailsPreviewComponent_div_1_i_27_Template, 1, 0, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 3);\n    i0.ɵɵelementStart(29, \"div\", 24);\n    i0.ɵɵelement(30, \"p\", 25);\n    i0.ɵɵpipe(31, \"safe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 26);\n    i0.ɵɵelementStart(33, \"div\", 27);\n    i0.ɵɵelement(34, \"img\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 29);\n    i0.ɵɵelementStart(36, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function CourseDetailsPreviewComponent_div_1_Template_button_click_36_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return ctx_r27.getEnrolled();\n    });\n    i0.ɵɵtext(37, \"Enroll Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(38, CourseDetailsPreviewComponent_div_1_div_38_Template, 3, 0, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(39, \"hr\", 32);\n    i0.ɵɵelementStart(40, \"div\", 33);\n    i0.ɵɵelementStart(41, \"h1\", 8);\n    i0.ɵɵtext(42, \"Course Outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(43, CourseDetailsPreviewComponent_div_1_div_43_Template, 16, 9, \"div\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate(ctx_r0.courseData.Title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.courseData.Rating)(\"max\", 5)(\"readonly\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r0.courseData.Rating, \")\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.courseData.Bookmarked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.courseData.Bookmarked);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(31, 12, ctx_r0.courseData.Description, \"html\"), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate2(\"src\", \"\", ctx_r0.mediaBaseUrl, \"\", ctx_r0.courseData.ImagePath, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.courseData.IsManager);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.courseData.Contents);\n  }\n}\n\nfunction CourseDetailsPreviewComponent_ng_template_2_div_6_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 73);\n  }\n\n  if (rf & 2) {\n    const item_r29 = i0.ɵɵnextContext().$implicit;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r30.baseUrl + item_r29.ImagePath, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction CourseDetailsPreviewComponent_ng_template_2_div_6_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 74);\n  }\n}\n\nfunction CourseDetailsPreviewComponent_ng_template_2_div_6_div_14_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 78);\n  }\n}\n\nfunction CourseDetailsPreviewComponent_ng_template_2_div_6_div_14_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 79);\n  }\n}\n\nfunction CourseDetailsPreviewComponent_ng_template_2_div_6_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtemplate(1, CourseDetailsPreviewComponent_ng_template_2_div_6_div_14_i_1_Template, 1, 0, \"i\", 76);\n    i0.ɵɵtemplate(2, CourseDetailsPreviewComponent_ng_template_2_div_6_div_14_i_2_Template, 1, 0, \"i\", 77);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r29.Selected);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r29.Selected);\n  }\n}\n\nconst _c0 = function (a0, a1) {\n  return {\n    \"enrolled\": a0,\n    \"cursor-pointer\": a1\n  };\n};\n\nfunction CourseDetailsPreviewComponent_ng_template_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelementStart(1, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function CourseDetailsPreviewComponent_ng_template_2_div_6_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r38);\n      const item_r29 = restoredCtx.$implicit;\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return ctx_r37.onClickTeammate(item_r29);\n    });\n    i0.ɵɵelementStart(2, \"div\", 63);\n    i0.ɵɵtemplate(3, CourseDetailsPreviewComponent_ng_template_2_div_6_img_3_Template, 1, 1, \"img\", 64);\n    i0.ɵɵtemplate(4, CourseDetailsPreviewComponent_ng_template_2_div_6_img_4_Template, 1, 0, \"img\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 66);\n    i0.ɵɵelementStart(6, \"div\", 67);\n    i0.ɵɵelementStart(7, \"h5\", 68);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 69);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 70);\n    i0.ɵɵelementStart(12, \"small\", 71);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, CourseDetailsPreviewComponent_ng_template_2_div_6_div_14_Template, 3, 2, \"div\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r29 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c0, item_r29.Enrolled, !item_r29.Enrolled));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r29.ImagePath);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r29.ImagePath);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r29.Name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r29.Position);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r29.PIN);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r29.Enrolled);\n  }\n}\n\nfunction CourseDetailsPreviewComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelementStart(1, \"h4\", 53);\n    i0.ɵɵtext(2, \" Enroll My Teammates \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function CourseDetailsPreviewComponent_ng_template_2_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return ctx_r39.modalHideTeammatesEnrollment();\n    });\n    i0.ɵɵelement(4, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 56);\n    i0.ɵɵtemplate(6, CourseDetailsPreviewComponent_ng_template_2_div_6_Template, 15, 10, \"div\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 58);\n    i0.ɵɵelementStart(8, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function CourseDetailsPreviewComponent_ng_template_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return ctx_r41.modalHideTeammatesEnrollment();\n    });\n    i0.ɵɵelement(9, \"i\", 55);\n    i0.ɵɵtext(10, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function CourseDetailsPreviewComponent_ng_template_2_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return ctx_r42.onSubmitTeammatesEnrollment();\n    });\n    i0.ɵɵelement(12, \"i\", 60);\n    i0.ɵɵtext(13, \" Enroll \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.myTeammates);\n  }\n}\n\nexport class CourseDetailsPreviewComponent {\n  constructor(router, _service, toastr, confirmService, route, _location, modalService) {\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.confirmService = confirmService;\n    this.route = route;\n    this._location = _location;\n    this.modalService = modalService;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.timestamp = new Date().getTime();\n    this.courseData = null;\n    this.rate = 0;\n    this.myTeammates = [];\n    this.pdfSrc = 'https://pdfobject.com/pdf/sample-3pp.pdf';\n    this.courseId = this.route.snapshot.paramMap.get('courseId');\n    this.router.events.subscribe(evt => {\n      if (!(evt instanceof NavigationEnd)) {\n        return;\n      }\n\n      window.scrollTo(0, 0);\n    });\n  }\n\n  ngOnInit() {\n    this.getCourseDetails();\n    setTimeout(() => {\n      this.loadJquery();\n    }, 500);\n  }\n\n  loadJquery() {\n    (function ($) {})($);\n  }\n\n  backClicked() {\n    localStorage.setItem('reload-with-page', '1');\n    console.log('this._location', this._location.historyGo());\n\n    this._location.back();\n  }\n\n  getCourseDetails() {\n    this.blockUI.start('loading...');\n\n    this._service.get('course/get-course-preview/' + this.courseId).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        if (res.Data.Enrollment) {\n          this.router.navigate(['course-details', res.Data.Id]);\n          return;\n        }\n\n        this.courseData = res.Data;\n        this.rate = this.courseData.Rating;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  formatFileSize(fileSizeKb) {\n    if (fileSizeKb >= 1048576) {\n      fileSizeKb = (fileSizeKb / 1048576).toFixed(2) + ' GB';\n    } else if (fileSizeKb >= 1024) {\n      fileSizeKb = (fileSizeKb / 1024).toFixed(2) + ' MB';\n    } else if (fileSizeKb > 1) {\n      fileSizeKb = fileSizeKb + ' KB';\n    } else {\n      fileSizeKb = '0 Kb';\n    }\n\n    return fileSizeKb;\n  }\n\n  createBookmarkOrUnbookmark() {\n    this.blockUI.start('loading...');\n\n    this._service.get('course/bookmark-or-unbookmark/' + this.courseId).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.getCourseDetails();\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  getEnrolled() {\n    this.confirmService.confirm('Are you sure?', 'You are enrolling this course.', 'Yes, Enroll').subscribe({\n      next: data => {\n        if (data) {\n          this.blockUI.start('loading...');\n\n          this._service.get('course/enroll/' + this.courseId).subscribe({\n            next: res => {\n              if (res.Status === ResponseStatus.Warning) {\n                this.toastr.warning(res.Message, 'Warning!', {\n                  timeOut: 2000\n                });\n                return;\n              } else if (res.Status === ResponseStatus.Error) {\n                this.toastr.error(res.Message, 'Error!', {\n                  timeOut: 2000\n                });\n                return;\n              }\n\n              this.router.navigate(['course-details/', this.courseId]);\n            },\n            error: err => {\n              this.toastr.warning(err.Messaage || err, 'Warning!', {\n                closeButton: true,\n                disableTimeOut: false\n              });\n              this.blockUI.stop();\n            },\n            complete: () => this.blockUI.stop()\n          });\n        }\n      },\n      error: error => {},\n      complete: () => {}\n    });\n  } // getEnrolled() {\n  //   this.confirmService\n  //     .confirm('Are you sure?', 'You are enrolling this course.', 'Yes, Enroll')\n  //     .pipe(\n  //       tap((data) => {\n  //         if (data) {\n  //           this.blockUI.start('loading...');\n  //         }\n  //       }),\n  //       switchMap((data) => {\n  //         if (!data) return of(null); // if user cancels\n  //         return this._service.get('course/enroll/' + this.courseId);\n  //       }),\n  //       catchError((err) => {\n  //         this.toastr.warning(err?.Message || err, 'Warning!', {\n  //           closeButton: true,\n  //           disableTimeOut: false,\n  //         });\n  //         this.blockUI.stop();\n  //         return of(null); // to continue the stream\n  //       })\n  //     )\n  //     .subscribe((res: any) => {\n  //       if (!res) return;\n  //       if (res.Status === ResponseStatus.Warning) {\n  //         this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });\n  //       } else if (res.Status === ResponseStatus.Error) {\n  //         this.toastr.error(res.Message, 'Error!', { timeOut: 2000 });\n  //       } else {\n  //         this.router.navigate(['course-details/', this.courseId]);\n  //       }\n  //       this.blockUI.stop();\n  //     });\n  // }\n\n\n  openTeammatesEnrollment(template) {\n    this.blockUI.start('Loading data. Please wait...');\n\n    this._service.get('course/get-my-teammates/' + this.courseId).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.myTeammates = res.Data;\n      this.modalRef = this.modalService.show(template, {\n        class: 'gray',\n        backdrop: 'static'\n      });\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.warning(err.Message || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  onClickTeammate(item) {\n    if (item.Enrolled) return;\n    item.Selected = !item.Selected;\n  }\n\n  modalHideTeammatesEnrollment() {\n    this.modalRef.hide();\n    this.myTeammates = [];\n  }\n\n  onSubmitTeammatesEnrollment() {\n    let traineeIds = this.myTeammates.filter(x => x.Selected).map(x => x.Id);\n\n    if (traineeIds.length === 0) {\n      this.toastr.warning('No teammate has been selected to enroll', 'WARNING!');\n      return;\n    }\n\n    this.blockUI.start('Saving data. Please wait...');\n\n    this._service.post('course/enroll-teammates/' + this.courseId, traineeIds).subscribe({\n      next: res => {\n        this.blockUI.stop();\n\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.modalHideTeammatesEnrollment();\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.warning(err.Message || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n}\n\nCourseDetailsPreviewComponent.ɵfac = function CourseDetailsPreviewComponent_Factory(t) {\n  return new (t || CourseDetailsPreviewComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i4.ConfirmService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Location), i0.ɵɵdirectiveInject(i6.BsModalService));\n};\n\nCourseDetailsPreviewComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CourseDetailsPreviewComponent,\n  selectors: [[\"app-course-details-preview\"]],\n  decls: 4,\n  vars: 1,\n  consts: [[\"class\", \"container-fluid position-relative zindex-5 pb-0 mb-md-1 p-3 margin-top-neg-15\", 4, \"ngIf\"], [\"templateFeedBackModal\", \"\"], [1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"row\"], [1, \"col-lg-10\", \"p-5\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"btn\", \"btn-link\", \"text-decoration-none\", \"fs-4\", \"fw-bold\", \"btn-sm\", \"mb-2\", 3, \"click\"], [1, \"fs-4\", \"ai-arrow-left\", \"fs-base\", \"me-2\"], [1, \"mt-1\", \"mb-4\"], [1, \"col-md-7\"], [1, \"col-md-10\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-2\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-3\"], [1, \"row\", \"mt--2\"], [1, \"col-12\", \"pe-0\"], [1, \"fs-3\", \"text-gold\", 3, \"ngModel\", \"max\", \"readonly\", \"ngModelChange\"], [1, \"h4\", \"pt-1\", \"text-gold\"], [1, \"col-md-2\"], [1, \"d-inline-block\", \"fw-normal\", \"px-2\", \"py-1\", \"rounded-1\", \"float-end\"], [\"class\", \"fa fa-bookmark text-gold fs-3 div-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"fa-regular fa-bookmark text-muted fs-3 div-pointer\", 3, \"click\", 4, \"ngIf\"], [1, \"col-md-12\", \"py-3\"], [1, \"text-justify\", 3, \"innerHTML\"], [1, \"col-md-5\"], [1, \"card-img-top\", \"card-img-bottom\"], [3, \"src\"], [1, \"d-grid\", \"mt-3\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [\"class\", \"d-grid mt-3\", 4, \"ngIf\"], [1, \"mt-5\"], [1, \"col-md-12\"], [\"class\", \"row border-bottom py-3 \", 4, \"ngFor\", \"ngForOf\"], [1, \"fa\", \"fa-bookmark\", \"text-gold\", \"fs-3\", \"div-pointer\", 3, \"click\"], [1, \"fa-regular\", \"fa-bookmark\", \"text-muted\", \"fs-3\", \"div-pointer\", 3, \"click\"], [1, \"row\", \"border-bottom\", \"py-3\"], [1, \"col-10\", \"div-pointer\"], [1, \"left-icon-div\", \"me-2\"], [\"class\", \"card-floating-icon-custom\", 4, \"ngIf\"], [1, \"fs-sm\", \"ps-sm-3\"], [1, \"d-flex\", \"justify-content-start\", \"text-heading\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fw-bold\", \"fs-6\"], [\"class\", \"text-primary fw-bold fs-xs pt-2 mb-1 \", 4, \"ngIf\"], [1, \"card-floating-icon-custom\"], [1, \"fa\", \"fa-play\"], [1, \"fa\", \"fa-clock\"], [1, \"fa\", \"fa-file\"], [1, \"ai-award\"], [1, \"text-primary\", \"fw-bold\", \"fs-xs\", \"pt-2\", \"mb-1\"], [1, \"modal-header\"], [1, \"modal-title\", \"float-start\"], [\"type\", \"button \", \"aria-label\", \"Close \", 1, \"close\", \"btn\", \"btn-mini\", \"btn-outline-danger\", \"float-end\", 3, \"click\"], [1, \"fa\", \"fa-close\"], [1, \"modal-body\"], [\"class\", \"card rounded mb-1\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"modal-footer\"], [1, \"btn\", \"btn-outline-danger\", \"me-2\", 3, \"click\"], [1, \"fa\", \"fa-check\"], [1, \"card\", \"rounded\", \"mb-1\", 3, \"ngClass\"], [1, \"row\", \"g-0\", 3, \"click\"], [1, \"col-md-3\", \"d-flex\", \"align-items-center\"], [\"class\", \"img-fluid rounded-start\", \"alt\", \"...\", 3, \"src\", 4, \"ngIf\"], [\"src\", \"assets/img/demo/profile.jpg\", \"class\", \"img-fluid rounded-start\", \"alt\", \"...\", 4, \"ngIf\"], [1, \"col-md-8\", \"d-flex\", \"align-items-center\"], [1, \"card-body\", \"p-1\"], [1, \"card-title\", \"text-primary\", \"mb-0\"], [1, \"card-text\", \"mb-0\"], [1, \"card-text\"], [1, \"text-muted\"], [\"class\", \"col-md-1 d-flex align-items-center\", 4, \"ngIf\"], [\"alt\", \"...\", 1, \"img-fluid\", \"rounded-start\", 3, \"src\"], [\"src\", \"assets/img/demo/profile.jpg\", \"alt\", \"...\", 1, \"img-fluid\", \"rounded-start\"], [1, \"col-md-1\", \"d-flex\", \"align-items-center\"], [\"class\", \"fas fa-check-circle text-primary fs-3\", 4, \"ngIf\"], [\"class\", \"far fa-circle fs-3\", 4, \"ngIf\"], [1, \"fas\", \"fa-check-circle\", \"text-primary\", \"fs-3\"], [1, \"far\", \"fa-circle\", \"fs-3\"]],\n  template: function CourseDetailsPreviewComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵtemplate(1, CourseDetailsPreviewComponent_div_1_Template, 44, 15, \"div\", 0);\n      i0.ɵɵtemplate(2, CourseDetailsPreviewComponent_ng_template_2_Template, 14, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.courseData);\n    }\n  },\n  directives: [i7.BlockUIComponent, i5.NgIf, i8.RatingComponent, i9.NgControlStatus, i9.NgModel, i5.NgForOf, i5.NgClass, i10.DefaultClassDirective],\n  pipes: [i11.SafePipe, i5.DatePipe],\n  styles: [\"\"],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], CourseDetailsPreviewComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}