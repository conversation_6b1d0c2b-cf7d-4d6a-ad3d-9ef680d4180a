{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ColumnMode } from \"@swimlane/ngx-datatable\";\nimport { Validators } from \"@angular/forms\";\nimport { BlockUI } from \"ng-block-ui\";\nimport { environment } from \"../../environments/environment\";\nimport { UploadDialogComponent } from \"../_helpers/upload-dialog/dialog.component\";\nimport * as moment from \"moment\";\nimport { Page } from \"../_models/page\";\nimport { ResponseStatus } from \"../_models/enum\";\nimport { trigger, transition, style, animate } from \"@angular/animations\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/modal\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../_services/common.service\";\nimport * as i4 from \"../_helpers/confirm-dialog/confirm.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"@angular/material/dialog\";\nimport * as i8 from \"ng-block-ui\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@ng-select/ng-select\";\nimport * as i11 from \"@angular/flex-layout/extended\";\nimport * as i12 from \"@swimlane/ngx-datatable\";\nimport * as i13 from \"@danielmoncada/angular-datetime-picker\";\nimport * as i14 from \"ngx-moment\";\n\nfunction BelatedTraineeNotifyComponent_ng_select_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 23);\n    i0.ɵɵlistener(\"change\", function BelatedTraineeNotifyComponent_ng_select_14_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return ctx_r12.onChangeCourse($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r0.courseList);\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_div_15_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1, \"Course is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, BelatedTraineeNotifyComponent_div_15_span_1_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.g.courseId.errors.required);\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_div_20_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1, \"Exam is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, BelatedTraineeNotifyComponent_div_20_span_1_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.g.examId.errors.required);\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelementStart(1, \"label\", 28);\n    i0.ɵɵtext(2, \" Due Date of the Exam : \");\n    i0.ɵɵelementStart(3, \"b\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r3.dueDate, \"DD-MMM-YYYY hh:mm A\"));\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelementStart(1, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function BelatedTraineeNotifyComponent_div_22_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n\n      const _r10 = i0.ɵɵreference(35);\n\n      return ctx_r16.openModal(_r10);\n    });\n    i0.ɵɵelement(2, \"i\", 31);\n    i0.ɵɵtext(3, \" Notify Belated trainee(s)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r18 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r18);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r18, \" \");\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_ng_template_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r19 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r19);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r19, \" \");\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_ng_template_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r20 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r20);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r20, \" \");\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_ng_template_32_input_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 35);\n    i0.ɵɵlistener(\"change\", function BelatedTraineeNotifyComponent_ng_template_32_input_1_Template_input_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return ctx_r22.selectAll($event.currentTarget.checked);\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"checked\", ctx_r21.allRowsSelected);\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\");\n    i0.ɵɵtemplate(1, BelatedTraineeNotifyComponent_ng_template_32_input_1_Template, 1, 1, \"input\", 33);\n    i0.ɵɵelementStart(2, \"span\", 34);\n    i0.ɵɵtext(3, \" Select All \\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.rows.length > 0);\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 35);\n    i0.ɵɵlistener(\"change\", function BelatedTraineeNotifyComponent_ng_template_33_Template_input_change_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const row_r25 = restoredCtx.row;\n      const ctx_r26 = i0.ɵɵnextContext();\n      return ctx_r26.setSelectedTrainee(row_r25.Id, $event.currentTarget.checked);\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"checked\", ctx_r9.isSelected);\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_ng_template_34_div_16_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1, \" Start date & time is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_ng_template_34_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, BelatedTraineeNotifyComponent_ng_template_34_div_16_span_1_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.f.startDate.errors.required);\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_ng_template_34_div_23_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1, \" End date & time is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_ng_template_34_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, BelatedTraineeNotifyComponent_ng_template_34_div_23_span_1_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.f.endDate.errors.required);\n  }\n}\n\nfunction BelatedTraineeNotifyComponent_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"h4\", 37);\n    i0.ɵɵelementStart(2, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function BelatedTraineeNotifyComponent_ng_template_34_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return ctx_r34.modalHide();\n    });\n    i0.ɵɵelement(3, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 40);\n    i0.ɵɵelementStart(5, \"form\", 5);\n    i0.ɵɵelementStart(6, \"div\", 0);\n    i0.ɵɵelementStart(7, \"div\", 1);\n    i0.ɵɵelementStart(8, \"div\", 41);\n    i0.ɵɵelementStart(9, \"div\", 3);\n    i0.ɵɵelementStart(10, \"div\", 42);\n    i0.ɵɵelementStart(11, \"label\", 43);\n    i0.ɵɵtext(12, \"Start Date & Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 44);\n    i0.ɵɵelement(14, \"owl-date-time\", null, 45);\n    i0.ɵɵtemplate(16, BelatedTraineeNotifyComponent_ng_template_34_div_16_Template, 2, 1, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 42);\n    i0.ɵɵelementStart(18, \"label\", 43);\n    i0.ɵɵtext(19, \"End Date & Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"input\", 46);\n    i0.ɵɵelement(21, \"owl-date-time\", null, 47);\n    i0.ɵɵtemplate(23, BelatedTraineeNotifyComponent_ng_template_34_div_23_Template, 2, 1, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 48);\n    i0.ɵɵelementStart(25, \"div\", 49);\n    i0.ɵɵelementStart(26, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function BelatedTraineeNotifyComponent_ng_template_34_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return ctx_r36.modalHide();\n    });\n    i0.ɵɵelement(27, \"i\", 39);\n    i0.ɵɵtext(28, \" Close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function BelatedTraineeNotifyComponent_ng_template_34_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return ctx_r37.sendEmail();\n    });\n    i0.ɵɵelement(30, \"i\", 52);\n    i0.ɵɵtext(31, \" Save and Notify\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r28 = i0.ɵɵreference(15);\n\n    const _r30 = i0.ɵɵreference(22);\n\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r11.modalTitle, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"formGroup\", ctx_r11.entryForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"owlDateTime\", _r28)(\"owlDateTimeTrigger\", _r28)(\"max\", ctx_r11.f.endDate.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.submitted && ctx_r11.f.startDate.errors);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"owlDateTime\", _r30)(\"owlDateTimeTrigger\", _r30)(\"min\", ctx_r11.f.startDate.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.submitted && ctx_r11.f.endDate.errors);\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nexport class BelatedTraineeNotifyComponent {\n  constructor(modalService, formBuilder, _service, confirmService, toastr, config, dialog) {\n    this.modalService = modalService;\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.confirmService = confirmService;\n    this.toastr = toastr;\n    this.dialog = dialog;\n    this.submitted = false;\n    this.filterSubmitted = false;\n    this.isSelected = false;\n    this.allRowsSelected = false;\n    this.modalTitle = \"Extend Time\";\n    this.btnSaveText = \"Save\";\n    this.to_Show = false;\n    this.modalConfig = {\n      class: \"gray modal-md\",\n      backdrop: \"static\"\n    };\n    this.searchText = \"\";\n    this.selected_count = 0;\n    this.selected_items = [];\n    this.courseList = [];\n    this.divisionList = [];\n    this.traineeList = [];\n    this.examList = [];\n    this.rows = [];\n    this.loadingIndicator = false;\n    this.ColumnMode = ColumnMode;\n    this.page = new Page();\n    this.scrollBarHorizontal = window.innerWidth < 1200;\n    this.itemObj = {};\n    this.dueDate = \"\"; //imgBaseUrl = environment.imageUrl;\n\n    this.baseUrl = environment.baseUrl;\n    this.bsValue = [];\n    this.page.pageNumber = 0;\n    this.page.size = 5;\n\n    window.onresize = () => {\n      this.scrollBarHorizontal = window.innerWidth < 1200;\n    };\n\n    this.bsConfig = Object.assign({}, {\n      containerClass: \"theme-blue\"\n    });\n    config.seconds = false;\n    config.spinners = false;\n  }\n\n  ngOnInit() {\n    this.entryForm = this.formBuilder.group({\n      startDate: [null, [Validators.required]],\n      endDate: [null, [Validators.required]] // startTime: [null, [Validators.required]],\n      // endTime: [null, [Validators.required]],\n\n    });\n    this.filterForm = this.formBuilder.group({\n      courseId: [null, [Validators.required]],\n      examId: [null, [Validators.required]]\n    });\n    this.bsConfig = Object.assign({}, {\n      minDate: new Date(),\n      containerClass: \"theme-blue\"\n    });\n    this.getCourseList();\n  }\n\n  setPage(pageInfo) {\n    this.page.pageNumber = pageInfo.offset;\n  }\n\n  get f() {\n    return this.entryForm.controls;\n  }\n\n  get g() {\n    return this.filterForm.controls;\n  }\n\n  getCourseList() {\n    this._service.get(\"course/dropdown-list\").subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.courseList = res.Data;\n    }, () => {});\n  }\n\n  onFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    if (this.selected_count === 0) {\n      this.toastr.warning(\"Please selecta minimum one trainee\", \"Warning!\", {\n        timeOut: 2000\n      });\n      return;\n    }\n\n    this.blockUI.start(\"Saving...\");\n    const obj = {\n      CourseId: this.entryForm.value.courseId,\n      Trainees: this.selected_items.map(function (x) {\n        return x.Id;\n      })\n    }; // const formData = new FormData();\n    // formData.append('Model', JSON.stringify(obj));\n\n    this._service.post(\"course/enroll-trainee\", obj).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, \"Success!\", {\n        timeOut: 2000\n      });\n      this.modalHide(); // this.filterList();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  modalHide() {\n    this.modalRef.hide();\n    this.submitted = false;\n  }\n\n  openModal(template) {\n    // this.entryForm.controls['isActive'].setValue(true);\n    this.modalRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  sendEmail() {\n    this.submitted = true; //this.entryForm.value.startDate.setTime(this.entryForm.value.startTime);\n    //this.entryForm.value.endDate.setTime(this.entryForm.value.endTime);\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    const obj = {\n      ExamId: this.filterForm.value.examId,\n      Trainees: this.rows.filter(y => y.Selected === true).map(function (x) {\n        return x.Id;\n      }),\n      startDate: moment(this.entryForm.value.startDate).toISOString(),\n      endDate: moment(this.entryForm.value.endDate).toISOString()\n    };\n    this.modalHide();\n    this.confirmService.confirm(\"Are you sure?\", \"By confirming you will notify all the selected trainees and reschedule the exam time for the notified trainees.\").subscribe(result => {\n      if (result) {\n        this.blockUI.start(\"Notifying Belated Trainees...\");\n\n        this._service.post(\"course/notify-belated-trainees\", obj).subscribe(res => {\n          this.blockUI.stop();\n\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, \"Warning!\", {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, \"Error!\", {\n              closeButton: true,\n              disableTimeOut: false,\n              enableHtml: true\n            });\n            return;\n          }\n\n          this.toastr.success(res.Message, \"Success!\", {\n            timeOut: 2000\n          });\n          this.modalHide();\n          this.filterForm.reset(); //this.traineeList=[];\n\n          this.rows = [];\n          this.entryForm.reset();\n          this.selected_count = 0;\n          this.isSelected = false, this.submitted = false;\n        }, err => {\n          this.blockUI.stop();\n          this.toastr.warning(err.Message || err, \"Warning!\", {\n            timeOut: 2000\n          });\n        });\n      } else {\n        this.blockUI.stop();\n      }\n    }); // const formData = new FormData();\n    // formData.append('Model', JSON.stringify(obj));\n  }\n\n  openUploadDialog() {\n    let dialogRef = this.dialog.open(UploadDialogComponent, {\n      data: {\n        url: this._service.generateUrl(\"Course/UploadCourseAssign\"),\n        whiteList: [\"xlsx\", \"xls\"],\n        uploadtext: \"Please upload an Excel file\",\n        title: \"Upload trainee Course Assign File\"\n      },\n      width: \"50%\",\n      height: \"50%\"\n    }).afterClosed().subscribe(response => {\n      if (response) {\n        this.toastr.success(response, \"Success!\", {\n          timeOut: 2000\n        });\n        this.filterList();\n      }\n    });\n  }\n\n  filterList() {\n    this.submitted = true;\n    if (this.filterForm.invalid) return;\n    this.loadingIndicator = true;\n    const obj = {\n      courseId: this.filterForm.value.courseId,\n      examId: this.filterForm.value.examId\n    };\n\n    this._service.get(\"course/get-all-trainees-to-notify\", obj).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.rows = res.Data.Records;\n      this.rows = this.rows.filter(x => x.takenExam === false && x.Enrolled === true && x.isBelated === true);\n      this.traineeList = [];\n      this.rows.forEach(s => {\n        s.Selected = false;\n      });\n      this.submitted = false;\n      setTimeout(() => {\n        this.loadingIndicator = false;\n      }, 1000);\n    }, () => {});\n  }\n\n  setSelectedTrainee(id, selected) {\n    this.rows.forEach(s => {\n      if (id.indexOf(s.Id) >= 0) {\n        s.Selected = selected;\n        if (selected) this.selected_count++;else this.selected_count--;\n      }\n    });\n  }\n\n  selectAll(value) {\n    if (value) {\n      this.rows.forEach(s => {\n        s.Selected = true;\n        this.selected_count++;\n      });\n      this.isSelected = true;\n    } else {\n      this.rows.forEach(s => {\n        s.Selected = false;\n        this.selected_count--;\n      });\n      this.isSelected = false;\n    }\n  }\n\n  onChangeCourse(event) {\n    this.examList = [];\n    this.filterForm.controls[\"examId\"].setValue(null);\n\n    this._service.get(\"exam/certificate/dropdown-list/\" + event.Id).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.examList = res.Data;\n    }, () => {});\n  }\n\n  onChangeExam(event) {\n    //this.traineeList=[];\n    this.selected_count = 0;\n    this.rows = [];\n    this.filterList();\n    this.dueDate = event.Date ? event.Date : \"no ending date\";\n  }\n\n  onTimechange(event) {\n    if (this.f.startDate.value == event) {\n      console.log(\"yes\");\n    }\n\n    if (this.f.endDate.value == event) {\n      console.log(\"yes\");\n    }\n  }\n\n}\n\nBelatedTraineeNotifyComponent.ɵfac = function BelatedTraineeNotifyComponent_Factory(t) {\n  return new (t || BelatedTraineeNotifyComponent)(i0.ɵɵdirectiveInject(i1.BsModalService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.CommonService), i0.ɵɵdirectiveInject(i4.ConfirmService), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i6.NgbTimepickerConfig), i0.ɵɵdirectiveInject(i7.MatDialog));\n};\n\nBelatedTraineeNotifyComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: BelatedTraineeNotifyComponent,\n  selectors: [[\"app-belated-trainee-notify\"]],\n  decls: 36,\n  vars: 30,\n  consts: [[1, \"row\"], [1, \"col-lg-12\"], [1, \"card\", \"card-border-primary\"], [1, \"card-block\"], [1, \"col-sm-12\"], [\"autocomplete\", \"off\", 3, \"formGroup\"], [1, \"mb-3\", \"row\"], [1, \"col-sm-1\", \"col-form-label\", \"col-form-label-sm\", \"text-right\", \"required\"], [1, \"col-sm-3\"], [\"formControlName\", \"courseId\", \"class\", \"form-control form-control-sm\", \"bindLabel\", \"Title\", \"bindValue\", \"Id\", \"placeholder\", \"Select a course\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"change\", 4, \"ngIf\"], [\"class\", \"error-text\", 4, \"ngIf\"], [1, \"col-sm-2\"], [\"formControlName\", \"examId\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"clearable\", \"clearOnBackspace\", \"items\", \"change\"], [\"class\", \"col-sm-5 col-12\", 4, \"ngIf\"], [\"class\", \"col-md-8 mb-2 col-xs-12\", 4, \"ngIf\"], [\"rowHeight\", \"auto\", 1, \"material\", \"table-bordered\", 3, \"rows\", \"loadingIndicator\", \"columnMode\", \"headerHeight\", \"footerHeight\", \"limit\"], [\"name\", \"Trainee Name\", \"prop\", \"Name\", 3, \"width\", \"draggable\", \"sortable\"], [\"ngx-datatable-cell-template\", \"\"], [\"prop\", \"Division.Name\", 3, \"width\", \"draggable\", \"sortable\"], [\"prop\", \"Email\", 3, \"width\", \"draggable\", \"sortable\"], [\"prop\", \"Id\", \"name\", \"invite\", 3, \"width\", \"draggable\", \"sortable\"], [\"ngx-datatable-header-template\", \"\"], [\"template\", \"\"], [\"formControlName\", \"courseId\", \"bindLabel\", \"Title\", \"bindValue\", \"Id\", \"placeholder\", \"Select a course\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"change\"], [1, \"error-text\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [1, \"col-sm-5\", \"col-12\"], [1, \"col-form-label\", \"col-form-label-sm\", \"text-right\"], [1, \"col-md-8\", \"mb-2\", \"col-xs-12\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [1, \"feather\", \"icon-plus\"], [3, \"title\"], [\"type\", \"checkbox\", 3, \"checked\", \"change\", 4, \"ngIf\"], [1, \"cr\"], [\"type\", \"checkbox\", 3, \"checked\", \"change\"], [1, \"modal-header\"], [\"id\", \"modalTitle\", 1, \"modal-title\", \"float-start\", 3, \"innerHtml\"], [\"type\", \"button \", \"aria-label\", \"Close \", 1, \"close\", \"float-end\", \"btn\", \"btn-mini\", \"btn-danger\", \"btn\", \"btn-mini\", \"btn-outline-danger\", 3, \"click\"], [1, \"feather\", \"icon-x\"], [1, \"modal-body\"], [1, \"card\"], [1, \"mb-3\", \"col-12\"], [1, \"col-form-label\", \"col-form-label-sm\", \"required\"], [\"placeholder\", \"Set Start Date & Time\", \"formControlName\", \"startDate\", 1, \"form-control\", \"form-control-sm\", 3, \"owlDateTime\", \"owlDateTimeTrigger\", \"max\"], [\"startDate\", \"\"], [\"placeholder\", \"Set End Date & Time\", \"formControlName\", \"endDate\", 1, \"form-control\", \"form-control-sm\", 3, \"owlDateTime\", \"owlDateTimeTrigger\", \"min\"], [\"endDate\", \"\"], [1, \"modal-footer\"], [1, \"pe-4\"], [1, \"btn\", \"btn-outline-danger\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-outline-success\", 3, \"click\"], [1, \"feather\", \"icon-check-circle\"]],\n  template: function BelatedTraineeNotifyComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 0);\n      i0.ɵɵelementStart(4, \"div\", 1);\n      i0.ɵɵelementStart(5, \"div\", 2);\n      i0.ɵɵelementStart(6, \"div\", 3);\n      i0.ɵɵelementStart(7, \"div\", 0);\n      i0.ɵɵelementStart(8, \"div\", 4);\n      i0.ɵɵelementStart(9, \"form\", 5);\n      i0.ɵɵelementStart(10, \"div\", 6);\n      i0.ɵɵelementStart(11, \"label\", 7);\n      i0.ɵɵtext(12, \" Course \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"div\", 8);\n      i0.ɵɵtemplate(14, BelatedTraineeNotifyComponent_ng_select_14_Template, 1, 3, \"ng-select\", 9);\n      i0.ɵɵtemplate(15, BelatedTraineeNotifyComponent_div_15_Template, 2, 1, \"div\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"label\", 7);\n      i0.ɵɵtext(17, \" Exam \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"div\", 11);\n      i0.ɵɵelementStart(19, \"ng-select\", 12);\n      i0.ɵɵlistener(\"change\", function BelatedTraineeNotifyComponent_Template_ng_select_change_19_listener($event) {\n        return ctx.onChangeExam($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(20, BelatedTraineeNotifyComponent_div_20_Template, 2, 1, \"div\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(21, BelatedTraineeNotifyComponent_div_21_Template, 6, 4, \"div\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(22, BelatedTraineeNotifyComponent_div_22_Template, 4, 0, \"div\", 14);\n      i0.ɵɵelementStart(23, \"div\", 1);\n      i0.ɵɵelementStart(24, \"ngx-datatable\", 15);\n      i0.ɵɵelementStart(25, \"ngx-datatable-column\", 16);\n      i0.ɵɵtemplate(26, BelatedTraineeNotifyComponent_ng_template_26_Template, 2, 2, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(27, \"ngx-datatable-column\", 18);\n      i0.ɵɵtemplate(28, BelatedTraineeNotifyComponent_ng_template_28_Template, 2, 2, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(29, \"ngx-datatable-column\", 19);\n      i0.ɵɵtemplate(30, BelatedTraineeNotifyComponent_ng_template_30_Template, 2, 2, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(31, \"ngx-datatable-column\", 20);\n      i0.ɵɵtemplate(32, BelatedTraineeNotifyComponent_ng_template_32_Template, 4, 1, \"ng-template\", 21);\n      i0.ɵɵtemplate(33, BelatedTraineeNotifyComponent_ng_template_33_Template, 1, 1, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(34, BelatedTraineeNotifyComponent_ng_template_34_Template, 32, 10, \"ng-template\", null, 22, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.courseList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.g.courseId.errors);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(28, _c0, ctx.submitted && ctx.g.examId.errors))(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx.examList);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.g.examId.errors);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.g.examId.value);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.selected_count > 0);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"rows\", ctx.rows)(\"loadingIndicator\", ctx.loadingIndicator)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"limit\", 10);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 60)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n    }\n  },\n  directives: [i8.BlockUIComponent, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i9.NgIf, i10.NgSelectComponent, i2.NgControlStatus, i2.FormControlName, i9.NgClass, i11.DefaultClassDirective, i12.DatatableComponent, i12.DataTableColumnDirective, i12.DataTableColumnCellDirective, i12.DataTableColumnHeaderDirective, i2.DefaultValueAccessor, i13.OwlDateTimeInputDirective, i13.OwlDateTimeTriggerDirective, i13.OwlDateTimeComponent],\n  pipes: [i14.DateFormatPipe],\n  styles: [\".cdk-overlay-container{position:fixed;z-index:1000000!important}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger(\"inOutAnimation\", [transition(\":enter\", [style({\n      height: 0,\n      opacity: 0\n    }), animate(\"1s ease-out\", style({\n      height: 300,\n      opacity: 1\n    }))]), transition(\":leave\", [animate(\"1s ease-out\", style({\n      height: 0,\n      opacity: 0\n    }))])])]\n  }\n});\n\n__decorate([BlockUI()], BelatedTraineeNotifyComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}