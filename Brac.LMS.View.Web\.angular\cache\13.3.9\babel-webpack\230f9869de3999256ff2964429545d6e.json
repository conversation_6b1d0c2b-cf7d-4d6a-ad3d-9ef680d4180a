{"ast": null, "code": "import * as __NgCli_bootstrap_1 from \"@angular/platform-browser\";\nimport { enableProdMode } from '@angular/core';\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\n\nif (environment.production) {\n  enableProdMode();\n}\n\n__NgCli_bootstrap_1.platformBrowser().bootstrapModule(AppModule).catch(err => console.error(err));", "map": null, "metadata": {}, "sourceType": "module"}