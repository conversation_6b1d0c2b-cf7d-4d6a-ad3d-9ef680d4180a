{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { animationFrameProvider } from './animationFrameProvider';\nexport class AnimationFrameAction extends AsyncAction {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n  }\n\n  requestAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n\n    scheduler.actions.push(this);\n    return scheduler._scheduled || (scheduler._scheduled = animationFrameProvider.requestAnimationFrame(() => scheduler.flush(undefined)));\n  }\n\n  recycleAsyncId(scheduler, id, delay = 0) {\n    if (delay != null && delay > 0 || delay == null && this.delay > 0) {\n      return super.recycleAsyncId(scheduler, id, delay);\n    }\n\n    if (scheduler.actions.length === 0) {\n      animationFrameProvider.cancelAnimationFrame(id);\n      scheduler._scheduled = undefined;\n    }\n\n    return undefined;\n  }\n\n} //# sourceMappingURL=AnimationFrameAction.js.map", "map": null, "metadata": {}, "sourceType": "module"}