{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Directive, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/flex-layout/core';\nimport { StyleBuilder, BaseDirective2, CoreModule } from '@angular/flex-layout/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst ROW_DEFAULT = 'stretch';\nconst COL_DEFAULT = 'stretch';\nlet GridAlignStyleBuilder = /*#__PURE__*/(() => {\n  class GridAlignStyleBuilder extends StyleBuilder {\n    buildStyles(input) {\n      return buildCss$2(input || ROW_DEFAULT);\n    }\n\n  }\n\n  GridAlignStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵGridAlignStyleBuilder_BaseFactory;\n    return function GridAlignStyleBuilder_Factory(t) {\n      return (ɵGridAlignStyleBuilder_BaseFactory || (ɵGridAlignStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(GridAlignStyleBuilder)))(t || GridAlignStyleBuilder);\n    };\n  }();\n\n  GridAlignStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GridAlignStyleBuilder,\n    factory: GridAlignStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return GridAlignStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet GridAlignDirective = /*#__PURE__*/(() => {\n  class GridAlignDirective extends BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal) {\n      super(elementRef, styleBuilder, styler, marshal);\n      this.DIRECTIVE_KEY = 'grid-align';\n      this.styleCache = alignCache;\n      this.init();\n    }\n\n  }\n\n  GridAlignDirective.ɵfac = function GridAlignDirective_Factory(t) {\n    return new (t || GridAlignDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(GridAlignStyleBuilder), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  GridAlignDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: GridAlignDirective,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return GridAlignDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst alignCache = /*#__PURE__*/new Map();\nconst inputs$a = ['gdGridAlign', 'gdGridAlign.xs', 'gdGridAlign.sm', 'gdGridAlign.md', 'gdGridAlign.lg', 'gdGridAlign.xl', 'gdGridAlign.lt-sm', 'gdGridAlign.lt-md', 'gdGridAlign.lt-lg', 'gdGridAlign.lt-xl', 'gdGridAlign.gt-xs', 'gdGridAlign.gt-sm', 'gdGridAlign.gt-md', 'gdGridAlign.gt-lg'];\nconst selector$a = `\n  [gdGridAlign],\n  [gdGridAlign.xs], [gdGridAlign.sm], [gdGridAlign.md], [gdGridAlign.lg],[gdGridAlign.xl],\n  [gdGridAlign.lt-sm], [gdGridAlign.lt-md], [gdGridAlign.lt-lg], [gdGridAlign.lt-xl],\n  [gdGridAlign.gt-xs], [gdGridAlign.gt-sm], [gdGridAlign.gt-md], [gdGridAlign.gt-lg]\n`;\n/**\n * 'align' CSS Grid styling directive for grid children\n *  Defines positioning of child elements along row and column axis in a grid container\n *  Optional values: {row-axis} values or {row-axis column-axis} value pairs\n *\n *  @see https://css-tricks.com/snippets/css/complete-guide-grid/#prop-justify-self\n *  @see https://css-tricks.com/snippets/css/complete-guide-grid/#prop-align-self\n */\n\nlet DefaultGridAlignDirective = /*#__PURE__*/(() => {\n  class DefaultGridAlignDirective extends GridAlignDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$a;\n    }\n\n  }\n\n  DefaultGridAlignDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultGridAlignDirective_BaseFactory;\n    return function DefaultGridAlignDirective_Factory(t) {\n      return (ɵDefaultGridAlignDirective_BaseFactory || (ɵDefaultGridAlignDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultGridAlignDirective)))(t || DefaultGridAlignDirective);\n    };\n  }();\n\n  DefaultGridAlignDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultGridAlignDirective,\n    selectors: [[\"\", \"gdGridAlign\", \"\"], [\"\", \"gdGridAlign.xs\", \"\"], [\"\", \"gdGridAlign.sm\", \"\"], [\"\", \"gdGridAlign.md\", \"\"], [\"\", \"gdGridAlign.lg\", \"\"], [\"\", \"gdGridAlign.xl\", \"\"], [\"\", \"gdGridAlign.lt-sm\", \"\"], [\"\", \"gdGridAlign.lt-md\", \"\"], [\"\", \"gdGridAlign.lt-lg\", \"\"], [\"\", \"gdGridAlign.lt-xl\", \"\"], [\"\", \"gdGridAlign.gt-xs\", \"\"], [\"\", \"gdGridAlign.gt-sm\", \"\"], [\"\", \"gdGridAlign.gt-md\", \"\"], [\"\", \"gdGridAlign.gt-lg\", \"\"]],\n    inputs: {\n      gdGridAlign: \"gdGridAlign\",\n      \"gdGridAlign.xs\": \"gdGridAlign.xs\",\n      \"gdGridAlign.sm\": \"gdGridAlign.sm\",\n      \"gdGridAlign.md\": \"gdGridAlign.md\",\n      \"gdGridAlign.lg\": \"gdGridAlign.lg\",\n      \"gdGridAlign.xl\": \"gdGridAlign.xl\",\n      \"gdGridAlign.lt-sm\": \"gdGridAlign.lt-sm\",\n      \"gdGridAlign.lt-md\": \"gdGridAlign.lt-md\",\n      \"gdGridAlign.lt-lg\": \"gdGridAlign.lt-lg\",\n      \"gdGridAlign.lt-xl\": \"gdGridAlign.lt-xl\",\n      \"gdGridAlign.gt-xs\": \"gdGridAlign.gt-xs\",\n      \"gdGridAlign.gt-sm\": \"gdGridAlign.gt-sm\",\n      \"gdGridAlign.gt-md\": \"gdGridAlign.gt-md\",\n      \"gdGridAlign.gt-lg\": \"gdGridAlign.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultGridAlignDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction buildCss$2(align = '') {\n  const css = {},\n        [rowAxis, columnAxis] = align.split(' '); // Row axis\n\n  switch (rowAxis) {\n    case 'end':\n      css['justify-self'] = 'end';\n      break;\n\n    case 'center':\n      css['justify-self'] = 'center';\n      break;\n\n    case 'stretch':\n      css['justify-self'] = 'stretch';\n      break;\n\n    case 'start':\n      css['justify-self'] = 'start';\n      break;\n\n    default:\n      css['justify-self'] = ROW_DEFAULT; // default row axis\n\n      break;\n  } // Column axis\n\n\n  switch (columnAxis) {\n    case 'end':\n      css['align-self'] = 'end';\n      break;\n\n    case 'center':\n      css['align-self'] = 'center';\n      break;\n\n    case 'stretch':\n      css['align-self'] = 'stretch';\n      break;\n\n    case 'start':\n      css['align-self'] = 'start';\n      break;\n\n    default:\n      css['align-self'] = COL_DEFAULT; // default column axis\n\n      break;\n  }\n\n  return css;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_MAIN$1 = 'start';\nconst DEFAULT_CROSS$1 = 'stretch';\nlet GridAlignColumnsStyleBuilder = /*#__PURE__*/(() => {\n  class GridAlignColumnsStyleBuilder extends StyleBuilder {\n    buildStyles(input, parent) {\n      return buildCss$1(input || `${DEFAULT_MAIN$1} ${DEFAULT_CROSS$1}`, parent.inline);\n    }\n\n  }\n\n  GridAlignColumnsStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵGridAlignColumnsStyleBuilder_BaseFactory;\n    return function GridAlignColumnsStyleBuilder_Factory(t) {\n      return (ɵGridAlignColumnsStyleBuilder_BaseFactory || (ɵGridAlignColumnsStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(GridAlignColumnsStyleBuilder)))(t || GridAlignColumnsStyleBuilder);\n    };\n  }();\n\n  GridAlignColumnsStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GridAlignColumnsStyleBuilder,\n    factory: GridAlignColumnsStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return GridAlignColumnsStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet GridAlignColumnsDirective = /*#__PURE__*/(() => {\n  class GridAlignColumnsDirective extends BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal) {\n      super(elementRef, styleBuilder, styler, marshal);\n      this.DIRECTIVE_KEY = 'grid-align-columns';\n      this._inline = false;\n      this.init();\n    }\n\n    get inline() {\n      return this._inline;\n    }\n\n    set inline(val) {\n      this._inline = coerceBooleanProperty(val);\n    } // *********************************************\n    // Protected methods\n    // *********************************************\n\n\n    updateWithValue(value) {\n      this.styleCache = this.inline ? alignColumnsInlineCache : alignColumnsCache;\n      this.addStyles(value, {\n        inline: this.inline\n      });\n    }\n\n  }\n\n  GridAlignColumnsDirective.ɵfac = function GridAlignColumnsDirective_Factory(t) {\n    return new (t || GridAlignColumnsDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(GridAlignColumnsStyleBuilder), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  GridAlignColumnsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: GridAlignColumnsDirective,\n    inputs: {\n      inline: [\"gdInline\", \"inline\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return GridAlignColumnsDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst alignColumnsCache = /*#__PURE__*/new Map();\nconst alignColumnsInlineCache = /*#__PURE__*/new Map();\nconst inputs$9 = ['gdAlignColumns', 'gdAlignColumns.xs', 'gdAlignColumns.sm', 'gdAlignColumns.md', 'gdAlignColumns.lg', 'gdAlignColumns.xl', 'gdAlignColumns.lt-sm', 'gdAlignColumns.lt-md', 'gdAlignColumns.lt-lg', 'gdAlignColumns.lt-xl', 'gdAlignColumns.gt-xs', 'gdAlignColumns.gt-sm', 'gdAlignColumns.gt-md', 'gdAlignColumns.gt-lg'];\nconst selector$9 = `\n  [gdAlignColumns],\n  [gdAlignColumns.xs], [gdAlignColumns.sm], [gdAlignColumns.md],\n  [gdAlignColumns.lg], [gdAlignColumns.xl], [gdAlignColumns.lt-sm],\n  [gdAlignColumns.lt-md], [gdAlignColumns.lt-lg], [gdAlignColumns.lt-xl],\n  [gdAlignColumns.gt-xs], [gdAlignColumns.gt-sm], [gdAlignColumns.gt-md],\n  [gdAlignColumns.gt-lg]\n`;\n/**\n * 'column alignment' CSS Grid styling directive\n * Configures the alignment in the column direction\n * @see https://css-tricks.com/snippets/css/complete-guide-grid/#article-header-id-19\n * @see https://css-tricks.com/snippets/css/complete-guide-grid/#article-header-id-21\n */\n\nlet DefaultGridAlignColumnsDirective = /*#__PURE__*/(() => {\n  class DefaultGridAlignColumnsDirective extends GridAlignColumnsDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$9;\n    }\n\n  }\n\n  DefaultGridAlignColumnsDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultGridAlignColumnsDirective_BaseFactory;\n    return function DefaultGridAlignColumnsDirective_Factory(t) {\n      return (ɵDefaultGridAlignColumnsDirective_BaseFactory || (ɵDefaultGridAlignColumnsDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultGridAlignColumnsDirective)))(t || DefaultGridAlignColumnsDirective);\n    };\n  }();\n\n  DefaultGridAlignColumnsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultGridAlignColumnsDirective,\n    selectors: [[\"\", \"gdAlignColumns\", \"\"], [\"\", \"gdAlignColumns.xs\", \"\"], [\"\", \"gdAlignColumns.sm\", \"\"], [\"\", \"gdAlignColumns.md\", \"\"], [\"\", \"gdAlignColumns.lg\", \"\"], [\"\", \"gdAlignColumns.xl\", \"\"], [\"\", \"gdAlignColumns.lt-sm\", \"\"], [\"\", \"gdAlignColumns.lt-md\", \"\"], [\"\", \"gdAlignColumns.lt-lg\", \"\"], [\"\", \"gdAlignColumns.lt-xl\", \"\"], [\"\", \"gdAlignColumns.gt-xs\", \"\"], [\"\", \"gdAlignColumns.gt-sm\", \"\"], [\"\", \"gdAlignColumns.gt-md\", \"\"], [\"\", \"gdAlignColumns.gt-lg\", \"\"]],\n    inputs: {\n      gdAlignColumns: \"gdAlignColumns\",\n      \"gdAlignColumns.xs\": \"gdAlignColumns.xs\",\n      \"gdAlignColumns.sm\": \"gdAlignColumns.sm\",\n      \"gdAlignColumns.md\": \"gdAlignColumns.md\",\n      \"gdAlignColumns.lg\": \"gdAlignColumns.lg\",\n      \"gdAlignColumns.xl\": \"gdAlignColumns.xl\",\n      \"gdAlignColumns.lt-sm\": \"gdAlignColumns.lt-sm\",\n      \"gdAlignColumns.lt-md\": \"gdAlignColumns.lt-md\",\n      \"gdAlignColumns.lt-lg\": \"gdAlignColumns.lt-lg\",\n      \"gdAlignColumns.lt-xl\": \"gdAlignColumns.lt-xl\",\n      \"gdAlignColumns.gt-xs\": \"gdAlignColumns.gt-xs\",\n      \"gdAlignColumns.gt-sm\": \"gdAlignColumns.gt-sm\",\n      \"gdAlignColumns.gt-md\": \"gdAlignColumns.gt-md\",\n      \"gdAlignColumns.gt-lg\": \"gdAlignColumns.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultGridAlignColumnsDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction buildCss$1(align, inline) {\n  const css = {},\n        [mainAxis, crossAxis] = align.split(' '); // Main axis\n\n  switch (mainAxis) {\n    case 'center':\n      css['align-content'] = 'center';\n      break;\n\n    case 'space-around':\n      css['align-content'] = 'space-around';\n      break;\n\n    case 'space-between':\n      css['align-content'] = 'space-between';\n      break;\n\n    case 'space-evenly':\n      css['align-content'] = 'space-evenly';\n      break;\n\n    case 'end':\n      css['align-content'] = 'end';\n      break;\n\n    case 'start':\n      css['align-content'] = 'start';\n      break;\n\n    case 'stretch':\n      css['align-content'] = 'stretch';\n      break;\n\n    default:\n      css['align-content'] = DEFAULT_MAIN$1; // default main axis\n\n      break;\n  } // Cross-axis\n\n\n  switch (crossAxis) {\n    case 'start':\n      css['align-items'] = 'start';\n      break;\n\n    case 'center':\n      css['align-items'] = 'center';\n      break;\n\n    case 'end':\n      css['align-items'] = 'end';\n      break;\n\n    case 'stretch':\n      css['align-items'] = 'stretch';\n      break;\n\n    default:\n      // 'stretch'\n      css['align-items'] = DEFAULT_CROSS$1; // default cross axis\n\n      break;\n  }\n\n  css['display'] = inline ? 'inline-grid' : 'grid';\n  return css;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_MAIN = 'start';\nconst DEFAULT_CROSS = 'stretch';\nlet GridAlignRowsStyleBuilder = /*#__PURE__*/(() => {\n  class GridAlignRowsStyleBuilder extends StyleBuilder {\n    buildStyles(input, parent) {\n      return buildCss(input || `${DEFAULT_MAIN} ${DEFAULT_CROSS}`, parent.inline);\n    }\n\n  }\n\n  GridAlignRowsStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵGridAlignRowsStyleBuilder_BaseFactory;\n    return function GridAlignRowsStyleBuilder_Factory(t) {\n      return (ɵGridAlignRowsStyleBuilder_BaseFactory || (ɵGridAlignRowsStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(GridAlignRowsStyleBuilder)))(t || GridAlignRowsStyleBuilder);\n    };\n  }();\n\n  GridAlignRowsStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GridAlignRowsStyleBuilder,\n    factory: GridAlignRowsStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return GridAlignRowsStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet GridAlignRowsDirective = /*#__PURE__*/(() => {\n  class GridAlignRowsDirective extends BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal) {\n      super(elementRef, styleBuilder, styler, marshal);\n      this.DIRECTIVE_KEY = 'grid-align-rows';\n      this._inline = false;\n      this.init();\n    }\n\n    get inline() {\n      return this._inline;\n    }\n\n    set inline(val) {\n      this._inline = coerceBooleanProperty(val);\n    } // *********************************************\n    // Protected methods\n    // *********************************************\n\n\n    updateWithValue(value) {\n      this.styleCache = this.inline ? alignRowsInlineCache : alignRowsCache;\n      this.addStyles(value, {\n        inline: this.inline\n      });\n    }\n\n  }\n\n  GridAlignRowsDirective.ɵfac = function GridAlignRowsDirective_Factory(t) {\n    return new (t || GridAlignRowsDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(GridAlignRowsStyleBuilder), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  GridAlignRowsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: GridAlignRowsDirective,\n    inputs: {\n      inline: [\"gdInline\", \"inline\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return GridAlignRowsDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst alignRowsCache = /*#__PURE__*/new Map();\nconst alignRowsInlineCache = /*#__PURE__*/new Map();\nconst inputs$8 = ['gdAlignRows', 'gdAlignRows.xs', 'gdAlignRows.sm', 'gdAlignRows.md', 'gdAlignRows.lg', 'gdAlignRows.xl', 'gdAlignRows.lt-sm', 'gdAlignRows.lt-md', 'gdAlignRows.lt-lg', 'gdAlignRows.lt-xl', 'gdAlignRows.gt-xs', 'gdAlignRows.gt-sm', 'gdAlignRows.gt-md', 'gdAlignRows.gt-lg'];\nconst selector$8 = `\n  [gdAlignRows],\n  [gdAlignRows.xs], [gdAlignRows.sm], [gdAlignRows.md],\n  [gdAlignRows.lg], [gdAlignRows.xl], [gdAlignRows.lt-sm],\n  [gdAlignRows.lt-md], [gdAlignRows.lt-lg], [gdAlignRows.lt-xl],\n  [gdAlignRows.gt-xs], [gdAlignRows.gt-sm], [gdAlignRows.gt-md],\n  [gdAlignRows.gt-lg]\n`;\n/**\n * 'row alignment' CSS Grid styling directive\n * Configures the alignment in the row direction\n * @see https://css-tricks.com/snippets/css/complete-guide-grid/#article-header-id-18\n * @see https://css-tricks.com/snippets/css/complete-guide-grid/#article-header-id-20\n */\n\nlet DefaultGridAlignRowsDirective = /*#__PURE__*/(() => {\n  class DefaultGridAlignRowsDirective extends GridAlignRowsDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$8;\n    }\n\n  }\n\n  DefaultGridAlignRowsDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultGridAlignRowsDirective_BaseFactory;\n    return function DefaultGridAlignRowsDirective_Factory(t) {\n      return (ɵDefaultGridAlignRowsDirective_BaseFactory || (ɵDefaultGridAlignRowsDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultGridAlignRowsDirective)))(t || DefaultGridAlignRowsDirective);\n    };\n  }();\n\n  DefaultGridAlignRowsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultGridAlignRowsDirective,\n    selectors: [[\"\", \"gdAlignRows\", \"\"], [\"\", \"gdAlignRows.xs\", \"\"], [\"\", \"gdAlignRows.sm\", \"\"], [\"\", \"gdAlignRows.md\", \"\"], [\"\", \"gdAlignRows.lg\", \"\"], [\"\", \"gdAlignRows.xl\", \"\"], [\"\", \"gdAlignRows.lt-sm\", \"\"], [\"\", \"gdAlignRows.lt-md\", \"\"], [\"\", \"gdAlignRows.lt-lg\", \"\"], [\"\", \"gdAlignRows.lt-xl\", \"\"], [\"\", \"gdAlignRows.gt-xs\", \"\"], [\"\", \"gdAlignRows.gt-sm\", \"\"], [\"\", \"gdAlignRows.gt-md\", \"\"], [\"\", \"gdAlignRows.gt-lg\", \"\"]],\n    inputs: {\n      gdAlignRows: \"gdAlignRows\",\n      \"gdAlignRows.xs\": \"gdAlignRows.xs\",\n      \"gdAlignRows.sm\": \"gdAlignRows.sm\",\n      \"gdAlignRows.md\": \"gdAlignRows.md\",\n      \"gdAlignRows.lg\": \"gdAlignRows.lg\",\n      \"gdAlignRows.xl\": \"gdAlignRows.xl\",\n      \"gdAlignRows.lt-sm\": \"gdAlignRows.lt-sm\",\n      \"gdAlignRows.lt-md\": \"gdAlignRows.lt-md\",\n      \"gdAlignRows.lt-lg\": \"gdAlignRows.lt-lg\",\n      \"gdAlignRows.lt-xl\": \"gdAlignRows.lt-xl\",\n      \"gdAlignRows.gt-xs\": \"gdAlignRows.gt-xs\",\n      \"gdAlignRows.gt-sm\": \"gdAlignRows.gt-sm\",\n      \"gdAlignRows.gt-md\": \"gdAlignRows.gt-md\",\n      \"gdAlignRows.gt-lg\": \"gdAlignRows.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultGridAlignRowsDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction buildCss(align, inline) {\n  const css = {},\n        [mainAxis, crossAxis] = align.split(' '); // Main axis\n\n  switch (mainAxis) {\n    case 'center':\n    case 'space-around':\n    case 'space-between':\n    case 'space-evenly':\n    case 'end':\n    case 'start':\n    case 'stretch':\n      css['justify-content'] = mainAxis;\n      break;\n\n    default:\n      css['justify-content'] = DEFAULT_MAIN; // default main axis\n\n      break;\n  } // Cross-axis\n\n\n  switch (crossAxis) {\n    case 'start':\n    case 'center':\n    case 'end':\n    case 'stretch':\n      css['justify-items'] = crossAxis;\n      break;\n\n    default:\n      // 'stretch'\n      css['justify-items'] = DEFAULT_CROSS; // default cross axis\n\n      break;\n  }\n\n  css['display'] = inline ? 'inline-grid' : 'grid';\n  return css;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_VALUE$7 = 'auto';\nlet GridAreaStyleBuilder = /*#__PURE__*/(() => {\n  class GridAreaStyleBuilder extends StyleBuilder {\n    buildStyles(input) {\n      return {\n        'grid-area': input || DEFAULT_VALUE$7\n      };\n    }\n\n  }\n\n  GridAreaStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵGridAreaStyleBuilder_BaseFactory;\n    return function GridAreaStyleBuilder_Factory(t) {\n      return (ɵGridAreaStyleBuilder_BaseFactory || (ɵGridAreaStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(GridAreaStyleBuilder)))(t || GridAreaStyleBuilder);\n    };\n  }();\n\n  GridAreaStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GridAreaStyleBuilder,\n    factory: GridAreaStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return GridAreaStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet GridAreaDirective = /*#__PURE__*/(() => {\n  class GridAreaDirective extends BaseDirective2 {\n    constructor(elRef, styleUtils, styleBuilder, marshal) {\n      super(elRef, styleBuilder, styleUtils, marshal);\n      this.DIRECTIVE_KEY = 'grid-area';\n      this.styleCache = gridAreaCache;\n      this.init();\n    }\n\n  }\n\n  GridAreaDirective.ɵfac = function GridAreaDirective_Factory(t) {\n    return new (t || GridAreaDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(GridAreaStyleBuilder), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  GridAreaDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: GridAreaDirective,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return GridAreaDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst gridAreaCache = /*#__PURE__*/new Map();\nconst inputs$7 = ['gdArea', 'gdArea.xs', 'gdArea.sm', 'gdArea.md', 'gdArea.lg', 'gdArea.xl', 'gdArea.lt-sm', 'gdArea.lt-md', 'gdArea.lt-lg', 'gdArea.lt-xl', 'gdArea.gt-xs', 'gdArea.gt-sm', 'gdArea.gt-md', 'gdArea.gt-lg'];\nconst selector$7 = `\n  [gdArea],\n  [gdArea.xs], [gdArea.sm], [gdArea.md], [gdArea.lg], [gdArea.xl],\n  [gdArea.lt-sm], [gdArea.lt-md], [gdArea.lt-lg], [gdArea.lt-xl],\n  [gdArea.gt-xs], [gdArea.gt-sm], [gdArea.gt-md], [gdArea.gt-lg]\n`;\n/**\n * 'grid-area' CSS Grid styling directive\n * Configures the name or position of an element within the grid\n * @see https://css-tricks.com/snippets/css/complete-guide-grid/#article-header-id-27\n */\n\nlet DefaultGridAreaDirective = /*#__PURE__*/(() => {\n  class DefaultGridAreaDirective extends GridAreaDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$7;\n    }\n\n  }\n\n  DefaultGridAreaDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultGridAreaDirective_BaseFactory;\n    return function DefaultGridAreaDirective_Factory(t) {\n      return (ɵDefaultGridAreaDirective_BaseFactory || (ɵDefaultGridAreaDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultGridAreaDirective)))(t || DefaultGridAreaDirective);\n    };\n  }();\n\n  DefaultGridAreaDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultGridAreaDirective,\n    selectors: [[\"\", \"gdArea\", \"\"], [\"\", \"gdArea.xs\", \"\"], [\"\", \"gdArea.sm\", \"\"], [\"\", \"gdArea.md\", \"\"], [\"\", \"gdArea.lg\", \"\"], [\"\", \"gdArea.xl\", \"\"], [\"\", \"gdArea.lt-sm\", \"\"], [\"\", \"gdArea.lt-md\", \"\"], [\"\", \"gdArea.lt-lg\", \"\"], [\"\", \"gdArea.lt-xl\", \"\"], [\"\", \"gdArea.gt-xs\", \"\"], [\"\", \"gdArea.gt-sm\", \"\"], [\"\", \"gdArea.gt-md\", \"\"], [\"\", \"gdArea.gt-lg\", \"\"]],\n    inputs: {\n      gdArea: \"gdArea\",\n      \"gdArea.xs\": \"gdArea.xs\",\n      \"gdArea.sm\": \"gdArea.sm\",\n      \"gdArea.md\": \"gdArea.md\",\n      \"gdArea.lg\": \"gdArea.lg\",\n      \"gdArea.xl\": \"gdArea.xl\",\n      \"gdArea.lt-sm\": \"gdArea.lt-sm\",\n      \"gdArea.lt-md\": \"gdArea.lt-md\",\n      \"gdArea.lt-lg\": \"gdArea.lt-lg\",\n      \"gdArea.lt-xl\": \"gdArea.lt-xl\",\n      \"gdArea.gt-xs\": \"gdArea.gt-xs\",\n      \"gdArea.gt-sm\": \"gdArea.gt-sm\",\n      \"gdArea.gt-md\": \"gdArea.gt-md\",\n      \"gdArea.gt-lg\": \"gdArea.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultGridAreaDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_VALUE$6 = 'none';\nconst DELIMETER = '|';\nlet GridAreasStyleBuiler = /*#__PURE__*/(() => {\n  class GridAreasStyleBuiler extends StyleBuilder {\n    buildStyles(input, parent) {\n      const areas = (input || DEFAULT_VALUE$6).split(DELIMETER).map(v => `\"${v.trim()}\"`);\n      return {\n        'display': parent.inline ? 'inline-grid' : 'grid',\n        'grid-template-areas': areas.join(' ')\n      };\n    }\n\n  }\n\n  GridAreasStyleBuiler.ɵfac = /* @__PURE__ */function () {\n    let ɵGridAreasStyleBuiler_BaseFactory;\n    return function GridAreasStyleBuiler_Factory(t) {\n      return (ɵGridAreasStyleBuiler_BaseFactory || (ɵGridAreasStyleBuiler_BaseFactory = i0.ɵɵgetInheritedFactory(GridAreasStyleBuiler)))(t || GridAreasStyleBuiler);\n    };\n  }();\n\n  GridAreasStyleBuiler.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GridAreasStyleBuiler,\n    factory: GridAreasStyleBuiler.ɵfac,\n    providedIn: 'root'\n  });\n  return GridAreasStyleBuiler;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet GridAreasDirective = /*#__PURE__*/(() => {\n  class GridAreasDirective extends BaseDirective2 {\n    constructor(elRef, styleUtils, styleBuilder, marshal) {\n      super(elRef, styleBuilder, styleUtils, marshal);\n      this.DIRECTIVE_KEY = 'grid-areas';\n      this._inline = false;\n      this.init();\n    }\n\n    get inline() {\n      return this._inline;\n    }\n\n    set inline(val) {\n      this._inline = coerceBooleanProperty(val);\n    } // *********************************************\n    // Protected methods\n    // *********************************************\n\n\n    updateWithValue(value) {\n      this.styleCache = this.inline ? areasInlineCache : areasCache;\n      this.addStyles(value, {\n        inline: this.inline\n      });\n    }\n\n  }\n\n  GridAreasDirective.ɵfac = function GridAreasDirective_Factory(t) {\n    return new (t || GridAreasDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(GridAreasStyleBuiler), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  GridAreasDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: GridAreasDirective,\n    inputs: {\n      inline: [\"gdInline\", \"inline\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return GridAreasDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst areasCache = /*#__PURE__*/new Map();\nconst areasInlineCache = /*#__PURE__*/new Map();\nconst inputs$6 = ['gdAreas', 'gdAreas.xs', 'gdAreas.sm', 'gdAreas.md', 'gdAreas.lg', 'gdAreas.xl', 'gdAreas.lt-sm', 'gdAreas.lt-md', 'gdAreas.lt-lg', 'gdAreas.lt-xl', 'gdAreas.gt-xs', 'gdAreas.gt-sm', 'gdAreas.gt-md', 'gdAreas.gt-lg'];\nconst selector$6 = `\n  [gdAreas],\n  [gdAreas.xs], [gdAreas.sm], [gdAreas.md], [gdAreas.lg], [gdAreas.xl],\n  [gdAreas.lt-sm], [gdAreas.lt-md], [gdAreas.lt-lg], [gdAreas.lt-xl],\n  [gdAreas.gt-xs], [gdAreas.gt-sm], [gdAreas.gt-md], [gdAreas.gt-lg]\n`;\n/**\n * 'grid-template-areas' CSS Grid styling directive\n * Configures the names of elements within the grid\n * @see https://css-tricks.com/snippets/css/complete-guide-grid/#article-header-id-14\n */\n\nlet DefaultGridAreasDirective = /*#__PURE__*/(() => {\n  class DefaultGridAreasDirective extends GridAreasDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$6;\n    }\n\n  }\n\n  DefaultGridAreasDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultGridAreasDirective_BaseFactory;\n    return function DefaultGridAreasDirective_Factory(t) {\n      return (ɵDefaultGridAreasDirective_BaseFactory || (ɵDefaultGridAreasDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultGridAreasDirective)))(t || DefaultGridAreasDirective);\n    };\n  }();\n\n  DefaultGridAreasDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultGridAreasDirective,\n    selectors: [[\"\", \"gdAreas\", \"\"], [\"\", \"gdAreas.xs\", \"\"], [\"\", \"gdAreas.sm\", \"\"], [\"\", \"gdAreas.md\", \"\"], [\"\", \"gdAreas.lg\", \"\"], [\"\", \"gdAreas.xl\", \"\"], [\"\", \"gdAreas.lt-sm\", \"\"], [\"\", \"gdAreas.lt-md\", \"\"], [\"\", \"gdAreas.lt-lg\", \"\"], [\"\", \"gdAreas.lt-xl\", \"\"], [\"\", \"gdAreas.gt-xs\", \"\"], [\"\", \"gdAreas.gt-sm\", \"\"], [\"\", \"gdAreas.gt-md\", \"\"], [\"\", \"gdAreas.gt-lg\", \"\"]],\n    inputs: {\n      gdAreas: \"gdAreas\",\n      \"gdAreas.xs\": \"gdAreas.xs\",\n      \"gdAreas.sm\": \"gdAreas.sm\",\n      \"gdAreas.md\": \"gdAreas.md\",\n      \"gdAreas.lg\": \"gdAreas.lg\",\n      \"gdAreas.xl\": \"gdAreas.xl\",\n      \"gdAreas.lt-sm\": \"gdAreas.lt-sm\",\n      \"gdAreas.lt-md\": \"gdAreas.lt-md\",\n      \"gdAreas.lt-lg\": \"gdAreas.lt-lg\",\n      \"gdAreas.lt-xl\": \"gdAreas.lt-xl\",\n      \"gdAreas.gt-xs\": \"gdAreas.gt-xs\",\n      \"gdAreas.gt-sm\": \"gdAreas.gt-sm\",\n      \"gdAreas.gt-md\": \"gdAreas.gt-md\",\n      \"gdAreas.gt-lg\": \"gdAreas.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultGridAreasDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_VALUE$5 = 'initial';\nlet GridAutoStyleBuilder = /*#__PURE__*/(() => {\n  class GridAutoStyleBuilder extends StyleBuilder {\n    buildStyles(input, parent) {\n      let [direction, dense] = (input || DEFAULT_VALUE$5).split(' ');\n\n      if (direction !== 'column' && direction !== 'row' && direction !== 'dense') {\n        direction = 'row';\n      }\n\n      dense = dense === 'dense' && direction !== 'dense' ? ' dense' : '';\n      return {\n        'display': parent.inline ? 'inline-grid' : 'grid',\n        'grid-auto-flow': direction + dense\n      };\n    }\n\n  }\n\n  GridAutoStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵGridAutoStyleBuilder_BaseFactory;\n    return function GridAutoStyleBuilder_Factory(t) {\n      return (ɵGridAutoStyleBuilder_BaseFactory || (ɵGridAutoStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(GridAutoStyleBuilder)))(t || GridAutoStyleBuilder);\n    };\n  }();\n\n  GridAutoStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GridAutoStyleBuilder,\n    factory: GridAutoStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return GridAutoStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet GridAutoDirective = /*#__PURE__*/(() => {\n  class GridAutoDirective extends BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal) {\n      super(elementRef, styleBuilder, styler, marshal);\n      this._inline = false;\n      this.DIRECTIVE_KEY = 'grid-auto';\n      this.init();\n    }\n\n    get inline() {\n      return this._inline;\n    }\n\n    set inline(val) {\n      this._inline = coerceBooleanProperty(val);\n    } // *********************************************\n    // Protected methods\n    // *********************************************\n\n\n    updateWithValue(value) {\n      this.styleCache = this.inline ? autoInlineCache : autoCache;\n      this.addStyles(value, {\n        inline: this.inline\n      });\n    }\n\n  }\n\n  GridAutoDirective.ɵfac = function GridAutoDirective_Factory(t) {\n    return new (t || GridAutoDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(GridAutoStyleBuilder), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  GridAutoDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: GridAutoDirective,\n    inputs: {\n      inline: [\"gdInline\", \"inline\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return GridAutoDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst autoCache = /*#__PURE__*/new Map();\nconst autoInlineCache = /*#__PURE__*/new Map();\nconst inputs$5 = ['gdAuto', 'gdAuto.xs', 'gdAuto.sm', 'gdAuto.md', 'gdAuto.lg', 'gdAuto.xl', 'gdAuto.lt-sm', 'gdAuto.lt-md', 'gdAuto.lt-lg', 'gdAuto.lt-xl', 'gdAuto.gt-xs', 'gdAuto.gt-sm', 'gdAuto.gt-md', 'gdAuto.gt-lg'];\nconst selector$5 = `\n  [gdAuto],\n  [gdAuto.xs], [gdAuto.sm], [gdAuto.md], [gdAuto.lg], [gdAuto.xl],\n  [gdAuto.lt-sm], [gdAuto.lt-md], [gdAuto.lt-lg], [gdAuto.lt-xl],\n  [gdAuto.gt-xs], [gdAuto.gt-sm], [gdAuto.gt-md], [gdAuto.gt-lg]\n`;\n/**\n * 'grid-auto-flow' CSS Grid styling directive\n * Configures the auto placement algorithm for the grid\n * @see https://css-tricks.com/snippets/css/complete-guide-grid/#article-header-id-23\n */\n\nlet DefaultGridAutoDirective = /*#__PURE__*/(() => {\n  class DefaultGridAutoDirective extends GridAutoDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$5;\n    }\n\n  }\n\n  DefaultGridAutoDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultGridAutoDirective_BaseFactory;\n    return function DefaultGridAutoDirective_Factory(t) {\n      return (ɵDefaultGridAutoDirective_BaseFactory || (ɵDefaultGridAutoDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultGridAutoDirective)))(t || DefaultGridAutoDirective);\n    };\n  }();\n\n  DefaultGridAutoDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultGridAutoDirective,\n    selectors: [[\"\", \"gdAuto\", \"\"], [\"\", \"gdAuto.xs\", \"\"], [\"\", \"gdAuto.sm\", \"\"], [\"\", \"gdAuto.md\", \"\"], [\"\", \"gdAuto.lg\", \"\"], [\"\", \"gdAuto.xl\", \"\"], [\"\", \"gdAuto.lt-sm\", \"\"], [\"\", \"gdAuto.lt-md\", \"\"], [\"\", \"gdAuto.lt-lg\", \"\"], [\"\", \"gdAuto.lt-xl\", \"\"], [\"\", \"gdAuto.gt-xs\", \"\"], [\"\", \"gdAuto.gt-sm\", \"\"], [\"\", \"gdAuto.gt-md\", \"\"], [\"\", \"gdAuto.gt-lg\", \"\"]],\n    inputs: {\n      gdAuto: \"gdAuto\",\n      \"gdAuto.xs\": \"gdAuto.xs\",\n      \"gdAuto.sm\": \"gdAuto.sm\",\n      \"gdAuto.md\": \"gdAuto.md\",\n      \"gdAuto.lg\": \"gdAuto.lg\",\n      \"gdAuto.xl\": \"gdAuto.xl\",\n      \"gdAuto.lt-sm\": \"gdAuto.lt-sm\",\n      \"gdAuto.lt-md\": \"gdAuto.lt-md\",\n      \"gdAuto.lt-lg\": \"gdAuto.lt-lg\",\n      \"gdAuto.lt-xl\": \"gdAuto.lt-xl\",\n      \"gdAuto.gt-xs\": \"gdAuto.gt-xs\",\n      \"gdAuto.gt-sm\": \"gdAuto.gt-sm\",\n      \"gdAuto.gt-md\": \"gdAuto.gt-md\",\n      \"gdAuto.gt-lg\": \"gdAuto.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultGridAutoDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_VALUE$4 = 'auto';\nlet GridColumnStyleBuilder = /*#__PURE__*/(() => {\n  class GridColumnStyleBuilder extends StyleBuilder {\n    buildStyles(input) {\n      return {\n        'grid-column': input || DEFAULT_VALUE$4\n      };\n    }\n\n  }\n\n  GridColumnStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵGridColumnStyleBuilder_BaseFactory;\n    return function GridColumnStyleBuilder_Factory(t) {\n      return (ɵGridColumnStyleBuilder_BaseFactory || (ɵGridColumnStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(GridColumnStyleBuilder)))(t || GridColumnStyleBuilder);\n    };\n  }();\n\n  GridColumnStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GridColumnStyleBuilder,\n    factory: GridColumnStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return GridColumnStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet GridColumnDirective = /*#__PURE__*/(() => {\n  class GridColumnDirective extends BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal) {\n      super(elementRef, styleBuilder, styler, marshal);\n      this.DIRECTIVE_KEY = 'grid-column';\n      this.styleCache = columnCache;\n      this.init();\n    }\n\n  }\n\n  GridColumnDirective.ɵfac = function GridColumnDirective_Factory(t) {\n    return new (t || GridColumnDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(GridColumnStyleBuilder), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  GridColumnDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: GridColumnDirective,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return GridColumnDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst columnCache = /*#__PURE__*/new Map();\nconst inputs$4 = ['gdColumn', 'gdColumn.xs', 'gdColumn.sm', 'gdColumn.md', 'gdColumn.lg', 'gdColumn.xl', 'gdColumn.lt-sm', 'gdColumn.lt-md', 'gdColumn.lt-lg', 'gdColumn.lt-xl', 'gdColumn.gt-xs', 'gdColumn.gt-sm', 'gdColumn.gt-md', 'gdColumn.gt-lg'];\nconst selector$4 = `\n  [gdColumn],\n  [gdColumn.xs], [gdColumn.sm], [gdColumn.md], [gdColumn.lg], [gdColumn.xl],\n  [gdColumn.lt-sm], [gdColumn.lt-md], [gdColumn.lt-lg], [gdColumn.lt-xl],\n  [gdColumn.gt-xs], [gdColumn.gt-sm], [gdColumn.gt-md], [gdColumn.gt-lg]\n`;\n/**\n * 'grid-column' CSS Grid styling directive\n * Configures the name or position of an element within the grid\n * @see https://css-tricks.com/snippets/css/complete-guide-grid/#article-header-id-26\n */\n\nlet DefaultGridColumnDirective = /*#__PURE__*/(() => {\n  class DefaultGridColumnDirective extends GridColumnDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$4;\n    }\n\n  }\n\n  DefaultGridColumnDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultGridColumnDirective_BaseFactory;\n    return function DefaultGridColumnDirective_Factory(t) {\n      return (ɵDefaultGridColumnDirective_BaseFactory || (ɵDefaultGridColumnDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultGridColumnDirective)))(t || DefaultGridColumnDirective);\n    };\n  }();\n\n  DefaultGridColumnDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultGridColumnDirective,\n    selectors: [[\"\", \"gdColumn\", \"\"], [\"\", \"gdColumn.xs\", \"\"], [\"\", \"gdColumn.sm\", \"\"], [\"\", \"gdColumn.md\", \"\"], [\"\", \"gdColumn.lg\", \"\"], [\"\", \"gdColumn.xl\", \"\"], [\"\", \"gdColumn.lt-sm\", \"\"], [\"\", \"gdColumn.lt-md\", \"\"], [\"\", \"gdColumn.lt-lg\", \"\"], [\"\", \"gdColumn.lt-xl\", \"\"], [\"\", \"gdColumn.gt-xs\", \"\"], [\"\", \"gdColumn.gt-sm\", \"\"], [\"\", \"gdColumn.gt-md\", \"\"], [\"\", \"gdColumn.gt-lg\", \"\"]],\n    inputs: {\n      gdColumn: \"gdColumn\",\n      \"gdColumn.xs\": \"gdColumn.xs\",\n      \"gdColumn.sm\": \"gdColumn.sm\",\n      \"gdColumn.md\": \"gdColumn.md\",\n      \"gdColumn.lg\": \"gdColumn.lg\",\n      \"gdColumn.xl\": \"gdColumn.xl\",\n      \"gdColumn.lt-sm\": \"gdColumn.lt-sm\",\n      \"gdColumn.lt-md\": \"gdColumn.lt-md\",\n      \"gdColumn.lt-lg\": \"gdColumn.lt-lg\",\n      \"gdColumn.lt-xl\": \"gdColumn.lt-xl\",\n      \"gdColumn.gt-xs\": \"gdColumn.gt-xs\",\n      \"gdColumn.gt-sm\": \"gdColumn.gt-sm\",\n      \"gdColumn.gt-md\": \"gdColumn.gt-md\",\n      \"gdColumn.gt-lg\": \"gdColumn.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultGridColumnDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_VALUE$3 = 'none';\nconst AUTO_SPECIFIER$1 = '!';\nlet GridColumnsStyleBuilder = /*#__PURE__*/(() => {\n  class GridColumnsStyleBuilder extends StyleBuilder {\n    buildStyles(input, parent) {\n      input = input || DEFAULT_VALUE$3;\n      let auto = false;\n\n      if (input.endsWith(AUTO_SPECIFIER$1)) {\n        input = input.substring(0, input.indexOf(AUTO_SPECIFIER$1));\n        auto = true;\n      }\n\n      const css = {\n        'display': parent.inline ? 'inline-grid' : 'grid',\n        'grid-auto-columns': '',\n        'grid-template-columns': ''\n      };\n      const key = auto ? 'grid-auto-columns' : 'grid-template-columns';\n      css[key] = input;\n      return css;\n    }\n\n  }\n\n  GridColumnsStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵGridColumnsStyleBuilder_BaseFactory;\n    return function GridColumnsStyleBuilder_Factory(t) {\n      return (ɵGridColumnsStyleBuilder_BaseFactory || (ɵGridColumnsStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(GridColumnsStyleBuilder)))(t || GridColumnsStyleBuilder);\n    };\n  }();\n\n  GridColumnsStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GridColumnsStyleBuilder,\n    factory: GridColumnsStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return GridColumnsStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet GridColumnsDirective = /*#__PURE__*/(() => {\n  class GridColumnsDirective extends BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal) {\n      super(elementRef, styleBuilder, styler, marshal);\n      this.DIRECTIVE_KEY = 'grid-columns';\n      this._inline = false;\n      this.init();\n    }\n\n    get inline() {\n      return this._inline;\n    }\n\n    set inline(val) {\n      this._inline = coerceBooleanProperty(val);\n    } // *********************************************\n    // Protected methods\n    // *********************************************\n\n\n    updateWithValue(value) {\n      this.styleCache = this.inline ? columnsInlineCache : columnsCache;\n      this.addStyles(value, {\n        inline: this.inline\n      });\n    }\n\n  }\n\n  GridColumnsDirective.ɵfac = function GridColumnsDirective_Factory(t) {\n    return new (t || GridColumnsDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(GridColumnsStyleBuilder), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  GridColumnsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: GridColumnsDirective,\n    inputs: {\n      inline: [\"gdInline\", \"inline\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return GridColumnsDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst columnsCache = /*#__PURE__*/new Map();\nconst columnsInlineCache = /*#__PURE__*/new Map();\nconst inputs$3 = ['gdColumns', 'gdColumns.xs', 'gdColumns.sm', 'gdColumns.md', 'gdColumns.lg', 'gdColumns.xl', 'gdColumns.lt-sm', 'gdColumns.lt-md', 'gdColumns.lt-lg', 'gdColumns.lt-xl', 'gdColumns.gt-xs', 'gdColumns.gt-sm', 'gdColumns.gt-md', 'gdColumns.gt-lg'];\nconst selector$3 = `\n  [gdColumns],\n  [gdColumns.xs], [gdColumns.sm], [gdColumns.md], [gdColumns.lg], [gdColumns.xl],\n  [gdColumns.lt-sm], [gdColumns.lt-md], [gdColumns.lt-lg], [gdColumns.lt-xl],\n  [gdColumns.gt-xs], [gdColumns.gt-sm], [gdColumns.gt-md], [gdColumns.gt-lg]\n`;\n/**\n * 'grid-template-columns' CSS Grid styling directive\n * Configures the sizing for the columns in the grid\n * Syntax: <column value> [auto]\n * @see https://css-tricks.com/snippets/css/complete-guide-grid/#article-header-id-13\n */\n\nlet DefaultGridColumnsDirective = /*#__PURE__*/(() => {\n  class DefaultGridColumnsDirective extends GridColumnsDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$3;\n    }\n\n  }\n\n  DefaultGridColumnsDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultGridColumnsDirective_BaseFactory;\n    return function DefaultGridColumnsDirective_Factory(t) {\n      return (ɵDefaultGridColumnsDirective_BaseFactory || (ɵDefaultGridColumnsDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultGridColumnsDirective)))(t || DefaultGridColumnsDirective);\n    };\n  }();\n\n  DefaultGridColumnsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultGridColumnsDirective,\n    selectors: [[\"\", \"gdColumns\", \"\"], [\"\", \"gdColumns.xs\", \"\"], [\"\", \"gdColumns.sm\", \"\"], [\"\", \"gdColumns.md\", \"\"], [\"\", \"gdColumns.lg\", \"\"], [\"\", \"gdColumns.xl\", \"\"], [\"\", \"gdColumns.lt-sm\", \"\"], [\"\", \"gdColumns.lt-md\", \"\"], [\"\", \"gdColumns.lt-lg\", \"\"], [\"\", \"gdColumns.lt-xl\", \"\"], [\"\", \"gdColumns.gt-xs\", \"\"], [\"\", \"gdColumns.gt-sm\", \"\"], [\"\", \"gdColumns.gt-md\", \"\"], [\"\", \"gdColumns.gt-lg\", \"\"]],\n    inputs: {\n      gdColumns: \"gdColumns\",\n      \"gdColumns.xs\": \"gdColumns.xs\",\n      \"gdColumns.sm\": \"gdColumns.sm\",\n      \"gdColumns.md\": \"gdColumns.md\",\n      \"gdColumns.lg\": \"gdColumns.lg\",\n      \"gdColumns.xl\": \"gdColumns.xl\",\n      \"gdColumns.lt-sm\": \"gdColumns.lt-sm\",\n      \"gdColumns.lt-md\": \"gdColumns.lt-md\",\n      \"gdColumns.lt-lg\": \"gdColumns.lt-lg\",\n      \"gdColumns.lt-xl\": \"gdColumns.lt-xl\",\n      \"gdColumns.gt-xs\": \"gdColumns.gt-xs\",\n      \"gdColumns.gt-sm\": \"gdColumns.gt-sm\",\n      \"gdColumns.gt-md\": \"gdColumns.gt-md\",\n      \"gdColumns.gt-lg\": \"gdColumns.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultGridColumnsDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_VALUE$2 = '0';\nlet GridGapStyleBuilder = /*#__PURE__*/(() => {\n  class GridGapStyleBuilder extends StyleBuilder {\n    buildStyles(input, parent) {\n      return {\n        'display': parent.inline ? 'inline-grid' : 'grid',\n        'grid-gap': input || DEFAULT_VALUE$2\n      };\n    }\n\n  }\n\n  GridGapStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵGridGapStyleBuilder_BaseFactory;\n    return function GridGapStyleBuilder_Factory(t) {\n      return (ɵGridGapStyleBuilder_BaseFactory || (ɵGridGapStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(GridGapStyleBuilder)))(t || GridGapStyleBuilder);\n    };\n  }();\n\n  GridGapStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GridGapStyleBuilder,\n    factory: GridGapStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return GridGapStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet GridGapDirective = /*#__PURE__*/(() => {\n  class GridGapDirective extends BaseDirective2 {\n    constructor(elRef, styleUtils, styleBuilder, marshal) {\n      super(elRef, styleBuilder, styleUtils, marshal);\n      this.DIRECTIVE_KEY = 'grid-gap';\n      this._inline = false;\n      this.init();\n    }\n\n    get inline() {\n      return this._inline;\n    }\n\n    set inline(val) {\n      this._inline = coerceBooleanProperty(val);\n    } // *********************************************\n    // Protected methods\n    // *********************************************\n\n\n    updateWithValue(value) {\n      this.styleCache = this.inline ? gapInlineCache : gapCache;\n      this.addStyles(value, {\n        inline: this.inline\n      });\n    }\n\n  }\n\n  GridGapDirective.ɵfac = function GridGapDirective_Factory(t) {\n    return new (t || GridGapDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(GridGapStyleBuilder), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  GridGapDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: GridGapDirective,\n    inputs: {\n      inline: [\"gdInline\", \"inline\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return GridGapDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst gapCache = /*#__PURE__*/new Map();\nconst gapInlineCache = /*#__PURE__*/new Map();\nconst inputs$2 = ['gdGap', 'gdGap.xs', 'gdGap.sm', 'gdGap.md', 'gdGap.lg', 'gdGap.xl', 'gdGap.lt-sm', 'gdGap.lt-md', 'gdGap.lt-lg', 'gdGap.lt-xl', 'gdGap.gt-xs', 'gdGap.gt-sm', 'gdGap.gt-md', 'gdGap.gt-lg'];\nconst selector$2 = `\n  [gdGap],\n  [gdGap.xs], [gdGap.sm], [gdGap.md], [gdGap.lg], [gdGap.xl],\n  [gdGap.lt-sm], [gdGap.lt-md], [gdGap.lt-lg], [gdGap.lt-xl],\n  [gdGap.gt-xs], [gdGap.gt-sm], [gdGap.gt-md], [gdGap.gt-lg]\n`;\n/**\n * 'grid-gap' CSS Grid styling directive\n * Configures the gap between items in the grid\n * Syntax: <row gap> [<column-gap>]\n * @see https://css-tricks.com/snippets/css/complete-guide-grid/#article-header-id-17\n */\n\nlet DefaultGridGapDirective = /*#__PURE__*/(() => {\n  class DefaultGridGapDirective extends GridGapDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$2;\n    }\n\n  }\n\n  DefaultGridGapDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultGridGapDirective_BaseFactory;\n    return function DefaultGridGapDirective_Factory(t) {\n      return (ɵDefaultGridGapDirective_BaseFactory || (ɵDefaultGridGapDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultGridGapDirective)))(t || DefaultGridGapDirective);\n    };\n  }();\n\n  DefaultGridGapDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultGridGapDirective,\n    selectors: [[\"\", \"gdGap\", \"\"], [\"\", \"gdGap.xs\", \"\"], [\"\", \"gdGap.sm\", \"\"], [\"\", \"gdGap.md\", \"\"], [\"\", \"gdGap.lg\", \"\"], [\"\", \"gdGap.xl\", \"\"], [\"\", \"gdGap.lt-sm\", \"\"], [\"\", \"gdGap.lt-md\", \"\"], [\"\", \"gdGap.lt-lg\", \"\"], [\"\", \"gdGap.lt-xl\", \"\"], [\"\", \"gdGap.gt-xs\", \"\"], [\"\", \"gdGap.gt-sm\", \"\"], [\"\", \"gdGap.gt-md\", \"\"], [\"\", \"gdGap.gt-lg\", \"\"]],\n    inputs: {\n      gdGap: \"gdGap\",\n      \"gdGap.xs\": \"gdGap.xs\",\n      \"gdGap.sm\": \"gdGap.sm\",\n      \"gdGap.md\": \"gdGap.md\",\n      \"gdGap.lg\": \"gdGap.lg\",\n      \"gdGap.xl\": \"gdGap.xl\",\n      \"gdGap.lt-sm\": \"gdGap.lt-sm\",\n      \"gdGap.lt-md\": \"gdGap.lt-md\",\n      \"gdGap.lt-lg\": \"gdGap.lt-lg\",\n      \"gdGap.lt-xl\": \"gdGap.lt-xl\",\n      \"gdGap.gt-xs\": \"gdGap.gt-xs\",\n      \"gdGap.gt-sm\": \"gdGap.gt-sm\",\n      \"gdGap.gt-md\": \"gdGap.gt-md\",\n      \"gdGap.gt-lg\": \"gdGap.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultGridGapDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_VALUE$1 = 'auto';\nlet GridRowStyleBuilder = /*#__PURE__*/(() => {\n  class GridRowStyleBuilder extends StyleBuilder {\n    buildStyles(input) {\n      return {\n        'grid-row': input || DEFAULT_VALUE$1\n      };\n    }\n\n  }\n\n  GridRowStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵGridRowStyleBuilder_BaseFactory;\n    return function GridRowStyleBuilder_Factory(t) {\n      return (ɵGridRowStyleBuilder_BaseFactory || (ɵGridRowStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(GridRowStyleBuilder)))(t || GridRowStyleBuilder);\n    };\n  }();\n\n  GridRowStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GridRowStyleBuilder,\n    factory: GridRowStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return GridRowStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet GridRowDirective = /*#__PURE__*/(() => {\n  class GridRowDirective extends BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal) {\n      super(elementRef, styleBuilder, styler, marshal);\n      this.DIRECTIVE_KEY = 'grid-row';\n      this.styleCache = rowCache;\n      this.init();\n    }\n\n  }\n\n  GridRowDirective.ɵfac = function GridRowDirective_Factory(t) {\n    return new (t || GridRowDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(GridRowStyleBuilder), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  GridRowDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: GridRowDirective,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return GridRowDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst rowCache = /*#__PURE__*/new Map();\nconst inputs$1 = ['gdRow', 'gdRow.xs', 'gdRow.sm', 'gdRow.md', 'gdRow.lg', 'gdRow.xl', 'gdRow.lt-sm', 'gdRow.lt-md', 'gdRow.lt-lg', 'gdRow.lt-xl', 'gdRow.gt-xs', 'gdRow.gt-sm', 'gdRow.gt-md', 'gdRow.gt-lg'];\nconst selector$1 = `\n  [gdRow],\n  [gdRow.xs], [gdRow.sm], [gdRow.md], [gdRow.lg], [gdRow.xl],\n  [gdRow.lt-sm], [gdRow.lt-md], [gdRow.lt-lg], [gdRow.lt-xl],\n  [gdRow.gt-xs], [gdRow.gt-sm], [gdRow.gt-md], [gdRow.gt-lg]\n`;\n/**\n * 'grid-row' CSS Grid styling directive\n * Configures the name or position of an element within the grid\n * @see https://css-tricks.com/snippets/css/complete-guide-grid/#article-header-id-26\n */\n\nlet DefaultGridRowDirective = /*#__PURE__*/(() => {\n  class DefaultGridRowDirective extends GridRowDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs$1;\n    }\n\n  }\n\n  DefaultGridRowDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultGridRowDirective_BaseFactory;\n    return function DefaultGridRowDirective_Factory(t) {\n      return (ɵDefaultGridRowDirective_BaseFactory || (ɵDefaultGridRowDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultGridRowDirective)))(t || DefaultGridRowDirective);\n    };\n  }();\n\n  DefaultGridRowDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultGridRowDirective,\n    selectors: [[\"\", \"gdRow\", \"\"], [\"\", \"gdRow.xs\", \"\"], [\"\", \"gdRow.sm\", \"\"], [\"\", \"gdRow.md\", \"\"], [\"\", \"gdRow.lg\", \"\"], [\"\", \"gdRow.xl\", \"\"], [\"\", \"gdRow.lt-sm\", \"\"], [\"\", \"gdRow.lt-md\", \"\"], [\"\", \"gdRow.lt-lg\", \"\"], [\"\", \"gdRow.lt-xl\", \"\"], [\"\", \"gdRow.gt-xs\", \"\"], [\"\", \"gdRow.gt-sm\", \"\"], [\"\", \"gdRow.gt-md\", \"\"], [\"\", \"gdRow.gt-lg\", \"\"]],\n    inputs: {\n      gdRow: \"gdRow\",\n      \"gdRow.xs\": \"gdRow.xs\",\n      \"gdRow.sm\": \"gdRow.sm\",\n      \"gdRow.md\": \"gdRow.md\",\n      \"gdRow.lg\": \"gdRow.lg\",\n      \"gdRow.xl\": \"gdRow.xl\",\n      \"gdRow.lt-sm\": \"gdRow.lt-sm\",\n      \"gdRow.lt-md\": \"gdRow.lt-md\",\n      \"gdRow.lt-lg\": \"gdRow.lt-lg\",\n      \"gdRow.lt-xl\": \"gdRow.lt-xl\",\n      \"gdRow.gt-xs\": \"gdRow.gt-xs\",\n      \"gdRow.gt-sm\": \"gdRow.gt-sm\",\n      \"gdRow.gt-md\": \"gdRow.gt-md\",\n      \"gdRow.gt-lg\": \"gdRow.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultGridRowDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_VALUE = 'none';\nconst AUTO_SPECIFIER = '!';\nlet GridRowsStyleBuilder = /*#__PURE__*/(() => {\n  class GridRowsStyleBuilder extends StyleBuilder {\n    buildStyles(input, parent) {\n      input = input || DEFAULT_VALUE;\n      let auto = false;\n\n      if (input.endsWith(AUTO_SPECIFIER)) {\n        input = input.substring(0, input.indexOf(AUTO_SPECIFIER));\n        auto = true;\n      }\n\n      const css = {\n        'display': parent.inline ? 'inline-grid' : 'grid',\n        'grid-auto-rows': '',\n        'grid-template-rows': ''\n      };\n      const key = auto ? 'grid-auto-rows' : 'grid-template-rows';\n      css[key] = input;\n      return css;\n    }\n\n  }\n\n  GridRowsStyleBuilder.ɵfac = /* @__PURE__ */function () {\n    let ɵGridRowsStyleBuilder_BaseFactory;\n    return function GridRowsStyleBuilder_Factory(t) {\n      return (ɵGridRowsStyleBuilder_BaseFactory || (ɵGridRowsStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(GridRowsStyleBuilder)))(t || GridRowsStyleBuilder);\n    };\n  }();\n\n  GridRowsStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GridRowsStyleBuilder,\n    factory: GridRowsStyleBuilder.ɵfac,\n    providedIn: 'root'\n  });\n  return GridRowsStyleBuilder;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet GridRowsDirective = /*#__PURE__*/(() => {\n  class GridRowsDirective extends BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal) {\n      super(elementRef, styleBuilder, styler, marshal);\n      this.DIRECTIVE_KEY = 'grid-rows';\n      this._inline = false;\n      this.init();\n    }\n\n    get inline() {\n      return this._inline;\n    }\n\n    set inline(val) {\n      this._inline = coerceBooleanProperty(val);\n    } // *********************************************\n    // Protected methods\n    // *********************************************\n\n\n    updateWithValue(value) {\n      this.styleCache = this.inline ? rowsInlineCache : rowsCache;\n      this.addStyles(value, {\n        inline: this.inline\n      });\n    }\n\n  }\n\n  GridRowsDirective.ɵfac = function GridRowsDirective_Factory(t) {\n    return new (t || GridRowsDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(GridRowsStyleBuilder), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller));\n  };\n\n  GridRowsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: GridRowsDirective,\n    inputs: {\n      inline: [\"gdInline\", \"inline\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return GridRowsDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst rowsCache = /*#__PURE__*/new Map();\nconst rowsInlineCache = /*#__PURE__*/new Map();\nconst inputs = ['gdRows', 'gdRows.xs', 'gdRows.sm', 'gdRows.md', 'gdRows.lg', 'gdRows.xl', 'gdRows.lt-sm', 'gdRows.lt-md', 'gdRows.lt-lg', 'gdRows.lt-xl', 'gdRows.gt-xs', 'gdRows.gt-sm', 'gdRows.gt-md', 'gdRows.gt-lg'];\nconst selector = `\n  [gdRows],\n  [gdRows.xs], [gdRows.sm], [gdRows.md], [gdRows.lg], [gdRows.xl],\n  [gdRows.lt-sm], [gdRows.lt-md], [gdRows.lt-lg], [gdRows.lt-xl],\n  [gdRows.gt-xs], [gdRows.gt-sm], [gdRows.gt-md], [gdRows.gt-lg]\n`;\n/**\n * 'grid-template-rows' CSS Grid styling directive\n * Configures the sizing for the rows in the grid\n * Syntax: <column value> [auto]\n * @see https://css-tricks.com/snippets/css/complete-guide-grid/#article-header-id-13\n */\n\nlet DefaultGridRowsDirective = /*#__PURE__*/(() => {\n  class DefaultGridRowsDirective extends GridRowsDirective {\n    constructor() {\n      super(...arguments);\n      this.inputs = inputs;\n    }\n\n  }\n\n  DefaultGridRowsDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵDefaultGridRowsDirective_BaseFactory;\n    return function DefaultGridRowsDirective_Factory(t) {\n      return (ɵDefaultGridRowsDirective_BaseFactory || (ɵDefaultGridRowsDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultGridRowsDirective)))(t || DefaultGridRowsDirective);\n    };\n  }();\n\n  DefaultGridRowsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultGridRowsDirective,\n    selectors: [[\"\", \"gdRows\", \"\"], [\"\", \"gdRows.xs\", \"\"], [\"\", \"gdRows.sm\", \"\"], [\"\", \"gdRows.md\", \"\"], [\"\", \"gdRows.lg\", \"\"], [\"\", \"gdRows.xl\", \"\"], [\"\", \"gdRows.lt-sm\", \"\"], [\"\", \"gdRows.lt-md\", \"\"], [\"\", \"gdRows.lt-lg\", \"\"], [\"\", \"gdRows.lt-xl\", \"\"], [\"\", \"gdRows.gt-xs\", \"\"], [\"\", \"gdRows.gt-sm\", \"\"], [\"\", \"gdRows.gt-md\", \"\"], [\"\", \"gdRows.gt-lg\", \"\"]],\n    inputs: {\n      gdRows: \"gdRows\",\n      \"gdRows.xs\": \"gdRows.xs\",\n      \"gdRows.sm\": \"gdRows.sm\",\n      \"gdRows.md\": \"gdRows.md\",\n      \"gdRows.lg\": \"gdRows.lg\",\n      \"gdRows.xl\": \"gdRows.xl\",\n      \"gdRows.lt-sm\": \"gdRows.lt-sm\",\n      \"gdRows.lt-md\": \"gdRows.lt-md\",\n      \"gdRows.lt-lg\": \"gdRows.lt-lg\",\n      \"gdRows.lt-xl\": \"gdRows.lt-xl\",\n      \"gdRows.gt-xs\": \"gdRows.gt-xs\",\n      \"gdRows.gt-sm\": \"gdRows.gt-sm\",\n      \"gdRows.gt-md\": \"gdRows.gt-md\",\n      \"gdRows.gt-lg\": \"gdRows.gt-lg\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return DefaultGridRowsDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst ALL_DIRECTIVES = [DefaultGridAlignDirective, DefaultGridAlignColumnsDirective, DefaultGridAlignRowsDirective, DefaultGridAreaDirective, DefaultGridAreasDirective, DefaultGridAutoDirective, DefaultGridColumnDirective, DefaultGridColumnsDirective, DefaultGridGapDirective, DefaultGridRowDirective, DefaultGridRowsDirective];\n/**\n * *****************************************************************\n * Define module for the CSS Grid API\n * *****************************************************************\n */\n\nlet GridModule = /*#__PURE__*/(() => {\n  class GridModule {}\n\n  GridModule.ɵfac = function GridModule_Factory(t) {\n    return new (t || GridModule)();\n  };\n\n  GridModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: GridModule\n  });\n  GridModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CoreModule]]\n  });\n  return GridModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { DefaultGridAlignColumnsDirective, DefaultGridAlignDirective, DefaultGridAlignRowsDirective, DefaultGridAreaDirective, DefaultGridAreasDirective, DefaultGridAutoDirective, DefaultGridColumnDirective, DefaultGridColumnsDirective, DefaultGridGapDirective, DefaultGridRowDirective, DefaultGridRowsDirective, GridAlignColumnsDirective, GridAlignColumnsStyleBuilder, GridAlignDirective, GridAlignRowsDirective, GridAlignRowsStyleBuilder, GridAlignStyleBuilder, GridAreaDirective, GridAreaStyleBuilder, GridAreasDirective, GridAreasStyleBuiler, GridAutoDirective, GridAutoStyleBuilder, GridColumnDirective, GridColumnStyleBuilder, GridColumnsDirective, GridColumnsStyleBuilder, GridGapDirective, GridGapStyleBuilder, GridModule, GridRowDirective, GridRowStyleBuilder, GridRowsDirective, GridRowsStyleBuilder };", "map": null, "metadata": {}, "sourceType": "module"}