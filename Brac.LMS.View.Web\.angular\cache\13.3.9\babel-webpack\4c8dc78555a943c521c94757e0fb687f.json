{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let AuthLayoutComponent = /*#__PURE__*/(() => {\n  class AuthLayoutComponent {}\n\n  AuthLayoutComponent.ɵfac = function AuthLayoutComponent_Factory(t) {\n    return new (t || AuthLayoutComponent)();\n  };\n\n  AuthLayoutComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AuthLayoutComponent,\n    selectors: [[\"app-layout\"]],\n    decls: 1,\n    vars: 0,\n    template: function AuthLayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"router-outlet\");\n      }\n    },\n    directives: [i1.RouterOutlet],\n    encapsulation: 2\n  });\n  return AuthLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}