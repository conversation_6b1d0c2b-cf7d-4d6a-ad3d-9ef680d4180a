{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { environment } from 'src/environments/environment';\nimport { NavigationEnd } from '@angular/router';\nimport { BlockUI } from 'ng-block-ui';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"../_services/authentication.service\";\nimport * as i5 from \"ng-block-ui\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = [\"inputProfileImage\"];\n\nfunction ProfileComponent_div_8_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 20);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r3.profileData.Name);\n  }\n}\n\nfunction ProfileComponent_div_8_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 21);\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate2(\"src\", \"\", ctx_r4.mediaBaseUrl, \"\", ctx_r4.profileData.ImagePath, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r4.profileData.Name);\n  }\n}\n\nfunction ProfileComponent_div_8_br_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"br\");\n  }\n}\n\nfunction ProfileComponent_div_8_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1, \"Upload JPG, GIF or PNG image.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ProfileComponent_div_8_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_8_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return ctx_r10.uploadProfileImage();\n    });\n    i0.ɵɵelement(1, \"i\", 24);\n    i0.ɵɵtext(2, \"Upload\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ProfileComponent_div_8_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_8_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return ctx_r12.clearFile();\n    });\n    i0.ɵɵelement(1, \"i\", 26);\n    i0.ɵɵtext(2, \"Clear\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ProfileComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵtemplate(2, ProfileComponent_div_8_img_2_Template, 1, 1, \"img\", 11);\n    i0.ɵɵtemplate(3, ProfileComponent_div_8_img_3_Template, 1, 3, \"img\", 12);\n    i0.ɵɵelementStart(4, \"div\", 13);\n    i0.ɵɵelementStart(5, \"input\", 14, 15);\n    i0.ɵɵlistener(\"change\", function ProfileComponent_div_8_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return ctx_r14.onImageFileChange($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ProfileComponent_div_8_br_7_Template, 1, 0, \"br\", 16);\n    i0.ɵɵtemplate(8, ProfileComponent_div_8_div_8_Template, 2, 0, \"div\", 17);\n    i0.ɵɵtemplate(9, ProfileComponent_div_8_button_9_Template, 3, 0, \"button\", 18);\n    i0.ɵɵtemplate(10, ProfileComponent_div_8_button_10_Template, 3, 0, \"button\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.profileData.ImagePath);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profileData.ImagePath);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profileImage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.profileImage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profileImage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profileImage);\n  }\n}\n\nfunction ProfileComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelementStart(1, \"div\", 28);\n    i0.ɵɵelementStart(2, \"table\", 29);\n    i0.ɵɵelementStart(3, \"tr\", 30);\n    i0.ɵɵelementStart(4, \"td\", 31);\n    i0.ɵɵtext(5, \"PIN\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 32);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"tr\", 30);\n    i0.ɵɵelementStart(11, \"td\", 33);\n    i0.ɵɵtext(12, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 32);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"tr\", 30);\n    i0.ɵɵelementStart(18, \"td\", 33);\n    i0.ɵɵtext(19, \"Gender\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\");\n    i0.ɵɵtext(21, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\", 32);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"tr\", 30);\n    i0.ɵɵelementStart(25, \"td\", 33);\n    i0.ɵɵtext(26, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\");\n    i0.ɵɵtext(28, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"td\", 32);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"tr\", 30);\n    i0.ɵɵelementStart(32, \"td\", 33);\n    i0.ɵɵtext(33, \"Phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"td\");\n    i0.ɵɵtext(35, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"td\", 32);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"tr\", 30);\n    i0.ɵɵelementStart(39, \"td\", 33);\n    i0.ɵɵtext(40, \"Date Of Joining\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"td\");\n    i0.ɵɵtext(42, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"td\", 32);\n    i0.ɵɵtext(44);\n    i0.ɵɵpipe(45, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"tr\", 30);\n    i0.ɵɵelementStart(47, \"td\", 33);\n    i0.ɵɵtext(48, \"Employee Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"td\");\n    i0.ɵɵtext(50, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"td\", 32);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"tr\", 30);\n    i0.ɵɵelementStart(54, \"td\", 33);\n    i0.ɵɵtext(55, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"td\");\n    i0.ɵɵtext(57, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"td\", 32);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.profileData.PIN);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.profileData.Name);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.profileData.Gender);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.profileData.Email);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.profileData.PhoneNo);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(45, 8, ctx_r1.profileData.DateOfJoining));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.profileData.EmployeeType);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.profileData.Address);\n  }\n}\n\nfunction ProfileComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelementStart(1, \"div\", 28);\n    i0.ɵɵelementStart(2, \"table\", 29);\n    i0.ɵɵelementStart(3, \"tr\", 30);\n    i0.ɵɵelementStart(4, \"td\", 31);\n    i0.ɵɵtext(5, \"Division\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 32);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"tr\", 30);\n    i0.ɵɵelementStart(11, \"td\", 33);\n    i0.ɵɵtext(12, \"Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 32);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"tr\", 30);\n    i0.ɵɵelementStart(18, \"td\", 33);\n    i0.ɵɵtext(19, \"Unit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\");\n    i0.ɵɵtext(21, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\", 32);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"tr\", 30);\n    i0.ɵɵelementStart(25, \"td\", 33);\n    i0.ɵɵtext(26, \"Sub Unit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\");\n    i0.ɵɵtext(28, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"td\", 32);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"tr\", 30);\n    i0.ɵɵelementStart(32, \"td\", 33);\n    i0.ɵɵtext(33, \"Grade\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"td\");\n    i0.ɵɵtext(35, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"td\", 32);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"tr\", 30);\n    i0.ɵɵelementStart(39, \"td\", 33);\n    i0.ɵɵtext(40, \"Position\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"td\");\n    i0.ɵɵtext(42, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"td\", 32);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"tr\", 30);\n    i0.ɵɵelementStart(46, \"td\", 33);\n    i0.ɵɵtext(47, \"Work Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"td\");\n    i0.ɵɵtext(49, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"td\", 32);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"tr\", 30);\n    i0.ɵɵelementStart(53, \"td\", 33);\n    i0.ɵɵtext(54, \"Line Manager \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"td\");\n    i0.ɵɵtext(56, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"td\", 32);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.profileData.Division);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.profileData.Department);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.profileData.Unit);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.profileData.SubUnit);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.profileData.Grade);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.profileData.Position);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.profileData.WorkLocation);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.profileData.LineManagerName, \" - \", ctx_r2.profileData.LineManagerPIN, \"\");\n  }\n}\n\nexport class ProfileComponent {\n  constructor(router, _service, toastr, route, authService) {\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.route = route;\n    this.authService = authService;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.timestamp = new Date().getTime();\n    this.profileData = null;\n    this.profileImage = null;\n    this.router.events.subscribe(evt => {\n      if (!(evt instanceof NavigationEnd)) {\n        return;\n      }\n\n      window.scrollTo(0, 0);\n    });\n  }\n\n  ngOnInit() {\n    this.getUserProfile();\n  }\n\n  getUserProfile() {\n    this._service.get('trainee/get-profile').subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        console.log(res.Data);\n        this.profileData = res.Data;\n        console.log(this.profileData);\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n      },\n      complete: () => {}\n    });\n  }\n\n  onImageFileChange(event) {\n    this.profileImage = null;\n\n    if (event.target.files && event.target.files[0]) {\n      let allowedImageMimeTypes = [\"image/jpeg\", \"image/gif\", \"image/png\"];\n      let imageFile = event.target.files[0];\n\n      if (!allowedImageMimeTypes.includes(imageFile.type)) {\n        this.toastr.error('Unsupported file type: ' + imageFile.type, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.clearFile();\n      }\n\n      var reader = new FileReader();\n\n      reader.onload = event => {\n        this.profileImage = {\n          baseFile: event.target.result,\n          rawFile: imageFile\n        };\n      };\n\n      reader.readAsDataURL(event.target.files[0]);\n    }\n  }\n\n  uploadProfileImage() {\n    this.blockUI.start('Uploading image. Please wait...');\n    let formData = new FormData();\n\n    if (this.profileImage) {\n      formData.append('photo', this.profileImage.rawFile);\n    } else {\n      this.toastr.warning('Please choose a image first.', 'Warning!', {\n        timeOut: 2000\n      });\n      return;\n    }\n\n    this._service.post('account/update-photo', formData).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        } // window.location.reload();\n\n\n        this.authService.updateImage(res.Data);\n        this.profileData.ImagePath = res.Data;\n        this.clearFile();\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  clearFile() {\n    this.inputProfileImage.nativeElement.value = '';\n    this.profileImage = null;\n  }\n\n}\n\nProfileComponent.ɵfac = function ProfileComponent_Factory(t) {\n  return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i4.AuthenticationService));\n};\n\nProfileComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ProfileComponent,\n  selectors: [[\"app-profile\"]],\n  viewQuery: function ProfileComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputProfileImage = _t.first);\n    }\n  },\n  decls: 12,\n  vars: 3,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\", \"font-weight-bold\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\", \"font-weight-bold\"], [1, \"mt-1\", \"mb-4\"], [\"class\", \"rounded-3 p-4 mb-4\", 4, \"ngIf\"], [1, \"row\"], [\"class\", \"col-md-6\", 4, \"ngIf\"], [1, \"rounded-3\", \"p-4\", \"mb-4\"], [1, \"d-block\", \"d-sm-flex\", \"align-items-center\"], [\"class\", \"d-block rounded-circle mx-sm-0 mx-auto mb-3 mb-sm-0\", \"src\", \"assets/img/demo/profile.jpg\", \"width\", \"160\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"d-block rounded-circle mx-sm-0 mx-auto mb-3 mb-sm-0\", \"width\", \"160\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"ps-sm-3\", \"text-center\", \"text-sm-start\"], [\"accept\", \"image/png, image/jpeg\", \"type\", \"file\", 1, \"form-control\", 3, \"change\"], [\"inputProfileImage\", \"\"], [4, \"ngIf\"], [\"class\", \"p mt-1 mb-0 fs-ms  font-weight-bold\", 4, \"ngIf\"], [\"class\", \"btn btn-success shadow btn-sm mb-2 me-2\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-danger shadow btn-sm mb-2\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"src\", \"assets/img/demo/profile.jpg\", \"width\", \"160\", 1, \"d-block\", \"rounded-circle\", \"mx-sm-0\", \"mx-auto\", \"mb-3\", \"mb-sm-0\", 3, \"alt\"], [\"width\", \"160\", 1, \"d-block\", \"rounded-circle\", \"mx-sm-0\", \"mx-auto\", \"mb-3\", \"mb-sm-0\", 3, \"src\", \"alt\"], [1, \"p\", \"mt-1\", \"mb-0\", \"fs-ms\", \"font-weight-bold\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"shadow\", \"btn-sm\", \"mb-2\", \"me-2\", 3, \"click\"], [1, \"ai-upload\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"shadow\", \"btn-sm\", \"mb-2\", 3, \"click\"], [1, \"ai-refresh-cw\", \"me-2\"], [1, \"col-md-6\"], [1, \"table-responsive\"], [1, \"table\", \"table-borderless\"], [1, \"table-line-height\"], [\"width\", \"180\", 1, \"fw-bold\"], [1, \"font-weight-bold\"], [1, \"fw-bold\"]],\n  template: function ProfileComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h1\", 4);\n      i0.ɵɵtext(6, \"Personal Information\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(7, \"hr\", 5);\n      i0.ɵɵtemplate(8, ProfileComponent_div_8_Template, 11, 6, \"div\", 6);\n      i0.ɵɵelementStart(9, \"div\", 7);\n      i0.ɵɵtemplate(10, ProfileComponent_div_10_Template, 60, 10, \"div\", 8);\n      i0.ɵɵtemplate(11, ProfileComponent_div_11_Template, 59, 9, \"div\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngIf\", ctx.profileData);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.profileData);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.profileData);\n    }\n  },\n  directives: [i5.BlockUIComponent, i6.NgIf],\n  pipes: [i6.DatePipe],\n  styles: [\"\"]\n});\n\n__decorate([BlockUI()], ProfileComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}