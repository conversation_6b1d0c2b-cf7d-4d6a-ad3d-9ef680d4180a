{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, PLATFORM_ID, Inject, Directive, Input, NgModule } from '@angular/core';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport { take } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\n\nlet FocusTrapManager = /*#__PURE__*/(() => {\n  class FocusTrapManager {\n    constructor() {\n      // A stack of the FocusTraps on the page. Only the FocusTrap at the\n      // top of the stack is active.\n      this._focusTrapStack = [];\n    }\n    /**\n     * Disables the FocusTrap at the top of the stack, and then pushes\n     * the new FocusTrap onto the stack.\n     */\n\n\n    register(focusTrap) {\n      // Dedupe focusTraps that register multiple times.\n      this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n      let stack = this._focusTrapStack;\n\n      if (stack.length) {\n        stack[stack.length - 1]._disable();\n      }\n\n      stack.push(focusTrap);\n\n      focusTrap._enable();\n    }\n    /**\n     * Removes the FocusTrap from the stack, and activates the\n     * FocusTrap that is the new top of the stack.\n     */\n\n\n    deregister(focusTrap) {\n      focusTrap._disable();\n\n      const stack = this._focusTrapStack;\n      const i = stack.indexOf(focusTrap);\n\n      if (i !== -1) {\n        stack.splice(i, 1);\n\n        if (stack.length) {\n          stack[stack.length - 1]._enable();\n        }\n      }\n    }\n\n  }\n\n  FocusTrapManager.ɵfac = function FocusTrapManager_Factory(t) {\n    return new (t || FocusTrapManager)();\n  };\n\n  FocusTrapManager.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapManager,\n    factory: FocusTrapManager.ɵfac,\n    providedIn: 'root'\n  });\n  return FocusTrapManager;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\n\n\nlet hasV8BreakIterator; // We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\n\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n} catch (_a) {\n  hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\n\n\nlet Platform = /*#__PURE__*/(() => {\n  class Platform {\n    constructor(_platformId) {\n      this._platformId = _platformId; // We want to use the Angular platform check because if the Document is shimmed\n      // without the navigator, the following checks will fail. This is preferred because\n      // sometimes the Document may be shimmed without the user's knowledge or intention\n\n      /** Whether the Angular application is being rendered in the browser. */\n\n      this.isBrowser = this._platformId ? isPlatformBrowser(this._platformId) : typeof document === 'object' && !!document;\n      /** Whether the current browser is Microsoft Edge. */\n\n      this.EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n      /** Whether the current rendering engine is Microsoft Trident. */\n\n      this.TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent); // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n\n      /** Whether the current rendering engine is Blink. */\n\n      this.BLINK = this.isBrowser && !!(window.chrome || hasV8BreakIterator) && typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT; // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n      // ensure that Webkit runs standalone and is not used as another engine's base.\n\n      /** Whether the current rendering engine is WebKit. */\n\n      this.WEBKIT = this.isBrowser && /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n      /** Whether the current platform is Apple iOS. */\n\n      this.IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window); // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n      // them self as Gecko-like browsers and modify the userAgent's according to that.\n      // Since we only cover one explicit Firefox case, we can simply check for Firefox\n      // instead of having an unstable check for Gecko.\n\n      /** Whether the current browser is Firefox. */\n\n      this.FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n      /** Whether the current platform is Android. */\n      // Trident on mobile adds the android platform to the userAgent to trick detections.\n\n      this.ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT; // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n      // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n      // Safari browser should also use Webkit as its layout engine.\n\n      /** Whether the current browser is Safari. */\n\n      this.SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n    }\n\n  }\n\n  Platform.ɵfac = function Platform_Factory(t) {\n    return new (t || Platform)(i0.ɵɵinject(PLATFORM_ID));\n  };\n\n  Platform.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Platform,\n    factory: Platform.ɵfac,\n    providedIn: 'root'\n  });\n  return Platform;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Configuration for the isFocusable method.\n */\n\n\nclass IsFocusableConfig {\n  constructor() {\n    /**\n     * Whether to count an element as focusable even if it is not currently visible.\n     */\n    this.ignoreVisibility = false;\n  }\n\n} // The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n\n/**\n * Utility for checking the interactivity of an element, such as whether is is focusable or\n * tabbable.\n */\n\n\nlet InteractivityChecker = /*#__PURE__*/(() => {\n  class InteractivityChecker {\n    constructor(_platform) {\n      this._platform = _platform;\n    }\n    /**\n     * Gets whether an element is disabled.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is disabled.\n     */\n\n\n    isDisabled(element) {\n      // This does not capture some cases, such as a non-form control with a disabled attribute or\n      // a form control inside of a disabled form, but should capture the most common cases.\n      return element.hasAttribute('disabled');\n    }\n    /**\n     * Gets whether an element is visible for the purposes of interactivity.\n     *\n     * This will capture states like `display: none` and `visibility: hidden`, but not things like\n     * being clipped by an `overflow: hidden` parent or being outside the viewport.\n     *\n     * @returns Whether the element is visible.\n     */\n\n\n    isVisible(element) {\n      return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n    }\n    /**\n     * Gets whether an element can be reached via Tab key.\n     * Assumes that the element has already been checked with isFocusable.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is tabbable.\n     */\n\n\n    isTabbable(element) {\n      // Nothing is tabbable on the server 😎\n      if (!this._platform.isBrowser) {\n        return false;\n      }\n\n      const frameElement = getFrameElement(getWindow(element));\n\n      if (frameElement) {\n        // Frame elements inherit their tabindex onto all child elements.\n        if (getTabIndexValue(frameElement) === -1) {\n          return false;\n        } // Browsers disable tabbing to an element inside of an invisible frame.\n\n\n        if (!this.isVisible(frameElement)) {\n          return false;\n        }\n      }\n\n      let nodeName = element.nodeName.toLowerCase();\n      let tabIndexValue = getTabIndexValue(element);\n\n      if (element.hasAttribute('contenteditable')) {\n        return tabIndexValue !== -1;\n      }\n\n      if (nodeName === 'iframe' || nodeName === 'object') {\n        // The frame or object's content may be tabbable depending on the content, but it's\n        // not possibly to reliably detect the content of the frames. We always consider such\n        // elements as non-tabbable.\n        return false;\n      } // In iOS, the browser only considers some specific elements as tabbable.\n\n\n      if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n        return false;\n      }\n\n      if (nodeName === 'audio') {\n        // Audio elements without controls enabled are never tabbable, regardless\n        // of the tabindex attribute explicitly being set.\n        if (!element.hasAttribute('controls')) {\n          return false;\n        } // Audio elements with controls are by default tabbable unless the\n        // tabindex attribute is set to `-1` explicitly.\n\n\n        return tabIndexValue !== -1;\n      }\n\n      if (nodeName === 'video') {\n        // For all video elements, if the tabindex attribute is set to `-1`, the video\n        // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n        // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n        // tabindex attribute is the source of truth here.\n        if (tabIndexValue === -1) {\n          return false;\n        } // If the tabindex is explicitly set, and not `-1` (as per check before), the\n        // video element is always tabbable (regardless of whether it has controls or not).\n\n\n        if (tabIndexValue !== null) {\n          return true;\n        } // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n        // has controls enabled. Firefox is special as videos are always tabbable regardless\n        // of whether there are controls or not.\n\n\n        return this._platform.FIREFOX || element.hasAttribute('controls');\n      }\n\n      return element.tabIndex >= 0;\n    }\n    /**\n     * Gets whether an element can be focused by the user.\n     *\n     * @param element Element to be checked.\n     * @param config The config object with options to customize this method's behavior\n     * @returns Whether the element is focusable.\n     */\n\n\n    isFocusable(element, config) {\n      // Perform checks in order of left to most expensive.\n      // Again, naive approach that does not capture many edge cases and browser quirks.\n      return isPotentiallyFocusable(element) && !this.isDisabled(element) && ((config === null || config === void 0 ? void 0 : config.ignoreVisibility) || this.isVisible(element));\n    }\n\n  }\n\n  InteractivityChecker.ɵfac = function InteractivityChecker_Factory(t) {\n    return new (t || InteractivityChecker)(i0.ɵɵinject(Platform));\n  };\n\n  InteractivityChecker.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InteractivityChecker,\n    factory: InteractivityChecker.ɵfac,\n    providedIn: 'root'\n  });\n  return InteractivityChecker;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\n\n\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch (_a) {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\n\n\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\n\n\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\n\n\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\n\n\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\n\n\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\n\n\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\n\n\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n\n  let tabIndex = element.getAttribute('tabindex'); // IE11 parses tabindex=\"\" as the value \"-32768\"\n\n  if (tabIndex == '-32768') {\n    return false;\n  }\n\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\n\n\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  } // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n\n\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\n\n\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\n\n\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\n\n\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\n\n\nfunction coerceBooleanProperty(value) {\n  return value != null && `${value}` !== 'false';\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to misalign.\n *\n * @deprecated Use `ConfigurableFocusTrap` instead.\n * @breaking-change for 11.0.0 Remove this class.\n */\n\n\nclass FocusTrap {\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._hasAttached = false; // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n\n    this.startAnchorListener = () => this.focusLastTabbableElement();\n\n    this.endAnchorListener = () => this.focusFirstTabbableElement();\n\n    this._enabled = true;\n\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Whether the focus trap is active. */\n\n\n  get enabled() {\n    return this._enabled;\n  }\n\n  set enabled(value) {\n    this._enabled = value;\n\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n\n\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n\n      if (startAnchor.parentNode) {\n        startAnchor.parentNode.removeChild(startAnchor);\n      }\n    }\n\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n\n      if (endAnchor.parentNode) {\n        endAnchor.parentNode.removeChild(endAnchor);\n      }\n    }\n\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfuly. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n\n\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n\n      this._hasAttached = true;\n    }\n\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then either focuses the first element that the\n   * user specified, or the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n\n\n  focusInitialElementWhenReady() {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement()));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n\n\n  focusFirstTabbableElementWhenReady() {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement()));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n\n\n  focusLastTabbableElementWhenReady() {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement()));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n\n\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    let markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n\n    for (let i = 0; i < markers.length; i++) {\n      // @breaking-change 8.0.0\n      if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n      } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n      }\n    }\n\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n\n\n  focusInitialElement() {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if (redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      } // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n\n\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n\n        focusableChild === null || focusableChild === void 0 ? void 0 : focusableChild.focus();\n        return !!focusableChild;\n      }\n\n      redirectToElement.focus();\n      return true;\n    }\n\n    return this.focusFirstTabbableElement();\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n\n\n  focusFirstTabbableElement() {\n    const redirectToElement = this._getRegionBoundary('start');\n\n    if (redirectToElement) {\n      redirectToElement.focus();\n    }\n\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n\n\n  focusLastTabbableElement() {\n    const redirectToElement = this._getRegionBoundary('end');\n\n    if (redirectToElement) {\n      redirectToElement.focus();\n    }\n\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n\n\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n\n\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    } // Iterate in DOM order. Note that IE doesn't have `children` for SVG so we fall\n    // back to `childNodes` which includes text nodes, comments etc.\n\n\n    let children = root.children || root.childNodes;\n\n    for (let i = 0; i < children.length; i++) {\n      let tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n\n\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    } // Iterate in reverse DOM order.\n\n\n    let children = root.children || root.childNodes;\n\n    for (let i = children.length - 1; i >= 0; i--) {\n      let tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n  /** Creates an anchor element. */\n\n\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n\n\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n\n\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n\n\n  _executeOnStable(fn) {\n    if (this._ngZone.isStable) {\n      fn();\n    } else {\n      this._ngZone.onStable.pipe(take(1)).subscribe(fn);\n    }\n  }\n\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n * @deprecated Use `ConfigurableFocusTrapFactory` instead.\n * @breaking-change for 11.0.0 Remove this class.\n */\n\n\nlet FocusTrapFactory = /*#__PURE__*/(() => {\n  class FocusTrapFactory {\n    constructor(_checker, _ngZone, _document) {\n      this._checker = _checker;\n      this._ngZone = _ngZone;\n      this._document = _document;\n    }\n    /**\n     * Creates a focus-trapped region around the given element.\n     * @param element The element around which focus will be trapped.\n     * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n     *     manually by the user.\n     * @returns The created focus trap instance.\n     */\n\n\n    create(element, deferCaptureElements = false) {\n      return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements);\n    }\n\n  }\n\n  FocusTrapFactory.ɵfac = function FocusTrapFactory_Factory(t) {\n    return new (t || FocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n  };\n\n  FocusTrapFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapFactory,\n    factory: FocusTrapFactory.ɵfac,\n    providedIn: 'root'\n  });\n  return FocusTrapFactory;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Directive for trapping focus within a region. */\n\n\nlet FocusTrapDirective = /*#__PURE__*/(() => {\n  class FocusTrapDirective {\n    constructor(_elementRef, _focusTrapFactory, _document) {\n      this._elementRef = _elementRef;\n      this._focusTrapFactory = _focusTrapFactory;\n      /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n\n      this._previouslyFocusedElement = null;\n      this._autoCapture = false;\n      this._document = _document;\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n    /** Whether the focus trap is active. */\n\n\n    get enabled() {\n      return this.focusTrap.enabled;\n    }\n\n    set enabled(value) {\n      this.focusTrap.enabled = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the directive should automatically move focus into the trapped region upon\n     * initialization and return focus to the previous activeElement upon destruction.\n     */\n\n\n    get autoCapture() {\n      return this._autoCapture;\n    }\n\n    set autoCapture(value) {\n      this._autoCapture = coerceBooleanProperty(value);\n    }\n\n    ngOnDestroy() {\n      this.focusTrap.destroy(); // If we stored a previously focused element when using autoCapture, return focus to that\n      // element now that the trapped region is being destroyed.\n\n      if (this._previouslyFocusedElement) {\n        this._previouslyFocusedElement.focus();\n\n        this._previouslyFocusedElement = null;\n      }\n    }\n\n    ngAfterContentInit() {\n      this.focusTrap.attachAnchors();\n\n      if (this.autoCapture) {\n        this._captureFocus();\n      }\n    }\n\n    ngDoCheck() {\n      if (!this.focusTrap.hasAttached()) {\n        this.focusTrap.attachAnchors();\n      }\n    }\n\n    ngOnChanges(changes) {\n      const autoCaptureChange = changes['autoCapture'];\n\n      if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap.hasAttached()) {\n        this._captureFocus();\n      }\n    }\n\n    _captureFocus() {\n      this._previouslyFocusedElement = this._document.activeElement;\n      this.focusTrap.focusInitialElementWhenReady();\n    }\n\n  }\n\n  FocusTrapDirective.ɵfac = function FocusTrapDirective_Factory(t) {\n    return new (t || FocusTrapDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT));\n  };\n\n  FocusTrapDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FocusTrapDirective,\n    selectors: [[\"\", \"focusTrap\", \"\"]],\n    inputs: {\n      enabled: [\"cdkTrapFocus\", \"enabled\"],\n      autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\"]\n    },\n    exportAs: [\"focusTrap\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return FocusTrapDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet FocusTrapModule = /*#__PURE__*/(() => {\n  class FocusTrapModule {\n    static forRoot() {\n      return {\n        ngModule: FocusTrapModule,\n        providers: [FocusTrapManager, Platform, InteractivityChecker]\n      };\n    }\n\n  }\n\n  FocusTrapModule.ɵfac = function FocusTrapModule_Factory(t) {\n    return new (t || FocusTrapModule)();\n  };\n\n  FocusTrapModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FocusTrapModule\n  });\n  FocusTrapModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule]]\n  });\n  return FocusTrapModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { FocusTrap, FocusTrapDirective, FocusTrapModule }; //# sourceMappingURL=ngx-bootstrap-focus-trap.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}