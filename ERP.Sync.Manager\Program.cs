﻿using Brac.LMS.DB;
using Brac.LMS.Models;

using Dapper;

using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;

using Newtonsoft.Json;

using RestSharp;

using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace ERP.Sync.Manager
{
    class Program
    {
        private static string logPath;
        private static string baseUrl;
        private static string apiResource;
        private static string apiToken;
        private static string apiVendor;
        static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine($"Sync manager Running...");
                logPath = ConfigurationManager.AppSettings["LogPath"].ToString();
                baseUrl = ConfigurationManager.AppSettings["APIBaseUrl"].ToString();
                apiResource = ConfigurationManager.AppSettings["APIResource"].ToString();
                apiToken = ConfigurationManager.AppSettings["APIToken"].ToString();
                apiVendor = ConfigurationManager.AppSettings["APIVendor"].ToString();

                if (!Directory.Exists(logPath)) Directory.CreateDirectory(logPath);
                using (var context = new ApplicationDbContext())
                {
                    var startDate = await context.Configurations.Select(x => x.LastERPSyncDate).FirstOrDefaultAsync();
                    if (startDate.HasValue) startDate = startDate < DateTime.Today.AddDays(-2) ? startDate.Value.AddDays(1) : startDate;
                    else startDate = DateTime.Today.AddDays(-1);

                    // For Live Syncronization
                    if (await context.Trainees.AnyAsync())
                        await GetAllEmployeesAsync(context, startDate.Value, DateTime.Today.AddDays(-1));
                    else
                        await GetAllEmployeesAsync(context, DateTime.Parse("01-Jan-2010"), DateTime.Today.AddDays(-1));

                    //For Instant/Force Synchronization 
                    //await GetAllEmployeesAsync(context, DateTime.Parse("01-Jan-2010"), DateTime.Today.AddDays(-1));
                    //await GetAllEmployeesAsyncTemp(context);
                    //await GetAllEmployeesAsyncTemp3(context, DateTime.Parse("01-Jan-2010"), DateTime.Today.AddYears(5));
                }
                Write("Completed");
            }
            catch (Exception ex)
            {
                Write("Error Main: " + ex.Message);
            }
            //Console.ReadKey();
        }

        static async Task GetAllEmployeesAsync(ApplicationDbContext context, DateTime startDate, DateTime endDate)
        {
            var client = new RestClient(baseUrl);

            try
            {
                RestRequest request;
                IRestResponse<ERPAPIResponse> response;
                int totalPages = 0, pageNo = 0, noOfRows = 100, saved, updated;

                var units = await context.Units.ToListAsync();
                var departments = await context.Departments.ToListAsync();
                var divisions = await context.Divisions.ToListAsync();
                var subUnits = await context.SubUnits.ToListAsync();

                Trainee trainee = null;
                Unit unit = null;
                Department department = null;
                Division division = null;
                SubUnit subUnit = null;
                ApplicationUser user = null;
                IdentityResult result = null;

                var userManager = new ApplicationUserManager(new UserStore<ApplicationUser, ApplicationRole, string, IdentityUserLogin, IdentityUserRole, IdentityUserClaim>(context));

                userManager.MaxFailedAccessAttemptsBeforeLockout = 3;
                userManager.UserLockoutEnabledByDefault = true;
                userManager.DefaultAccountLockoutTimeSpan = TimeSpan.FromMinutes(5);

                userManager.PasswordValidator = new PasswordValidator
                {
                    RequiredLength = 12,
                    RequireNonLetterOrDigit = true,
                    RequireDigit = false,
                    RequireLowercase = false,
                    RequireUppercase = false,
                };

                string pin;

                do
                {
                    saved = 0;
                    updated = 0;
                    request = new RestRequest(apiResource, Method.POST, DataFormat.Json);
                    request.AddHeader("Authorization", $"Bearer {apiToken}");

                    request.AddJsonBody(new
                    {
                        Vendor = apiVendor,
                        FromDate = startDate.ToString("yyyyMMdd"),
                        ToDate = endDate.ToString("yyyyMMdd"),
                        PageNo = pageNo,
                        NoOfRows = noOfRows
                    });
                    response = await client.ExecuteAsync<ERPAPIResponse>(request);
                    if (!response.IsSuccessful)
                    {
                        Console.WriteLine(response.ErrorMessage);
                        Write($"Error on ERPSyncManager: {JsonConvert.SerializeObject(response)}");
                        //Console.WriteLine("Press any key to exit...");
                        //Console.ReadKey();
                        //return;
                    }

                    Console.WriteLine($"Data Length: {response.Data.totalRowCount}");
                    Console.WriteLine($"Employee Count: {response.Data.data.Count}");
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        if (response.Data.data != null && response.Data.data.Any())
                        {
                            totalPages = (int)Math.Ceiling(response.Data.totalRowCount / noOfRows);

                            foreach (var item in response.Data.data)
                            {
                                try
                                {
                                    pin = Convert.ToString(item.employeE_ID);
                                    trainee = await context.Trainees.FirstOrDefaultAsync(x => x.PIN == pin);
                                    if (trainee == null) trainee = new Trainee
                                    {
                                        Active = true,
                                        PIN = item.employeE_ID
                                    };

                                    unit = units.FirstOrDefault(x => x.Name == item.unit);
                                    if (unit == null && !string.IsNullOrEmpty(item.unit))
                                    {
                                        unit = new Unit
                                        {
                                            Name = item.unit,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow.ToLocalTime()
                                        };
                                        context.Units.Add(unit);
                                        await context.SaveChangesAsync();
                                        units.Add(unit);
                                    }

                                    subUnit = subUnits.FirstOrDefault(x => x.Name == item.suB_UNIT);
                                    if (subUnit == null && !string.IsNullOrEmpty(item.suB_UNIT))
                                    {
                                        subUnit = new SubUnit
                                        {
                                            Name = item.suB_UNIT,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow.ToLocalTime()
                                        };
                                        context.SubUnits.Add(subUnit);
                                        await context.SaveChangesAsync();
                                        subUnits.Add(subUnit);
                                    }

                                    division = divisions.FirstOrDefault(x => x.Name == item.division);
                                    if (division == null && !string.IsNullOrEmpty(item.division))
                                    {
                                        division = new Division
                                        {
                                            Name = item.division,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow.ToLocalTime()
                                        };
                                        context.Divisions.Add(division);
                                        await context.SaveChangesAsync();
                                        divisions.Add(division);
                                    }

                                    department = departments.FirstOrDefault(x => x.Name == item.department);
                                    if (department == null && !string.IsNullOrEmpty(item.department))
                                    {
                                        department = new Department
                                        {
                                            Name = item.department,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow.ToLocalTime()
                                        };
                                        context.Departments.Add(department);
                                        await context.SaveChangesAsync();
                                        departments.Add(department);
                                    }

                                    trainee.Name = item.employeE_NAME;
                                    trainee.Email = item.emaiL_ADDRESS;
                                    trainee.PhoneNo = !string.IsNullOrEmpty(item.phonE_NUMBER) ? item.phonE_NUMBER.Trim() : null;
                                    trainee.DateOfJoining = item.datE_OF_JOINING;
                                    trainee.Position = item.position;
                                    trainee.Grade = item.grade;
                                    trainee.DivisionId = division?.Id;
                                    trainee.DepartmentId = department?.Id;
                                    trainee.UnitId = unit?.Id;
                                    trainee.SubUnitId = subUnit?.Id;
                                    trainee.LineManagerPIN = item.linE_MGR_PIN;
                                    trainee.LineManagerName = item.linE_MGR_NAME;
                                    trainee.WorkLocation = item.worK_LOCATION;
                                    trainee.Gender = (Gender)Enum.Parse(typeof(Gender), item.gender);
                                    trainee.EmployeeType = item.employmenT_TYPE;

                                    user = await userManager.FindByNameAsync(trainee.PIN);
                                    if (user == null) user = new ApplicationUser { UserName = trainee.PIN };

                                    user.PhoneNumber = trainee.PhoneNo;
                                    user.Email = trainee.Email ?? trainee.PIN + "@bracbank.com";
                                    user.FirstName = trainee.Name;
                                    user.Active = trainee.Active;
                                    user.UserType = UserType.Trainee;
                                    user.Gender = trainee.Gender;
                                    user.LoginType = LoginType.External;
                                    user.LockoutEnabled = true;
                                    user.LastPasswordChanged = DateTime.Now.ToLocalTime();

                                    using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                                    {
                                        if (trainee.Id == Guid.Empty)
                                        {

                                            result = await userManager.CreateAsync(user, "ALO@*********");
                                            if (!result.Succeeded)
                                            {
                                                Write($"Error on user creation | PIN: {trainee.PIN} | Phone: {trainee.PhoneNo} | {JsonConvert.SerializeObject(result)}");
                                                throw new Exception("Error on user creation: n\\" + string.Join("n\\", result.Errors));
                                            }

                                            result = await userManager.AddToRoleAsync(user.Id, "Trainee");
                                            if (!result.Succeeded)
                                            {
                                                Write($"Error on user role assigning | PIN: {trainee.PIN} | UserID: {user.Id} | {JsonConvert.SerializeObject(result)}");
                                                throw new Exception("Error on user role assigning: n\\" + string.Join("n\\", result.Errors));
                                            }

                                            trainee.UserId = user.Id;

                                            trainee.Id = Guid.NewGuid();
                                            trainee.CreatorId = "ERP-Sync";
                                            trainee.CreatedDate = DateTime.UtcNow.ToLocalTime();
                                            context.Trainees.Add(trainee);
                                            await context.SaveChangesAsync();
                                            saved++;
                                        }
                                        else
                                        {
                                            result = await userManager.UpdateAsync(user);
                                            if (!result.Succeeded)
                                            {
                                                Write($"Error on user modification | UserName: {user.UserName} | UserID: {user.Id} | {JsonConvert.SerializeObject(result)}");
                                                throw new Exception("Error on user modification: n\\" + string.Join("n\\", result.Errors));
                                            }

                                            trainee.ModifierId = "ERP-Sync";
                                            trainee.ModifiedDate = DateTime.UtcNow.ToLocalTime();
                                            context.Entry(trainee).State = EntityState.Modified;
                                            await context.SaveChangesAsync();
                                            updated++;
                                        }
                                        scope.Complete();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Write("Error on employee store/update: " + ex.Message);
                                    Write(JsonConvert.SerializeObject(ex));
                                    //Console.WriteLine("Press any key to exit...");
                                    //Console.ReadKey();
                                    //return;
                                }
                            }
                        }
                    }
                    else
                    {
                        Write($"Request not success | {JsonConvert.SerializeObject(response)}");
                        throw new Exception(!string.IsNullOrEmpty(response.ErrorMessage) ? response.ErrorMessage : response.StatusDescription);
                    }
                    Write($"{DateTime.Now:hh:mm:ss} => From {startDate.ToString("dd MMM yyyy")} to {endDate.ToString("dd MMM yyyy")} : Page {pageNo} DONE. Saved: {saved} & Updated: {updated}");
                        pageNo++;
                } while (pageNo < totalPages);

                await context.Database.ExecuteSqlCommandAsync("UPDATE Configuration SET LastERPSyncDate=@DATE", new SqlParameter("@DATE", endDate));
                await context.SaveChangesAsync();
            }
            catch (Exception error)
            {
                Write("Error on all employee store: " + error.Message);
                Write(JsonConvert.SerializeObject(error));

                //Console.WriteLine("Press any key to exit...");
                //Console.ReadKey();
                //return;
            }
        }

        static async Task GetAllEmployeesAsyncTemp(ApplicationDbContext context)
        {
            var client = new RestClient(baseUrl);

            try
            {
                RestRequest request;
                IRestResponse<ERPAPIResponse> response;
                int totalPages = 0, noOfRows = 100, saved, updated;

                var units = await context.Units.ToListAsync();
                var departments = await context.Departments.ToListAsync();
                var divisions = await context.Divisions.ToListAsync();
                var subUnits = await context.SubUnits.ToListAsync();

                DateTime startDate = DateTime.Parse("01-Jan-2010"), endDate = DateTime.Parse("16-Feb-2022");

                var pageNos = new int[] { 65, 67, 69, 72 };


                Trainee trainee = null;
                Unit unit = null;
                Department department = null;
                Division division = null;
                SubUnit subUnit = null;
                ApplicationUser user = null;
                IdentityResult result = null;

                var userManager = new ApplicationUserManager(new UserStore<ApplicationUser, ApplicationRole, string, IdentityUserLogin, IdentityUserRole, IdentityUserClaim>(context));

                foreach (var pageNo in pageNos)
                {
                    saved = 0;
                    updated = 0;
                    request = new RestRequest(apiResource, Method.POST, DataFormat.Json);
                    request.AddHeader("Authorization", $"Bearer {apiToken}");

                    request.AddJsonBody(new
                    {
                        Vendor = apiVendor,
                        FromDate = startDate.ToString("yyyyMMdd"),
                        ToDate = endDate.ToString("yyyyMMdd"),
                        PageNo = pageNo,
                        NoOfRows = noOfRows
                    });

                    response = await client.ExecuteAsync<ERPAPIResponse>(request);
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        if (response.Data.data != null && response.Data.data.Any())
                        {
                            totalPages = (int)Math.Ceiling(response.Data.totalRowCount / noOfRows);

                            userManager.MaxFailedAccessAttemptsBeforeLockout = 3;
                            userManager.UserLockoutEnabledByDefault = true;
                            userManager.DefaultAccountLockoutTimeSpan = TimeSpan.FromMinutes(5);

                            userManager.PasswordValidator = new PasswordValidator
                            {
                                RequiredLength = 4,
                                RequireNonLetterOrDigit = false,
                                RequireDigit = false,
                                RequireLowercase = false,
                                RequireUppercase = false,
                            };

                            string pin;


                            foreach (var item in response.Data.data)
                            {
                                try
                                {
                                    pin = Convert.ToString(item.employeE_ID);
                                    trainee = await context.Trainees.FirstOrDefaultAsync(x => x.PIN == pin);
                                    if (trainee == null) trainee = new Trainee
                                    {
                                        Active = true,
                                        PIN = item.employeE_ID
                                    };

                                    unit = units.FirstOrDefault(x => x.Name == item.unit);
                                    if (unit == null && !string.IsNullOrEmpty(item.unit))
                                    {
                                        unit = new Unit
                                        {
                                            Name = item.unit,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow
                                        };
                                        context.Units.Add(unit);
                                        await context.SaveChangesAsync();
                                        units.Add(unit);
                                    }

                                    subUnit = subUnits.FirstOrDefault(x => x.Name == item.suB_UNIT);
                                    if (subUnit == null && !string.IsNullOrEmpty(item.suB_UNIT))
                                    {
                                        subUnit = new SubUnit
                                        {
                                            Name = item.suB_UNIT,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow
                                        };
                                        context.SubUnits.Add(subUnit);
                                        await context.SaveChangesAsync();
                                        subUnits.Add(subUnit);
                                    }

                                    division = divisions.FirstOrDefault(x => x.Name == item.division);
                                    if (division == null && !string.IsNullOrEmpty(item.division))
                                    {
                                        division = new Division
                                        {
                                            Name = item.division,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow
                                        };
                                        context.Divisions.Add(division);
                                        await context.SaveChangesAsync();
                                        divisions.Add(division);
                                    }

                                    department = departments.FirstOrDefault(x => x.Name == item.department);
                                    if (department == null && !string.IsNullOrEmpty(item.department))
                                    {
                                        department = new Department
                                        {
                                            Name = item.department,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow
                                        };
                                        context.Departments.Add(department);
                                        await context.SaveChangesAsync();
                                        departments.Add(department);
                                    }

                                    trainee.Name = item.employeE_NAME;
                                    trainee.Email = item.emaiL_ADDRESS;
                                    trainee.PhoneNo = !string.IsNullOrEmpty(item.phonE_NUMBER) ? item.phonE_NUMBER.Trim() : null;
                                    trainee.DateOfJoining = item.datE_OF_JOINING;
                                    trainee.Position = item.position;
                                    trainee.Grade = item.grade;
                                    trainee.DivisionId = division?.Id;
                                    trainee.DepartmentId = department?.Id;
                                    trainee.UnitId = unit?.Id;
                                    trainee.SubUnitId = subUnit?.Id;
                                    trainee.LineManagerPIN = item.linE_MGR_PIN;
                                    trainee.LineManagerName = item.linE_MGR_NAME;
                                    trainee.WorkLocation = item.worK_LOCATION;
                                    trainee.Gender = (Gender)Enum.Parse(typeof(Gender), item.gender);
                                    trainee.EmployeeType = item.employmenT_TYPE;

                                    user = await userManager.FindByNameAsync(trainee.PIN);
                                    if (user == null) user = new ApplicationUser { UserName = trainee.PIN };

                                    user.PhoneNumber = trainee.PhoneNo;
                                    user.Email = trainee.Email ?? trainee.PIN + "@bracbank.com";
                                    user.FirstName = trainee.Name;
                                    user.Active = trainee.Active;
                                    user.UserType = UserType.Trainee;
                                    user.Gender = trainee.Gender;
                                    user.LoginType = LoginType.External;
                                    user.LockoutEnabled = true;

                                    using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                                    {
                                        if (trainee.Id == Guid.Empty)
                                        {

                                            result = await userManager.CreateAsync(user, trainee.PhoneNo != null && trainee.PhoneNo.Length > 6 ? trainee.PhoneNo : "BRACeLearning2021");
                                            if (!result.Succeeded)
                                                throw new Exception("Error on user creation: n\\" + string.Join("n\\", result.Errors));

                                            result = await userManager.AddToRoleAsync(user.Id, "Trainee");
                                            if (!result.Succeeded)
                                            {
                                                throw new Exception("Error on user creation: n\\" + string.Join("n\\", result.Errors));
                                            }

                                            trainee.UserId = user.Id;

                                            trainee.Id = Guid.NewGuid();
                                            trainee.CreatorId = "ERP-Sync";
                                            trainee.CreatedDate = DateTime.UtcNow;
                                            context.Trainees.Add(trainee);
                                            await context.SaveChangesAsync();
                                            saved++;
                                        }
                                        else
                                        {
                                            result = await userManager.UpdateAsync(user);
                                            if (!result.Succeeded)
                                                throw new Exception("Error on user modification: n\\" + string.Join("n\\", result.Errors));

                                            trainee.ModifierId = "ERP-Sync";
                                            trainee.ModifiedDate = DateTime.UtcNow;
                                            context.Entry(trainee).State = EntityState.Modified;
                                            await context.SaveChangesAsync();
                                            updated++;
                                        }
                                        scope.Complete();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Write("Error: " + ex.Message);
                                }
                            }
                        }
                    }
                    else throw new Exception(response.ErrorMessage);
                    Write($"{DateTime.Now:hh:mm:ss} => Page {pageNo} DONE. Saved: {saved} & Updated: {updated}");
                }
            }
            catch (Exception error)
            {
                Write("Error: " + error.Message);
            }
        }

        static async Task GetAllEmployeesAsyncTemp2(ApplicationDbContext context, DateTime startDate, DateTime endDate)
        {
            var client = new RestClient(baseUrl);

            try
            {
                RestRequest request;
                IRestResponse<ERPAPIResponse> response;
                int totalPages = 0, pageNo = 0, noOfRows = 100, saved, updated;

                var units = await context.Units.ToListAsync();
                var departments = await context.Departments.ToListAsync();
                var divisions = await context.Divisions.ToListAsync();
                var subUnits = await context.SubUnits.ToListAsync();


                Trainee trainee = null;
                Unit unit = null;
                Department department = null;
                Division division = null;
                SubUnit subUnit = null;
                ApplicationUser user = null;
                IdentityResult result = null;

                var userManager = new ApplicationUserManager(new UserStore<ApplicationUser, ApplicationRole, string, IdentityUserLogin, IdentityUserRole, IdentityUserClaim>(context));

                do
                {
                    saved = 0;
                    updated = 0;
                    request = new RestRequest(apiResource, Method.POST, DataFormat.Json);
                    request.AddHeader("Authorization", $"Bearer {apiToken}");

                    request.AddJsonBody(new
                    {
                        Vendor = apiVendor,
                        FromDate = startDate.ToString("yyyyMMdd"),
                        ToDate = endDate.ToString("yyyyMMdd"),
                        PageNo = pageNo,
                        NoOfRows = noOfRows
                    });

                    response = await client.ExecuteAsync<ERPAPIResponse>(request);
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        if (response.Data.data != null && response.Data.data.Any())
                        {
                            totalPages = (int)Math.Ceiling(response.Data.totalRowCount / noOfRows);

                            userManager.MaxFailedAccessAttemptsBeforeLockout = 3;
                            userManager.UserLockoutEnabledByDefault = true;
                            userManager.DefaultAccountLockoutTimeSpan = TimeSpan.FromMinutes(5);

                            userManager.PasswordValidator = new PasswordValidator
                            {
                                RequiredLength = 4,
                                RequireNonLetterOrDigit = false,
                                RequireDigit = false,
                                RequireLowercase = false,
                                RequireUppercase = false,
                            };

                            string pin;


                            foreach (var item in response.Data.data)
                            {
                                try
                                {
                                    pin = Convert.ToString(item.employeE_ID);
                                    trainee = await context.Trainees.FirstOrDefaultAsync(x => x.PIN == pin);
                                    if (trainee != null) continue;

                                    trainee = new Trainee
                                    {
                                        Active = true,
                                        PIN = item.employeE_ID
                                    };

                                    unit = units.FirstOrDefault(x => x.Name == item.unit);
                                    if (unit == null && !string.IsNullOrEmpty(item.unit))
                                    {
                                        unit = new Unit
                                        {
                                            Name = item.unit,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow
                                        };
                                        context.Units.Add(unit);
                                        await context.SaveChangesAsync();
                                        units.Add(unit);
                                    }

                                    subUnit = subUnits.FirstOrDefault(x => x.Name == item.suB_UNIT);
                                    if (subUnit == null && !string.IsNullOrEmpty(item.suB_UNIT))
                                    {
                                        subUnit = new SubUnit
                                        {
                                            Name = item.suB_UNIT,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow
                                        };
                                        context.SubUnits.Add(subUnit);
                                        await context.SaveChangesAsync();
                                        subUnits.Add(subUnit);
                                    }

                                    division = divisions.FirstOrDefault(x => x.Name == item.division);
                                    if (division == null && !string.IsNullOrEmpty(item.division))
                                    {
                                        division = new Division
                                        {
                                            Name = item.division,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow
                                        };
                                        context.Divisions.Add(division);
                                        await context.SaveChangesAsync();
                                        divisions.Add(division);
                                    }

                                    department = departments.FirstOrDefault(x => x.Name == item.department);
                                    if (department == null && !string.IsNullOrEmpty(item.department))
                                    {
                                        department = new Department
                                        {
                                            Name = item.department,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow
                                        };
                                        context.Departments.Add(department);
                                        await context.SaveChangesAsync();
                                        departments.Add(department);
                                    }

                                    trainee.Name = item.employeE_NAME;
                                    trainee.Email = item.emaiL_ADDRESS;
                                    trainee.PhoneNo = !string.IsNullOrEmpty(item.phonE_NUMBER) ? item.phonE_NUMBER.Trim() : null;
                                    trainee.DateOfJoining = item.datE_OF_JOINING;
                                    trainee.Position = item.position;
                                    trainee.Grade = item.grade;
                                    trainee.DivisionId = division?.Id;
                                    trainee.DepartmentId = department?.Id;
                                    trainee.UnitId = unit?.Id;
                                    trainee.SubUnitId = subUnit?.Id;
                                    trainee.LineManagerPIN = item.linE_MGR_PIN;
                                    trainee.LineManagerName = item.linE_MGR_NAME;
                                    trainee.WorkLocation = item.worK_LOCATION;
                                    trainee.Gender = (Gender)Enum.Parse(typeof(Gender), item.gender);
                                    trainee.EmployeeType = item.employmenT_TYPE;

                                    user = await userManager.FindByNameAsync(trainee.PIN);
                                    if (user == null) user = new ApplicationUser { UserName = trainee.PIN };

                                    user.PhoneNumber = trainee.PhoneNo;
                                    user.Email = trainee.Email ?? trainee.PIN + "@bracbank.com";
                                    user.FirstName = trainee.Name;
                                    user.Active = trainee.Active;
                                    user.UserType = UserType.Trainee;
                                    user.Gender = trainee.Gender;
                                    user.LoginType = LoginType.External;
                                    user.LockoutEnabled = true;

                                    using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                                    {
                                        if (trainee.Id == Guid.Empty)
                                        {

                                            result = await userManager.CreateAsync(user, trainee.PhoneNo != null && trainee.PhoneNo.Length > 6 ? trainee.PhoneNo : "BRACeLearning2021");
                                            if (!result.Succeeded)
                                                throw new Exception("Error on user creation: n\\" + string.Join("n\\", result.Errors));

                                            result = await userManager.AddToRoleAsync(user.Id, "Trainee");
                                            if (!result.Succeeded)
                                            {
                                                throw new Exception("Error on user creation: n\\" + string.Join("n\\", result.Errors));
                                            }

                                            trainee.UserId = user.Id;

                                            trainee.Id = Guid.NewGuid();
                                            trainee.CreatorId = "ERP-Sync";
                                            trainee.CreatedDate = DateTime.UtcNow;
                                            context.Trainees.Add(trainee);
                                            await context.SaveChangesAsync();
                                            saved++;
                                        }
                                        else
                                        {
                                            result = await userManager.UpdateAsync(user);
                                            if (!result.Succeeded)
                                                throw new Exception("Error on user modification: n\\" + string.Join("n\\", result.Errors));

                                            trainee.ModifierId = "ERP-Sync";
                                            trainee.ModifiedDate = DateTime.UtcNow;
                                            context.Entry(trainee).State = EntityState.Modified;
                                            await context.SaveChangesAsync();
                                            updated++;
                                        }
                                        scope.Complete();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Write("Error: " + ex.Message);
                                }
                            }
                        }
                    }
                    else throw new Exception(response.ErrorMessage);
                    Write($"{DateTime.Now:hh:mm:ss} => Page {pageNo} DONE. Saved: {saved} & Updated: {updated}");
                    pageNo++;
                } while (pageNo < totalPages);

            }
            catch (Exception error)
            {
                Write("Error: " + error.Message);
            }
        }

        static async Task GetAllEmployeesAsyncTemp3(ApplicationDbContext context, DateTime startDate, DateTime endDate)
        {
            var client = new RestClient(baseUrl);

            try
            {
                RestRequest request;
                IRestResponse<ERPAPIResponse> response;
                int totalPages = 0, pageNo = 0, noOfRows = 100, saved, updated;

                var units = await context.Units.ToListAsync();
                var departments = await context.Departments.ToListAsync();
                var divisions = await context.Divisions.ToListAsync();
                var subUnits = await context.SubUnits.ToListAsync();


                Trainee trainee = null;
                Unit unit = null;
                Department department = null;
                Division division = null;
                SubUnit subUnit = null;
                ApplicationUser user = null;
                IdentityResult result = null;

                var userManager = new ApplicationUserManager(new UserStore<ApplicationUser, ApplicationRole, string, IdentityUserLogin, IdentityUserRole, IdentityUserClaim>(context));

                userManager.MaxFailedAccessAttemptsBeforeLockout = 3;
                userManager.UserLockoutEnabledByDefault = true;
                userManager.DefaultAccountLockoutTimeSpan = TimeSpan.FromMinutes(5);

                userManager.PasswordValidator = new PasswordValidator
                {
                    RequiredLength = 4,
                    RequireNonLetterOrDigit = false,
                    RequireDigit = false,
                    RequireLowercase = false,
                    RequireUppercase = false,
                };

                string pin;

                do
                {
                    saved = 0;
                    updated = 0;
                    request = new RestRequest(apiResource, Method.POST, DataFormat.Json);
                    request.AddHeader("Authorization", $"Bearer {apiToken}");

                    request.AddJsonBody(new
                    {
                        Vendor = apiVendor,
                        FromDate = startDate.ToString("yyyyMMdd"),
                        ToDate = endDate.ToString("yyyyMMdd"),
                        PageNo = pageNo,
                        NoOfRows = noOfRows
                    });

                    response = await client.ExecuteAsync<ERPAPIResponse>(request);
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        if (response.Data.data != null && response.Data.data.Any())
                        {
                            totalPages = (int)Math.Ceiling(response.Data.totalRowCount / noOfRows);

                            foreach (var item in response.Data.data)
                            {
                                try
                                {
                                    pin = Convert.ToString(item.employeE_ID);
                                    trainee = await context.Trainees.FirstOrDefaultAsync(x => x.PIN == pin);
                                    if (trainee != null) continue;

                                    trainee = new Trainee
                                    {
                                        Active = true,
                                        PIN = item.employeE_ID
                                    };

                                    unit = units.FirstOrDefault(x => x.Name == item.unit);
                                    if (unit == null && !string.IsNullOrEmpty(item.unit))
                                    {
                                        unit = new Unit
                                        {
                                            Name = item.unit,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow
                                        };
                                        context.Units.Add(unit);
                                        await context.SaveChangesAsync();
                                        units.Add(unit);
                                    }

                                    subUnit = subUnits.FirstOrDefault(x => x.Name == item.suB_UNIT);
                                    if (subUnit == null && !string.IsNullOrEmpty(item.suB_UNIT))
                                    {
                                        subUnit = new SubUnit
                                        {
                                            Name = item.suB_UNIT,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow
                                        };
                                        context.SubUnits.Add(subUnit);
                                        await context.SaveChangesAsync();
                                        subUnits.Add(subUnit);
                                    }

                                    division = divisions.FirstOrDefault(x => x.Name == item.division);
                                    if (division == null && !string.IsNullOrEmpty(item.division))
                                    {
                                        division = new Division
                                        {
                                            Name = item.division,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow
                                        };
                                        context.Divisions.Add(division);
                                        await context.SaveChangesAsync();
                                        divisions.Add(division);
                                    }

                                    department = departments.FirstOrDefault(x => x.Name == item.department);
                                    if (department == null && !string.IsNullOrEmpty(item.department))
                                    {
                                        department = new Department
                                        {
                                            Name = item.department,
                                            Active = true,
                                            CreatedDate = DateTime.UtcNow
                                        };
                                        context.Departments.Add(department);
                                        await context.SaveChangesAsync();
                                        departments.Add(department);
                                    }

                                    trainee.Name = item.employeE_NAME;
                                    trainee.Email = item.emaiL_ADDRESS;
                                    trainee.PhoneNo = !string.IsNullOrEmpty(item.phonE_NUMBER) ? item.phonE_NUMBER.Trim() : null;
                                    trainee.DateOfJoining = item.datE_OF_JOINING;
                                    trainee.Position = item.position;
                                    trainee.Grade = item.grade;
                                    trainee.DivisionId = division?.Id;
                                    trainee.DepartmentId = department?.Id;
                                    trainee.UnitId = unit?.Id;
                                    trainee.SubUnitId = subUnit?.Id;
                                    trainee.LineManagerPIN = item.linE_MGR_PIN;
                                    trainee.LineManagerName = item.linE_MGR_NAME;
                                    trainee.WorkLocation = item.worK_LOCATION;
                                    trainee.Gender = (Gender)Enum.Parse(typeof(Gender), item.gender);
                                    trainee.EmployeeType = item.employmenT_TYPE;

                                    user = await userManager.FindByNameAsync(trainee.PIN);
                                    if (user == null) user = new ApplicationUser { UserName = trainee.PIN };

                                    user.PhoneNumber = trainee.PhoneNo;
                                    user.Email = trainee.Email ?? trainee.PIN + "@bracbank.com";
                                    user.FirstName = trainee.Name;
                                    user.Active = trainee.Active;
                                    user.UserType = UserType.Trainee;
                                    user.Gender = trainee.Gender;
                                    user.LoginType = LoginType.External;
                                    user.LockoutEnabled = true;

                                    using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                                    {
                                        result = await userManager.CreateAsync(user, trainee.PhoneNo != null && trainee.PhoneNo.Length > 6 ? trainee.PhoneNo : "BRACeLearning2021");
                                        if (!result.Succeeded)
                                            throw new Exception("Error on user creation: n\\" + string.Join("n\\", result.Errors));

                                        result = await userManager.AddToRoleAsync(user.Id, "Trainee");
                                        if (!result.Succeeded)
                                        {
                                            throw new Exception("Error on user creation: n\\" + string.Join("n\\", result.Errors));
                                        }

                                        trainee.UserId = user.Id;

                                        trainee.Id = Guid.NewGuid();
                                        trainee.CreatorId = "ERP-Sync";
                                        trainee.CreatedDate = DateTime.UtcNow;
                                        context.Trainees.Add(trainee);
                                        await context.SaveChangesAsync();
                                        saved++;
                                        scope.Complete();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Write("Error on employee store/update: " + ex.Message);
                                }
                            }
                        }
                    }
                    else
                    {
                        throw new Exception(!string.IsNullOrEmpty(response.ErrorMessage) ? response.ErrorMessage : response.StatusDescription);
                    }
                    Write($"{DateTime.Now:hh:mm:ss} => From {startDate.ToString("dd MMM yyyy")} to {endDate.ToString("dd MMM yyyy")} : Page {pageNo} DONE. Saved: {saved} & Updated: {updated}");
                    pageNo++;
                } while (pageNo < totalPages);

                await context.Database.ExecuteSqlCommandAsync("UPDATE Configuration SET LastERPSyncDate=@DATE", new SqlParameter("@DATE", endDate));
                await context.SaveChangesAsync();
            }
            catch (Exception error)
            {
                Write("Error on all employee store: " + error.Message);
            }
        }

        //static async Task Test(ApplicationDbContext context, DateTime startDate, DateTime endDate)
        //{
        //    var client = new RestClient(baseUrl);

        //    try
        //    {
        //        RestRequest request;
        //        IRestResponse<ERPAPIResponse> response;
        //        int totalPages = 0, pageNo = 69, noOfRows = 100;


        //        var userManager = new ApplicationUserManager(new UserStore<ApplicationUser, ApplicationRole, string, IdentityUserLogin, IdentityUserRole, IdentityUserClaim>(context));

        //        request = new RestRequest(apiResource, Method.POST, DataFormat.Json);
        //        request.AddHeader("Authorization", $"Bearer {apiToken}");

        //        request.AddJsonBody(new
        //        {
        //            Vendor = apiVendor,
        //            FromDate = startDate.ToString("yyyyMMdd"),
        //            ToDate = endDate.ToString("yyyyMMdd"),
        //            PageNo = pageNo,
        //            NoOfRows = noOfRows
        //        });

        //        response = await client.ExecuteAsync<ERPAPIResponse>(request);
        //        if (response.StatusCode == HttpStatusCode.OK)
        //        {
        //            if (response.Data.data != null && response.Data.data.Any())
        //            {
        //                totalPages = (int)Math.Ceiling(response.Data.totalRowCount / noOfRows);

        //                var pins = response.Data.data.Select(x => Convert.ToString(x.employeE_ID)).ToList();
        //                Write($"{DateTime.Now:hh:mm:ss} => Page {pageNo} DONE. PINS: {string.Join(",", pins)}");
        //            }
        //        }
        //        else throw new Exception(response.ErrorMessage);

        //    }
        //    catch (Exception error)
        //    {
        //        Write("Error: " + error.Message);
        //    }
        //}

        public static void Write(string msg)
        {
            try
            {
                using (StreamWriter w = File.AppendText(Path.Combine(logPath ?? @"C:\AppLog\e-Learning", DateTime.Now.ToString("yyyy_MM_dd") + ".txt")))
                {
                    Log(msg, w);
                }
            }
            catch (Exception)
            {
                // ignore
            }
        }

        static private void Log(string msg, TextWriter w)
        {
            try
            {
                w.Write(Environment.NewLine);
                w.Write("[{0}]", DateTime.Now.ToLongTimeString());
                w.Write("\t");
                w.WriteLine(" {0}", msg);
            }
            catch (Exception ex)
            {
                throw (ex);
            }
        }

        static Exception GetRealException(Exception error)
        {
            Exception realerror = error;
            while (realerror.InnerException != null)
                realerror = realerror.InnerException;

            return realerror;
        }
    }
}
