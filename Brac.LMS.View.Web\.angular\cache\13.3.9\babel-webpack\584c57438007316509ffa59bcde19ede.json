{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { environment } from 'src/environments/environment';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { ColumnMode } from '@swimlane/ngx-datatable';\nimport { Page } from '../_models/page';\nimport { BlockUI } from 'ng-block-ui';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"ng-block-ui\";\nimport * as i5 from \"ngx-bootstrap/tabs\";\nimport * as i6 from \"@swimlane/ngx-datatable\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"ngx-moment\";\n\nfunction CertificationsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Results \");\n  }\n}\n\nconst _c0 = function (a1) {\n  return [\"/course-details\", a1];\n};\n\nfunction CertificationsComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r19 = ctx.row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r19[\"Title\"]);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, row_r19.CourseId));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", row_r19[\"Title\"], \" \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r20 = ctx.value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, value_r20, \"DD-MMM-YYYY\"), \" \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_21_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r21 = i0.ɵɵnextContext().row;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", row_r21.GainedMarks + \"/\" + row_r21.TotalMarks, \" \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_21_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationsComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CertificationsComponent_ng_template_21_span_0_Template, 2, 1, \"span\", 29);\n    i0.ɵɵtemplate(1, CertificationsComponent_ng_template_21_span_1_Template, 2, 0, \"span\", 29);\n  }\n\n  if (rf & 2) {\n    const row_r21 = ctx.row;\n    i0.ɵɵproperty(\"ngIf\", row_r21[\"Status\"] === \"Published\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r21[\"Status\"] !== \"Published\");\n  }\n}\n\nfunction CertificationsComponent_ng_template_23_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r25 = i0.ɵɵnextContext().row;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", row_r25.Score, \" \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_23_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationsComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CertificationsComponent_ng_template_23_span_0_Template, 2, 1, \"span\", 29);\n    i0.ɵɵtemplate(1, CertificationsComponent_ng_template_23_span_1_Template, 2, 0, \"span\", 29);\n  }\n\n  if (rf & 2) {\n    const row_r25 = ctx.row;\n    i0.ɵɵproperty(\"ngIf\", row_r25[\"Status\"] === \"Published\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r25[\"Status\"] !== \"Published\");\n  }\n}\n\nfunction CertificationsComponent_ng_template_25_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r29 = i0.ɵɵnextContext().row;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", row_r29.Grade, \" \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_25_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationsComponent_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CertificationsComponent_ng_template_25_span_0_Template, 2, 1, \"span\", 29);\n    i0.ɵɵtemplate(1, CertificationsComponent_ng_template_25_span_1_Template, 2, 0, \"span\", 29);\n  }\n\n  if (rf & 2) {\n    const row_r29 = ctx.row;\n    i0.ɵɵproperty(\"ngIf\", row_r29[\"Status\"] === \"Published\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r29[\"Status\"] !== \"Published\");\n  }\n}\n\nfunction CertificationsComponent_ng_template_27_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r33 = i0.ɵɵnextContext().value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r33);\n  }\n}\n\nfunction CertificationsComponent_ng_template_27_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r33 = i0.ɵɵnextContext().value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r33);\n  }\n}\n\nfunction CertificationsComponent_ng_template_27_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r33 = i0.ɵɵnextContext().value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r33, \" \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"container-element\", 30);\n    i0.ɵɵtemplate(1, CertificationsComponent_ng_template_27_span_1_Template, 2, 1, \"span\", 31);\n    i0.ɵɵtemplate(2, CertificationsComponent_ng_template_27_span_2_Template, 2, 1, \"span\", 32);\n    i0.ɵɵtemplate(3, CertificationsComponent_ng_template_27_span_3_Template, 2, 1, \"span\", 33);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r33 = ctx.value;\n    i0.ɵɵproperty(\"ngSwitch\", value_r33);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Failed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Passed\");\n  }\n}\n\nfunction CertificationsComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r40 = ctx.value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r40);\n  }\n}\n\nfunction CertificationsComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r41 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r41);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r41);\n  }\n}\n\nfunction CertificationsComponent_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Certificates \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r42 = ctx.row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r42[\"Title\"]);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, row_r42.CourseId));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", row_r42[\"Title\"], \" \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r43 = ctx.value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, value_r43, \"DD-MMM-YYYY\"), \" \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r44 = ctx.row;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", row_r44.GainedMarks + \"/\" + row_r44.TotalMarks, \" \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r45 = ctx.value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r45);\n  }\n}\n\nfunction CertificationsComponent_ng_template_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function CertificationsComponent_ng_template_47_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r48);\n      const row_r46 = restoredCtx.row;\n      const ctx_r47 = i0.ɵɵnextContext();\n      return ctx_r47.downloadCertificate(row_r46);\n    });\n    i0.ɵɵelement(1, \"i\", 39);\n    i0.ɵɵtext(2, \" Download \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationsComponent_ng_template_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Ghoori Certificates \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r49 = ctx.row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r49[\"CourseName\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", row_r49[\"CourseName\"], \" \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r50 = ctx.value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, value_r50, \"DD-MMM-YYYY\"), \" \");\n  }\n}\n\nfunction CertificationsComponent_ng_template_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function CertificationsComponent_ng_template_59_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r53);\n      const row_r51 = restoredCtx.row;\n      const ctx_r52 = i0.ɵɵnextContext();\n      return ctx_r52.downloadGhooriCertificate(row_r51.CertficationPath);\n    });\n    i0.ɵɵtext(1, \" View \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport class CertificationsComponent {\n  constructor(router, _service, toastr, route) {\n    var _a;\n\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.route = route;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.timestamp = new Date().getTime();\n    this.courseList = [];\n    this.resultPage = new Page();\n    this.certificatePage = new Page(); // testEventComponent:string;\n\n    this.certificates = [];\n    this.ghooriCertificates = [];\n    this.results = [];\n    this.loadingIndicator = false;\n    this.ColumnMode = ColumnMode;\n    this.scrollBarHorizontal = window.innerWidth < 1200;\n    this.resultPage.pageNumber = 0;\n    this.resultPage.size = 10;\n    this.certificatePage.pageNumber = 0;\n    this.certificatePage.size = 10;\n    this.tab = (_a = this.route.snapshot.paramMap.get(\"active_tab\")) !== null && _a !== void 0 ? _a : 'My Exam';\n  }\n\n  ngOnInit() {\n    // this.commonService.aClickedEvent\n    // .subscribe((data) => {\n    // this.testEventComponent=data;\n    // console.log('Certification component subscription: ',this.testEventComponent);\n    // });\n    // console.log('Certification component out of subscription: ',this.testEventComponent);\n    this.getResultList();\n    this.getCertificateList();\n    this.getGhooriCertificateList();\n  }\n\n  setResultPage(pageInfo) {\n    this.resultPage.pageNumber = pageInfo.offset;\n    this.getResultList();\n  }\n\n  getResultList() {\n    const obj = {\n      courseId: null,\n      size: this.resultPage.size,\n      pageNumber: this.resultPage.pageNumber\n    };\n    this.loadingIndicator = true;\n\n    this._service.get('exam/certificate-test/get-exam-results', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.results = res.Data.Records;\n        console.log(this.results);\n        this.resultPage.totalElements = res.Data.Total;\n        this.resultPage.totalPages = Math.ceil(this.resultPage.totalElements / this.resultPage.size);\n        setTimeout(() => {\n          this.loadingIndicator = false; // this.tableComponent.recalculate();\n        }, 500);\n      },\n      error: err => {\n        this.toastr.error(err.message || err, 'Error!', {\n          timeOut: 2000\n        });\n        setTimeout(() => {\n          this.loadingIndicator = false;\n        }, 1000);\n      },\n      complete: () => {}\n    });\n  }\n\n  setCertificatePage(pageInfo) {\n    this.certificatePage.pageNumber = pageInfo.offset;\n    this.getCertificateList();\n  }\n\n  getCertificateList() {\n    const obj = {\n      courseId: null,\n      size: this.certificatePage.size,\n      pageNumber: this.certificatePage.pageNumber\n    };\n    this.loadingIndicator = true;\n\n    this._service.get('exam/get-my-certificates', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.certificates = res.Data.Records;\n        console.log('res', res);\n        this.certificatePage.totalElements = res.Data.Total;\n        this.certificatePage.totalPages = Math.ceil(this.certificatePage.totalElements / this.certificatePage.size);\n        setTimeout(() => {\n          this.loadingIndicator = false; // this.tableComponent.recalculate();\n        }, 500);\n      },\n      error: err => {\n        this.toastr.error(err.message || err, 'Error!', {\n          timeOut: 2000\n        });\n        setTimeout(() => {\n          this.loadingIndicator = false;\n        }, 1000);\n      },\n      complete: () => {}\n    });\n  }\n\n  getGhooriCertificateList() {\n    const obj = {\n      size: this.certificatePage.size,\n      pageNumber: this.certificatePage.pageNumber\n    };\n    this.loadingIndicator = true;\n\n    this._service.get('ghoori-learning/get-certificate', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.ghooriCertificates = res.Data.Records;\n        console.log('res', res);\n        this.certificatePage.totalElements = res.Data.Total;\n        this.certificatePage.totalPages = Math.ceil(this.certificatePage.totalElements / this.certificatePage.size);\n        setTimeout(() => {\n          this.loadingIndicator = false; // this.tableComponent.recalculate();\n        }, 500);\n      },\n      error: err => {\n        this.toastr.error(err.message || err, 'Error!', {\n          timeOut: 2000\n        });\n        setTimeout(() => {\n          this.loadingIndicator = false;\n        }, 1000);\n      },\n      complete: () => {}\n    });\n  }\n\n  downloadCertificate(item) {\n    console.log(item);\n    this.blockUI.start('Generating certificate. Please wait...');\n\n    this._service.downloadFile('course/download-certificate/' + item.CourseId).subscribe({\n      next: res => {\n        // this.pdfViewerOnDemand.pdfSrc = res; // pdfSrc can be Blob or Uint8Array\n        // this.pdfViewerOnDemand.refresh();\n        // this.ngxSmartModalService.create('certificateModal', this.tpl).open();\n        const url = window.URL.createObjectURL(res);\n        var link = document.createElement('a');\n        link.href = url;\n        link.target = 'blank';\n        link.rel = 'noopener';\n        link.download = item.Title + \" Certificate.pdf\";\n        link.click();\n      },\n      error: err => {\n        this.toastr.error(err.message || err, 'Error!', {\n          timeOut: 2000\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  downloadGhooriCertificate(rowPath) {\n    console.log(rowPath);\n    const redirectUrl = `${this.baseUrl}${rowPath}`;\n    console.log('redirectUrl', redirectUrl); // Navigate to the new page\n\n    window.open(redirectUrl, '_blank');\n  }\n\n}\n\nCertificationsComponent.ɵfac = function CertificationsComponent_Factory(t) {\n  return new (t || CertificationsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n};\n\nCertificationsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CertificationsComponent,\n  selectors: [[\"app-certifications\"]],\n  decls: 60,\n  vars: 75,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-2\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"mt-1\", \"mb-4\"], [1, \"section\"], [\"type\", \"pills\", 1, \"\", 3, \"justified\"], [3, \"active\"], [\"tabHeading\", \"\", \"class\", \"justify-content-center mb-2\"], [1, \"col-12\"], [1, \"row\", \"justify-content-center\"], [1, \"col-lg-12\", \"style-min-hight-400\"], [\"rowHeight\", \"auto\", 1, \"material\", 3, \"scrollbarH\", \"rows\", \"loadingIndicator\", \"columnMode\", \"headerHeight\", \"footerHeight\", \"externalPaging\", \"count\", \"offset\", \"limit\", \"page\"], [\"name\", \"Course\", \"cellClass\", \"text-break\", 3, \"width\", \"draggable\", \"sortable\"], [\"ngx-datatable-cell-template\", \"\"], [\"name\", \"Attended Date\", \"prop\", \"StartDate\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"name\", \"Marks\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"name\", \"Score\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"name\", \"Grade\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"name\", \"Result\", \"prop\", \"Result\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"name\", \"Attempts\", \"prop\", \"Attempts\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"name\", \"Comments\", \"prop\", \"CheckerComments\", \"cellClass\", \"text-truncate\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"name\", \"Certificate Date\", \"prop\", \"CertificateDate\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Marks\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Grade\", \"prop\", \"Grade\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Action\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Certificate Date\", \"prop\", \"DateOfCertification\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"routerLinkActive\", \"router-link-active\", 1, \"fs-6\", 3, \"routerLink\", \"title\"], [4, \"ngIf\"], [3, \"ngSwitch\"], [\"class\", \"badge bg-danger\", 4, \"ngSwitchCase\"], [\"class\", \"badge bg-success\", 4, \"ngSwitchCase\"], [\"class\", \"badge bg-warning\", 4, \"ngSwitchDefault\"], [1, \"badge\", \"bg-danger\"], [1, \"badge\", \"bg-success\"], [1, \"badge\", \"bg-warning\"], [3, \"title\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-download\"], [1, \"fs-6\", 3, \"title\"]],\n  template: function CertificationsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h1\", 4);\n      i0.ɵɵtext(6, \"Certifications\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(7, \"hr\", 5);\n      i0.ɵɵelementStart(8, \"div\", 6);\n      i0.ɵɵelementStart(9, \"tabset\", 7);\n      i0.ɵɵelementStart(10, \"tab\", 8);\n      i0.ɵɵtemplate(11, CertificationsComponent_ng_template_11_Template, 1, 0, \"ng-template\", 9);\n      i0.ɵɵelementStart(12, \"div\", 10);\n      i0.ɵɵelementStart(13, \"div\", 11);\n      i0.ɵɵelementStart(14, \"div\", 12);\n      i0.ɵɵelementStart(15, \"ngx-datatable\", 13);\n      i0.ɵɵlistener(\"page\", function CertificationsComponent_Template_ngx_datatable_page_15_listener($event) {\n        return ctx.setResultPage($event);\n      });\n      i0.ɵɵelementStart(16, \"ngx-datatable-column\", 14);\n      i0.ɵɵtemplate(17, CertificationsComponent_ng_template_17_Template, 2, 5, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"ngx-datatable-column\", 16);\n      i0.ɵɵtemplate(19, CertificationsComponent_ng_template_19_Template, 3, 4, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"ngx-datatable-column\", 17);\n      i0.ɵɵtemplate(21, CertificationsComponent_ng_template_21_Template, 2, 2, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"ngx-datatable-column\", 18);\n      i0.ɵɵtemplate(23, CertificationsComponent_ng_template_23_Template, 2, 2, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"ngx-datatable-column\", 19);\n      i0.ɵɵtemplate(25, CertificationsComponent_ng_template_25_Template, 2, 2, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(26, \"ngx-datatable-column\", 20);\n      i0.ɵɵtemplate(27, CertificationsComponent_ng_template_27_Template, 4, 3, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(28, \"ngx-datatable-column\", 21);\n      i0.ɵɵtemplate(29, CertificationsComponent_ng_template_29_Template, 2, 1, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"ngx-datatable-column\", 22);\n      i0.ɵɵtemplate(31, CertificationsComponent_ng_template_31_Template, 2, 2, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(32, \"tab\", 8);\n      i0.ɵɵtemplate(33, CertificationsComponent_ng_template_33_Template, 1, 0, \"ng-template\", 9);\n      i0.ɵɵelementStart(34, \"div\", 10);\n      i0.ɵɵelementStart(35, \"div\", 11);\n      i0.ɵɵelementStart(36, \"div\", 12);\n      i0.ɵɵelementStart(37, \"ngx-datatable\", 13);\n      i0.ɵɵlistener(\"page\", function CertificationsComponent_Template_ngx_datatable_page_37_listener($event) {\n        return ctx.setCertificatePage($event);\n      });\n      i0.ɵɵelementStart(38, \"ngx-datatable-column\", 14);\n      i0.ɵɵtemplate(39, CertificationsComponent_ng_template_39_Template, 2, 5, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(40, \"ngx-datatable-column\", 23);\n      i0.ɵɵtemplate(41, CertificationsComponent_ng_template_41_Template, 3, 4, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(42, \"ngx-datatable-column\", 24);\n      i0.ɵɵtemplate(43, CertificationsComponent_ng_template_43_Template, 2, 1, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(44, \"ngx-datatable-column\", 25);\n      i0.ɵɵtemplate(45, CertificationsComponent_ng_template_45_Template, 2, 1, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(46, \"ngx-datatable-column\", 26);\n      i0.ɵɵtemplate(47, CertificationsComponent_ng_template_47_Template, 3, 0, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(48, \"tab\", 8);\n      i0.ɵɵtemplate(49, CertificationsComponent_ng_template_49_Template, 1, 0, \"ng-template\", 9);\n      i0.ɵɵelementStart(50, \"div\", 10);\n      i0.ɵɵelementStart(51, \"div\", 11);\n      i0.ɵɵelementStart(52, \"div\", 12);\n      i0.ɵɵelementStart(53, \"ngx-datatable\", 13);\n      i0.ɵɵlistener(\"page\", function CertificationsComponent_Template_ngx_datatable_page_53_listener($event) {\n        return ctx.setCertificatePage($event);\n      });\n      i0.ɵɵelementStart(54, \"ngx-datatable-column\", 14);\n      i0.ɵɵtemplate(55, CertificationsComponent_ng_template_55_Template, 2, 2, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(56, \"ngx-datatable-column\", 27);\n      i0.ɵɵtemplate(57, CertificationsComponent_ng_template_57_Template, 3, 4, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(58, \"ngx-datatable-column\", 26);\n      i0.ɵɵtemplate(59, CertificationsComponent_ng_template_59_Template, 2, 0, \"ng-template\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"justified\", false);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"active\", ctx.tab == \"My Exam\" ? true : false);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"scrollbarH\", ctx.scrollBarHorizontal)(\"rows\", ctx.results)(\"loadingIndicator\", ctx.loadingIndicator)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"externalPaging\", true)(\"count\", ctx.resultPage.totalElements)(\"offset\", ctx.resultPage.pageNumber)(\"limit\", ctx.resultPage.size);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", 400)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"active\", ctx.tab == \"My Achivement\" ? true : false);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"scrollbarH\", ctx.scrollBarHorizontal)(\"rows\", ctx.certificates)(\"loadingIndicator\", ctx.loadingIndicator)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"externalPaging\", true)(\"count\", ctx.certificatePage.totalElements)(\"offset\", ctx.certificatePage.pageNumber)(\"limit\", ctx.certificatePage.size);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", 400)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 150)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 150)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 150)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 150)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"active\", ctx.tab == \"Ghoori Achivement\" ? true : false);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"scrollbarH\", ctx.scrollBarHorizontal)(\"rows\", ctx.ghooriCertificates)(\"loadingIndicator\", ctx.loadingIndicator)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"externalPaging\", true)(\"count\", ctx.certificatePage.totalElements)(\"offset\", ctx.certificatePage.pageNumber)(\"limit\", ctx.certificatePage.size);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", 400)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 150)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 150)(\"draggable\", false)(\"sortable\", false);\n    }\n  },\n  directives: [i4.BlockUIComponent, i5.TabsetComponent, i5.TabDirective, i5.TabHeadingDirective, i6.DatatableComponent, i6.DataTableColumnDirective, i6.DataTableColumnCellDirective, i1.RouterLinkWithHref, i1.RouterLinkActive, i7.NgIf, i7.NgSwitch, i7.NgSwitchCase, i7.NgSwitchDefault],\n  pipes: [i8.DateFormatPipe],\n  styles: [\"app-certifications tabset ul{justify-content:center!important;margin-bottom:.5rem!important}app-certifications tabset .nav-pills .nav-link{border:1px solid #e5e2e2!important}app-certifications tabset .nav-pills:hover .nav-link:hover{border:1px solid #0071BB!important;background-color:#f5c253!important;color:#0071bb!important}app-certifications tabset .nav-pills .nav-item{min-width:150px;text-align:center}app-certifications tabset .nav-pills .nav-link.active,app-certifications tabset .nav-pills .show>.nav-link{border:1px solid #F5C253!important;background-color:#0071bb!important;color:#f5c253!important}.style-min-hight-400{min-height:400px}\\n\"],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], CertificationsComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}