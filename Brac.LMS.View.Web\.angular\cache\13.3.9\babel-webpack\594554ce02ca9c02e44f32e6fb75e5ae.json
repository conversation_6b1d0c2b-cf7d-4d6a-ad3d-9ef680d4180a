{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { TemplateRef } from '@angular/core';\nimport { environment } from '../../environments/environment';\nimport { NavigationEnd } from '@angular/router';\nimport { BlockUI } from 'ng-block-ui';\nimport { Timer } from 'src/app/_models/timer';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { LocalStorageHelper } from 'src/app/_helpers/local-storage-helper';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { Page } from '../_models/page';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-smart-modal\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"src/app/_helpers/confirm-dialog/confirm.service\";\nimport * as i6 from \"../_services/authentication.service\";\nimport * as i7 from \"../shared/pagination/pagination.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"ng-block-ui\";\nimport * as i10 from \"@angular/flex-layout/extended\";\nimport * as i11 from \"ngx-bootstrap/tabs\";\nimport * as i12 from \"ngx-bootstrap/accordion\";\nimport * as i13 from \"../shared/pagination/pagination.component\";\nimport * as i14 from \"@angular/forms\";\nimport * as i15 from \"@swimlane/ngx-datatable\";\nimport * as i16 from \"../_helpers/safe-pipe\";\nimport * as i17 from \"ngx-moment\";\n\nfunction CourseMockTestComponent_div_3_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Mock Test \");\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_3_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelementStart(1, \"accordion\", 37);\n    i0.ɵɵelementStart(2, \"accordion-group\", 38);\n    i0.ɵɵelement(3, \"div\", 39);\n    i0.ɵɵpipe(4, \"safe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"isAnimated\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(4, 2, ctx_r14.pagedetail.ExamInstructions, \"html\"), i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelementStart(1, \"div\", 29);\n    i0.ɵɵelementStart(2, \"div\", 30);\n    i0.ɵɵelementStart(3, \"div\", 1);\n    i0.ɵɵelementStart(4, \"div\", 31);\n    i0.ɵɵelementStart(5, \"ul\", 32);\n    i0.ɵɵelementStart(6, \"li\", 33);\n    i0.ɵɵelement(7, \"i\", 34);\n    i0.ɵɵtext(8, \" Test Name : \");\n    i0.ɵɵelementStart(9, \"b\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"li\");\n    i0.ɵɵelement(12, \"i\", 34);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"li\");\n    i0.ɵɵelement(15, \"i\", 34);\n    i0.ɵɵtext(16, \" Question Type : MCQ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 31);\n    i0.ɵɵelementStart(18, \"ul\", 32);\n    i0.ɵɵelementStart(19, \"li\");\n    i0.ɵɵelement(20, \"i\", 34);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\");\n    i0.ɵɵelement(23, \"i\", 34);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, CourseMockTestComponent_div_3_div_13_div_3_div_25_Template, 5, 5, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r9.pagedetail.ExamName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Total Marks : \", ctx_r9.pagedetail.Marks, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" Total Duration : \", ctx_r9.getHourMint(ctx_r9.pagedetail.DurationMnt), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Pending Quota : \", ctx_r9.pagedetail.PendingQuota, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.pagedetail.ExamInstructions);\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_4_h3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.notAllowMessage, \" \");\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_4_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function CourseMockTestComponent_div_3_div_13_div_4_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(4);\n      return ctx_r17.startQuiz();\n    });\n    i0.ɵɵtext(1, \" Start Exam \");\n    i0.ɵɵelement(2, \"i\", 47);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, CourseMockTestComponent_div_3_div_13_div_4_h3_1_Template, 2, 1, \"h3\", 41);\n    i0.ɵɵelementStart(2, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function CourseMockTestComponent_div_3_div_13_div_4_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return ctx_r19.backClicked();\n    });\n    i0.ɵɵelement(3, \"i\", 43);\n    i0.ɵɵtext(4, \" Go Back \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CourseMockTestComponent_div_3_div_13_div_4_button_5_Template, 3, 0, \"button\", 44);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.pagedetail.Allow);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.pagedetail.Allow);\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelementStart(1, \"div\", 48);\n    i0.ɵɵelementStart(2, \"app-pagination\", 49);\n    i0.ɵɵlistener(\"pageChange\", function CourseMockTestComponent_div_3_div_13_div_6_Template_app_pagination_pageChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(3);\n      return ctx_r21.onPageChange($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"totalItems\", ctx_r11.questionList.length)(\"paginationControlToShow\", 10)(\"itemsPerPage\", ctx_r11.page.size)(\"definitions\", ctx_r11.questionList)(\"currentPage\", ctx_r11.page.pageNumber);\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_7_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 64);\n    i0.ɵɵelementStart(3, \"p\", 65);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 66);\n    i0.ɵɵelementStart(6, \"p\", 65);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r24.questionList[ctx_r24.qIndex].Question, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" [\", ctx_r24.questionList[ctx_r24.qIndex].Mark, \"] \");\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_7_div_14_ul_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 69);\n    i0.ɵɵelementStart(1, \"input\", 70);\n    i0.ɵɵlistener(\"click\", function CourseMockTestComponent_div_3_div_13_div_7_div_14_ul_2_li_1_Template_input_click_1_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(6);\n      return ctx_r29.selectSpecific(ctx_r29.questionList[ctx_r29.qIndex].Options);\n    })(\"ngModelChange\", function CourseMockTestComponent_div_3_div_13_div_7_div_14_ul_2_li_1_Template_input_ngModelChange_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r30);\n      const option_r27 = restoredCtx.$implicit;\n      return option_r27.Selected = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 71);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const option_r27 = ctx.$implicit;\n    const oi_r28 = ctx.index;\n    const ctx_r26 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"name\", \"radioGroup\" + ctx_r26.qIndex)(\"ngModel\", option_r27.Selected)(\"id\", \"option\" + oi_r28 + \"-\" + ctx_r26.qIndex)(\"value\", option_r27.Text);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", \"option\" + oi_r28 + \"-\" + ctx_r26.qIndex);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r27.Text, \" \");\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_7_div_14_ul_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 67);\n    i0.ɵɵtemplate(1, CourseMockTestComponent_div_3_div_13_div_7_div_14_ul_2_li_1_Template, 4, 6, \"li\", 68);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r25.questionList[ctx_r25.qIndex].Options);\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_7_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, CourseMockTestComponent_div_3_div_13_div_7_div_14_div_1_Template, 8, 3, \"div\", 61);\n    i0.ɵɵtemplate(2, CourseMockTestComponent_div_3_div_13_div_7_div_14_ul_2_Template, 2, 2, \"ul\", 62);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.timer.isRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.timer.isRunning);\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelementStart(1, \"div\", 50);\n    i0.ɵɵelementStart(2, \"div\", 51);\n    i0.ɵɵelementStart(3, \"div\", 1);\n    i0.ɵɵelementStart(4, \"div\", 52);\n    i0.ɵɵelement(5, \"i\", 53);\n    i0.ɵɵelementStart(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 54);\n    i0.ɵɵelement(9, \"i\", 55);\n    i0.ɵɵelementStart(10, \"strong\");\n    i0.ɵɵtext(11, \" Time Left: \");\n    i0.ɵɵelementStart(12, \"b\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, CourseMockTestComponent_div_3_div_13_div_7_div_14_Template, 3, 2, \"div\", 56);\n    i0.ɵɵelementStart(15, \"div\", 57);\n    i0.ɵɵelementStart(16, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function CourseMockTestComponent_div_3_div_13_div_7_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext(3);\n      return ctx_r32.prevQuestion();\n    });\n    i0.ɵɵelement(17, \"i\", 43);\n    i0.ɵɵtext(18, \" Prev \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function CourseMockTestComponent_div_3_div_13_div_7_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r34 = i0.ɵɵnextContext(3);\n      return ctx_r34.nextQuestion();\n    });\n    i0.ɵɵtext(20);\n    i0.ɵɵelement(21, \"i\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\" QUESTION \", ctx_r12.qIndex + 1, \" of \", ctx_r12.questionList.length, \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate3(\"\", ctx_r12.timer.hour, \"h : \", ctx_r12.timer.minute, \"m : \", ctx_r12.timer.second, \"s\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.timer.isRunning);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r12.qIndex === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.btnNextText, \" \");\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_8_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 64);\n    i0.ɵɵelementStart(3, \"b\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 66);\n    i0.ɵɵelementStart(6, \"b\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 1);\n    i0.ɵɵelementStart(9, \"div\", 28);\n    i0.ɵɵelementStart(10, \"div\", 1);\n    i0.ɵɵelementStart(11, \"div\", 77);\n    i0.ɵɵelementStart(12, \"input\", 78);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseMockTestComponent_div_3_div_13_div_8_div_1_div_5_Template_input_ngModelChange_12_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const item_r37 = restoredCtx.$implicit;\n      return item_r37.Options[0].Selected = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" a. \");\n    i0.ɵɵelementStart(14, \"label\", 79);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 77);\n    i0.ɵɵelementStart(17, \"input\", 78);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseMockTestComponent_div_3_div_13_div_8_div_1_div_5_Template_input_ngModelChange_17_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const item_r37 = restoredCtx.$implicit;\n      return item_r37.Options[1].Selected = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18, \" b. \");\n    i0.ɵɵelementStart(19, \"label\", 79);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 77);\n    i0.ɵɵelementStart(22, \"input\", 78);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseMockTestComponent_div_3_div_13_div_8_div_1_div_5_Template_input_ngModelChange_22_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const item_r37 = restoredCtx.$implicit;\n      return item_r37.Options[2].Selected = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" c. \");\n    i0.ɵɵelementStart(24, \"label\", 79);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 77);\n    i0.ɵɵelementStart(27, \"input\", 78);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseMockTestComponent_div_3_div_13_div_8_div_1_div_5_Template_input_ngModelChange_27_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const item_r37 = restoredCtx.$implicit;\n      return item_r37.Options[3].Selected = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \" d. \");\n    i0.ɵɵelementStart(29, \"label\", 79);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"hr\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r37 = ctx.$implicit;\n    const i_r38 = ctx.index;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", i_r38 + 1, \". \", item_r37.Question, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"[\", item_r37.Mark, \"]\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"option0\", i_r38, \"\");\n    i0.ɵɵproperty(\"ngModel\", item_r37.Options[0].Selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"option0\", i_r38, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r37.Options[0].Text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"option1\", i_r38, \"\");\n    i0.ɵɵproperty(\"ngModel\", item_r37.Options[1].Selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"option1\", i_r38, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r37.Options[1].Text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"option2\", i_r38, \"\");\n    i0.ɵɵproperty(\"ngModel\", item_r37.Options[2].Selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"option2\", i_r38, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r37.Options[2].Text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"option3\", i_r38, \"\");\n    i0.ɵɵproperty(\"ngModel\", item_r37.Options[3].Selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"option3\", i_r38, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r37.Options[3].Text);\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelementStart(1, \"div\", 51);\n    i0.ɵɵelementStart(2, \"h5\", 74);\n    i0.ɵɵtext(3, \"MCQ\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 75);\n    i0.ɵɵtemplate(5, CourseMockTestComponent_div_3_div_13_div_8_div_1_div_5_Template, 32, 19, \"div\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r35.mcqList);\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵtemplate(1, CourseMockTestComponent_div_3_div_13_div_8_div_1_Template, 6, 1, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.mcqList.length > 0);\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelementStart(1, \"div\", 22);\n    i0.ɵɵelementStart(2, \"div\", 1);\n    i0.ɵɵtemplate(3, CourseMockTestComponent_div_3_div_13_div_3_Template, 26, 5, \"div\", 23);\n    i0.ɵɵtemplate(4, CourseMockTestComponent_div_3_div_13_div_4_Template, 6, 2, \"div\", 24);\n    i0.ɵɵelementStart(5, \"div\", 1);\n    i0.ɵɵtemplate(6, CourseMockTestComponent_div_3_div_13_div_6_Template, 3, 5, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, CourseMockTestComponent_div_3_div_13_div_7_Template, 22, 8, \"div\", 26);\n    i0.ɵɵtemplate(8, CourseMockTestComponent_div_3_div_13_div_8_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.quizRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.quizRunning);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.quizRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.quizRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.timer && ctx_r6.timer.isRunning);\n  }\n}\n\nfunction CourseMockTestComponent_div_3_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Results \");\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_16_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const rowIndex_r49 = ctx.rowIndex;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", rowIndex_r49 + 1, \" \");\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_16_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r50 = ctx.value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, value_r50, \"DD-MMM-YYYY hh:mm:ss A\"), \"\");\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_16_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r51 = ctx.value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r51);\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_16_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r52 = ctx.value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r52);\n  }\n}\n\nfunction CourseMockTestComponent_div_3_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵelementStart(1, \"div\", 81);\n    i0.ɵɵelementStart(2, \"ngx-datatable\", 82);\n    i0.ɵɵelementStart(3, \"ngx-datatable-column\", 83);\n    i0.ɵɵtemplate(4, CourseMockTestComponent_div_3_div_16_ng_template_4_Template, 2, 1, \"ng-template\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ngx-datatable-column\", 85);\n    i0.ɵɵtemplate(6, CourseMockTestComponent_div_3_div_16_ng_template_6_Template, 3, 4, \"ng-template\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ngx-datatable-column\", 86);\n    i0.ɵɵtemplate(8, CourseMockTestComponent_div_3_div_16_ng_template_8_Template, 2, 1, \"ng-template\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"ngx-datatable-column\", 87);\n    i0.ɵɵtemplate(10, CourseMockTestComponent_div_3_div_16_ng_template_10_Template, 2, 1, \"ng-template\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollbarH\", true)(\"rows\", ctx_r8.resultList)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"limit\", 10);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"maxWidth\", 80)(\"draggable\", false)(\"sortable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"col-lg-12\": a0\n  };\n};\n\nfunction CourseMockTestComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelementStart(1, \"div\", 9);\n    i0.ɵɵelementStart(2, \"div\", 10);\n    i0.ɵɵelementStart(3, \"div\", 11);\n    i0.ɵɵelementStart(4, \"h3\", 12);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"a\", 13);\n    i0.ɵɵlistener(\"click\", function CourseMockTestComponent_div_3_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r53 = i0.ɵɵnextContext();\n      return ctx_r53.backClicked();\n    });\n    i0.ɵɵelement(7, \"i\", 14);\n    i0.ɵɵtext(8, \"Go Back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 15);\n    i0.ɵɵelementStart(10, \"tabset\");\n    i0.ɵɵelementStart(11, \"tab\", 16);\n    i0.ɵɵlistener(\"selectTab\", function CourseMockTestComponent_div_3_Template_tab_selectTab_11_listener($event) {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r55 = i0.ɵɵnextContext();\n      return ctx_r55.changeTab(\"Exam\", $event);\n    });\n    i0.ɵɵtemplate(12, CourseMockTestComponent_div_3_ng_template_12_Template, 1, 0, \"ng-template\", 17);\n    i0.ɵɵtemplate(13, CourseMockTestComponent_div_3_div_13_Template, 9, 5, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"tab\", 16);\n    i0.ɵɵlistener(\"selectTab\", function CourseMockTestComponent_div_3_Template_tab_selectTab_14_listener($event) {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return ctx_r56.changeTab(\"Results\", $event);\n    });\n    i0.ɵɵtemplate(15, CourseMockTestComponent_div_3_ng_template_15_Template, 1, 0, \"ng-template\", 17);\n    i0.ɵɵtemplate(16, CourseMockTestComponent_div_3_div_16_Template, 11, 14, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"hr\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx_r0.quizRunning));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.pagedetail.CourseTitle, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.pagedetail);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.pagedetail);\n  }\n}\n\nfunction CourseMockTestComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelementStart(1, \"div\", 63);\n    i0.ɵɵelementStart(2, \"h3\", 88);\n    i0.ɵɵtext(3, \"Result Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 89);\n    i0.ɵɵelementStart(5, \"li\", 90);\n    i0.ɵɵtext(6, \" Correct Answer \");\n    i0.ɵɵelementStart(7, \"span\", 91);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"li\", 90);\n    i0.ɵɵtext(10, \" Obtained Percentage \");\n    i0.ɵɵelementStart(11, \"span\", 91);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 92);\n    i0.ɵɵelementStart(14, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function CourseMockTestComponent_div_6_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r58);\n      i0.ɵɵnextContext();\n\n      const _r3 = i0.ɵɵreference(8);\n\n      return _r3.open();\n    });\n    i0.ɵɵtext(15, \" Show Result \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function CourseMockTestComponent_div_6_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return ctx_r59.resultModalClose();\n    });\n    i0.ɵɵtext(17, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.result.NoOfCorrectAnsweredQs, \" out of \", ctx_r2.result.Questions.length, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.result.Scored, \"%\");\n  }\n}\n\nfunction CourseMockTestComponent_div_9_div_7_label_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 102);\n    i0.ɵɵtext(1, \"not answered\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseMockTestComponent_div_9_div_7_div_7_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 109);\n    i0.ɵɵtext(1, \"Correct Answer\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c1 = function (a0, a1) {\n  return {\n    \"bg-success\": a0,\n    \"bg-danger\": a1\n  };\n};\n\nfunction CourseMockTestComponent_div_9_div_7_div_7_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 110);\n    i0.ɵɵtext(1, \"You Answered\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const option_r65 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c1, option_r65.correct, !option_r65.correct));\n  }\n}\n\nconst _c2 = function (a0, a1) {\n  return {\n    \"text-success\": a0,\n    \"text-danger\": a1\n  };\n};\n\nfunction CourseMockTestComponent_div_9_div_7_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103);\n    i0.ɵɵelementStart(1, \"div\", 104);\n    i0.ɵɵelement(2, \"input\", 105);\n    i0.ɵɵelementStart(3, \"label\", 106);\n    i0.ɵɵtext(4);\n    i0.ɵɵtemplate(5, CourseMockTestComponent_div_9_div_7_div_7_span_5_Template, 2, 0, \"span\", 107);\n    i0.ɵɵtemplate(6, CourseMockTestComponent_div_9_div_7_div_7_span_6_Template, 2, 4, \"span\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const option_r65 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", option_r65.answered);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c2, option_r65.correct, !option_r65.correct && option_r65.answered));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r65.value, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", option_r65.correctAns && !option_r65.answered);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", option_r65.answered);\n  }\n}\n\nconst _c3 = function (a0) {\n  return {\n    \"text-danger\": a0\n  };\n};\n\nfunction CourseMockTestComponent_div_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵelementStart(1, \"div\", 95);\n    i0.ɵɵelementStart(2, \"b\", 99);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CourseMockTestComponent_div_9_div_7_label_4_Template, 2, 0, \"label\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 95);\n    i0.ɵɵelementStart(6, \"div\", 1);\n    i0.ɵɵtemplate(7, CourseMockTestComponent_div_9_div_7_div_7_Template, 7, 8, \"div\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r61 = ctx.$implicit;\n    const i_r62 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c3, !item_r61.Answered));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", i_r62 + 1, \". \", item_r61.Question, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r61.Answered);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", item_r61.Options);\n  }\n}\n\nfunction CourseMockTestComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r70 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelementStart(1, \"div\", 63);\n    i0.ɵɵelementStart(2, \"h3\", 88);\n    i0.ɵɵtext(3, \"Mock Test Result\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 73);\n    i0.ɵɵelementStart(5, \"div\", 63);\n    i0.ɵɵelementStart(6, \"div\", 95);\n    i0.ɵɵtemplate(7, CourseMockTestComponent_div_9_div_7_Template, 8, 7, \"div\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 92);\n    i0.ɵɵelementStart(9, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function CourseMockTestComponent_div_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r69 = i0.ɵɵnextContext();\n\n      const _r3 = i0.ɵɵreference(8);\n\n      return _r3.close() && ctx_r69.resultModalClose();\n    });\n    i0.ɵɵtext(10, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.result.Questions);\n  }\n}\n\nexport class CourseMockTestComponent {\n  constructor(router, _service, ngxSmartModalService, toastr, confirmService, authService, pageService, route, _location) {\n    this.router = router;\n    this._service = _service;\n    this.ngxSmartModalService = ngxSmartModalService;\n    this.toastr = toastr;\n    this.confirmService = confirmService;\n    this.authService = authService;\n    this.pageService = pageService;\n    this.route = route;\n    this._location = _location;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.quizRunning = false;\n    this.questionList = [];\n    this.qIndex = -1;\n    this.btnNextText = 'Next';\n    this.noOfAnsweredQs = 0;\n    this.mcqList = [];\n    this.timer = new Timer();\n    this.resultList = [];\n    this.allowAnswerSubmit = false;\n    this.page = new Page();\n    this.isConnected = true;\n    this.answerStatus = ''; // this.courseId = this.route.snapshot.paramMap.get(\"courseId\");\n\n    this.examId = this.route.snapshot.paramMap.get('examId');\n    this.baseUrl = environment.baseUrl;\n    this.authService.getCurrentUser().subscribe(user => {\n      this.currentUser = user;\n    });\n    this.page.startsFrom = 1;\n    this.page.pageNumber = 1;\n    this.page.size = 1;\n    this.router.events.subscribe(evt => {\n      if (!(evt instanceof NavigationEnd)) {\n        return;\n      }\n\n      window.scrollTo(0, 0);\n    });\n  }\n\n  ngOnInit() {\n    this.getCourseMockTestDetails();\n  }\n\n  ngAfterViewInit() {\n    // window.addEventListener('popstate', (event) => {\n    //   this.quizRunning ? alert(\"Your exam has been terminated!\") : true;\n    //   this.quizRunning ? this.saveAnswer(true) : true;\n    // });\n    this.popStateHandler = event => {\n      if (this.quizRunning) {\n        alert('Your exam has been terminated!');\n        this.saveAnswer(true);\n      }\n    };\n\n    window.addEventListener('popstate', this.popStateHandler);\n  }\n\n  ngOnDestroy() {\n    if (this.popStateHandler) {\n      window.removeEventListener('popstate', this.popStateHandler);\n    }\n\n    if (this.timerSubscription) this.timerSubscription.unsubscribe();\n    if (this.interval) clearInterval(this.interval);\n  }\n\n  getHourMint(duration) {\n    return Math.floor(duration / 60) + 'h ' + duration % 60 + 'm';\n  }\n\n  changeTab(type, e) {\n    switch (type) {\n      case 'Results':\n        if (this.resultList.length === 0) this.getExamResult();\n        break;\n\n      default:\n        break;\n    }\n  }\n\n  getCourseMockTestDetails() {\n    this.blockUI.start('Getting data...');\n\n    this._service.get('exam/mock-test/get-exam-info/' + this.examId).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n\n          this._location.back();\n\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n\n          this._location.back();\n\n          return;\n        }\n\n        this.pagedetail = res.Data;\n        console.log('this.pagedetail', this.pagedetail);\n        this.notAllowMessage = res.Message;\n        if (!res.Data.CanAttend) return;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  selectSpecific(lists) {\n    lists.map(x => x.Selected = false);\n  }\n\n  startQuiz() {\n    this.questionList = [];\n    const savedExam = LocalStorageHelper.get(this.examId + '~' + this.currentUser.Id);\n\n    if (savedExam) {\n      this.questionList = savedExam.QuestionList;\n      this.quiz = {\n        StartDate: savedExam.StartDate ? savedExam.StartDate : new Date().toISOString()\n      };\n      this.qIndex = savedExam.QIndex;\n      this.quizRunning = true;\n      this.allowAnswerSubmit = true;\n      this.timerSubscription = this.timer.start(savedExam.ExamTime).subscribe(status => {\n        if (status === 'ended') {\n          this.onTimesUp();\n          this.timerSubscription.unsubscribe();\n        }\n      });\n    } else {\n      this.blockUI.start('Starting exam. Please wait...');\n\n      this._service.get('exam/mock-test/get-questions/' + this.examId).subscribe({\n        next: res => {\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              closeButton: true,\n              disableTimeOut: false,\n              enableHtml: true\n            });\n            return;\n          } // **FIX: Use ISO string for proper datetime serialization**\n\n\n          this.quiz = {\n            StartDate: new Date().toISOString()\n          };\n          this.traineeMockTestId = res.Data.TraineeMockTestId;\n          res.Data.MCQs.forEach(element => {\n            this.questionList.push({\n              Id: element.Id,\n              Question: element.Question,\n              Options: [{\n                Text: element.Option1,\n                Selected: false\n              }, {\n                Text: element.Option2,\n                Selected: false\n              }, {\n                Text: element.Option3,\n                Selected: false\n              }, {\n                Text: element.Option4,\n                Selected: false\n              }],\n              Mark: element.Mark\n            });\n          });\n          this.qIndex = 0;\n          this.quizRunning = true;\n          this.allowAnswerSubmit = true;\n          this.timerSubscription = this.timer.start(this.pagedetail.DurationMnt * 60).subscribe(status => {\n            if (status === 'ended') {\n              this.onTimesUp();\n              this.timerSubscription.unsubscribe();\n            }\n          });\n        },\n        error: err => {\n          this.toastr.error(err.message || err, 'Error!', {\n            closeButton: true,\n            disableTimeOut: true,\n            enableHtml: true\n          });\n          this.blockUI.stop();\n        },\n        complete: () => this.blockUI.stop()\n      });\n    }\n  }\n\n  onPageChange(page) {\n    this.setPage(page);\n  }\n\n  setPage(pageNumber) {\n    this.page.pageNumber = pageNumber;\n    this.qIndex = pageNumber - 1;\n    this.btnNextText = this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';\n  }\n\n  onTimesUp() {\n    //  this.saveAnswerSheetIntoLocalStorage(true);\n    this.ngxSmartModalService.closeAll();\n    this.confirmService.close();\n    this.saveAnswer(true);\n    this.toastr.success('Your answer will get submitted automatically', \"Time's Up!!\");\n  }\n\n  nextQuestion() {\n    if (this.qIndex < this.questionList.length - 1) this.qIndex++;else {\n      this.timer.pause();\n      this.submitAnswer();\n      return;\n    }\n    this.btnNextText = this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';\n    this.page.pageNumber = this.qIndex + 1;\n    this.pageService.currentPage.next(this.page.pageNumber);\n  }\n\n  prevQuestion() {\n    if (this.qIndex !== 0) this.qIndex--;\n    this.page.pageNumber = this.page.pageNumber - 1;\n    this.pageService.currentPage.next(this.page.pageNumber);\n  }\n\n  submitAnswer() {\n    this.confirmService.confirm('Are you sure?', 'You are going to submit answer. ' + this.answerStatus, 'Yes, Submit Answer').subscribe(result => {\n      if (result) {\n        this.timer.pause();\n        this.saveAnswer();\n      } else {\n        this.timer.resume();\n      }\n    });\n  }\n\n  saveAnswer(autoSubmission = false) {\n    // const data = LocalStorageHelper.get(this.examId + '~' + this.currentUser.Id);\n    if (!this.isConnected) {\n      this.toastr.error('You have no internet connection', 'ALERT!', {\n        timeOut: 4000\n      });\n      this.timer.resume();\n      return;\n    }\n\n    let submittedMCQList = [];\n    this.questionList.forEach(element => {\n      submittedMCQList.push({\n        QuestionId: element.Id,\n        Answered: element.Options.map(function (x, i) {\n          if (x.Selected) return i + 1;else return 0;\n        }).filter(x => x > 0).join()\n      });\n    });\n    const obj = {\n      TraineeMockTestId: this.traineeMockTestId,\n      ExamId: this.examId,\n      // **FIX: Use ISO strings for proper datetime serialization**\n      StartTime: this.quiz.StartDate,\n      EndTime: new Date().toISOString(),\n      AutoSubmission: autoSubmission,\n      MCQList: submittedMCQList\n    };\n    this.blockUI.start('Submitting answer...');\n\n    this._service.post('exam/mock-test/save-answers', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.error(res.Message, 'Warning!', {\n            closeButton: true,\n            disableTimeOut: true,\n            enableHtml: true\n          });\n          this.timer.resume();\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: true,\n            enableHtml: true\n          });\n          this.timer.resume();\n          return;\n        }\n\n        this.confirmService.close();\n        this.allowAnswerSubmit = false;\n        this.result = res.Data;\n        this.getExamResult();\n        this.result.Questions.forEach(element => {\n          element.Options = [];\n\n          for (let i = 1; i <= 4; i++) {\n            let option = {\n              id: i,\n              value: element['Option' + i],\n              answered: element.Answered.indexOf(i.toString()) !== -1,\n              correctAns: element.CorrectAnswers.indexOf(i.toString()) !== -1,\n              correct: false\n            };\n            option.correct = option.answered && option.correctAns;\n            element.Options.push(option);\n          }\n        });\n        if (!this.resultModal) this.resultModal = this.ngxSmartModalService.create('resultModal', this.tpl);\n        this.resultModal.open();\n      },\n      error: err => {\n        this.timer.resume();\n        this.toastr.error(err.message || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: true,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => {\n        this.blockUI.stop();\n      }\n    });\n  }\n\n  getExamResult() {\n    this._service.get('exam/mock-test/get-exam-results/' + this.examId).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.resultList = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => console.log('complete')\n    });\n  } // private saveAnswerSheetIntoLocalStorage(autoSubmission: boolean = false) {\n  //    let submittedMCQList = [], submittedTFQList = [], submittedFIGQList = [], submittedLRMQList = [], submittedWQList = [];\n  //   this.questionList.filter(x => x.Type === 'MCQ').forEach(element => {\n  //     submittedMCQList.push({\n  //       QuestionId: element.Id,\n  //       Answered: element.Options.map(function (x, i) {\n  //         if (x.Selected) return i + 1;\n  //         else return 0;\n  //       }).filter(x => x > 0).join()\n  //     });\n  //   });\n  //   // this.questionList.filter(x => x.Type === 'TFQ').forEach(element => {\n  //   //   submittedTFQList.push({\n  //   //     QuestionId: element.Id,\n  //   //     Answered: element.Answer,\n  //   //     CorrectAnswer: !element.Answer ? (element.CorrectAnswer ? element.CorrectAnswer.trim() : element.CorrectAnswer) : null\n  //   //   });\n  //   // });\n  //   // this.questionList.filter(x => x.Type === 'FIGQ').forEach(element => {\n  //   //   submittedFIGQList.push({\n  //   //     QuestionId: element.Id,\n  //   //     Answered: element.Answer ? element.Answer.trim() : element.Answer\n  //   //   });\n  //   // });\n  //   // let mQuestions = this.questionList.find(x => x.Type === 'LRMQ');\n  //   // if (mQuestions)\n  //   //   for (let i = 0; i < mQuestions.LeftSides.length; i++) {\n  //   //     const element = mQuestions.LeftSides[i];\n  //   //     submittedLRMQList.push({\n  //   //       QuestionId: element.Id,\n  //   //       Answered: mQuestions.RightSides[i]\n  //   //     });\n  //   //   }\n  //   // this.questionList.filter(x => x.Type === 'WQ').forEach(element => {\n  //   //   submittedWQList.push({\n  //   //     QuestionId: element.Id,\n  //   //     Answered: element.Answer ? element.Answer.trim() : element.Answer\n  //   //   });\n  //   // });\n  //   const obj = {\n  //     ExamId: this.examId,\n  //     StartTime: this.quiz.StartDate,\n  //     EndTime: new Date().toISOString(),\n  //     AutoSubmission: autoSubmission,\n  //     ExamTime: this.timer.timeLeft,\n  //     QIndex: this.qIndex,\n  //    // MCQList: submittedMCQList,\n  //     // TFQList: submittedTFQList,\n  //     // FIGQList: submittedFIGQList,\n  //     // LRMQList: submittedLRMQList,\n  //     // WQList: submittedWQList,\n  //     QuestionList: this.questionList\n  //   }\n  //   LocalStorageHelper.setWithExpiry(this.examId + '~' + this.currentUser.Id, obj, 3);\n  //   // this.answerStatus = '<br/>';\n  //   // if (obj.MCQList.length > 0) this.answerStatus += 'MCQ answered: ' + obj.MCQList.filter(x => x.Answered).length + \" out of \" + obj.MCQList.length;\n  //   // if (obj.TFQList.length > 0) this.answerStatus += ' <br/> True/False answered - True: ' + obj.TFQList.filter(x => x.Answered).length + \" & False: \" + obj.TFQList.filter(x => !x.Answered).length;\n  //   // if (obj.FIGQList.length > 0) this.answerStatus += ' <br/> Fill in the gap answered: ' + obj.FIGQList.filter(x => x.Answered).length + \" out of \" + obj.FIGQList.length;\n  //   // if (obj.WQList.length > 0) this.answerStatus += ' <br/> Written answered ' + obj.WQList.filter(x => x.Answered).length + \" out of \" + obj.WQList.length;\n  // }\n\n\n  resultModalClose() {\n    this.quizRunning = false;\n    this.timer.stop();\n    this.timerSubscription.unsubscribe();\n\n    this._location.back();\n\n    this.result = null;\n  }\n\n  backClicked() {\n    this.quizRunning ? this.saveAnswer(true) : true;\n    !this.quizRunning ? this._location.back() : true;\n  }\n\n}\n\nCourseMockTestComponent.ɵfac = function CourseMockTestComponent_Factory(t) {\n  return new (t || CourseMockTestComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.NgxSmartModalService), i0.ɵɵdirectiveInject(i4.ToastrService), i0.ɵɵdirectiveInject(i5.ConfirmService), i0.ɵɵdirectiveInject(i6.AuthenticationService), i0.ɵɵdirectiveInject(i7.PaginationService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i8.Location));\n};\n\nCourseMockTestComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CourseMockTestComponent,\n  selectors: [[\"app-course-mock-test\"]],\n  viewQuery: function CourseMockTestComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tpl = _t.first);\n    }\n  },\n  decls: 10,\n  vars: 7,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"row\"], [\"class\", \"col p-5\", 3, \"ngClass\", 4, \"ngIf\"], [\"identifier\", \"resultModal\", \"customClass\", \"nsm-dialog-animation-btt\", 3, \"escapable\", \"dismissable\", \"onAnyCloseEventFinished\"], [\"resultModal\", \"\"], [\"class\", \"card\", 4, \"ngIf\"], [\"identifier\", \"resultDetailsModal\", \"customClass\", \"nsm-dialog-animation-btt custom-modal\", 3, \"escapable\", \"dismissable\"], [\"resultDetailsModal\", \"\"], [1, \"col\", \"p-5\", 3, \"ngClass\"], [1, \"row\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\", \"p-4\"], [1, \"pt-2\", \"p-md-3\"], [1, \"col-12\", \"d-flex\", \"justify-content-between\"], [1, \"h3\", \"text-break\", \"d-flex\", \"align-items-center\"], [1, \"btn\", \"btn-link\", \"fs-4\", \"fw-bold\", \"btn-sm\", \"d-flex\", \"align-items-center\", \"border-start\", 3, \"click\"], [1, \"fs-4\", \"ai-arrow-left\", \"fs-base\", \"me-2\"], [1, \"col-12\", \"mb-3\"], [3, \"selectTab\"], [\"tabHeading\", \"\"], [\"class\", \"section\", 4, \"ngIf\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"mt-1\", \"mb-4\"], [1, \"section\"], [1, \"col-12\"], [\"class\", \"col-sm-12\", 4, \"ngIf\"], [\"class\", \"col-sm-12 text-center\", 4, \"ngIf\"], [\"class\", \"col-12 mb-3\", 4, \"ngIf\"], [\"class\", \"col-12\", 4, \"ngIf\"], [\"class\", \"col-sm-10 col-sm-12\", 4, \"ngIf\"], [1, \"col-sm-12\"], [1, \"sidebar\"], [1, \"widget\", \"widget_recent_post\"], [1, \"col-md-6\"], [1, \"list_none\", \"blog_meta\"], [1, \"text-break\"], [1, \"fa\", \"fa-arrow-right\", \"me-2\"], [\"class\", \"col-12 my-3\", 4, \"ngIf\"], [1, \"col-12\", \"my-3\"], [3, \"isAnimated\"], [\"heading\", \"EXAM INSTRUCTIONS (click here to see)\", \"panelClass\", \"custom-accordion\"], [3, \"innerHTML\"], [1, \"col-sm-12\", \"text-center\"], [\"class\", \"p-3 text-danger\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-lg\", \"me-3\", 3, \"click\"], [1, \"fa\", \"fa-arrow-left\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-lg\", 3, \"click\", 4, \"ngIf\"], [1, \"p-3\", \"text-danger\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-lg\", 3, \"click\"], [1, \"fa\", \"fa-arrow-right\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"totalItems\", \"paginationControlToShow\", \"itemsPerPage\", \"definitions\", \"currentPage\", \"pageChange\"], [1, \"card\", \"q-panel\", \"noselect\"], [1, \"card-header\"], [1, \"col-lg-8\", \"col-md-5\", \"col-12\", \"qs-counter\"], [1, \"fa\", \"fa-question-circle\"], [1, \"col-lg-4\", \"col-md-5\", \"col-12\", \"timer\"], [1, \"fa\", \"fa-clock-o\"], [\"class\", \"question-body noselect\", 4, \"ngIf\"], [1, \"card-footer\", \"d-flex\", \"justify-content-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-info\", \"btn-box\", \"me-1\", 3, \"disabled\", \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"me-1\", 3, \"click\"], [1, \"question-body\", \"noselect\"], [\"class\", \"card-body\", 4, \"ngIf\"], [\"class\", \"list-group\", 4, \"ngIf\"], [1, \"card-body\"], [1, \"col-sm-11\", \"col-10\"], [1, \"text-question\", \"mb-0\"], [1, \"col-sm-1\", \"col-2\"], [1, \"list-group\"], [\"class\", \"list-group-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\"], [\"type\", \"radio\", 1, \"form-check-input\", 3, \"name\", \"ngModel\", \"id\", \"value\", \"click\", \"ngModelChange\"], [1, \"form-check-label\", 3, \"for\"], [1, \"col-sm-10\", \"col-sm-12\"], [1, \"card\"], [1, \"card-title\", \"mb-0\"], [1, \"card-body\", \"pt-3\"], [\"class\", \"col-sm-12\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-6\", \"col-sm-6\", \"col-12\"], [\"type\", \"checkbox\", 3, \"id\", \"ngModel\", \"ngModelChange\"], [1, \"ms-2\", \"option-text\", 3, \"for\"], [1, \"mb-3\", \"mt-3\"], [1, \"col-lg-12\", \"cmt-style-1\"], [\"columnMode\", \"force\", \"rowHeight\", \"auto\", 1, \"material\", 3, \"scrollbarH\", \"rows\", \"headerHeight\", \"footerHeight\", \"limit\"], [\"name\", \"SL#\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"ngx-datatable-cell-template\", \"\"], [\"prop\", \"StartDate\", \"name\", \"Participation Date\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"prop\", \"TotalMarks\", \"name\", \"Total Marks\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"prop\", \"GainedMarks\", \"name\", \"Gained Marks\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [1, \"card-title\"], [1, \"list-group\", \"list-group-flush\"], [1, \"list-group-item\", \"bold\"], [1, \"label\", \"label-success\", \"float-right\"], [1, \"card-footer\", \"text-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"btn-sm\", \"me-2\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"ml-1\", 3, \"click\"], [1, \"col-xs-12\"], [\"class\", \"row mb-3\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"ml-1\", 3, \"click\"], [1, \"row\", \"mb-3\"], [3, \"ngClass\"], [\"class\", \"badge bg-danger ms-2\", 4, \"ngIf\"], [\"class\", \"col-lg-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"bg-danger\", \"ms-2\"], [1, \"col-lg-6\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"disabled\", \"\", 1, \"form-check-input\", 3, \"checked\"], [1, \"form-check-label\", 3, \"ngClass\"], [\"class\", \"badge bg-info\", 4, \"ngIf\"], [\"class\", \"badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"badge\", \"bg-info\"], [1, \"badge\", 3, \"ngClass\"]],\n  template: function CourseMockTestComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵtemplate(3, CourseMockTestComponent_div_3_Template, 18, 6, \"div\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ngx-smart-modal\", 3, 4);\n      i0.ɵɵlistener(\"onAnyCloseEventFinished\", function CourseMockTestComponent_Template_ngx_smart_modal_onAnyCloseEventFinished_4_listener() {\n        return ctx.resultModalClose();\n      });\n      i0.ɵɵtemplate(6, CourseMockTestComponent_div_6_Template, 18, 3, \"div\", 5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"ngx-smart-modal\", 6, 7);\n      i0.ɵɵtemplate(9, CourseMockTestComponent_div_9_Template, 11, 1, \"div\", 5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.pagedetail);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"escapable\", false)(\"dismissable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.result);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"escapable\", false)(\"dismissable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.result);\n    }\n  },\n  directives: [i9.BlockUIComponent, i8.NgIf, i8.NgClass, i10.DefaultClassDirective, i11.TabsetComponent, i11.TabDirective, i11.TabHeadingDirective, i12.AccordionComponent, i12.AccordionPanelComponent, i13.PaginationComponent, i8.NgForOf, i14.RadioControlValueAccessor, i14.DefaultValueAccessor, i14.NgControlStatus, i14.NgModel, i14.CheckboxControlValueAccessor, i15.DatatableComponent, i15.DataTableColumnDirective, i15.DataTableColumnCellDirective, i3.NgxSmartModalComponent],\n  pipes: [i16.SafePipe, i17.DateFormatPipe],\n  styles: [\".example-list[_ngcontent-%COMP%]{width:100%;max-width:100%;border:solid 1px #ccc;display:block;background:white;border-radius:4px;overflow:hidden}.example-box[_ngcontent-%COMP%]{padding:10px;border-bottom:solid 1px #ccc;color:#000000de;display:flex;flex-direction:row;align-items:center;justify-content:space-between;box-sizing:border-box;cursor:move;background:white;font-size:14px}.cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px #0003,0 8px 10px 1px #00000024,0 3px 14px 2px #0000001f}.cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.example-box[_ngcontent-%COMP%]:last-child{border:none}.example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.example-custom-placeholder[_ngcontent-%COMP%]{background:#ccc;border:dotted 3px #999;min-height:60px;transition:transform .25s cubic-bezier(0,0,.2,1)}.text-question[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#423838}.timer[_ngcontent-%COMP%]{font-size:16px;color:red;text-align:right}.timer[_ngcontent-%COMP%]   b[_ngcontent-%COMP%]{font-weight:900}@media (max-width: 768px){.timer[_ngcontent-%COMP%]{text-align:center;margin-top:5px;border-top:1px solid #8383a1;display:block}}.q-panel[_ngcontent-%COMP%], .q-panel[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{background:#d7f4ff;font-size:19px;font-weight:600;color:#423838}.q-panel[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%]{font-size:19px!important;color:#423838}.cmt-style-1[_ngcontent-%COMP%]{min-height:400px}.cmt-style-2[_ngcontent-%COMP%]{width:100%;height:200px;display:block}\"],\n  data: {\n    animation: [trigger('inOutAnimation', [transition(':enter', [style({\n      height: 0,\n      opacity: 0\n    }), animate('0.5s ease-out', style({\n      height: 50,\n      opacity: 1\n    }))]), transition(':leave', [style({\n      height: 50,\n      opacity: 1\n    }), animate('0.5s ease-in', style({\n      height: 0,\n      opacity: 0\n    }))])])]\n  }\n});\n\n__decorate([BlockUI()], CourseMockTestComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}