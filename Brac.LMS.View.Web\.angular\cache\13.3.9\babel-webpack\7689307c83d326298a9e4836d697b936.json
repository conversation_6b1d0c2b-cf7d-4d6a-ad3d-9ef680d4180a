{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthLayoutComponent } from './layouts/auth/auth-layout.component';\nimport { WebLayoutComponent } from './layouts/web/web-layout.component';\nimport { AuthGuard } from './_helpers/auth.guard';\nimport { AppComponent } from './app.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport const AppRoutes = [{\n  path: '',\n  component: AuthLayoutComponent,\n  loadChildren: () => import('./authentication/login/login.module').then(m => m.LoginModule)\n}, {\n  path: '',\n  component: WebLayoutComponent,\n  children: [{\n    path: 'dashboard',\n    loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'learning-hours',\n    loadChildren: () => import('./learning-hours/learning-hours.module').then(m => m.LearningHoursModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'profile',\n    loadChildren: () => import('./profile/profile.module').then(m => m.ProfileModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'notifications',\n    loadChildren: () => import('./notifications/notifications.module').then(m => m.NotificationsModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'bookmarks',\n    loadChildren: () => import('./bookmarks/bookmarks.module').then(m => m.BookmarksModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'course-details/:courseId',\n    loadChildren: () => import('./course-details/course-details.module').then(m => m.CourseDetailsModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'course-details-preview/:courseId',\n    loadChildren: () => import('./course-details-preview/course-details-preview.module').then(m => m.CourseDetailsPreviewModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'my-courses',\n    loadChildren: () => import('./my-courses/my-courses.module').then(m => m.MyCoursesModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'available-courses',\n    loadChildren: () => import('./available-courses/available-courses.module').then(m => m.AvailableCoursesModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'all-courses',\n    loadChildren: () => import('./all-courses/all-courses.module').then(m => m.AllCoursesModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'my-progress',\n    loadChildren: () => import('./my-progress/my-progress.module').then(m => m.MyProgressModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'forum-post',\n    loadChildren: () => import('./forum-post/forum-post.module').then(m => m.ForumPostModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'forum',\n    loadChildren: () => import('./forum/forum.module').then(m => m.ForumModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'forum-details/:id',\n    loadChildren: () => import('./forum-details/forum-details.module').then(m => m.ForumDetailsModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'certifications',\n    loadChildren: () => import('./certifications/certifications.module').then(m => m.CertificationsModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'certifications/:active_tab',\n    loadChildren: () => import('./certifications/certifications.module').then(m => m.CertificationsModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'e-library',\n    loadChildren: () => import('./e-library/e-library.module').then(m => m.ELibraryModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'evaluation-test-result',\n    loadChildren: () => import('./evaluation-test-result/evaluation-test-result.module').then(m => m.EvaluationTestResultModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'reports',\n    loadChildren: () => import('./reports/reports.module').then(m => m.ReportsModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'external-courses',\n    loadChildren: () => import('./external-courses/external-courses.module').then(m => m.ExternalCoursesModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'external-course-preview/:courseId',\n    loadChildren: () => import('./external-course-preview/external-course-preview.module').then(m => m.ExternsalCoursePreviewModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'ghoori-learning',\n    loadChildren: () => import('./ghoori-learning/ghoori-learning.module').then(m => m.GhooriLearningModule),\n    canActivate: [AuthGuard]\n  }]\n}, {\n  path: 'course-certificate-test/:examId',\n  component: AppComponent,\n  loadChildren: () => import('./course-certificate-test/course-certificate-test.module').then(m => m.CourseCertificateTestModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'course-mock-test/:examId',\n  component: AppComponent,\n  loadChildren: () => import('./course-mock-test/course-mock-test.module').then(m => m.CourseMockTestModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'evaluation-test/:examId',\n  component: AppComponent,\n  loadChildren: () => import('./evaluation-test/evaluation-test.module').then(m => m.EvaluationTestModule),\n  canActivate: [AuthGuard]\n}, {\n  path: '**',\n  redirectTo: '/'\n}];\nexport let AppRoutingModule = /*#__PURE__*/(() => {\n  class AppRoutingModule {}\n\n  AppRoutingModule.ɵfac = function AppRoutingModule_Factory(t) {\n    return new (t || AppRoutingModule)();\n  };\n\n  AppRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppRoutingModule\n  });\n  AppRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forRoot(AppRoutes, {\n      relativeLinkResolution: 'legacy'\n    })], RouterModule]\n  });\n  return AppRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}