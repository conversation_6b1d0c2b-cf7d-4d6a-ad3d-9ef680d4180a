{"ast": null, "code": "import { HttpResponse } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../_services/authentication.service\";\nimport * as i2 from \"ngx-toastr\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"ng-block-ui\";\nimport * as i5 from \"../_services/nonce.service\";\nexport let ErrorInterceptor = /*#__PURE__*/(() => {\n  class ErrorInterceptor {\n    constructor(authService, toastr, route, blockUIService, nonceService) {\n      this.authService = authService;\n      this.toastr = toastr;\n      this.route = route;\n      this.blockUIService = blockUIService;\n      this.nonceService = nonceService;\n    }\n\n    intercept(request, next) {\n      return next.handle(request).pipe(tap(event => {\n        // debugger;\n        console.log(event);\n\n        if (event instanceof HttpResponse) {\n          console.log(event.headers); // // Extract the nonce value from the Content-Security-Policy header\n          // const cspHeader = event.headers.get('Content-Security-Policy');\n          // const nonceMatch = cspHeader.match(/'nonce-([^']+)'/);\n          // if (nonceMatch && nonceMatch.length > 1) {\n          //   const nonce = nonceMatch[1];\n          //   // Emit an event or save the nonce value to be used in the application\n          //   // For demonstration, I'll just log it\n          //   console.log('Nonce Value:', nonce);\n          //   this.nonceService.addNonceToStyles(nonce);\n          // }\n        }\n      }), catchError(err => {\n        // debugger;\n        let errorMsg = '';\n\n        switch (err.status) {\n          case 0:\n            errorMsg = 'No internet connection. Please check your internet connection.';\n            break;\n\n          case 401:\n            errorMsg = err.error.Message || err.statusText;\n            this.authService.doLogoutUser();\n            this.route.navigate([this.authService.LOGIN_PATH]);\n            console.log(errorMsg);\n            this.blockUIService.resetGlobal();\n            return throwError(() => 'Unauthorized Access');\n\n          case 400:\n            errorMsg = err.error;\n            break;\n\n          default:\n            errorMsg = err.error.Message || err.error.error_description || err.error || err.name || err.statusText;\n            break;\n        }\n\n        this.toastr.warning(errorMsg, 'Warning!', {\n          timeOut: 3000\n        });\n        return throwError(() => errorMsg);\n      }));\n    }\n\n  }\n\n  ErrorInterceptor.ɵfac = function ErrorInterceptor_Factory(t) {\n    return new (t || ErrorInterceptor)(i0.ɵɵinject(i1.AuthenticationService), i0.ɵɵinject(i2.ToastrService), i0.ɵɵinject(i3.Router), i0.ɵɵinject(i4.BlockUIService), i0.ɵɵinject(i5.NonceService));\n  };\n\n  ErrorInterceptor.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ErrorInterceptor,\n    factory: ErrorInterceptor.ɵfac\n  });\n  return ErrorInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module"}