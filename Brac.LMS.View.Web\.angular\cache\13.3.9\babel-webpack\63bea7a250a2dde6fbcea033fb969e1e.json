{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport * as i0 from \"@angular/core\";\nexport let WebLayoutModule = /*#__PURE__*/(() => {\n  class WebLayoutModule {}\n\n  WebLayoutModule.ɵfac = function WebLayoutModule_Factory(t) {\n    return new (t || WebLayoutModule)();\n  };\n\n  WebLayoutModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: WebLayoutModule\n  });\n  WebLayoutModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule, ReactiveFormsModule, MatExpansionModule]]\n  });\n  return WebLayoutModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}