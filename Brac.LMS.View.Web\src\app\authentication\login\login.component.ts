import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { AuthenticationService } from '../../_services/authentication.service';
import { ToastrService } from 'ngx-toastr';
import { BlockUI } from 'ng-block-ui';
import { Router, ActivatedRoute } from '@angular/router';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { MustMatch } from 'src/app/_helpers/must-match.validator';
import { CommonService } from 'src/app/_services/common.service';
import { ResponseStatus } from 'src/app/_models/enum';
import { Timer } from 'src/app/_models/timer';
import { Subscription } from 'rxjs/internal/Subscription';
import { environment } from 'src/environments/environment';
import { NgxSmartModalService, NgxSmartModalComponent } from 'ngx-smart-modal';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css'],
})
export class LoginComponent implements OnInit {
  LoginForm: FormGroup;
  pinForm: FormGroup;
  resetPasswordForm: FormGroup;
  submitted = false;
  show: boolean = false;
  returnUrl: string = '';
  @BlockUI() blockUI: any;
  modalRef: BsModalRef;
  otpStep: number = 0;
  otp = new FormControl('', [Validators.required, Validators.minLength(6)]);
  resendOtpDisabled: boolean = false;
  capsLockIsOn = false;
  timer: Timer = new Timer();
  timerSubscription: Subscription;
  mediaBaseUrl = environment.mediaBaseUrl;
  pdfSrc: any = null;
  docObj: any = null;
  @ViewChild(TemplateRef, { static: false }) tpl: TemplateRef<any>;
  reportFileName: string;
  attachment: any;

  constructor(
    public formBuilder: FormBuilder,
    private authService: AuthenticationService,
    private _service: CommonService,
    private toastr: ToastrService,
    private router: Router,
    private modalService: BsModalService,
    private route: ActivatedRoute,
    public ngxSmartModalService: NgxSmartModalService
  ) {
    this.authService.isLoggedIn().subscribe((isLoggedIn) => {
      if (isLoggedIn) {
        this.router.navigate([this.authService.INITIAL_PATH]);
        return;
      }
    });
  }

  ngOnInit() {
    this.LoginForm = this.formBuilder.group({
      Username: ['', [Validators.required]],
      Password: ['', [Validators.required]],
    });

    this.pinForm = this.formBuilder.group({
      pin: ['', [Validators.required]],
      phoneNumber: [''],
    });

    this.resetPasswordForm = this.formBuilder.group(
      {
        new_password: ['', [Validators.required, Validators.minLength(12)]],
        confirmPassword: ['', Validators.required],
      },
      {
        validator: MustMatch('new_password', 'confirmPassword'),
      }
    );
    this.getAttachments();
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';
  }

  get f() {
    return this.LoginForm.controls;
  }

  get pf() {
    return this.pinForm.controls;
  }

  get rpf() {
    return this.resetPasswordForm.controls;
  }

  onLoginSubmit() {
    this.submitted = true;
    if (this.LoginForm.invalid) {
      return;
    }
    this.blockUI.start('Processing. Please wait...');

    this.authService
      .login({
        UserName: this.LoginForm.value.Username,
        Password: this.LoginForm.value.Password,
      })
      .subscribe({
        next: (data: any) => {
          this.blockUI.stop();
          if (data) {
            this.toastr.success('Successfully logged in', 'Success!', {
              timeOut: 2000,
            });
            if (this.returnUrl === '/') this.router.navigate(['/dashboard']);
            else this.router.navigate([this.returnUrl]);
          }
        },
        error: (e) => {
          this.blockUI.stop();
          if (e.status === 400) {
            this.toastr.error('Unauthorized request found', 'Warning!', {
              timeOut: 3000,
            });
          } else if (e.status === 401) {
            this.toastr.error('Invalid Username Or Password', 'Warning!', {
              timeOut: 3000,
            });
          }
        },
        complete: () => this.blockUI.stop(),
      });
  }

  forgotPassword(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, {
      class: 'gray',
      backdrop: 'static',
    });
  }

  modalHideForgotPassword() {
    console.log('This', {
      pinForm: this.pinForm,
      resetPasswordForm: this.resetPasswordForm,
      otp: this.otp,
      otpStep: this.otpStep,
      modalRef: this.modalRef,
      submitted: this.submitted,
    });
    this.pinForm.reset();
    this.resetPasswordForm.reset();
    // this.otp.reset();
    this.otp = new FormControl('', [
      Validators.required,
      Validators.minLength(6),
    ]);
    this.otpStep = 0;
    this.modalRef.hide();
    this.submitted = false;
  }

  onSendOtpRequest() {
    this.submitted = true;
    if (this.pinForm.invalid) return;
    this.blockUI.start('Submitting data. Please wait...');
    this._service
      .post('account/request-for-otp/' + this.pinForm.value.pin)
      .subscribe({
        next: (res: any) => {
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, 'Error!', {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          if (res.Data) this.pinForm.controls['phoneNumber'].setValue(res.Data);
          if (res.Data && !res.Message) {
            this.otpStep = 1;
            this.submitted = false;
            this.resendOtpDisabled = true;
            this.timerSubscription = this.timer
              .start(2 * 60)
              .subscribe((status) => {
                if (status === 'ended') {
                  this.resendOtpDisabled = false;
                  this.timerSubscription.unsubscribe();
                }
              });
          }
          if (res.Message) {
            this.resendOtpDisabled = false;
            if (this.timerSubscription) this.timerSubscription.unsubscribe();
            this.toastr.error(res.Message, 'Error!', {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
          }
        },
        error: (err) => {
          this.toastr.warning(err.Messaage || err, 'Error!', {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          this.blockUI.stop();
        },
        complete: () => this.blockUI.stop(),
      });
  }

  validateOTP() {
    this.blockUI.start('Validating your OTP. Please wait...');
    const obj = {
      pin: this.pinForm.value.pin,
      otp: this.otp.value,
    };
    this._service.get('account/validate-otp', obj).subscribe({
      next: (res: any) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, 'Error!', {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        if (res.Data) {
          this.otpStep = 2;
          this.otp = res.Data.Otp;
          this.submitted = false;
          this.resendOtpDisabled = true;
        } else if (res.Message)
          this.toastr.warning(res.Message, 'Warning!', { timeOut: 3000 });
      },
      error: (err) => {
        this.toastr.warning(err.Messaage || err, 'Error!', {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
        this.blockUI.stop();
      },
      complete: () => this.blockUI.stop(),
    });
  }

  resendOTP() {
    this.resendOtpDisabled = true;
    this.timerSubscription = this.timer.start(2 * 60).subscribe((status) => {
      if (status === 'ended') {
        this.resendOtpDisabled = false;
        this.timerSubscription.unsubscribe();
      }
    });
    this.onSendOtpRequest();
  }

  onFormSubmit() {
    this.submitted = true;
    if (this.resetPasswordForm.invalid) return;

    const obj = {
      Pin: this.pinForm.value.pin,
      Password: this.resetPasswordForm.value.new_password,
      // Otp: this.otp
    };
    this.blockUI.start('Submitting data. Please wait...');
    this._service.post('account/reset-password-by-otp', obj).subscribe({
      next: (res: any) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, 'Error!', {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.toastr.success(res.Message, 'Success!');
        this.modalHideForgotPassword();
        return;
      },
      error: (err) => {
        this.toastr.warning(err.Messaage || err, 'Error!', {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
        this.blockUI.stop();
      },
      complete: () => this.blockUI.stop(),
    });
  }
  openPdf(type) {
    this.ngxSmartModalService.create('docModal', this.tpl).open();
    this._service
      .getPDFFile(
        this.mediaBaseUrl +
          '/api/configuration/download-document-file?type=' +
          type
      )
      .subscribe((res) => {
        this.docObj = res;
        console.log('res res', res);
        this.pdfSrc = res;
      });
  }
  onKeyDown(event: KeyboardEvent) {
    const capsLockOn = event.getModifierState('CapsLock');
    this.capsLockIsOn = capsLockOn;
  }
  downloadDoc() {
    var link = document.createElement('a');
    link.href = this.mediaBaseUrl + this.docObj.FilePath;
    link.target = '_blank';
    link.rel = 'noopener';
    link.download =
      this.docObj.Title + '.' + this.docObj.FilePath.split('.').pop();
    link.click();
    link.remove();
  }
  getAttachments() {
    this.blockUI.start('Getting data...');
    this._service.get('configuration/doc-or-info-file-available').subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, 'Error!', {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        console.log('attachment', res.Data);
        this.attachment = res.Data;
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.warning(err.Message || err, 'Warning!', {
          closeButton: true,
          disableTimeOut: false,
        });
      }
    );
  }
  modalClose() {
    this.pdfSrc = null;
    this.docObj = null;
  }
}
