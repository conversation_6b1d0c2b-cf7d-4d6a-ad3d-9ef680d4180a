{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Konka<PERSON> Devanagari script [gom-deva]\n//! author : The Discoverer : https://github.com/WikiDiscoverer\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  function processRelativeTime(number, withoutSuffix, key, isFuture) {\n    var format = {\n      s: ['थोडया सॅकंडांनी', 'थोडे सॅकंड'],\n      ss: [number + ' सॅकंडांनी', number + ' सॅकंड'],\n      m: ['एका मिणटान', 'एक मिनूट'],\n      mm: [number + ' मिणटांनी', number + ' मिणटां'],\n      h: ['एका वरान', 'एक वर'],\n      hh: [number + ' वरांनी', number + ' वरां'],\n      d: ['एका दिसान', 'एक दीस'],\n      dd: [number + ' दिसांनी', number + ' दीस'],\n      M: ['एका म्हयन्यान', 'एक म्हयनो'],\n      MM: [number + ' म्हयन्यानी', number + ' म्हयने'],\n      y: ['एका वर्सान', 'एक वर्स'],\n      yy: [number + ' वर्सांनी', number + ' वर्सां']\n    };\n    return isFuture ? format[key][0] : format[key][1];\n  }\n\n  var gomDeva = moment.defineLocale('gom-deva', {\n    months: {\n      standalone: 'जानेवारी_फेब्रुवारी_मार्च_एप्रील_मे_जून_जुलय_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर'.split('_'),\n      format: 'जानेवारीच्या_फेब्रुवारीच्या_मार्चाच्या_एप्रीलाच्या_मेयाच्या_जूनाच्या_जुलयाच्या_ऑगस्टाच्या_सप्टेंबराच्या_ऑक्टोबराच्या_नोव्हेंबराच्या_डिसेंबराच्या'.split('_'),\n      isFormat: /MMMM(\\s)+D[oD]?/\n    },\n    monthsShort: 'जाने._फेब्रु._मार्च_एप्री._मे_जून_जुल._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'आयतार_सोमार_मंगळार_बुधवार_बिरेस्तार_सुक्रार_शेनवार'.split('_'),\n    weekdaysShort: 'आयत._सोम._मंगळ._बुध._ब्रेस्त._सुक्र._शेन.'.split('_'),\n    weekdaysMin: 'आ_सो_मं_बु_ब्रे_सु_शे'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'A h:mm [वाजतां]',\n      LTS: 'A h:mm:ss [वाजतां]',\n      L: 'DD-MM-YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY A h:mm [वाजतां]',\n      LLLL: 'dddd, MMMM Do, YYYY, A h:mm [वाजतां]',\n      llll: 'ddd, D MMM YYYY, A h:mm [वाजतां]'\n    },\n    calendar: {\n      sameDay: '[आयज] LT',\n      nextDay: '[फाल्यां] LT',\n      nextWeek: '[फुडलो] dddd[,] LT',\n      lastDay: '[काल] LT',\n      lastWeek: '[फाटलो] dddd[,] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s',\n      past: '%s आदीं',\n      s: processRelativeTime,\n      ss: processRelativeTime,\n      m: processRelativeTime,\n      mm: processRelativeTime,\n      h: processRelativeTime,\n      hh: processRelativeTime,\n      d: processRelativeTime,\n      dd: processRelativeTime,\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(वेर)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        // the ordinal 'वेर' only applies to day of the month\n        case 'D':\n          return number + 'वेर';\n\n        default:\n        case 'M':\n        case 'Q':\n        case 'DDD':\n        case 'd':\n        case 'w':\n        case 'W':\n          return number;\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week\n      doy: 3 // The week that contains Jan 4th is the first week of the year (7 + 0 - 4)\n\n    },\n    meridiemParse: /राती|सकाळीं|दनपारां|सांजे/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n\n      if (meridiem === 'राती') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'सकाळीं') {\n        return hour;\n      } else if (meridiem === 'दनपारां') {\n        return hour > 12 ? hour : hour + 12;\n      } else if (meridiem === 'सांजे') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'राती';\n      } else if (hour < 12) {\n        return 'सकाळीं';\n      } else if (hour < 16) {\n        return 'दनपारां';\n      } else if (hour < 20) {\n        return 'सांजे';\n      } else {\n        return 'राती';\n      }\n    }\n  });\n  return gomDeva;\n});", "map": null, "metadata": {}, "sourceType": "script"}