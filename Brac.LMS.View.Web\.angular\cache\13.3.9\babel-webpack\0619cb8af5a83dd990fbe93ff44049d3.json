{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from './../shared/shared.module';\nimport { WebLayoutModule } from '../layouts/web/web-layout.module';\nimport { NotificationsRoutes } from './notifications.routing';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let NotificationsModule = /*#__PURE__*/(() => {\n  class NotificationsModule {}\n\n  NotificationsModule.ɵfac = function NotificationsModule_Factory(t) {\n    return new (t || NotificationsModule)();\n  };\n\n  NotificationsModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: NotificationsModule\n  });\n  NotificationsModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(NotificationsRoutes), SharedModule, WebLayoutModule]]\n  });\n  return NotificationsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}