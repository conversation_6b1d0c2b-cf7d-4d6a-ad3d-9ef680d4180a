{"ast": null, "code": "import { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function refCount() {\n  return operate((source, subscriber) => {\n    let connection = null;\n    source._refCount++;\n    const refCounter = new OperatorSubscriber(subscriber, undefined, undefined, undefined, () => {\n      if (!source || source._refCount <= 0 || 0 < --source._refCount) {\n        connection = null;\n        return;\n      }\n\n      const sharedConnection = source._connection;\n      const conn = connection;\n      connection = null;\n\n      if (sharedConnection && (!conn || sharedConnection === conn)) {\n        sharedConnection.unsubscribe();\n      }\n\n      subscriber.unsubscribe();\n    });\n    source.subscribe(refCounter);\n\n    if (!refCounter.closed) {\n      connection = source.connect();\n    }\n  });\n} //# sourceMappingURL=refCount.js.map", "map": null, "metadata": {}, "sourceType": "module"}