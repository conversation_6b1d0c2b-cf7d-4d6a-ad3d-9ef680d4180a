{"ast": null, "code": "import { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function ignoreElements() {\n  return operate((source, subscriber) => {\n    source.subscribe(new OperatorSubscriber(subscriber, noop));\n  });\n} //# sourceMappingURL=ignoreElements.js.map", "map": null, "metadata": {}, "sourceType": "module"}