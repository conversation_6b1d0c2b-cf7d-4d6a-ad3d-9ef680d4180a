{"ast": null, "code": "import { Subject } from '../Subject';\nimport { from } from '../observable/from';\nimport { operate } from '../util/lift';\nimport { fromSubscribable } from '../observable/fromSubscribable';\nconst DEFAULT_CONFIG = {\n  connector: () => new Subject()\n};\nexport function connect(selector, config = DEFAULT_CONFIG) {\n  const {\n    connector\n  } = config;\n  return operate((source, subscriber) => {\n    const subject = connector();\n    from(selector(fromSubscribable(subject))).subscribe(subscriber);\n    subscriber.add(source.subscribe(subject));\n  });\n} //# sourceMappingURL=connect.js.map", "map": null, "metadata": {}, "sourceType": "module"}