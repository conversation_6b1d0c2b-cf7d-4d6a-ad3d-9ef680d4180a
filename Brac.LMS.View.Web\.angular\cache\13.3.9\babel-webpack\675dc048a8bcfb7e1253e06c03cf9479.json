{"ast": null, "code": "import * as i0 from \"@angular/core\"; // Use block-ui-template class to center div if desired\n\nexport let BlockTemplateCmp = /*#__PURE__*/(() => {\n  class BlockTemplateCmp {}\n\n  BlockTemplateCmp.ɵfac = function BlockTemplateCmp_Factory(t) {\n    return new (t || BlockTemplateCmp)();\n  };\n\n  BlockTemplateCmp.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BlockTemplateCmp,\n    selectors: [[\"ng-component\"]],\n    decls: 4,\n    vars: 1,\n    consts: [[1, \"block-ui-template\", \"d-flex\", \"justify-content-center\"], [1, \"block-ui-loader\"]],\n    template: function BlockTemplateCmp_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"div\", 1);\n        i0.ɵɵelementStart(2, \"p\");\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.message, \"\");\n      }\n    },\n    styles: [\".block-ui-loader[_ngcontent-%COMP%]{border-radius:50%;border-top:10px solid #0071bb;border-bottom:10px solid #0071bb;border-right:10px solid #e5a812;border-left:10px solid #e5a812;width:60px;height:60px;animation:spin 2s linear infinite;float:left}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.block-ui-template[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:20px 10px 0;font-size:x-large;float:left;color:#f3f3f3;font-family:cursive}\"]\n  });\n  return BlockTemplateCmp;\n})();", "map": null, "metadata": {}, "sourceType": "module"}