{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { environment } from 'src/environments/environment';\nimport { NavigationEnd } from '@angular/router';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { Page } from '../_models/page';\nimport { BlockUI } from 'ng-block-ui';\nimport { debounceTime } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../app.component\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../_services/common.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"ng-block-ui\";\nimport * as i7 from \"@ng-select/ng-select\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"ngx-bootstrap/tooltip\";\nimport * as i10 from \"ngx-pagination\";\n\nfunction BookmarksComponent_div_22_div_1_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const course_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", course_r6.NoOfContentsStudied, \" / \", course_r6.NoOfContents, \" \", course_r6.NoOfContents > 1 ? \"Lectures\" : \"Lecture\", \" \");\n  }\n}\n\nfunction BookmarksComponent_div_22_div_1_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1, \" Completed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction BookmarksComponent_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelementStart(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function BookmarksComponent_div_22_div_1_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const course_r6 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return ctx_r11.goToPage(course_r6);\n    });\n    i0.ɵɵelementStart(2, \"div\", 24);\n    i0.ɵɵelement(3, \"img\", 25);\n    i0.ɵɵelementStart(4, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function BookmarksComponent_div_22_div_1_Template_div_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const course_r6 = restoredCtx.$implicit;\n      const i_r7 = restoredCtx.index;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return ctx_r13.onUnbookmark(course_r6, i_r7);\n    });\n    i0.ɵɵelementStart(5, \"div\", 27);\n    i0.ɵɵelement(6, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 29);\n    i0.ɵɵelementStart(8, \"div\", 6);\n    i0.ɵɵelementStart(9, \"div\", 30);\n    i0.ɵɵelementStart(10, \"h4\", 31);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 6);\n    i0.ɵɵelementStart(13, \"div\", 32);\n    i0.ɵɵelementStart(14, \"div\", 33);\n    i0.ɵɵtext(15);\n    i0.ɵɵelement(16, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 32);\n    i0.ɵɵtemplate(18, BookmarksComponent_div_22_div_1_div_18_Template, 2, 3, \"div\", 35);\n    i0.ɵɵtemplate(19, BookmarksComponent_div_22_div_1_div_19_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const course_r6 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate2(\"src\", \"\", ctx_r5.mediaBaseUrl, \"\", course_r6.ImagePath, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"tooltip\", course_r6.Title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", course_r6.Title, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", course_r6.Rating, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", course_r6.NoOfContentsStudied != course_r6.NoOfContents);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", course_r6.NoOfContentsStudied == course_r6.NoOfContents);\n  }\n}\n\nconst _c0 = function (a0, a1, a2) {\n  return {\n    itemsPerPage: a0,\n    currentPage: a1,\n    totalItems: a2\n  };\n};\n\nfunction BookmarksComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, BookmarksComponent_div_22_div_1_Template, 20, 7, \"div\", 21);\n    i0.ɵɵpipe(2, \"paginate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(2, 1, ctx_r1.bookmarkList, i0.ɵɵpureFunction3(4, _c0, ctx_r1.page.size, ctx_r1.page.pageNumber, ctx_r1.page.totalElements)));\n  }\n}\n\nfunction BookmarksComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h6\", 37);\n    i0.ɵɵtext(1, \"No Item Found\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction BookmarksComponent_div_25_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.page.showingResult());\n  }\n}\n\nfunction BookmarksComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelementStart(1, \"div\", 39);\n    i0.ɵɵtemplate(2, BookmarksComponent_div_25_p_2_Template, 2, 1, \"p\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 41);\n    i0.ɵɵelementStart(4, \"nav\", 42);\n    i0.ɵɵelementStart(5, \"pagination-controls\", 43);\n    i0.ɵɵlistener(\"pageChange\", function BookmarksComponent_div_25_Template_pagination_controls_pageChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return ctx_r15.setPage($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.page);\n  }\n}\n\nexport class BookmarksComponent {\n  constructor(appComponent, formBuilder, router, _service, toastr) {\n    this.appComponent = appComponent;\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.timestamp = new Date().getTime();\n    this.bookmarkList = [];\n    this.page = new Page();\n    this.categoryList = [];\n    this.page.startsFrom = 1;\n    this.page.pageNumber = 1;\n    this.page.size = 9;\n    this.router.events.subscribe(evt => {\n      if (!(evt instanceof NavigationEnd)) {\n        return;\n      }\n\n      window.scrollTo(0, 0);\n    });\n  }\n\n  handleSelectClick(selectElement) {\n    this.appComponent.handleSelectClick(selectElement);\n  }\n\n  ngOnInit() {\n    this.filterForm = this.formBuilder.group({\n      name: [null],\n      categoryId: [null],\n      courseType: [null]\n    });\n    this.filterForm.get(\"name\").valueChanges.pipe(debounceTime(700)).subscribe(() => {\n      this.getList();\n    });\n    this.getList();\n    this.getCategoryList();\n  }\n\n  getCategoryList() {\n    this._service.get('course-category/dropdown-list').subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.categoryList = res.Data;\n    }, err => {});\n  }\n\n  setPage(pageNumber) {\n    this.page.pageNumber = pageNumber;\n    this.getList();\n  }\n\n  goToPage(course) {\n    if (course.Enrollment) {\n      this.router.navigate(['course-details', course.Id]);\n    } else {\n      this.router.navigate(['course-details-preview', course.Id]);\n    }\n  }\n\n  getList() {\n    let obj = {\n      name: this.filterForm.value.name ? this.filterForm.value.name.trim() : null,\n      categoryId: this.filterForm.value.categoryId,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber - 1\n    };\n    this.blockUI.start('Getting data. Please wait...');\n\n    this._service.get('course/get-my-bookmarked-courses', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.bookmarkList = res.Data.Records;\n        this.page.pageTotalElements = res.Data.Records.length;\n        this.page.totalElements = res.Data.Total;\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        if (this.page.totalPages == 1) this.page.pageNumber = 1;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  onUnbookmark(course, index) {\n    this.blockUI.start('Executing. Please wait...');\n\n    this._service.get('course/bookmark-or-unbookmark/' + course.Id).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.bookmarkList.splice(index, 1);\n        this.page.pageTotalElements--;\n        this.page.totalElements--;\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  navigateTo() {\n    var courseType = this.filterForm.value.courseType;\n    console.log('courseType', courseType);\n    this.router.navigateByUrl(courseType);\n  }\n\n}\n\nBookmarksComponent.ɵfac = function BookmarksComponent_Factory(t) {\n  return new (t || BookmarksComponent)(i0.ɵɵdirectiveInject(i1.AppComponent), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.CommonService), i0.ɵɵdirectiveInject(i5.ToastrService));\n};\n\nBookmarksComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: BookmarksComponent,\n  selectors: [[\"app-bookmarks\"]],\n  decls: 26,\n  vars: 7,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"mt-1\", \"mb-4\"], [1, \"row\"], [\"autocomplete\", \"off\", 1, \"col-lg-8\", \"col-12\", 3, \"formGroup\"], [1, \"col-lg-6\", \"col-12\", \"mb-3\"], [1, \"input-group\"], [\"for\", \"name\", 1, \"visually-hidden\"], [\"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Type name to search...\", 1, \"form-control\", \"rounded-custom\", \"pe-5\"], [1, \"ai-search\", \"position-absolute\", \"top-50\", \"end-0\", \"translate-middle-y\", \"me-3\"], [1, \"col-lg-3\", \"col-12\", \"mb-3\"], [\"for\", \"categoryId\", 1, \"visually-hidden\"], [\"formControlName\", \"categoryId\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Category\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\", \"change\"], [\"selectElement\", \"\"], [\"class\", \"row mb-5 mt-3 align-items-center\", 4, \"ngIf\", \"ngIfElse\"], [\"elseTemplate\", \"\"], [\"class\", \"row text-center\", 4, \"ngIf\"], [1, \"row\", \"mb-5\", \"mt-3\", \"align-items-center\"], [\"class\", \"col-md-4 px-1 my-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"px-1\", \"my-1\"], [1, \"card\", \"border-0\", \"card-floating-position-custom\", \"shadow\", \"mx-1\", \"my-1\", \"div-pointer\", 3, \"click\"], [1, \"card-img-top\", \"card-img-bottom\"], [\"alt\", \"Maldives\", 3, \"src\"], [1, \"card-floating-div-top-right\", 3, \"click\"], [1, \"d-inline-block\", \"fw-normal\", \"bg-faded-full-black\", \"text-white\", \"px-2\", \"py-1\", \"rounded-1\", \"float-end\"], [1, \"fa\", \"fa-bookmark\", \"text-gold\"], [1, \"card-body\", \"bc-style-1\", 3, \"tooltip\"], [1, \"col-md-12\"], [1, \"text-start\", \"text-dark\", \"limit-text-lines\"], [1, \"col-6\"], [1, \"d-inline-block\", \"fw-normal\", \"bg-faded-black\", \"text-white\", \"px-2\", \"py-1\", \"rounded-1\"], [1, \"fa\", \"fa-star\", \"text-gold\"], [\"class\", \"d-inline-block fw-normal bg-faded-black text-white px-2 py-1 rounded-1 float-end\", 4, \"ngIf\"], [1, \"d-inline-block\", \"fw-normal\", \"bg-faded-black\", \"text-white\", \"px-2\", \"py-1\", \"rounded-1\", \"float-end\"], [1, \"py-2\"], [1, \"row\", \"text-center\"], [1, \"col-md-3\", \"col-xs-12\"], [4, \"ngIf\"], [1, \"col-md-9\", \"col-xs-12\"], [1, \"align-items-center\"], [3, \"pageChange\"]],\n  template: function BookmarksComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r17 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h1\", 4);\n      i0.ɵɵtext(6, \"My Bookmarks\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(7, \"hr\", 5);\n      i0.ɵɵelementStart(8, \"div\", 6);\n      i0.ɵɵelementStart(9, \"form\", 7);\n      i0.ɵɵelementStart(10, \"div\", 6);\n      i0.ɵɵelementStart(11, \"div\", 8);\n      i0.ɵɵelementStart(12, \"div\", 9);\n      i0.ɵɵelementStart(13, \"label\", 10);\n      i0.ɵɵtext(14, \"Name\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(15, \"input\", 11);\n      i0.ɵɵelement(16, \"i\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"div\", 13);\n      i0.ɵɵelementStart(18, \"label\", 14);\n      i0.ɵɵtext(19, \"Category\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"ng-select\", 15, 16);\n      i0.ɵɵlistener(\"click\", function BookmarksComponent_Template_ng_select_click_20_listener() {\n        i0.ɵɵrestoreView(_r17);\n\n        const _r0 = i0.ɵɵreference(21);\n\n        return ctx.handleSelectClick(_r0);\n      })(\"change\", function BookmarksComponent_Template_ng_select_change_20_listener() {\n        return ctx.getList();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(22, BookmarksComponent_div_22_Template, 3, 8, \"div\", 17);\n      i0.ɵɵtemplate(23, BookmarksComponent_ng_template_23_Template, 2, 0, \"ng-template\", null, 18, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(25, BookmarksComponent_div_25_Template, 6, 1, \"div\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      const _r2 = i0.ɵɵreference(24);\n\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(11);\n      i0.ɵɵproperty(\"clearable\", true)(\"clearOnBackspace\", true)(\"items\", ctx.categoryList);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.bookmarkList.length > 0)(\"ngIfElse\", _r2);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.bookmarkList.length > 0);\n    }\n  },\n  directives: [i6.BlockUIComponent, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.DefaultValueAccessor, i2.NgControlStatus, i2.FormControlName, i7.NgSelectComponent, i8.NgIf, i8.NgForOf, i9.TooltipDirective, i10.PaginationControlsComponent],\n  pipes: [i10.PaginatePipe],\n  styles: [\".limit-text-lines[_ngcontent-%COMP%]{display:-webkit-box;height:2.6em;-webkit-line-clamp:2;-webkit-box-orient:vertical;text-overflow:ellipsis;overflow:hidden;white-space:pre-wrap;position:relative}.bc-style-1[_ngcontent-%COMP%]{padding:8px;background-color:#f0f8ff;border-radius:0 0 15px 15px}\"]\n});\n\n__decorate([BlockUI()], BookmarksComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}