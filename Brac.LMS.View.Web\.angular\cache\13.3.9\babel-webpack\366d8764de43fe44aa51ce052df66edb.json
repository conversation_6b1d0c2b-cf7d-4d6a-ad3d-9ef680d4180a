{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { ElementRef, Directive, EventEmitter, Inject, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\n\nfunction throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\n\n\nfunction throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\n\n\nfunction throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\n\n\nfunction throwUnknownPortalTypeError() {\n  throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' + 'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\n\n\nfunction throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\n\n\nfunction throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\n\n\nclass Portal {\n  /** Attach this portal to a host. */\n  attach(host) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (host == null) {\n        throwNullPortalOutletError();\n      }\n\n      if (host.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n    }\n\n    this._attachedHost = host;\n    return host.attach(this);\n  }\n  /** Detach this portal from its host */\n\n\n  detach() {\n    let host = this._attachedHost;\n\n    if (host != null) {\n      this._attachedHost = null;\n      host.detach();\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwNoPortalAttachedError();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n\n\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n\n\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\n\n\nclass ComponentPortal extends Portal {\n  constructor(component, viewContainerRef, injector, componentFactoryResolver) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n    this.componentFactoryResolver = componentFactoryResolver;\n  }\n\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\n\n\nclass TemplatePortal extends Portal {\n  constructor(template, viewContainerRef, context) {\n    super();\n    this.templateRef = template;\n    this.viewContainerRef = viewContainerRef;\n    this.context = context;\n  }\n\n  get origin() {\n    return this.templateRef.elementRef;\n  }\n  /**\n   * Attach the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n\n\n  attach(host, context = this.context) {\n    this.context = context;\n    return super.attach(host);\n  }\n\n  detach() {\n    this.context = undefined;\n    return super.detach();\n  }\n\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\n\n\nclass DomPortal extends Portal {\n  constructor(element) {\n    super();\n    this.element = element instanceof ElementRef ? element.nativeElement : element;\n  }\n\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\n\n\nclass BasePortalOutlet {\n  constructor() {\n    /** Whether this host has already been permanently disposed. */\n    this._isDisposed = false; // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n\n    this.attachDomPortal = null;\n  }\n  /** Whether this host has an attached portal. */\n\n\n  hasAttached() {\n    return !!this._attachedPortal;\n  }\n  /** Attaches a portal. */\n\n\n  attach(portal) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!portal) {\n        throwNullPortalError();\n      }\n\n      if (this.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n\n      if (this._isDisposed) {\n        throwPortalOutletAlreadyDisposedError();\n      }\n    }\n\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal); // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n    } else if (this.attachDomPortal && portal instanceof DomPortal) {\n      this._attachedPortal = portal;\n      return this.attachDomPortal(portal);\n    }\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwUnknownPortalTypeError();\n    }\n  }\n  /** Detaches a previously attached portal. */\n\n\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n\n      this._attachedPortal = null;\n    }\n\n    this._invokeDisposeFn();\n  }\n  /** Permanently dispose of this portal host. */\n\n\n  dispose() {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n\n    this._invokeDisposeFn();\n\n    this._isDisposed = true;\n  }\n  /** @docs-private */\n\n\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n\n  _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n\n      this._disposeFn = null;\n    }\n  }\n\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\n\n\nclass BasePortalHost extends BasePortalOutlet {}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\n\n\nclass DomPortalOutlet extends BasePortalOutlet {\n  constructor(\n  /** Element into which the content is projected. */\n  outletElement, _componentFactoryResolver, _appRef, _defaultInjector,\n  /**\n   * @deprecated `_document` Parameter to be made required.\n   * @breaking-change 10.0.0\n   */\n  _document) {\n    super();\n    this.outletElement = outletElement;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n    this._defaultInjector = _defaultInjector;\n    /**\n     * Attaches a DOM portal by transferring its content into the outlet.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n\n    this.attachDomPortal = portal => {\n      // @breaking-change 10.0.0 Remove check and error once the\n      // `_document` constructor parameter is required.\n      if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Cannot attach DOM portal without _document constructor parameter');\n      }\n\n      const element = portal.element;\n\n      if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('DOM portal content must be attached to a parent node.');\n      } // Anchor used to save the element's previous position so\n      // that we can restore it when the portal is detached.\n\n\n      const anchorNode = this._document.createComment('dom-portal');\n\n      element.parentNode.insertBefore(anchorNode, element);\n      this.outletElement.appendChild(element);\n      this._attachedPortal = portal;\n      super.setDisposeFn(() => {\n        // We can't use `replaceWith` here because IE doesn't support it.\n        if (anchorNode.parentNode) {\n          anchorNode.parentNode.replaceChild(element, anchorNode);\n        }\n      });\n    };\n\n    this._document = _document;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n\n\n  attachComponentPortal(portal) {\n    const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n    const componentFactory = resolver.resolveComponentFactory(portal.component);\n    let componentRef; // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n\n    if (portal.viewContainerRef) {\n      componentRef = portal.viewContainerRef.createComponent(componentFactory, portal.viewContainerRef.length, portal.injector || portal.viewContainerRef.injector);\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      componentRef = componentFactory.create(portal.injector || this._defaultInjector);\n\n      this._appRef.attachView(componentRef.hostView);\n\n      this.setDisposeFn(() => {\n        this._appRef.detachView(componentRef.hostView);\n\n        componentRef.destroy();\n      });\n    } // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n\n\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n    this._attachedPortal = portal;\n    return componentRef;\n  }\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n\n\n  attachTemplatePortal(portal) {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context); // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode)); // Note that we want to detect changes after the nodes have been moved so that\n    // any directives inside the portal that are looking at the DOM inside a lifecycle\n    // hook won't be invoked too early.\n\n    viewRef.detectChanges();\n    this.setDisposeFn(() => {\n      let index = viewContainer.indexOf(viewRef);\n\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    });\n    this._attachedPortal = portal; // TODO(jelbourn): Return locals from view.\n\n    return viewRef;\n  }\n  /**\n   * Clears out a portal from the DOM.\n   */\n\n\n  dispose() {\n    super.dispose();\n    this.outletElement.remove();\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n\n\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\n\n\nclass DomPortalHost extends DomPortalOutlet {}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\n\n\nlet CdkPortal = /*#__PURE__*/(() => {\n  class CdkPortal extends TemplatePortal {\n    constructor(templateRef, viewContainerRef) {\n      super(templateRef, viewContainerRef);\n    }\n\n  }\n\n  CdkPortal.ɵfac = function CdkPortal_Factory(t) {\n    return new (t || CdkPortal)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n  };\n\n  CdkPortal.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkPortal,\n    selectors: [[\"\", \"cdkPortal\", \"\"]],\n    exportAs: [\"cdkPortal\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return CdkPortal;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\n\n\nlet TemplatePortalDirective = /*#__PURE__*/(() => {\n  class TemplatePortalDirective extends CdkPortal {}\n\n  TemplatePortalDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵTemplatePortalDirective_BaseFactory;\n    return function TemplatePortalDirective_Factory(t) {\n      return (ɵTemplatePortalDirective_BaseFactory || (ɵTemplatePortalDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TemplatePortalDirective)))(t || TemplatePortalDirective);\n    };\n  }();\n\n  TemplatePortalDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TemplatePortalDirective,\n    selectors: [[\"\", \"cdk-portal\", \"\"], [\"\", \"portal\", \"\"]],\n    exportAs: [\"cdkPortal\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkPortal,\n      useExisting: TemplatePortalDirective\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return TemplatePortalDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\n\n\nlet CdkPortalOutlet = /*#__PURE__*/(() => {\n  class CdkPortalOutlet extends BasePortalOutlet {\n    constructor(_componentFactoryResolver, _viewContainerRef,\n    /**\n     * @deprecated `_document` parameter to be made required.\n     * @breaking-change 9.0.0\n     */\n    _document) {\n      super();\n      this._componentFactoryResolver = _componentFactoryResolver;\n      this._viewContainerRef = _viewContainerRef;\n      /** Whether the portal component is initialized. */\n\n      this._isInitialized = false;\n      /** Emits when a portal is attached to the outlet. */\n\n      this.attached = new EventEmitter();\n      /**\n       * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n       * @param portal Portal to be attached.\n       * @deprecated To be turned into a method.\n       * @breaking-change 10.0.0\n       */\n\n      this.attachDomPortal = portal => {\n        // @breaking-change 9.0.0 Remove check and error once the\n        // `_document` constructor parameter is required.\n        if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw Error('Cannot attach DOM portal without _document constructor parameter');\n        }\n\n        const element = portal.element;\n\n        if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw Error('DOM portal content must be attached to a parent node.');\n        } // Anchor used to save the element's previous position so\n        // that we can restore it when the portal is detached.\n\n\n        const anchorNode = this._document.createComment('dom-portal');\n\n        portal.setAttachedHost(this);\n        element.parentNode.insertBefore(anchorNode, element);\n\n        this._getRootNode().appendChild(element);\n\n        this._attachedPortal = portal;\n        super.setDisposeFn(() => {\n          if (anchorNode.parentNode) {\n            anchorNode.parentNode.replaceChild(element, anchorNode);\n          }\n        });\n      };\n\n      this._document = _document;\n    }\n    /** Portal associated with the Portal outlet. */\n\n\n    get portal() {\n      return this._attachedPortal;\n    }\n\n    set portal(portal) {\n      // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n      // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n      // and attach a portal programmatically in the parent component. When Angular does the first CD\n      // round, it will fire the setter with empty string, causing the user's content to be cleared.\n      if (this.hasAttached() && !portal && !this._isInitialized) {\n        return;\n      }\n\n      if (this.hasAttached()) {\n        super.detach();\n      }\n\n      if (portal) {\n        super.attach(portal);\n      }\n\n      this._attachedPortal = portal || null;\n    }\n    /** Component or view reference that is attached to the portal. */\n\n\n    get attachedRef() {\n      return this._attachedRef;\n    }\n\n    ngOnInit() {\n      this._isInitialized = true;\n    }\n\n    ngOnDestroy() {\n      super.dispose();\n      this._attachedPortal = null;\n      this._attachedRef = null;\n    }\n    /**\n     * Attach the given ComponentPortal to this PortalOutlet using the ComponentFactoryResolver.\n     *\n     * @param portal Portal to be attached to the portal outlet.\n     * @returns Reference to the created component.\n     */\n\n\n    attachComponentPortal(portal) {\n      portal.setAttachedHost(this); // If the portal specifies an origin, use that as the logical location of the component\n      // in the application tree. Otherwise use the location of this PortalOutlet.\n\n      const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n      const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n      const componentFactory = resolver.resolveComponentFactory(portal.component);\n      const ref = viewContainerRef.createComponent(componentFactory, viewContainerRef.length, portal.injector || viewContainerRef.injector); // If we're using a view container that's different from the injected one (e.g. when the portal\n      // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n      // inside of the alternate view container.\n\n      if (viewContainerRef !== this._viewContainerRef) {\n        this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n      }\n\n      super.setDisposeFn(() => ref.destroy());\n      this._attachedPortal = portal;\n      this._attachedRef = ref;\n      this.attached.emit(ref);\n      return ref;\n    }\n    /**\n     * Attach the given TemplatePortal to this PortalHost as an embedded View.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n\n\n    attachTemplatePortal(portal) {\n      portal.setAttachedHost(this);\n\n      const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context);\n\n      super.setDisposeFn(() => this._viewContainerRef.clear());\n      this._attachedPortal = portal;\n      this._attachedRef = viewRef;\n      this.attached.emit(viewRef);\n      return viewRef;\n    }\n    /** Gets the root node of the portal outlet. */\n\n\n    _getRootNode() {\n      const nativeElement = this._viewContainerRef.element.nativeElement; // The directive could be set on a template which will result in a comment\n      // node being the root. Use the comment's parent node if that is the case.\n\n      return nativeElement.nodeType === nativeElement.ELEMENT_NODE ? nativeElement : nativeElement.parentNode;\n    }\n\n  }\n\n  CdkPortalOutlet.ɵfac = function CdkPortalOutlet_Factory(t) {\n    return new (t || CdkPortalOutlet)(i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT));\n  };\n\n  CdkPortalOutlet.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkPortalOutlet,\n    selectors: [[\"\", \"cdkPortalOutlet\", \"\"]],\n    inputs: {\n      portal: [\"cdkPortalOutlet\", \"portal\"]\n    },\n    outputs: {\n      attached: \"attached\"\n    },\n    exportAs: [\"cdkPortalOutlet\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return CdkPortalOutlet;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\n\n\nlet PortalHostDirective = /*#__PURE__*/(() => {\n  class PortalHostDirective extends CdkPortalOutlet {}\n\n  PortalHostDirective.ɵfac = /* @__PURE__ */function () {\n    let ɵPortalHostDirective_BaseFactory;\n    return function PortalHostDirective_Factory(t) {\n      return (ɵPortalHostDirective_BaseFactory || (ɵPortalHostDirective_BaseFactory = i0.ɵɵgetInheritedFactory(PortalHostDirective)))(t || PortalHostDirective);\n    };\n  }();\n\n  PortalHostDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: PortalHostDirective,\n    selectors: [[\"\", \"cdkPortalHost\", \"\"], [\"\", \"portalHost\", \"\"]],\n    inputs: {\n      portal: [\"cdkPortalHost\", \"portal\"]\n    },\n    exportAs: [\"cdkPortalHost\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkPortalOutlet,\n      useExisting: PortalHostDirective\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return PortalHostDirective;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet PortalModule = /*#__PURE__*/(() => {\n  class PortalModule {}\n\n  PortalModule.ɵfac = function PortalModule_Factory(t) {\n    return new (t || PortalModule)();\n  };\n\n  PortalModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PortalModule\n  });\n  PortalModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return PortalModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n * @deprecated Use `Injector.create` instead.\n * @breaking-change 11.0.0\n */\n\n\nclass PortalInjector {\n  constructor(_parentInjector, _customTokens) {\n    this._parentInjector = _parentInjector;\n    this._customTokens = _customTokens;\n  }\n\n  get(token, notFoundValue) {\n    const value = this._customTokens.get(token);\n\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n\n    return this._parentInjector.get(token, notFoundValue);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BasePortalHost, BasePortalOutlet, CdkPortal, CdkPortalOutlet, ComponentPortal, DomPortal, DomPortalHost, DomPortalOutlet, Portal, PortalHostDirective, PortalInjector, PortalModule, TemplatePortal, TemplatePortalDirective }; //# sourceMappingURL=portal.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}