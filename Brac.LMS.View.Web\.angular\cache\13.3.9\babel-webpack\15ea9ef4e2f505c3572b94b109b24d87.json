{"ast": null, "code": "import { HttpRequest, HttpEventType } from '@angular/common/http';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let UploadService = /*#__PURE__*/(() => {\n  class UploadService {\n    constructor(http) {\n      this.http = http;\n    }\n\n    upload(files, url) {\n      // this will be the our resulting map\n      const status = {};\n      files.forEach(file => {\n        // create a new multipart-form for every file\n        const formData = new FormData();\n        formData.append('file', file, file.name); // create a http-post request and pass the form\n        // tell it to report the upload progress\n\n        const req = new HttpRequest('POST', url, formData, {\n          reportProgress: true\n        }); // create a new progress-subject for every file\n\n        const progress = new Subject();\n        let result = new Subject(); // send the http-request and subscribe for progress-updates\n\n        const startTime = new Date().getTime();\n        this.http.request(req).subscribe(event => {\n          if (event.type === HttpEventType.UploadProgress) {\n            // calculate the progress percentage\n            const percentDone = Math.round(100 * event.loaded / event.total); // pass the percentage into the progress-stream\n\n            progress.next(percentDone);\n          } else if (event.type === HttpEventType.Response) {\n            // Close the progress-stream if we get an answer form the API\n            // The upload is complete\n            result.next(event.body);\n            progress.complete();\n            result.complete();\n          }\n        }); // Save every progress-observable in a map of all observables\n\n        status[file.name] = {\n          progress: progress.asObservable(),\n          result: result.asObservable()\n        };\n      }); // return the map of progress.observables\n\n      return status;\n    }\n\n  }\n\n  UploadService.ɵfac = function UploadService_Factory(t) {\n    return new (t || UploadService)(i0.ɵɵinject(i1.HttpClient));\n  };\n\n  UploadService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: UploadService,\n    factory: UploadService.ɵfac\n  });\n  return UploadService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}