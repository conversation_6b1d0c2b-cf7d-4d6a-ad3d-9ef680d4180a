﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using Microsoft.Owin;
using Microsoft.Owin.Security.Cookies;
using Microsoft.Owin.Security.Google;
using Microsoft.Owin.Security.OAuth;
using Owin;
using Brac.LMS.App.API.Providers;
using Brac.LMS.App.API.Models;
using Brac.LMS.DB;
using Microsoft.Owin.Cors;
using Microsoft.AspNet.Identity.Owin;
using Brac.LMS.Models;
using System.Web.Cors;
using System.Threading.Tasks;

namespace Brac.LMS.App.API
{
    public partial class Startup
    {
        public static OAuthAuthorizationServerOptions OAuthOptions { get; private set; }

        public static string PublicClientId { get; private set; }

        // For more information on configuring authentication, please visit https://go.microsoft.com/fwlink/?LinkId=301864
        public void ConfigureAuth(IAppBuilder app)
        {
            var corsPolicy = new CorsPolicy
            {
                // uncomment on bbl-alo Live
                //Origins = { "https://elearning.bracbank.com" },
                //Origins = { "self" },
                // uncomment on bbl-alo UAT
                //Origins = { "https://************:9007", "https://elearninguat.bracbank.com:9007", "https://*************:9007" },
                // uncomment on bacbon server
                Origins = { "https://bbl-alo.bacbonltd.net" },
                // uncomment on Development
                //Origins = { "https://localhost:4200", "https://localhost:4201" },
                // Origins = { "*" },

                // Allow specific headers
                //Headers = { "Authorization", "Content-Type" },
                Headers = { "Authorization", "Content-Type" },

                // Allow all HTTP methods
                Methods = { "GET", "POST", "PUT", "DELETE" },

                // Allow cookies to be sent across domains
                SupportsCredentials = true

            };

            // Create a new CORS options object and add the policy to it
            var corsOptions = new CorsOptions
            {
                PolicyProvider = new CorsPolicyProvider
                {
                    PolicyResolver = context => Task.FromResult(corsPolicy)
                }
            };

            app.UseCors(corsOptions);
            //app.UseCors(CorsOptions.AllowAll);


            // Configure the db context and user manager to use a single instance per request
            app.CreatePerOwinContext(ApplicationDbContext.Create);
            app.CreatePerOwinContext<ApplicationUserManager>(ApplicationUserManager.Create);
            app.CreatePerOwinContext<ApplicationRoleManager>(ApplicationRoleManager.Create);


            app.Use<AntiXssMiddleware>();

            // Enable the application to use a cookie to store information for the signed in user
            // and to use a cookie to temporarily store information about a user logging in with a third party login provider
            app.UseCookieAuthentication(new CookieAuthenticationOptions
            {
                AuthenticationType = DefaultAuthenticationTypes.ApplicationCookie,
                LoginPath = new PathString("/Token"),
                CookieSecure = CookieSecureOption.Always,
                CookieHttpOnly = true,
                CookieSameSite = SameSiteMode.None,
                Provider = new CookieAuthenticationProvider
                {
                    // Enables the application to validate the security stamp when the user logs in.
                    // This is a security feature which is used when you change a password or add an external login to your account.  
                    OnValidateIdentity = SecurityStampValidator.OnValidateIdentity<ApplicationUserManager, ApplicationUser>(
                        validateInterval: TimeSpan.FromHours(2),
                        regenerateIdentity: (manager, user) => user.GenerateUserIdentityAsync(manager, DefaultAuthenticationTypes.ApplicationCookie))
                },
                ExpireTimeSpan = TimeSpan.FromHours(24)
            });
            app.UseExternalSignInCookie(DefaultAuthenticationTypes.ExternalCookie);

            // Configure the application for OAuth based flow
            PublicClientId = "self";
            OAuthOptions = new OAuthAuthorizationServerOptions
            {
                TokenEndpointPath = new PathString("/Token"),
                Provider = new ApplicationOAuthProvider(PublicClientId),
                AuthorizeEndpointPath = new PathString("/api/Account/ExternalLogin"),
                AccessTokenExpireTimeSpan = TimeSpan.FromHours(24),
                AllowInsecureHttp = false, // uncomment on local development
                //AllowInsecureHttp = true,  
            };

            // Enable the application to use bearer tokens to authenticate users
            app.UseOAuthBearerTokens(OAuthOptions);

            // Uncomment the following lines to enable logging in with third party login providers
            //app.UseMicrosoftAccountAuthentication(
            //    clientId: "",
            //    clientSecret: "");

            //app.UseTwitterAuthentication(
            //    consumerKey: "",
            //    consumerSecret: "");

            //app.UseFacebookAuthentication(
            //    appId: "",
            //    appSecret: "");

            //app.UseGoogleAuthentication(new GoogleOAuth2AuthenticationOptions()
            //{
            //    ClientId = "",
            //    ClientSecret = ""
            //});
        }
    }
}
