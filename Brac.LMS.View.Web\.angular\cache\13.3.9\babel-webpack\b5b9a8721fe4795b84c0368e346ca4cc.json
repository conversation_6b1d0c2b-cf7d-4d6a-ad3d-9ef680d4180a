{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/platform-browser\";\nexport let TitleComponent = /*#__PURE__*/(() => {\n  class TitleComponent {\n    constructor(router, route, titleService) {\n      this.router = router;\n      this.route = route;\n      this.titleService = titleService;\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n        let currentRoute = this.route.root;\n        let title = '';\n\n        do {\n          const childrenRoutes = currentRoute.children;\n          currentRoute = null;\n          childrenRoutes.forEach(routes => {\n            if (routes.outlet === 'primary') {\n              title = routes.snapshot.data.breadcrumb;\n              currentRoute = routes;\n            }\n          });\n        } while (currentRoute);\n\n        this.titleService.setTitle(title + ' | BBL Alternate Learning Outlet (BBL ALO) ');\n      });\n    }\n\n  }\n\n  TitleComponent.ɵfac = function TitleComponent_Factory(t) {\n    return new (t || TitleComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Title));\n  };\n\n  TitleComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TitleComponent,\n    selectors: [[\"app-title\"]],\n    decls: 1,\n    vars: 0,\n    template: function TitleComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"span\");\n      }\n    },\n    encapsulation: 2\n  });\n  return TitleComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}