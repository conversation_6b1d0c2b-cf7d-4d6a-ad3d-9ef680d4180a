{"ast": null, "code": "import { from } from '../observable/from';\nimport { take } from '../operators/take';\nimport { Subject } from '../Subject';\nimport { SafeSubscriber } from '../Subscriber';\nimport { operate } from '../util/lift';\nexport function share(options = {}) {\n  const {\n    connector = () => new Subject(),\n    resetOnError = true,\n    resetOnComplete = true,\n    resetOnRefCountZero = true\n  } = options;\n  return wrapperSource => {\n    let connection = null;\n    let resetConnection = null;\n    let subject = null;\n    let refCount = 0;\n    let hasCompleted = false;\n    let hasErrored = false;\n\n    const cancelReset = () => {\n      resetConnection === null || resetConnection === void 0 ? void 0 : resetConnection.unsubscribe();\n      resetConnection = null;\n    };\n\n    const reset = () => {\n      cancelReset();\n      connection = subject = null;\n      hasCompleted = hasErrored = false;\n    };\n\n    const resetAndUnsubscribe = () => {\n      const conn = connection;\n      reset();\n      conn === null || conn === void 0 ? void 0 : conn.unsubscribe();\n    };\n\n    return operate((source, subscriber) => {\n      refCount++;\n\n      if (!hasErrored && !hasCompleted) {\n        cancelReset();\n      }\n\n      const dest = subject = subject !== null && subject !== void 0 ? subject : connector();\n      subscriber.add(() => {\n        refCount--;\n\n        if (refCount === 0 && !hasErrored && !hasCompleted) {\n          resetConnection = handleReset(resetAndUnsubscribe, resetOnRefCountZero);\n        }\n      });\n      dest.subscribe(subscriber);\n\n      if (!connection) {\n        connection = new SafeSubscriber({\n          next: value => dest.next(value),\n          error: err => {\n            hasErrored = true;\n            cancelReset();\n            resetConnection = handleReset(reset, resetOnError, err);\n            dest.error(err);\n          },\n          complete: () => {\n            hasCompleted = true;\n            cancelReset();\n            resetConnection = handleReset(reset, resetOnComplete);\n            dest.complete();\n          }\n        });\n        from(source).subscribe(connection);\n      }\n    })(wrapperSource);\n  };\n}\n\nfunction handleReset(reset, on, ...args) {\n  if (on === true) {\n    reset();\n    return null;\n  }\n\n  if (on === false) {\n    return null;\n  }\n\n  return on(...args).pipe(take(1)).subscribe(() => reset());\n} //# sourceMappingURL=share.js.map", "map": null, "metadata": {}, "sourceType": "module"}