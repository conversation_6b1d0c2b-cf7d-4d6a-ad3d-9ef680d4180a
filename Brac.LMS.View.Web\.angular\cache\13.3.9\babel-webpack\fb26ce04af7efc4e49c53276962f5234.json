{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { operate } from '../util/lift';\nexport function catchError(selector) {\n  return operate((source, subscriber) => {\n    let innerSub = null;\n    let syncUnsub = false;\n    let handledResult;\n    innerSub = source.subscribe(new OperatorSubscriber(subscriber, undefined, undefined, err => {\n      handledResult = innerFrom(selector(err, catchError(selector)(source)));\n\n      if (innerSub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        handledResult.subscribe(subscriber);\n      } else {\n        syncUnsub = true;\n      }\n    }));\n\n    if (syncUnsub) {\n      innerSub.unsubscribe();\n      innerSub = null;\n      handledResult.subscribe(subscriber);\n    }\n  });\n} //# sourceMappingURL=catchError.js.map", "map": null, "metadata": {}, "sourceType": "module"}