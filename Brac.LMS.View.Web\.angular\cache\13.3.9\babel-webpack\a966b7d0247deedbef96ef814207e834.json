{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let PaginationService = /*#__PURE__*/(() => {\n  class PaginationService {\n    constructor() {\n      // This will be used to emit the current page number\n      this.currentPage = new Subject();\n      this.previousPage = new Subject();\n    }\n\n  }\n\n  PaginationService.ɵfac = function PaginationService_Factory(t) {\n    return new (t || PaginationService)();\n  };\n\n  PaginationService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PaginationService,\n    factory: PaginationService.ɵfac,\n    providedIn: 'root'\n  });\n  return PaginationService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}