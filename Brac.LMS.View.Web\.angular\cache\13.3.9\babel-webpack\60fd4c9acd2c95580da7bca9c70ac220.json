{"ast": null, "code": "/**\n * @license ngx-smart-modal\n * MIT license\n */\nimport { ApplicationRef, ChangeDetectorRef, Component, ComponentFactoryResolver, EventEmitter, HostListener, Inject, Injectable, Injector, Input, NgModule, Output, PLATFORM_ID, Renderer2, TemplateRef, Type, ViewChildren, ViewContainerRef } from '@angular/core';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/common';\nconst _c0 = [\"nsmContent\"];\nconst _c1 = [\"nsmDialog\"];\nconst _c2 = [\"nsmOverlay\"];\nconst _c3 = [\"dynamicContent\"];\n\nfunction NgxSmartModalComponent_div_0_ng_template_7_Template(rf, ctx) {}\n\nfunction NgxSmartModalComponent_div_0_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"button\", 10);\n    ɵngcc0.ɵɵlistener(\"click\", function NgxSmartModalComponent_div_0_button_10_Template_button_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r8);\n      const ctx_r7 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r7.close();\n    });\n    ɵngcc0.ɵɵnamespaceSVG();\n    ɵngcc0.ɵɵelementStart(1, \"svg\", 11);\n    ɵngcc0.ɵɵelementStart(2, \"g\");\n    ɵngcc0.ɵɵelement(3, \"path\", 12);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(4, \"g\");\n    ɵngcc0.ɵɵelement(5, \"path\", 13);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n}\n\nconst _c4 = function (a0, a2) {\n  return {\n    \"transparent\": a0,\n    \"overlay\": true,\n    \"nsm-overlay-open\": a2\n  };\n};\n\nconst _c5 = function (a1, a2) {\n  return [\"nsm-dialog\", a1, a2];\n};\n\nfunction NgxSmartModalComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"div\", 1, 2);\n    ɵngcc0.ɵɵlistener(\"mousedown\", function NgxSmartModalComponent_div_0_Template_div_mousedown_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r10);\n      const ctx_r9 = ɵngcc0.ɵɵnextContext();\n      return ctx_r9.dismiss($event);\n    });\n    ɵngcc0.ɵɵelementStart(2, \"div\", 3, 4);\n    ɵngcc0.ɵɵelementStart(4, \"div\", 5, 6);\n    ɵngcc0.ɵɵelementStart(6, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(7, NgxSmartModalComponent_div_0_ng_template_7_Template, 0, 0, \"ng-template\", null, 8, ɵngcc0.ɵɵtemplateRefExtractor);\n    ɵngcc0.ɵɵprojection(9);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵtemplate(10, NgxSmartModalComponent_div_0_button_10_Template, 6, 0, \"button\", 9);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵstyleProp(\"z-index\", ctx_r0.visible ? ctx_r0.layerPosition - 1 : -1);\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpureFunction2(11, _c4, !ctx_r0.backdrop, ctx_r0.openedClass));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵstyleProp(\"z-index\", ctx_r0.visible ? ctx_r0.layerPosition : -1);\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpureFunction2(14, _c5, ctx_r0.customClass, ctx_r0.openedClass ? \"nsm-dialog-open\" : \"nsm-dialog-close\"));\n    ɵngcc0.ɵɵattribute(\"aria-hidden\", ctx_r0.openedClass ? false : true)(\"aria-label\", ctx_r0.ariaLabel)(\"aria-labelledby\", ctx_r0.ariaLabelledBy)(\"aria-describedby\", ctx_r0.ariaDescribedBy);\n    ɵngcc0.ɵɵadvance(8);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r0.closable);\n  }\n}\n\nconst _c6 = [\"*\"];\nconst NgxSmartModalConfig = {\n  bodyClassOpen: 'dialog-open',\n  prefixEvent: 'ngx-smart-modal.'\n};\n/**\n * @record\n */\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\nlet NgxSmartModalComponent = /*#__PURE__*/(() => {\n  class NgxSmartModalComponent {\n    /**\n     * @param {?} _renderer\n     * @param {?} _changeDetectorRef\n     * @param {?} componentFactoryResolver\n     * @param {?} _document\n     * @param {?} _platformId\n     */\n    constructor(_renderer, _changeDetectorRef, componentFactoryResolver, _document, _platformId) {\n      this._renderer = _renderer;\n      this._changeDetectorRef = _changeDetectorRef;\n      this.componentFactoryResolver = componentFactoryResolver;\n      this._document = _document;\n      this._platformId = _platformId;\n      this.closable = true;\n      this.escapable = true;\n      this.dismissable = true;\n      this.identifier = '';\n      this.customClass = 'nsm-dialog-animation-fade';\n      this.visible = false;\n      this.backdrop = true;\n      this.force = true;\n      this.hideDelay = 500;\n      this.autostart = false;\n      this.target = '';\n      this.ariaLabel = null;\n      this.ariaLabelledBy = null;\n      this.ariaDescribedBy = null;\n      this.refocus = true;\n      this.visibleChange = new EventEmitter();\n      this.onClose = new EventEmitter();\n      this.onCloseFinished = new EventEmitter();\n      this.onDismiss = new EventEmitter();\n      this.onDismissFinished = new EventEmitter();\n      this.onAnyCloseEvent = new EventEmitter();\n      this.onAnyCloseEventFinished = new EventEmitter();\n      this.onOpen = new EventEmitter();\n      this.onOpenFinished = new EventEmitter();\n      this.onEscape = new EventEmitter();\n      this.onDataAdded = new EventEmitter();\n      this.onDataRemoved = new EventEmitter();\n      this.layerPosition = 1041;\n      this.overlayVisible = false;\n      this.openedClass = false;\n      this.createFrom = 'html';\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnInit() {\n      if (!this.identifier || !this.identifier.length) {\n        throw new Error('identifier field isn’t set. Please set one before calling <ngx-smart-modal> in a template.');\n      }\n\n      this._sendEvent('create');\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngAfterViewInit() {\n      if (this.contentComponent) {\n        const\n        /** @type {?} */\n        factory = this.componentFactoryResolver.resolveComponentFactory(this.contentComponent);\n        this.createDynamicContent(this.dynamicContentContainer, factory);\n        this.dynamicContentContainer.changes.subscribe(contentViewContainers => {\n          this.createDynamicContent(contentViewContainers, factory);\n        });\n      }\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnDestroy() {\n      this._sendEvent('delete');\n    }\n    /**\n     * Open the modal instance\n     *\n     * @param {?=} top open the modal top of all other\n     * @return {?} the modal component\n     */\n\n\n    open(top) {\n      this._sendEvent('open', {\n        top: top\n      });\n\n      return this;\n    }\n    /**\n     * Close the modal instance\n     *\n     * @return {?} the modal component\n     */\n\n\n    close() {\n      this._sendEvent('close');\n\n      return this;\n    }\n    /**\n     * Dismiss the modal instance\n     *\n     * @param {?} e the event sent by the browser\n     * @return {?} the modal component\n     */\n\n\n    dismiss(e) {\n      if (!this.dismissable || !e.target.classList.contains('overlay')) {\n        return this;\n      }\n\n      this._sendEvent('dismiss');\n\n      return this;\n    }\n    /**\n     * Toggle visibility of the modal instance\n     *\n     * @param {?=} top open the modal top of all other\n     * @return {?} the modal component\n     */\n\n\n    toggle(top) {\n      this._sendEvent('toggle', {\n        top: top\n      });\n\n      return this;\n    }\n    /**\n     * Add a custom class to the modal instance\n     *\n     * @param {?} className the class to add\n     * @return {?} the modal component\n     */\n\n\n    addCustomClass(className) {\n      if (!this.customClass.length) {\n        this.customClass = className;\n      } else {\n        this.customClass += ' ' + className;\n      }\n\n      return this;\n    }\n    /**\n     * Remove a custom class to the modal instance\n     *\n     * @param {?=} className the class to remove\n     * @return {?} the modal component\n     */\n\n\n    removeCustomClass(className) {\n      if (className) {\n        this.customClass = this.customClass.replace(className, '').trim();\n      } else {\n        this.customClass = '';\n      }\n\n      return this;\n    }\n    /**\n     * Returns the visibility state of the modal instance\n     * @return {?}\n     */\n\n\n    isVisible() {\n      return this.visible;\n    }\n    /**\n     * Checks if data is attached to the modal instance\n     * @return {?}\n     */\n\n\n    hasData() {\n      return this._data !== undefined;\n    }\n    /**\n     * Attach data to the modal instance\n     *\n     * @param {?} data the data to attach\n     * @param {?=} force override potentially attached data\n     * @return {?} the modal component\n     */\n\n\n    setData(data, force) {\n      if (!this.hasData() || this.hasData() && force) {\n        this._data = data;\n        this.onDataAdded.emit(this._data);\n        this.markForCheck();\n      }\n\n      return this;\n    }\n    /**\n     * Retrieve the data attached to the modal instance\n     * @return {?}\n     */\n\n\n    getData() {\n      return this._data;\n    }\n    /**\n     * Remove the data attached to the modal instance\n     *\n     * @return {?} the modal component\n     */\n\n\n    removeData() {\n      this._data = undefined;\n      this.onDataRemoved.emit(true);\n      this.markForCheck();\n      return this;\n    }\n    /**\n     * Add body class modal opened\n     *\n     * @return {?} the modal component\n     */\n\n\n    addBodyClass() {\n      this._renderer.addClass(this._document.body, NgxSmartModalConfig.bodyClassOpen);\n\n      return this;\n    }\n    /**\n     * Add body class modal opened\n     *\n     * @return {?} the modal component\n     */\n\n\n    removeBodyClass() {\n      this._renderer.removeClass(this._document.body, NgxSmartModalConfig.bodyClassOpen);\n\n      return this;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    markForCheck() {\n      try {\n        this._changeDetectorRef.detectChanges();\n      } catch (\n      /** @type {?} */\n      e) {}\n\n      this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Listens for window resize event and recalculates modal instance position if it is element-relative\n     * @return {?}\n     */\n\n\n    targetPlacement() {\n      if (!this.isBrowser || !this.nsmDialog.length || !this.nsmContent.length || !this.nsmOverlay.length || !this.target) {\n        return false;\n      }\n\n      const\n      /** @type {?} */\n      targetElement = this._document.querySelector(this.target);\n\n      if (!targetElement) {\n        return false;\n      }\n\n      const\n      /** @type {?} */\n      targetElementRect = targetElement.getBoundingClientRect();\n      const\n      /** @type {?} */\n      bodyRect = this.nsmOverlay.first.nativeElement.getBoundingClientRect();\n      const\n      /** @type {?} */\n      nsmContentRect = this.nsmContent.first.nativeElement.getBoundingClientRect();\n      const\n      /** @type {?} */\n      nsmDialogRect = this.nsmDialog.first.nativeElement.getBoundingClientRect();\n      const\n      /** @type {?} */\n      marginLeft = parseInt(\n      /** @type {?} */\n      getComputedStyle(this.nsmContent.first.nativeElement).marginLeft, 10);\n      const\n      /** @type {?} */\n      marginTop = parseInt(\n      /** @type {?} */\n      getComputedStyle(this.nsmContent.first.nativeElement).marginTop, 10);\n      let\n      /** @type {?} */\n      offsetTop = targetElementRect.top - nsmDialogRect.top - (nsmContentRect.height - targetElementRect.height) / 2;\n      let\n      /** @type {?} */\n      offsetLeft = targetElementRect.left - nsmDialogRect.left - (nsmContentRect.width - targetElementRect.width) / 2;\n\n      if (offsetLeft + nsmDialogRect.left + nsmContentRect.width + marginLeft * 2 > bodyRect.width) {\n        offsetLeft = bodyRect.width - (nsmDialogRect.left + nsmContentRect.width) - marginLeft * 2;\n      } else if (offsetLeft + nsmDialogRect.left < 0) {\n        offsetLeft = -nsmDialogRect.left;\n      }\n\n      if (offsetTop + nsmDialogRect.top + nsmContentRect.height + marginTop > bodyRect.height) {\n        offsetTop = bodyRect.height - (nsmDialogRect.top + nsmContentRect.height) - marginTop;\n      }\n\n      this._renderer.setStyle(this.nsmContent.first.nativeElement, 'top', (offsetTop < 0 ? 0 : offsetTop) + 'px');\n\n      this._renderer.setStyle(this.nsmContent.first.nativeElement, 'left', offsetLeft + 'px');\n    }\n    /**\n     * @param {?} name\n     * @param {?=} extraData\n     * @return {?}\n     */\n\n\n    _sendEvent(name, extraData) {\n      if (!this.isBrowser) {\n        return false;\n      }\n\n      const\n      /** @type {?} */\n      data = {\n        extraData: extraData,\n        instance: {\n          id: this.identifier,\n          modal: this\n        }\n      };\n      const\n      /** @type {?} */\n      event = new CustomEvent(NgxSmartModalConfig.prefixEvent + name, {\n        detail: data\n      });\n      return window.dispatchEvent(event);\n    }\n    /**\n     * Is current platform browser\n     * @return {?}\n     */\n\n\n    get isBrowser() {\n      return isPlatformBrowser(this._platformId);\n    }\n    /**\n     * Creates content inside provided ViewContainerRef\n     * @param {?} changes\n     * @param {?} factory\n     * @return {?}\n     */\n\n\n    createDynamicContent(changes, factory) {\n      changes.forEach(viewContainerRef => {\n        viewContainerRef.clear();\n        viewContainerRef.createComponent(factory);\n        this.markForCheck();\n      });\n    }\n\n  }\n\n  NgxSmartModalComponent.ɵfac = function NgxSmartModalComponent_Factory(t) {\n    return new (t || NgxSmartModalComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ComponentFactoryResolver), ɵngcc0.ɵɵdirectiveInject(DOCUMENT), ɵngcc0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n\n  NgxSmartModalComponent.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n    type: NgxSmartModalComponent,\n    selectors: [[\"ngx-smart-modal\"]],\n    viewQuery: function NgxSmartModalComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵviewQuery(_c0, 5);\n        ɵngcc0.ɵɵviewQuery(_c1, 5);\n        ɵngcc0.ɵɵviewQuery(_c2, 5);\n        ɵngcc0.ɵɵviewQuery(_c3, 5, ViewContainerRef);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.nsmContent = _t);\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.nsmDialog = _t);\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.nsmOverlay = _t);\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.dynamicContentContainer = _t);\n      }\n    },\n    hostBindings: function NgxSmartModalComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"resize\", function NgxSmartModalComponent_resize_HostBindingHandler() {\n          return ctx.targetPlacement();\n        }, false, ɵngcc0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      closable: \"closable\",\n      escapable: \"escapable\",\n      dismissable: \"dismissable\",\n      identifier: \"identifier\",\n      customClass: \"customClass\",\n      visible: \"visible\",\n      backdrop: \"backdrop\",\n      force: \"force\",\n      hideDelay: \"hideDelay\",\n      autostart: \"autostart\",\n      target: \"target\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      ariaDescribedBy: \"ariaDescribedBy\",\n      refocus: \"refocus\"\n    },\n    outputs: {\n      visibleChange: \"visibleChange\",\n      onClose: \"onClose\",\n      onCloseFinished: \"onCloseFinished\",\n      onDismiss: \"onDismiss\",\n      onDismissFinished: \"onDismissFinished\",\n      onAnyCloseEvent: \"onAnyCloseEvent\",\n      onAnyCloseEventFinished: \"onAnyCloseEventFinished\",\n      onOpen: \"onOpen\",\n      onOpenFinished: \"onOpenFinished\",\n      onEscape: \"onEscape\",\n      onDataAdded: \"onDataAdded\",\n      onDataRemoved: \"onDataRemoved\"\n    },\n    ngContentSelectors: _c6,\n    decls: 1,\n    vars: 1,\n    consts: [[3, \"z-index\", \"ngClass\", \"mousedown\", 4, \"ngIf\"], [3, \"ngClass\", \"mousedown\"], [\"nsmOverlay\", \"\"], [3, \"ngClass\"], [\"nsmDialog\", \"\"], [1, \"nsm-content\"], [\"nsmContent\", \"\"], [1, \"nsm-body\"], [\"dynamicContent\", \"\"], [\"type\", \"button\", \"aria-label\", \"Close\", \"class\", \"nsm-dialog-btn-close\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"nsm-dialog-btn-close\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"version\", \"1.1\", \"id\", \"Layer_1\", \"x\", \"0px\", \"y\", \"0px\", \"viewBox\", \"0 0 512 512\", 0, \"xml\", \"space\", \"preserve\", \"width\", \"16px\", \"height\", \"16px\"], [\"d\", \"M505.943,6.058c-8.077-8.077-21.172-8.077-29.249,0L6.058,476.693c-8.077,8.077-8.077,21.172,0,29.249    C10.096,509.982,15.39,512,20.683,512c5.293,0,10.586-2.019,14.625-6.059L505.943,35.306    C514.019,27.23,514.019,14.135,505.943,6.058z\", \"fill\", \"currentColor\"], [\"d\", \"M505.942,476.694L35.306,6.059c-8.076-8.077-21.172-8.077-29.248,0c-8.077,8.076-8.077,21.171,0,29.248l470.636,470.636    c4.038,4.039,9.332,6.058,14.625,6.058c5.293,0,10.587-2.019,14.624-6.057C514.018,497.866,514.018,484.771,505.942,476.694z\", \"fill\", \"currentColor\"]],\n    template: function NgxSmartModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵtemplate(0, NgxSmartModalComponent_div_0_Template, 11, 17, \"div\", 0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.overlayVisible);\n      }\n    },\n    directives: [ɵngcc1.NgIf, ɵngcc1.NgClass],\n    encapsulation: 2\n  });\n  /** @nocollapse */\n\n  return NgxSmartModalComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\n\nlet NgxSmartModalStackService = /*#__PURE__*/(() => {\n  class NgxSmartModalStackService {\n    constructor() {\n      this._modalStack = [];\n    }\n    /**\n     * Add a new modal instance. This step is essential and allows to retrieve any modal at any time.\n     * It stores an object that contains the given modal identifier and the modal itself directly in the `modalStack`.\n     *\n     * @param {?} modalInstance The object that contains the given modal identifier and the modal itself.\n     * @param {?=} force Optional parameter that forces the overriding of modal instance if it already exists.\n     * @return {?} nothing special.\n     */\n\n\n    addModal(modalInstance, force) {\n      if (force) {\n        const\n        /** @type {?} */\n        i = this._modalStack.findIndex(o => o.id === modalInstance.id);\n\n        if (i > -1) {\n          this._modalStack[i].modal = modalInstance.modal;\n        } else {\n          this._modalStack.push(modalInstance);\n        }\n\n        return;\n      }\n\n      this._modalStack.push(modalInstance);\n    }\n    /**\n     * Retrieve a modal instance by its identifier.\n     *\n     * @param {?} id The modal identifier used at creation time.\n     * @return {?}\n     */\n\n\n    getModal(id) {\n      const\n      /** @type {?} */\n      i = this._modalStack.find(o => o.id === id);\n\n      if (i !== undefined) {\n        return i.modal;\n      } else {\n        throw new Error(`Cannot find modal with identifier ${id}`);\n      }\n    }\n    /**\n     * Retrieve all the created modals.\n     *\n     * @return {?} an array that contains all modal instances.\n     */\n\n\n    getModalStack() {\n      return this._modalStack;\n    }\n    /**\n     * Retrieve all the opened modals. It looks for all modal instances with their `visible` property set to `true`.\n     *\n     * @return {?} an array that contains all the opened modals.\n     */\n\n\n    getOpenedModals() {\n      return this._modalStack.filter(o => o.modal.visible);\n    }\n    /**\n     * Retrieve the opened modal with highest z-index.\n     *\n     * @return {?} the opened modal with highest z-index.\n     */\n\n\n    getTopOpenedModal() {\n      if (!this.getOpenedModals().length) {\n        throw new Error('No modal is opened');\n      }\n\n      return this.getOpenedModals().map(o => o.modal).reduce((highest, item) => item.layerPosition > highest.layerPosition ? item : highest, this.getOpenedModals()[0].modal);\n    }\n    /**\n     * Get the higher `z-index` value between all the modal instances. It iterates over the `ModalStack` array and\n     * calculates a higher value (it takes the highest index value between all the modal instances and adds 1).\n     * Use it to make a modal appear foreground.\n     *\n     * @return {?} a higher index from all the existing modal instances.\n     */\n\n\n    getHigherIndex() {\n      return Math.max(...this._modalStack.map(o => o.modal.layerPosition), 1041) + 1;\n    }\n    /**\n     * It gives the number of modal instances. It's helpful to know if the modal stack is empty or not.\n     *\n     * @return {?} the number of modal instances.\n     */\n\n\n    getModalStackCount() {\n      return this._modalStack.length;\n    }\n    /**\n     * Remove a modal instance from the modal stack.\n     *\n     * @param {?} id The modal identifier.\n     * @return {?} the removed modal instance.\n     */\n\n\n    removeModal(id) {\n      const\n      /** @type {?} */\n      i = this._modalStack.findIndex(o => o.id === id);\n\n      if (i > -1) {\n        this._modalStack.splice(i, 1);\n      }\n    }\n\n  }\n\n  NgxSmartModalStackService.ɵfac = function NgxSmartModalStackService_Factory(t) {\n    return new (t || NgxSmartModalStackService)();\n  };\n\n  NgxSmartModalStackService.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n    token: NgxSmartModalStackService,\n    factory: NgxSmartModalStackService.ɵfac\n  });\n  /** @nocollapse */\n\n  return NgxSmartModalStackService;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\n\nlet NgxSmartModalService = /*#__PURE__*/(() => {\n  class NgxSmartModalService {\n    /**\n     * @param {?} _componentFactoryResolver\n     * @param {?} _appRef\n     * @param {?} _injector\n     * @param {?} _modalStack\n     * @param {?} applicationRef\n     * @param {?} _document\n     * @param {?} _platformId\n     */\n    constructor(_componentFactoryResolver, _appRef, _injector, _modalStack, applicationRef, _document, _platformId) {\n      this._componentFactoryResolver = _componentFactoryResolver;\n      this._appRef = _appRef;\n      this._injector = _injector;\n      this._modalStack = _modalStack;\n      this.applicationRef = applicationRef;\n      this._document = _document;\n      this._platformId = _platformId;\n      /**\n       * Close the latest opened modal if escape key event is emitted\n       * @param event The Keyboard Event\n       */\n\n      this._escapeKeyboardEvent = event => {\n        if (event.key === 'Escape') {\n          try {\n            const\n            /** @type {?} */\n            modal = this.getTopOpenedModal();\n\n            if (!modal.escapable) {\n              return false;\n            }\n\n            modal.onEscape.emit(modal);\n            this.closeLatestModal();\n            return true;\n          } catch (\n          /** @type {?} */\n          e) {\n            return false;\n          }\n        }\n\n        return false;\n      };\n      /**\n       * While modal is open, the focus stay on it\n       * @param event The Keyboar dEvent\n       */\n\n\n      this._trapFocusModal = event => {\n        if (event.key === 'Tab') {\n          try {\n            const\n            /** @type {?} */\n            modal = this.getTopOpenedModal();\n\n            if (!modal.nsmDialog.first.nativeElement.contains(document.activeElement)) {\n              event.preventDefault();\n              event.stopPropagation();\n              modal.nsmDialog.first.nativeElement.focus();\n            }\n\n            return true;\n          } catch (\n          /** @type {?} */\n          e) {\n            return false;\n          }\n        }\n\n        return false;\n      };\n\n      this._addEvents();\n    }\n    /**\n     * Add a new modal instance. This step is essential and allows to retrieve any modal at any time.\n     * It stores an object that contains the given modal identifier and the modal itself directly in the `modalStack`.\n     *\n     * @param {?} modalInstance The object that contains the given modal identifier and the modal itself.\n     * @param {?=} force Optional parameter that forces the overriding of modal instance if it already exists.\n     * @return {?} nothing special.\n     */\n\n\n    addModal(modalInstance, force) {\n      this._modalStack.addModal(modalInstance, force);\n    }\n    /**\n     * Retrieve a modal instance by its identifier.\n     *\n     * @param {?} id The modal identifier used at creation time.\n     * @return {?}\n     */\n\n\n    getModal(id) {\n      return this._modalStack.getModal(id);\n    }\n    /**\n     * Alias of `getModal` to retrieve a modal instance by its identifier.\n     *\n     * @param {?} id The modal identifier used at creation time.\n     * @return {?}\n     */\n\n\n    get(id) {\n      return this.getModal(id);\n    }\n    /**\n     * Open a given modal\n     *\n     * @param {?} id The modal identifier used at creation time.\n     * @param {?=} force Tell the modal to open top of all other opened modals\n     * @return {?}\n     */\n\n\n    open(id, force = false) {\n      return this._openModal(this.get(id), force);\n    }\n    /**\n     * Close a given modal\n     *\n     * @param {?} id The modal identifier used at creation time.\n     * @return {?}\n     */\n\n\n    close(id) {\n      return this._closeModal(this.get(id));\n    }\n    /**\n     * Close all opened modals\n     * @return {?}\n     */\n\n\n    closeAll() {\n      this.getOpenedModals().forEach(instance => {\n        this._closeModal(instance.modal);\n      });\n    }\n    /**\n     * Toggles a given modal\n     * If the retrieved modal is opened it closes it, else it opens it.\n     *\n     * @param {?} id The modal identifier used at creation time.\n     * @param {?=} force Tell the modal to open top of all other opened modals\n     * @return {?}\n     */\n\n\n    toggle(id, force = false) {\n      return this._toggleModal(this.get(id), force);\n    }\n    /**\n     * Retrieve all the created modals.\n     *\n     * @return {?} an array that contains all modal instances.\n     */\n\n\n    getModalStack() {\n      return this._modalStack.getModalStack();\n    }\n    /**\n     * Retrieve all the opened modals. It looks for all modal instances with their `visible` property set to `true`.\n     *\n     * @return {?} an array that contains all the opened modals.\n     */\n\n\n    getOpenedModals() {\n      return this._modalStack.getOpenedModals();\n    }\n    /**\n     * Retrieve the opened modal with highest z-index.\n     *\n     * @return {?} the opened modal with highest z-index.\n     */\n\n\n    getTopOpenedModal() {\n      return this._modalStack.getTopOpenedModal();\n    }\n    /**\n     * Get the higher `z-index` value between all the modal instances. It iterates over the `ModalStack` array and\n     * calculates a higher value (it takes the highest index value between all the modal instances and adds 1).\n     * Use it to make a modal appear foreground.\n     *\n     * @return {?} a higher index from all the existing modal instances.\n     */\n\n\n    getHigherIndex() {\n      return this._modalStack.getHigherIndex();\n    }\n    /**\n     * It gives the number of modal instances. It's helpful to know if the modal stack is empty or not.\n     *\n     * @return {?} the number of modal instances.\n     */\n\n\n    getModalStackCount() {\n      return this._modalStack.getModalStackCount();\n    }\n    /**\n     * Remove a modal instance from the modal stack.\n     *\n     * @param {?} id The modal identifier.\n     * @return {?} the removed modal instance.\n     */\n\n\n    removeModal(id) {\n      this._modalStack.removeModal(id);\n    }\n    /**\n     * Associate data to an identified modal. If the modal isn't already associated to some data, it creates a new\n     * entry in the `modalData` array with its `id` and the given `data`. If the modal already has data, it rewrites\n     * them with the new ones. Finally if no modal found it returns an error message in the console and false value\n     * as method output.\n     *\n     * @param {?} data The data you want to associate to the modal.\n     * @param {?} id The modal identifier.\n     * @param {?=} force If true, overrides the previous stored data if there was.\n     * @return {?} true if the given modal exists and the process has been tried, either false.\n     */\n\n\n    setModalData(data, id, force) {\n      let\n      /** @type {?} */\n      i;\n\n      if (i = this.get(id)) {\n        i.setData(data, force);\n        return true;\n      } else {\n        return false;\n      }\n    }\n    /**\n     * Retrieve modal data by its identifier.\n     *\n     * @param {?} id The modal identifier used at creation time.\n     * @return {?} the associated modal data.\n     */\n\n\n    getModalData(id) {\n      let\n      /** @type {?} */\n      i;\n\n      if (i = this.get(id)) {\n        return i.getData();\n      }\n\n      return null;\n    }\n    /**\n     * Reset the data attached to a given modal.\n     *\n     * @param {?} id The modal identifier used at creation time.\n     * @return {?} the removed data or false if modal doesn't exist.\n     */\n\n\n    resetModalData(id) {\n      if (!!this._modalStack.getModalStack().find(o => o.id === id)) {\n        const\n        /** @type {?} */\n        removed = this.getModal(id).getData();\n        this.getModal(id).removeData();\n        return removed;\n      } else {\n        return false;\n      }\n    }\n    /**\n     * Close the latest opened modal if it has been declared as escapable\n     * Using a debounce system because one or more modals could be listening\n     * escape key press event.\n     * @return {?}\n     */\n\n\n    closeLatestModal() {\n      this.getTopOpenedModal().close();\n    }\n    /**\n     * Create dynamic NgxSmartModalComponent\n     * @template T\n     * @param {?} id The modal identifier used at creation time.\n     * @param {?} content The modal content ( string, templateRef or Component )\n     * @param {?=} options\n     * @return {?}\n     */\n\n\n    create(id, content, options = {}) {\n      try {\n        return this.getModal(id);\n      } catch (\n      /** @type {?} */\n      e) {\n        const\n        /** @type {?} */\n        componentFactory = this._componentFactoryResolver.resolveComponentFactory(NgxSmartModalComponent);\n\n        const\n        /** @type {?} */\n        ngContent = this._resolveNgContent(content);\n\n        const\n        /** @type {?} */\n        componentRef = componentFactory.create(this._injector, ngContent);\n\n        if (content instanceof Type) {\n          componentRef.instance.contentComponent = content;\n        }\n\n        componentRef.instance.identifier = id;\n        componentRef.instance.createFrom = 'service';\n\n        if (typeof options.closable === 'boolean') {\n          componentRef.instance.closable = options.closable;\n        }\n\n        if (typeof options.escapable === 'boolean') {\n          componentRef.instance.escapable = options.escapable;\n        }\n\n        if (typeof options.dismissable === 'boolean') {\n          componentRef.instance.dismissable = options.dismissable;\n        }\n\n        if (typeof options.customClass === 'string') {\n          componentRef.instance.customClass = options.customClass;\n        }\n\n        if (typeof options.backdrop === 'boolean') {\n          componentRef.instance.backdrop = options.backdrop;\n        }\n\n        if (typeof options.force === 'boolean') {\n          componentRef.instance.force = options.force;\n        }\n\n        if (typeof options.hideDelay === 'number') {\n          componentRef.instance.hideDelay = options.hideDelay;\n        }\n\n        if (typeof options.autostart === 'boolean') {\n          componentRef.instance.autostart = options.autostart;\n        }\n\n        if (typeof options.target === 'string') {\n          componentRef.instance.target = options.target;\n        }\n\n        if (typeof options.ariaLabel === 'string') {\n          componentRef.instance.ariaLabel = options.ariaLabel;\n        }\n\n        if (typeof options.ariaLabelledBy === 'string') {\n          componentRef.instance.ariaLabelledBy = options.ariaLabelledBy;\n        }\n\n        if (typeof options.ariaDescribedBy === 'string') {\n          componentRef.instance.ariaDescribedBy = options.ariaDescribedBy;\n        }\n\n        if (typeof options.refocus === 'boolean') {\n          componentRef.instance.refocus = options.refocus;\n        }\n\n        this._appRef.attachView(componentRef.hostView);\n\n        const\n        /** @type {?} */\n        domElem =\n        /** @type {?} */\n\n        /** @type {?} */\n        componentRef.hostView.rootNodes[0];\n\n        this._document.body.appendChild(domElem);\n\n        return componentRef.instance;\n      }\n    }\n    /**\n     * @return {?}\n     */\n\n\n    _addEvents() {\n      if (!this.isBrowser) {\n        return false;\n      }\n\n      window.addEventListener(NgxSmartModalConfig.prefixEvent + 'create',\n      /** @type {?} */\n      e => {\n        this._initModal(e.detail.instance);\n      });\n      window.addEventListener(NgxSmartModalConfig.prefixEvent + 'delete',\n      /** @type {?} */\n      e => {\n        this._deleteModal(e.detail.instance);\n      });\n      window.addEventListener(NgxSmartModalConfig.prefixEvent + 'open',\n      /** @type {?} */\n      e => {\n        this._openModal(e.detail.instance.modal, e.detail.top);\n      });\n      window.addEventListener(NgxSmartModalConfig.prefixEvent + 'toggle',\n      /** @type {?} */\n      e => {\n        this._toggleModal(e.detail.instance.modal, e.detail.top);\n      });\n      window.addEventListener(NgxSmartModalConfig.prefixEvent + 'close',\n      /** @type {?} */\n      e => {\n        this._closeModal(e.detail.instance.modal);\n      });\n      window.addEventListener(NgxSmartModalConfig.prefixEvent + 'dismiss',\n      /** @type {?} */\n      e => {\n        this._dismissModal(e.detail.instance.modal);\n      });\n      window.addEventListener('keyup', this._escapeKeyboardEvent);\n      return true;\n    }\n    /**\n     * @param {?} modalInstance\n     * @return {?}\n     */\n\n\n    _initModal(modalInstance) {\n      modalInstance.modal.layerPosition += this.getModalStackCount();\n      this.addModal(modalInstance, modalInstance.modal.force);\n\n      if (modalInstance.modal.autostart) {\n        this.open(modalInstance.id);\n      }\n    }\n    /**\n     * @param {?} modal\n     * @param {?=} top\n     * @return {?}\n     */\n\n\n    _openModal(modal, top) {\n      if (modal.visible) {\n        return false;\n      }\n\n      this.lastElementFocused = document.activeElement;\n\n      if (modal.escapable) {\n        window.addEventListener('keyup', this._escapeKeyboardEvent);\n      }\n\n      if (modal.backdrop) {\n        window.addEventListener('keydown', this._trapFocusModal);\n      }\n\n      if (top) {\n        modal.layerPosition = this.getHigherIndex();\n      }\n\n      modal.addBodyClass();\n      modal.overlayVisible = true;\n      modal.visible = true;\n      modal.onOpen.emit(modal);\n      modal.markForCheck();\n      setTimeout(() => {\n        modal.openedClass = true;\n\n        if (modal.target) {\n          modal.targetPlacement();\n        }\n\n        modal.nsmDialog.first.nativeElement.setAttribute('role', 'dialog');\n        modal.nsmDialog.first.nativeElement.setAttribute('tabIndex', '-1');\n        modal.nsmDialog.first.nativeElement.setAttribute('aria-modal', 'true');\n        modal.nsmDialog.first.nativeElement.focus();\n        modal.markForCheck();\n        modal.onOpenFinished.emit(modal);\n      });\n      return true;\n    }\n    /**\n     * @param {?} modal\n     * @param {?=} top\n     * @return {?}\n     */\n\n\n    _toggleModal(modal, top) {\n      if (modal.visible) {\n        return this._closeModal(modal);\n      } else {\n        return this._openModal(modal, top);\n      }\n    }\n    /**\n     * @param {?} modal\n     * @return {?}\n     */\n\n\n    _closeModal(modal) {\n      if (!modal.openedClass) {\n        return false;\n      }\n\n      modal.openedClass = false;\n      modal.onClose.emit(modal);\n      modal.onAnyCloseEvent.emit(modal);\n\n      if (this.getOpenedModals().length < 2) {\n        modal.removeBodyClass();\n        window.removeEventListener('keyup', this._escapeKeyboardEvent);\n        window.removeEventListener('keydown', this._trapFocusModal);\n      }\n\n      setTimeout(() => {\n        modal.visibleChange.emit(modal.visible);\n        modal.visible = false;\n        modal.overlayVisible = false;\n        modal.nsmDialog.first.nativeElement.removeAttribute('tabIndex');\n        modal.markForCheck();\n        modal.onCloseFinished.emit(modal);\n        modal.onAnyCloseEventFinished.emit(modal);\n\n        if (modal.refocus) {\n          this.lastElementFocused.focus();\n        }\n      }, modal.hideDelay);\n      return true;\n    }\n    /**\n     * @param {?} modal\n     * @return {?}\n     */\n\n\n    _dismissModal(modal) {\n      if (!modal.openedClass) {\n        return false;\n      }\n\n      modal.openedClass = false;\n      modal.onDismiss.emit(modal);\n      modal.onAnyCloseEvent.emit(modal);\n\n      if (this.getOpenedModals().length < 2) {\n        modal.removeBodyClass();\n      }\n\n      setTimeout(() => {\n        modal.visible = false;\n        modal.visibleChange.emit(modal.visible);\n        modal.overlayVisible = false;\n        modal.markForCheck();\n        modal.onDismissFinished.emit(modal);\n        modal.onAnyCloseEventFinished.emit(modal);\n      }, modal.hideDelay);\n      return true;\n    }\n    /**\n     * @param {?} modalInstance\n     * @return {?}\n     */\n\n\n    _deleteModal(modalInstance) {\n      this.removeModal(modalInstance.id);\n\n      if (!this.getModalStack().length) {\n        modalInstance.modal.removeBodyClass();\n      }\n    }\n    /**\n     * Resolve content according to the types\n     * @template T\n     * @param {?} content The modal content ( string, templateRef or Component )\n     * @return {?}\n     */\n\n\n    _resolveNgContent(content) {\n      if (typeof content === 'string') {\n        const\n        /** @type {?} */\n        element = this._document.createTextNode(content);\n\n        return [[element]];\n      }\n\n      if (content instanceof TemplateRef) {\n        const\n        /** @type {?} */\n        viewRef = content.createEmbeddedView(\n        /** @type {?} */\n        null);\n        this.applicationRef.attachView(viewRef);\n        return [viewRef.rootNodes];\n      }\n\n      return [];\n    }\n    /**\n     * Is current platform browser\n     * @return {?}\n     */\n\n\n    get isBrowser() {\n      return isPlatformBrowser(this._platformId);\n    }\n\n  }\n\n  NgxSmartModalService.ɵfac = function NgxSmartModalService_Factory(t) {\n    return new (t || NgxSmartModalService)(ɵngcc0.ɵɵinject(ɵngcc0.ComponentFactoryResolver), ɵngcc0.ɵɵinject(ɵngcc0.ApplicationRef), ɵngcc0.ɵɵinject(ɵngcc0.Injector), ɵngcc0.ɵɵinject(NgxSmartModalStackService), ɵngcc0.ɵɵinject(ɵngcc0.ApplicationRef), ɵngcc0.ɵɵinject(DOCUMENT), ɵngcc0.ɵɵinject(PLATFORM_ID));\n  };\n\n  NgxSmartModalService.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n    token: NgxSmartModalService,\n    factory: NgxSmartModalService.ɵfac\n  });\n  /** @nocollapse */\n\n  return NgxSmartModalService;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\n\nlet NgxSmartModalModule = /*#__PURE__*/(() => {\n  class NgxSmartModalModule {\n    /**\n     * @param {?} serivce\n     */\n    constructor(serivce) {\n      this.serivce = serivce;\n    }\n    /**\n     * Use in AppModule: new instance of NgxSmartModal.\n     * @return {?}\n     */\n\n\n    static forRoot() {\n      return {\n        ngModule: NgxSmartModalModule,\n        providers: [NgxSmartModalService, NgxSmartModalStackService]\n      };\n    }\n    /**\n     * Use in features modules with lazy loading: new instance of NgxSmartModal.\n     * @return {?}\n     */\n\n\n    static forChild() {\n      return {\n        ngModule: NgxSmartModalModule,\n        providers: [NgxSmartModalService, NgxSmartModalStackService]\n      };\n    }\n\n  }\n\n  NgxSmartModalModule.ɵfac = function NgxSmartModalModule_Factory(t) {\n    return new (t || NgxSmartModalModule)(ɵngcc0.ɵɵinject(NgxSmartModalService));\n  };\n\n  NgxSmartModalModule.ɵmod = /*@__PURE__*/ɵngcc0.ɵɵdefineNgModule({\n    type: NgxSmartModalModule\n  });\n  NgxSmartModalModule.ɵinj = /*@__PURE__*/ɵngcc0.ɵɵdefineInjector({\n    imports: [[CommonModule]]\n  });\n  /** @nocollapse */\n\n  return NgxSmartModalModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(NgxSmartModalModule, {\n    declarations: function () {\n      return [NgxSmartModalComponent];\n    },\n    imports: function () {\n      return [CommonModule];\n    },\n    exports: function () {\n      return [NgxSmartModalComponent];\n    }\n  });\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n// Public classes.\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\n/**\n * Entry point for all public APIs of the package.\n */\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { NgxSmartModalService, NgxSmartModalComponent, NgxSmartModalModule, NgxSmartModalConfig, NgxSmartModalStackService as ɵa }; //# sourceMappingURL=ngx-smart-modal.js.map", "map": null, "metadata": {}, "sourceType": "module"}