{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport let NonceService = /*#__PURE__*/(() => {\n  class NonceService {\n    constructor(rendererFactory, document) {\n      this.rendererFactory = rendererFactory;\n      this.document = document;\n      this.renderer = this.rendererFactory.createRenderer(null, null);\n    }\n\n    addNonceToStyles(nonce) {\n      // Get the <head> element\n      const head = this.document.head; // Find all <style> elements within the <head> tag\n\n      const headStyleElements = head.querySelectorAll('style'); // Apply nonce attribute to <style> elements in <head>\n\n      headStyleElements.forEach(style => {\n        if (!style.hasAttribute('nonce')) {\n          this.renderer.setAttribute(style, 'nonce', nonce);\n        }\n      }); // Find all <link> elements within the <head> tag\n\n      const headLinkElements = head.querySelectorAll('link'); // Apply nonce attribute to <style> elements in <head>\n\n      headLinkElements.forEach(style => {\n        if (!style.hasAttribute('nonce')) {\n          this.renderer.setAttribute(style, 'nonce', nonce);\n        }\n      }); // Find all <link> elements within the <head> tag\n\n      const headScriptElements = head.querySelectorAll('script'); // Apply nonce attribute to <style> elements in <head>\n\n      headScriptElements.forEach(style => {\n        if (!style.hasAttribute('nonce')) {\n          this.renderer.setAttribute(style, 'nonce', nonce);\n        }\n      }); // Find all <style> elements within the <body> tag\n\n      const bodyStyleElements = this.document.body.querySelectorAll('style'); // Apply nonce attribute to <style> elements in <body>\n\n      bodyStyleElements.forEach(style => {\n        if (!style.hasAttribute('nonce')) {\n          this.renderer.setAttribute(style, 'nonce', nonce);\n        }\n      });\n    }\n\n  }\n\n  NonceService.ɵfac = function NonceService_Factory(t) {\n    return new (t || NonceService)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(DOCUMENT));\n  };\n\n  NonceService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: NonceService,\n    factory: NonceService.ɵfac,\n    providedIn: 'root'\n  });\n  return NonceService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}