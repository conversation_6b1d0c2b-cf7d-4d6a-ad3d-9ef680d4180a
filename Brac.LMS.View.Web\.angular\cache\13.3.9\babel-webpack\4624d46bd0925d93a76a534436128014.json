{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/flex-layout/flex\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../safe-pipe\";\n\nfunction ConfirmComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function ConfirmComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return ctx_r1.dialogRef.close(false);\n    });\n    i0.ɵɵelement(1, \"i\", 7);\n    i0.ɵɵtext(2, \" Cancel \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport let ConfirmComponent = /*#__PURE__*/(() => {\n  class ConfirmComponent {\n    constructor(dialogRef) {\n      this.dialogRef = dialogRef;\n      this.confirmButtonText = 'Confirm';\n      this.cancelButtonText = 'Cancel';\n      this.disableCloseButton = false;\n    }\n\n  }\n\n  ConfirmComponent.ɵfac = function ConfirmComponent_Factory(t) {\n    return new (t || ConfirmComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef));\n  };\n\n  ConfirmComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ConfirmComponent,\n    selectors: [[\"app-confirm\"]],\n    decls: 9,\n    vars: 7,\n    consts: [[\"matDialogTitle\", \"\"], [1, \"mat-typography\", 3, \"innerHTML\"], [\"align\", \"end\", \"fxLayoutAlign\", \"space-around center\"], [\"matDialogClose\", \"\", \"class\", \"btn btn-danger btn-sm me-2\", 3, \"click\", 4, \"ngIf\"], [\"matDialogClose\", \"\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"fa\", \"fa-check\"], [\"matDialogClose\", \"\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fa\", \"fa-close\"]],\n    template: function ConfirmComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"h1\", 0);\n        i0.ɵɵtext(1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(2, \"mat-dialog-content\", 1);\n        i0.ɵɵpipe(3, \"safe\");\n        i0.ɵɵelementStart(4, \"mat-dialog-actions\", 2);\n        i0.ɵɵtemplate(5, ConfirmComponent_button_5_Template, 3, 0, \"button\", 3);\n        i0.ɵɵelementStart(6, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function ConfirmComponent_Template_button_click_6_listener() {\n          return ctx.dialogRef.close(true);\n        });\n        i0.ɵɵelement(7, \"i\", 5);\n        i0.ɵɵtext(8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.title, \"\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(3, 4, ctx.message, \"html\"), i0.ɵɵsanitizeHtml);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", !ctx.disableCloseButton);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.confirmButtonText, \" \");\n      }\n    },\n    directives: [i1.MatDialogTitle, i1.MatDialogContent, i1.MatDialogActions, i2.DefaultLayoutAlignDirective, i3.NgIf, i1.MatDialogClose],\n    pipes: [i4.SafePipe],\n    encapsulation: 2\n  });\n  return ConfirmComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}