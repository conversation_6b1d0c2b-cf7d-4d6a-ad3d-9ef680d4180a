{"ast": null, "code": "/**\n * @license Angular v13.1.3\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport { isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, NgZone, ApplicationRef, PLATFORM_ID, APP_INITIALIZER, Injector, NgModule } from '@angular/core';\nimport { defer, throwError, fromEvent, of, concat, Subject, NEVER, merge } from 'rxjs';\nimport { map, filter, switchMap, publish, take, tap, delay } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst ERR_SW_NOT_SUPPORTED = 'Service workers are disabled or not supported by this browser';\n\nfunction errorObservable(message) {\n  return defer(() => throwError(new Error(message)));\n}\n/**\n * @publicApi\n */\n\n\nclass NgswCommChannel {\n  constructor(serviceWorker) {\n    this.serviceWorker = serviceWorker;\n\n    if (!serviceWorker) {\n      this.worker = this.events = this.registration = errorObservable(ERR_SW_NOT_SUPPORTED);\n    } else {\n      const controllerChangeEvents = fromEvent(serviceWorker, 'controllerchange');\n      const controllerChanges = controllerChangeEvents.pipe(map(() => serviceWorker.controller));\n      const currentController = defer(() => of(serviceWorker.controller));\n      const controllerWithChanges = concat(currentController, controllerChanges);\n      this.worker = controllerWithChanges.pipe(filter(c => !!c));\n      this.registration = this.worker.pipe(switchMap(() => serviceWorker.getRegistration()));\n      const rawEvents = fromEvent(serviceWorker, 'message');\n      const rawEventPayload = rawEvents.pipe(map(event => event.data));\n      const eventsUnconnected = rawEventPayload.pipe(filter(event => event && event.type));\n      const events = eventsUnconnected.pipe(publish());\n      events.connect();\n      this.events = events;\n    }\n  }\n\n  postMessage(action, payload) {\n    return this.worker.pipe(take(1), tap(sw => {\n      sw.postMessage(Object.assign({\n        action\n      }, payload));\n    })).toPromise().then(() => undefined);\n  }\n\n  postMessageWithOperation(type, payload, operationNonce) {\n    const waitForOperationCompleted = this.waitForOperationCompleted(operationNonce);\n    const postMessage = this.postMessage(type, payload);\n    return Promise.all([postMessage, waitForOperationCompleted]).then(([, result]) => result);\n  }\n\n  generateNonce() {\n    return Math.round(Math.random() * 10000000);\n  }\n\n  eventsOfType(type) {\n    let filterFn;\n\n    if (typeof type === 'string') {\n      filterFn = event => event.type === type;\n    } else {\n      filterFn = event => type.includes(event.type);\n    }\n\n    return this.events.pipe(filter(filterFn));\n  }\n\n  nextEventOfType(type) {\n    return this.eventsOfType(type).pipe(take(1));\n  }\n\n  waitForOperationCompleted(nonce) {\n    return this.eventsOfType('OPERATION_COMPLETED').pipe(filter(event => event.nonce === nonce), take(1), map(event => {\n      if (event.result !== undefined) {\n        return event.result;\n      }\n\n      throw new Error(event.error);\n    })).toPromise();\n  }\n\n  get isEnabled() {\n    return !!this.serviceWorker;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Subscribe and listen to\n * [Web Push\n * Notifications](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices) through\n * Angular Service Worker.\n *\n * @usageNotes\n *\n * You can inject a `SwPush` instance into any component or service\n * as a dependency.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"inject-sw-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * To subscribe, call `SwPush.requestSubscription()`, which asks the user for permission.\n * The call returns a `Promise` with a new\n * [`PushSubscription`](https://developer.mozilla.org/en-US/docs/Web/API/PushSubscription)\n * instance.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * A request is rejected if the user denies permission, or if the browser\n * blocks or does not support the Push API or ServiceWorkers.\n * Check `SwPush.isEnabled` to confirm status.\n *\n * Invoke Push Notifications by pushing a message with the following payload.\n *\n * ```ts\n * {\n *   \"notification\": {\n *     \"actions\": NotificationAction[],\n *     \"badge\": USVString,\n *     \"body\": DOMString,\n *     \"data\": any,\n *     \"dir\": \"auto\"|\"ltr\"|\"rtl\",\n *     \"icon\": USVString,\n *     \"image\": USVString,\n *     \"lang\": DOMString,\n *     \"renotify\": boolean,\n *     \"requireInteraction\": boolean,\n *     \"silent\": boolean,\n *     \"tag\": DOMString,\n *     \"timestamp\": DOMTimeStamp,\n *     \"title\": DOMString,\n *     \"vibrate\": number[]\n *   }\n * }\n * ```\n *\n * Only `title` is required. See `Notification`\n * [instance\n * properties](https://developer.mozilla.org/en-US/docs/Web/API/Notification#Instance_properties).\n *\n * While the subscription is active, Service Worker listens for\n * [PushEvent](https://developer.mozilla.org/en-US/docs/Web/API/PushEvent)\n * occurrences and creates\n * [Notification](https://developer.mozilla.org/en-US/docs/Web/API/Notification)\n * instances in response.\n *\n * Unsubscribe using `SwPush.unsubscribe()`.\n *\n * An application can subscribe to `SwPush.notificationClicks` observable to be notified when a user\n * clicks on a notification. For example:\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-notification-clicks\"\n * header=\"app.component.ts\"></code-example>\n *\n * You can read more on handling notification clicks in the [Service worker notifications\n * guide](guide/service-worker-notifications).\n *\n * @see [Push Notifications](https://developers.google.com/web/fundamentals/codelabs/push-notifications/)\n * @see [Angular Push Notifications](https://blog.angular-university.io/angular-push-notifications/)\n * @see [MDN: Push API](https://developer.mozilla.org/en-US/docs/Web/API/Push_API)\n * @see [MDN: Notifications API](https://developer.mozilla.org/en-US/docs/Web/API/Notifications_API)\n * @see [MDN: Web Push API Notifications best practices](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices)\n *\n * @publicApi\n */\n\n\nlet SwPush = /*#__PURE__*/(() => {\n  class SwPush {\n    constructor(sw) {\n      this.sw = sw;\n      this.subscriptionChanges = new Subject();\n\n      if (!sw.isEnabled) {\n        this.messages = NEVER;\n        this.notificationClicks = NEVER;\n        this.subscription = NEVER;\n        return;\n      }\n\n      this.messages = this.sw.eventsOfType('PUSH').pipe(map(message => message.data));\n      this.notificationClicks = this.sw.eventsOfType('NOTIFICATION_CLICK').pipe(map(message => message.data));\n      this.pushManager = this.sw.registration.pipe(map(registration => registration.pushManager));\n      const workerDrivenSubscriptions = this.pushManager.pipe(switchMap(pm => pm.getSubscription()));\n      this.subscription = merge(workerDrivenSubscriptions, this.subscriptionChanges);\n    }\n    /**\n     * True if the Service Worker is enabled (supported by the browser and enabled via\n     * `ServiceWorkerModule`).\n     */\n\n\n    get isEnabled() {\n      return this.sw.isEnabled;\n    }\n    /**\n     * Subscribes to Web Push Notifications,\n     * after requesting and receiving user permission.\n     *\n     * @param options An object containing the `serverPublicKey` string.\n     * @returns A Promise that resolves to the new subscription object.\n     */\n\n\n    requestSubscription(options) {\n      if (!this.sw.isEnabled) {\n        return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n      }\n\n      const pushOptions = {\n        userVisibleOnly: true\n      };\n      let key = this.decodeBase64(options.serverPublicKey.replace(/_/g, '/').replace(/-/g, '+'));\n      let applicationServerKey = new Uint8Array(new ArrayBuffer(key.length));\n\n      for (let i = 0; i < key.length; i++) {\n        applicationServerKey[i] = key.charCodeAt(i);\n      }\n\n      pushOptions.applicationServerKey = applicationServerKey;\n      return this.pushManager.pipe(switchMap(pm => pm.subscribe(pushOptions)), take(1)).toPromise().then(sub => {\n        this.subscriptionChanges.next(sub);\n        return sub;\n      });\n    }\n    /**\n     * Unsubscribes from Service Worker push notifications.\n     *\n     * @returns A Promise that is resolved when the operation succeeds, or is rejected if there is no\n     *          active subscription or the unsubscribe operation fails.\n     */\n\n\n    unsubscribe() {\n      if (!this.sw.isEnabled) {\n        return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n      }\n\n      const doUnsubscribe = sub => {\n        if (sub === null) {\n          throw new Error('Not subscribed to push notifications.');\n        }\n\n        return sub.unsubscribe().then(success => {\n          if (!success) {\n            throw new Error('Unsubscribe failed!');\n          }\n\n          this.subscriptionChanges.next(null);\n        });\n      };\n\n      return this.subscription.pipe(take(1), switchMap(doUnsubscribe)).toPromise();\n    }\n\n    decodeBase64(input) {\n      return atob(input);\n    }\n\n  }\n\n  SwPush.ɵfac = function SwPush_Factory(t) {\n    return new (t || SwPush)(i0.ɵɵinject(NgswCommChannel));\n  };\n\n  SwPush.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SwPush,\n    factory: SwPush.ɵfac\n  });\n  return SwPush;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Subscribe to update notifications from the Service Worker, trigger update\n * checks, and forcibly activate updates.\n *\n * @see {@link guide/service-worker-communications Service worker communication guide}\n *\n * @publicApi\n */\n\n\nlet SwUpdate = /*#__PURE__*/(() => {\n  class SwUpdate {\n    constructor(sw) {\n      this.sw = sw;\n\n      if (!sw.isEnabled) {\n        this.versionUpdates = NEVER;\n        this.available = NEVER;\n        this.activated = NEVER;\n        this.unrecoverable = NEVER;\n        return;\n      }\n\n      this.versionUpdates = this.sw.eventsOfType(['VERSION_DETECTED', 'VERSION_INSTALLATION_FAILED', 'VERSION_READY']);\n      this.available = this.versionUpdates.pipe(filter(evt => evt.type === 'VERSION_READY'), map(evt => ({\n        type: 'UPDATE_AVAILABLE',\n        current: evt.currentVersion,\n        available: evt.latestVersion\n      })));\n      this.activated = this.sw.eventsOfType('UPDATE_ACTIVATED');\n      this.unrecoverable = this.sw.eventsOfType('UNRECOVERABLE_STATE');\n    }\n    /**\n     * True if the Service Worker is enabled (supported by the browser and enabled via\n     * `ServiceWorkerModule`).\n     */\n\n\n    get isEnabled() {\n      return this.sw.isEnabled;\n    }\n    /**\n     * Checks for an update and waits until the new version is downloaded from the server and ready\n     * for activation.\n     *\n     * @returns a promise that\n     * - resolves to `true` if a new version was found and is ready to be activated.\n     * - resolves to `false` if no new version was found\n     * - rejects if any error occurs\n     */\n\n\n    checkForUpdate() {\n      if (!this.sw.isEnabled) {\n        return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n      }\n\n      const nonce = this.sw.generateNonce();\n      return this.sw.postMessageWithOperation('CHECK_FOR_UPDATES', {\n        nonce\n      }, nonce);\n    }\n    /**\n     * Updates the current client (i.e. browser tab) to the latest version that is ready for\n     * activation.\n     *\n     * @returns a promise that\n     *  - resolves to `true` if an update was activated successfully\n     *  - resolves to `false` if no update was available (for example, the client was already on the\n     *    latest version).\n     *  - rejects if any error occurs\n     */\n\n\n    activateUpdate() {\n      if (!this.sw.isEnabled) {\n        return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n      }\n\n      const nonce = this.sw.generateNonce();\n      return this.sw.postMessageWithOperation('ACTIVATE_UPDATE', {\n        nonce\n      }, nonce);\n    }\n\n  }\n\n  SwUpdate.ɵfac = function SwUpdate_Factory(t) {\n    return new (t || SwUpdate)(i0.ɵɵinject(NgswCommChannel));\n  };\n\n  SwUpdate.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SwUpdate,\n    factory: SwUpdate.ɵfac\n  });\n  return SwUpdate;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Token that can be used to provide options for `ServiceWorkerModule` outside of\n * `ServiceWorkerModule.register()`.\n *\n * You can use this token to define a provider that generates the registration options at runtime,\n * for example via a function call:\n *\n * {@example service-worker/registration-options/module.ts region=\"registration-options\"\n *     header=\"app.module.ts\"}\n *\n * @publicApi\n */\n\n\nclass SwRegistrationOptions {}\n\nconst SCRIPT = /*#__PURE__*/new InjectionToken('NGSW_REGISTER_SCRIPT');\n\nfunction ngswAppInitializer(injector, script, options, platformId) {\n  const initializer = () => {\n    if (!(isPlatformBrowser(platformId) && 'serviceWorker' in navigator && options.enabled !== false)) {\n      return;\n    } // Wait for service worker controller changes, and fire an INITIALIZE action when a new SW\n    // becomes active. This allows the SW to initialize itself even if there is no application\n    // traffic.\n\n\n    navigator.serviceWorker.addEventListener('controllerchange', () => {\n      if (navigator.serviceWorker.controller !== null) {\n        navigator.serviceWorker.controller.postMessage({\n          action: 'INITIALIZE'\n        });\n      }\n    });\n    let readyToRegister$;\n\n    if (typeof options.registrationStrategy === 'function') {\n      readyToRegister$ = options.registrationStrategy();\n    } else {\n      const [strategy, ...args] = (options.registrationStrategy || 'registerWhenStable:30000').split(':');\n\n      switch (strategy) {\n        case 'registerImmediately':\n          readyToRegister$ = of(null);\n          break;\n\n        case 'registerWithDelay':\n          readyToRegister$ = delayWithTimeout(+args[0] || 0);\n          break;\n\n        case 'registerWhenStable':\n          readyToRegister$ = !args[0] ? whenStable(injector) : merge(whenStable(injector), delayWithTimeout(+args[0]));\n          break;\n\n        default:\n          // Unknown strategy.\n          throw new Error(`Unknown ServiceWorker registration strategy: ${options.registrationStrategy}`);\n      }\n    } // Don't return anything to avoid blocking the application until the SW is registered.\n    // Also, run outside the Angular zone to avoid preventing the app from stabilizing (especially\n    // given that some registration strategies wait for the app to stabilize).\n    // Catch and log the error if SW registration fails to avoid uncaught rejection warning.\n\n\n    const ngZone = injector.get(NgZone);\n    ngZone.runOutsideAngular(() => readyToRegister$.pipe(take(1)).subscribe(() => navigator.serviceWorker.register(script, {\n      scope: options.scope\n    }).catch(err => console.error('Service worker registration failed with:', err))));\n  };\n\n  return initializer;\n}\n\nfunction delayWithTimeout(timeout) {\n  return of(null).pipe(delay(timeout));\n}\n\nfunction whenStable(injector) {\n  const appRef = injector.get(ApplicationRef);\n  return appRef.isStable.pipe(filter(stable => stable));\n}\n\nfunction ngswCommChannelFactory(opts, platformId) {\n  return new NgswCommChannel(isPlatformBrowser(platformId) && opts.enabled !== false ? navigator.serviceWorker : undefined);\n}\n/**\n * @publicApi\n */\n\n\nlet ServiceWorkerModule = /*#__PURE__*/(() => {\n  class ServiceWorkerModule {\n    /**\n     * Register the given Angular Service Worker script.\n     *\n     * If `enabled` is set to `false` in the given options, the module will behave as if service\n     * workers are not supported by the browser, and the service worker will not be registered.\n     */\n    static register(script, opts = {}) {\n      return {\n        ngModule: ServiceWorkerModule,\n        providers: [{\n          provide: SCRIPT,\n          useValue: script\n        }, {\n          provide: SwRegistrationOptions,\n          useValue: opts\n        }, {\n          provide: NgswCommChannel,\n          useFactory: ngswCommChannelFactory,\n          deps: [SwRegistrationOptions, PLATFORM_ID]\n        }, {\n          provide: APP_INITIALIZER,\n          useFactory: ngswAppInitializer,\n          deps: [Injector, SCRIPT, SwRegistrationOptions, PLATFORM_ID],\n          multi: true\n        }]\n      };\n    }\n\n  }\n\n  ServiceWorkerModule.ɵfac = function ServiceWorkerModule_Factory(t) {\n    return new (t || ServiceWorkerModule)();\n  };\n\n  ServiceWorkerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ServiceWorkerModule\n  });\n  ServiceWorkerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [SwPush, SwUpdate]\n  });\n  return ServiceWorkerModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ServiceWorkerModule, SwPush, SwRegistrationOptions, SwUpdate }; //# sourceMappingURL=service-worker.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}