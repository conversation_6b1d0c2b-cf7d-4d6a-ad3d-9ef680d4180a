{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { TemplateRef } from '@angular/core';\nimport { environment } from 'src/environments/environment';\nimport { BlockUI } from 'ng-block-ui';\nimport { debounceTime } from 'rxjs';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { Page } from '../_models/page';\nimport { ColumnMode } from '@swimlane/ngx-datatable';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../app.component\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../_services/common.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"../_services/authentication.service\";\nimport * as i7 from \"ngx-smart-modal\";\nimport * as i8 from \"ng-block-ui\";\nimport * as i9 from \"@ng-select/ng-select\";\nimport * as i10 from \"@swimlane/ngx-datatable\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"ngx-extended-pdf-viewer\";\nconst _c0 = [\"myTable\"];\n\nfunction LearningHoursComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const rowIndex_r13 = ctx.rowIndex;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", rowIndex_r13 + 1, \" \");\n  }\n}\n\nfunction LearningHoursComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r14 = ctx.row;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", row_r14.Title, \" \");\n  }\n}\n\nfunction LearningHoursComponent_ng_template_28_span_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function LearningHoursComponent_ng_template_28_span_0_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const item_r19 = i0.ɵɵnextContext().$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return ctx_r22.openModal(item_r19);\n    });\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵtext(3, \" Start\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LearningHoursComponent_ng_template_28_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtemplate(1, LearningHoursComponent_ng_template_28_span_0_div_1_Template, 4, 0, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r19 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r19.Type == \"Exam\");\n  }\n}\n\nfunction LearningHoursComponent_ng_template_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LearningHoursComponent_ng_template_28_span_0_Template, 2, 1, \"span\", 32);\n  }\n\n  if (rf & 2) {\n    const row_r16 = ctx.row;\n    i0.ɵɵproperty(\"ngForOf\", row_r16.List);\n  }\n}\n\nfunction LearningHoursComponent_ng_template_30_span_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function LearningHoursComponent_ng_template_30_span_0_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const item_r28 = i0.ɵɵnextContext().$implicit;\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return ctx_r30.openModal(item_r28);\n    });\n    i0.ɵɵelement(2, \"i\", 38);\n    i0.ɵɵtext(3, \" Open\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LearningHoursComponent_ng_template_30_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtemplate(1, LearningHoursComponent_ng_template_30_span_0_div_1_Template, 4, 0, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r28 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r28.Type == \"Document\");\n  }\n}\n\nfunction LearningHoursComponent_ng_template_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LearningHoursComponent_ng_template_30_span_0_Template, 2, 1, \"span\", 37);\n  }\n\n  if (rf & 2) {\n    const row_r25 = ctx.row;\n    i0.ɵɵproperty(\"ngForOf\", row_r25.List);\n  }\n}\n\nfunction LearningHoursComponent_ng_template_32_span_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function LearningHoursComponent_ng_template_32_span_0_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const item_r36 = i0.ɵɵnextContext().$implicit;\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return ctx_r38.openModal(item_r36);\n    });\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵtext(3, \" Play\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LearningHoursComponent_ng_template_32_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtemplate(1, LearningHoursComponent_ng_template_32_span_0_div_1_Template, 4, 0, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r36 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r36.Type == \"Video\");\n  }\n}\n\nfunction LearningHoursComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LearningHoursComponent_ng_template_32_span_0_Template, 2, 1, \"span\", 37);\n  }\n\n  if (rf & 2) {\n    const row_r33 = ctx.row;\n    i0.ɵɵproperty(\"ngForOf\", row_r33.List);\n  }\n}\n\nfunction LearningHoursComponent_h4_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedContent.Title);\n  }\n}\n\nfunction LearningHoursComponent_video_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"video\", 41);\n    i0.ɵɵelement(1, \"source\", 42);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"src\", \"\", ctx_r8.mediaBaseUrl, \"\", ctx_r8.selectedContent.FilePath, \"\", i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction LearningHoursComponent_h4_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.selectedContent.Title);\n  }\n}\n\nfunction LearningHoursComponent_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelementStart(1, \"ngx-extended-pdf-viewer\", 47);\n    i0.ɵɵlistener(\"srcChange\", function LearningHoursComponent_div_42_div_1_Template_ngx_extended_pdf_viewer_srcChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r43 = i0.ɵɵnextContext(2);\n      return ctx_r43.pdfSrc = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r41.pdfSrc)(\"zoom\", \"page-width\")(\"showPrintButton\", false)(\"showRotateButton\", false)(\"showOpenFileButton\", false)(\"showDownloadButton\", false)(\"showBookmarkButton\", true)(\"showSecondaryToolbarButton\", false)(\"useBrowserLocale\", true);\n  }\n}\n\nfunction LearningHoursComponent_div_42_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵelementStart(1, \"div\", 49);\n    i0.ɵɵelementStart(2, \"p\", 50);\n    i0.ɵɵtext(3, \" This document can't preview here. You may download the document. Please click below button to download the document. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function LearningHoursComponent_div_42_div_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return ctx_r45.downloadDoc();\n    });\n    i0.ɵɵelement(5, \"i\", 52);\n    i0.ɵɵtext(6, \" Download \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LearningHoursComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, LearningHoursComponent_div_42_div_1_Template, 2, 9, \"div\", 44);\n    i0.ɵɵtemplate(2, LearningHoursComponent_div_42_div_2_Template, 7, 0, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.docObj.PDF);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.docObj.PDF);\n  }\n}\n\nconst _c1 = function () {\n  return {\n    prop: \"name\",\n    dir: \"desc\"\n  };\n};\n\nconst _c2 = function (a0) {\n  return [a0];\n};\n\nexport class LearningHoursComponent {\n  constructor(appComponent, formBuilder, router, _service, toastr, authService, ngxSmartModalService) {\n    this.appComponent = appComponent;\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.authService = authService;\n    this.ngxSmartModalService = ngxSmartModalService;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.timestamp = new Date().getTime();\n    this.selectedContent = null;\n    this.learningHoursContentList = [];\n    this.learningHoursContentGroupByList = [];\n    this.page = new Page();\n    this.docObj = null;\n    this.pdfSrc = null;\n    this.categoryList = [];\n    this.rows = [];\n    this.loadingIndicator = false;\n    this.ColumnMode = ColumnMode;\n    this.scrollBarHorizontal = window.innerWidth < 1200; // this.page.startsFrom = 1;\n\n    this.page.pageNumber = 0;\n    this.page.size = 10;\n    this.authService.getCurrentUser().subscribe(user => this.currentUser = user);\n  }\n\n  handleSelectClick(selectElement) {\n    this.appComponent.handleSelectClick(selectElement);\n  }\n\n  ngOnInit() {\n    this.filterForm = this.formBuilder.group({\n      name: [null],\n      categoryId: [null]\n    });\n    this.filterForm.get(\"name\").valueChanges.pipe(debounceTime(700)).subscribe(() => {\n      this.getList();\n    });\n    this.getList();\n    this.getCategoryList();\n  }\n\n  getCategoryList() {\n    this._service.get('learning-hour-category/dropdown-list').subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.categoryList = res.Data;\n    }, err => {});\n  }\n\n  setPage(pageNumber) {\n    this.page.pageNumber = pageNumber.offset;\n    this.getList();\n  }\n\n  getList() {\n    let obj = {\n      name: this.filterForm.value.name ? this.filterForm.value.name.trim() : null,\n      categoryId: this.filterForm.value.categoryId,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber\n    };\n    this.blockUI.start('Getting data. Please wait...');\n\n    this._service.get('course/get-learning-hour-groupby-contents', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        } // this.learningHoursContentList = res.Data.Records;\n        // this.page.pageTotalElements = res.Data.Records.length;\n\n\n        this.page.pageTotalElements = res.Data.GroupByTitleRecords.length;\n        this.learningHoursContentGroupByList = res.Data.GroupByTitleRecords;\n        this.page.totalElements = res.Data.Total;\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  toggleExpandGroup(group) {\n    this.table.groupHeader.toggleExpandGroup(group);\n  }\n\n  openModal(item) {\n    // this.modalRefVideo = this.modalService.show(template, this.modalConfigVideo);\n    this.selectedContent = item;\n\n    switch (item.Type) {\n      case \"Video\":\n        this.ngxSmartModalService.create('videoModal', this.tpl).open();\n        setTimeout(() => {\n          this.saveActivity(item, 'Doc');\n        }, 10000);\n        break;\n\n      case \"Document\":\n        this.onDocClick(item);\n        this.ngxSmartModalService.create('docModal', this.tpl).open();\n        break;\n\n      case \"Exam\":\n        this.router.navigate(['evaluation-test/' + item.Id]);\n        break;\n    }\n  }\n\n  onDocClick(doc) {\n    this.docObj = null; //  if (doc.Restricted) return;\n\n    this.docObj = doc;\n\n    if (doc.FilePath.split('.').pop() === 'pdf') {\n      // console.log('doc',doc)\n      // console.log('docOpen',doc.FilePath.split('.').pop())\n      this.openPdf(doc); // console.log('file',doc.FilePath)\n      // this.openPdf(\"/Files/OpenMaterial/Documents/DOCUWFI20221203124503.pdf\");\n\n      this.docObj.PDF = true;\n    } else {\n      this.docObj.PDF = false;\n    }\n  }\n\n  openPdf(item) {\n    this._service.getPDFFile(this.mediaBaseUrl + '/api/course/download-document-file?partialPath=' + item.FilePath).subscribe(res => {\n      this.pdfSrc = res;\n      setTimeout(() => {\n        this.saveActivity(item, 'Doc');\n      }, 10000);\n    });\n  }\n\n  downloadDoc() {\n    var link = document.createElement('a');\n    link.href = this.mediaBaseUrl + this.docObj.FilePath;\n    link.target = '_blank';\n    link.rel = 'noopener';\n    link.download = this.docObj.Title + '.' + this.docObj.FilePath.split('.').pop();\n    link.click();\n    link.remove();\n  }\n\n  saveActivity(item, type) {\n    let obj = {\n      title: item.Title,\n      contentType: type\n    };\n    this.blockUI.start('Saving activities. Please wait...');\n\n    this._service.get('open-material/save-trainee-evaluation-activity', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => {\n        this.blockUI.stop();\n      }\n    });\n  }\n\n  modalClose() {\n    this.selectedContent = null;\n  }\n\n}\n\nLearningHoursComponent.ɵfac = function LearningHoursComponent_Factory(t) {\n  return new (t || LearningHoursComponent)(i0.ɵɵdirectiveInject(i1.AppComponent), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.CommonService), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i6.AuthenticationService), i0.ɵɵdirectiveInject(i7.NgxSmartModalService));\n};\n\nLearningHoursComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: LearningHoursComponent,\n  selectors: [[\"app-learning-hours\"]],\n  viewQuery: function LearningHoursComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 5);\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tpl = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.table = _t.first);\n    }\n  },\n  decls: 43,\n  vars: 42,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"mt-1\", \"mb-4\"], [1, \"row\"], [\"autocomplete\", \"off\", 1, \"col-lg-8\", \"col-12\", 3, \"formGroup\"], [1, \"col-lg-8\", \"col-12\", \"mb-3\"], [1, \"input-group\"], [\"for\", \"name\", 1, \"visually-hidden\"], [\"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Type name to search...\", 1, \"form-control\", \"rounded-custom\", \"pe-5\"], [1, \"ai-search\", \"position-absolute\", \"top-50\", \"end-0\", \"translate-middle-y\", \"me-3\"], [1, \"col-lg-4\", \"col-12\", \"mb-3\"], [\"for\", \"categoryId\", 1, \"visually-hidden\"], [\"formControlName\", \"categoryId\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Category\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\", \"change\"], [\"selectElement\", \"\"], [\"rowHeight\", \"auto\", 1, \"material\", 3, \"rows\", \"loadingIndicator\", \"columnMode\", \"headerHeight\", \"footerHeight\", \"externalPaging\", \"count\", \"offset\", \"limit\", \"scrollbarH\", \"sorts\", \"page\"], [\"name\", \"SL#\", 3, \"width\", \"draggable\", \"sortable\", \"resizeable\"], [\"ngx-datatable-cell-template\", \"\"], [\"name\", \"Title\", 3, \"draggable\", \"sortable\", \"width\", \"resizeable\"], [\"name\", \"Exam\", 3, \"width\", \"draggable\", \"sortable\", \"resizeable\"], [\"name\", \"Document\", 3, \"width\", \"draggable\", \"sortable\", \"resizeable\"], [\"name\", \"Video\", 3, \"width\", \"draggable\", \"sortable\", \"resizeable\"], [\"identifier\", \"videoModal\", \"customClass\", \"nsm-dialog-animation-btt modal-lg\", 3, \"onAnyCloseEventFinished\"], [\"videoModal\", \"\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"w-100\", \"controls\", \"\", 4, \"ngIf\"], [\"identifier\", \"docModal\", \"customClass\", \"nsm-dialog-animation-btt modal-lg\", 3, \"onAnyCloseEventFinished\"], [\"docModal\", \"\"], [\"class\", \"p-1\", 4, \"ngIf\"], [1, \"font-weight-bold\"], [\"class\", \"font-weight-bold pointer\", 4, \"ngFor\", \"ngForOf\"], [1, \"font-weight-bold\", \"pointer\"], [4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-clipboard-check\"], [\"class\", \"font-weight-bold  pointer\", 4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-clipboard\"], [1, \"fas\", \"fa-play-circle\"], [1, \"text-center\"], [\"controls\", \"\", 1, \"w-100\"], [\"type\", \"video/mp4\", 3, \"src\"], [1, \"p-1\"], [\"class\", \"w-100 height-605\", 4, \"ngIf\"], [\"class\", \"card py-3 mb-2 height-205\", 4, \"ngIf\"], [1, \"w-100\", \"height-605\"], [3, \"src\", \"zoom\", \"showPrintButton\", \"showRotateButton\", \"showOpenFileButton\", \"showDownloadButton\", \"showBookmarkButton\", \"showSecondaryToolbarButton\", \"useBrowserLocale\", \"srcChange\"], [1, \"card\", \"py-3\", \"mb-2\", \"height-205\"], [1, \"card-body\"], [1, \"font-size-20\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"mt-2\", 3, \"click\"], [1, \"fa\", \"fa-download\"]],\n  template: function LearningHoursComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r47 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h1\", 4);\n      i0.ɵɵtext(6, \"Learning Hours\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(7, \"hr\", 5);\n      i0.ɵɵelementStart(8, \"div\", 6);\n      i0.ɵɵelementStart(9, \"form\", 7);\n      i0.ɵɵelementStart(10, \"div\", 6);\n      i0.ɵɵelementStart(11, \"div\", 8);\n      i0.ɵɵelementStart(12, \"div\", 9);\n      i0.ɵɵelementStart(13, \"label\", 10);\n      i0.ɵɵtext(14, \"Name\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(15, \"input\", 11);\n      i0.ɵɵelement(16, \"i\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"div\", 13);\n      i0.ɵɵelementStart(18, \"label\", 14);\n      i0.ɵɵtext(19, \"Category\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"ng-select\", 15, 16);\n      i0.ɵɵlistener(\"click\", function LearningHoursComponent_Template_ng_select_click_20_listener() {\n        i0.ɵɵrestoreView(_r47);\n\n        const _r0 = i0.ɵɵreference(21);\n\n        return ctx.handleSelectClick(_r0);\n      })(\"change\", function LearningHoursComponent_Template_ng_select_change_20_listener() {\n        return ctx.getList();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"ngx-datatable\", 17);\n      i0.ɵɵlistener(\"page\", function LearningHoursComponent_Template_ngx_datatable_page_22_listener($event) {\n        return ctx.setPage($event);\n      });\n      i0.ɵɵelementStart(23, \"ngx-datatable-column\", 18);\n      i0.ɵɵtemplate(24, LearningHoursComponent_ng_template_24_Template, 2, 1, \"ng-template\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(25, \"ngx-datatable-column\", 20);\n      i0.ɵɵtemplate(26, LearningHoursComponent_ng_template_26_Template, 2, 1, \"ng-template\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(27, \"ngx-datatable-column\", 21);\n      i0.ɵɵtemplate(28, LearningHoursComponent_ng_template_28_Template, 1, 1, \"ng-template\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(29, \"ngx-datatable-column\", 22);\n      i0.ɵɵtemplate(30, LearningHoursComponent_ng_template_30_Template, 1, 1, \"ng-template\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(31, \"ngx-datatable-column\", 23);\n      i0.ɵɵtemplate(32, LearningHoursComponent_ng_template_32_Template, 1, 1, \"ng-template\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(33, \"ngx-smart-modal\", 24, 25);\n      i0.ɵɵlistener(\"onAnyCloseEventFinished\", function LearningHoursComponent_Template_ngx_smart_modal_onAnyCloseEventFinished_33_listener() {\n        return ctx.modalClose();\n      });\n      i0.ɵɵtemplate(35, LearningHoursComponent_h4_35_Template, 2, 1, \"h4\", 26);\n      i0.ɵɵelementStart(36, \"div\");\n      i0.ɵɵtemplate(37, LearningHoursComponent_video_37_Template, 2, 2, \"video\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(38, \"ngx-smart-modal\", 28, 29);\n      i0.ɵɵlistener(\"onAnyCloseEventFinished\", function LearningHoursComponent_Template_ngx_smart_modal_onAnyCloseEventFinished_38_listener() {\n        return ctx.modalClose();\n      });\n      i0.ɵɵtemplate(40, LearningHoursComponent_h4_40_Template, 2, 1, \"h4\", 26);\n      i0.ɵɵelementStart(41, \"div\");\n      i0.ɵɵtemplate(42, LearningHoursComponent_div_42_Template, 3, 2, \"div\", 30);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(11);\n      i0.ɵɵproperty(\"clearable\", true)(\"clearOnBackspace\", true)(\"items\", ctx.categoryList);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"rows\", ctx.learningHoursContentGroupByList)(\"loadingIndicator\", ctx.loadingIndicator)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 50)(\"footerHeight\", 50)(\"externalPaging\", true)(\"count\", ctx.page.totalElements)(\"offset\", ctx.page.pageNumber)(\"limit\", ctx.page.size)(\"scrollbarH\", ctx.scrollBarHorizontal)(\"sorts\", i0.ɵɵpureFunction1(40, _c2, i0.ɵɵpureFunction0(39, _c1)));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", 10)(\"draggable\", false)(\"sortable\", false)(\"resizeable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false)(\"width\", 150)(\"resizeable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 5)(\"draggable\", false)(\"sortable\", false)(\"resizeable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 5)(\"draggable\", false)(\"sortable\", false)(\"resizeable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 5)(\"draggable\", false)(\"sortable\", false)(\"resizeable\", false);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedContent);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedContent);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedContent);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.docObj);\n    }\n  },\n  directives: [i8.BlockUIComponent, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.DefaultValueAccessor, i2.NgControlStatus, i2.FormControlName, i9.NgSelectComponent, i10.DatatableComponent, i10.DataTableColumnDirective, i10.DataTableColumnCellDirective, i11.NgForOf, i11.NgIf, i7.NgxSmartModalComponent, i12.NgxExtendedPdfViewerComponent],\n  styles: [\".pointer[_ngcontent-%COMP%]{cursor:pointer}.height-205[_ngcontent-%COMP%]{height:205px}.height-605[_ngcontent-%COMP%]{height:605px}\"]\n});\n\n__decorate([BlockUI()], LearningHoursComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}