{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function windowCount(windowSize, startWindowEvery = 0) {\n  const startEvery = startWindowEvery > 0 ? startWindowEvery : windowSize;\n  return operate((source, subscriber) => {\n    let windows = [new Subject()];\n    let starts = [];\n    let count = 0;\n    subscriber.next(windows[0].asObservable());\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      for (const window of windows) {\n        window.next(value);\n      }\n\n      const c = count - windowSize + 1;\n\n      if (c >= 0 && c % startEvery === 0) {\n        windows.shift().complete();\n      }\n\n      if (++count % startEvery === 0) {\n        const window = new Subject();\n        windows.push(window);\n        subscriber.next(window.asObservable());\n      }\n    }, () => {\n      while (windows.length > 0) {\n        windows.shift().complete();\n      }\n\n      subscriber.complete();\n    }, err => {\n      while (windows.length > 0) {\n        windows.shift().error(err);\n      }\n\n      subscriber.error(err);\n    }, () => {\n      starts = null;\n      windows = null;\n    }));\n  });\n} //# sourceMappingURL=windowCount.js.map", "map": null, "metadata": {}, "sourceType": "module"}