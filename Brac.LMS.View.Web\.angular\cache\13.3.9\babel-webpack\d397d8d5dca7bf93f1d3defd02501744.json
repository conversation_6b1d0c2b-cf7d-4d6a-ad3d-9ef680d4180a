{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, NgModule, InjectionToken, Injectable, Inject, SecurityContext, Component, HostBinding, HostListener } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { Subject } from 'rxjs';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i2 from '@angular/platform-browser';\nconst _c0 = [\"toast-component\", \"\"];\n\nfunction Toast_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function Toast_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return ctx_r5.remove();\n    });\n    i0.ɵɵelementStart(1, \"span\", 6);\n    i0.ɵɵtext(2, \"\\xD7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction Toast_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"[\", ctx_r7.duplicatesCount + 1, \"]\");\n  }\n}\n\nfunction Toast_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, Toast_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.titleClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.duplicatesCount);\n  }\n}\n\nfunction Toast_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.options.messageClass);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.message, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction Toast_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r3.options.messageClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r3.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.message, \" \");\n  }\n}\n\nfunction Toast_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r4.width + \"%\");\n  }\n}\n\nfunction ToastNoAnimation_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ToastNoAnimation_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return ctx_r5.remove();\n    });\n    i0.ɵɵelementStart(1, \"span\", 6);\n    i0.ɵɵtext(2, \"\\xD7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ToastNoAnimation_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"[\", ctx_r7.duplicatesCount + 1, \"]\");\n  }\n}\n\nfunction ToastNoAnimation_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, ToastNoAnimation_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.titleClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.duplicatesCount);\n  }\n}\n\nfunction ToastNoAnimation_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.options.messageClass);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.message, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction ToastNoAnimation_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r3.options.messageClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r3.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.message, \" \");\n  }\n}\n\nfunction ToastNoAnimation_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r4.width + \"%\");\n  }\n}\n\nlet ToastContainerDirective = /*#__PURE__*/(() => {\n  class ToastContainerDirective {\n    constructor(el) {\n      this.el = el;\n    }\n\n    getContainerElement() {\n      return this.el.nativeElement;\n    }\n\n  }\n\n  ToastContainerDirective.ɵfac = function ToastContainerDirective_Factory(t) {\n    return new (t || ToastContainerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n\n  ToastContainerDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ToastContainerDirective,\n    selectors: [[\"\", \"toastContainer\", \"\"]],\n    exportAs: [\"toastContainer\"]\n  });\n  return ToastContainerDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet ToastContainerModule = /*#__PURE__*/(() => {\n  class ToastContainerModule {}\n\n  ToastContainerModule.ɵfac = function ToastContainerModule_Factory(t) {\n    return new (t || ToastContainerModule)();\n  };\n\n  ToastContainerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ToastContainerModule\n  });\n  ToastContainerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return ToastContainerModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\n\n\nclass ComponentPortal {\n  constructor(component, injector) {\n    this.component = component;\n    this.injector = injector;\n  }\n  /** Attach this portal to a host. */\n\n\n  attach(host, newestOnTop) {\n    this._attachedHost = host;\n    return host.attach(this, newestOnTop);\n  }\n  /** Detach this portal from its host */\n\n\n  detach() {\n    const host = this._attachedHost;\n\n    if (host) {\n      this._attachedHost = undefined;\n      return host.detach();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n\n\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalHost reference without performing `attach()`. This is used directly by\n   * the PortalHost when it is performing an `attach()` or `detach()`.\n   */\n\n\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n\n}\n/**\n * Partial implementation of PortalHost that only deals with attaching a\n * ComponentPortal\n */\n\n\nclass BasePortalHost {\n  attach(portal, newestOnTop) {\n    this._attachedPortal = portal;\n    return this.attachComponentPortal(portal, newestOnTop);\n  }\n\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost();\n    }\n\n    this._attachedPortal = undefined;\n\n    if (this._disposeFn) {\n      this._disposeFn();\n\n      this._disposeFn = undefined;\n    }\n  }\n\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n\n}\n/**\n * Everything a toast needs to launch\n */\n\n\nclass ToastPackage {\n  constructor(toastId, config, message, title, toastType, toastRef) {\n    this.toastId = toastId;\n    this.config = config;\n    this.message = message;\n    this.title = title;\n    this.toastType = toastType;\n    this.toastRef = toastRef;\n    this._onTap = new Subject();\n    this._onAction = new Subject();\n    this.toastRef.afterClosed().subscribe(() => {\n      this._onAction.complete();\n\n      this._onTap.complete();\n    });\n  }\n  /** Fired on click */\n\n\n  triggerTap() {\n    this._onTap.next();\n\n    if (this.config.tapToDismiss) {\n      this._onTap.complete();\n    }\n  }\n\n  onTap() {\n    return this._onTap.asObservable();\n  }\n  /** available for use in custom toast */\n\n\n  triggerAction(action) {\n    this._onAction.next(action);\n  }\n\n  onAction() {\n    return this._onAction.asObservable();\n  }\n\n}\n\nconst DefaultNoComponentGlobalConfig = {\n  maxOpened: 0,\n  autoDismiss: false,\n  newestOnTop: true,\n  preventDuplicates: false,\n  countDuplicates: false,\n  resetTimeoutOnDuplicate: false,\n  includeTitleDuplicates: false,\n  iconClasses: {\n    error: 'toast-error',\n    info: 'toast-info',\n    success: 'toast-success',\n    warning: 'toast-warning'\n  },\n  // Individual\n  closeButton: false,\n  disableTimeOut: false,\n  timeOut: 5000,\n  extendedTimeOut: 1000,\n  enableHtml: false,\n  progressBar: false,\n  toastClass: 'ngx-toastr',\n  positionClass: 'toast-top-right',\n  titleClass: 'toast-title',\n  messageClass: 'toast-message',\n  easing: 'ease-in',\n  easeTime: 300,\n  tapToDismiss: true,\n  onActivateTick: false,\n  progressAnimation: 'decreasing',\n  payload: null\n};\nconst TOAST_CONFIG = new InjectionToken('ToastConfig');\n/**\n * Reference to a toast opened via the Toastr service.\n */\n\nclass ToastRef {\n  constructor(_overlayRef) {\n    this._overlayRef = _overlayRef;\n    /** Count of duplicates of this toast */\n\n    this.duplicatesCount = 0;\n    /** Subject for notifying the user that the toast has finished closing. */\n\n    this._afterClosed = new Subject();\n    /** triggered when toast is activated */\n\n    this._activate = new Subject();\n    /** notifies the toast that it should close before the timeout */\n\n    this._manualClose = new Subject();\n    /** notifies the toast that it should reset the timeouts */\n\n    this._resetTimeout = new Subject();\n    /** notifies the toast that it should count a duplicate toast */\n\n    this._countDuplicate = new Subject();\n  }\n\n  manualClose() {\n    this._manualClose.next();\n\n    this._manualClose.complete();\n  }\n\n  manualClosed() {\n    return this._manualClose.asObservable();\n  }\n\n  timeoutReset() {\n    return this._resetTimeout.asObservable();\n  }\n\n  countDuplicate() {\n    return this._countDuplicate.asObservable();\n  }\n  /**\n   * Close the toast.\n   */\n\n\n  close() {\n    this._overlayRef.detach();\n\n    this._afterClosed.next();\n\n    this._manualClose.next();\n\n    this._afterClosed.complete();\n\n    this._manualClose.complete();\n\n    this._activate.complete();\n\n    this._resetTimeout.complete();\n\n    this._countDuplicate.complete();\n  }\n  /** Gets an observable that is notified when the toast is finished closing. */\n\n\n  afterClosed() {\n    return this._afterClosed.asObservable();\n  }\n\n  isInactive() {\n    return this._activate.isStopped;\n  }\n\n  activate() {\n    this._activate.next();\n\n    this._activate.complete();\n  }\n  /** Gets an observable that is notified when the toast has started opening. */\n\n\n  afterActivate() {\n    return this._activate.asObservable();\n  }\n  /** Reset the toast timouts and count duplicates */\n\n\n  onDuplicate(resetTimeout, countDuplicate) {\n    if (resetTimeout) {\n      this._resetTimeout.next();\n    }\n\n    if (countDuplicate) {\n      this._countDuplicate.next(++this.duplicatesCount);\n    }\n  }\n\n}\n/** Custom injector type specifically for instantiating components with a toast. */\n\n\nclass ToastInjector {\n  constructor(_toastPackage, _parentInjector) {\n    this._toastPackage = _toastPackage;\n    this._parentInjector = _parentInjector;\n  }\n\n  get(token, notFoundValue, flags) {\n    if (token === ToastPackage) {\n      return this._toastPackage;\n    }\n\n    return this._parentInjector.get(token, notFoundValue, flags);\n  }\n\n}\n/**\n * A PortalHost for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n *\n * This is the only part of the portal core that directly touches the DOM.\n */\n\n\nclass DomPortalHost extends BasePortalHost {\n  constructor(_hostDomElement, _componentFactoryResolver, _appRef) {\n    super();\n    this._hostDomElement = _hostDomElement;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   */\n\n\n  attachComponentPortal(portal, newestOnTop) {\n    const componentFactory = this._componentFactoryResolver.resolveComponentFactory(portal.component);\n\n    let componentRef; // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the ChangeDetector for that component to the application (which\n    // happens automatically when using a ViewContainer).\n\n    componentRef = componentFactory.create(portal.injector); // When creating a component outside of a ViewContainer, we need to manually register\n    // its ChangeDetector with the application. This API is unfortunately not yet published\n    // in Angular core. The change detector must also be deregistered when the component\n    // is destroyed to prevent memory leaks.\n\n    this._appRef.attachView(componentRef.hostView);\n\n    this.setDisposeFn(() => {\n      this._appRef.detachView(componentRef.hostView);\n\n      componentRef.destroy();\n    }); // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n\n    if (newestOnTop) {\n      this._hostDomElement.insertBefore(this._getComponentRootNode(componentRef), this._hostDomElement.firstChild);\n    } else {\n      this._hostDomElement.appendChild(this._getComponentRootNode(componentRef));\n    }\n\n    return componentRef;\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n\n\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n\n}\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\n\n\nclass OverlayRef {\n  constructor(_portalHost) {\n    this._portalHost = _portalHost;\n  }\n\n  attach(portal, newestOnTop = true) {\n    return this._portalHost.attach(portal, newestOnTop);\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns Resolves when the overlay has been detached.\n   */\n\n\n  detach() {\n    return this._portalHost.detach();\n  }\n\n}\n/** Container inside which all toasts will render. */\n\n\nlet OverlayContainer = /*#__PURE__*/(() => {\n  class OverlayContainer {\n    constructor(_document) {\n      this._document = _document;\n    }\n\n    ngOnDestroy() {\n      if (this._containerElement && this._containerElement.parentNode) {\n        this._containerElement.parentNode.removeChild(this._containerElement);\n      }\n    }\n    /**\n     * This method returns the overlay container element. It will lazily\n     * create the element the first time  it is called to facilitate using\n     * the container in non-browser environments.\n     * @returns the container element\n     */\n\n\n    getContainerElement() {\n      if (!this._containerElement) {\n        this._createContainer();\n      }\n\n      return this._containerElement;\n    }\n    /**\n     * Create the overlay container element, which is simply a div\n     * with the 'cdk-overlay-container' class on the document body.\n     */\n\n\n    _createContainer() {\n      const container = this._document.createElement('div');\n\n      container.classList.add('overlay-container');\n\n      this._document.body.appendChild(container);\n\n      this._containerElement = container;\n    }\n\n  }\n\n  OverlayContainer.ɵfac = function OverlayContainer_Factory(t) {\n    return new (t || OverlayContainer)(i0.ɵɵinject(DOCUMENT));\n  };\n\n  OverlayContainer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayContainer,\n    factory: OverlayContainer.ɵfac,\n    providedIn: 'root'\n  });\n  return OverlayContainer;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/* eslint-disable @typescript-eslint/no-non-null-assertion */\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalHost, so any kind of Portal can be loaded into one.\n */\n\n\nlet Overlay = /*#__PURE__*/(() => {\n  class Overlay {\n    constructor(_overlayContainer, _componentFactoryResolver, _appRef, _document) {\n      this._overlayContainer = _overlayContainer;\n      this._componentFactoryResolver = _componentFactoryResolver;\n      this._appRef = _appRef;\n      this._document = _document; // Namespace panes by overlay container\n\n      this._paneElements = new Map();\n    }\n    /**\n     * Creates an overlay.\n     * @returns A reference to the created overlay.\n     */\n\n\n    create(positionClass, overlayContainer) {\n      // get existing pane if possible\n      return this._createOverlayRef(this.getPaneElement(positionClass, overlayContainer));\n    }\n\n    getPaneElement(positionClass = '', overlayContainer) {\n      if (!this._paneElements.get(overlayContainer)) {\n        this._paneElements.set(overlayContainer, {});\n      }\n\n      if (!this._paneElements.get(overlayContainer)[positionClass]) {\n        this._paneElements.get(overlayContainer)[positionClass] = this._createPaneElement(positionClass, overlayContainer);\n      }\n\n      return this._paneElements.get(overlayContainer)[positionClass];\n    }\n    /**\n     * Creates the DOM element for an overlay and appends it to the overlay container.\n     * @returns Newly-created pane element\n     */\n\n\n    _createPaneElement(positionClass, overlayContainer) {\n      const pane = this._document.createElement('div');\n\n      pane.id = 'toast-container';\n      pane.classList.add(positionClass);\n      pane.classList.add('toast-container');\n\n      if (!overlayContainer) {\n        this._overlayContainer.getContainerElement().appendChild(pane);\n      } else {\n        overlayContainer.getContainerElement().appendChild(pane);\n      }\n\n      return pane;\n    }\n    /**\n     * Create a DomPortalHost into which the overlay content can be loaded.\n     * @param pane The DOM element to turn into a portal host.\n     * @returns A portal host for the given DOM element.\n     */\n\n\n    _createPortalHost(pane) {\n      return new DomPortalHost(pane, this._componentFactoryResolver, this._appRef);\n    }\n    /**\n     * Creates an OverlayRef for an overlay in the given DOM element.\n     * @param pane DOM element for the overlay\n     */\n\n\n    _createOverlayRef(pane) {\n      return new OverlayRef(this._createPortalHost(pane));\n    }\n\n  }\n\n  Overlay.ɵfac = function Overlay_Factory(t) {\n    return new (t || Overlay)(i0.ɵɵinject(OverlayContainer), i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(i0.ApplicationRef), i0.ɵɵinject(DOCUMENT));\n  };\n\n  Overlay.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Overlay,\n    factory: Overlay.ɵfac,\n    providedIn: 'root'\n  });\n  return Overlay;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet ToastrService = /*#__PURE__*/(() => {\n  class ToastrService {\n    constructor(token, overlay, _injector, sanitizer, ngZone) {\n      this.overlay = overlay;\n      this._injector = _injector;\n      this.sanitizer = sanitizer;\n      this.ngZone = ngZone;\n      this.currentlyActive = 0;\n      this.toasts = [];\n      this.index = 0;\n      this.toastrConfig = Object.assign(Object.assign({}, token.default), token.config);\n\n      if (token.config.iconClasses) {\n        this.toastrConfig.iconClasses = Object.assign(Object.assign({}, token.default.iconClasses), token.config.iconClasses);\n      }\n    }\n    /** show toast */\n\n\n    show(message, title, override = {}, type = '') {\n      return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show successful toast */\n\n\n    success(message, title, override = {}) {\n      const type = this.toastrConfig.iconClasses.success || '';\n      return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show error toast */\n\n\n    error(message, title, override = {}) {\n      const type = this.toastrConfig.iconClasses.error || '';\n      return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show info toast */\n\n\n    info(message, title, override = {}) {\n      const type = this.toastrConfig.iconClasses.info || '';\n      return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show warning toast */\n\n\n    warning(message, title, override = {}) {\n      const type = this.toastrConfig.iconClasses.warning || '';\n      return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /**\n     * Remove all or a single toast by id\n     */\n\n\n    clear(toastId) {\n      // Call every toastRef manualClose function\n      for (const toast of this.toasts) {\n        if (toastId !== undefined) {\n          if (toast.toastId === toastId) {\n            toast.toastRef.manualClose();\n            return;\n          }\n        } else {\n          toast.toastRef.manualClose();\n        }\n      }\n    }\n    /**\n     * Remove and destroy a single toast by id\n     */\n\n\n    remove(toastId) {\n      const found = this._findToast(toastId);\n\n      if (!found) {\n        return false;\n      }\n\n      found.activeToast.toastRef.close();\n      this.toasts.splice(found.index, 1);\n      this.currentlyActive = this.currentlyActive - 1;\n\n      if (!this.toastrConfig.maxOpened || !this.toasts.length) {\n        return false;\n      }\n\n      if (this.currentlyActive < this.toastrConfig.maxOpened && this.toasts[this.currentlyActive]) {\n        const p = this.toasts[this.currentlyActive].toastRef;\n\n        if (!p.isInactive()) {\n          this.currentlyActive = this.currentlyActive + 1;\n          p.activate();\n        }\n      }\n\n      return true;\n    }\n    /**\n     * Determines if toast message is already shown\n     */\n\n\n    findDuplicate(title = '', message = '', resetOnDuplicate, countDuplicates) {\n      const {\n        includeTitleDuplicates\n      } = this.toastrConfig;\n\n      for (const toast of this.toasts) {\n        const hasDuplicateTitle = includeTitleDuplicates && toast.title === title;\n\n        if ((!includeTitleDuplicates || hasDuplicateTitle) && toast.message === message) {\n          toast.toastRef.onDuplicate(resetOnDuplicate, countDuplicates);\n          return toast;\n        }\n      }\n\n      return null;\n    }\n    /** create a clone of global config and apply individual settings */\n\n\n    applyConfig(override = {}) {\n      return Object.assign(Object.assign({}, this.toastrConfig), override);\n    }\n    /**\n     * Find toast object by id\n     */\n\n\n    _findToast(toastId) {\n      for (let i = 0; i < this.toasts.length; i++) {\n        if (this.toasts[i].toastId === toastId) {\n          return {\n            index: i,\n            activeToast: this.toasts[i]\n          };\n        }\n      }\n\n      return null;\n    }\n    /**\n     * Determines the need to run inside angular's zone then builds the toast\n     */\n\n\n    _preBuildNotification(toastType, message, title, config) {\n      if (config.onActivateTick) {\n        return this.ngZone.run(() => this._buildNotification(toastType, message, title, config));\n      }\n\n      return this._buildNotification(toastType, message, title, config);\n    }\n    /**\n     * Creates and attaches toast data to component\n     * returns the active toast, or in case preventDuplicates is enabled the original/non-duplicate active toast.\n     */\n\n\n    _buildNotification(toastType, message, title, config) {\n      if (!config.toastComponent) {\n        throw new Error('toastComponent required');\n      } // max opened and auto dismiss = true\n      // if timeout = 0 resetting it would result in setting this.hideTime = Date.now(). Hence, we only want to reset timeout if there is\n      // a timeout at all\n\n\n      const duplicate = this.findDuplicate(title, message, this.toastrConfig.resetTimeoutOnDuplicate && config.timeOut > 0, this.toastrConfig.countDuplicates);\n\n      if ((this.toastrConfig.includeTitleDuplicates && title || message) && this.toastrConfig.preventDuplicates && duplicate !== null) {\n        return duplicate;\n      }\n\n      this.previousToastMessage = message;\n      let keepInactive = false;\n\n      if (this.toastrConfig.maxOpened && this.currentlyActive >= this.toastrConfig.maxOpened) {\n        keepInactive = true;\n\n        if (this.toastrConfig.autoDismiss) {\n          this.clear(this.toasts[0].toastId);\n        }\n      }\n\n      const overlayRef = this.overlay.create(config.positionClass, this.overlayContainer);\n      this.index = this.index + 1;\n      let sanitizedMessage = message;\n\n      if (message && config.enableHtml) {\n        sanitizedMessage = this.sanitizer.sanitize(SecurityContext.HTML, message);\n      }\n\n      const toastRef = new ToastRef(overlayRef);\n      const toastPackage = new ToastPackage(this.index, config, sanitizedMessage, title, toastType, toastRef);\n      const toastInjector = new ToastInjector(toastPackage, this._injector);\n      const component = new ComponentPortal(config.toastComponent, toastInjector);\n      const portal = overlayRef.attach(component, this.toastrConfig.newestOnTop);\n      toastRef.componentInstance = portal.instance;\n      const ins = {\n        toastId: this.index,\n        title: title || '',\n        message: message || '',\n        toastRef,\n        onShown: toastRef.afterActivate(),\n        onHidden: toastRef.afterClosed(),\n        onTap: toastPackage.onTap(),\n        onAction: toastPackage.onAction(),\n        portal\n      };\n\n      if (!keepInactive) {\n        this.currentlyActive = this.currentlyActive + 1;\n        setTimeout(() => {\n          ins.toastRef.activate();\n        });\n      }\n\n      this.toasts.push(ins);\n      return ins;\n    }\n\n  }\n\n  ToastrService.ɵfac = function ToastrService_Factory(t) {\n    return new (t || ToastrService)(i0.ɵɵinject(TOAST_CONFIG), i0.ɵɵinject(Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i2.DomSanitizer), i0.ɵɵinject(i0.NgZone));\n  };\n\n  ToastrService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ToastrService,\n    factory: ToastrService.ɵfac,\n    providedIn: 'root'\n  });\n  return ToastrService;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet Toast = /*#__PURE__*/(() => {\n  class Toast {\n    constructor(toastrService, toastPackage, ngZone) {\n      this.toastrService = toastrService;\n      this.toastPackage = toastPackage;\n      this.ngZone = ngZone;\n      /** width of progress bar */\n\n      this.width = -1;\n      /** a combination of toast type and options.toastClass */\n\n      this.toastClasses = '';\n      /** controls animation */\n\n      this.state = {\n        value: 'inactive',\n        params: {\n          easeTime: this.toastPackage.config.easeTime,\n          easing: 'ease-in'\n        }\n      };\n      this.message = toastPackage.message;\n      this.title = toastPackage.title;\n      this.options = toastPackage.config;\n      this.originalTimeout = toastPackage.config.timeOut;\n      this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n      this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n        this.activateToast();\n      });\n      this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n        this.remove();\n      });\n      this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n        this.resetTimeout();\n      });\n      this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n        this.duplicatesCount = count;\n      });\n    }\n    /** hides component when waiting to be displayed */\n\n\n    get displayStyle() {\n      if (this.state.value === 'inactive') {\n        return 'none';\n      }\n\n      return;\n    }\n\n    ngOnDestroy() {\n      this.sub.unsubscribe();\n      this.sub1.unsubscribe();\n      this.sub2.unsubscribe();\n      this.sub3.unsubscribe();\n      clearInterval(this.intervalId);\n      clearTimeout(this.timeout);\n    }\n    /**\n     * activates toast and sets timeout\n     */\n\n\n    activateToast() {\n      this.state = Object.assign(Object.assign({}, this.state), {\n        value: 'active'\n      });\n\n      if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n        this.outsideTimeout(() => this.remove(), this.options.timeOut);\n        this.hideTime = new Date().getTime() + this.options.timeOut;\n\n        if (this.options.progressBar) {\n          this.outsideInterval(() => this.updateProgress(), 10);\n        }\n      }\n    }\n    /**\n     * updates progress bar width\n     */\n\n\n    updateProgress() {\n      if (this.width === 0 || this.width === 100 || !this.options.timeOut) {\n        return;\n      }\n\n      const now = new Date().getTime();\n      const remaining = this.hideTime - now;\n      this.width = remaining / this.options.timeOut * 100;\n\n      if (this.options.progressAnimation === 'increasing') {\n        this.width = 100 - this.width;\n      }\n\n      if (this.width <= 0) {\n        this.width = 0;\n      }\n\n      if (this.width >= 100) {\n        this.width = 100;\n      }\n    }\n\n    resetTimeout() {\n      clearTimeout(this.timeout);\n      clearInterval(this.intervalId);\n      this.state = Object.assign(Object.assign({}, this.state), {\n        value: 'active'\n      });\n      this.outsideTimeout(() => this.remove(), this.originalTimeout);\n      this.options.timeOut = this.originalTimeout;\n      this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n      this.width = -1;\n\n      if (this.options.progressBar) {\n        this.outsideInterval(() => this.updateProgress(), 10);\n      }\n    }\n    /**\n     * tells toastrService to remove this toast after animation time\n     */\n\n\n    remove() {\n      if (this.state.value === 'removed') {\n        return;\n      }\n\n      clearTimeout(this.timeout);\n      this.state = Object.assign(Object.assign({}, this.state), {\n        value: 'removed'\n      });\n      this.outsideTimeout(() => this.toastrService.remove(this.toastPackage.toastId), +this.toastPackage.config.easeTime);\n    }\n\n    tapToast() {\n      if (this.state.value === 'removed') {\n        return;\n      }\n\n      this.toastPackage.triggerTap();\n\n      if (this.options.tapToDismiss) {\n        this.remove();\n      }\n    }\n\n    stickAround() {\n      if (this.state.value === 'removed') {\n        return;\n      }\n\n      clearTimeout(this.timeout);\n      this.options.timeOut = 0;\n      this.hideTime = 0; // disable progressBar\n\n      clearInterval(this.intervalId);\n      this.width = 0;\n    }\n\n    delayedHideToast() {\n      if (this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut' || this.options.extendedTimeOut === 0 || this.state.value === 'removed') {\n        return;\n      }\n\n      this.outsideTimeout(() => this.remove(), this.options.extendedTimeOut);\n      this.options.timeOut = this.options.extendedTimeOut;\n      this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n      this.width = -1;\n\n      if (this.options.progressBar) {\n        this.outsideInterval(() => this.updateProgress(), 10);\n      }\n    }\n\n    outsideTimeout(func, timeout) {\n      if (this.ngZone) {\n        this.ngZone.runOutsideAngular(() => this.timeout = setTimeout(() => this.runInsideAngular(func), timeout));\n      } else {\n        this.timeout = setTimeout(() => func(), timeout);\n      }\n    }\n\n    outsideInterval(func, timeout) {\n      if (this.ngZone) {\n        this.ngZone.runOutsideAngular(() => this.intervalId = setInterval(() => this.runInsideAngular(func), timeout));\n      } else {\n        this.intervalId = setInterval(() => func(), timeout);\n      }\n    }\n\n    runInsideAngular(func) {\n      if (this.ngZone) {\n        this.ngZone.run(() => func());\n      } else {\n        func();\n      }\n    }\n\n  }\n\n  Toast.ɵfac = function Toast_Factory(t) {\n    return new (t || Toast)(i0.ɵɵdirectiveInject(ToastrService), i0.ɵɵdirectiveInject(ToastPackage), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n\n  Toast.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Toast,\n    selectors: [[\"\", \"toast-component\", \"\"]],\n    hostVars: 5,\n    hostBindings: function Toast_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function Toast_click_HostBindingHandler() {\n          return ctx.tapToast();\n        })(\"mouseenter\", function Toast_mouseenter_HostBindingHandler() {\n          return ctx.stickAround();\n        })(\"mouseleave\", function Toast_mouseleave_HostBindingHandler() {\n          return ctx.delayedHideToast();\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵsyntheticHostProperty(\"@flyInOut\", ctx.state);\n        i0.ɵɵclassMap(ctx.toastClasses);\n        i0.ɵɵstyleProp(\"display\", ctx.displayStyle);\n      }\n    },\n    attrs: _c0,\n    decls: 5,\n    vars: 5,\n    consts: [[\"type\", \"button\", \"class\", \"toast-close-button\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"alertdialog\", \"aria-live\", \"polite\", 3, \"class\", \"innerHTML\", 4, \"ngIf\"], [\"role\", \"alertdialog\", \"aria-live\", \"polite\", 3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"toast-close-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"role\", \"alertdialog\", \"aria-live\", \"polite\", 3, \"innerHTML\"], [\"role\", \"alertdialog\", \"aria-live\", \"polite\"], [1, \"toast-progress\"]],\n    template: function Toast_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, Toast_button_0_Template, 3, 0, \"button\", 0);\n        i0.ɵɵtemplate(1, Toast_div_1_Template, 3, 5, \"div\", 1);\n        i0.ɵɵtemplate(2, Toast_div_2_Template, 1, 3, \"div\", 2);\n        i0.ɵɵtemplate(3, Toast_div_3_Template, 2, 4, \"div\", 3);\n        i0.ɵɵtemplate(4, Toast_div_4_Template, 2, 2, \"div\", 4);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.options.closeButton);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.title);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.message && ctx.options.enableHtml);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.message && !ctx.options.enableHtml);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.options.progressBar);\n      }\n    },\n    directives: [i3.NgIf],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('flyInOut', [state('inactive', style({\n        opacity: 0\n      })), state('active', style({\n        opacity: 1\n      })), state('removed', style({\n        opacity: 0\n      })), transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')), transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))])]\n    }\n  });\n  return Toast;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst DefaultGlobalConfig = Object.assign(Object.assign({}, DefaultNoComponentGlobalConfig), {\n  toastComponent: Toast\n});\nlet ToastrModule = /*#__PURE__*/(() => {\n  class ToastrModule {\n    static forRoot(config = {}) {\n      return {\n        ngModule: ToastrModule,\n        providers: [{\n          provide: TOAST_CONFIG,\n          useValue: {\n            default: DefaultGlobalConfig,\n            config\n          }\n        }]\n      };\n    }\n\n  }\n\n  ToastrModule.ɵfac = function ToastrModule_Factory(t) {\n    return new (t || ToastrModule)();\n  };\n\n  ToastrModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ToastrModule\n  });\n  ToastrModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule]]\n  });\n  return ToastrModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet ToastrComponentlessModule = /*#__PURE__*/(() => {\n  class ToastrComponentlessModule {\n    static forRoot(config = {}) {\n      return {\n        ngModule: ToastrModule,\n        providers: [{\n          provide: TOAST_CONFIG,\n          useValue: {\n            default: DefaultNoComponentGlobalConfig,\n            config\n          }\n        }]\n      };\n    }\n\n  }\n\n  ToastrComponentlessModule.ɵfac = function ToastrComponentlessModule_Factory(t) {\n    return new (t || ToastrComponentlessModule)();\n  };\n\n  ToastrComponentlessModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ToastrComponentlessModule\n  });\n  ToastrComponentlessModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule]]\n  });\n  return ToastrComponentlessModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet ToastNoAnimation = /*#__PURE__*/(() => {\n  class ToastNoAnimation {\n    constructor(toastrService, toastPackage, appRef) {\n      this.toastrService = toastrService;\n      this.toastPackage = toastPackage;\n      this.appRef = appRef;\n      /** width of progress bar */\n\n      this.width = -1;\n      /** a combination of toast type and options.toastClass */\n\n      this.toastClasses = '';\n      /** controls animation */\n\n      this.state = 'inactive';\n      this.message = toastPackage.message;\n      this.title = toastPackage.title;\n      this.options = toastPackage.config;\n      this.originalTimeout = toastPackage.config.timeOut;\n      this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n      this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n        this.activateToast();\n      });\n      this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n        this.remove();\n      });\n      this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n        this.resetTimeout();\n      });\n      this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n        this.duplicatesCount = count;\n      });\n    }\n    /** hides component when waiting to be displayed */\n\n\n    get displayStyle() {\n      if (this.state === 'inactive') {\n        return 'none';\n      }\n    }\n\n    ngOnDestroy() {\n      this.sub.unsubscribe();\n      this.sub1.unsubscribe();\n      this.sub2.unsubscribe();\n      this.sub3.unsubscribe();\n      clearInterval(this.intervalId);\n      clearTimeout(this.timeout);\n    }\n    /**\n     * activates toast and sets timeout\n     */\n\n\n    activateToast() {\n      this.state = 'active';\n\n      if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n        this.timeout = setTimeout(() => {\n          this.remove();\n        }, this.options.timeOut);\n        this.hideTime = new Date().getTime() + this.options.timeOut;\n\n        if (this.options.progressBar) {\n          this.intervalId = setInterval(() => this.updateProgress(), 10);\n        }\n      }\n\n      if (this.options.onActivateTick) {\n        this.appRef.tick();\n      }\n    }\n    /**\n     * updates progress bar width\n     */\n\n\n    updateProgress() {\n      if (this.width === 0 || this.width === 100 || !this.options.timeOut) {\n        return;\n      }\n\n      const now = new Date().getTime();\n      const remaining = this.hideTime - now;\n      this.width = remaining / this.options.timeOut * 100;\n\n      if (this.options.progressAnimation === 'increasing') {\n        this.width = 100 - this.width;\n      }\n\n      if (this.width <= 0) {\n        this.width = 0;\n      }\n\n      if (this.width >= 100) {\n        this.width = 100;\n      }\n    }\n\n    resetTimeout() {\n      clearTimeout(this.timeout);\n      clearInterval(this.intervalId);\n      this.state = 'active';\n      this.options.timeOut = this.originalTimeout;\n      this.timeout = setTimeout(() => this.remove(), this.originalTimeout);\n      this.hideTime = new Date().getTime() + (this.originalTimeout || 0);\n      this.width = -1;\n\n      if (this.options.progressBar) {\n        this.intervalId = setInterval(() => this.updateProgress(), 10);\n      }\n    }\n    /**\n     * tells toastrService to remove this toast after animation time\n     */\n\n\n    remove() {\n      if (this.state === 'removed') {\n        return;\n      }\n\n      clearTimeout(this.timeout);\n      this.state = 'removed';\n      this.timeout = setTimeout(() => this.toastrService.remove(this.toastPackage.toastId));\n    }\n\n    tapToast() {\n      if (this.state === 'removed') {\n        return;\n      }\n\n      this.toastPackage.triggerTap();\n\n      if (this.options.tapToDismiss) {\n        this.remove();\n      }\n    }\n\n    stickAround() {\n      if (this.state === 'removed') {\n        return;\n      }\n\n      clearTimeout(this.timeout);\n      this.options.timeOut = 0;\n      this.hideTime = 0; // disable progressBar\n\n      clearInterval(this.intervalId);\n      this.width = 0;\n    }\n\n    delayedHideToast() {\n      if (this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut' || this.options.extendedTimeOut === 0 || this.state === 'removed') {\n        return;\n      }\n\n      this.timeout = setTimeout(() => this.remove(), this.options.extendedTimeOut);\n      this.options.timeOut = this.options.extendedTimeOut;\n      this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n      this.width = -1;\n\n      if (this.options.progressBar) {\n        this.intervalId = setInterval(() => this.updateProgress(), 10);\n      }\n    }\n\n  }\n\n  ToastNoAnimation.ɵfac = function ToastNoAnimation_Factory(t) {\n    return new (t || ToastNoAnimation)(i0.ɵɵdirectiveInject(ToastrService), i0.ɵɵdirectiveInject(ToastPackage), i0.ɵɵdirectiveInject(i0.ApplicationRef));\n  };\n\n  ToastNoAnimation.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ToastNoAnimation,\n    selectors: [[\"\", \"toast-component\", \"\"]],\n    hostVars: 4,\n    hostBindings: function ToastNoAnimation_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function ToastNoAnimation_click_HostBindingHandler() {\n          return ctx.tapToast();\n        })(\"mouseenter\", function ToastNoAnimation_mouseenter_HostBindingHandler() {\n          return ctx.stickAround();\n        })(\"mouseleave\", function ToastNoAnimation_mouseleave_HostBindingHandler() {\n          return ctx.delayedHideToast();\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.toastClasses);\n        i0.ɵɵstyleProp(\"display\", ctx.displayStyle);\n      }\n    },\n    attrs: _c0,\n    decls: 5,\n    vars: 5,\n    consts: [[\"type\", \"button\", \"class\", \"toast-close-button\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"alert\", \"aria-live\", \"polite\", 3, \"class\", \"innerHTML\", 4, \"ngIf\"], [\"role\", \"alert\", \"aria-live\", \"polite\", 3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"toast-close-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"role\", \"alert\", \"aria-live\", \"polite\", 3, \"innerHTML\"], [\"role\", \"alert\", \"aria-live\", \"polite\"], [1, \"toast-progress\"]],\n    template: function ToastNoAnimation_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ToastNoAnimation_button_0_Template, 3, 0, \"button\", 0);\n        i0.ɵɵtemplate(1, ToastNoAnimation_div_1_Template, 3, 5, \"div\", 1);\n        i0.ɵɵtemplate(2, ToastNoAnimation_div_2_Template, 1, 3, \"div\", 2);\n        i0.ɵɵtemplate(3, ToastNoAnimation_div_3_Template, 2, 4, \"div\", 3);\n        i0.ɵɵtemplate(4, ToastNoAnimation_div_4_Template, 2, 2, \"div\", 4);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.options.closeButton);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.title);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.message && ctx.options.enableHtml);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.message && !ctx.options.enableHtml);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.options.progressBar);\n      }\n    },\n    directives: [i3.NgIf],\n    encapsulation: 2\n  });\n  return ToastNoAnimation;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nconst DefaultNoAnimationsGlobalConfig = Object.assign(Object.assign({}, DefaultNoComponentGlobalConfig), {\n  toastComponent: ToastNoAnimation\n});\nlet ToastNoAnimationModule = /*#__PURE__*/(() => {\n  class ToastNoAnimationModule {\n    static forRoot(config = {}) {\n      return {\n        ngModule: ToastNoAnimationModule,\n        providers: [{\n          provide: TOAST_CONFIG,\n          useValue: {\n            default: DefaultNoAnimationsGlobalConfig,\n            config\n          }\n        }]\n      };\n    }\n\n  }\n\n  ToastNoAnimationModule.ɵfac = function ToastNoAnimationModule_Factory(t) {\n    return new (t || ToastNoAnimationModule)();\n  };\n\n  ToastNoAnimationModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ToastNoAnimationModule\n  });\n  ToastNoAnimationModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule]]\n  });\n  return ToastNoAnimationModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BasePortalHost, ComponentPortal, DefaultGlobalConfig, DefaultNoAnimationsGlobalConfig, DefaultNoComponentGlobalConfig, Overlay, OverlayContainer, OverlayRef, TOAST_CONFIG, Toast, ToastContainerDirective, ToastContainerModule, ToastInjector, ToastNoAnimation, ToastNoAnimationModule, ToastPackage, ToastRef, ToastrComponentlessModule, ToastrModule, ToastrService }; //# sourceMappingURL=ngx-toastr.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}