{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Pipe, EventEmitter, Component, Input, Output, NgModule } from '@angular/core';\nimport * as i3 from '@angular/forms';\nimport { FormGroup, FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nfunction NgOtpInputComponent_div_0_input_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 3, 4);\n    i0.ɵɵlistener(\"paste\", function NgOtpInputComponent_div_0_input_1_Template_input_paste_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return ctx_r5.handlePaste($event);\n    })(\"keyup\", function NgOtpInputComponent_div_0_input_1_Template_input_keyup_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const i_r3 = restoredCtx.index;\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return ctx_r7.onKeyUp($event, i_r3);\n    })(\"input\", function NgOtpInputComponent_div_0_input_1_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return ctx_r8.onInput($event);\n    })(\"keydown\", function NgOtpInputComponent_div_0_input_1_Template_input_keydown_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const i_r3 = restoredCtx.index;\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return ctx_r9.onKeyDown($event, i_r3);\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMapInterpolate1(\"otp-input \", ctx_r1.config.inputClass, \"\");\n    i0.ɵɵpropertyInterpolate2(\"id\", \"otp_\", i_r3, \"_\", ctx_r1.componentKey, \"\");\n    i0.ɵɵproperty(\"pattern\", ctx_r1.config.allowNumbersOnly ? \"\\\\d*\" : \"\")(\"type\", ctx_r1.inputType)(\"placeholder\", (ctx_r1.config == null ? null : ctx_r1.config.placeholder) || \"\")(\"ngStyle\", ctx_r1.config.inputStyles)(\"formControl\", ctx_r1.otpForm.controls[item_r2]);\n  }\n}\n\nfunction NgOtpInputComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, NgOtpInputComponent_div_0_input_1_Template, 2, 10, \"input\", 2);\n    i0.ɵɵpipe(2, \"keys\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"ng-otp-input-wrapper wrapper \", ctx_r0.config.containerClass, \"\");\n    i0.ɵɵpropertyInterpolate1(\"id\", \"c_\", ctx_r0.componentKey, \"\");\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.config.containerStyles);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(2, 6, ctx_r0.otpForm == null ? null : ctx_r0.otpForm.controls));\n  }\n}\n\nclass KeyboardUtil {\n  static ifBackspaceOrDelete(event) {\n    return this.ifKey(event, 'Backspace;Delete;Del');\n  }\n\n  static ifRightArrow(event) {\n    return this.ifKey(event, 'ArrowRight;Right');\n  }\n\n  static ifLeftArrow(event) {\n    return this.ifKey(event, 'ArrowLeft;Left');\n  }\n\n  static ifSpacebar(event) {\n    return this.ifKey(event, 'Spacebar; '); //don't remove the space after ; as this will check for space key\n  }\n\n  static ifKey(event, keys) {\n    let keysToCheck = keys.split(';');\n    return keysToCheck.some(k => k === event.key);\n  }\n\n}\n\nlet KeysPipe = /*#__PURE__*/(() => {\n  class KeysPipe {\n    transform(value) {\n      return Object.keys(value);\n    }\n\n  }\n\n  /** @nocollapse */\n  KeysPipe.ɵfac = function KeysPipe_Factory(t) {\n    return new (t || KeysPipe)();\n  };\n  /** @nocollapse */\n\n\n  KeysPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"keys\",\n    type: KeysPipe,\n    pure: true\n  });\n  return KeysPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet NgOtpInputComponent = /*#__PURE__*/(() => {\n  class NgOtpInputComponent {\n    constructor(keysPipe) {\n      this.keysPipe = keysPipe;\n      this.config = {\n        length: 4\n      }; // tslint:disable-next-line: no-output-on-prefix\n\n      this.onInputChange = new EventEmitter();\n      this.inputControls = new Array(this.config.length);\n      this.componentKey = Math.random().toString(36).substring(2) + new Date().getTime().toString(36);\n    }\n\n    get inputType() {\n      var _a, _b;\n\n      return ((_a = this.config) === null || _a === void 0 ? void 0 : _a.isPasswordInput) ? 'password' : ((_b = this.config) === null || _b === void 0 ? void 0 : _b.allowNumbersOnly) ? 'tel' : 'text';\n    }\n\n    ngOnInit() {\n      this.otpForm = new FormGroup({});\n\n      for (let index = 0; index < this.config.length; index++) {\n        this.otpForm.addControl(this.getControlName(index), new FormControl());\n      }\n\n      this.otpForm.valueChanges.subscribe(v => {\n        this.keysPipe.transform(this.otpForm.controls).forEach(k => {\n          var val = this.otpForm.controls[k].value;\n\n          if (val && val.length > 1) {\n            if (val.length >= this.config.length) {\n              this.setValue(val);\n            } else {\n              this.rebuildValue();\n            }\n          }\n        });\n      });\n    }\n\n    ngAfterViewInit() {\n      if (!this.config.disableAutoFocus) {\n        const containerItem = document.getElementById(`c_${this.componentKey}`);\n\n        if (containerItem) {\n          const ele = containerItem.getElementsByClassName('otp-input')[0];\n\n          if (ele && ele.focus) {\n            ele.focus();\n          }\n        }\n      }\n    }\n\n    getControlName(idx) {\n      return `ctrl_${idx}`;\n    }\n\n    onKeyDown($event, inputIdx) {\n      if (KeyboardUtil.ifSpacebar($event)) {\n        $event.preventDefault();\n        return false;\n      }\n    }\n\n    onInput($event) {\n      let newVal = this.currentVal ? `${this.currentVal}${$event.target.value}` : $event.target.value;\n\n      if (this.config.allowNumbersOnly && !this.validateNumber(newVal)) {\n        $event.target.value = '';\n        $event.stopPropagation();\n        $event.preventDefault();\n        return;\n      }\n    }\n\n    onKeyUp($event, inputIdx) {\n      const nextInputId = this.appendKey(`otp_${inputIdx + 1}`);\n      const prevInputId = this.appendKey(`otp_${inputIdx - 1}`);\n\n      if (KeyboardUtil.ifRightArrow($event)) {\n        $event.preventDefault();\n        this.setSelected(nextInputId);\n        return;\n      }\n\n      if (KeyboardUtil.ifLeftArrow($event)) {\n        $event.preventDefault();\n        this.setSelected(prevInputId);\n        return;\n      }\n\n      if (KeyboardUtil.ifBackspaceOrDelete($event) && !$event.target.value) {\n        this.setSelected(prevInputId);\n        this.rebuildValue();\n        return;\n      }\n\n      if (!$event.target.value) {\n        return;\n      }\n\n      if (this.ifValidKeyCode($event)) {\n        this.setSelected(nextInputId);\n      }\n\n      this.rebuildValue();\n    }\n\n    validateNumber(val) {\n      return val && /^\\d*\\.?\\d*$/.test(val);\n    }\n\n    appendKey(id) {\n      return `${id}_${this.componentKey}`;\n    }\n\n    setSelected(eleId) {\n      this.focusTo(eleId);\n      const ele = document.getElementById(eleId);\n\n      if (ele && ele.setSelectionRange) {\n        setTimeout(() => {\n          ele.setSelectionRange(0, 1);\n        }, 0);\n      }\n    }\n\n    ifValidKeyCode(event) {\n      const inp = event.key;\n      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);\n      return isMobile || /[a-zA-Z0-9-_]/.test(inp) || this.config.allowKeyCodes && this.config.allowKeyCodes.includes(event.keyCode);\n    }\n\n    focusTo(eleId) {\n      const ele = document.getElementById(eleId);\n\n      if (ele) {\n        ele.focus();\n      }\n    } // method to set component value\n\n\n    setValue(value) {\n      if (this.config.allowNumbersOnly && isNaN(value)) {\n        return;\n      }\n\n      this.otpForm.reset();\n\n      if (!value) {\n        this.rebuildValue();\n        return;\n      }\n\n      value = value.toString().replace(/\\s/g, ''); // remove whitespace\n\n      Array.from(value).forEach((c, idx) => {\n        if (this.otpForm.get(this.getControlName(idx))) {\n          this.otpForm.get(this.getControlName(idx)).setValue(c);\n        }\n      });\n\n      if (!this.config.disableAutoFocus) {\n        const containerItem = document.getElementById(`c_${this.componentKey}`);\n        var indexOfElementToFocus = value.length < this.config.length ? value.length : this.config.length - 1;\n        let ele = containerItem.getElementsByClassName('otp-input')[indexOfElementToFocus];\n\n        if (ele && ele.focus) {\n          ele.focus();\n        }\n      }\n\n      this.rebuildValue();\n    }\n\n    rebuildValue() {\n      var _a;\n\n      let val = '';\n      this.keysPipe.transform(this.otpForm.controls).forEach(k => {\n        if (this.otpForm.controls[k].value) {\n          let ctrlVal = this.otpForm.controls[k].value;\n          let isLengthExceed = ctrlVal.length > 1;\n          let isCaseTransformEnabled = !this.config.allowNumbersOnly && this.config.letterCase && (this.config.letterCase.toLocaleLowerCase() == 'upper' || this.config.letterCase.toLocaleLowerCase() == 'lower');\n          ctrlVal = ctrlVal[0];\n          let transformedVal = isCaseTransformEnabled ? this.config.letterCase.toLocaleLowerCase() == 'upper' ? ctrlVal.toUpperCase() : ctrlVal.toLowerCase() : ctrlVal;\n\n          if (isCaseTransformEnabled && transformedVal == ctrlVal) {\n            isCaseTransformEnabled = false;\n          } else {\n            ctrlVal = transformedVal;\n          }\n\n          val += ctrlVal;\n\n          if (isLengthExceed || isCaseTransformEnabled) {\n            this.otpForm.controls[k].setValue(ctrlVal);\n          }\n        }\n      });\n\n      if ((_a = this.formCtrl) === null || _a === void 0 ? void 0 : _a.setValue) {\n        this.formCtrl.setValue(val);\n      }\n\n      this.onInputChange.emit(val);\n      this.currentVal = val;\n    }\n\n    handlePaste(e) {\n      // Get pasted data via clipboard API\n      let clipboardData = e.clipboardData || window['clipboardData'];\n\n      if (clipboardData) {\n        var pastedData = clipboardData.getData('Text');\n      } // Stop data actually being pasted into div\n\n\n      e.stopPropagation();\n      e.preventDefault();\n\n      if (!pastedData || this.config.allowNumbersOnly && !this.validateNumber(pastedData)) {\n        return;\n      }\n\n      this.setValue(pastedData);\n    }\n\n  }\n\n  /** @nocollapse */\n  NgOtpInputComponent.ɵfac = function NgOtpInputComponent_Factory(t) {\n    return new (t || NgOtpInputComponent)(i0.ɵɵdirectiveInject(KeysPipe));\n  };\n  /** @nocollapse */\n\n\n  NgOtpInputComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NgOtpInputComponent,\n    selectors: [[\"ng-otp-input\"]],\n    inputs: {\n      config: \"config\",\n      formCtrl: \"formCtrl\"\n    },\n    outputs: {\n      onInputChange: \"onInputChange\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[3, \"class\", \"id\", \"ngStyle\", 4, \"ngIf\"], [3, \"id\", \"ngStyle\"], [\"autocomplete\", \"one-time-code\", 3, \"pattern\", \"type\", \"placeholder\", \"ngStyle\", \"class\", \"formControl\", \"id\", \"paste\", \"keyup\", \"input\", \"keydown\", 4, \"ngFor\", \"ngForOf\"], [\"autocomplete\", \"one-time-code\", 3, \"pattern\", \"type\", \"placeholder\", \"ngStyle\", \"formControl\", \"id\", \"paste\", \"keyup\", \"input\", \"keydown\"], [\"inp\", \"\"]],\n    template: function NgOtpInputComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NgOtpInputComponent_div_0_Template, 3, 8, \"div\", 0);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.otpForm == null ? null : ctx.otpForm.controls);\n      }\n    },\n    directives: [i2.NgIf, i2.NgStyle, i2.NgForOf, i3.DefaultValueAccessor, i3.PatternValidator, i3.NgControlStatus, i3.FormControlDirective],\n    pipes: [KeysPipe],\n    styles: [\".otp-input[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:4px;border:solid 1px #c5c5c5;text-align:center;font-size:32px}.ng-otp-input-wrapper[_ngcontent-%COMP%]   .otp-input[_ngcontent-%COMP%]:not(:last-child){margin-right:8px}@media screen and (max-width: 767px){.otp-input[_ngcontent-%COMP%]{width:40px;font-size:24px;height:40px}}@media screen and (max-width: 420px){.otp-input[_ngcontent-%COMP%]{width:30px;font-size:18px;height:30px}}\"]\n  });\n  return NgOtpInputComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet NgOtpInputModule = /*#__PURE__*/(() => {\n  class NgOtpInputModule {}\n\n  /** @nocollapse */\n  NgOtpInputModule.ɵfac = function NgOtpInputModule_Factory(t) {\n    return new (t || NgOtpInputModule)();\n  };\n  /** @nocollapse */\n\n\n  NgOtpInputModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NgOtpInputModule\n  });\n  /** @nocollapse */\n\n  NgOtpInputModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [KeysPipe],\n    imports: [[CommonModule, FormsModule, ReactiveFormsModule]]\n  });\n  return NgOtpInputModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nclass Config {}\n/*\r\n * Public API Surface of ng-otp-input\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\n\nexport { NgOtpInputComponent, Config as NgOtpInputConfig, NgOtpInputModule }; //# sourceMappingURL=ng-otp-input.js.map", "map": null, "metadata": {}, "sourceType": "module"}