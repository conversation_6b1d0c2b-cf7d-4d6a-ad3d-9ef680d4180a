{"ast": null, "code": "import { Observable } from './Observable';\nimport { Subscription, EMPTY_SUBSCRIPTION } from './Subscription';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { arrRemove } from './util/arrRemove';\nimport { errorContext } from './util/errorContext';\nexport let Subject = /*#__PURE__*/(() => {\n  class Subject extends Observable {\n    constructor() {\n      super();\n      this.closed = false;\n      this.observers = [];\n      this.isStopped = false;\n      this.hasError = false;\n      this.thrownError = null;\n    }\n\n    lift(operator) {\n      const subject = new AnonymousSubject(this, this);\n      subject.operator = operator;\n      return subject;\n    }\n\n    _throwIfClosed() {\n      if (this.closed) {\n        throw new ObjectUnsubscribedError();\n      }\n    }\n\n    next(value) {\n      errorContext(() => {\n        this._throwIfClosed();\n\n        if (!this.isStopped) {\n          const copy = this.observers.slice();\n\n          for (const observer of copy) {\n            observer.next(value);\n          }\n        }\n      });\n    }\n\n    error(err) {\n      errorContext(() => {\n        this._throwIfClosed();\n\n        if (!this.isStopped) {\n          this.hasError = this.isStopped = true;\n          this.thrownError = err;\n          const {\n            observers\n          } = this;\n\n          while (observers.length) {\n            observers.shift().error(err);\n          }\n        }\n      });\n    }\n\n    complete() {\n      errorContext(() => {\n        this._throwIfClosed();\n\n        if (!this.isStopped) {\n          this.isStopped = true;\n          const {\n            observers\n          } = this;\n\n          while (observers.length) {\n            observers.shift().complete();\n          }\n        }\n      });\n    }\n\n    unsubscribe() {\n      this.isStopped = this.closed = true;\n      this.observers = null;\n    }\n\n    get observed() {\n      var _a;\n\n      return ((_a = this.observers) === null || _a === void 0 ? void 0 : _a.length) > 0;\n    }\n\n    _trySubscribe(subscriber) {\n      this._throwIfClosed();\n\n      return super._trySubscribe(subscriber);\n    }\n\n    _subscribe(subscriber) {\n      this._throwIfClosed();\n\n      this._checkFinalizedStatuses(subscriber);\n\n      return this._innerSubscribe(subscriber);\n    }\n\n    _innerSubscribe(subscriber) {\n      const {\n        hasError,\n        isStopped,\n        observers\n      } = this;\n      return hasError || isStopped ? EMPTY_SUBSCRIPTION : (observers.push(subscriber), new Subscription(() => arrRemove(observers, subscriber)));\n    }\n\n    _checkFinalizedStatuses(subscriber) {\n      const {\n        hasError,\n        thrownError,\n        isStopped\n      } = this;\n\n      if (hasError) {\n        subscriber.error(thrownError);\n      } else if (isStopped) {\n        subscriber.complete();\n      }\n    }\n\n    asObservable() {\n      const observable = new Observable();\n      observable.source = this;\n      return observable;\n    }\n\n  }\n\n  Subject.create = (destination, source) => {\n    return new AnonymousSubject(destination, source);\n  };\n\n  return Subject;\n})();\nexport class AnonymousSubject extends Subject {\n  constructor(destination, source) {\n    super();\n    this.destination = destination;\n    this.source = source;\n  }\n\n  next(value) {\n    var _a, _b;\n\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.next) === null || _b === void 0 ? void 0 : _b.call(_a, value);\n  }\n\n  error(err) {\n    var _a, _b;\n\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.error) === null || _b === void 0 ? void 0 : _b.call(_a, err);\n  }\n\n  complete() {\n    var _a, _b;\n\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.complete) === null || _b === void 0 ? void 0 : _b.call(_a);\n  }\n\n  _subscribe(subscriber) {\n    var _a, _b;\n\n    return (_b = (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber)) !== null && _b !== void 0 ? _b : EMPTY_SUBSCRIPTION;\n  }\n\n} //# sourceMappingURL=Subject.js.map", "map": null, "metadata": {}, "sourceType": "module"}