{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { map } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let CommonService = /*#__PURE__*/(() => {\n  class CommonService {\n    constructor(http) {\n      this.http = http;\n      this.notificationTracker = new BehaviorSubject(0);\n    } // @Output() aClickedEvent = new EventEmitter<string>();\n    // AClicked(msg: string) {\n    //   this.aClickedEvent.emit(msg);\n    // }\n\n\n    get(url, obj, apiUrl) {\n      var _a; // debugger\n\n\n      let params = new HttpParams();\n\n      if (obj) {\n        for (const key in obj) {\n          if (obj.hasOwnProperty(key)) {\n            params = params.append(key, (_a = obj[key]) !== null && _a !== void 0 ? _a : '');\n          }\n        }\n      }\n\n      return this.http.get((apiUrl !== null && apiUrl !== void 0 ? apiUrl : environment.apiUrl) + url, {\n        params,\n        withCredentials: true\n      }).pipe(map(res => {\n        return res;\n      }));\n    }\n\n    post(url, params) {\n      return this.http.post(environment.apiUrl + url, params, {\n        withCredentials: true\n      }).pipe(map(res => {\n        return res;\n      }));\n    }\n\n    postGhoori(url, params, apiUrl) {\n      console.log('apiUrl', apiUrl);\n      console.log('url', url);\n      console.log('params', params);\n      return this.http.post((apiUrl !== null && apiUrl !== void 0 ? apiUrl : environment.apiUrl) + url, params).pipe(map(res => {\n        return res;\n      }));\n    }\n\n    postMultipart(url, params) {\n      return this.http.post(environment.apiUrl + url, params, {\n        headers: {\n          'Content-Type': undefined\n        },\n        withCredentials: true\n      }).pipe(map(res => {\n        return res;\n      }));\n    }\n\n    generateUrl(url = null) {\n      return environment.apiUrl + url;\n    }\n\n    downloadFile(url, obj) {\n      let params = new HttpParams();\n\n      if (obj) {\n        for (const key in obj) {\n          if (obj.hasOwnProperty(key)) {\n            params = params.append(key, obj[key]);\n          }\n        }\n      }\n\n      return this.http.get(environment.apiUrl + url, {\n        responseType: 'blob',\n        params,\n        withCredentials: true\n      }).pipe(map(result => {\n        return result;\n      }));\n    }\n\n    getPDFFile(url) {\n      return this.http.get(url, {\n        responseType: 'blob',\n        withCredentials: true\n      }).pipe(map(result => {\n        return result;\n      }));\n    }\n\n  }\n\n  CommonService.ɵfac = function CommonService_Factory(t) {\n    return new (t || CommonService)(i0.ɵɵinject(i1.HttpClient));\n  };\n\n  CommonService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CommonService,\n    factory: CommonService.ɵfac,\n    providedIn: 'root'\n  });\n  return CommonService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}