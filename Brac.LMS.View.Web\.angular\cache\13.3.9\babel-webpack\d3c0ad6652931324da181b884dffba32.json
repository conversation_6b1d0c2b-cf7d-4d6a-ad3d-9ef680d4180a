{"ast": null, "code": "import { MatDialogState } from '@angular/material/dialog';\nimport { ConfirmComponent } from './confirm.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nexport let ConfirmService = /*#__PURE__*/(() => {\n  class ConfirmService {\n    constructor(dialog) {\n      this.dialog = dialog;\n    }\n\n    confirm(title, message, confirmButtonText, cancelButtonText, disableClose) {\n      this.dialogRef = this.dialog.open(ConfirmComponent, {\n        disableClose: true\n      });\n      this.dialogRef.componentInstance.title = title;\n      this.dialogRef.componentInstance.message = message;\n      if (confirmButtonText) this.dialogRef.componentInstance.confirmButtonText = confirmButtonText;\n      if (cancelButtonText) this.dialogRef.componentInstance.cancelButtonText = cancelButtonText;\n      if (disableClose) this.dialogRef.componentInstance.disableCloseButton = disableClose;\n      return this.dialogRef.afterClosed();\n    }\n\n    confirmTabChange(title, message, confirmButtonText, cancelButtonText, disableClose) {\n      this.dialogRef = this.dialog.open(ConfirmComponent, {\n        disableClose: true\n      });\n      this.dialogRef.componentInstance.title = title;\n      this.dialogRef.componentInstance.message = message;\n      if (confirmButtonText) this.dialogRef.componentInstance.confirmButtonText = confirmButtonText;\n      if (cancelButtonText) this.dialogRef.componentInstance.cancelButtonText = cancelButtonText;\n      if (disableClose) this.dialogRef.componentInstance.disableCloseButton = disableClose;\n      return this.dialogRef.afterClosed();\n    }\n\n    close() {\n      if (this.dialogRef && this.dialogRef.getState() == 0\n      /* OPEN */\n      ) this.dialogRef.close();\n    }\n\n  }\n\n  ConfirmService.ɵfac = function ConfirmService_Factory(t) {\n    return new (t || ConfirmService)(i0.ɵɵinject(i1.MatDialog));\n  };\n\n  ConfirmService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ConfirmService,\n    factory: ConfirmService.ɵfac\n  });\n  return ConfirmService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}