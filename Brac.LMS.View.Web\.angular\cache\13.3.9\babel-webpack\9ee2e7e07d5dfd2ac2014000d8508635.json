{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Latvian [lv]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/skakri\n//! author : <PERSON><PERSON><PERSON> : https://github.com/JanisE\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var units = {\n    ss: 'sekundes_sekundēm_sekunde_sekundes'.split('_'),\n    m: 'minūtes_minūtēm_minūte_minūtes'.split('_'),\n    mm: 'minūtes_minūtēm_minūte_minūtes'.split('_'),\n    h: 'stundas_stundām_stunda_stundas'.split('_'),\n    hh: 'stundas_stundām_stunda_stundas'.split('_'),\n    d: 'dienas_dienām_diena_dienas'.split('_'),\n    dd: 'dienas_dienām_diena_dienas'.split('_'),\n    M: 'mēneša_mēnešiem_mēnesis_mēneši'.split('_'),\n    MM: 'mēneša_mēnešiem_mēnesis_mēneši'.split('_'),\n    y: 'gada_gadiem_gads_gadi'.split('_'),\n    yy: 'gada_gadiem_gads_gadi'.split('_')\n  };\n  /**\n   * @param withoutSuffix boolean true = a length of time; false = before/after a period of time.\n   */\n\n  function format(forms, number, withoutSuffix) {\n    if (withoutSuffix) {\n      // E.g. \"21 minūte\", \"3 minūtes\".\n      return number % 10 === 1 && number % 100 !== 11 ? forms[2] : forms[3];\n    } else {\n      // E.g. \"21 minūtes\" as in \"pēc 21 minūtes\".\n      // E.g. \"3 minūtēm\" as in \"pēc 3 minūtēm\".\n      return number % 10 === 1 && number % 100 !== 11 ? forms[0] : forms[1];\n    }\n  }\n\n  function relativeTimeWithPlural(number, withoutSuffix, key) {\n    return number + ' ' + format(units[key], number, withoutSuffix);\n  }\n\n  function relativeTimeWithSingular(number, withoutSuffix, key) {\n    return format(units[key], number, withoutSuffix);\n  }\n\n  function relativeSeconds(number, withoutSuffix) {\n    return withoutSuffix ? 'dažas sekundes' : 'dažām sekundēm';\n  }\n\n  var lv = moment.defineLocale('lv', {\n    months: 'janvāris_februāris_marts_aprīlis_maijs_jūnijs_jūlijs_augusts_septembris_oktobris_novembris_decembris'.split('_'),\n    monthsShort: 'jan_feb_mar_apr_mai_jūn_jūl_aug_sep_okt_nov_dec'.split('_'),\n    weekdays: 'svētdiena_pirmdiena_otrdiena_trešdiena_ceturtdiena_piektdiena_sestdiena'.split('_'),\n    weekdaysShort: 'Sv_P_O_T_C_Pk_S'.split('_'),\n    weekdaysMin: 'Sv_P_O_T_C_Pk_S'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY.',\n      LL: 'YYYY. [gada] D. MMMM',\n      LLL: 'YYYY. [gada] D. MMMM, HH:mm',\n      LLLL: 'YYYY. [gada] D. MMMM, dddd, HH:mm'\n    },\n    calendar: {\n      sameDay: '[Šodien pulksten] LT',\n      nextDay: '[Rīt pulksten] LT',\n      nextWeek: 'dddd [pulksten] LT',\n      lastDay: '[Vakar pulksten] LT',\n      lastWeek: '[Pagājušā] dddd [pulksten] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'pēc %s',\n      past: 'pirms %s',\n      s: relativeSeconds,\n      ss: relativeTimeWithPlural,\n      m: relativeTimeWithSingular,\n      mm: relativeTimeWithPlural,\n      h: relativeTimeWithSingular,\n      hh: relativeTimeWithPlural,\n      d: relativeTimeWithSingular,\n      dd: relativeTimeWithPlural,\n      M: relativeTimeWithSingular,\n      MM: relativeTimeWithPlural,\n      y: relativeTimeWithSingular,\n      yy: relativeTimeWithPlural\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return lv;\n});", "map": null, "metadata": {}, "sourceType": "script"}