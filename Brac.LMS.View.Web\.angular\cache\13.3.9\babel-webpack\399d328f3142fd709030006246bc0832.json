{"ast": null, "code": "import { coerceElement, coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { debounceTime } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\n\nlet MutationObserverFactory = /*#__PURE__*/(() => {\n  class MutationObserverFactory {\n    create(callback) {\n      return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n    }\n\n  }\n\n  MutationObserverFactory.ɵfac = function MutationObserverFactory_Factory(t) {\n    return new (t || MutationObserverFactory)();\n  };\n\n  MutationObserverFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MutationObserverFactory,\n    factory: MutationObserverFactory.ɵfac,\n    providedIn: 'root'\n  });\n  return MutationObserverFactory;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** An injectable service that allows watching elements for changes to their content. */\n\n\nlet ContentObserver = /*#__PURE__*/(() => {\n  class ContentObserver {\n    constructor(_mutationObserverFactory) {\n      this._mutationObserverFactory = _mutationObserverFactory;\n      /** Keeps track of the existing MutationObservers so they can be reused. */\n\n      this._observedElements = new Map();\n    }\n\n    ngOnDestroy() {\n      this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n    }\n\n    observe(elementOrRef) {\n      const element = coerceElement(elementOrRef);\n      return new Observable(observer => {\n        const stream = this._observeElement(element);\n\n        const subscription = stream.subscribe(observer);\n        return () => {\n          subscription.unsubscribe();\n\n          this._unobserveElement(element);\n        };\n      });\n    }\n    /**\n     * Observes the given element by using the existing MutationObserver if available, or creating a\n     * new one if not.\n     */\n\n\n    _observeElement(element) {\n      if (!this._observedElements.has(element)) {\n        const stream = new Subject();\n\n        const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n\n        if (observer) {\n          observer.observe(element, {\n            characterData: true,\n            childList: true,\n            subtree: true\n          });\n        }\n\n        this._observedElements.set(element, {\n          observer,\n          stream,\n          count: 1\n        });\n      } else {\n        this._observedElements.get(element).count++;\n      }\n\n      return this._observedElements.get(element).stream;\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n     * observing this element.\n     */\n\n\n    _unobserveElement(element) {\n      if (this._observedElements.has(element)) {\n        this._observedElements.get(element).count--;\n\n        if (!this._observedElements.get(element).count) {\n          this._cleanupObserver(element);\n        }\n      }\n    }\n    /** Clean up the underlying MutationObserver for the specified element. */\n\n\n    _cleanupObserver(element) {\n      if (this._observedElements.has(element)) {\n        const {\n          observer,\n          stream\n        } = this._observedElements.get(element);\n\n        if (observer) {\n          observer.disconnect();\n        }\n\n        stream.complete();\n\n        this._observedElements.delete(element);\n      }\n    }\n\n  }\n\n  ContentObserver.ɵfac = function ContentObserver_Factory(t) {\n    return new (t || ContentObserver)(i0.ɵɵinject(MutationObserverFactory));\n  };\n\n  ContentObserver.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ContentObserver,\n    factory: ContentObserver.ɵfac,\n    providedIn: 'root'\n  });\n  return ContentObserver;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\n\n\nlet CdkObserveContent = /*#__PURE__*/(() => {\n  class CdkObserveContent {\n    constructor(_contentObserver, _elementRef, _ngZone) {\n      this._contentObserver = _contentObserver;\n      this._elementRef = _elementRef;\n      this._ngZone = _ngZone;\n      /** Event emitted for each change in the element's content. */\n\n      this.event = new EventEmitter();\n      this._disabled = false;\n      this._currentSubscription = null;\n    }\n    /**\n     * Whether observing content is disabled. This option can be used\n     * to disconnect the underlying MutationObserver until it is needed.\n     */\n\n\n    get disabled() {\n      return this._disabled;\n    }\n\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n      this._disabled ? this._unsubscribe() : this._subscribe();\n    }\n    /** Debounce interval for emitting the changes. */\n\n\n    get debounce() {\n      return this._debounce;\n    }\n\n    set debounce(value) {\n      this._debounce = coerceNumberProperty(value);\n\n      this._subscribe();\n    }\n\n    ngAfterContentInit() {\n      if (!this._currentSubscription && !this.disabled) {\n        this._subscribe();\n      }\n    }\n\n    ngOnDestroy() {\n      this._unsubscribe();\n    }\n\n    _subscribe() {\n      this._unsubscribe();\n\n      const stream = this._contentObserver.observe(this._elementRef); // TODO(mmalerba): We shouldn't be emitting on this @Output() outside the zone.\n      // Consider brining it back inside the zone next time we're making breaking changes.\n      // Bringing it back inside can cause things like infinite change detection loops and changed\n      // after checked errors if people's code isn't handling it properly.\n\n\n      this._ngZone.runOutsideAngular(() => {\n        this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n      });\n    }\n\n    _unsubscribe() {\n      var _a;\n\n      (_a = this._currentSubscription) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n    }\n\n  }\n\n  CdkObserveContent.ɵfac = function CdkObserveContent_Factory(t) {\n    return new (t || CdkObserveContent)(i0.ɵɵdirectiveInject(ContentObserver), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n\n  CdkObserveContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkObserveContent,\n    selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n    inputs: {\n      disabled: [\"cdkObserveContentDisabled\", \"disabled\"],\n      debounce: \"debounce\"\n    },\n    outputs: {\n      event: \"cdkObserveContent\"\n    },\n    exportAs: [\"cdkObserveContent\"]\n  });\n  return CdkObserveContent;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet ObserversModule = /*#__PURE__*/(() => {\n  class ObserversModule {}\n\n  ObserversModule.ɵfac = function ObserversModule_Factory(t) {\n    return new (t || ObserversModule)();\n  };\n\n  ObserversModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ObserversModule\n  });\n  ObserversModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MutationObserverFactory]\n  });\n  return ObserversModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule }; //# sourceMappingURL=observers.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}