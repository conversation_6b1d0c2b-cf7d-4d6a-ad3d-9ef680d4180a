{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { environment } from 'src/environments/environment';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { BlockUI } from 'ng-block-ui';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/_helpers/confirm-dialog/confirm.service\";\nimport * as i2 from \"ngx-toastr\";\nimport * as i3 from \"src/app/_services/authentication.service\";\nimport * as i4 from \"src/app/_services/common.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/expansion\";\nimport * as i8 from \"@angular/flex-layout/extended\";\n\nfunction SideMenuComponent_div_1_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 26);\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r9.currentUser.FullName);\n  }\n}\n\nfunction SideMenuComponent_div_1_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 27);\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r10.currentUser.FullName);\n  }\n}\n\nfunction SideMenuComponent_div_1_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 28);\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r11.currentUser.FullName);\n  }\n}\n\nfunction SideMenuComponent_div_1_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 29);\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate2(\"src\", \"\", ctx_r12.baseUrl, \"\", ctx_r12.currentUser.ImagePath, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r12.currentUser.FullName);\n  }\n}\n\nconst _c0 = function () {\n  return [\"/dashboard\"];\n};\n\nfunction SideMenuComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵelement(1, \"img\", 19);\n    i0.ɵɵtemplate(2, SideMenuComponent_div_1_img_2_Template, 1, 1, \"img\", 20);\n    i0.ɵɵtemplate(3, SideMenuComponent_div_1_img_3_Template, 1, 1, \"img\", 21);\n    i0.ɵɵtemplate(4, SideMenuComponent_div_1_img_4_Template, 1, 1, \"img\", 22);\n    i0.ɵɵtemplate(5, SideMenuComponent_div_1_img_5_Template, 1, 3, \"img\", 23);\n    i0.ɵɵelementStart(6, \"h6\", 24);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 25);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(7, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.currentUser.ImagePath && ctx_r0.currentUser.Gender == \"Others\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.currentUser.ImagePath && ctx_r0.currentUser.Gender == \"Male\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.currentUser.ImagePath && ctx_r0.currentUser.Gender == \"Female\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser.ImagePath);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.FullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.Position);\n  }\n}\n\nfunction SideMenuComponent_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.unseenNotification);\n  }\n}\n\nconst _c1 = function () {\n  return [\"/learning-hours\"];\n};\n\nfunction SideMenuComponent_a_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 31);\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵtext(2, \"Learning Hours \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\n\nconst _c2 = function () {\n  return [\"/all-courses\"];\n};\n\nfunction SideMenuComponent_mat_accordion_18_mat_tab_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-tab\", 40);\n    i0.ɵɵtext(1, \" Available Courses \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\n\nconst _c3 = function () {\n  return [\"/my-courses\"];\n};\n\nfunction SideMenuComponent_mat_accordion_18_mat_tab_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-tab\", 41);\n    i0.ɵɵtext(1, \" My Courses \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\n\nconst _c4 = function () {\n  return [\"/bookmarks\"];\n};\n\nfunction SideMenuComponent_mat_accordion_18_mat_tab_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-tab\", 42);\n    i0.ɵɵtext(1, \" Bookmarks \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c4));\n  }\n}\n\nconst _c5 = function () {\n  return {\n    \"font-size\": \"1rem\",\n    \"color\": \"#37384e !important\"\n  };\n};\n\nfunction SideMenuComponent_mat_accordion_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-accordion\");\n    i0.ɵɵelementStart(1, \"mat-expansion-panel\", 33);\n    i0.ɵɵelementStart(2, \"mat-expansion-panel-header\");\n    i0.ɵɵelementStart(3, \"mat-panel-title\", 34, 35);\n    i0.ɵɵelement(5, \"i\", 36);\n    i0.ɵɵtext(6, \" Courses \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-tab-group\");\n    i0.ɵɵtemplate(8, SideMenuComponent_mat_accordion_18_mat_tab_8_Template, 2, 2, \"mat-tab\", 37);\n    i0.ɵɵtemplate(9, SideMenuComponent_mat_accordion_18_mat_tab_9_Template, 2, 2, \"mat-tab\", 38);\n    i0.ɵɵtemplate(10, SideMenuComponent_mat_accordion_18_mat_tab_10_Template, 2, 2, \"mat-tab\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"expanded\", ctx_r3.courseIsExpanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(5, _c5));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isTrainee);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isTrainee);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isTrainee);\n  }\n}\n\nconst _c6 = function () {\n  return [\"/ghoori-learning\"];\n};\n\nfunction SideMenuComponent_a_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 31);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵtext(2, \"Ghoori Learning \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c6));\n  }\n}\n\nconst _c7 = function () {\n  return [\"/external-courses\"];\n};\n\nfunction SideMenuComponent_a_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 31);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵtext(2, \"External Courses \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c7));\n  }\n}\n\nconst _c8 = function () {\n  return [\"/evaluation-test-result\"];\n};\n\nfunction SideMenuComponent_a_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 31);\n    i0.ɵɵelement(1, \"i\", 44);\n    i0.ɵɵtext(2, \"Evaluation Test Result \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c8));\n  }\n}\n\nconst _c9 = function () {\n  return [\"/e-library\"];\n};\n\nfunction SideMenuComponent_a_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 31);\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2, \"E-Library \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c9));\n  }\n}\n\nconst _c10 = function () {\n  return [\"/forum\"];\n};\n\nfunction SideMenuComponent_a_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 31);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"Forum \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c10));\n  }\n}\n\nconst _c11 = function () {\n  return [\"/profile\"];\n};\n\nconst _c12 = function () {\n  return [\"/notifications\"];\n};\n\nconst _c13 = function () {\n  return [\"/my-progress\"];\n};\n\nconst _c14 = function () {\n  return [\"/certifications\"];\n};\n\nconst _c15 = function () {\n  return [\"/reports\"];\n};\n\nexport class SideMenuComponent {\n  constructor(confirmService, toastr, authService, service, router) {\n    this.confirmService = confirmService;\n    this.toastr = toastr;\n    this.authService = authService;\n    this.service = service;\n    this.router = router;\n    this.unseenNotification = 0;\n    this.subscriptios = [];\n    this.isTrainee = false;\n    this.panelOpenState = true;\n    this.clickedToExpand = false;\n    this.accordionExpandedIndex = 0;\n    this.tabSelectedIndex = 0;\n    this.courseIsExpanded = false;\n    this.baseUrl = environment.baseUrl;\n    this.ghooriUrl = environment.ghooriUrl;\n  }\n\n  ngOnInit() {\n    const currentUrl = this.router.url;\n    this.courseIsExpanded = currentUrl == '/all-courses' || currentUrl == '/my-courses' || currentUrl == '/bookmarks' ? true : false;\n    this.authService.getCurrentUser().subscribe(user => {\n      this.currentUser = user;\n      console.log('currentUser', this.currentUser);\n      this.isTrainee = user.Roles.indexOf('Trainee') !== -1;\n    });\n    this.subscriptios.push(this.service.notificationTracker.subscribe(unseenNotification => {\n      this.unseenNotification = unseenNotification;\n    }));\n    this.getUnseenCount();\n  }\n\n  ngOnDestroy() {\n    this.subscriptios.forEach(subscription => {\n      subscription.unsubscribe();\n    });\n  }\n\n  click(number) {\n    if (number == 1 || number == 2 || number == 3) {\n      this.clickedToExpand = true;\n    } else {\n      this.clickedToExpand = false;\n    }\n  }\n\n  logout() {\n    this.confirmService.confirm('Are you sure?', 'You are logging out.', \"Yes, Log Me Out\").subscribe({\n      next: data => {\n        if (data) {\n          this.authService.logout(window.location.hostname).subscribe(() => window.location.reload());\n        }\n      },\n      error: error => {\n        if (error.status === 400) {\n          this.toastr.error('Unauthorized request found', 'Success!', {\n            timeOut: 3000\n          });\n        } else if (error.status === 401) {\n          this.toastr.error('Invalid Email Or Password', 'Success!', {\n            timeOut: 3000\n          });\n        }\n      },\n      complete: () => {}\n    });\n  }\n\n  onTabSelected(event) {\n    this.accordionExpandedIndex = event.index + 1;\n  }\n\n  getUnseenCount() {\n    this.service.get('notification/trainee/unseen-count').subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.unseenNotification = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => {}\n    });\n  }\n\n  loginToGhoori() {\n    let obj = {\n      username: this.currentUser.FullName,\n      pin: this.currentUser.UserName,\n      access_key: \"lDRBqT7NR77yAFScp4m1GOJ4bMmXXkt5\"\n    };\n    this.blockUI.start('Processing authentication. Please wait...');\n    this.service.postGhoori('api/brac/user-authentication', obj, this.ghooriUrl).subscribe({\n      next: res => {\n        if (res.message !== \"Token has been created successfully.\") {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          this.blockUI.stop();\n          return;\n        }\n\n        ;\n        const redirectUrl = `${res.redirect_url}`; // Navigate to the new page\n        // window.open(redirectUrl, '_blank');\n\n        this.router.navigate(['/ghoori-learning']);\n        this.blockUI.stop();\n      } // this.learningHoursContentList = res.Data.Records;\n\n    });\n  }\n\n}\n\nSideMenuComponent.ɵfac = function SideMenuComponent_Factory(t) {\n  return new (t || SideMenuComponent)(i0.ɵɵdirectiveInject(i1.ConfirmService), i0.ɵɵdirectiveInject(i2.ToastrService), i0.ɵɵdirectiveInject(i3.AuthenticationService), i0.ɵɵdirectiveInject(i4.CommonService), i0.ɵɵdirectiveInject(i5.Router));\n};\n\nSideMenuComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: SideMenuComponent,\n  selectors: [[\"app-web-side-menu\"]],\n  decls: 36,\n  vars: 21,\n  consts: [[1, \"rounded-1\", \"text-dark\"], [\"class\", \"p-2 mb-1 text-center text-dark vh-30 \", 4, \"ngIf\"], [1, \"d-lg-none\", \"px-2\", \"pb-4\", \"text-center\"], [\"href\", \"#scrollable-menu\", \"data-bs-toggle\", \"collapse\", 1, \"btn\", \"btn-primary\", \"px-5\", \"mb-2\"], [1, \"ai-menu\", \"me-2\"], [1, \"navcontent\"], [\"routerLinkActive\", \"active\", 1, \"d-flex\", \"align-items-center\", \"text-dark\", \"nav-link-style\", \"px-4\", \"py-3\", 3, \"routerLink\"], [1, \"ai-home\", \"fs-lg\", \"me-2\", \"fw-bolder\", \"text-dark\"], [1, \"ai-user\", \"fs-lg\", \"me-2\", \"fw-bolder\", \"text-dark\"], [1, \"ai-bell\", \"fs-lg\", \"me-2\", \"fw-bolder\", \"text-dark\"], [\"class\", \"badge bg-pill bg-danger\", 4, \"ngIf\"], [\"class\", \"d-flex text-dark align-items-center nav-link-style px-4 py-3\", \"routerLinkActive\", \"active\", 3, \"routerLink\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"ai-bar-chart-2\", \"fs-lg\", \"me-2\", \"fw-bolder\", \"text-dark\"], [1, \"ai-award\", \"fs-lg\", \"me-2\", \"fw-bolder\", \"text-dark\"], [1, \"fa-solid\", \"fa-box-open\", \"fs-lg\", \"me-2\", \"fw-bolder\", \"text-dark\"], [1, \"d-flex\", \"align-items-center\", \"text-danger\", \"fw-bolder\", \"nav-link-style\", \"px-4\", \"py-3\", \"border-top\", \"sm-cursor-pointer\", 3, \"click\"], [1, \"ai-log-out\", \"fs-lg\", \"me-2\"], [1, \"p-2\", \"mb-1\", \"text-center\", \"text-dark\", \"vh-30\"], [\"width\", \"180\", \"src\", \"assets/img/logo/ALO-Logo_512x300.svg\", 1, \"d-block\", \"mx-auto\", 3, \"routerLink\"], [\"class\", \"d-block rounded-circle mx-auto my-2\", \"src\", \"assets/img/user/other.jpg\", \"width\", \"80\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"d-block rounded-circle mx-auto my-2\", \"src\", \"assets/img/user/male.jpg\", \"width\", \"80\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"d-block rounded-circle mx-auto my-2\", \"src\", \"assets/img/user/female.jpg\", \"width\", \"80\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"d-block rounded-circle mx-auto my-2\", \"width\", \"80\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"mb-0\", \"pt-1\", \"text-dark\"], [1, \"fs-sm\"], [\"src\", \"assets/img/user/other.jpg\", \"width\", \"80\", 1, \"d-block\", \"rounded-circle\", \"mx-auto\", \"my-2\", 3, \"alt\"], [\"src\", \"assets/img/user/male.jpg\", \"width\", \"80\", 1, \"d-block\", \"rounded-circle\", \"mx-auto\", \"my-2\", 3, \"alt\"], [\"src\", \"assets/img/user/female.jpg\", \"width\", \"80\", 1, \"d-block\", \"rounded-circle\", \"mx-auto\", \"my-2\", 3, \"alt\"], [\"width\", \"80\", 1, \"d-block\", \"rounded-circle\", \"mx-auto\", \"my-2\", 3, \"src\", \"alt\"], [1, \"badge\", \"bg-pill\", \"bg-danger\"], [\"routerLinkActive\", \"active\", 1, \"d-flex\", \"text-dark\", \"align-items-center\", \"nav-link-style\", \"px-4\", \"py-3\", 3, \"routerLink\"], [1, \"fab\", \"fa-audible\", \"fs-lg\", \"me-2\", \"fw-bolder\", \"text-dark\"], [3, \"expanded\"], [3, \"ngStyle\"], [\"panelTitle\", \"\"], [1, \"fa\", \"fa-book-open\", \"fs-lg\", \"me-2\", \"fw-bolder\", \"text-dark\"], [\"class\", \"d-flex text-dark align-items-center nav-link-style px-4 py-3 cursor-pointer\", \"routerLinkActive\", \"active\", \"label\", \"Tab 1\", 3, \"routerLink\", 4, \"ngIf\"], [\"class\", \"d-flex text-dark align-items-center nav-link-style px-4 py-3 cursor-pointer\", \"routerLinkActive\", \"active\", \"label\", \"Tab 2\", 3, \"routerLink\", 4, \"ngIf\"], [\"class\", \"d-flex text-dark align-items-center nav-link-style px-4 py-3 cursor-pointer\", \"routerLinkActive\", \"active\", \"label\", \"Tab 3\", 3, \"routerLink\", 4, \"ngIf\"], [\"routerLinkActive\", \"active\", \"label\", \"Tab 1\", 1, \"d-flex\", \"text-dark\", \"align-items-center\", \"nav-link-style\", \"px-4\", \"py-3\", \"cursor-pointer\", 3, \"routerLink\"], [\"routerLinkActive\", \"active\", \"label\", \"Tab 2\", 1, \"d-flex\", \"text-dark\", \"align-items-center\", \"nav-link-style\", \"px-4\", \"py-3\", \"cursor-pointer\", 3, \"routerLink\"], [\"routerLinkActive\", \"active\", \"label\", \"Tab 3\", 1, \"d-flex\", \"text-dark\", \"align-items-center\", \"nav-link-style\", \"px-4\", \"py-3\", \"cursor-pointer\", 3, \"routerLink\"], [1, \"fa\", \"fa-book-atlas\", \"fs-lg\", \"me-2\", \"fw-bolder\", \"text-dark\"], [1, \"ai-clock\", \"fs-lg\", \"me-2\", \"fw-bolder\", \"text-dark\"], [1, \"ai-book-open\", \"fs-lg\", \"me-2\", \"fw-bolder\", \"text-dark\"], [1, \"ai-command\", \"fs-lg\", \"me-2\", \"fw-bolder\", \"text-dark\"]],\n  template: function SideMenuComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵtemplate(1, SideMenuComponent_div_1_Template, 10, 8, \"div\", 1);\n      i0.ɵɵelementStart(2, \"div\", 2);\n      i0.ɵɵelementStart(3, \"a\", 3);\n      i0.ɵɵelement(4, \"i\", 4);\n      i0.ɵɵtext(5, \"Menu \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"div\", 5);\n      i0.ɵɵelementStart(7, \"a\", 6);\n      i0.ɵɵelement(8, \"i\", 7);\n      i0.ɵɵtext(9, \"Dashboard \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(10, \"a\", 6);\n      i0.ɵɵelement(11, \"i\", 8);\n      i0.ɵɵtext(12, \"Profile\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"a\", 6);\n      i0.ɵɵelementStart(14, \"i\", 9);\n      i0.ɵɵtemplate(15, SideMenuComponent_span_15_Template, 2, 1, \"span\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(16, \"Notifications \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(17, SideMenuComponent_a_17_Template, 3, 2, \"a\", 11);\n      i0.ɵɵtemplate(18, SideMenuComponent_mat_accordion_18_Template, 11, 6, \"mat-accordion\", 12);\n      i0.ɵɵelementStart(19, \"a\", 6);\n      i0.ɵɵelement(20, \"i\", 13);\n      i0.ɵɵtext(21, \"My Progress \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(22, SideMenuComponent_a_22_Template, 3, 2, \"a\", 11);\n      i0.ɵɵtemplate(23, SideMenuComponent_a_23_Template, 3, 2, \"a\", 11);\n      i0.ɵɵtemplate(24, SideMenuComponent_a_24_Template, 3, 2, \"a\", 11);\n      i0.ɵɵelementStart(25, \"a\", 6);\n      i0.ɵɵelement(26, \"i\", 14);\n      i0.ɵɵtext(27, \"Certifications \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(28, SideMenuComponent_a_28_Template, 3, 2, \"a\", 11);\n      i0.ɵɵtemplate(29, SideMenuComponent_a_29_Template, 3, 2, \"a\", 11);\n      i0.ɵɵelementStart(30, \"a\", 6);\n      i0.ɵɵelement(31, \"i\", 15);\n      i0.ɵɵtext(32, \" Reports \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(33, \"a\", 16);\n      i0.ɵɵlistener(\"click\", function SideMenuComponent_Template_a_click_33_listener() {\n        return ctx.logout();\n      });\n      i0.ɵɵelement(34, \"i\", 17);\n      i0.ɵɵtext(35, \"Log out\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(15, _c0));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(16, _c11));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(17, _c12));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.unseenNotification > 0);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.isTrainee);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isTrainee);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(18, _c13));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.isTrainee);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isTrainee);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isTrainee);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(19, _c14));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.isTrainee);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isTrainee);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(20, _c15));\n    }\n  },\n  directives: [i6.NgIf, i5.RouterLink, i5.RouterLinkWithHref, i5.RouterLinkActive, i7.MatAccordion, i7.MatExpansionPanel, i7.MatExpansionPanelHeader, i7.MatExpansionPanelTitle, i6.NgStyle, i8.DefaultStyleDirective],\n  styles: [\".mat-expansion-panel[_ngcontent-%COMP%]{background-image:linear-gradient(#f5c253,#F4BF2D)}.mat-expansion-panel-header-title[_ngcontent-%COMP%]{color:#37384e!important}.nav-link-style[_ngcontent-%COMP%]{border-radius:1rem}.nav-link-style[_ngcontent-%COMP%]:hover{background-color:#fff;border-radius:1rem}.mat-expansion-panel-header.active[_ngcontent-%COMP%]{background-color:#f0f0f0;border-radius:1rem}.navcontent[_ngcontent-%COMP%]{width:100%;height:62vh;overflow:auto}.navcontent[_ngcontent-%COMP%]::-webkit-scrollbar{width:12px}.sm-cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}\"]\n});\n\n__decorate([BlockUI()], SideMenuComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}