{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Component, Input, EventEmitter, Inject, Output, HostBinding, NgModule } from '@angular/core';\nimport { isBs3 } from 'ngx-bootstrap/utils';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i2 from 'ngx-bootstrap/collapse';\nimport { CollapseModule } from 'ngx-bootstrap/collapse';\n/**\n * Configuration service, provides default values for the AccordionComponent.\n */\n\nconst _c0 = [\"*\"];\n\nconst _c1 = function (a0) {\n  return {\n    \"text-muted\": a0\n  };\n};\n\nfunction AccordionPanelComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c1, ctx_r0.isDisabled));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.heading, \" \");\n  }\n}\n\nconst _c2 = [[[\"\", \"accordion-heading\", \"\"]], \"*\"];\nconst _c3 = [\"[accordion-heading]\", \"*\"];\nlet AccordionConfig = /*#__PURE__*/(() => {\n  class AccordionConfig {\n    constructor() {\n      /** Whether the other panels should be closed when a panel is opened */\n      this.closeOthers = false;\n      /** turn on/off animation */\n\n      this.isAnimated = false;\n    }\n\n  }\n\n  AccordionConfig.ɵfac = function AccordionConfig_Factory(t) {\n    return new (t || AccordionConfig)();\n  };\n\n  AccordionConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AccordionConfig,\n    factory: AccordionConfig.ɵfac,\n    providedIn: 'root'\n  });\n  return AccordionConfig;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Displays collapsible content panels for presenting information in a limited amount of space. */\n\n\nlet AccordionComponent = /*#__PURE__*/(() => {\n  class AccordionComponent {\n    constructor(config) {\n      /** turn on/off animation */\n      this.isAnimated = false;\n      /** if `true` expanding one item will close all others */\n\n      this.closeOthers = false;\n      this.groups = [];\n      Object.assign(this, config);\n    }\n\n    closeOtherPanels(openGroup) {\n      if (!this.closeOthers) {\n        return;\n      }\n\n      this.groups.forEach(group => {\n        if (group !== openGroup) {\n          group.isOpen = false;\n        }\n      });\n    }\n\n    addGroup(group) {\n      group.isAnimated = this.isAnimated;\n      this.groups.push(group);\n    }\n\n    removeGroup(group) {\n      const index = this.groups.indexOf(group);\n\n      if (index !== -1) {\n        this.groups.splice(index, 1);\n      }\n    }\n\n  }\n\n  AccordionComponent.ɵfac = function AccordionComponent_Factory(t) {\n    return new (t || AccordionComponent)(i0.ɵɵdirectiveInject(AccordionConfig));\n  };\n\n  AccordionComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: AccordionComponent,\n    selectors: [[\"accordion\"]],\n    hostAttrs: [\"role\", \"tablist\", 1, \"panel-group\", 2, \"display\", \"block\"],\n    hostVars: 1,\n    hostBindings: function AccordionComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-multiselectable\", ctx.closeOthers);\n      }\n    },\n    inputs: {\n      isAnimated: \"isAnimated\",\n      closeOthers: \"closeOthers\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function AccordionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2\n  });\n  return AccordionComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * ### Accordion heading\n * Instead of using `heading` attribute on the `accordion-group`, you can use\n * an `accordion-heading` attribute on `any` element inside of a group that\n * will be used as group's header template.\n */\n\n\nlet AccordionPanelComponent = /*#__PURE__*/(() => {\n  class AccordionPanelComponent {\n    constructor(accordion) {\n      /** turn on/off animation */\n      this.isAnimated = false;\n      /** Provides an ability to use Bootstrap's contextual panel classes\n       * (`panel-primary`, `panel-success`, `panel-info`, etc...).\n       * List of all available classes [available here]\n       * (https://getbootstrap.com/docs/3.3/components/#panels-alternatives)\n       */\n\n      this.panelClass = 'panel-default';\n      /** if <code>true</code> — disables accordion group */\n\n      this.isDisabled = false;\n      /** Emits when the opened state changes */\n\n      this.isOpenChange = new EventEmitter();\n      this._isOpen = false;\n      this.accordion = accordion;\n    } // Questionable, maybe .panel-open should be on child div.panel element?\n\n    /** Is accordion group open or closed. This property supports two-way binding */\n\n\n    get isOpen() {\n      return this._isOpen;\n    }\n\n    set isOpen(value) {\n      if (value !== this.isOpen) {\n        if (value) {\n          this.accordion.closeOtherPanels(this);\n        }\n\n        this._isOpen = value;\n        Promise.resolve(null).then(() => {\n          this.isOpenChange.emit(value);\n        });\n      }\n    }\n\n    get isBs3() {\n      return isBs3();\n    }\n\n    ngOnInit() {\n      this.accordion.addGroup(this);\n    }\n\n    ngOnDestroy() {\n      this.accordion.removeGroup(this);\n    }\n\n    toggleOpen() {\n      if (!this.isDisabled) {\n        this.isOpen = !this.isOpen;\n      }\n    }\n\n  }\n\n  AccordionPanelComponent.ɵfac = function AccordionPanelComponent_Factory(t) {\n    return new (t || AccordionPanelComponent)(i0.ɵɵdirectiveInject(AccordionComponent));\n  };\n\n  AccordionPanelComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: AccordionPanelComponent,\n    selectors: [[\"accordion-group\"], [\"accordion-panel\"]],\n    hostAttrs: [1, \"panel\", 2, \"display\", \"block\"],\n    hostVars: 2,\n    hostBindings: function AccordionPanelComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"panel-open\", ctx.isOpen);\n      }\n    },\n    inputs: {\n      heading: \"heading\",\n      panelClass: \"panelClass\",\n      isDisabled: \"isDisabled\",\n      isOpen: \"isOpen\"\n    },\n    outputs: {\n      isOpenChange: \"isOpenChange\"\n    },\n    ngContentSelectors: _c3,\n    decls: 9,\n    vars: 6,\n    consts: [[1, \"panel\", \"card\", 3, \"ngClass\"], [\"role\", \"tab\", 1, \"panel-heading\", \"card-header\", 3, \"ngClass\", \"click\"], [1, \"panel-title\"], [\"role\", \"button\", 1, \"accordion-toggle\"], [\"class\", \"btn btn-link\", \"type\", \"button\", 3, \"ngClass\", 4, \"ngIf\"], [\"role\", \"tabpanel\", 1, \"panel-collapse\", \"collapse\", 3, \"collapse\", \"isAnimated\"], [1, \"panel-body\", \"card-block\", \"card-body\"], [\"type\", \"button\", 1, \"btn\", \"btn-link\", 3, \"ngClass\"]],\n    template: function AccordionPanelComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c2);\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵlistener(\"click\", function AccordionPanelComponent_Template_div_click_1_listener() {\n          return ctx.toggleOpen();\n        });\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵtemplate(4, AccordionPanelComponent_button_4_Template, 2, 4, \"button\", 4);\n        i0.ɵɵprojection(5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 5);\n        i0.ɵɵelementStart(7, \"div\", 6);\n        i0.ɵɵprojection(8, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", ctx.panelClass);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", ctx.isDisabled ? \"panel-disabled\" : \"panel-enabled\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"aria-expanded\", ctx.isOpen);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.heading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"collapse\", !ctx.isOpen)(\"isAnimated\", ctx.isAnimated);\n      }\n    },\n    directives: [i1.NgClass, i1.NgIf, i2.CollapseDirective],\n    styles: [\"[_nghost-%COMP%]   .card-header.panel-enabled[_ngcontent-%COMP%]{cursor:pointer}[_nghost-%COMP%]   .card-header.panel-disabled[_ngcontent-%COMP%]   .btn.btn-link[_ngcontent-%COMP%]{cursor:default;text-decoration:none}\"]\n  });\n  return AccordionPanelComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AccordionModule = /*#__PURE__*/(() => {\n  class AccordionModule {\n    static forRoot() {\n      return {\n        ngModule: AccordionModule,\n        providers: []\n      };\n    }\n\n  }\n\n  AccordionModule.ɵfac = function AccordionModule_Factory(t) {\n    return new (t || AccordionModule)();\n  };\n\n  AccordionModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: AccordionModule\n  });\n  AccordionModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule, CollapseModule]]\n  });\n  return AccordionModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { AccordionComponent, AccordionConfig, AccordionModule, AccordionPanelComponent }; //# sourceMappingURL=ngx-bootstrap-accordion.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}