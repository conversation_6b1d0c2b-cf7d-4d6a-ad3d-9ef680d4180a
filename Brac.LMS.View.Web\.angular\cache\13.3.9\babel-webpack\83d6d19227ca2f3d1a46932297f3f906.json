{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Macedonian [mk]\n//! author : <PERSON><PERSON> : https://github.com/B0k0\n//! author : <PERSON><PERSON><PERSON> : https://github.com/bkyceh\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var mk = moment.defineLocale('mk', {\n    months: 'јануари_февруари_март_април_мај_јуни_јули_август_септември_октомври_ноември_декември'.split('_'),\n    monthsShort: 'јан_фев_мар_апр_м<PERSON><PERSON>_јун_јул_авг_сеп_окт_ное_дек'.split('_'),\n    weekdays: 'недела_понеделник_вторник_среда_четврток_петок_сабота'.split('_'),\n    weekdaysShort: 'нед_пон_вто_сре_чет_пет_саб'.split('_'),\n    weekdaysMin: 'нe_пo_вт_ср_че_пе_сa'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'D.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY H:mm',\n      LLLL: 'dddd, D MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[Денес во] LT',\n      nextDay: '[Утре во] LT',\n      nextWeek: '[Во] dddd [во] LT',\n      lastDay: '[Вчера во] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n          case 3:\n          case 6:\n            return '[Изминатата] dddd [во] LT';\n\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[Изминатиот] dddd [во] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'за %s',\n      past: 'пред %s',\n      s: 'неколку секунди',\n      ss: '%d секунди',\n      m: 'една минута',\n      mm: '%d минути',\n      h: 'еден час',\n      hh: '%d часа',\n      d: 'еден ден',\n      dd: '%d дена',\n      M: 'еден месец',\n      MM: '%d месеци',\n      y: 'една година',\n      yy: '%d години'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(ев|ен|ти|ви|ри|ми)/,\n    ordinal: function (number) {\n      var lastDigit = number % 10,\n          last2Digits = number % 100;\n\n      if (number === 0) {\n        return number + '-ев';\n      } else if (last2Digits === 0) {\n        return number + '-ен';\n      } else if (last2Digits > 10 && last2Digits < 20) {\n        return number + '-ти';\n      } else if (lastDigit === 1) {\n        return number + '-ви';\n      } else if (lastDigit === 2) {\n        return number + '-ри';\n      } else if (lastDigit === 7 || lastDigit === 8) {\n        return number + '-ми';\n      } else {\n        return number + '-ти';\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n\n    }\n  });\n  return mk;\n});", "map": null, "metadata": {}, "sourceType": "script"}